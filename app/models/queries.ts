/* tslint:disable */
/* eslint-disable */
// this is an auto generated file. This will be overwritten

export const listTenant = /* GraphQL */ `
  query ListTenant(
    $type: TenantType!
    $resource: TenantResources
    $openWithinDays: Int
    $newlyOpened: Boolean
    $size: Int
    $orderBy: [TenantOrderByInput]
  ) {
    listTenant(
      type: $type
      resource: $resource
      openWithinDays: $openWithinDays
      newlyOpened: $newlyOpened
      size: $size
      orderBy: $orderBy
    ) {
      id
      title
      logoImage
      location
      aemTenantDetails {
        price
        categories
        openedDate
        logoImage
        backgroundImage
        shortImage
        longImage
        additionalTileImages
      }
    }
  }
`
export const listAEMDine = /* GraphQL */ `
  query ListAEMDine {
    listAEMDine {
      title
      image
      expiryDate
      sequenceNum
      source
      link
    }
  }
`

export const listFilterPillDineShop = /* GraphQL */ `
  query ($input: ListFilterPillDineShopInput!) {
    listFilterPillDineShop(input: $input) {
      data {
        tagTitle
        tagName
        childTags {
          tagTitle
          tagName
          filterType
        }
      }
      pill_options {
        tagTitle
        tagName
        filterType
      }
    }
  }
`

export const getFilterListDine = /* GraphQL */ `
  query GetFilterListDine($filterTag: String) {
    getFilterListDine(filterTag: $filterTag) {
      locations
      availability
      price
      areas
      default
      cuisines
      dietery
    }
  }
`

export const getFilterListShop = /* GraphQL */ `
  query GetFilterListShop($filterTag: String) {
    getFilterListShop(filterTag: $filterTag) {
      locations
      availability
      price
      areas
      default
      Categories
      offers
    }
  }
`

export const diningGuideQuery = /* GraphQL */ `
  query ListAEMDine {
    listAEMDine {
      title
      image
      expiryDate
      sequenceNum
      source
      link
    }
  }
`
export const dineFilterParameterQuery = `
query {
  getFilterListDine {
    dinePills {
      tagName
      tagTitle
      childTags {
        tagName
        tagTitle
      }
    }
  }
  }
`
export const shopFilterParameterQuery = `
query {
  getFilterListShop {
    shopPills {
      tagName
      tagTitle
      childTags {
        tagName
        tagTitle
      }
    }
  }
  }
`
export const mainPromoQuery = `
query($type: String!) {
  getMainPromoDineShop(type: $type) {
    offerId
    tenantId
    image
    promoTitle
    subCopy
    navigationType
    navigationValue
    redirect {
      redirectTarget
      utmParameters {
          content
          medium
          term
          campaign
      }
    }
  }
}`

export const pageConfigQuery = /* GraphQL */ `
  query ($type: String!, $version: Int!) {
    getPageConfig(type: $type, version: $version) {
      name
      _uid
      props {
        type
        title
        optionalTitle
        screen
      }
    }
  }
`

export const getRestaurantBlogsAndReviewsQuery = `
query($tenantID: String!) {
  getBlogsAndReviews(tenantID: $tenantID) {
      blogLogoImage
      blogTitle
      blogLink
      description
      blogType
  }
}
`
export const getRestaurantChangiEatsQuery = `
query($tenantID: Int!) {
  getChangiEats (tenantID: $tenantID) {
      image
      copy
      buttonLabel
      link
  }
}
`
export const getRestaurantRewardInfoQuary = `
query($tenantID: String!) {
  getTenantReward(id: $tenantID) 
  {
      title
      text
      icon
      link
      linkText
	    dismissible
  }
}
`
export const shopPartnerOfferQuery = `
query {
  getTenantOffer
 {
    icon
    partnerName
    sectionTitle
    subCopy
    offersID
    offers {
       offerId
       image
       tenantName
       offerTitle
       subCopy
       linkURL
       ribbonText
    }
  }
}
`

export const getRestaurantDetailsQuery = `
query($id: String!) {
  getDineRestaurantDetail(id: $id) {
    id
    title
    titleZh
    titleZhHant
    localRef
    locationDetails {
      openCloseStatus {
        status
        colorCode
      }
      alwaysOn
      contact
      terminal
      mapName
      localRef
      unitNo
      area
      description
      descriptionZh
      descriptionZhHant
      timingsInfo {
        day
        timings
      }
      hourComment
      menuLink
    }
    websiteLink
    websiteLinkZh
    bookTableCTA
    description
    descriptionZh
    descriptionZhHant
    logo
    heroCarouselImage
    price
    changiPay
    changiEats
    tags
    viewMenuURL
    exploreCategories {
      filterType
      image
      sequenceNumber
      tagName
      tagTitle
    }
    chopeUrl
    aemTenantDetails {
      iscurl
    }
  }
}`

export const forYouRewardsCard = `
query {
  getChangiRewards {
    reward {
        icon
        point
        tokenText
        memberText
        currentTierInfo
        currentTierTarget
        nextTierTarget
        currentExpenditureValue
        target
    }
    rewardTravel {
        icon
        currentTierInfo
        flightsCompleted
        flightsRegistered
        memberText
        currentTierTarget
        nextTierTarget
        membershipType
        target
    }
    rewardTravelUpgrade {
        tierInfo
        membershipType
        subCopyText
        buttonTitle
        currentTier
    }
  }
}
`

export const shopDetailScreenQuery = `
query($id : String!) {
  getShopDetail(id:$id) {
    id
        title
        titleZh
        titleZhHant
        localRef
        locationDetails {
            contact
            terminal
            mapName
            localRef
            unitNo
            area
            timingsInfo {
                day
                timings
            }
            openCloseStatus {
                status
                colorCode
            }
            alwaysOn
            description
            descriptionZh
            descriptionZhHant
            hourComment
        }
        websiteLink
        websiteLinkZh
        description
        descriptionZh
        descriptionZhHant
        logo
        heroCarouselImage
        price
        changiPay
        iSCAvailability
        iSCURL
        tags
        exploreCategories {
          filterType
          image
          sequenceNumber
          tagName
          tagTitle
        }
        aemTenantDetails {
          iscurl
        }
  }

}
`

export const getForYouMoreOptionsQuery = `
query($displayScreen: MoreOptionDisplayScreen) {
  getMoreOptions(displayScreen: $displayScreen)  {
      icon
      title
      redirection
      sequenceNumber
      displayScreen
      navigationType
      redirect {
        redirectTarget
        utmParameters {
            content
            medium
            term
            campaign
        }
      }
      navigationValue
  }
}
`

export const shortcutLinksQuery = `
query($location: Locations) {
  getShortcutLinks(location: $location) {
    icon
    title
    sequenceNumber
    url
    label
    navigationType
    navigationValue
    redirect {
      redirectTarget
      utmParameters {
          content
          medium
          term
          campaign
      }
    }
  }
}`

export const forYouAskMax = `
query {
  getAskMax {
      description
      highLights
      image
      placeholderText
      buttonText
      title
  }
}
`
export const newlyOpened = `
query($type: TenantType!)
{
  getNewlyOpened(
    type: $type
  ) {
    id
    title
    location
    location_display
    aemTenantDetails {
      logoImage
      backgroundImage
    }
  }
}
`
export const exploreMoreLanding = `
query($type: TenantType!){
  getExploreMoreLanding(
    type: $type
  ) {
      nodes {
        aemTenantDetails {
          additionalTileImages
          area {
              filterType
              sequenceNumber
              tagName
              tagTitle
          }
          categories
          dietary {
              filterType
              sequenceNumber
              tagName
              tagTitle
          }
          logoImage
          price
          rewards {
            title
          }
        }
        area_display
        id
        location
        location_display
        logoImage
        title
      }
      moreRecords
  }
}
`
export const masonryLanding = `
query($type: TenantType!)
{
  getMasonryLanding(
    type: $type
  ) {
      nodes {
        id
        title
        location
        location_display
        logoImage
        aemTenantDetails {
          shortImage
          longImage
        }
        area_display
      }
      moreRecords
  }
}
`
export const signInQuery = `
  query($code: String!, $grantType: String!) {
    getOCIDToken(code: $code, grantType: $grantType) {
      access_token
      expires_in
      refresh_token
      scope
      token_type
    }
  }
`

export const signUpQuery = `query($input: MircoServiceSignUpInput!) {
  getMircoServicesSignup(input: $input) {
      onechangi {
          ocid
          accountId
          accountValidatedStatus
          accountCreationStatus
      }
      ishopChangi {
          iscId
          accountLinkStatus
          accountCreationStatus
      }
      changiRewards {
          memberId
          accountLinkStatus
          accountCreationStatus
      }
      changiRewardsTravel {
          crtMemberId
          accountLinkStatus
          accountCreationStatus
          crtUrl
      }
  }
}`

export const notToBeMissed = `
query {
  getNotMissedOffers 
  {
      offerId
      ribbonText
      image
      tenantName
      offerTitle
      expiryDate
      linkURL
      isReedeemable
  }
}`

export const spotLightBrandOfferQuery = `
query { 
  getShopOfferSwimLane{
      logo
      sectionTitle
      sectionSubTitle
      products{
          image
          brandName
          productID
          productName
          mainPrice {
            currency
            value
          }
          salePrice {
            currency
            value
          }
          ribbonText
          iSCLink
          navigationType
          navigationValue
          redirect {
            redirectTarget
            utmParameters {
                content
                medium
                term
                campaign
            }
          }
      }
  }
}
`
export const dineFilterResultQuery = `
query($filter: [SelectedFilterParams] $after: String $before: String $first: Int $orderBy: [TenantOrderByInput] $size: Int) {
  getResultScreenByFilterDine(
      filter: $filter
      orderBy: $orderBy
      after: $after
      before: $before
      first: $first
      size: $size
      ){
          nodes {
              id
              title
              location
              location_display
              logoImage
              aem_availability
              aemTenantDetails{
                  logoImage
                  categories
                  price
                  additionalTileImages
                  rewards{
                      title
                  }
              }
          }
          pageInfo{
              endCursor
              startCursor
              hasPreviousPage
              hasNextPage
          }
          totalCount
      }
}`

export const shopFilterResultQuery = `
query($filter: [SelectedFilterParams] $after: String $before: String $first: Int $orderBy: [TenantOrderByInput] $size: Int) {
  getResultScreenByFilterShop(
      filter: $filter
      orderBy: $orderBy
      after: $after
      before: $before
      first: $first
      size: $size
      ){
          nodes {
              id
              title
              location
              location_display
              logoImage
              aemTenantDetails{
                  logoImage
                  categories
                  price
                  additionalTileImages
                  rewards{
                      title
                  }
              }
          }
          pageInfo{
              endCursor
              startCursor
              hasPreviousPage
              hasNextPage
          }
          totalCount
      }
}`

export const getFlights = `
query getFlights($direction: String!, $next_token: String, $page_size: String , $scheduled_date: String , $terminal: String, $scheduled_time: String) {
  getFlights(direction: $direction, page_size: $page_size, next_token: $next_token , scheduled_date: $scheduled_date , terminal: $terminal, scheduled_time: $scheduled_time) {
    flights {
        flight_number
        aircraft_type
        airline
        airport
        check_in_row
        current_gate
        direction
        display_checkinrowctr
        display_gate
        display_timestamp
        display_timestamp_mapping
        drop_off_door
        flight_status
        flight_type
        last_updated_timestamp
        master_flight_number
        nature
        nearest_carpark
        offblock_timestamp
        scheduled_date
        scheduled_time
        slave_flights
        technical_flight_status1
        technical_flight_status2
        terminal
        via
        estimated_timestamp
        previous_gate
        display_belt
        pick_up_door
        actual_timestamp
        firstbag_timestamp
        lastbag_timestamp
        airline_details {
            code
            name
            name_zh
            name_zh_hant
            transfer_counters
            transit
            logo_url
            eligible_fast_checkin
        }
        airport_details {
            code
            name
            name_zh
            name_zh_hant
            lat
            lng
            country_code
            country
        }
        via_airport_details {
            code
            name
            name_zh
            name_zh_hant
            lat
            lng
            country_code            
        }
        origin_dep_date
        origin_dep_time
        origin_dep_country
        origin_dep_terminal
        origin_via_country
        origin_via_date
        status_mapping {
            show_gate
            listing_status_en
            listing_status_zh
            details_status_en
            details_status_zh
            status_text_color
            belt_status_en            
        }
      }
    next_token
  }
}
`

export const getFlightsV2 = `
query getFlights($direction: String!, $next_token: String, $page_size: String , $scheduled_date: String , $terminal: String, $scheduled_time: String, $airport: String, $airline: String) {
  getFlights(direction: $direction, page_size: $page_size, next_token: $next_token , scheduled_date: $scheduled_date , terminal: $terminal, scheduled_time: $scheduled_time, airline: $airline, airport: $airport) {
    flights {
        flight_number
        aircraft_type
        airline
        airport
        check_in_row
        current_gate
        direction
        display_checkinrowctr
        display_gate
        display_timestamp
        display_timestamp_mapping
        drop_off_door
        flight_status
        flight_type
        last_updated_timestamp
        master_flight_number
        nature
        nearest_carpark
        offblock_timestamp
        scheduled_date
        scheduled_time
        slave_flights
        technical_flight_status1
        technical_flight_status2
        terminal
        via
        estimated_timestamp
        previous_gate
        display_belt
        pick_up_door
        actual_timestamp
        firstbag_timestamp
        lastbag_timestamp
        airline_details {
            code
            name
            name_zh
            name_zh_hant
            transfer_counters
            transit
            logo_url
            eligible_fast_checkin
        }
        airport_details {
            code
            name
            name_zh
            name_zh_hant
            lat
            lng
            country_code
            country
        }
        via_airport_details {
            code
            name
            name_zh
            name_zh_hant
            lat
            lng
            country_code            
        }
        origin_dep_date
        origin_dep_time
        origin_dep_country
        origin_dep_terminal
        origin_via_country
        origin_via_date
        status_mapping {
            show_gate
            listing_status_en
            listing_status_zh
            details_status_en
            details_status_zh
            status_text_color
            belt_status_en            
        }
      }
    next_token
  }
}
`

export const getFlyEarlierListQuery = `
query getFlyEarlierListQuery($direction: String!, $prev: String, $next_token: String, $page_size: String, $scheduled_date: String, $terminal: String, $scheduled_time: String) {
  getFlights(direction: $direction, prev: $prev, next_token: $next_token, page_size: $page_size , scheduled_date: $scheduled_date , terminal: $terminal, scheduled_time: $scheduled_time) {
    flights {
        flight_number
        aircraft_type
        airline
        airport
        check_in_row
        current_gate
        direction
        display_checkinrowctr
        display_gate
        display_timestamp
        display_timestamp_mapping
        drop_off_door
        flight_status
        flight_type
        last_updated_timestamp
        master_flight_number
        nature
        nearest_carpark
        offblock_timestamp
        scheduled_date
        scheduled_time
        slave_flights
        technical_flight_status1
        technical_flight_status2
        terminal
        via
        estimated_timestamp
        previous_gate
        display_belt
        pick_up_door
        actual_timestamp
        firstbag_timestamp
        lastbag_timestamp
        airline_details {
            code
            name
            name_zh
            name_zh_hant
            transfer_counters
            transit
            logo_url
            eligible_fast_checkin
        }
        airport_details {
            code
            name
            name_zh
            name_zh_hant
            lat
            lng
            country_code
        }
        via_airport_details {
            code
            name
            name_zh
            name_zh_hant
            lat
            lng
            country_code            
        }
        origin_dep_date
        origin_dep_time
        origin_dep_country
        origin_dep_terminal
        origin_via_country
        origin_via_date
        status_mapping {
            show_gate
            listing_status_en
            listing_status_zh
            details_status_en
            details_status_zh
            status_text_color
            belt_status_en            
        }
      }
    next_token
  }
}
`

export const getHorizontalContentsQuery = `
query {
  getHorizontalContents {
      sequenceNumber
      image
      subCopyText
      title
      navigationType
      navigationValue
      redirect {
        redirectTarget
        utmParameters {
            content
            medium
            term
            campaign
        }
      }
  }
}
`
export const getCRHowToUseQuery = `
query {
  getCRHowToUse {
      title
      highlights
    }
}
`

export const getCuisinesCategories = `
query($type: String!) {
  getCuisineCategories(type: $type) {
      image
      tagTitle
      tagName
      filterType
  }
}`

export const getTransactionsQuery = `
query($memberId: String!, $pageNumber: Int!, $year: Int) {
  getTransactions(memberId: $memberId , pageNumber: $pageNumber, year: $year){
      transactions {
        type
        title
        terminal
        date
        point
        nettSpent
        qtyRedeemed
      }
      paging {
        pageNumber
        pageCount
        totalTransactionCounts
      }
  }
}`

export const getAccountProfileDetails = `
query {
  getAcccountProfileDetails {
    status {
      status
      success
      message
      errorCode
    }
    data {
      id
      email
      vehicleIU
      krisflyerNo
      capitastarNo
      staffPassExpiry
      staffPassNumber
      firstName
      lastName
      phoneNum
      dob
      gender
      residentialCountry
      postalCode
      countryCode
      address
      nationality
      emailMessage
      logo1
      logo2
      linkFeedback
      linkCapitaStar
      validateAccountStatus
      crStatus {
        status
        message
        errorCode
      }
      iscStatus {
        status
        message
        errorCode
      }
      crtStatus {
        status
        success
        message
        errorCode
      }
    }
  }
}
`
export const getRedemptionCatalogueQuery = `
query {
  getRedemptionCatalogue {
    status {
        success
        message
        errorCode
    }
    itemLists {
        SKU
        imageLink
        categoryCode
        name
        points
    }
  }
}
`

export const getFlightList = `
query($direction: FlightDirection! $after: String $before: String $query: String){
  getFlightList(
    direction: $direction
    after: $after
    before: $before
    query: $query
    ) {
    initialQuery
    nodes {
      flightTime:scheduledTime
      flightStatus
      logo
      flightNumber
      flightUniqueId
      destinationCode
      departingCode
      flightDate
      scheduledDate
      codeShare:codeShares
      transits {
        id
        code
      }
    }
    pageInfo {
      endCursor
      hasNextPage
      hasPreviousPage
      startCursor
    }
  }
}
`

export const linkCapitastarMobileNoQuery = `
mutation ($mobileNumber: String! $memberId: String!){   
  linkCapitastarMobileNo (mobileNumber: $mobileNumber memberId: $memberId){
        memberNumber
        status {
        status
        success
        message
        errorCode
        aemEHRCode
      }   
    } 
  }
`
export const getRedeemNewRewardsQuery = `
query {
  getRedeemNewRewards {
    status {
        success
        message
        errorCode
    }
    itemLists {
        SKU
        imageLink
        categoryCode
        name
        points
    }
  }
}`

export const flyCityCodes = `
query{
  getAirports {
    code
    country_code
    lat
    lng
    name
    name_zh
    name_zh_hant
  }
}`

export const changePasswordUrlQuery = `
query {
  getIchangiResetPasswordLink {
      url
  }
}`

export const getOfferDetail = /* GraphQL */ `
  query ($id: String!) {
    getOfferDetail(id: $id) {
      offerID
      additionalImages
      logo
      tenantID
      offerTitle
      tagsToDisplay
      section1Title
      section1Text
      section2Title
      section2Text
      section3Title
      section3Text
      buttonLabel
      buttonLink
      locationDetails {
        locationMainTitle
        location
        localRef
        contactNumber
        websiteLink
        menuLink
        openingHours {
          day
          timings
        }
        openCloseStatus {
          status
          colorCode
        }
        terminal
        unitNo
      }
    }
  }
`

export const searchDineShopQuery = `
query searchTenants ($text: String!, $filter: SearchFilter, $page_number: Int, $page_size: Int) {
  search(category: TENANTS, text: $text, filter: $filter, page_number: $page_number, page_size: $page_size) {
    total
    page_size
    page_number
    items {
      ... on TenantDetail {
        aem_location {
          filterType
          sequenceNumber
          tagName
          tagTitle
        }
        aemTenantDetails {
          dietary {
            filterType
            sequenceNumber
            tagName
            tagTitle
          }
        }
        area {
          filterType
          sequenceNumber
          tagName
          tagTitle
        }
        area_display
        backgroundImageUrl
        categories {
          filterType
          sequenceNumber
          tagName
          tagTitle
        }
        category
        changiEats
        changiEatsURL
        changiPay
        heroCarouselImage
        id
        image
        iscAvailability
        iscURL
        keywords
        link
        local_id
        location
        location_display
        location_list {
          always_on
          area
          caption
          caption_zh
          caption_zh_hant
          description
          description_zh
          description_zh_hant
          email
          hours
          hours_zh
          hours_zh_hant
          mapname
          shop
          tel
          terminal
          unit_no
          x
          y
        }
        logoImage
        logoImageUrl
        longImageUrl
        name
        openedDate
        price {
          filterType
          sequenceNumber
          tagName
          tagTitle
        }
        priceStrSearch
        shortImageUrl
        source
        tenantId
        tenantLocation
        title
        type
        viewMenuURL
      }
    }
	}
}

`

export const getRedemptionDetailQuery = `
query($SKU: String!) {
  getRedemptionDetail (SKU: $SKU){
      status {
          success
          message
          errorCode
      }
      redemptionDetails {
          SKU
          imageLink
          name
          categoryCode
          catalogCode
          pointsText
          itemSummary
          itemDescription
          points
          alternateID
          productRef5
      }
  }
}
`

export const getRedeemExploreMoreRewardsQuery = `
query (
  $catalogue: String!
  $sortBy: [SortByRedemption]
  $before: String
  $after: String
  $exceptSKU: String
  $memberId: String) {
  getRedeemExploreMoreRewards (
    catalogue: $catalogue
    sortBy: $sortBy
    before: $before
    after: $after
    exceptSKU: $exceptSKU
    memberId: $memberId
    ) {
    status {
        status
        success
        message
        errorCode
    }
    itemLists {
        SKU
        imageLink
        categoryCode
        name
        points
    }
    paging {
        pageInfo {
            startCursor
            endCursor
            hasNextPage
            hasPreviousPage
        }
        totalItemCounts
    }
    catalogLists {
        catalogCode
        categoryCode
        sequenceNumber
    }
  }
}
`
export const getCampaignsQuery = `
query {
  getCampaigns {
    status {
      success
      message
      errorCode
      status
      statusCode
    }
    data {
      type
      title
      imageUrl
      subtitle
      gradientInfo
      navigationType
      navigationValue
      redirect {
        redirectTarget
        utmParameters {
            content
            medium
            term
            campaign
        }
      }
      orientation
      campaignCode
      packageCode
      redirectToShowOnListing
      offerId
      sequenceNumber
      tabName
      fragmentTitle
    }
  }
}
`
export const getFlyFilters = `
query{
  getFlyFilters {
    flyFilters {
      tagTitle
      tagName
      childTags {
        tagName
        tagTitle
      }
    }
  }
}`

export const getExploreCategoriesQuery = `
query($pageNumber: Int!, $tabName: String!, $email: String) {
  getExploreCategories(pageNumber: $pageNumber, tabName: $tabName, email: $email) {
    status {
      success
      message
      errorCode
    }
    exploreCategories {
      tabName
      sequenceNumber
      pinnedTiles
      events {
        packageCode
        title
        imageThumbnail
        imageListing
        imageHero
        tags
        location
        eventStart
        eventEnd
        dateDetails
        ticketPrices
        aboutDetails
        aboutLink
        entryRequirements
        termAndConditions
        sequenceNumber
        isPinned
        bookingUrl
      }
      attractions {
        title
        image
        startDate
        expiryDate
        operatingHours
        sequenceNumber
        location
        isPinned
        navigation {
          type
          value
        }
      }
      paging {
        pageNumber
        pageCount
        totalItemCounts
      }
    }
  }
}
`

export const getPackageDetailsQuery = `
query($packageCode: String!, $email: String) {
  getPackageDetails(
    packageCode: $packageCode,
    email: $email
  ) {
    status {
        success
        message
        errorCode
    }
    data {
        packageCode
        title
        imageThumbnail
        imageListing
        imageHero
        tags
        location
        eventStart
        eventEnd
        dateDetails
        ticketPrices
        aboutDetails
        aboutLink
        entryRequirements
        termAndConditions
        bookingUrl
    }
  }
}
`

export const getEditBookingPageUrlQuery = `
query($email: String!) {
  getEditBookingPageUrl(
    email: $email
  ) {
      statusEditBookingPageUrl {
        message
        statusCode
      }
      editBookingPageUrls {
        editBookingPageUrl
        timeTs
      }
    }
}
`

export const getAllBookingsQueryV2 = `
query($input: PlaypassBookingInput!){
  getPlaypassBookings_v2(input: $input) {
    data {
      type
      tags
      bookingId
      icon
      title
      subTitle
      quantity
      startDate
      startTime
      location
      locationCode
      endDate
      status
      productName
      timerTs
      cartId
      playpassEventCard {
          image
          title
          date
          time
      }
    }
  }
}
`

export const aboutChangiLifeQuery = `
query {
  getAboutChangiLife {
    changiLifeDetails {
      icon
      title
      description
    }
    moreOptions {
      icon
      title
      redirection
      sequenceNumber
      displayScreen
      keywords {
        sequenceNumber
        tagName
        tagTitle
      }
      navigationType
      navigationValue
      redirect {
        redirectTarget
        utmParameters {
            content
            medium
            term
            campaign
        }
      }
      url
    }
  }
}
`

export const rateAirportExperienceQuery = `
query {
  getImageForRateExperience {
      id
      path
  }
}
`

export const mostPopularRestaurantsQuery = `
query($type: String!) {
  getTopRestaurantDefault(type: $type) {
      id
      title
      location
      location_display
      aemTenantDetails {
          logoImage
          cuisine {
              tagTitle
          }
          price
          additionalTileImages
          rewards {
              title
          }
          categories
      }
  }
}
`
export const mostPopularRestaurantsShopQuery = `
query($type: String!) {
  getTopRestaurantDefault(type: $type) {
    aemTenantDetails {
        additionalTileImages
        area {
            filterType
            sequenceNumber
            tagName
            tagTitle
        }
        availability {
            filterType
            sequenceNumber
            tagName
            tagTitle
        }
        backgroundImage
        categories
        cuisine {
            filterType
            sequenceNumber
            tagName
            tagTitle
        }
        dietary {
            filterType
            sequenceNumber
            tagName
            tagTitle
        }
        location {
            filterType
            sequenceNumber
            tagName
            tagTitle
        }
        logoImage
        longImage
        openedDate
        price
        rewards {
            dismissible
            displayLocation
            expiryDate
            icon
            link
            linkText
            offerID
            startDate
            tenantIds
            text
            title
        }
        shortImage
    }
    filter {
        child
        main
    }
    id
    location
    logoImage
    location_display
    title
  }
}
`

export const getRedeemHeroRewardQuery = `
query {
  getRedeemHeroReward {
    SKU
    image
    promoTitle
    subCopy
    sequenceNumber
    memberEligibility  
    navigationType
    navigationValue  
    redirect {
      redirectTarget
      utmParameters {
          content
          medium
          term
          campaign
      }
    }
  }
}
`

export const getTimelineTiles = `
query($direction:FlightDirection! $flightno:String! $airportCode:String! $flightUniqueId:String!){
  getTimelineTiles(airportCode:$airportCode direction:$direction flightno:$flightno flightUniqueId:$flightUniqueId) {
    title
    image
    section{
      sectionID
      sectionTitle
      sequenceNumber
      tileItems {
        ... on TimelineTilesVariation1 {
          tileID
          sectionID
          description
          image
          linkText
          navigationType
          navigationValue
          sequenceNumber
          title
          variationType
        }
        ... on TimelineTilesVariation2 {
          tileID
          sectionID
          description
          image
          linkLabel
          linkLabel2
          linkText
          linkText2
          navigationType
          navigationType2
          navigationValue
          navigationValue2
          sequenceNumber
          title
          variationType
        }
      }
    }
  }
}
`
export const getTravelChecklists = `
query($input: GetTravelChecklistInput!) {
  getTravelChecklists(input: $input) {
    sectionID
    sectionTitle
    sectionKey
    sequenceNumber
    flyDirection {
      filterType
      sequenceNumber
      tagName
      tagTitle
    }
    variations {
      applicabilitySummaryTimeline
      contents {
        contentID
        description
        image
        navigation {
          type
          value
        }
        redirect {
          redirectTarget
          utmParameters {
              content
              medium
              term
              campaign
          }
        }
        linkText
        title
      }
      description
      endDate
      imageSummaryTimeline
      image
      link1Label
      link1Navigation {
        type
        value
      }
      link1Redirect {
        redirectTarget
        utmParameters {
            content
            medium
            term
            campaign
        }
      }
      link1Text
      link2Label
      link2Navigation {
        type
        value
      }
      link2Redirect {
        redirectTarget
        utmParameters {
            content
            medium
            term
            campaign
        }
      }
      link2Text
      linkTextSummaryTimeline
      priorityIndex
      startDate
      summaryTimelineNavigation {
        type
        value
      }
      tileID
      title
      titleSummaryTimeline
      type
      variationType {
        filterType
        sequenceNumber
        tagName
        tagTitle
      }
    }
  }
}
`

export const searchFlights = `
query SearchFlights ($text: String!, $filter: SearchFilter, $page_number: Int, $page_size: Int) {
  flights: search(category: FLIGHTS, text: $text, filter: $filter, page_number: $page_number, page_size: $page_size) {
    items {
      ... on Flight {
        aircraft_type
        airline
        actual_timestamp
        airport
        check_in_row
        current_gate
        direction
        display_belt
        display_checkinrowctr
        display_gate
        display_timestamp
        display_timestamp_mapping
        drop_off_door
        estimated_timestamp
        firstbag_timestamp
        flight_number
        flight_status
        flight_type
        last_updated_timestamp
        lastbag_timestamp
        master_flight_number
        nature
        nearest_carpark
        offblock_timestamp
        origin_dep_country
        origin_dep_date
        origin_dep_terminal
        origin_dep_time
        origin_via_country
        origin_via_date
        pick_up_door
        previous_gate
        scheduled_date
        scheduled_time
        slave_flights
        technical_flight_status1
        technical_flight_status2
        terminal
        via
        via_airport_details {
          code
          country_code
          lat
          lng
          name
          name_zh
          name_zh_hant
        }
        status_mapping {
          belt_status_en
          details_status_en
          details_status_zh
          listing_status_en
          listing_status_zh
          status_text_color
          show_gate
        }
        airline_details {
          code
          logo_url
          name
          name_zh
          name_zh_hant
          transfer_counters
          transit
          eligible_fast_checkin
        }
        airport_details {
          code
          country
          country_code
          lat
          lng
          name
          name_zh
          name_zh_hant
        }
      }
    }
    page_number
    page_size
    total
  }
}
`
export const searchAllFlights = `
query SearchFlights ($text: String!, $filter: SearchFilter, $page_number: Int, $page_size: Int) {
  flights: search(category: FLIGHTS, text: $text, filter: $filter, page_number: $page_number, page_size: $page_size) {
    items {
      ... on Flight {
        aircraft_type
        airline
        actual_timestamp
        airport
        check_in_row
        current_gate
        direction
        display_belt
        display_checkinrowctr
        display_gate
        display_timestamp
        display_timestamp_mapping
        drop_off_door
        estimated_timestamp
        firstbag_timestamp
        flight_number
        flight_status
        flight_type
        last_updated_timestamp
        lastbag_timestamp
        master_flight_number
        nature
        nearest_carpark
        offblock_timestamp
        origin_dep_country
        origin_dep_date
        origin_dep_terminal
        origin_dep_time
        origin_via_country
        origin_via_date
        pick_up_door
        previous_gate
        scheduled_date
        scheduled_time
        slave_flights
        technical_flight_status1
        technical_flight_status2
        terminal
        via
        via_airport_details {
          code
          country_code
          lat
          lng
          name
          name_zh
          name_zh_hant
        }
        status_mapping {
          belt_status_en
          details_status_en
          details_status_zh
          listing_status_en
          listing_status_zh
          status_text_color
          show_gate
        }
        airline_details {
          code
          logo_url
          name
          name_zh
          name_zh_hant
          transfer_counters
          transit
          eligible_fast_checkin
        }
        airport_details {
          code
          country
          country_code
          lat
          lng
          name
          name_zh
          name_zh_hant
        }
      }
    }
    isDepartureFightData
    page_number
    page_size
    total
  },
  airlines: search(category: AIRLINES, text: $text, page_number: 1, page_size: 100) {
    items {
      ... on AirlineDetail {
        name_zh
        name_zh_hant
        code
        logo_url
        name
        transfer_counters
        transit
      }
    }
  },
  cities: search(category: AIRPORTS, text: $text, page_number: 1, page_size: 100) {
    items {
      ... on AirportDetail {
        name_zh
        name_zh_hant
        code
        country_code
        lat
        lng
        name
      }
    }
  }
}
`
export const getSubscriptionCentresQuery = `
query($username: String!) {
  getSubscriptionCentres(username: $username) {
    isUser
    crEmail
    crSms
    crtPreferredLanguage
    crtEmail
    crtSms
    languagePreference
    iscEmail
    iscSms
    caEdm
    rsCrLink
    rsCrtLink
    rsIscLink
  }
}
`

export const updateSubscriptionCentresQuery = `
mutation ($username: String!, $input: SubscriptionCentresInput!) {
  updateSubscriptionCentres(username: $username, input: $input) {
    status
    success
    message
  }
}
`
export const getRedeemProgrammePointQuery = `
query($memberId: String, $catalogCode: String!) {
  getRedeemProgrammePoint(memberId: $memberId, catalogCode: $catalogCode) {
    maxqty
    programmeYears {
       year
       yearText
       point
       pointText
    }
  }
}`

export const getEventDetailsStatusBannerQuery = `
query($packageCode: String!) {
  getEventDetailsStatusBanners (
    packageCode: $packageCode,
  ) {
    statusGetPackages {
        statusCode
        message
    }
    statusCheckSlotsAvailability {
        statusCode
        message
    }
    statusGetSchemes {
        statusCode
        message
    }
    bannerStatus {
      iconUrl
      colorContentCode
      colorLayoutCode
      mainMessage
      subMessage
      isDisplay
    }
  }
}
`

export const getEventDetailsTokenBannersQuery = `
query($packageCode: String!, $email: String!, $shouldProcessCRTransaction: Boolean!) {
  getEventDetailsTokenBanners ( packageCode: $packageCode, email: $email, shouldProcessCRTransaction: $shouldProcessCRTransaction) {
    statusGetPackages {
      message
      statusCode
	  }
    statusProcessCRTransactions {
      message
      statusCode
    }
    statusGetTokenBalance{
      message
      statusCode
    }
    bannerToken {
      iconUrl
      title
      description
      linkText
      navigationType
      navigationValue
    }
  }
}
`

export const resendActivationLinkQuery = `
query($email: String!) {
  resendActivationLink(email: $email) {
      success
      message
  }
}
`
export const getIntoCityOrAirport = `
query($direction:FlightDirection! $flightUniqueId:String!){
  getIntoCityOrAirport(direction:$direction, flightUniqueId:$flightUniqueId){
    title
    label1
    link1
    label2
    link2
    label3
    link3
    data1
    data2
    data3
}
}
`

export const getIntoCityOrAirportV2 = `
query getIntoCityOrAirport_v2($direction:FlightDirection! $flightUniqueId:String!){
  getIntoCityOrAirport_v2(direction:$direction, flightUniqueId:$flightUniqueId){
    title
    label1
    link1
    label2
    link2
    label3
    link3
    data1
    data2
    data3
}
}
`

export const myTravelInsertFlight = `
mutation insertMyTravelFlightDetail($input:saveFlightInput!) {
  saveFlight (input: $input) {
      status
      message
      referralFlow
      eligibleForGameChance
      isOptedIn
  }
}
`

export const getMyTravelFlightDetails = `
query getSavedFlights ($startScheduledDate: AWSDate, $endScheduledDate: AWSDate, $username: String, $myTravelUidEnabled: Boolean) {
  getSavedFlights(startScheduledDate: $startScheduledDate, endScheduledDate: $endScheduledDate, username: $username, myTravelUidEnabled: $myTravelUidEnabled) {
    flightInfo {
        flight_number
        aircraft_type
        airline
        airport
        check_in_row
        current_gate
        direction
        display_checkinrowctr
        display_gate
        display_timestamp
        display_timestamp_mapping
        drop_off_door
        flight_status
        flight_type
        last_updated_timestamp
        master_flight_number
        nature
        nearest_carpark
        offblock_timestamp
        scheduled_date
        scheduled_time
        slave_flights
        technical_flight_status1
        technical_flight_status2
        terminal
        via
        estimated_timestamp
        previous_gate
        display_belt
        pick_up_door
        actual_timestamp
        firstbag_timestamp
        lastbag_timestamp
        airline_details {
            code
            name
            name_zh
            name_zh_hant
            transfer_counters
            transit
            logo_url
            eligible_fast_checkin
        }
        airport_details {
            code
            name
            name_zh
            name_zh_hant
            lat
            lng
            country_code
        }
        via_airport_details {
            code
            name
            name_zh
            name_zh_hant
            lat
            lng
            country_code            
        }
        origin_dep_date
        origin_dep_time
        origin_dep_country
        origin_dep_terminal
        origin_via_country
        origin_via_date
        status_mapping {
            show_gate
            listing_status_en
            listing_status_zh
            details_status_en
            details_status_zh
            status_text_color
            belt_status_en            
        }
    }
    savedFlightInfo {
        deviceId
        flightDirection
        flightNo
        history {
            action
            dateTime
            source
            transactionId
        }
        iataAirportCode
        isPassenger
        lastUpdatedDate
        lastUpdatedTimestamp
        scheduledDate
        scheduledTime
    }
  }
}
`

export const deleteMyTravelFlightDetail = `
mutation deleteMyTravelFlightDetail($input: removeFlightInput!) {
  removeFlight(input: $input) {
    message
    status
  }
}
`

export const getCRTBenefitCard = `
query($benefitType:CRTBenefitType! $flightDirection:FlightDirection) {
  getCRTBenefitCard(benefitType:$benefitType, flightDirection:$flightDirection) {
      icon
      title
      highlights
      url
  }
}
`

export const swipeToRedeemQuery = `
mutation ($input: SwipeToRedeemInput!){
  swipeToRedeem (input: $input){
      overallStatus {
          success
          itemRedemptionStatus {
              success
              status
              message
          }
          voucherIssuanceStatus {
              success
              status
              message
          }
          carParkRebateConversion {
              success
              status
              message
          }
          cagPointsRedemption {
              success
              status
              message
          }
          sendEmailStatus {
              success
              status
              message
          }
      }
      data {
          successText
          nameOfReward
          numberOfReward
      }
  }
}
`
export const getPrivilegesListQuery = `
query ($email: String!) {
  getPrivilegesList(email: $email) {
    status {
      success
      message
      errorCode
    }
    data {
      privilegesType
      code
      name
      description
      image
      expiry
      voucherType
      quantity
    }
    webView {
      crUrl
      crtUrl
      crtUpgradeUrl
    }
    privilegesType {
      privilegesTypeKey
      privilegesTypeValue
    }
  }
}
`
export const getTotalPrivilegesQuery = `
query ($email: String!) {
  getPrivilegesList(email: $email) {
    status {
      success
      message
      errorCode
    }
    totalItemCounts
  }
}
`

export const getEventConfirmationQuery = /* GraphQL */ `
  query ($email: String, $cartId: String!) {
    getEventConfirmation(email: $email, cardId: $cartId) {
      statusGetAllBookings {
        message
        statusCode
      }
      totalPaid
      eventPassDetails {
        title
        qrContent
        countOfTickets
        location
        startDate
        startTime
      }
      paymentDetails {
        amountPaidTitle
        amountPaidValue
        paymentVia
      }
      viewBreakDown {
        slotName
        countText
        passName
        beforePrice
        sgdPrice
        addOns {
          slotName
          sgpPrice
        }
      }
    }
  }
`
export const getConfigQuery = `
query MyQuery {
  getClientConfig {
    flyClientConfig {
      codeShareAnimationTime
      refreshInterval
    }
  }
}
`
export const getIchangiResetPasswordLink = `
query getIchangiResetPasswordLink {
  getIchangiResetPasswordLink {
     url
  }
}
`
export const loginPageContentQuery = `
query getLoginPageContent {
  getLoginPageContent {
    backgroundImage
    biometricEnabled
    logo
    socialLogin {
      icon
      iconType
      tooltip
    }
  }
}
`
export const getDetailFlight = `
query($direction:String! $flightNumber:String! $scheduledDate:String! $airlineCode:String!, $myTravelUidEnabled: Boolean, $includeTravelInfo: Boolean){
  getFlightDetails(direction: $direction, flightNumber: $flightNumber, scheduledDate: $scheduledDate, myTravelUidEnabled: $myTravelUidEnabled, includeTravelInfo: $includeTravelInfo) {
    flightInfo {
        flight_number
        actual_timestamp
        aircraft_type
        airline
        airport
        check_in_row
        current_gate
        direction
        display_checkinrowctr
        display_gate
        display_timestamp
        display_timestamp_mapping
        drop_off_door
        flight_status
        flight_type
        last_updated_timestamp
        master_flight_number
        nature
        nearest_carpark
        offblock_timestamp
        scheduled_date
        scheduled_time
        slave_flights
        technical_flight_status1
        technical_flight_status2
        terminal
        terminal_disclaimer
        via
        estimated_timestamp
        previous_gate
        display_belt
        pick_up_door
        actual_timestamp
        firstbag_timestamp
        lastbag_timestamp
        airline_details {
            code
            name
            name_zh
            name_zh_hant
            transfer_counters
            transit
            logo_url
            eligible_fast_checkin
        }
        airport_details {
            code
            name
            name_zh
            name_zh_hant
            lat
            lng
            country_code
            country
        }
        via_airport_details {
            code
            name
            name_zh
            name_zh_hant
            lat
            lng
            country_code            
        }
        origin_dep_date
        origin_dep_time
        origin_dep_country
        origin_dep_terminal
        origin_via_country
        origin_via_date
        status_mapping {
            show_gate
            listing_status_en
            listing_status_zh
            details_status_en
            details_status_zh
            status_text_color
            belt_status_en            
        }
    }
    myTravelInfo {
      is_saved
      is_passenger
      baggage_tracking
    }
    groundTransport {
      type
      name
      transportType
      subText1
      subText2
      price
      time
      url
    }
  }
  getOnlineCheckin(direction: $direction, flightNumber: $flightNumber, scheduledDate: $scheduledDate) {
    link
    linkText
  }
  getEarlyCheckin(airlineCode: $airlineCode) {
    link
    linkText
  }
}
`

export const getDetailFlightV2 = `
query($direction:String! $flightNumber:String! $scheduledDate:String! , $myTravelUidEnabled: Boolean, $includeTravelInfo: Boolean){
  getFlightDetails(direction: $direction, flightNumber: $flightNumber, scheduledDate: $scheduledDate, myTravelUidEnabled: $myTravelUidEnabled, includeTravelInfo: $includeTravelInfo) {
    flightInfo {
        flight_number
        actual_timestamp
        aircraft_type
        airline
        airport
        check_in_row
        current_gate
        direction
        display_checkinrowctr
        display_gate
        display_timestamp
        display_timestamp_mapping
        drop_off_door
        flight_status
        flight_type
        last_updated_timestamp
        master_flight_number
        nature
        nearest_carpark
        offblock_timestamp
        scheduled_date
        scheduled_time
        slave_flights
        technical_flight_status1
        technical_flight_status2
        terminal
        terminal_disclaimer
        via
        estimated_timestamp
        previous_gate
        display_belt
        pick_up_door
        actual_timestamp
        firstbag_timestamp
        lastbag_timestamp
        airline_details {
            code
            name
            name_zh
            name_zh_hant
            transfer_counters
            transit
            logo_url
            eligible_fast_checkin
        }
        airport_details {
            code
            name
            name_zh
            name_zh_hant
            lat
            lng
            country_code
            country
        }
        via_airport_details {
            code
            name
            name_zh
            name_zh_hant
            lat
            lng
            country_code            
        }
        origin_dep_date
        origin_dep_time
        origin_dep_country
        origin_dep_terminal
        origin_via_country
        origin_via_date
        est_fb_time
        status_mapping {
            show_gate
            listing_status_en
            listing_status_zh
            details_status_en
            details_status_zh
            status_text_color
            belt_status_en            
        }
    }
    myTravelInfo {
      is_saved
      is_passenger
      baggage_tracking
    }
    groundTransport {
      type
      name
      transportType
      subText1
      subText2
      price
      time
      url
    }
    flightInsurance {
        bannerType
    }
  }
  getOnlineCheckin(direction: $direction, flightNumber: $flightNumber, scheduledDate: $scheduledDate) {
    link
    linkText
  }
}
`
export const getDetailFlightForScanBoardingPassV2 = `
query($direction:String! $flightNumber:String! $scheduledDate:String! , $myTravelUidEnabled: Boolean, $includeTravelInfo: Boolean){
  getFlightDetailsForScanBoardingPass(direction: $direction, flightNumber: $flightNumber, scheduledDate: $scheduledDate, myTravelUidEnabled: $myTravelUidEnabled, includeTravelInfo: $includeTravelInfo) {
    flightInfo {
        flight_number
        actual_timestamp
        aircraft_type
        airline
        airport
        check_in_row
        current_gate
        direction
        display_checkinrowctr
        display_gate
        display_timestamp
        display_timestamp_mapping
        drop_off_door
        flight_status
        flight_type
        last_updated_timestamp
        master_flight_number
        nature
        nearest_carpark
        offblock_timestamp
        scheduled_date
        scheduled_time
        slave_flights
        technical_flight_status1
        technical_flight_status2
        terminal
        terminal_disclaimer
        via
        estimated_timestamp
        previous_gate
        display_belt
        pick_up_door
        actual_timestamp
        firstbag_timestamp
        lastbag_timestamp
        airline_details {
            code
            name
            name_zh
            name_zh_hant
            transfer_counters
            transit
            logo_url
            eligible_fast_checkin
        }
        airport_details {
            code
            name
            name_zh
            name_zh_hant
            lat
            lng
            country_code
            country
        }
        via_airport_details {
            code
            name
            name_zh
            name_zh_hant
            lat
            lng
            country_code            
        }
        origin_dep_date
        origin_dep_time
        origin_dep_country
        origin_dep_terminal
        origin_via_country
        origin_via_date
        status_mapping {
            show_gate
            listing_status_en
            listing_status_zh
            details_status_en
            details_status_zh
            status_text_color
            belt_status_en            
        }
    }
    myTravelInfo {
      is_saved
      is_passenger
      baggage_tracking
    }
    groundTransport {
      type
      name
      transportType
      subText1
      subText2
      price
      time
      url
    }
  }
  getOnlineCheckin(direction: $direction, flightNumber: $flightNumber, scheduledDate: $scheduledDate) {
    link
    linkText
  }
}
`

export const getDetailFlightForScanBoardingPass = `
query($direction:String! $flightNumber:String! $scheduledDate:String! $airlineCode:String!, $myTravelUidEnabled: Boolean, $includeTravelInfo: Boolean){
  getFlightDetailsForScanBoardingPass(direction: $direction, flightNumber: $flightNumber, scheduledDate: $scheduledDate, myTravelUidEnabled: $myTravelUidEnabled, includeTravelInfo: $includeTravelInfo) {
    flightInfo {
        flight_number
        actual_timestamp
        aircraft_type
        airline
        airport
        check_in_row
        current_gate
        direction
        display_checkinrowctr
        display_gate
        display_timestamp
        display_timestamp_mapping
        drop_off_door
        flight_status
        flight_type
        last_updated_timestamp
        master_flight_number
        nature
        nearest_carpark
        offblock_timestamp
        scheduled_date
        scheduled_time
        slave_flights
        technical_flight_status1
        technical_flight_status2
        terminal
        terminal_disclaimer
        via
        estimated_timestamp
        previous_gate
        display_belt
        pick_up_door
        actual_timestamp
        firstbag_timestamp
        lastbag_timestamp
        airline_details {
            code
            name
            name_zh
            name_zh_hant
            transfer_counters
            transit
            logo_url
            eligible_fast_checkin
        }
        airport_details {
            code
            name
            name_zh
            name_zh_hant
            lat
            lng
            country_code
            country
        }
        via_airport_details {
            code
            name
            name_zh
            name_zh_hant
            lat
            lng
            country_code            
        }
        origin_dep_date
        origin_dep_time
        origin_dep_country
        origin_dep_terminal
        origin_via_country
        origin_via_date
        status_mapping {
            show_gate
            listing_status_en
            listing_status_zh
            details_status_en
            details_status_zh
            status_text_color
            belt_status_en            
        }
    }
    myTravelInfo {
      is_saved
      is_passenger
      baggage_tracking
    }
    groundTransport {
      type
      name
      transportType
      subText1
      subText2
      price
      time
      url
    }
  }
  getOnlineCheckin(direction: $direction, flightNumber: $flightNumber, scheduledDate: $scheduledDate) {
    link
    linkText
  }
  getEarlyCheckin(airlineCode: $airlineCode) {
    link
    linkText
  }
}
`
/**
 *
 * getEarlyCheckin(airlineCode:$airlineCode flightStatus:$flightStatus){
    link
    linkText
  }
  getOnlineCheckin(flightUniqueId:$flightUniqueId){
    link
    linkText
  }
 */

export const getChangiGame = `
query($qrCodeId: String, $flightNo: String, $gameCode: String!, $crCardNo: String, $userEmail: String) {
  getChangiGames(qrCodeId: $qrCodeId, flightNo: $flightNo, gameCode: $gameCode, crCardNo: $crCardNo, userEmail: $userEmail) {
    url
  }
}
`

export const getWalletMyOrders = `
query getOrdersQuery {
  getOrders(page_size: 10) {
    image_url
    order_date
    status
    title
    total
    transaction_number
    consignments {
      checkout_type
      consignment_code
      consignment_status
      fluent_id
      image_url
      status_display {
        color
        text
      }
      title
      total_items
      total_price
    }
  }
}
`

export const getOrdersListV2 = `
query getOrdersQuery {
  getOrders(page_size: 100) {
    image_url
    order_date
    status
    title
    total
    transaction_number
    consignments {
      checkout_type
      consignment_code
      consignment_status
      fluent_id
      image_url
      status_display {
        color
        text
      }
      title
      total_items
      total_price
    }
  }
}
`

export const getWalletOrderDetails = `
query getOrderDetailsQuery ($transactionNo: String!, $ocidEmail: String!) {
  getOrderDetails(transactionNo: $transactionNo, ocidEmail: $ocidEmail) {
    deliveryCost
    earnedRewardPoints
    consignments {
      checkoutType
      collectionDetails {
        display
        terminalCode
      }
      consignmentCode
      consignmentStatus
      deliveryDetails {
        deliveryAddress {
          addressAlias
          countryISDCode
          firstName
          formattedAddress
          lastName
          phone
          postalCode
          unit
          country {
            name
          }
        }
        deliveryDate
        deliveryMethod
        deliverySlot
        shippedBy
        trackingURL
      }
      landsideCollectionDetails {
        collectionDetail
        collectionAddress {
            cellphone
            country {
                isdCode
                isocode
                name
            }
            countryISDCode
            defaultAddress
            firstName
            formattedAddress
            id
            lastName
            line1
            line2
            phone
            postalCode
            shippingAddress
            title
            titleCode
            unit
            visibleInAddressBook
        }
        deliveryDate
        collectionMethod
        deliveryTime
        contactNumber
      }
      products {
        description
        basePrice
        brandName
        productName
        quantity
        totalPrice
        variant {
          measureType
          measureUnit
          qualifier
        }
      }
      statusDisplay {
        color
        text
      }
      subTotal
      title
      totalItems
    }
    gstType
    netTotal
    paymentMethod
    redeemedPoints
    redeemedValues
    status
    subTotal
    totalItems
    totalPrice
    transactionNo
    voucherCode
    voucherDiscounts
  }
}
`
export const getWalletOrderDetails_v2 = `
query getOrderDetailsQuery_v2 ($transactionNo: String!, $ocidEmail: String!) {
  getOrderDetails_v2(transactionNo: $transactionNo, ocidEmail: $ocidEmail) {
    deliveryCost
    earnedRewardPoints
    consignments {
      checkoutType
      collectionDetails {
        display
        terminalCode
      }
      consignmentCode
      consignmentStatus
      deliveryDetails {
        deliveryAddress {
          addressAlias
          countryISDCode
          firstName
          formattedAddress
          lastName
          phone
          postalCode
          unit
          country {
            name
          }
        }
        deliveryDate
        deliveryTime
        deliveryMethod
        deliverySlot
        shippedBy
        trackingURL
      }
      landsideCollectionDetails {
        collectionDetail
        collectionAddress {
            cellphone
            country {
                isdCode
                isocode
                name
            }
            countryISDCode
            defaultAddress
            firstName
            formattedAddress
            id
            lastName
            line1
            line2
            phone
            postalCode
            shippingAddress
            title
            titleCode
            unit
            visibleInAddressBook
        }
        deliveryDate
        collectionMethod
        deliveryTime
        contactNumber
        terminalCode
      }
      products {
        description
        basePrice
        brandName
        imageUrl
        productName
        quantity
        totalPrice
        variant {
          measureType
          measureUnit
          qualifier
        }
      }
      statusDisplay {
        color
        text
      }
      subTotal
      title
      totalItems
    }
    gstType
    netTotal
    paymentMethod
    redeemedPoints
    redeemedPointsFormatted
    redeemedValues
    status
    subTotal
    totalItems
    totalPrice
    transactionNo
    voucherCode
    voucherDiscounts
  }
}
`

export const getPlaypassBookingsVersionTwo = `
query getPlaypassBookings_v2($input: PlaypassBookingInput) {
  getPlaypassBookings_v2(input: $input) {
    data {
      allowCancel
      bookingId
      bookingKey
      bookingStatus
      bookingType
      cartId
      earliestArrival
      editCount
      editMaxCount
      endDate
      endScheduled
      endScheduledTxt
      endTime
      icon
      image
      info {
        text
        url
      }
      isActive
      latestArrival
      latestCancelBefStartMins
      latestEditBefStartMins
      location
      locationCode
      packageCd
      passName
      pkgCode
      playpassEventCard {
        date
        image
        time
        title
      }
      productName
      quantity
      startDate
      startScheduled
      startScheduledTxt
      startTime
      status
      subTitle
      tags
      ticketIds
      timerTs
      title
      type
    }
    total
  }
}
`

export const getShortcutLinksExplore = `
query($isViewMore: Boolean!, $latitude: String!, $longitude: String!, $myTravelUidEnabled: Boolean, $tierCode: String!, $hasValidFlights: Boolean) {
  getShortcutLinksExplore(input: {isViewMore: $isViewMore, latitude: $latitude, longitude: $longitude, myTravelUidEnabled: $myTravelUidEnabled, tierCode: $tierCode}, hasValidFlights: $hasValidFlights) {
    userType
    shortcutLinks {
      sequenceNumber
      label
      icon
      url
      title
      buttonLabel
      navigationType
      navigationValue  
      redirect {
        redirectTarget
        utmParameters {
            content
            medium
            term
            campaign
        }
      }
      type
      tabName
      playpassPackageCd
      playpassCampaignCd
      redirectToShowOnListing
    }
  }
}
`

export const getShortcutLinksExploreV2 = `
query($latitude: String!, $longitude: String!, $tierCode: String!, $screenType: String, $isStaff: Boolean) {
  getShortcutLinksExplore_v2(input: {longitude: $longitude, latitude: $latitude, tierCode: $tierCode, isStaff: $isStaff}, screenType: $screenType) {
    userType
    shortcutLinks {
      enableForDefault
      spotlightQuickLinks {
        ...QuickLinkFields
      }
      regularFirstRowQuickLinks {
        ...QuickLinkFields
      }
      regularSecondRowQuickLinks {
        ...QuickLinkFields
      }
    }
  }
}

fragment QuickLinkFields on QuickLink {
  name
  icon
  visibility
  tag
  viewAllCategoryType
  viewAll
  navigation {
    text
    navigation {
      type
      value
    }
    redirect {
      redirectTarget
      utmParameters {
        content
        campaign
        medium
        term
      }
    }
    filterOptions {
      tagTitle
      tagName
      filterType
      sequenceNumber
      childTags
    }
  }
}
`
export const getWalletCredits = `
query getCredits {
  getCredits {
    description
    items {
      expiryDate
      totalCredits
    }
    name
    redeemableCredits
    tokenImage
    tokenType
    totalCredits
  }
}
`

export const getWalletMyPerks = `
query getWalletMyPerks ($input: GetPerkInput!) {
  getPerks_v2(input: $input) {
    active_perk_count
    perk_filter
    active_perks {
      ... on PlaypassPerk {
        perk_type
        category_code
        category_name
        expiry_dt
        token_img_url
        token_name_plural
        token_name_singular
        token_qty
        token_type
        validated_flg
      }
      ... on ChangiRewardPerk {
        perk_type
        perk_code
        catalog_code
        category_code
        category_name
        expiry_dt
        privileges_type
        token_img_url
        token_name_plural
        token_name_singular
        token_qty
        cr_vouchers_campaign
        description
        terms_and_conditions
      }
      ... on CpmsPerk {
        perk_type
        category_code
        category_name
        token_img_url
        token_name_singular
        expiry_dt
        balance
      }
      ... on InstantWinPrize {
        perk_type
        category_code
        categoryLabel
        chanceId
        description
        drawnDate
        image
        labelText
        luckyDrawCode
        redirectUrl
        subDescription
        subDescription2
        title
        validTill
      }
    }
    past_perks {
      ... on PlaypassPerk {
        perk_type
        category_code
        expiry_dt
        category_name
        token_img_url
        token_name_plural
        token_name_singular
        token_qty
        token_type
        validated_flg
      }
      ... on ChangiRewardPerk {
        perk_type
        perk_code
        catalog_code
        category_code
        category_name
        expiry_dt
        privileges_type
        token_img_url
        token_name_plural
        token_name_singular
        token_qty
        cr_vouchers_campaign
        description
        terms_and_conditions
      }
      ... on CpmsPerk {
        perk_type
        category_code
        category_name
        token_img_url
        token_name_singular
        expiry_dt
        balance
      }
    }
  }
}
`

export const getWalletMyPerksVoucherAPI = `
query getWalletMyPerks ($input: GetPerkInput!) {
  getPerks_v3(input: $input) {
    active_perk_count
    perk_filter
    active_perks {
      ... on PlaypassPerk {
        perk_type
        category_code
        category_name
        expiry_dt
        token_img_url
        token_name_plural
        token_name_singular
        token_qty
        token_type
        validated_flg
      }
      ... on ChangiRewardPerk {
        perk_type
        perk_code
        catalog_code
        category_code
        category_name
        expiry_dt
        valid_from
        privileges_type
        token_img_url
        token_name_plural
        token_name_singular
        token_qty
        redemption_mode
        cr_vouchers_campaign
        description
        terms_and_conditions
      }
      ... on CpmsPerk {
        perk_type
        category_code
        category_name
        token_img_url
        token_name_singular
        expiry_dt
        balance
      }
      ... on InstantWinPrize {
        perk_type
        category_code
        categoryLabel
        chanceId
        description
        drawnDate
        image
        labelText
        luckyDrawCode
        redirectUrl
        subDescription
        subDescription2
        title
        expiry_dt
      }
    }
    past_perks {
      ... on PlaypassPerk {
        perk_type
        category_code
        expiry_dt
        category_name
        token_img_url
        token_name_plural
        token_name_singular
        token_qty
        token_type
        validated_flg
      }
      ... on ChangiRewardPerk {
        perk_type
        perk_code
        catalog_code
        category_code
        category_name
        expiry_dt
        privileges_type
        token_img_url
        token_name_plural
        token_name_singular
        token_qty
        cr_vouchers_campaign
        description
        terms_and_conditions
      }
      ... on CpmsPerk {
        perk_type
        category_code
        category_name
        token_img_url
        token_name_singular
        expiry_dt
        balance
      }
    }
    errors
  }
}
`

export const getWalletMyPerksDetail = `
query getWalletMyPerksDetail ($input: GetPerkDetailInput!) {
  getPerkDetail (input: $input) {
    about_details
    about_link
    campaign_cd
    cancellation_booking_changes
    event_date_details
    event_end
    event_location
    event_start
    fixed_activity_cd
    package_cd
    package_end
    package_hero_img_url
    package_item_list {
      activity_cd
      item_fixed_scheme_cd
      item_name
      item_sequence_no
      item_thumbnail_url
      package_type
    }
    package_listing_img_url
    package_name
    package_start
    package_tags
    package_thumbnail_url
    price
    package_type
    send_email_flag
    tnc_link
    token_type
    visible_flag
  }
}
`

export const getPlayPassDineShopV2 = `
query getEPICPerks($playpassInput: PlaypassPackageInput, $parkingInput: GetParkingTileInput) {
    getPlaypassPackages(input: $playpassInput) {
        creditedTokens {
            tokenType
            expiryDt
            tokenQty
            tokenNameSingular
            tokenNamePlural
            tokenImgUrl
            header
            packageCodes
        }
        packages {
            code
            campaignCode
            listingImageUrl
            name
            tags
            heroUrl
            thumbnailUrl
            type
            startTime
            endTime
            tokenType
            visibleFlag
            header
            mechanics
        }
    }

    getParkingTiles(input: $parkingInput) {
        aemParkingTile {
            locationDisplaying
            image
            tag
            title
            description
            credited
            cta {
                text 
                navigationLink {
                    type
                    value
                }
                aemUtmRedirect {
                    redirectTarget
                    utmParameters {
                        content
                        medium
                        term
                        campaign
                    }
                }
            }
        }
        cpmsParkingTile {
            direction
            parkingIcon
            progressBar
            title1
            title2
            subtitle
            stamp
            spentAmount
        }
    }

    getCM24RewardsInfo {
        totalCarChances
        totalMilChances
    }
}
`

export const getWalletMyPerksDetailV2 = `
query getPerkDetail_v2 ($input: GetPerkDetailInput_v2!) {
  getPerkDetail_v2 (input: $input) {
    changi_reward_detail {
      remarks
      valid_from
      valid_to
      voucher_type_code
      voucher_type_name
      voucher_url
      voucher_used_on
      is_used
      mark_timestamp
      category_code
      terms
      eligible_outlet {
        outlet_code
        outlet_name
      }
      valid_datetime_msg
      voucher_type_description
    }
    playpass_detail {
      about_details
      about_link
      campaign_cd
      cancellation_booking_changes
      entry_requirements
      event_date_details
      event_end
      event_location
      event_start
      fixed_activity_cd
      package_cd
      package_end
      package_hero_img_url
      package_item_list {
        activity_cd
        item_fixed_scheme_cd
        item_name
        item_sequence_no
        item_thumbnail_url
        package_type
      }
      package_listing_img_url
      package_name
      package_start
      package_tags
      package_thumbnail_url
      package_type
      price
      send_email_flag
      tnc_link
      token_type
      visible_flag
    }
    gamification_prize_detail {
      id
      title
      image
      description
      subDescription
      subDescription2
      labelText
      promoCode
      createdDt
      redeemDt
      validTill
      redeemable
      categoryCode
      redemptionMode
      qty
      eligibleOutlet
    }
  }
}
`

export const getUpcomingEventV2 = `
 query getUpcomingEvent ($input: PlaypassBookingInput!) {
  getPlaypassBookings_v2(input: $input) {
    data {
      packageCd
      type
      tags
      bookingId
      endDate
      endTime
      icon
      location
      locationCode
      productName
      quantity
      startDate
      startTime
      status
      subTitle
      tags
      title
      type
      playpassEventCard {
        image
        title
        date
        time
        imageV2
        titleV2
      }
      bookingKey
      earliestArrival
      latestArrival
      bookingStatus
      isActive
      upcomingStartScheduledTxt
      upcomingEndScheduledTxt
    }
  }
 }
`

export const getJustForYouCarousel = `
query {
  getExploreJustForYou {
    sectionTitle
    data {
        imageUrl
        url
        brandName
        productName
        salePrice {
            currency
            value
        }
        mainPrice {
            currency
            value
        }
        ribbonText
        navigationType
        navigationValue
        redirect {
          redirectTarget
          utmParameters {
              content
              medium
              term
              campaign
          }
        }
     }
  }
}
`

export const getEditUrlForWalletMyTravel = `
query getTravelBookingDetailEditUrl ($input: GetTravelBookingDetailEditUrlInput!) {
  getTravelBookingDetailEditUrl (input: $input){
    url
  }
}
`

export const getCarParkAvailability = `
query getCarparkAvailability{
  getCarparkAvailability{
    terminalCode
    terminalName
    name
    name_zh
    general_spaces
    accessible_spaces
    lat
    lng
    message
    message_zh
  }
}
`

export const getCarParkAvailabilityV2 = `
query getCarparkAvailability_v2($input: GetCarparkAvailabilityInput){
  getCarparkAvailability_v2(input: $input){
    data {
      accessible_spaces
      general_spaces
      lat
      lng
      message
      message_zh
      name
      name_zh
      terminalCode
      terminalName
    }
  }
}
`

export const getUpComingFlight = `
query getUpcommingFlights($myTravelUidEnabled: Boolean) {
  getUpcommingFlights(myTravelUidEnabled: $myTravelUidEnabled) {
    flightMessage
    flightTitle
    flightInfo {
        flight_number
        direction
    }
    flightContent {
        priorityIndex
        applicabilitySummaryTimeline
        imageSummaryTimeline
        titleSummaryTimeline
        linkTextSummaryTimeline
        summaryTimelineNavigation {
            type
            value
        }
        summaryTimelineRedirect {
          redirectTarget
          utmParameters {
              content
              medium
              term
              campaign
          }
        }
    }
  }
}
`
export const getUpComingFlightV2 = `
query getUpcommingFlights_v2($input: GetUpcomingFlightsInput) {
  getUpcommingFlights_v2(input: $input) {
    data {
      flightTitle
      flightMessage
      flightInfo {
            flight_number
            direction
        }
      flightContent {
        ... on GroundTransportOption {
            type
            name
            transportType
            subText1
            subText2
            price
            time
            url
        }
        ... on UpCommingFlightContent {
            type
            tileID
            priorityIndex
            applicabilitySummaryTimeline
            imageSummaryTimeline
            titleSummaryTimeline
            linkTextSummaryTimeline
            summaryTimelineNavigation {
                type
                value
            }
            summaryTimelineRedirect {
              redirectTarget
              utmParameters {
                  content
                  medium
                  term
                  campaign
              }
            }
        }
      }
    }
  }
}
`
export const getExploreChangi = `
query getExploreChangi ($category: String!, $categoryCode: [String!], $username: String!, $pageNumber: Int!, $pageSize: Int!, $date: String!, $locations: [String!], $pkgCode: String!) {
  getExploreChangi(category: $category, categoryCode: $categoryCode, username: $username, pageNumber: $pageNumber, pageSize: $pageSize, date: $date, locations: $locations, pkgCode: $pkgCode) {
    categories {
      category
      categoryCode
    }
    dataByCategory {
      aboutDetails
      aboutLink
      dateDetails
      endDate
      entryRequirements
      eventEnd
      eventLocation
      eventStart
      imageHero
      imageListing
      imageThumbnail
      imageUrl
      navigationType
      navigationValue
      redirect {
        redirectTarget
        utmParameters {
            content
            medium
            term
            campaign
        }
      }
      operatingHours
      packageCode
      campaignCode
      price
      source
      startDate
      tags
      termAndConditions
      title
      tokenType
      earnCrPointFlg
      playpassRibbonList {
        image
        text
      }
    }
    isPerksAvailable
    perkHeroImage
    heroImage
    perkTexts
    paging {
      pageNumber
      pageSize
      totalItemCounts
    }
  }
}
`
// CP-1122 fix
// export const searchAllQuery = `
// query searchAll($text: String!, $page_number: Int, $page_size: Int, $flight_filter: SearchFilter, $facilities_page_size: Int) {
//   cities:search(category: AIRPORTS, text: $text, filter: {}, page_number: $page_number, page_size: $page_size) {
//     items {
//       ... on AirportDetail {
//         score
//         name_zh
//         name_zh_hant
//         code
//         country_code
//         lat
//         lng
//         name
//       }
//     }
//     page_number
//     page_size
//     total
//   }
//   airlines:search(category: AIRLINES, text: $text, filter: {}, page_number: $page_number, page_size: $page_size) {
//     items {
//       ... on AirlineDetail {
//         score
//         name_zh
//         name_zh_hant
//         code
//         logo_url
//         name
//         transfer_counters
//         transit
//       }
//     }
//     page_number
//     page_size
//     total
//   }
//   flights:search(category: FLIGHTS, text: $text, filter: $flight_filter, page_number: $page_number, page_size: $page_size) {
//     items {
//       ... on Flight {
//         score
//         aircraft_type
//         airline
//         actual_timestamp
//         airport
//         check_in_row
//         current_gate
//         direction
//         display_belt
//         display_checkinrowctr
//         display_gate
//         display_timestamp
//         drop_off_door
//         estimated_timestamp
//         firstbag_timestamp
//         flight_number
//         flight_status
//         flight_type
//         last_updated_timestamp
//         lastbag_timestamp
//         master_flight_number
//         nature
//         nearest_carpark
//         offblock_timestamp
//         origin_dep_country
//         origin_dep_date
//         origin_dep_terminal
//         origin_dep_time
//         origin_via_country
//         origin_via_date
//         pick_up_door
//         previous_gate
//         scheduled_date
//         scheduled_time
//         slave_flights
//         technical_flight_status1
//         technical_flight_status2
//         terminal
//         via
//         via_airport_details {
//           code
//           country_code
//           lat
//           lng
//           name
//           name_zh
//           name_zh_hant
//         }
//         status_mapping {
//           belt_status_en
//           details_status_en
//           details_status_zh
//           listing_status_en
//           listing_status_zh
//           status_text_color
//           show_gate
//         }
//         airline_details {
//           code
//           logo_url
//           name
//           name_zh
//           name_zh_hant
//           transfer_counters
//           transit
//         }
//         airport_details {
//           code
//           country_code
//           lat
//           lng
//           name
//           name_zh
//           name_zh_hant
//         }
//       }
//     }
//     isDepartureFightData
//     page_number
//     page_size
//     total
//   }
//   dines:search(category: TENANTS, text: $text, filter: {type: "dine"}, page_number: $page_number, page_size: $page_size) {
//     items {
//       ... on TenantDetail {
//         id
//         name
//         aem_location {
//           filterType
//           sequenceNumber
//           tagName
//           tagTitle
//         }
//         area {
//           filterType
//           sequenceNumber
//           tagName
//           tagTitle
//         }
//         backgroundImageUrl
//         categories {
//           filterType
//           sequenceNumber
//           tagName
//           tagTitle
//         }
//         category
//         changiEats
//         changiEatsURL
//         changiPay
//         dietary {
//           filterType
//           sequenceNumber
//           tagName
//           tagTitle
//         }
//         heroCarouselImage
//         image
//         iscAvailability
//         iscURL
//         keywords
//         link
//         local_id
//         location
//         location_display
//         location_list {
//           always_on
//           area
//           caption
//           caption_zh
//           caption_zh_hant
//           description
//           description_zh
//           description_zh_hant
//           email
//           hours
//           hours_zh
//           hours_zh_hant
//           mapname
//           shop
//           tel
//           terminal
//           unit_no
//           x
//           y
//         }
//         logoImage
//         logoImageUrl
//         longImageUrl
//         openedDate
//         price {
//           filterType
//           sequenceNumber
//           tagName
//           tagTitle
//         }
//         priceStrSearch
//         shortImageUrl
//         source
//         tenantId
//         tenantLocation
//         title
//         type
//         viewMenuURL
//       }
//     }
//     page_number
//     page_size
//     total
//   }
//   shops:search(category: TENANTS, text: $text, filter: {type: "shop"}, page_number: $page_number, page_size: $page_size) {
//     items {
//       ... on TenantDetail {
//         id
//         name
//         aem_location {
//           filterType
//           sequenceNumber
//           tagName
//           tagTitle
//         }
//         area {
//           filterType
//           sequenceNumber
//           tagName
//           tagTitle
//         }
//         backgroundImageUrl
//         categories {
//           filterType
//           sequenceNumber
//           tagName
//           tagTitle
//         }
//         category
//         changiEats
//         changiEatsURL
//         changiPay
//         dietary {
//           filterType
//           sequenceNumber
//           tagName
//           tagTitle
//         }
//         heroCarouselImage
//         image
//         iscAvailability
//         iscURL
//         keywords
//         link
//         local_id
//         location
//         location_display
//         location_list {
//           always_on
//           area
//           caption
//           caption_zh
//           caption_zh_hant
//           description
//           description_zh
//           description_zh_hant
//           email
//           hours
//           hours_zh
//           hours_zh_hant
//           mapname
//           shop
//           tel
//           terminal
//           unit_no
//           x
//           y
//         }
//         logoImage
//         logoImageUrl
//         longImageUrl
//         openedDate
//         price {
//           filterType
//           sequenceNumber
//           tagName
//           tagTitle
//         }
//         priceStrSearch
//         shortImageUrl
//         source
//         tenantId
//         tenantLocation
//         title
//         type
//         viewMenuURL
//       }
//     }
//     page_number
//     page_size
//     total
//   }
//   facilities:search(category: FACILITIES, text: $text, filter: {}, page_number: $page_number, page_size: $facilities_page_size) {
//     items {
//       ... on FacilityDetail {
//         contentId
//         cagOverride
//         customerEligibilityShorten
//         expiryDate
//         customerEligibility {
//           filterType
//           sequenceNumber
//           tagName
//           tagTitle
//         }
//         area {
//           filterType
//           sequenceNumber
//           tagName
//           tagTitle
//         }
//         flow {
//           filterType
//           sequenceNumber
//           tagName
//           tagTitle
//         }
//         image
//         location {
//           filterType
//           sequenceNumber
//           tagName
//           tagTitle
//         }
//         locationDescription {
//           filterType
//           sequenceNumber
//           tagName
//           tagTitle
//         }
//         locationDisplayText
//         name
//         navigation {
//           type
//           value
//         }
//         sequenceNumber
//         startDate
//         title
//         type
//         userGroup {
//           filterType
//           sequenceNumber
//           tagName
//           tagTitle
//         }
//       }
//     }
//     page_number
//     page_size
//     total
//   }
//   events:search(category: EVENTS, text: $text, page_number: $page_number, page_size: $page_size) {
//     items {
//       ... on EventDetail {
//           score
// 			id
//             about_details
//             about_link
//             campaign_cd
//             cancellation_booking_changes
//             code
//             entry_requirements
//             event_date_details
//             event_end
//             event_location
//             event_start
//             fixed_activity_cd
//             fixed_scheme_cd
//             package_end
//             package_hero_img_url
//             package_item_list
//             package_listing_img_url
//             package_name
//             package_tags
//             package_thumbnail_url
//             package_type
//             price
//             start_time
//             tnc_link
//             token_type
//             type
//             type_code
//             visible_flag
//       }
//     }
//     page_number
//     page_size
//     total
//   }
//   attractions:search(category: ATTRACTIONS, text: $text, page_number: $page_number, page_size: $facilities_page_size) {
//     items {
//       ... on AttractionDetail {
//         attractionId
//           cagOverride
//           category
//           expiryDate
//           id
//           image
//           isNew
//           locationDisplayText
//           operatingHours
//           sequenceNumber
//           startDate
//           title
//           underPromotion
//           customerEligibility {
//             filterType
//             sequenceNumber
//             tagName
//             tagTitle
//           }
//           location {
//             filterType
//             sequenceNumber
//             tagName
//             tagTitle
//           }
//           locationDescription {
//             filterType
//             sequenceNumber
//             tagName
//             tagTitle
//           }
//           navigation {
//             type
//             value
//           }
//           specialFlag {
//             filterType
//             sequenceNumber
//             tagName
//             tagTitle
//           }
//           userGroupTag {
//             filterType
//             sequenceNumber
//             tagName
//             tagTitle
//           }
//       }
//     }
//     page_number
//     page_size
//     total
//   }
// }
// `
export const searchAllQuery = `
query searchAll($text: String!, $page_number: Int, $page_size: Int, $flight_filter: SearchFilter, $facilities_page_size: Int) {
  search(category: ALL, text: $text, filter: $flight_filter, facilities_page_size: $facilities_page_size, page_number: $page_number, page_size: $page_size, attractions_page_size: $facilities_page_size) {
    groups {
      cities {
        items {
          score
          name
          name_zh
          name_zh_hant
          code
          country_code
          lat
          lng
        }
      }
      airlines {
        items {
          score
          name_zh
          name_zh_hant
          code
          logo_url
          name
          transfer_counters
          transit
        }
      }
      flights {
        items {
          score
          aircraft_type
          airline
          actual_timestamp
          airport
          check_in_row
          current_gate
          direction
          display_belt
          display_checkinrowctr
          display_gate
          display_timestamp
          display_timestamp_mapping
          drop_off_door
          estimated_timestamp
          firstbag_timestamp
          flight_number
          flight_status
          flight_type
          last_updated_timestamp
          lastbag_timestamp
          master_flight_number
          nature
          nearest_carpark
          offblock_timestamp
          origin_dep_country
          origin_dep_date
          origin_dep_terminal
          origin_dep_time
          origin_via_country
          origin_via_date
          pick_up_door
          previous_gate
          scheduled_date
          scheduled_time
          slave_flights
          technical_flight_status1
          technical_flight_status2
          terminal
          via
          via_airport_details {
            code
            country_code
            lat
            lng
            name
            name_zh
            name_zh_hant
          }
          status_mapping {
            belt_status_en
            details_status_en
            details_status_zh
            listing_status_en
            listing_status_zh
            status_text_color
            show_gate
          }
          airline_details {
            code
            logo_url
            name
            name_zh
            name_zh_hant
            transfer_counters
            transit
            eligible_fast_checkin
          }
          airport_details {
            code
            country
            country_code
            lat
            lng
            name
            name_zh
            name_zh_hant
          }
        }
        isDepartureFightData
      }
      dines {
        items {
          aem_location {
            filterType
            sequenceNumber
            tagName
            tagTitle
          }
          aemTenantDetails {
            dietary {
              filterType
              sequenceNumber
              tagName
              tagTitle
            }
          }
          area {
            filterType
            sequenceNumber
            tagName
            tagTitle
          }
          area_display
          backgroundImageUrl
          categories {
            filterType
            sequenceNumber
            tagName
            tagTitle
          }
          category
          changiEats
          changiEatsURL
          changiPay
          heroCarouselImage
          id
          image
          iscAvailability
          iscURL
          keywords
          link
          local_id
          location
          location_display
          location_list {
            always_on
            area
            caption
            caption_zh
            caption_zh_hant
            description
            description_zh
            description_zh_hant
            email
            hours
            hours_zh
            hours_zh_hant
            mapname
            shop
            tel
            terminal
            unit_no
            x
            y
          }
          logoImage
          logoImageUrl
          longImageUrl
          name
          openedDate
          price {
            filterType
            sequenceNumber
            tagName
            tagTitle
          }
          priceStrSearch
          shortImageUrl
          source
          tenantId
          tenantLocation
          title
          type
          viewMenuURL
        }
      }
      shops {
        items {
          id
          name
          aem_location {
            filterType
            sequenceNumber
            tagName
            tagTitle
          }
          aemTenantDetails {
            dietary {
              filterType
              sequenceNumber
              tagName
              tagTitle
            }
          }
          area {
            filterType
            sequenceNumber
            tagName
            tagTitle
          }
          area_display
          backgroundImageUrl
          categories {
            filterType
            sequenceNumber
            tagName
            tagTitle
          }
          category
          changiEats
          changiEatsURL
          changiPay
          dietary {
            filterType
            sequenceNumber
            tagName
            tagTitle
          }
          heroCarouselImage
          image
          iscAvailability
          iscURL
          keywords
          link
          local_id
          location
          location_display
          location_list {
            always_on
            area
            caption
            caption_zh
            caption_zh_hant
            description
            description_zh
            description_zh_hant
            email
            hours
            hours_zh
            hours_zh_hant
            mapname
            shop
            tel
            terminal
            unit_no
            x
            y
          }
          logoImage
          logoImageUrl
          longImageUrl
          openedDate
          price {
            filterType
            sequenceNumber
            tagName
            tagTitle
          }
          priceStrSearch
          shortImageUrl
          source
          tenantId
          tenantLocation
          title
          type
          viewMenuURL
        }
      }
      facilities {
        items {
          contentId
          cagOverride
          customerEligibilityShorten
          expiryDate
          customerEligibility {
            filterType
            sequenceNumber
            tagName
            tagTitle
          }
          area {
            filterType
            sequenceNumber
            tagName
            tagTitle
          }
          flow {
            filterType
            sequenceNumber
            tagName
            tagTitle
          }
          image
          location {
            filterType
            sequenceNumber
            tagName
            tagTitle
          }
          locationDescription {
            filterType
            sequenceNumber
            tagName
            tagTitle
          }
          locationDisplayText
          name
          navigation {
            type
            value
          }
          sequenceNumber
          startDate
          title
          type
          userGroup {
            filterType
            sequenceNumber
            tagName
            tagTitle
          }
        }
      }
      events {
        items {
          score
          id
          about_details
          about_link
          campaign_cd
          cancellation_booking_changes
          code
          entry_requirements
          event_date_details
          event_end
          event_location
          event_start
          fixed_activity_cd
          fixed_scheme_cd
          package_end
          package_hero_img_url
          package_item_list
          package_listing_img_url
          package_name
          package_tags
          package_thumbnail_url
          package_type
          price
          start_time
          tnc_link
          token_type
          type
          type_code
          visible_flag
          earn_cr_points_flg
        }
      }
      attractions {
        items {
          attractionId
          cagOverride
          category
          expiryDate
          id
          image
          isNew
          locationDisplayText
          operatingHours
          sequenceNumber
          startDate
          title
          underPromotion
          customerEligibility {
            filterType
            sequenceNumber
            tagName
            tagTitle
          }
          location {
            filterType
            sequenceNumber
            tagName
            tagTitle
          }
          locationDescription {
            filterType
            sequenceNumber
            tagName
            tagTitle
          }
          navigation {
            type
            value
          }
          specialFlag {
            filterType
            sequenceNumber
            tagName
            tagTitle
          }
          userGroupTag {
            filterType
            sequenceNumber
            tagName
            tagTitle
          }
        }
      }
    }
  }
}
`

export const searchAllV2Query = `
query searchAllV2($text: String!, $page_number: Int, $page_size: Int, $filter: SearchFilter) {
  search_v2(category: EVERYTHING, text: $text, filter: $filter, page_number: $page_number, page_size: $page_size) {
    total_flights
    total_airlines
    total_airports
    total
    isDepartureFightData
    page_number
    page_size
    facets {
        locationList
        areaList
        dietary
        availability
        cuisine
        categories {
            shops
            dines
            events
            attractions
            facilities
        }
    }
    items {
      ... on TenantDetailV2 {
          score
          id
          source
          title
          title_zh
          type
          locationDisplayText
          location
          image
          operatingHours
          category
          category_zh
          tags
          tags_zh
          iscurl
          chopeUrl
          dietary
          dietary_zh
          availability
          availability_zh
          cuisine
          cuisine_zh
          areaList
          locationList
      }
      ... on FacilityDetailV2 {
          score
          id
          contentId
          source
          title
          locationDisplayText
          location
          image
          tags
          tags_zh
          availability
          availability_zh
          areaList
          navigation {
              type
              value
          }
          redirect {
              redirectTarget
              utmParameters {
                  content
                  medium
                  term
                  campaign
              }
          }
          app_feature
          base_description
          locationList
      }
      ... on AttractionDetailV2 {
          score
          id
          source
          title
          locationDisplayText
          location
          image
          category
          free
          areaList
          navigation {
              type
              value
          }
          redirect {
              redirectTarget
              utmParameters {
                  content
                  medium
                  term
                  campaign
              }
          }
          locationList
      }
      ... on EventDetailV2 {
          score
          id
          code
          source
          title
          locationDisplayText
          location
          image
          price
          event_start
          event_end
          token_type
          type
          visible_flag
          package_type
          type_code
          earn_cr_points_flg
          tags
          tags_zh
          availability
          availability_zh
          price_text
          free
          areaList
          locationList
      }
      ... on CAContentDetailV2 {
          score
          id
          source
          site
          path
          pageTitle
          jcrtitle
          jcrdescription
      }
      ... on MapPoiV2 {
          score
          id
          source
          title
          title_zh
          category
          map_poi
          image
          location
          areaList
          locationDisplayText
      }
      ... on IscDetailV2 {
          score
          id
          source
          title
          title_zh
          category
          brand
          orders
          saleprice
          originalprice
          pageurl
          newproduct
          thumbnailurl
          zhprice
          inventory
          promotion
          zhdiscountedprice
          navigation {
              type
              value
          }
          redirect {
              redirectTarget
              utmParameters {
                  content
                  medium
                  term
                  campaign
              }
          }
      }
    }
  }
}
`

export const getSettingUserPreferences = `
query getUserPreferences ($category: SubscriptionCategory!, $device_id: String!, $last_login_id: String,  $email: String, $subscription_group_ids:[SubscriptionGroupId]!){
  getUserPreferences(category: $category, device_id: $device_id, last_login_id: $last_login_id,  email: $email, subscription_group_ids: $subscription_group_ids) {
    subscription_group_id
    subscription_status
    subscription_type
    language
  }
}
`
export const getSettingUserPreferencesV2 = `
query getUserPreferences ($category: SubscriptionCategory!, $device_id: String!, $last_login_id: String,  $email: String, $subscription_group_ids:[SubscriptionGroupId]!){
  getUserPreferences_v2(category: $category, device_id: $device_id, last_login_id: $last_login_id,  email: $email, subscription_group_ids: $subscription_group_ids) {
    subscription_group_id
    subscription_status
    subscription_type
    language
  }
}
`
export const putUserPreferences = `
mutation ($device_id: String!, $last_login_id: String, $subscription_groups: [UserPreferenceInput]!, $language: Language!, $email: String, $phone_number: String) {
  putUserPreferences(device_id: $device_id, last_login_id: $last_login_id, subscription_groups: $subscription_groups, language: $language, email: $email, phone_number: $phone_number) {
    message
    status
  }
}
`
export const putUserPreferencesV2 = `
mutation ($device_id: String!, $last_login_id: String, $subscription_groups: [UserPreferenceInput]!, $language: Language!, $email: String, $phone_number: String) {
  putUserPreferences_v2(device_id: $device_id, last_login_id: $last_login_id, subscription_groups: $subscription_groups, language: $language, email: $email, phone_number: $phone_number) {
    message
    status
  }
}
`
export const getDeepLinkQuery = `
query getDeepLink ($stateCode: String!, $params: String!, $input: DeepLinkInput){
  getDeepLink(stateCode: $stateCode, params: $params, input: $input) {
    stateCode
    redirectUri
    basicAuth {
      password
      username
    }
  }
}
`

export const getDeepLinkV2Query = `
query getDeepLink_v2 ($stateCode: String!, $params: String!, $input: DeepLinkInput){
  getDeepLink_v2(stateCode: $stateCode, params: $params, input: $input) {
    stateCode
    redirectUri
    basicAuth {
      password
      username
    }
    requireLogin
  }
}
`

export const notificationQuery = `
query NotificationQuery ($targetId: String!, $category: String, $filter: String, $categories: [String]){
  getUserNotifications(targetId: $targetId, category: $category, filter: $filter, categories: $categories) 
  {
    id
    title
    label
    source
    message
    is_read
    category
    target_id
    type
    created_dt
    expired_dt
    extra_json_data
    last_updated_dt
    subscription_group_id
  }
}`

export const markNotificationAsReadQuery = `
mutation MarkNotificationAsReadQuery ($idNotification: String!){
  markNotificationAsRead(id: $idNotification, isRead: true) {
    message
    status
  }
}
`

export const markAllNotificationsAsReadQuery = `
query MarkNotificationAsReadQuery ($input: MarkNotificationsAsReadInput){
  markNotificationsAsRead(input: $input) {
    message
    status
  }
}
`

export const getCarParkFareDetail = `
query getCarparkFareDetails {
  getCarparkFareDetails {
    terminalCode
    terminalName
    name
    name_zh
    message
    message_zh
    limitMin
    hour
    min
    hourFare
    minFare
    cappedAt
    capType
  }
}
`

export const getCarParkFareDetailV2 = `
query getCarparkFareDetails_v2($input: GetCarparkFareDetailsInput){
  getCarparkFareDetails_v2(input: $input){
    data {
      capType
      cappedAt
      hour
      hourFare
      limitMin
      message
      message_zh
      min
      minFare
      name
      name_zh
      terminalCode
      terminalName
    }
  }
}
`
export const updatePersonaTags = `
mutation ($input: PersonaTagInput) {
  updatePersonaTags(input: $input) {
    message
    status
  }
}
`

export const onNotificationPublished = `
subscription onNotificationPublished ($filter: String!) {  
  onNotificationPublished (filter: $filter) {
    category
    type
    title
    target_id
    subscription_group_id
    source
    message
    label
    is_read
    id
    expired_dt
    created_dt
    last_updated_dt
    extra_json_data
  }
}
`
export const notificationsCountQuery = `
query NotificationsCountQuery ($targetId: String!) {
  getNotificationCount(targetId: $targetId) {
      count
  }
}
`
export const getMyCars = `
query getMyCars ($carNo: String!) {
  getMyCars(carNo: $carNo) {
    status
    items {
        slotName
        level
        img_Url
        licenseNumber
        slotID
        inTime
        buildingName
    }
    type
    totalCount
}
}
`
export const getMyCarLocation = `
query getMyCarLocation ($slotID: String!, $level: String!) {
  getMyCarLocation(slotID: $slotID, level: $level) {
    status
    location {
        floorCode
        floorName
        mapImgURL
        x1
        y1
        x2
        y2
    }
    type
}
}
`

export const getLocationDirectionQuery = `
query getLocationDirections ($input: GetDirectionsInput!) {
  getLocationDirections(input: $input) {
    data
  }
}
`

export const getYourBenefitsCardsQuery = `
query getYourBenefitsCards {
  getYourBenefitsCards {
    imgUrl
    navigationType
    sequenceNumber
    navigationValue
    redirect {
      redirectTarget
      utmParameters {
          content
          medium
          term
          campaign
      }
    }
    subCopyText
    title
  }
}
`
export const getYourBenefitsCardsVoucherQuery = `
query getYourBenefitsCards($input: GetBenefitCardInput) {
  getYourBenefitsCards_v3(input: $input) {
    imgUrl
    navigationType
    sequenceNumber
    navigationValue
    redirect {
      redirectTarget
      utmParameters {
          content
          medium
          term
          campaign
      }
    }
    subCopyText
    title
    forStaff
  }
}
`

export const processCrTransaction = `
query processCrTransaction {
  processCrTransaction
}
`

export const getShortcutLinksAccount = `
query getShortcutLinksAccount {
  getShortcutLinksAccount {
    cagOverride
    icon
    label
    navigationType
    navigationValue
    redirect {
      redirectTarget
      utmParameters {
          content
          medium
          term
          campaign
      }
    }
    sequenceNumber
    title
    count
  }
}
`

export const subScriptionCarParkAvailability = `
subscription carparkAvailabilityUpdates {
  carparkAvailabilityUpdates {
    terminalName
    terminalCode
    name_zh
    name_zh_old
    name_old
    name
    message_zh
    message_zh_old
    message
    message_old
    lng
    lat
    general_spaces
    accessible_spaces
  }
}
`
export const getToiletList = `
query {
  getToiletList {
    name
    data {
      name
      data
    }
  }
}
`
export const searchFacilitiesAndServices = `
query searchTenants ($text: String!, $page_number: Int, $page_size: Int) {
  search(category: FACILITIES, text: $text, page_number: $page_number, page_size: $page_size) {
    items {
      ... on FacilityDetail {
        contentId
        cagOverride
        customerEligibilityShorten
        expiryDate
        customerEligibility {
          filterType
          sequenceNumber
          tagName
          tagTitle
        }
        area {
          filterType
          sequenceNumber
          tagName
          tagTitle
        }
        flow {
          filterType
          sequenceNumber
          tagName
          tagTitle
        }
        image
        location {
          filterType
          sequenceNumber
          tagName
          tagTitle
        }
        locationDescription {
          filterType
          sequenceNumber
          tagName
          tagTitle
        }
        locationDisplayText
        name
        navigation {
          type
          value
        }
        sequenceNumber
        startDate
        title
        type
        userGroup {
          filterType
          sequenceNumber
          tagName
          tagTitle
        }
      }
    }
    page_number
    page_size
    total
  }
}
`

export const sendOtp = `
query sendOtp($phoneNumber: String, $email: String) {
  sendOtp(input: {phoneNumber: $phoneNumber, email: $email}) {
    result
    status
  }
}
`

export const verifyOtp = `
query verifyOtp ($input: VerifyOtpInput) {
  verifyOtp(input: $input) {
    result
    status
  }
}
`

export const deleteNotificationsQuery = `
query MyQuery($input: DeleteNotificationInput) {
  deleteNotification(input: $input) {
    message
    status
  }
}
`

export const searchAttractionsQuery = `
query search ($text: String!, $page_number: Int, $page_size: Int) {
  search(category: ATTRACTIONS, text: $text, page_number: $page_number, page_size: $page_size) {
    items {
      ... on AttractionDetail {
          attractionId
          cagOverride
          category
          expiryDate
          id
          image
          isNew
          locationDisplayText
          operatingHours
          sequenceNumber
          startDate
          title
          underPromotion
          customerEligibility {
            filterType
            sequenceNumber
            tagName
            tagTitle
          }
          location {
            filterType
            sequenceNumber
            tagName
            tagTitle
          }
          locationDescription {
            filterType
            sequenceNumber
            tagName
            tagTitle
          }
          navigation {
            type
            value
          }
          specialFlag {
            filterType
            sequenceNumber
            tagName
            tagTitle
          }
          userGroupTag {
            filterType
            sequenceNumber
            tagName
            tagTitle
          }
      }
    }
    page_number
    page_size
    total
  }
}
`

export const searchEventsQuery = `
query search ($text: String!, $page_number: Int, $page_size: Int) {
  search(category: EVENTS, text: $text, page_number: $page_number, page_size: $page_size) {
    items {
      ... on EventDetail {
        id
            about_details
            about_link
            campaign_cd
            cancellation_booking_changes
            code
            entry_requirements
            event_date_details
            event_end
            event_location
            event_start
            fixed_activity_cd
            fixed_scheme_cd
            package_end
            package_hero_img_url
            package_item_list
            package_listing_img_url
            package_name
            package_tags
            package_thumbnail_url
            package_type
            price
            start_time
            tnc_link
            token_type
            type
            type_code
            visible_flag
            earn_cr_points_flg
      }
    }
    page_number
    page_size
    total
  }
}
`
export const dineReservationQuery = `
query getDineReservations {
  getDineReservations {
      bookTableCTA
      changiEats
      changiEatsURL
      changiPay
      description
      descriptionZh
      descriptionZhHant
      heroCarouselImage
      id
      localRef
      locationDetails{
          alwaysOn
          area
          contact
          description
          descriptionZh
          descriptionZhHant
          level
          localRef 
          mapName
          openCloseStatus {
              colorCode
              status
          }
          terminal
          timingsInfo {
              day
              timings
          }
          unitNo
      }
      logo
      price
      tags
      title
      titleZh
      titleZhHant
      viewMenuURL
      websiteLink
      websiteLinkZh
      chopeUrl
      exploreCategories {
          filterType
          image
          sequenceNumber
          tagName
          tagTitle
      }
  }
}
`

export const shortLinkQuantityVoucherQueryV2 = `
query shortLinkQuantityQuery($getPerksInput: GetPerkInput, $myTravelUidEnabled: Boolean) {
    getSavedFlights(myTravelUidEnabled: $myTravelUidEnabled) {
      username
    }
    getCredits {
      name
    }
    getOrders(page_size: 1) {
      total_results
    }
    getPerks_v3(input: $getPerksInput) {
      active_perk_count
    }
    getPlaypassBookings_2(input: { scope: "all", isCount: true }) {
      playpass_count
    }
    getCarpassBookings(input: { isCount: true }) {
      carpass_count
    }
}
`

export const getResultScreenByFilterDineV2 = `
query getResultScreenByFilterDine_v2 ($filter: [SelectedFilterParams], $orderBy: [TenantOrderByInput], $page_number: Int, $size: Int) {
  getResultScreenByFilterDine_v2 (filter: $filter, orderBy: $orderBy, page_number: $page_number, size: $size) {
      nodes {
          aemTenantDetails {
              additionalTileImages
              area {
                  filterType
                  sequenceNumber
                  tagName
                  tagTitle
              }
              availability {
                  filterType
                  sequenceNumber
                  tagName
                  tagTitle
              }
              backgroundImage
              categories
              cuisine {
                  filterType
                  sequenceNumber
                  tagName
                  tagTitle
              }
              dietary {
                  filterType
                  sequenceNumber
                  tagName
                  tagTitle
              }
              location {
                  filterType
                  sequenceNumber
                  tagName
                  tagTitle
              }
              logoImage
              longImage
              openedDate
              price
              rewards {
                  dismissible
                  displayLocation
                  expiryDate
                  icon
                  link
                  linkText
                  offerID
                  startDate
                  tenantIds
                  text
                  title
              }
              shortImage
          }
          area_display
          filter {
              child
              main
          }
          id
          location
          location_display
          logoImage
          title
      }
      totalCount
  }
}
`

export const getResultScreenByFilterShopV2 = `
query getResultScreenByFilterShop_v2 ($filter: [SelectedFilterParams], $orderBy: [TenantOrderByInput], $page_number: Int, $size: Int) {
  getResultScreenByFilterShop_v2 (filter: $filter, orderBy: $orderBy, page_number: $page_number, size: $size) {
    nodes {
      aemTenantDetails {
        additionalTileImages
        area {
          filterType
          sequenceNumber
          tagName
          tagTitle
        }
        availability {
          filterType
          sequenceNumber
          tagName
          tagTitle
        } 
        backgroundImage
        categories
        cuisine {
          filterType
          sequenceNumber
          tagName
          tagTitle
        }
        dietary {
          filterType
          sequenceNumber
          tagName
          tagTitle
        }
        location {
          filterType
          sequenceNumber
          tagName
          tagTitle
        }
        logoImage
        longImage
        openedDate
        price
        rewards {
          dismissible
          displayLocation
          expiryDate
          icon
          link
          linkText
          offerID
          startDate
          tenantIds
          text
          title
        }
        shortImage
      }
      area_display
      filter {
        child
        main
      }
      id
      location
      location_display
      logoImage
      title
    }
    totalCount
  }
}
`

export const getStaffPerkSwimlaneQuery = `
query getStaffPerksSwimlane {
  getStaffPerksSwimlane(input: {category: "", page_number:1, page_size:10}) {
    promos {
      id
      title
      tenantName
      imageUrl
      campaignStartDate
      campaignEndDate
      placement
      type
      categories
    }
    status
  }
}
`

export const getStaffPerkListingQuery = `
query getStaffPerksListing ($input: StaffPerksListingInput){
  getStaffPerksListing(input: $input) {
    promos {
      id
      title
      tenantName
      imageUrl
      campaignStartDate
      campaignEndDate
      placement
      type
      categories
    }
    totalPromosCount
    status
  }
}
`

export const getStaffPerkPromotionDetailQuery = `
query getOfferDetails ($promo_id: String){
  getOfferDetails(input: {promo_id: $promo_id}) {
    promo {id, imageUrl, tags, title, description, linkLabel, linkUrl, howToUse, tnc, tenantId, tenantName, tenantIcon, tenantLocations, tenantOutlets, campaignStartDate, campaignEndDate, availability, iShopChangiAvailability, iShopChangiUrl, tenantType}
    status,
  }
}
`

export const markAsUsedQuery = `
query markUsedPerk ($input: MarkUsedPerkInput)  {
  markUsedPerk (input: $input) {
    success
  }
}
`

export const getLocationMappingQuery = `
  query getLocationMapping ($input: GetLocationMappingInput) {
  getLocationMapping(input: $input) {
    local_ref
  }
}
`

export const getTenantDetailQuery = `
  query getTenantDetail ($input: GetTenantDetailInput) {
    getTenantDetail (input: $input) {
      id
    }
  }
`

export const getAppscapadeBannerQuery = `
  query getAppscapadeBanner ($input: GetAppscapadeBannerInput!) {
    getAppscapadeBanner (input: $input) {
      status
      message
      chance_id
      type
      navigation_type
      navigation_value
      redirect {
        redirectTarget
        utmParameters {
            content
            medium
            term
            campaign
        }
      }
      image
      fragment_title
    }
  }
`

export const getAppscapadeLuckyDrawEligibleQuery = `
  query getAppscapadeLuckyDrawEligible ($input: GetAppscapadeLuckyDrawEligibleInput!) {
    getAppscapadeLuckyDrawEligible(input: $input) {
      status
      message
      chance_id
    }
  }
`

export const getAvailableDiscountStaffPerkQuery = `
  query getStaffPerksTenantDetails ($tenant_id: String) {
    getStaffPerksTenantDetails(input: {tenant_id: $tenant_id}) {
      promos {id, title, campaignStartDate, campaignEndDate}
      status,
    }
  }
`

export const getAvailableDiscountDealsPromosQuery = `
  query getDealsPromosTenantDetails ($tenant_id: String) {
    getDealsPromosTenantDetails(input: {tenant_id: $tenant_id}) {
      promos {id, title, campaignStartDate, campaignEndDate}
      status,
    }
  }
`

export const getFlightFilterOptionsQuery = `
query getFlightFilterOptions ($input: GetFlightFilterOptionsInput) {
  getFlightFilterOptions(input: $input) {
    terminal {
      name
      value
    }
    airline {
      image
      name
      value
    }
    city_airport {
      name
      value
    }
  }
}
`

export const getPointExpiryQuery = `
query getPointsExpiry($input: GetPointsExpiryInput) {
  getPointsExpiry(input: $input) {
    remaining_expiring_points_text
    points_expiry_data {
      points_expiring
      points_expiry_date
    }
  }
}`

export const getTotalPlaypassPerkQuery = `
query getWalletMyPerks ($input: GetPerkInput!) {
  getPerks_v3(input: $input) {
    active_perk_count
    active_perks {
      ... on PlaypassPerk {
        perk_type
        category_code
        category_name
        expiry_dt
        token_img_url
        token_name_plural
        token_name_singular
        token_qty
        token_type
        validated_flg
      }
    }
    errors
  }
}`

export const getCreditsV2 = `
  query getCredits_v2 {
    getCredits_v2 {
      data {
        description 
        items {
          expiryDate
          totalCredits
        }
        name
        redeemableCredits
        tokenImage
        tokenType
        totalCredits
      }
      total
    }
  }
`

export const getAccountInfo = `
  query getAccountInfoValues($creditsInput: GetCreditsInput, $perksInput: GetPerkInput!) {
    getCredits_v2(input: $creditsInput) {
      total
    }
    getPerks_v3(input: $perksInput) {
      active_perk_count
      errors
    }
    getOrders(page_size: 1) {
      total_results
    }
    getPlaypassBookings_v2(input: { scope: "all", isCount: true }) {
      total
    }
  }
`

export const getAccountInfoPromoCodes = `
  query getAccountInfoValues($creditsInput: GetCreditsInput, $perksInput: GetPerkInput!, $promoCodeInput: GetPromoCodesInput) {
    getCredits_v2(input: $creditsInput) {
        total
    }
    getPerks_v3(input: $perksInput) {
        errors
        active_perk_count
    }
    getOrders(page_size: 1) {
        total_results
    }
    getPlaypassBookings_v2(input: { scope: "all", isCount: true }) {
        total
    }
    getPromoCodes(input: $promoCodeInput) {
      count
    }
    getBookingsOrdersListing {
      totalBookingsOrdersCount
    }
}
`

export const getAccountInfoBookingsOrders = `
  query getAccountInfoValues($creditsInput: GetCreditsInput, $perksInput: GetPerkInput!) {
    getCredits_v2(input: $creditsInput) {
      total
    }
    getPerks_v3(input: $perksInput) {
      active_perk_count
      errors
    }
    getOrders(page_size: 1) {
      total_results
    }
    getPlaypassBookings_v2(input: { scope: "all", isCount: true }) {
      total
    }
    getBookingsOrdersListing {
      totalBookingsOrdersCount
    }
  }
`

export const getAccountInfoPromoCodesBookingsOrders = `
  query getAccountInfoValues($creditsInput: GetCreditsInput, $perksInput: GetPerkInput!, $promoCodeInput: GetPromoCodesInput) {
    getCredits_v2(input: $creditsInput) {
      total
    }
    getPerks_v3(input: $perksInput) {
      active_perk_count
      errors
    }
    getOrders(page_size: 1) {
      total_results
    }
    getPlaypassBookings_v2(input: { scope: "all", isCount: true }) {
      total
    }
    getPromoCodes(input: $promoCodeInput) {
      count
    }
    getBookingsOrdersListing {
      totalBookingsOrdersCount
    }
  }
`

export const getCreditsDetailsQuery = `
  query getCreditsDetails($input: GetCreditsInput) {
    getCredits_v2(input: $input) {
      data {
        name
        description
        redeemableCredits
        tokenImage
        tokenType
        totalCredits
        items {
          expiryDate
          totalCredits
        }
      }
      total
    }
  }
`

export const getFlightDetailsDeeplink = `
  query getReferralLink ($input: ReferralLinkInput!) {
    getReferralLink(input: $input) {
      shortLink
    }
  }
`

export const getFlightEarlyCheckinV2 = `
  query getEarlyCheckin_v2 ($input: GetEarlyCheckinInput) {
  getEarlyCheckin_v2 (input: $input) {
    openECIPopup
    linkEnable
    linkText
    link
    eligibleECIDuration
    eligibleECIStart {
        Date
        ECIDate
        ECIHour
    }
    eligibleECIEnd {
        Date
        ECIDate
        ECIHour
    }
    earliestEligibleECITime
    ECILounge {
      locationName
      locationAddress
      ECIDateHours {
        ECIDate
        ECIHour
      }
      ECIDateTimeHours {
        Date
        ECIDate
        ECIHour
      }
    }
  }
}
`

export const validatePromoCodeQuery = `
query quer($promoInput: ValidatePromoCodeInput!, $referralInput: ValidatePromoCodeInput!) {
      validatePromo: validatePromoCode(input: $promoInput) {
        data {
          code_valid
        }
        return_status
        return_message
      }
      validateReferral: validatePromoCode(input: $referralInput) {
        data {
          code_valid
        }
        return_status
        return_message
      }
    }
  `
export const getVouchersPrizesRedemptionsQuery = `
  query getVouchersPrizesRedemptions($input: GetPerkInput!) {
    getPerks_v3(input: $input) {
      active_perk_count
      perk_filter
      errors
      active_perks {
        ... on ChangiRewardPerk {
          perk_type
          perk_code
          catalog_code
          category_code
          category_name
          valid_from
          expiry_dt
          privileges_type
          token_img_url
          token_name_plural
          token_name_singular
          token_qty
          redemption_mode
          eligible_outlet {
              outlet_code
              outlet_name
          }
          cr_vouchers_campaign
          description
          terms_and_conditions
        }
        ... on InstantWinPrize {
          perk_type
          category_code
          categoryLabel
          chanceId
          description
          drawnDate
          image
          labelText
          luckyDrawCode
          redirectUrl
          subDescription
          subDescription2
          title
          expiry_dt
        }
        ... on GamificationPrize {
          perk_type
          category_code
          id
          title
          image
          description
          subDescription
          subDescription2
          prizeLabelText
          promoCode
          createdDt
          redeemDt
          validTill
          redeemable
          categoryCode
          redemptionMode
          qty
          eligibleOutlet
        }
      }
    }
  }
`

export const getVouchersPassesRedemptionsQuery = `
  query CombinedQueries($input: GetPerkInput!, $playpassInput: PlaypassBookingInput) {
    getPerks_v3: getPerks_v3(input: $input) {
      active_perk_count
      perk_filter
      errors
      active_perks {
        ... on ChangiRewardPerk {
          perk_type
          perk_code
          catalog_code
          category_code
          category_name
          valid_from
          expiry_dt
          privileges_type
          token_img_url
          token_name_plural
          token_name_singular
          token_qty
          redemption_mode
          eligible_outlet {
              outlet_code
              outlet_name
          }
          cr_vouchers_campaign
          description
          terms_and_conditions
        }
        ... on InstantWinPrize {
          perk_type
          category_code
          categoryLabel
          chanceId
          description
          drawnDate
          image
          labelText
          luckyDrawCode
          redirectUrl
          subDescription
          subDescription2
          title
          expiry_dt
        }
        ... on GamificationPrize {
          perk_type
          category_code
          id
          title
          image
          description
          subDescription
          subDescription2
          prizeLabelText
          promoCode
          createdDt
          redeemDt
          validTill
          redeemable
          categoryCode
          redemptionMode
          qty
          eligibleOutlet
        }
      }
    }

    playpass: getPlaypassBookings_v2(input: $playpassInput) {
      data {
        allowCancel
        bookingId
        bookingKey
        bookingStatus
        bookingType
        cartId
        earliestArrival
        editCount
        editMaxCount
        endDate
        endScheduled
        endScheduledTxt
        endTime
        icon
        image
        info {
          text
          url
        }
        isActive
        latestArrival
        latestCancelBefStartMins
        latestEditBefStartMins
        location
        locationCode
        packageCd
        passName
        pkgCode
        playpassEventCard {
          date
          image
          time
          title
        }
        productName
        quantity
        startDate
        startScheduled
        startScheduledTxt
        startTime
        status
        subTitle
        tags
        ticketIds
        timerTs
        title
        type
      }
      total
    }
  }
`

export const getOrderDetailsV2Query = `
  query getOrderDetailsQuery ($transactionNo: String!, $ocidEmail: String!) {
    getOrderDetails_v2(transactionNo: $transactionNo, ocidEmail: $ocidEmail) {
      deliveryCost
      earnedRewardPoints
      consignments {
        checkoutType
        collectionDetails {
          display
          terminalCode
        }
        consignmentCode
        consignmentStatus
        deliveryDetails {
          deliveryAddress {
            addressAlias
            countryISDCode
            firstName
            formattedAddress
            lastName
            phone
            postalCode
            unit
            country {
              name
            }
          }
          deliveryDate
          deliveryMethod
          deliverySlot
          shippedBy
          trackingURL
        }
        landsideCollectionDetails {
          collectionDetail
          collectionAddress {
              cellphone
              country {
                  isdCode
                  isocode
                  name
              }
              countryISDCode
              defaultAddress
              firstName
              formattedAddress
              id
              lastName
              line1
              line2
              phone
              postalCode
              shippingAddress
              title
              titleCode
              unit
              visibleInAddressBook
          }
          deliveryDate
          collectionMethod
          deliveryTime
          contactNumber
        }
        products {
          description
          basePrice
          brandName
          imageUrl
          productName
          quantity
          totalPrice
          variant {
            measureType
            measureUnit
            qualifier
          }
        }
        statusDisplay {
          color
          text
        }
        subTotal
        title
        totalItems
      }
      gstType
      netTotal
      paymentMethod
      redeemedPoints
      redeemedValues
      status
      subTotal
      totalItems
      totalPrice
      transactionNo
      voucherCode
      voucherDiscounts
    }
  }
`

export const getBookingListV2Query = `
  query getPlaypassBookings_v2($input: PlaypassBookingInput) {
    getPlaypassBookings_v2(input: $input) {
      data {
        allowCancel
        bookingId
        bookingKey
        bookingStatus
        bookingType
        cartId
        earliestArrival
        editCount
        editMaxCount
        endDate
        endScheduled
        endScheduledTxt
        endTime
        icon
        image
        info {
          text
          url
        }
        isActive
        latestArrival
        latestCancelBefStartMins
        latestEditBefStartMins
        location
        locationCode
        packageCd
        passName
        pkgCode
        playpassEventCard {
          date
          image
          time
          title
        }
        productName
        quantity
        startDate
        startScheduled
        startScheduledTxt
        startTime
        status
        subTitle
        tags
        ticketIds
        timerTs
        title
        type
      }
      total
    }
  }
`

export const getBaggageArrEstQuery = `
  query getBaggageArrEst($input: GetBaggageArrEstInput) {
    getBaggageArrEst(input: $input) {
      flight_id
      est_fb_time
      est_fb_time_display
    }
  }
`

export const getGamificationPlayerInfoQuery = `
  query getGamificationPlayerInfo {
    getGamificationPlayerInfo {
      stickerInventory {
        id
        name
        quantity
      }
    }
  }
`

export const getGamificationGameChanceQuery = `
  query getGamificationGameChance($input: GetGamificationGameChanceInput) {
    getGamificationGameChance(input: $input) {
      status
      message
      memberId
      taskCode
      chanceAwarded
    }
  }
`

export const getMaintenanceConfigurations = `
  query getMaintenanceConfigurations {
    getMaintenanceConfigurations {
        enable
        title
        description
        icon
        maintenanceType
        displayModule
        displayLocation {
            tagTitle
            tagName
            filterType
            sequenceNumber
        }
        startDate
        endDate
        buttonLabel
        navigation {
            type
            value
        }
        redirect {
            redirectTarget
            utmParameters {
                content
                medium
                term
                campaign
            }
        }
        runMode {
            icon
            title
            description
            enable
            navigation {
                type
                value
            }
            redirect {
                redirectTarget
                utmParameters {
                    content
                    medium
                    term
                    campaign
                }
            }
        }
    }
  }
  `

export const maintenanceConfigurationsUpdate = `
  subscription maintenanceConfigurationsUpdate {
    maintenanceConfigurationsUpdate {
        enable
        title
        description
        maintenanceType
        displayModule
        displayLocation {
            tagTitle
            tagName
            filterType
            sequenceNumber
        }
        icon
        startDate
        endDate
        buttonLabel
        navigation {
            type
            value
        }
        redirect {
            redirectTarget
            utmParameters {
                content
                medium
                term
                campaign
            }
        }
        runMode {
            icon
            title
            description
            enable
            navigation {
                type
                value
            }
            redirect {
                redirectTarget
                utmParameters {
                    content
                    medium
                    term
                    campaign
                }
            }
        }
    }
  }
  `

export const vCEACheckAirportPremise = `
  query vCEACheckAirportPremise($input: Coordinates!) {
    vCEACheckAirportPremise(input: $input) {
      isInAirportPremise
      vCEALandingPage
    }
  }
`
export const getGroupBuyBannersQuery = `
  query getGroupBuyBanners { 
    getGroupBuyBanners { 
      productImage 
      startDate 
      endDate 
      productName 
      groupBuyPrice 
      retailPrice
      bannerType
      webviewUrl
      isError
    } 
}
`

export const getDineShopTransactionSummary = `
  query getTransactionSummary {
    getTransactionSummary {
      isLogin
      totalNrOfTransactions
      totalPointsAwarded
      totalSubtotalAmount
      jewelTerminalSubtotalAmount
      mainTerminalSubtotalAmount
    }
  }
`
export const getMyTripQuery = `
  query getMyTripQuery ($input: GetMyTripInput) {
  getMyTrip(input: $input) {
    data {id, actualDepartureLocalDate, actualArrivalLocalDate, departurePlace, scheduledDepartureLocalDate, departureCheckInDesk, departureTerminal, departureGate, estimatedDepartureLocalDate, arrivalPlace, scheduledArrivalLocalDate, arrivalTerminal, arrivalGate, arrivalBaggageClaim, estimatedArrivalLocalDate, flightStatus, title, subtitle, updateFields, departureTimezone, arrivalTimezone, transit {id, actualDepartureLocalDate, actualArrivalLocalDate, departurePlace, scheduledDepartureLocalDate, departureCheckInDesk, departureTerminal, departureGate, estimatedDepartureLocalDate, arrivalPlace, scheduledArrivalLocalDate, arrivalTerminal, arrivalGate, arrivalBaggageClaim, estimatedArrivalLocalDate, flightStatus, updateFields, departureTimezone, arrivalTimezone}},
    status,
    message
  }
}
`

export const getWeatherQuery = `
  query getWeather ($input: GetWeatherInput) {
  getWeather(input: $input) {
    data {isDayTime, currentTemperature, dailyForecasts {date, minimumTemperature, maximumTemperature, dayIconUrl, dayIconPhrase, nightIconUrl, nightIconPhrase}},
    status,
    message
  }
}
`

export const searchFlightsV2Query = `
  query searchFlightsV2($text: String!, $page_number: Int, $page_size: Int, $filter: SearchFilter) {
    search_v2(category: FLIGHTS, text: $text, filter: $filter, page_number: $page_number, page_size: $page_size) {
      items {
        ... on Flight {
          scheduled_date
          scheduled_time
          display_timestamp
          actual_timestamp
          estimated_timestamp
          flight_number
          direction
          terminal
          slave_flights
          display_belt
          technical_flight_status1
          technical_flight_status2
          check_in_row
          display_gate
          status_mapping {
            belt_status_en
            details_status_en
            details_status_zh
            listing_status_en
            listing_status_zh
            status_text_color
            show_gate
          }
          airport_details {
            name
            country
          }
          via_airport_details {
            name
          }
          airline_details {
            name
            logo_url
          }
          flight_status
        }
      }
      page_number
      page_size
      total
    }
  }
`

export const getOnlineCheckInQuery = `
query getOnlineCheckin($direction: String!, $flightNumber: String!, $scheduledDate: String!) {
  getOnlineCheckin(direction: $direction, flightNumber: $flightNumber, scheduledDate: $scheduledDate) {
    link
    linkText
  }
}
`

export const getFlightJourney = `
query getFlightJourney($input: GetFlightJourneyInput) {
  getFlightJourney(input: $input) {
    data {equipment_name, current_long, current_lat, current_position_date, positions {long, lat, date}},
    status,
    message
  }
}
`

export const getPlaypassBookingActive = `
 query getPlaypassBookingActive ($input: PlaypassBookingInput!) {
  getPlaypassBookings_v2(input: $input) { 
    data {
      packageCd
      type
      tags
      bookingId
      endDate
      endTime
      icon
      location
      locationCode
      productName
      quantity
      startDate
      startTime
      status
      subTitle
      tags
      title
      timerTs
      cartId
      type
      playpassEventCard {
        image
        title
        date
        time
        imageV2
        titleV2
      }
      bookingKey
      earliestArrival
      latestArrival
      bookingStatus
      isActive
      upcomingStartScheduledTxt
      upcomingEndScheduledTxt
    }
  }
}
`

export const getPromoCodesQuery = `
  query getPromoCodes($input: GetPromoCodesInput) {
    getPromoCodes(input: $input) {
      count
      data {
        campaignName
        cta
        description
        endDate
        promoCode
        promoType
        startDate
        title
        validityPeriod
        voucherTypeCode
        gamificationPrizeId
      }
    }
  }
`

export const getPromoCodeDetailsQuery = `
  query getPromoCodeDetails($input: GetPerkDetailInput_v2!) {
    getPerkDetail_v2 (input: $input) {
      changi_reward_detail {
        aem_cf_title
        category_code
        cr_vouchers_campaign
        description
        eligible_outlet {
          outlet_code
          outlet_name
        }
        is_used
        mark_timestamp
        redemption_mode
        remarks
        terms_and_conditions
        terms
        valid_datetime_msg
        valid_from
        valid_to
        voucher_type_code
        voucher_type_name
        voucher_url
        voucher_used_on
        voucher_type_description
      }
      gamification_prize_detail {
        id
        title
        image
        description
        subDescription
        subDescription2
        labelText
        promoCode
        createdDt
        redeemDt
        validTill
        redeemable
        categoryCode
        redemptionMode
        qty
        eligibleOutlet
      }
    }
  }
`

export const getDealsPromos = `
  query getDealsPromosSwimlane ($input: StaffPerksListingInput) {
    getDealsPromosListing(input: $input) {
      promos {
          id,
          title,
          tenantName,
          imageUrl,
          campaignStartDate,
          campaignEndDate,
          type,
          categories,
          placement,
          area,
          terminal,
          locations
      }
      status,
    }
  }
`

export const getTenantListing = `
  query getTenantListing {
    getResultScreenByFilterDineAndShop {
        nodes {
            aemTenantDetails {
                additionalTileImages
                area {
                    filterType
                    sequenceNumber
                    tagName
                    tagTitle
                }
                availability {
                    filterType
                    sequenceNumber
                    tagName
                    tagTitle
                }
                backgroundImage
                categories
                cuisine {
                    filterType
                    sequenceNumber
                    tagName
                    tagTitle
                }
                dietary {
                    filterType
                    sequenceNumber
                    tagName
                    tagTitle
                }
                location {
                    filterType
                    sequenceNumber
                    tagName
                    tagTitle
                }
                logoImage
                longImage
                openedDate
                price
                rewards {
                    dismissible
                    displayLocation
                    expiryDate
                    icon
                    link
                    linkText
                    offerID
                    startDate
                    tenantIds
                    text
                    title
                }
                shortImage
            }
            filter {
                child
                main
            }
            id
            location
            logoImage
            title
            location_display
            area_display
            availability {
                    filterType
                    sequenceNumber
                    tagName
                    tagTitle
                }
            tenantType
            openingStatus
            staffPerk
            iscAvailable
            label
        }
        totalCount
    }
  }
`

export const listFilterPillDineShop_v2 = `
  query listFilterPillDineShop_v2 ($input: ListFilterPillDineShopInput) {
  listFilterPillDineShop_v2 (input: $input) {
    data {
      tagTitle
      childTags {
        tagTitle
        tagName
        filterType
      }
    }
    // pill_options {
    //     tagTitle
    //     tagName
    //     filterType
    //   }
  }
}
`
export const getInsuranceBookingsOrders = `
  query GetInsuranceBookingsOrders {
    getBookingsOrdersListing {
      data {
        orderId
        policyName
        insuredAdultsChildrenCounts
        destination
        duration
        planName
        totalCost
      }
      status
      totalBookingsOrdersCount
    }
  }
`;

export const getCombinedCreditsInsurancePlaypassOrders = `
  query CombinedQueries($creditsInput: GetCreditsInput, $playpassInput: PlaypassBookingInput) {
    credits: getCredits_v2(input: $creditsInput) {
      data {
        name
        description
        redeemableCredits
        tokenImage
        tokenType
        totalCredits
        items {
          expiryDate
          totalCredits
        }
      }
      total
    }

    insurance: getBookingsOrdersListing {
      data {
        orderId
        policyName
        insuredAdultsChildrenCounts
        destination
        duration
        planName
        totalCost
      }
      status
      totalBookingsOrdersCount
    }

    playpass: getPlaypassBookings_v2(input: $playpassInput) {
      data {
        allowCancel
        bookingId
        bookingKey
        bookingStatus
        bookingType
        cartId
        earliestArrival
        editCount
        editMaxCount
        endDate
        endScheduled
        endScheduledTxt
        endTime
        icon
        image
        info {
          text
          url
        }
        isActive
        latestArrival
        latestCancelBefStartMins
        latestEditBefStartMins
        location
        locationCode
        packageCd
        passName
        pkgCode
        playpassEventCard {
          date
          image
          time
          title
        }
        productName
        quantity
        startDate
        startScheduled
        startScheduledTxt
        startTime
        status
        subTitle
        tags
        ticketIds
        timerTs
        title
        type
      }
      total
    }

    orders: getOrders(page_size: 100) {
      image_url
      order_date
      status
      title
      total
      transaction_number
      consignments {
        checkout_type
        consignment_code
        consignment_status
        fluent_id
        image_url
        status_display {
          color
          text
        }
        title
        total_items
        total_price
      }
    }
  }
`;

export const getCombinedCreditsPlaypassOrders = `
  query CombinedQueries($creditsInput: GetCreditsInput, $playpassInput: PlaypassBookingInput) {
    credits: getCredits_v2(input: $creditsInput) {
      data {
        name
        description
        redeemableCredits
        tokenImage
        tokenType
        totalCredits
        items {
          expiryDate
          totalCredits
        }
      }
      total
    }

    playpass: getPlaypassBookings_v2(input: $playpassInput) {
      data {
        allowCancel
        bookingId
        bookingKey
        bookingStatus
        bookingType
        cartId
        earliestArrival
        editCount
        editMaxCount
        endDate
        endScheduled
        endScheduledTxt
        endTime
        icon
        image
        info {
          text
          url
        }
        isActive
        latestArrival
        latestCancelBefStartMins
        latestEditBefStartMins
        location
        locationCode
        packageCd
        passName
        pkgCode
        playpassEventCard {
          date
          image
          time
          title
        }
        productName
        quantity
        startDate
        startScheduled
        startScheduledTxt
        startTime
        status
        subTitle
        tags
        ticketIds
        timerTs
        title
        type
      }
      total
    }

    orders: getOrders(page_size: 100) {
      image_url
      order_date
      status
      title
      total
      transaction_number
      consignments {
        checkout_type
        consignment_code
        consignment_status
        fluent_id
        image_url
        status_display {
          color
          text
        }
        title
        total_items
        total_price
      }
    }
  }
`;

export const getCombinedInsurancePlaypassOrders = `
  query CombinedQueries($playpassInput: PlaypassBookingInput) {
    insurance: getBookingsOrdersListing {
      data {
        orderId
        policyName
        insuredAdultsChildrenCounts
        destination
        duration
        planName
        totalCost
      }
      status
      totalBookingsOrdersCount
    }

    playpass: getPlaypassBookings_v2(input: $playpassInput) {
      data {
        allowCancel
        bookingId
        bookingKey
        bookingStatus
        bookingType
        cartId
        earliestArrival
        editCount
        editMaxCount
        endDate
        endScheduled
        endScheduledTxt
        endTime
        icon
        image
        info {
          text
          url
        }
        isActive
        latestArrival
        latestCancelBefStartMins
        latestEditBefStartMins
        location
        locationCode
        packageCd
        passName
        pkgCode
        playpassEventCard {
          date
          image
          time
          title
        }
        productName
        quantity
        startDate
        startScheduled
        startScheduledTxt
        startTime
        status
        subTitle
        tags
        ticketIds
        timerTs
        title
        type
      }
      total
    }

    orders: getOrders(page_size: 100) {
      image_url
      order_date
      status
      title
      total
      transaction_number
      consignments {
        checkout_type
        consignment_code
        consignment_status
        fluent_id
        image_url
        status_display {
          color
          text
        }
        title
        total_items
        total_price
      }
    }
  }
`;

export const getCombinedPlaypassOrders = `
  query CombinedQueries($playpassInput: PlaypassBookingInput) {
    playpass: getPlaypassBookings_v2(input: $playpassInput) {
      data {
        allowCancel
        bookingId
        bookingKey
        bookingStatus
        bookingType
        cartId
        earliestArrival
        editCount
        editMaxCount
        endDate
        endScheduled
        endScheduledTxt
        endTime
        icon
        image
        info {
          text
          url
        }
        isActive
        latestArrival
        latestCancelBefStartMins
        latestEditBefStartMins
        location
        locationCode
        packageCd
        passName
        pkgCode
        playpassEventCard {
          date
          image
          time
          title
        }
        productName
        quantity
        startDate
        startScheduled
        startScheduledTxt
        startTime
        status
        subTitle
        tags
        ticketIds
        timerTs
        title
        type
      }
      total
    }

    orders: getOrders(page_size: 100) {
      image_url
      order_date
      status
      title
      total
      transaction_number
      consignments {
        checkout_type
        consignment_code
        consignment_status
        fluent_id
        image_url
        status_display {
          color
          text
        }
        title
        total_items
        total_price
      }
    }
  }
`;

export const getCombinedCreditsInsuranceOrders = `
  query CombinedQueries($creditsInput: GetCreditsInput) {
    credits: getCredits_v2(input: $creditsInput) {
      data {
        name
        description
        redeemableCredits
        tokenImage
        tokenType
        totalCredits
        items {
          expiryDate
          totalCredits
        }
      }
      total
    }

    insurance: getBookingsOrdersListing {
      data {
        orderId
        policyName
        insuredAdultsChildrenCounts
        destination
        duration
        planName
        totalCost
      }
      status
      totalBookingsOrdersCount
    }

    orders: getOrders(page_size: 100) {
      image_url
      order_date
      status
      title
      total
      transaction_number
      consignments {
        checkout_type
        consignment_code
        consignment_status
        fluent_id
        image_url
        status_display {
          color
          text
        }
        title
        total_items
        total_price
      }
    }
  }
`;

export const getCombinedCreditsOrders = `
  query CombinedQueries($creditsInput: GetCreditsInput) {
    credits: getCredits_v2(input: $creditsInput) {
      data {
        name
        description
        redeemableCredits
        tokenImage
        tokenType
        totalCredits
        items {
          expiryDate
          totalCredits
        }
      }
      total
    }

    orders: getOrders(page_size: 100) {
      image_url
      order_date
      status
      title
      total
      transaction_number
      consignments {
        checkout_type
        consignment_code
        consignment_status
        fluent_id
        image_url
        status_display {
          color
          text
        }
        title
        total_items
        total_price
      }
    }
  }
`;

export const getCombinedInsuranceOrders = `
  query CombinedQueries() {
    insurance: getBookingsOrdersListing {
      data {
        orderId
        policyName
        insuredAdultsChildrenCounts
        destination
        duration
        planName
        totalCost
      }
      status
      totalBookingsOrdersCount
    }

    orders: getOrders(page_size: 100) {
      image_url
      order_date
      status
      title
      total
      transaction_number
      consignments {
        checkout_type
        consignment_code
        consignment_status
        fluent_id
        image_url
        status_display {
          color
          text
        }
        title
        total_items
        total_price
      }
    }
  }
`;

export const getOrdersBOC = `
  query getOrdersQuery {
    orders: getOrders(page_size: 100) {
      image_url
      order_date
      status
      title
      total
      transaction_number
      consignments {
        checkout_type
        consignment_code
        consignment_status
        fluent_id
        image_url
        status_display {
          color
          text
        }
        title
        total_items
        total_price
      }
    }
  }
`;


export const getInsuranceOrderDetails = `
query getInsuranceOrderDetails($input: BookingsOrdersDetailsInput) {
  getBookingsOrdersDetails(input: $input) {
    data {
      orderId
      policyName
      policyNumber
      insuredAdultsChildrenCounts
      destination
      duration
      planName
      totalCost
      promoCode
      subtotalAmount
      discountAmount
    }
    status
  }
}
`