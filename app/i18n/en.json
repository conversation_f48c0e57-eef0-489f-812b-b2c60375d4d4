{"airlineLounge": {"contactLabel": " ({{label}})", "moreInfo": "More info"}, "common": {"ok": "OK!", "cancel": "Cancel", "back": "Back", "viewAll": "View all", "reload": "Reload", "retry": "Retry", "takeAPhoto": "Take a photo", "chooseFromLibrary": "Choose from library", "yes": "Yes", "no": "No", "none": "None", "okay": "Okay", "save": "Save", "clear": "Clear", "continue": "Continue", "close": "Close", "readMore": "Read more", "closeAll": "Close All", "notAvailable": "Not available", "activated": "Activated"}, "confirmEmail": {"checkEmail": "Check the email \n we sent you to validate \n your account", "resend": "Resend link", "linkSent": "<PERSON> sent!", "emailSent": "Email sent!", "tap": "Just tap the link we sent to", "openBtn": "Open Mail App", "resendCount": "Resend link in {{count}} secs", "validateEmailToContinue": "Validate your email address to continue!", "verifyEmailHint": "Please contact <NAME_EMAIL> should you wish to amend your registered e-mail address.", "errorMessage": "Sorry, we’re unable to resend the validation link. Please try again later.", "okay": "Okay", "thankyou": "Thanks for validating your email address!", "description": "Just tap the link we sent to <email> Please contact <NAME_EMAIL> should you wish to amend your registered e-mail address"}, "eCard": {"cardLabel": "Card No: ", "tierLabel": "Tier: ", "tierText": {"gold": "Gold", "member": "Member", "platinum": "Platinum", "staff_member": "Staff_Member", "staff_gold": "Staff_Gold", "staff_platinum": "Staff_Platinum"}}, "errors": {"invalidEmail": "Invalid email address.", "EHR41": {"title": "No internet connection", "description": "Please check your connection again, or connect to a different internet network.", "cta": "Retry"}, "EHR42": {"title": "Oops", "description": "Looks like something went wrong. Let’s try again in a few minutes.", "cta": "Reload"}, "noResults": {"title": "No Results", "description": "We couldn't find any results matching your selected filters. Clear or change your filters to see more."}}, "welcomeScreen": {"poweredBy": "POWERED BY CNXTS", "readyForLaunch": "Ready for launch.", "continue": "CONTINUE"}, "demoScreen": {"howTo": "HOW TO", "title": "What's In This Stack?", "tagLine": "Congratulations, you've got a very advanced React Native app template here.  Take advantage of this boilerplate!", "reactotron": "Demo Reactotron", "demoList": "Demo List", "androidReactotronHint": "If this doesn't work, ensure the Reactotron desktop app is running, run adb reverse tcp:9090 tcp:9090 from your terminal, and reload the app.", "iosReactotronHint": "If this doesn't work, ensure the Reactotron desktop app is running and reload app.", "seeWhatsTrending": "SEE WHAT'S TRENDING"}, "dineScreen": {"seeWhatsTrending": "SEE WHAT'S TRENDING", "searchBarText": "Restaurants, cuisines", "newlyOpened": "NEWLY OPENED", "exploreMore": "EXPLORE MORE", "cuisineSection": "Explore more cuisines", "contentGuide": "DINING GUIDES FOR YOU", "recommendedForYou": "RECOMMENDED FOR YOU", "mostPopularAtChange": "MOST POPULAR AT CHANGI", "ichangiExclusive": "iCHANGI EXCLUSIVES", "signUpOrLoginToRedeem": "Sign up or login to redeem", "unableToDisplayRestaurants": "We’re currently unable to display restaurants. Please try again later.", "unableToDisplayFilters": "We’re currently unable to display any filters. Please try again later."}, "exploreScreenV2": {"titleSearch": "Search for flights, food, shops, facilities", "titleErrorMathead": "Unable to load details", "contentErrorMathead": "Refresh to try again", "errorQuicklink": "Refresh to try again"}, "dineScreenV2": {"quickLink": {"dsDirectory": "Dine & Shop \n Directory", "spConcierge": "Shopping \n Concierge", "traveller": "iShopChangi \n Traveller Duty-Free", "nonTraveller": "iShopChangi \n Non-Traveller"}, "dealsPromo": {"title": "Deals & Promos", "seeAll": "See All"}, "dineGuides": {"title": "Dine & Shop Guides"}, "justForYou": {"title": "Just for You", "seeAll": "See All"}}, "dineShopDirectory": {"title": "Dine & Shop Directory", "tagStaffPerks": "Staff Perks", "tagAvailableonIShopChangi": "Available on iShopChangi", "titleEmpty": "No Results", "contentEmpty": "We couldn't find any results matching your selected filters. Clear or change your filters to see more."}, "exploreMore": {"outlets": "outlets"}, "shopScreen": {"searchBarText": "Shops, promos, categories", "newlyOpened": "NEWLY OPENED", "exploreShopsAtChangiSection": "EXPLORE SHOPS AT CHANGI", "notToBeMissed": "NOT TO BE MISSED!", "youMayAlsoLike": "YOU MAY ALSO LIKE", "shopAndSpin": "SHOP & SPIN", "unableToDisplayShops": "We’re currently unable to display shops. Please try again later.", "unableToDisplayFilters": "We’re currently unable to display any filters. Please try again later."}, "demoListScreen": {"title": "Demo List"}, "homepageMasthead": {"logInText": "Sign up or login", "welcomeText": "Hello!", "wallet": "Wallet", "reload": "Reload", "rewardsCard": "Rewards Card"}, "newlyOpened": {"multipleLocations": "Multiple Locations"}, "storybook": {"placeholder": "Placeholder", "field": "Field"}, "timelineFlyTiles": {"linkText": "View all in wallet"}, "upcomingEvent": {"today": "Today", "tomorrow": "Tomorrow", "yesterday": "Yesterday", "gate": "Gate", "to": "to", "days": ["Sun", "Mon", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "<PERSON><PERSON>", "Sat"], "month": ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]}, "flightListing": {"infoText": "This is a multi-leg flight. Tap for more details", "errorToastMessage": "Unable to load more flights"}, "dropdownSelectCard": {"selectYourProfile": "Select your profile", "descriptionSelectProfile": "This is so we can personalise your travel itinerary and flight notifications for you.", "descriptionSelectProfileV2": "For a personalised travel itinerary\nsuited to your needs", "imTravellingOnThisFlight": "I’m travelling on this flight", "imDroppingOffSomeone": "Someone I know is on this flight", "imPickingSomeone": "Someone I know is on this flight"}, "loginScreen": {"fieldValidations": {"blankEmail": "Please enter an email address or mobile number", "emailPattern": "Enter a valid email address or mobile number", "passwordBlank": "Please enter your <PERSON><PERSON> Account password"}, "others": {"emailLabel": "Email", "emailPlaceHolder": "Enter email address", "passwordLabel": "Password", "forgotPassword": "Forgot password?", "passwordPlaceholder": "Your Changi Account password", "buttonLabel": "<PERSON><PERSON>", "loginToYourAccountLabel": "Login to your Changi account", "loginToYourAccountPlaceHolder": "Mobile number or email address", "signUpLabel": "Sign up for a Changi account"}, "continueText": "Or continue with", "errorLoginTitle": "Unable to login", "ok": "OK!", "close": "Close", "tryAgain": "Try again", "retrievePassword": "Retrieve password", "errorProfileTitle": "Unable to get profile", "errorProfileMessage": "Sorry, we’re currently unable to get your profile.", "errorUseBiometricFirstTime": "Biometrics is not available for first time login.", "loginSuccess": "Logged in successfully"}, "signupScreen": {"continueText": "Or continue with", "titleText": "Get your Changi account", "descriptionText": "Enjoy GST-absorbed shopping at Changi Airport and exclusive member-only privileges!", "optionLogin": "Use mobile number instead", "buttons": {"signupWithEmail": "Email Address", "continue": "Continue"}, "fields": {"emailLabel": "Email", "emailPlaceholder": "Enter email address", "emailErrorBlank": "Please enter an email address", "emailErrorFormat": "Please enter a valid email address", "passwordLabel": "Create Password", "passwordPlaceholder": "Min 8 char with 1 numeral & uppercase", "passwordErrorBlank": "Please enter a password", "passwordErrorMin": "Must include at least 8 characters", "passwordErrorHasNumeral": "Must include at least one numeral", "passwordErrorHasUppercase": "Must include one or more uppercase characters", "passwordErrorInvalidCharacters": "Sorry, your password cannot contain glyphs or characters in other languages", "passwordErrorSecurityRequirements": "Sorry, your password does not meet our security requirements"}, "benefitsText": {"title": "Benefits when you sign up", "benefit1": "Earn points every time you shop.", "benefit2": "Get access to members-only privileges.", "benefit3": "Unlock exclusive offers when you \nsave flights!"}, "error": {"accountAlreadyExistAccount": "Oops! Looks like this account already exists.", "descriptionAlreadyExistAccount": "Please login to continue. If you have not verified your account, please check your email for the verification link.", "retry": "Retry"}}, "loginSignupTabs": {"login": "<PERSON><PERSON>", "signup": "Sign Up"}, "image-carousel": {"pagination": "{{index}}/{{total}}"}, "changiRewardsMemberCard": {"point": "point", "points": "points", "spend$": "Spend $", "gold": "Gold", "premium": "Premium", "platinum": "Platinum", "elite": "Elite", "add": "Add ", "flightsCompleted": "flights completed", "changiRewards": "<PERSON><PERSON>", "changiRewardsTravel": "<PERSON><PERSON> Travel"}, "dineShopFilter": {"applyFilters": "Apply filters", "clearAll": "Clear all", "applyFilters-v2": "Apply Filters", "clearAll-v2": "Clear All", "all": "All", "titleHeader": "Filters"}, "bottomNavigation": {"explore": "Explore", "fly": "Fly", "dineShop": "Dine & Shop", "buy": "Buy", "you": "You", "pay": "Pay", "account": "Account"}, "restaurantDetails": {"about": "ABOUT", "blogsAndReviews": "BLOGS AND REVIEWS", "area": " Area", "getDirection": "Get directions", "daily": "Daily", "viewWebsite": "View website", "viewMenu": "View menu", "bookATable": "Book a table", "reserve": "Reserve", "descriptionChope": "Reservations powered by"}, "forYouScreen": {"hello": "Hello!", "yourWallet": "Your Wallet", "changiLife": "ChangI life section to go here", "more": "more", "logout": {"button": "Log out", "buttonV2": "Log Out", "text": "Once logged out, you will need to re-login to access your eCard and vouchers."}, "btnLoggedIn": "Login/Sign-up", "resendVerifyLink": "Resend verification link", "seeYouLater": "See you later", "benefitsCard": {"title": "Log in to enjoy more app features", "titleNonVerified": "Activate your account", "titleLoggedOut": "You’re logged out", "subTitle": "Your benefits and rewards are waiting!", "subTitleNonVerified": "Check your email for your account activation link!", "subTitleLoggedOut": "Log in again to enjoy your benefits and rewards!", "text1": "Travel like never before with useful flight features, baggage tracker & other convenient services.", "text2": "Enjoy GST-absorbed shopping, redeem perks, and enjoy seamless payment experience.", "text3": "Book and redeem exclusive events, attractions, premiums; and more!", "textNonVerified1": "Enjoy GTS-absorbed shopping at public areas in Changi Ariport & iShopChangi", "textNonVerified2": "Book and redeem exclusive events, attractions, and premiums", "textNonVerified3": "Get a Sure-Win game token with a min. of $30 nett spend in a single receipt", "textLoggedOut1": "Book and redeem exclusive events, attractions, premiums.", "textLoggedOut2": "Enjoy a seamless and more rewarding shopping experience with Chang<PERSON> Pay and Changi Rewards.", "textLoggedOut3": "View your wallet and keep track of your perks, credits, and orders.", "login": "<PERSON><PERSON>"}, "errorAccount": {"title": "Sorry!", "content": "We are currently unable to retrieve your account details.", "subContent": "Please try again later. If the problem persists, contact <EMAIL>"}, "crPointsTransactionNExpiry": {"pointsTransactionBtnLabel": "Points Transaction", "pointsExpiryBtnLabel": "Points Expiry"}, "pointsPerks": {"redeemBtnLabel": "Redeem", "pointsUnit": "Points", "perksUnit": "Perks", "errorText": "Refresh to reload"}, "monarchConcierge": {"title": "MONARCH CONCIERGE", "subTitle": "WhatsApp your 24/7 complimentary concierge service."}, "accountSection": {"title": "Active Rewards & Orders", "tmpParkingContent": "Parking", "rebatesPromos": "Rebates & Promos", "carnivalCredits": "Carnival Credits", "vouchersPrizesRedemptions": "Vouchers, Prizes & Redemptions", "bookingOrders": "Bookings & Orders", "bookingOrdersCredits": "Bookings, Orders & Credits", "jewelPrivilegeCard": "Jewel Privileges", "changiRewards": "<PERSON><PERSON>s Privileges", "staffPrivileges": "Staff Privileges", "errorTitle": "Unable to load details", "errorDescription": "Refresh to try again", "refreshToReload": "Refresh to reload", "promoCodes": "Promo Codes", "newTag": "New", "vouchersPassesRedemptions": "Vouchers, Passes & Redemptions"}, "authStateSection": {"login": "<PERSON><PERSON>"}, "forYouNonLogin": {"accountTitle": "Account"}, "forYouCM24": {"title": "Spend & Win"}}, "aboutChangiLife": {"header": "About Changi App", "appVersion": "App version"}, "submitSuggestionFeedback": {"header": "Suggestions & Feedback"}, "mobileGSTRefund": {"header": "Mobile GST Refund"}, "rateAirportExperience": {"header": "Rate your experience", "title": "Changi Airport Experience", "content": "Help us improve by rating your experience in Changi Airport.", "item1": "Departure Experience", "item2": "Arrival Experience", "item3": "Airport Cleanliness", "item4": "Friendliness of Airport Staff", "item5": "Shopping", "item6": "Food & Beverage", "item7": "Other Facilities", "clearAll": "Clear all", "continue": "Continue"}, "shopDetailScreen": {"viewWebSite": "View website", "shopOnIshopChangi": "Shop on iShopChangi", "getDirections": "Get directions", "daily": "Daily", "area": " Area", "readMore": "Read more", "showLess": "Show less", "about": "ABOUT"}, "notToBeMissed": {"redeem": "Redeem", "validTill": "Valid till"}, "finishSignUp": {"header": "Finish signing up", "submit": "Sign Up", "firstName": {"placeholder": "First name", "label": "First name", "error": "Please enter a first name"}, "lastName": {"placeholder": "Last name", "label": "Last name", "error": "Please enter a last name"}, "dateOfBirth": {"placeholder": {"DD": "DD", "MM": "MM", "YYYY": "YYYY"}, "textHolder": "Select your date of birth", "label": "Date of birth", "error": "Please choose your date of birth", "least": "You must be at least 16 years old to set up an account.", "errorFormat": "Please choose your birth day the correct format."}, "country": {"placeholder": "Select country", "label": "Country/Territory of residence", "error": "Please choose a country/territory of residence"}, "mobileNumber": {"label": "Mobile number", "error": "Please enter a valid mobile number", "errorMinLength": "Mobile number should be minimum 8 number"}, "checkbox": {"text": "I want to receive information about all the offers and events at Changi."}, "termOrPolicy": {"text1": "By proceeding, I agree to CAG’s ", "text2": "Terms and Conditions ", "text3": "and ", "text4": "Privacy Policy, ", "text5": "and consent to the creation of my Changi account."}, "accountExisted": {"title": "Oops! Looks like this\naccount already exists.", "message": "Please login to continue. If you have not verified your account, please check your email for the verification link.", "button": "Retry"}, "login": "<PERSON><PERSON>", "signUpForUpdate": "Sign up for update"}, "popupError": {"oops": "Oops!", "somethingWrong": "Oops!\nSomething went wrong.", "somethingWrongOneline": "Oops! Something went wrong.", "retry": "Retry", "ok": "OK", "updateProfileMessage": "Sorry, we’re currently unable to update your profile. Please try again later.", "generalErrorMessage": "We could not process your request.\nPlease try again.", "networkErrorMessage": "Sorry, this service is currently unavailable.\nPlease try again later."}, "filterResult": {"sortBy": "Sort by", "nameA_Z": "Name (A-Z)", "nameZ_A": "Name (Z-A)", "priceL_H": "Price (Low to high)", "priceH_L": "Price (High to low)", "dine": "<PERSON>e", "shop": "Shop", "errorToastMessage": "Unable to load more restaurants", "shopErrorToastMessage": "Unable to load more shops", "noResults": "No results found", "noResultsMessage": "We couldn't find any results matching your selected filters.", "reselectFilters": "Reselect filters", "retry": "Retry", "reservationAvailable": "Reservations available"}, "transactionsScreen": {"loadMore": "Load more", "emptyBold": "No transactions yet!", "empty": "Any transactions with earned and redeemed \n <PERSON><PERSON> points will appear here.", "unable": "Unable to load more reward transactions", "retry": "Retry", "yourPoints": "Your Points", "transactions": "Transactions", "pointTransactionTabText": "Points Transaction", "pendingPointsTabText": "Pending Points", "pointExpiryTabText": "Points Expiry", "expiringPoints": "Expiring Points", "expiryDate": "Expiry Date", "emptyPointExpiryTitle": "You currently have no expiring points.", "emptyPointExpiryDescription": "Spend at participating stores and scan your Rewards card to earn Changi Rewards points.", "retroClaimsPrefix": "Missing points? ", "retroClaimsBtnLabel": "Submit a claim", "retroClaimApprovedLabel": "Approved", "notePoints": "Points balance reflected on this page are as of yesterday's points balance at 11:59 PM, and excludes any purchase transactions within seven (7) calendar days from the date of purchase.", "noteChangiRewards": "Changi Rewards points are available for redemption after seven (7) calendar days from the time they were first earned on a purchase transaction. ", "emptyPendingPoints": "You have no pending points."}, "departureSection": {"earlierFlights": "Tap to load earlier flights", "viewAllDepartureFlight": "View all departure flights", "searchBarText": "Flight, City, Airport", "viewAllArrivalFlight": "View all arrival flights", "scanButtonText": "Scan Boarding Pass", "titleBottomSheetEarlyCheckin": "Early Check-in Duration"}, "flyLanding": {"flightTabTitle": "Flights", "airportTabTitle": "Airport", "saved": "Saved", "yourFlightSaved": " Your flight is saved!", "removeFlight": "Flight has been deleted!", "removeFlightNew": "Flight has been unsaved!", "toolTipFly": "Tap the plus icon to save a flight and get notified with latest changes!", "toolTipFlyV2": "Tap the plus icon to save a flight for notifications and shopping deals!", "exploreAndFly": "Explore & Fly", "seeAirportMap": "See Airport Map"}, "flyLandingV2": {"myFlight": "My Flight", "fly": "Fly", "refreshToReload": "Refresh to reload"}, "flightListingV2": {"searchFlights": "Search Flights", "responseErrorDescription": "Flight information is currently unavailable. We are working to resolve the issue as quickly as possible.", "noFlightsForTheDay": "There are no flights scheduled for the day", "changiAirport": "Singapore Changi Airport", "lastUpdated": "Last Updated", "currentlyUnavailable": "Currently Unavailable"}, "facilitiesServices": {"title": "Facilities & Services", "location": "Location", "public": "Public", "publicArea": "Public Area", "transit": "Transit", "transitArea": "Transit Area", "all": "All", "clearAll": "Clear All", "applyFilters": "Apply Filters", "filters": "Filters"}, "flightLanding": {"departureTabTitle": "Departure", "arrivalTabTitle": "Arrival", "updatedSystemTime": "Updated", "noFlightsScheduled": "There are no flights scheduled for", "noFlightsScheduledForToday": "There are no flights scheduled for today", "noFlightsScheduledForThisDate": "There are no flights scheduled for this date", "noFlightsAvailable": "There are no flights available for", "noEarlierFlights": "There are no earlier flights available", "noEarlierFlightsAvailable": "There are no earlier flights for this date", "noMoreFlightsAvailable": "There are no more flights for this date", "noFlightsAvailableToday": "There are no flights scheduled for today", "currentlyUnableToDisplayFlight": "We’re currently unable to display flights. Please try again later.", "noFlightsForFilter": "We cannot find any flights matching your selected filters. Try clearing your filters or pick another date.", "noFlightsForSearchResult": "No flights found. Try another date or enter a relevant flight number or destination.", "noFlightsAvailableOnSelectedFilter": "We cannot find any flights matching your selected \n filters. Try clearing your filters or pick another date.", "okay": "Okay", "currentlyUnavailable": "Currently unavailable", "saveFlightError": "Sorry, we’re currently unable to save this flight. Please try again later.", "removeFlightError": "Sorry, we’re currently unable to remove your saved flight. Please try again later.", "areYouSure": "Are you sure?", "cancel": "Cancel", "remove": "Remove", "to": "to", "removeMessage1": "By removing your saved flight", "removeMessage2": "you will not be able to receive any alerts.", "feedBackToastErrorMessage": "Unable to refresh flights. \nLast updated ", "has": "has", "notSaveMessage": "and cannot be saved.", "alert": "<PERSON><PERSON>", "flight": "Flight", "travellingDuringCovid": "Travelling during covid-19", "transitingThroughChangi": "Transiting through Changi", "facilitiesAndServices": "Facilities & Services", "viewAll": "View all", "noMoreFlights": "No more flights", "loadMoreFlights": "Load more flights", "live": "Live", "realTimeUpdates": "Real-Time Updates", "trackTodayFlights": "Track Today's Flights", "arr": "ARR", "dep": "DEP", "flightInformation": "Flight Information", "loadEarlierFlights": "Load Earlier Flights", "flightNotice": "Flight details may change without notice. Please check again closer to the scheduled flight time."}, "airportLanding": {"travelMadeConvenient": "Travel made convenient", "facilitiesServices": "CHANGI Facilities & \nservices", "searchForFacilitiesAndServices": "Search for facilities & services", "allLocationsInPublicTransit": "Public & Transit areas in all terminals", "allLocationsInPublic": "Public area in all terminals", "specialAssistance": "Special Assistance", "filterByLocation": "Filter by location", "filter": "Filters", "location": "Locations", "airline": "Airline", "clearAll": "Clear all", "apply": "Apply filters", "area": "Area", "all": "All", "applyFilters": "Apply filters"}, "flightResultScreenLanding": {"flightResults": "Flights"}, "changiRewardsDetail": {"header": "<PERSON><PERSON>", "cardNo": "Card No: ", "redeemPoints": "Redeem points", "about": "About your points", "thereWasAnError": "There was an error. ", "tapToReload": "Tap to reload.", "reload": "Reload", "yourBenefits": "YOUR BENEFITS", "catalogue": "Redemption Catalogue", "cardNoCopied": "Card number copied!", "rewardCatalogue": "Rewards Catalogue"}, "changiRewardsEcard": {"header": "Rewards Card", "cardNoCopied": "Card number copied!", "scanToPay": "Scan to pay via Changi Pay"}, "setting": {"header": "Settings", "account": "Account <PERSON><PERSON>", "app": "App Settings", "profile": "Profile", "login": "<PERSON><PERSON>", "loginAndSecurity": "Login and Security", "memberships": "Linked Memberships", "interests": "Interests", "notifications": "Notifications", "language": "Language: English"}, "loginManagement": {"header": "Manage Login", "linkingSectionTitle": "Linked Social Accounts", "google": "Google", "facebook": "Facebook", "apple": "Apple", "unlink": "Unlink", "noSocial": "There are no social media accounts linked to your login profile.", "getAccountError": "Unable to retrieve information on your linked social media accounts. Please try again later.", "unlinkMsgConfirm": "Are you sure you want to unlink your {{social}} account?", "unlinkDes": "For your account security, you will be required to log in again.", "confirmSure": "Yes, I'm sure!", "titleUnlinkSuccess": "Your {{social}} account has been unlinked", "desUnlinkSuccess": "Please log in again to continue enjoying your benefits and rewards.", "btnGoBackHome": "Go back to Homepage", "biometricsAuthentication": "Biometrics Authentication", "enable": "Enable {{biometrics}}", "biometricsDisabledDes": "Please enable biometrics on your device to log in with fingerprint or face recognition.", "openPhoneSettings": "Open Phone Settings", "incorrectPassword": "Your password is incorrect.", "confirmOffPopup": {"title": "Are you sure you want to disable biometric login for Changi App?", "description": "You will have to login with your email address and password.", "confirm": "Disable {{biometric}}", "cancel": "Do not disable"}}, "profile": {"sectionTitle": {"personalDetails": "PERSONAL DETAILS", "contactDetails": "CONTACT DETAILS", "vehicleDetails": "VEHICLE DETAILS", "airStaffOnly": "FOR AIRPORT STAFF ONLY", "contactDetailDes": "You can use your verified contact details to log in to your Changi account."}, "verified": "Verified", "pendingVerify": "Pending verification", "airportStaff": "Airport staff", "name": "Name", "expires": "Expires", "modalEditTitle": {"personalDetail": "Edit Personal Details", "verhicleDetail": "Edit Vehicle Details", "airportPass": "Airport Staff", "address": "Edit Address", "editMobileNumber": "Edit Mobile Number", "verifyMobileNumber": "Verify Mobile Number", "editEmail": "Edit Email Address", "verifyEmail": "Verify Em<PERSON> Address", "labelPending": "Verify your mobile number to enable an easier login with your mobile number!", "mobileNumberLable1": "Your mobile number can also be used to login to your Changi account.", "mobileNumberLable2": "Enter the 6-digit OTP that we have sent to your mobile number ", "emailLable1": "Your email address is used to log in, as well as to receive important communications.", "emailLable2": "Enter the 6-digit OTP that we have sent to your email address ", "helpText": "To update your email address, please contact us at feedback.changiairport.com"}, "airportPassPlaceHolder": "Your airport pass number", "dobSuggestionToUpdate": "To update your date of birth, please contact us at feedback.changiairport.com", "inactive": "Your account has not been activated. \nPlease activate your account by clicking on the verification link that we have sent to your email address.", "header": "Edit Profile", "firstName": "First/Given Name", "lastName": "Last/Family Name", "errorFirstName": "Please enter a first name", "errorLastName": "Please enter a last name", "errorPhoneNum": "Please enter a valid mobile number", "errorEmail": "Enter a valid email address", "errorPhoneMinLength": "Mobile number should be minimum 8 number", "gender": "Gender", "male": "Male", "dob": "Date of birth", "female": "Female", "email": "Email address", "residentialCountry": "Country of residence", "phoneNum": "Mobile number", "nationality": "Nationality", "addressDetails": "Address details", "address": "Address", "errorAddress": "Please enter a valid address.", "postalCode": "Postal code", "errorPostalCode": "Only alphanumeric characters are allowed.", "vehicleIU": "In-vehicle IU Number", "placeholderVehicleIU": "Enter your 10-digit IU number", "errorVehicleIU": "Please enter a valid 10-digit IU number.", "areYouAnAirportStaff": "Are you an Airport Staff at Changi Airport?", "yes": "Yes", "no": "No", "staffPassNumber": "Airport pass number", "errorStaffPassNumber": "Enter a valid staff pass number (in alphanumerics).", "staffPassExpiry": "Expiry date", "errorStaffPassExpiry": "Provide the expiry date of your staff pass.", "hintStaffPassNumberAbove": "To enjoy staff benefits and privileges, please enter your Airport pass number and Expiry date", "hintStaffPassNumberBottom": "This can be found on the back of your airport pass", "submit": "Update", "verifyViaOTP": "Verify via OTP", "editMobileNumber": "Edit mobile number", "editEmail": "Edit email address", "success": "Profile saved!", "msg75": "Airport staff details updated", "msg76": "Personal details updated", "msg77": "Address updated", "msg80": "Vehicle details updated", "msg74": "Mobile number updated", "msg72": "Email address updated", "msg71": "You’ve entered an incorrect OTP.", "accountDeletion": {"title": "Account deletion", "label": "I would like to delete my account"}, "placeholder": "Please select a date", "expiryDatePlaceholder": "Your pass expiry date", "titlePopupSave": "Save your changes?", "contentPopupSave": "You have made some changes to your staff details.", "exitPopupText": "Exit without saving", "genderNone": "Prefer not to say", "captchaError1": "<PERSON><PERSON> Failed. ", "captchaError2": "Please try again"}, "deletingAccount": {"title": "Deleting your account", "goBack": "Go back"}, "screenError": {"oops": "Oops!", "oop": "Oops", "somethingWrong": "Looks like something went wrong. Let’s try again in a few minutes.", "reload": "Reload", "refresh": "Looks like something went wrong. \nRefresh to try again."}, "searchV2": {"header": "Search", "recent": {"title": "Recent Searches", "clearAllLabel": "Clear All"}, "autocomplete": {"text": "{{keyword}} in"}, "popular": {"title": "Popular Searches", "hide": "<PERSON>de"}, "placeHolder": {"all": "Shops, restaurants, and more", "all1": "Search for food, shops, facilities", "all2": "Search for products, attractions", "all3": "Try “coffee”, “maps” or “lounge”", "flights": "Tap here to search", "flights1": "Search for flights, airlines and cities", "flights2": "Try “SQ21”, “SIA”, “Tokyo”"}, "tabTitles": {"all": "All", "flights": "Flights"}, "error": {"unavailable": "Currently Unavailable", "unavailableMessage": "Search is temporarily unavailable. We’re working to resolve the issue as soon as possible."}, "itemResult": {"dateFromTo": "{{dateFrom}} to {{dateTo}}", "from": "From", "iscSearchBtn": "Search “{{keyword}}” on iShopChangi", "youMayAlsoLike": "You May Also Like", "noResults": "No Search Results", "noMatching": "Try again using different and more generic keywords."}, "switchTab": {"lookingForFlights": "Looking for flights? ", "searchKeywordFlights": "Search “{{keyword}}” in Flights"}, "tabAll": {"facets": {"PUBLIC": "Public", "TRANSIT": "Transit", "locationList": "Location", "dietary": "Dietary", "category": "Category", "availability": "Availability", "cuisine": "<PERSON><PERSON><PERSON><PERSON>", "dines": "<PERSON>e", "shops": "Shop", "events": "Events", "attractions": "Attractions", "facilities": "Facilities", "promotions": "Promotions", "others": "Others", "clear": "Clear", "open": "Open Now", "free": "Free"}, "filters": {"PUBLIC": "Public Area", "TRANSIT": "Transit Area", "locationList": "Location", "dietary": "Dietary", "category": "Category", "availability": "Availability", "cuisine": "<PERSON><PERSON><PERSON><PERSON>", "dinesCategory": "<PERSON>e", "shopsCategory": "Shop", "eventsCategory": "Events", "attractionsCategory": "Attractions", "facilitiesCategory": "Facilities", "promotionsCategory": "Promotions", "othersCategory": "Others", "dines": "<PERSON>e <PERSON>lters", "shops": "Shop Filters", "events": "Events Filters", "attractions": "Attractions Filters", "facilities": "Facilities Filters", "promotions": "Promotions Filters", "others": "Others Filters", "clear": "Clear", "clearAll": "Clear All", "applyFilters": "Apply Filters", "ok": "Okay", "unableToDisplayFilters": "We’re currently unable to display any filters.\n\n Please try again later."}}, "flightsTab": {"searchTermLabel": "Flight, Airline, Airport, or City", "searchTermPlaceholder": "What are you searching for?", "date": "Date", "or": "OR", "toastSwitchToDeparture": "Switch to Departure", "scanBoardingPass": "Scan Boarding Pass", "datePickerTitle": "Select your Flight Date", "datePickerFooter": "You're a true planner! Just a heads-up-you can save your flights up to one year in advance", "searchFlights": "Search Flights", "ARR": {"title": "Search Arrival Flights", "information": "You’re searching in Arrival flights.", "ask": "Search Departure flights instead."}, "DEP": {"title": "Search Departure Flights", "information": "You’re searching in Departure flights.", "ask": "Search Arrival flights instead."}}, "flightsResult": {"filter": {"tabs": {"flights": "Flights", "arrival": "Arrival Flights", "departure": "Departure Flights", "terminal": "Terminal", "airline": "Airline", "airport_city": "Airport/City", "city_airport": "City/Airport"}, "all": "All", "applyFilters": "Apply Filters", "clearAll": "Clear All", "emptyFilter": "There are no filters available", "okay": "Okay", "noResults": "No results found.", "airlineplaceHolder": "Search Airline", "cityAirportplaceHolder": "Search City/Airport", "departureToggle": "Departure", "arrivalToggle": "Arrival", "terminalTitle": "Terminal", "airlineTitle": "Airline", "apply": "Apply", "filter": "Filters", "terminalOptions": {"All": "All", "T1": "Terminal 1", "T2": "Terminal 2", "T3": "Terminal 3", "T4": "Terminal 4"}}, "statuses": {"confirmed": "Confirmed", "cancelled": "Cancelled", "gateOpen": "Gate Open", "delayed": "Delayed", "boarding": "Boarding", "lastCall": "Last Call", "gateClosing": "Gate Closing", "gateClosed": "Gate Closed", "departed": "Departed", "retimed": "Re-timed", "newGate": "New Gate", "landed": "Landed {{time}}", "diverted": "Diverted", "goToInfoCounter": "Go to Info Counter", "lastBagOnBelt": "Last Bag on Belt", "firstBagOnBelt": "First Bag on Belt", "onSchedule": "On Schedule", "checkGate": "Check Gate at {{time}}"}, "caption": "Flight details may change without notice. Please check again closer to the scheduled flight time.", "journey": {"arr": "{{airport}} to Singapore", "dep": "Singapore to {{airport}}"}, "via": "via {{airport}}", "terminal": {"prefix": "SIN ", "value": "T{{terminal}}"}, "notAvailable": "N/A", "baggageBelt": "Baggage Belt {{baggage<PERSON>elt}}", "checkinRow": "Check-in Row {{checkinRow}}", "gate": "Gate {{gate}}", "noFlightResults": "No Flight Results", "noMatching": "No flights matching your keywords were found on {{date}}.", "suggestionTryAgain": "Please try different keywords or select another date.", "compose": "No flights matching your keywords were found on {{date}}. Please try different keywords or select another date."}}, "search": {"recent": {"title": "RECENT SEARCHES", "clearAllLabel": "Clear all"}, "popular": {"title": "POPULAR SEARCHES"}, "placeHolder": {"all": "Flights, events, shops, restaurants", "dine": "Restaurants, cuisines, offers", "shop": "Shops, offers", "flights": "Flights, airlines, cities", "airport": "Airport info, facilities, amenities", "attractions": "Attractions, tickets", "events": "Events, offers, tickets"}, "resultCaption": {"dine": "DINING", "shop": "SHOPPING", "airport": "FACILITIES & SERVICES"}, "noResults": {"title": "No results found!", "message": "Sorry, we did not find any matches here for your search.\n\n  Try looking under “All”, or try again using different and/or more generic keywords."}, "tabTitles": {"all": "All", "dine": "<PERSON>e", "shop": "Shop", "flights": "Flights", "airport": "Airport", "attractions": "Attractions", "events": "Events"}, "sectionTitle": {"dine": "Dining", "shop": "Shopping", "airline": "Airlines", "flight": "Flights", "cities": "Cities", "facilities": "Facilities & Services", "attractions": "Attractions", "events": "Events"}, "searchFlights": {"cities": "cities", "airlines": "airlines", "flightFor": "flights for", "searchOtherDate": "Search other dates", "selectOtherDate": "Select other dates", "searchOtherDateFlight": "Search for this flight on other dates", "emptyDescriptionFlight": "We cannot find any flights today matching your keywords. Try another keyword, or pick another date.", "emptyDescriptionDepatureFlight": "We cannot find any departure flights matching your keywords. Toggle to the Arrival tab to search for arrival flights, or select another date.", "emptyDescriptionArrivalFlight": "We cannot find any arrival flights matching your keywords. Toggle to the Departure tab to search for departure flights, or select another date.", "emptySearchFlightsDescription": "We cannot find any flights matching your keywords. Try again using different or more general keywords."}, "dinePaginationError": "Unable to load more restaurants, cuisines, offers", "shopPaginationError": "Unable to load more shops, offers", "attractionsPaginationError": "Unable to load more attractions, tickets", "eventsPaginationError": "Unable to load more events, offers, tickets", "retry": "Retry", "scanButtonText": "Scan boarding pass", "viewAllFlight": "View all results in Flights", "viewAllDine": "View all results in Dine", "viewAllShop": "View all results in Shop", "Airport": "View all results in Airport", "viewMore": "View all flights", "viewAllAttractions": "View all results in Attractions", "viewAllEvents": "View all results in Events"}, "linkedMember": {"header": "Linked memberships", "krisFlyerUpdateSuccess": "KrisFlyer membership updated", "updateMessage": "Your details have been updated", "pointsToMiles": "KRISFLYER", "pointsToMilesDesc": "Link your KrisFlyer membership so that you will be able to redeem your Chang<PERSON> Rewards points for <PERSON><PERSON><PERSON><PERSON> miles", "krisFlyerLabel": "Your KrisFlyer membership number", "krisFlyerPlaceholder": "Membership number", "update": "Update", "jewelDoubleRewards": "JEWEL DOUBLE REWARDS", "capitaStarDesc": "Spend Once, Earn Twice! Earn both Changi Rewards points and CapitaStar STAR$® with a minimum spend of S$10 in a single receipt at Jewel Changi Airport!", "capitaStarLabel": "Your CapitaStar registered mobile number", "capitaStarLinkedLabel": "Your CapitaStar Membership Number", "linkMyAccount": "Link account", "clickHere": "De-link your CapitaStar account", "signupNow": "Sign up now!", "notCapitaStarMemberYet": "Not a CapitaStar member yet?", "delinkDesc": "If you wish to de-link your CapitaStar account,", "mobileNoError": "Please enter a valid mobile number", "krisflyerNoError": "Enter a valid 10-digit <PERSON><PERSON><PERSON><PERSON> membership number.", "milesSectionTitle": "Convert 1,440 Changi Rewards points for 200 KrisFlyer miles", "milesSectionConversion": "I agree to enrol for the auto conversion of my Chang<PERSON> Rewards points to <PERSON><PERSON><PERSON><PERSON> miles.\n\nPlease note that your given and family name used in your <PERSON><PERSON><PERSON><PERSON> account must be the same as your Chang<PERSON> Account profile.\n\nI also confirm that I am not currently residing in the European Union, the United Kingdom, the People’s Republic of China or California.", "enrolledSuccess": "Enrolled successfully", "updated": "Updated"}, "redemptionCatalogueScreen": {"newRewards": "New Rewards", "exploreMoreRewards": "Explore More rewards", "exploreMoreRewardsSub": "Lorem ipsum dolor sit amet, consectetur adipiscing elit.", "exploreMoreRewardsNoData": "There are currently no rewards available.", "exploreMoreRewardsError": "We’re currently unable to load our rewards catalogue. Please try again in a short while.", "sortBy": "Sort by", "ascending": "Price (Low to high)", "descending": "Price (High to low)", "az": "Name (A-Z)", "za": "Name (Z-A)", "loadMore": "Load more", "loadMoreToast": "Unable to load more rewards", "yourPointsBalance": "Your points balance", "validTill": "valid till", "points": "points", "redeemablePoints": "redeemable points", "filterBar": {"categoryDefaultLabel": "All Categories", "categoryLabelWithNumber": "Categories ({{amount}})", "sortingLabel": {"lowToHigh": "Points (Low to High)", "highToLow": "Points (High to Low)"}, "category": {"shop": "Shop", "dine": "<PERSON>e", "experiences": "Experiences", "travel": "Travel", "changiEVouchers": "Changi e-Vouchers", "parking": "Parking", "changiExclusives": "<PERSON><PERSON> Exclusives"}}}, "loginAndSecurity": {"header": "Login and Security", "enableFaceId": "Enable Biometric Login", "changePassword": "Change Password", "delinkDesc": "If you wish to de-link your CapitaStar account,"}, "dineShopOfferDetails": {"addVoucherToWallet": "Add voucher to wallet", "getDirection": "Get directions", "daily": "Daily", "viewWebsite": "View website", "viewMenu": "View menu"}, "redeemRewardDetailScreen": {"conditions": "Terms & Conditions", "total": "Total to be paid by redeeming", "select": "Select programme year to redeem your points from", "year": "Select programme year", "enough": "You do not have enough points to redeem this reward. Please choose another programme year to redeem your points from.", "enoughSingle": "You do not have enough points to redeem this reward.", "points": "points", "skuNotFoundTitle": "No reward found", "skuNotFoundMessage": "We’re unable to find this reward.", "browseAllRewards": "Browse all rewards", "swipe": "Swipe to redeem", "base": "Based on your selected programme year, we have adjusted the maximum redeemable quantity.", "goBack": "Go back", "pointsEach": "points each", "programme": "Programme", "programmeYear": "Programme Year", "pts": "pts", "outOfStock": "This reward is currently out of stock and unavailable for redemption.", "limitedAvailability": "Only {{quantity}} available for redemption."}, "exploreScreen": {"exploreChangi": "Explore Changi", "seeMore": "See More", "allLocations": "All locations in Public & Transit", "locationFilter": "Show available listings for:", "exploreChangiError": "We’re currently unable to load our events and attractions. Please try again in a short while.", "bookingToastMessage": "Your bookings will be reserved for {{time}}", "hours": "hrs", "minutes": "mins", "bookingToastButtonText": "Go to cart", "allDates": "All dates", "allLocation": "all locations", "events": {"noEvents": "There are currently no events available", "multipleLocations": "Multiple Locations", "from": "From", "errorToastMessage": "Unable to load more events", "retry": "Retry", "noItems": "There are no listings matching your selected filters.", "noItemsAction": "Choose another date and location to view more of our events and attractions!", "noItemsActionCategory": "Try another category, or choose another date and location to view more of our events and attractions!", "reSelectFilter": "Reselect filters"}, "toolTipECard": "Tap on this button to view and scan your e-card!", "staffPerk": {"title": "Staff Perks", "description": "For our Airport Community", "viewAll": "View all", "item": {"limitedOffer": "Limited"}}, "perksTextV2": "Spend to earn perks for events & premiums!", "baggagePrediction": {"title": "<PERSON><PERSON><PERSON>", "headerNote": {"variant12": "Bags on belt soon", "variant3": "Bags on belt now", "variant4": "Last bag on belt"}, "description": {"variant1": "We’ll need more time to deliver your bags.", "variant2": "Bags for your flight will be presented at belt soon.", "variant3": "Feel free to make your way over to collect your bags.", "variant4": "If you have not received your bags, please approach Lost & Found."}, "redirectLink": {"variant1": "Explore while waiting", "variant23Belt": "Belt {{value}}", "variant23Terminal": "Terminal {{value}}", "variant4": "Lost & Found Counter"}}}, "flightDetails": {"timelineError": "We’re currently unable to load your Travel Checklist. Please try again in a few minutes.", "flightDetailsCRTHeading": "Save flight to unlock", "saveFlight": "Save Flight", "removeFlight": "Remove Flight", "saveYourFlightForPerks": "Save your flight for perks", "notAvailableYet": "Not available yet", "terminal": "Terminal", "checkInRow": "Check-in Row", "gttdCardTitle": "Transport with shortest wait time now", "baggageGate": "Baggage Belt", "baggage": {"trackYourBaggage": "Track your baggage", "lastBagOnBelt": "Last bag on belt", "firstBagOnBelt": "First bag on belt", "baggageTracker": "Baggage tracker"}, "popupConfirmSaveFlight": {"title": "Save Flight?", "saveAndContinue": "Save and Continue", "noThanks": "No, thanks", "message": "Add your return flight to receive updates on flight or gate changes.", "messageOnlineCheckIn": "You may also like to save this flight to receive alerts and access more information.", "onlineCheckInOnly": "Online Check-in Only"}, "newPopupConfirmSaveFlight": {"title": "Flight saved!", "departureMessage": "Save your return flight to receive any flight updates or changes.", "arrivalMessage": "Save your departure flight to receive any flight updates or changes.", "addConnectingFlightButton": "Save a connecting flight", "addReturnFlightButton": "Save a return flight", "cancelButton": "I will do it later", "toastFlightSaved": "Flight saved!", "selectReturnDate": "Select Return Date"}, "popupBookingAlreadyExists": {"title": "Booking already exists", "buttonLabel": "View, edit or cancel booking", "buttonLabel2": "Go back"}, "popupCurrentlyUnavailable": {"title": "Currently unavailable", "content": "Sorry, this service is currently unavailable. Please try again later.", "buttonLabel": "Try again", "buttonLabel2": "Go back"}, "share": {"popupTitle": "Win 4 Business Class\nAir Tickets!", "popupDescription": "Refer a fellow traveler to save this flight and unlock an extra chance in The Great Changi Appscapade!", "popupDescriptionSaveAndShare": "Receive flight updates along with an extra chance in The Great Changi Appscapade when you refer a fellow traveler to save this flight.", "shareNow": "Share Flight Now", "saveAndShare": "Save and Share Flight", "shareMessageEligible": "✈️️ I am travelling on {{flightNumber}} from {{departingCode}} to {{destinationCode}} on {{scheduledDate}}{{actualTimestamp}}!\n🤳🏻 View my flight details and get yours saved to win 4x business class tickets in The Great Changi Appscapade: {{flightDetailFDL}}", "shareMessageNonEligible": "✈️️ I would like to share some useful information for flight {{flightNumber}} from {{departingCode}} to {{destinationCode}} on {{scheduledDate}}{{actualTimestamp}}!\n🤳🏻 More details here: {{flightDetailFDL}}", "findOutMore": "Find Out More", "unableToShareFlight": "Unable to share flight. Please try again", "popupRefereeDescription": "Travelling on this flight? Don't forget to earn an extra chance by saving this flight.", "popupRefereePrimarytext": "Got it", "popupRefereeSecondarytext": "Find Out More", "appscapadeChancePopupTitle": "+1 Chance Unlocked!", "appscapadeChancePopupDescription": "<PERSON>an boarding pass before departure to validate your chances in winning 4 Business Class air tickets and other amazing prizes.", "close": "Close"}, "fastCheckin": {"title": "FAST Check-in", "titleModal": "Fast and Seamless Travel", "content": "FAST Check-in refers to a suite of self-service features for departing passengers to check in more quickly and conveniently using automated systems at Changi Airport.", "buttonSeeMore": "Learn More"}, "flightCheckin": {"title": "Flight Check-in"}, "nearestCarPark": "Nearest Car Park", "airportPickup": "Airport Pick-up", "airportDropOff": "Airport Drop Off", "leavingTheAirport": "Leaving the Airport", "shortestWaitTimeNow": "Shortest wait time now", "moreOptions": "More options", "subTextMoreOptions": "for larger vehicles, alternative transport", "carRental": "Car Rental", "car": "Car", "carDesc": "Check parking information, redeem promotions, or enjoy preferential rates.", "busTrainTaxi": "Bus, Train, Taxi", "shuttleServices": "Shuttle Services", "coachToJohorBahru": "Coach to <PERSON><PERSON>", "transportForLargerGroup": "Transportation for larger groups and ample baggage space", "bookLargerVehiclesToSuiltYourNeeds": "Book larger vehicles to suit your needs", "flightInfo": {"cityTitle": "{{dep}} to {{arr}}", "updatedSystemTime": "Last Updated"}, "freeInsurance": {"title": "FLIGHTSAVER ASSURANCE", "descriptionPrefix": "Get a", "descriptionHighlight": "S$10 Changi e-voucher", "descriptionSuffix": "for unexpected flight delays", "add": "Add", "sharingInfoTitle": "Allow Data Sharing", "sharingInfoDescription": "Your delay coverage is funded by HL Assurance Pte. Ltd. and we will share your personal data with them for audits purposes, in accordance with their Privacy Policy.", "proceed": "Proceed"}}, "flyLocationFilter": {"all": "All", "applyFilters": "Apply filters", "clearAll": "Clear all", "emptyFilter": "There are no filters available", "okay": "Okay", "noResults": "No results found.", "airlineplaceHolder": "Search Airline", "cityAirportplaceHolder": "Search City/Airport", "departureToggle": "Departure", "arrivalToggle": "Arrival", "terminalTitle": "Terminal", "airlineTitle": "Airline", "apply": "Apply"}, "flyDateFilter": {"selectDate": "Select Date", "ok": "OK"}, "eventDetailsScreen": {"viewLocations": "View locations", "getDirections": "Get directions", "multipleLocations": "Multiple locations", "ticketPrices": "Ticket prices", "selectPasses": "Select passes", "about": "ABOUT", "findOutMore": "Find out more", "termsAndConditions": "Terms & CONDITIONS", "entryRequirements": "ENTRY REQUIREMENTS", "errorTitle": "Oops!", "errorContent": "Looks like something went wrong. Let’s try again in a few minutes.", "noEventFound": "No event found", "noEventFoundErrorContent": "We’re unable to find this event or attraction.", "browseAllEvents": "Browse all events", "viewYourCart": "View your cart"}, "popupConfirm": {"confirm": "Confirm", "cancel": "Cancel"}, "subscription": {"header": "Notifications", "settingHeader": "Notification Settings", "emailAndSms": "Email & SMS", "appNotif": "App Notifications", "channelLabel": "How would you like to receive updates?", "preferLanguageLabel": "Preferred language to receive updates:", "updateNotesLabel": "What’s included in my updates?", "confirm": "Confirm", "email": "Email", "sms": "SMS", "crDisabledMessage": "Receive updates on your favourite perks and latest promotions", "crtDisabledMessage": "Receive updates on travel privileges and perks at Changi Airport.", "iscDisableMessage": "Receive updates on special offers, new products and online deals", "english": "English", "bahasa": "Bahasa Melayu", "cn": "简体中文 (ZH)", "rewardsTitle": "REWARDS", "iShopTitle": "MARKETPLACES", "crTitle": "<PERSON><PERSON>", "crMessage": "<p>Changi Rewards is the retail and travel loyalty programme of Changi Airport. Membership is complimentary and comes with exclusive benefits and privileges. <a>Find out more here.</a></p>", "crMessageIShopChangi": "<p>iShopChangi is your online tax and duty-free shopping destination. <a>Find out more here.</a></p>", "crToggleLabel": "Receive updates on your favourite perks and latest promotions", "crDescription": "Receive updates on new rewards and members-only privileges", "crUpdateNote": "<ul><li>Year-long GST savings</li><li>Event invitations (e.g. movie screenings, behind-the-scenes airport tours)</li><li>Free parking at the airport</li><li>Member-only deals</li><li>Shopping rebates convertible to e-vouchers or airline miles</li></ul>", "crtTitle": "<PERSON><PERSON> Travel", "crtMessage": "<p>Changi Rewards Travel is the airport loyalty programme that rewards travellers with a host of privileges and services. <a>Find out more here.</a></p>", "crtToggleLabel": "Receive updates on travel privileges and perks at Changi Airport", "crtUpdateNote": "<ul><li>Member-only offers</li><li>Air ticket discounts</li><li>Travel privileges such as lounge access</li></ul>", "disableNotif": "Unsubscribe", "crConfirmTitle": "Are you sure you want to unsubscribe from Changi Rewards updates?", "crConfirmMessage": "You will no longer receive any email & SMS updates about your Changi Rewards perks and latest promotions.", "crtConfirmTitle": "Are you sure you want to unsubscribe from Changi Rewards Travel updates?", "crtConfirmMessage": "You will no longer receive any email & SMS updates about your Changi Rewards Travel privileges and perks.", "shopTitle": "LATEST IN ONLINE SHOPPING", "iscTitle": "iShopChangi", "iscMessage": "<p>iShopChangi is your online tax and duty-free shopping destination. <a>Find out more here.</a></p>", "iscToggleLabel": "Receive updates on special offers, new products and online deals", "iscUpdateNote": "<ul><li>Global product launches</li><li>Travel exclusives</li><li>Online-only offers and special discounts</li></ul>", "iscConfirmTitle": "Are you sure you want to unsubscribe from iShopChangi updates?", "iscConfirmMessage": "You will no longer receive any email & SMS updates about iShopChangi offers and promotions.", "changiAirport": "CHANGI AIRPORT", "emailUpdates": "Email updates", "caUpdateNotesLabel": "Stay in the know! Get email updates on the latest news and events that’s happening at Changi Airport, including:", "caUpdateNote": "<ul><li>Sneak peeks on upcoming events, such as the school holidays special and exclusive pop-up stores</li><li>Get inspired with new places to explore</li><li>Maximise your Changi experience with insider tips</li><li>Discover the insights behind developments and happenings related to Changi Airport and its community</li></ul>", "caConfirmTitle": "Are you sure you want to unsubscribe from Changi Airport email updates?", "caConfirmMessage": "You will no longer receive the latest news and happenings about Changi Airport.", "unableToLoad": "Unable to load preferences", "preferencesSaved": "Preferences saved!", "errorTitle": "Unable to save preferences", "errorDescription": "Sorry, we’re currently unable to save your preferences.", "propmtPermissionTitle": "Your notifications are currently turned off.", "propmtPermissionDescription": "Open Settings on your device to enable notifications to get the latest information on your flights, promotions and bookings.", "actionOpenSettings": "Settings", "actionRemindMeLater": "Remind Me Later", "close": "Close", "tryAgain": "Try again", "appNotification": {"allThingTravel": "All Things Travel", "flightInformation": "Flight information", "descriptionFlightInformation": "Receive real-time flight updates including delays and cancellations", "baggageInformation": "Baggage Information", "descriptionBaggageInformation": "Receive first and last bag statuses for your flight", "bestDealsAndLatestEvents": "Best Deals & Latest Events", "shoppingDeals": "Shopping Deals", "fBPromotions": "F&B Promotions", "latestEvents": "Latest Events", "changiRewardsMemberExclusives": "<PERSON><PERSON> Member Exclusives", "transactionsAndOrders": "Transactions & Orders", "changiRewards": "<PERSON><PERSON>", "changiPay": "Changi Pay", "getNotifiedWhenYouMakeATransaction": "Get notified when you make a transaction", "iShopChangi": "iShopChangi", "receiveUpdatesOnYourOrders": "Receive updates on your orders", "changiEats": "Changi Eats", "stayViligant": "Stay vigilant", "majorNewsAndAlerts": "Major news and alerts", "stayViligantDescription": "Receive important updates and announcements on airport operations and travel restrictions", "flightInfor": {"title": "Flight info", "description": "Receive updates about flights you are interested in", "flightStatus": "Flight Status (Delays, reschedules and cancellations)", "confirm": "Confirmed Flights", "boarding": "Boarding", "gateClosing": "Gate closing", "landed": "Landed Flights"}, "preferencesSaved": "Preferences Saved", "eventsAndPerks": {"title": "Events, Perks & Credits", "eventReminders": "Event Reminders", "eventRemindersDescription": "Receive reminders about your upcoming events and bookings", "perks": "Perks", "perksAndCredits": "Perks & Credits", "perksDescription": "Get notified when you earn and or unlock a perk for redemption", "perksAndCreditsDescription": "Get notified when you earn or unlock perks or credits, and when your perks or credits are expiring."}, "preferenceV2": {"travel": {"title": "Travel", "subTitle": "Flight & Baggage Alerts", "description": "Receive information and real-time updates on flight and baggage statuses"}, "marketing": {"title": "Marketing", "subTitle": "Events & Promotions", "description": "Stay informed on great deals and latest happenings at Changi Airport and Jewel"}, "services": {"title": "Services", "subTitle": "Transactions & More", "description": "Receive updates for your transactions, orders, perks, credits, as well as event reminders and other account services"}}}}, "errorOverlay": {"header": "Error", "variant1": {"title": "Oops!", "message": "Looks like something went wrong. Let’s try again in a few minutes.", "reload": "Reload", "callToAction": "Call to action", "anySecondaryCta": "Any secondary CTA"}, "variant2": {"title": "Oops, something went wrong...", "message": "An unexpected error occurred while processing your request.", "pleaseTryAgain": "Please try again.", "pleaseTryAgainInHours": "Please try again in a few hours.", "errorCode": "<Error code {{errorCode}}>", "retry": "Retry"}, "variant3": {"title": "No internet connection", "message": "Please check your connection again, or connect to a different internet network.", "retry": "Retry", "useWithoutInternet": "Use without internet"}, "variantSection": {"title": "Oh no!", "message": "We're currently unable to load our events and attractions. Please try again in a short while", "reload": "Reload"}}, "playPassWebView": {"bookingTitle": "Pass Options", "uploadReceiptTitle": "Receipt Upload", "cartTitle": "Your Cart", "takePhoto": "Take Photo", "choosePhoto": "<PERSON>ose Photo", "cancel": "Cancel", "uploadPhoto": "Upload Photo", "useCamera": "Use Camera"}, "redeemRewardSuccess": {"header": "Redeemed", "viewInWallet": "View in your perks", "viewInVPR": "View in Vouchers, Prizes & Redemptions", "viewInYourAccount": "View in Your Account", "yourRewardsAdded": "Your rewards have been added to your wallet.", "issuingError": "There was a problem issuing your voucher. Please contact <NAME_EMAIL>", "krisflyerSuccessDescription": "You will receive your redeemed Kris<PERSON><PERSON> miles within 21 business days from the date of redemption.", "exploreMoreRewards": "Explore More Rewards", "contactUs": "Contact us", "quantity": "Quantity", "viewInPromoCodes": "View in Promo Codes"}, "privilegesScreen": {"quantity": "Quantity", "expiry": "Expiry", "viewMore": "Load more", "header": "Privileges", "enjoy": "Enjoy our range of exclusive privileges. Simply flash your membership card and be rewarded!", "more": "Get more privileges", "unclock": "Redeem your Changi Rewards points or upgrade your Changi Rewards Travel tier to unlock more privileges.", "unable": "Unable to load more privileges.", "your": "Your Privileges", "upgrade": "upgrade for more CHANGI rewards travel Privileges"}, "krisflyerRedeem": {"title": "Confirm Krisflyer Details", "description": "<p>Ensure that your details are entered accurately, as you cannot change these details after redemption. All fields are mandatory.</p>", "descriptionLinked": "<p>Ensure that your details are entered accurately, as you cannot change these details after redemption. <a>Update your details</a></p>", "cancel": "Cancel", "submit": "Submit", "krisflyerNoLabel": "Your KrisFlyer Membership number", "krisflyerNoPlaceholder": "Membership number", "krisflyerNoError": "Please enter a Membership number", "firstNameLabel": "First Name", "firstNamePlaceholder": "Enter your first name", "firstNameError": "Please enter a first name", "lastNameLabel": "Last Name", "lastNamePlaceholder": "Enter your last name", "lastNameError": "Please enter a last name", "linkKrisflyerNo": "Link my <PERSON><PERSON><PERSON><PERSON> membership to my <PERSON><PERSON> Account", "residencePolicyText": "I confirm that I am not currently residing in the European Union, the United Kingdom, the People’s Republic of China or California.", "residencePolicyError": "Please confirm the status of your current residence"}, "eventConfirmation": {"editBookingFromWallet": "You can edit your booking from the wallet.", "addToCalendar": "Add to calendar", "viewInWallet": "View in wallet", "paymentSummary": "PAYMENT SUMMARY", "viewBreakDown": "View breakdown", "placesToExplore": "PLACES TO EXPLORE", "noEventFound": "No event found", "bookingNotFound": "Booking Not found", "unableToLoadBooking": "Sorry, we’re unable to load your booking details. To view your bookings, please view your wallet.", "viewBookings": "View bookings", "eventConfirmation": "Event Confirmation", "errorTitle": "Oops!", "errorContent": "Looks like something went wrong. Let’s try again in a few minutes."}, "notificationTabName": {"all": "All", "flights": "Flights", "advisores": "Advisories", "eventAndPromotion": "Events & Promotions", "transactions": "Transactions", "eventAndPerk": "Events & Perks"}, "notification": {"enableNotificationTitle": "Enable notifications to get the latest information on flights, promotions and bookings.", "enableNotificationButton": "Enable notifications", "enableNotificationContent": "Turn on your phone notifications to get the latest information on flights, promotions and bookings.", "deleteAllTitle": "Are you sure you want to delete all messages?", "deleteAllMessage": "This will clear all messages in your[categoryName] inbox. Once deleted you will not be able to retrieve them.", "deleteAllFirstButton": "Yes, delete all messages", "deleteAllSecondButton": "No, do not delete", "deleteErrorToast": "Unable to delete your notification(s). Please try again in a few minutes.", "deleteAllSuccessful": "Your messages have been deleted", "deleteSuccessful": "Message deleted", "filterNotificationInboxV2": {"Advisories": "Advisories", "Travel": "Travel", "Marketing": "Marketing", "Services": "Services"}, "emptyNotiTitleV2": "No notifications", "emptyNotiContentV2": "You’re all caught up! Do check back and stay tuned for the latest updates.", "noPermissionNotiContent": "Grant permission on your device to receive push notifications according to your preference settings.", "allowNotiButton": "Allow Notifications", "turnOffPermissionNotiTitle": "Your notifications are turned off", "turnOffPermissionNotiContent": "Don’t miss out—turn on your notifications to get real-time flight updates and exclusive offers!", "readAllSuccess": "All messages marked as read"}, "emptyNotification": {"all": {"title": "No notifications", "on_notification": {"description": "You’re all caught up! Check back here for updates on your saved flights and transactions. \n \nStay tuned for exclusive offers and events too!"}, "off_notification": {"description": "Don’t miss out! Turn on your notifications to get real-time flight updates and exclusive offers!", "turnOnNotification": "Turn on notifications"}}, "flights": {"title": "No flight updates", "on_notification": {"description": "Save your flights to get important flight updates and reminders.", "searchFlights": "Search flights"}, "off_notification": {"description": " Don’t miss out! Turn on your notifications to get important flight updates and reminders.", "turnOnNotificationForFlight": "Turn on notifications for flights", "searchFlights": "Search flights"}}, "advisories": {"title": "No advisories", "on_notification": {"description": "Any important airport updates and travel advisories will appear here."}, "off_notification": {"description": "Don’t miss out! Turn on your notifications to stay updated on airport announcements and travel advisories.", "turnOnNotificationForAdvisories": "Turn on notifications for advisories"}}, "eventPromotions": {"title": "No events & promotions", "on_notification": {"description": "Watch this space for exciting new offers and events!", "exploreOurAttractions": "Explore our attractions"}, "off_notification": {"description": "Don’t miss out! Turn on your notifications to get the latest updates on exciting events and offers!", "turnOnNotificationForEventPromotions": "Turn on notifications for events & promos", "exploreOurAttractions": "Explore our attractions"}}, "transactions": {"title": "No current transactions", "on_notification": {"description": "When you make a purchase or booking, your transaction will appear here."}, "off_notification": {"description": "Don’t miss out - turn on your notifications to receive transactions updates and alerts for purchases or bookings made with Changi App.", "turnOnNotificationForTransactions": "Turn on notifications for transactions"}}, "eventPerks": {"title": "No events & perks", "on_notification": {"description": "Watch this space for exciting new offers and events!", "exploreOurAttractions": "Explore Events & Attractions"}, "off_notification": {"description": "Don’t miss out—Turn your notifications on to get exciting new offers and updates on events!", "turnOnNotification": "Turn on Notifications", "exploreOurAttractions": "Explore Events & Attractions"}}}, "privacyPolicy": {"header": "Privacy Policy"}, "termsOfUsePolicy": {"header": "Terms of Use"}, "scanCode": {"description": "Position the barcode on your boarding pass within the window above. ", "flightNotFound": {"title": "Flight not found", "description": "Your flight may have departed or landed.", "firstButton": "Cancel", "secondButton": "Try again"}, "invalidBoardingPass": {"title": "Invalid boarding pass", "description": "Please try again or upload another image of your boarding pass.", "firstButton": "Cancel", "secondButton": "Try again"}, "noBoardingPassDetected": {"title": "Flight not found", "description": "Please try again or upload another image of your boarding pass.", "firstButton": "Cancel", "secondButton": "Try again"}, "needAccessPermission": {"title": "“Changi App” cannot access your camera", "description": "You won’t be able to make cashless payments, or scan your boarding pass! To grant access, tap on “<PERSON><PERSON><PERSON>” and turn on the Camera option for “Changi App”.", "firstButton": "Settings", "secondButton": "Ok"}}, "addEventToCalendar": {"addToCalendarLable": "Add to calendar", "needAccessPermission": {"title": "“Changi App” cannot access your calendar", "description": "You won’t be able to add events to your calendar.\n\nTo grant access, tap on “Settings” and turn on the Calendars option for “Changi App”.", "firstButton": "Settings", "secondButton": "Ok"}}, "updateOlderAccount": {"title": "Before you continue", "termAndConditionTitle": "Terms and Conditions", "agreeTermAndConditionTitle": "Agree Terms And Conditions", "completeYourProfile": "Complete your profile", "continue": "Continue", "dob": "Date of birth", "phoneNum": "Mobile number", "readLess": "Read less ", "popupConfirmTermAndCondition": {"comfirmationRequired": "Confirmation required", "content": "To continue using <PERSON><PERSON> Account, please accept the terms and conditions.", "btnAgree": "Agree to Terms & Conditions", "logOut": "Log out"}, "welcomeToLogin": "Thank you! You've been logged in"}, "requestPermission": {"camera": {"title": "\"Changi App\" would like to access your camera", "message": "This will allow you to scan QR codes for cashless payment, scan your boarding pass for travel privileges, or take photos for feedback purposes.", "buttonPositive": "Allow", "buttonNegative": "Don't Allow"}, "calendar": {"title": "\"Changi App\" would like to access your calendar", "message": "This lets you save events to your Calendar.", "buttonPositive": "Allow", "buttonNegative": "Don't Allow"}, "gallery": {"title": "\"Changi App\" cannot access your photos", "message": "This lets you share photos from your library, or save photos to your Camera Roll.", "cannotTitle": "\"Changi App\" would like to access your photos", "cannotMessage": "To grant access, tap on \"<PERSON><PERSON><PERSON>\" and turn on the Photos option for \"Changi App\"", "buttonPositive": "Allow", "buttonNegative": "Don't Allow", "firstButton": "Settings", "secondButton": "Not Now"}, "microphone": {"title": "\"Changi App\" would like to access your microphone", "message": "This enables the video-calling function for you to connect with our Virtual Changi Experience Ambassadors.", "buttonPositive": "Allow", "buttonNegative": "Don't Allow"}}, "walletAccountScreen": {"myOrder": {"filterOrdersBy": "Filter orders by", "filters": "Filters", "items": "items"}, "playPass": {"filterBy": "Filters", "location": "Locations", "categories": "<PERSON><PERSON> passes by"}, "perk": {"filterBy": "Filters", "categories": "Filter perks by", "instantWinPrizeText": "Redeem via email!"}, "playPassDetail": {"howToUse": "How to Use", "description": "The body copy will go here. Free form text from Ascentis CMS. Will contain Merchant URL. Different for each Merchant.To find out more information, visit website at www.information.com"}}, "justForYouCarouselScreen": {"title": "JUST FOR YOU", "shopAll": "Shop All"}, "carParkScreen": {"header": "Parking", "caculatorTab": {"title": "Car park rate calculator", "oldDescription": "Note: This car park rate calculator is for cars and vans only.  For motorcycles, a flat rate of $1.30/day applies.", "description": "Note: This car park rate calculator is for cars and vans only. For motorcycles, a flat rate of $1.40/day applies.", "titleDropdown": "Car Park", "descriptionDropdown": "For short term parking (90 mins). Parking charges of $5 per 30-minute block will apply for parking longer than 90 minutes.", "moreTime": "More than 24 hours", "arriveOn": "Arrive on", "leaveOn": "Leave on", "totalFee": "Total fee"}, "findMyCarTab": {"title": "LOCATE YOUR VEHICLE", "description": "Enter the digits of your vehicle licence plate.", "submit": "Submit", "selectYourVehicle": "SELECT YOUR VEHICLE", "titleNotFound": "No vehicle found", "descriptionNotFound": "We are unable to find a vehicle matching your licence plate.\n\n Kindly check that you’ve entered the correct licence plate and try again!", "buttonNotFound": "Okay", "titleSystemError": "System Error", "descriptionSystemError": "Oops! We are having trouble connecting to the service right now. Please try again later.", "buttonSystemError": "Okay", "select": "Select", "unableToLoadImage": "Unable to load image"}}, "orderDetailPage": {"orderDetailCard": {"orderDateLabel": "ORDER DATE", "paymentMethodLabel": "PAYMENT METHOD"}, "consignMentList": {"shippedBy": "Shipped by ", "orderNoLabel": "Order No: ", "subTotalLabel": "Sub-Total", "collectionMethodLabel": "Collection Method", "collectionDateTimeLabel": "Collection Date/Time", "collectionAddressLabel": "Collection Address", "collectionNumberLabel": "Contact Number", "trackShipment": "Track Shipment", "deliveryMethodLabel": "Delivery Method", "deliveryDateTimeLabel": "Delivery Date/Time", "deliveryAddressLabel": "Delivery Address", "freePrefix": "FREE: ", "quantityLabel": "Quantity", "itemPriceLabel": "<PERSON><PERSON>", "itemUnit": "item(s)", "freeText": "FREE"}, "orderTotal": {"orderTotalLabel": "Order Total", "orderSummaryLabel": "Order summary", "subTotalLabel": "Subtotal", "netTotalLabel": "Net total", "shippingLabel": "Shipping", "amountPayableLabel": "Amount payable", "changiRewardsPointsEarnedLabel": "<PERSON><PERSON>s points earned", "estimatedEarnedChangiRewardsPoints": "Estimated Earned <PERSON><PERSON> Points"}, "helpTextLabel": "Need help with your orders?", "contactUs": "Contact us"}, "notificationsScreen": {"inbox": "Inbox", "greaterThan99": "99+", "receivedDay": {"today": "Today", "yesterday": "Yesterday", "earlier": "Earlier"}, "settings": "Settings", "deleteAll": "Delete All"}, "grantPermission": {"title": "Connect with us!", "description": "Don’t miss out on latest news and offers curated specially for you.  Turn on notifications and manage your preferences in just a few taps!"}, "unableLoadDirectionLocation": {"title": "Unable to load location", "description": "Sorry, we’re currently unable to load this location.", "button": "Okay"}, "serviceUnavailebleDirectionLocation": {"title": "Currently unavailable", "description": "Sorry for the inconvenience. This service is not ready to use.", "button": "Okay"}, "toilets": {"header": "<PERSON><PERSON><PERSON>"}, "changiMillionaire": {"header": "Changi Millionaire"}, "askMax": {"header": "Ask Max"}, "changimap": {"header": "Getting Around", "header-v2": "Changi Airport & Jewel Map", "header-search": "Search", "more-info": "More info", "get-direction": "Get Directions", "placeholder-search": "Where do you want to go?", "placeholder-from": "Starting Location", "placeholder-to": "Destination", "walking-Route": "Walking Route", "accessible-Route": "Accessible Route", "start": "Start", "min": "min", "empty-direction": "We can’t find a way to your destination.", "update-locations": "Update Locations", "exit": "Exit", "from": "from", "to": "to", "To": "To", "titleSearchLocationFromTo": "Get Directions"}, "staffPerkListing": {"header": "Changi Staff Perk", "item": {"startDateMsg": "From {{date}}", "limitedOffer": "Limited Offer", "newlyAdded": "New"}, "filterBar": {"title": "Staff Perks", "locationText": {"default": "Location: All", "publicArea": "Public area", "transitArea": "Transit area", "publicNTransitAreas": "Public & Transit areas", "allTerminals": "all terminals", "displayFormat": "{{area}} in {{terminal}}"}, "locationTextDefault": "Location", "categoryPill": {"public": "Public", "transit": "Transit", "newlyAdded": "Newly Added", "dining": "Dining", "shopping": "Shopping", "iShopChangi": "iShopChangi", "services": "Services", "limitedOffer": "Limited Offer"}}, "filterBs": {"title": "Filters", "sectionLocation": "Location", "sectionCategories": "Categories", "sectionSortBy": "Sort By", "location": {"title": "Location", "publicArea": "Public Area", "transitArea": "Transit Area", "jewel": "Jewel", "terminal1": "Terminal 1", "terminal2": "Terminal 2", "terminal3": "Terminal 3", "terminal4": "Terminal 4"}, "category": {"title": "Categories", "newlyAdded": "Newly Added", "dining": "Dining", "shopping": "Shopping", "iShopChangiOffers": "iShopChangi Offers", "services": "Services", "limitedOffers": "Limited Offers"}, "sortBy": {"title": "Sort By", "latestAddedDate": "Latest Added Date", "az": "A-Z"}, "clearBtn": "Clear", "clearAllBtn": "Clear All", "applyFiltersBtn": "Apply Filters"}, "groupBuyBanner": {"buy": "Buy", "view": "View", "groupBuy": "GROUP BUY", "newDeals": "NEW DEALS EVERY MONDAY", "nextWeekDeal": "Swipe for next week’s deal"}}, "yourReward": {"header": "Your Reward", "markAsUsed": "Mark as used", "used": "Used", "showRewardCard": "Show Rewards Card", "termAndCondition": "Terms & conditions", "quantity": "Quantity", "redemptionLocation": "Location", "chooseALocation": "Choose a location", "swipeToUse": "Swipe to use"}, "yourRewardRedeemSuccess": {"header": "Redeemed"}, "offerDetail": {"header": "Offer <PERSON>"}, "expiryFlightNotification": {"title": "Flight details are not available", "message": "This flight may have already departed or landed, or is no longer available.", "buttonText": "Okay"}, "rootedDetections": {"title": "Check device security", "description": "To protect you from security risks, Changi App cannot be used on this device. This may be because your device is rooted / jailbroken, installed with less secure applications, or if your app is installed on external storage."}, "atomMap": {"header": "Getting Around"}, "staffPerk": {"emptyContent": "There are currently no new staff offers or perks available for this category. Check back again soon!"}, "scanBoardingPass": {"passAlreadyScanned": {"title": "This boarding pass has been used", "description": "Using a boarding pass to participate in Great Changi Appscapade is limited to one use.", "buttonCancelLabel": "Cancel"}, "notSavedInAdvance": {"title": "This boarding pass is not eligible for The Great Changi Appscapade", "description": "You will need to save this flight 24 hours prior to your departure time.", "buttonCancelLabel": "Cancel", "buttonAcceptLabel": "View Flight Info"}, "notDepartureFlight": {"title": "This boarding pass is not eligible for The Great Changi Appscapade", "description": "It is exclusively for departing flights and does not apply to arriving flights", "buttonCancelLabel": "Cancel", "buttonAcceptLabel": "View Flight Info"}, "notTravellingOnFlight": {"title": "This boarding pass is not eligible for The Great Changi Appscapade", "description": "It is exclusively for those who are departing on this flight.", "buttonCancelLabel": "Cancel", "buttonAcceptLabel": "View Flight Info"}, "maxEntriesSubmitted": {"title": "Unable to scan your boarding pass", "description": "Please try again later.", "buttonCancelLabel": "Cancel"}, "entryAlreadyExists": {"title": "You have already participated in Great Changi Appscapade with this flight", "description": "You cannot scan multiple boarding passes for the same flight.", "buttonCancelLabel": "Cancel", "buttonAcceptLabel": "View Flight Info"}}, "monarchPrivilegesScreen": {"header": "Monarch Privileges"}, "redemptionCatalogueScreenV2": {"titlePage": "Redeem", "yourTotalBalanceLabel": "Your Total Balance", "seeAllExpiryBtn": "See All Expiry", "unableToLoadDetailsLabel": "Unable to load details", "expiringLabel": "Expiring", "redeemablePointsLabel": "redeemable points", "pointsTabTitle": "Points", "perksTabTitle": "Perks", "perksTabCaption": "Perks are automatically credited when you scan your Changi Rewards card for qualifying purchases.", "thereAreCurrentlyNoRewardsAvailable": "There are currently no rewards available.", "validTillTxt": "Valid till {{value}}", "redeemEventsAndPremiums": "Redeem events and premiums", "youHave": "You have"}, "creditsScreen": {"header": "Credits", "valueDescription": "Candy Carnival Credits", "redeemableCreditUnit": "{{value}} redeemable credit", "redeemableCreditUnit_plural": "{{value}} redeemable credits", "creditsExpire": "{{value}} credit expire on {{date}}", "creditsExpire_plural": "{{value}} credits expire on {{date}}", "purchaseMoreCreditsBtn": "Purchase More Credits"}, "nativeLoginScreen": {"login": "<PERSON><PERSON>", "signUp": "Sign up", "submit": "Submit", "logInWith": "Log in with", "emailAddress": "Email Address", "enterPassword": "Enter password", "emptyEmailError": "Enter your email address.", "invalidEmailError": "Enter a valid email address.", "biometricsTitle": "Biometric login for Changi App", "biometricsLocked": "Maximum attempts exceeded, please login using other methods", "invalidEmailPassword": "Your email and password do not match. Please try again.", "disabledAccount": "Your account is temporarily unavailable. Please contact us", "sendEmailError": "Sorry, we’re unable to process your request. Please try again later.", "emptyPassword": "Enter your password", "captchaFailed": "<PERSON><PERSON> failed. Please try again", "getStarted": {"general": "Unlock more travel & shopping privileges at Changi Airport & Jewel", "playPass": "Unlock more adventures and exclusive privileges as you book ahead & skip the queues with <PERSON><PERSON>", "appscapade": "Unlock seamless travel and let more rewarding escapades take flight", "siftAndPick": "Unlock more travel & shopping privileges as you shop trendy fashion labels from all around the world", "loginOrCreate": "Log in or create a new account"}, "resetPassword": {"title": "Reset password", "descriptionPrimary": "Enter the email address associated with your Changi account.", "descriptionSecondary": "A link to reset your password will be sent to this email."}, "sentEmail": {"title": "Reset link sent", "descriptionPrimary": "Check your email for a link to reset your password.", "descriptionSecondary": "Password reset email will not be sent if there is no account registered with the email address you provided."}, "loginPage": {"title": "Log in to your account", "titleSavedEmail": "Welcome back", "txDescription": "Enter the email address and password associated with your Changi account.", "txDescriptionSavedEmail": "Please enter your password for ", "notYou": "(Not you?)", "forgotPassword": "Forgot Password?"}, "signUpPage": {"title": "Sign up for a new account", "btnContinue": "Continue", "verifyPass": {"passwordErrorMin": "Must include at least 8 characters", "passwordErrorMinAndNoSpace": "Must include at least 8 characters and no spaces", "passwordErrorHasNumeral": "Must include at least one numeral", "passwordErrorHasUppercase": "Must include one or more uppercase characters"}, "error": {"accountExists": "Please try signing up with another email address."}}, "verifyEmail": {"title": "Verify your email address", "description": "Enter the 6-digit OTP that we have sent to your email address:", "resendOtp": "Resend OTP", "resendOtpWaiting": "Resend OTP in {{time}} secs", "error": {"otpInvalid": "The OTP is either invalid or has expired. Please request a new OTP.", "otpIncorrect": "The OTP is incorrect. Please try again or request for a new OTP.", "tooManyRequest": "Too many codes requested, please try again in a few minutes"}}, "supplementData": {"title": "Complete your profile", "description": "You are just one step away from enjoying exclusive privileges with your account.", "mobileNumber": "Mobile Number", "countryCode": "Country Code", "firstName": "First/Given Name", "lastName": "Last/Family Name", "dob": "Date of Birth", "warningDob": "You must be 16 years old or above.", "addPromo": "Add promo or referral code", "promoCode": "Promo Code", "referralCode": "Referral Code", "selectAnOption": "Select an option", "apartTitle": "Be part of the Airport Community", "signUpTitle": "Sign up for updates", "apartDescription": "I’m currently working at Changi Airport or Jewel Changi Airport", "signUpDescription": "Send me news about exclusive deals, benefits and other promotions from the CAG Group and its business partners by text message.", "backButtonAlertTitle": "Missing profile details", "backButtonAlertContent": "These details are required for your Changi account.", "backButtonAlertPreferButtonLabel": "Complete Profile", "backButtonAlertNotPreferButtonLabel": "Log Out", "optional": "Optional", "error": {"emptyMobileNumber": "Enter your mobile number", "invalidMobileNumber": "Enter a valid mobile number.", "existedMobileNumber": "An account with this mobile number exists. Log in to this account or use a different number instead.", "emptyFirstName": "Enter your first name", "emptyLastName": "Enter your last name", "invalidName": "Only alphabetical letters are accepted.", "emptyDob": "Choose your date of birth.", "under16": "You must be 16 years old or above.", "invalidPromoCode": "This code is invalid."}}, "success": {"fullName": "Hi {{fullName}}!", "firstMessage": "Your account has been created.", "secondMessage": "You can now enjoy members-only benefits and privileges.", "ctaButtonLabel": "Let’s go!", "redirectingYouBackToMessage": "Redirecting you back to", "redirectingYouBackMessage": "Redirecting you back", "during": "in {{time}}s", "titlePopup": "Biometrics login has not been enabled", "messagePopup": "You can set it up later in your device settings for more convenient access.", "titlePopupOS": "Do you want to allow \"Changi App\" to use Biometrics login?", "faceId": "Face ID", "fingerprint": "Fingerprint", "messagePopupOS": "You will be able to log in to your account more quickly and securely.", "allow": "Allow", "notAllow": "Don't Allow", "promptMessageAndroid": "Biometric login for Changi App", "subTitleBiometrics": "Log in using your biometric credential"}, "linkAccount": {"title": "Verify it's you", "descriptionEmail": "You have an existing Changi account. Please enter your password for ", "descriptionEmailTail": " to link your social account.", "description": "This will allow you to login easily with your social account next time!", "alertTitle": "Verify your account to link your social account", "alertButtonPrimary": "Enter Account Password", "desEmailSecondary": "We found an existing account for ", "desEmailTailSecondary": "Please enter your password for this Changi account if you wish to link it to your social account for future logins.", "alertTitleSecondary": "Verify your account", "desSecondary": "Just a few more steps to link your social account for future logins!", "alertButtonSecondary": "Return to Login", "signInLinkedAccount": "Sign in with your linked social account(s)", "linkedSocialContent": "Your Changi account is currently linked to your social account.", "signInLinkedAccountDescription": "Sign in with your linked social account to continue. "}, "originatingPortal": {"changiApp": "Changi App", "changiMillionaire": "Changi Millionaire", "appscapade": "Appscapade", "siftAndPick": "Sift&Pick"}}, "changiRewardsPrivileges": {"headerTitle": "<PERSON><PERSON>s Privileges", "memberTabTitle": "Member", "goldTabTitle": "Gold", "platinumTabTitle": "Platinum", "monarchTabTitle": "Monarch"}, "sessionExpiredPopup": {"title": "You've been logged out", "message": "Your login session has expired. For your account security, please log in again to continue enjoying your benefits and privileges.", "loginBtn": "<PERSON><PERSON>", "doItLaterBtn": "I'll do it later"}, "vouchersPrizesRedemptionsScreen": {"title": "Vouchers, Prizes & Redemptions", "redeemViaEmail": "Redeem via email", "validUntil": "Valid till {{date}}", "appscapadeTitle": "CHANGI APPSCAPADE", "vouchersPassesRedemptionsScreen": "Vouchers, Passes & Redemptions"}, "incomingCardError": {"title": "Unable to display flight information", "description": "It will be back shortly"}, "bookingsOrdersScreen": {"title": "Bookings & Orders", "titleHasCredits": "Bookings, Orders & Credits", "bookingCardTitle": "BOOKING", "orderCardTitle": "ORDER", "orderConsignment": {"content_singular": "{{price}} · {{itemAmount}} item", "content_plural": "{{price}} · {{itemAmount}} items"}}, "orderDetailsScreen": {"title": "Order {{orderId}}", "totalCardPromoTitle": "Promo Applied - {{code}}", "totalCardRedeemLabel": "Redeemed {{value}} Rewards Points"}, "insuranceOrderDetailsScreen": {"title": "Your Order", "editBooking": "Edit Booking", "forEnquiries": "For Enquiries", "planDetails": {"title": "<PERSON><PERSON> (Superior)", "policyNumber": "Policy Number: {{number}}", "disclaimer": "ChangiAssure is a travel insurance product which is underwritten by HL Assurance Pte. Ltd. and is distributed by Changi Travel Services Pte Ltd.", "makeClaimButton": "Make a Claim"}, "paymentSummary": {"title": "PAYMENT SUMMARY", "amountPaid": "Amount paid", "viewBreakdown": "View breakdown", "subtotal": "Subtotal", "promoDiscount": "Promo Discount"}, "goToChangiAssure": "Go to ChangiAssure", "contactChangiAssure": "Contact ChangiAssure", "call": "Call", "email": "Email", "whatsapp": "WhatsApp", "generalEnquiries": "General Enquiries:", "claimsEnquiries": "Claims Enquiries:"}, "playPassBookingDetail": {"titlePage": "Your Booking", "quantityLabel": "Quantity", "showRewardCard": "Show Rewards Card", "eventDetails": "Event Details", "editBooking": "Edit Booking", "editBookingDisableMsg": "This booking cannot be edited", "cancelBooking": "Cancel Booking", "cancelBookingDisableMsg": "This booking cannot be cancelled", "findOutMore": "Find Out More", "addToCalendar": "Add to Calendar"}, "savedFlightsScreen": {"headerTitle": "Saved Flights"}, "accountOverlay": {"buttonText": "Continue to your Account"}, "appLoadingError": {"title": "Error", "description": "Looks like something went wrong.\nPlease try again. If the issue persists, kindly close and restart the app."}, "onboardingOverlayScreen": {"title": "Your receipt must show the following details clearly.", "doNotShowAgainLabel": "Do not show me this again"}, "retroClaims": {"takePhotoGuideMessage": "Position your receipt within the camera frame.", "needAccessPermission": {"title": "“Changi App” cannot access your camera", "description": "To grant access, tap on <PERSON><PERSON><PERSON><PERSON>” and turn on the Camera option for “Changi App”.", "firstButton": "Settings", "secondButton": "Not now"}, "allowNotification": {"message": "Get notified instantly when your claim status is updated", "ctaButton": "Allow Notifications"}}, "l2Announcement": {"details": {"closeAllBtn": "Close All"}}, "retroClaimsNotifications": {"titlePageClaimApproved": "<PERSON><PERSON><PERSON> Approved", "titlePageClaimDeclined": "<PERSON><PERSON><PERSON> Declined", "titleClaimApproved": "Your receipt claim is approved.", "titleClaimDeclined": "Your receipt claim has been declined.", "descriptionApproved": "Your receipt claim submitted on {{date}} has been approved.", "descriptionDeclined": "Your claim was declined for the following reasons.", "pointsCredited": "Points Credited", "nettSpend": "<PERSON><PERSON>pend", "transactionDate": "Transaction Date", "outlet": "Outlet", "viewPointTransactions": "View Points Transaction", "submitAnotherClaim": "Submit Another <PERSON>", "getInTouch": "Get In Touch", "messageApproved": "Your Changi Rewards points have been added to your account.", "messageDeclined": "Your receipt claim has been declined.", "nettSpendValue": "{{amount}}", "reasonDeclined": {"first": "Receipt must be from a participating outlet in Changi Airport or Jewel.", "second": "You have exceeded the number of retrospective claims this month.", "third": "Receipt details cannot be detected.", "fourth": "Submission must be made within 7 days of transaction date.", "fifth": "Minimum nett spend of SGD $10."}, "declinedMessage": "If you require further assistance, please contact us with your account and receipt details."}, "vECA": {"contactUs": "Contact Us", "contactLiveAgent": "Contact Live Agent", "email": "Email", "onlineForm": "Online\nForm", "call": "Phone\nCall", "liveAgent": "Video\nCall", "askMax": "Ask MAX", "moreOptions": "More Options", "viewOtherChannels": "View Other Channels", "notInTheChangiAirportTerminalsMessage": "Live agent chat is available only within Changi Airport Terminals and requires access to your camera and microphone. For assistance, please use our other communication channels.", "enableLocation": "Enable Location", "notNow": "Not Now", "enableLocationMessage": "Live agent chat is available exclusively in the Changi Airport Terminals. Please enable your location now.", "serviceUnavailable": "Service Unavailable", "serviceUnavailableMessage": "We apologise for any inconvenience caused. For assistance, please use our other communication channels.", "enableLocationAlertTitle": "“Changi App” cannot access your location", "enableLocationAlertMessage": "To grant access, tap on “Set<PERSON><PERSON>” and turn on the Location option for “Changi App”.", "enableLocationAlertSettings": "Settings", "enableLocationAlertOk": "Ok", "microphonePermission": {"title": "\"Changi App\" cannot access your microphone", "description": "To enable the video-calling function and connect with our Virtual Changi Experience Ambassadors, tap on \"Settings\" and turn on the Microphone option for \"Changi App\".", "firstButton": "Settings", "secondButton": "Ok"}}, "flightDetailV2": {"error": {"unableToLoad": "Unable to load"}, "unsaveFlight": "Unsave Flight", "saveAndShare": "Save and Share Flight", "titleModalSaveAndShare": "You may also like to save this flight to receive \n alerts and access more information:", "shareOnly": "Share Flight Only", "flightInformationHub": {"header": "I'm travelling on this flight", "menu": {"travel": "Travel", "shop": "Shop", "dine": "<PERSON>e", "facilities": "Facilities"}, "sections": {"flightCheckIn": {"titleError": "Flight Check-In", "title": "Flight Check-In", "onlineCheckIn": {"label": "Online Check-In", "highlight": "Via airline"}, "earlyCheckIn": {"label": "Early check-in", "highlight": "Via available counters", "additionalInfo": "Check opening hours"}}, "baggageServices": {"title": "Baggage Services", "baggageCheckIn": "Baggage Check-in", "baggageStorage": "Baggage Storage", "trackBaggage": "Track Baggage"}, "gettingToAirport": {"title": "Getting to Airport", "car": "Car", "busTrainTaxi": "Bus, Train, Taxi"}, "destinationInfo": {"title": "About Your Destination: Ho Chi Minh City", "info": "Weather | Entry Requirements"}, "airportServices": {"title": "Airport Info & Services", "mfa": {"title": "Register your trip with MFA", "description": "For Singaporeans or SG PRs who want to register your travel with the Ministry of Foreign Affairs"}, "carbonOffset": "Offset Air Travel Emissions", "disabilityServices": "Book Wheelchair & Stroller", "baggageRegulation": "Baggage Regulation"}, "travelEssential": {"title": "Travel Essential", "info": "Travel Insurance | Get WIFI Router | Get E-SIM | Exchange Currency Online | Pay with Changi Pay"}, "map": {"titleError": "View Terminal {{terminal}} Map", "viewMapButton": "View Terminal {{terminal}} Map"}, "transferInfo": {"titleError": "Transfer in T{{terminal}}", "title": "Transfer in T{{terminal}}", "skytrainInfo": "Skytrain T{{terminal}} → T{{skyTrain1}}  |  Skytrain T{{terminal}} → T{{skyTrain2}}  |  To/From T4 by Shuttle Bus  |  To/From Jewel", "t4Info": "To/From T4 by Shuttle Bus | Between T1, T2, T3 | To/From Jewel", "operating": "Operating: {{startTime}}-{{endTime}}", "skytrainTo": "Skytrain T{{start}} → T{{end}}", "public": "Public", "transit": "Transit", "toFromT4": "To/From T4 by Shuttle Bus", "toFromJewel": "To/From Jewel", "inPublicTransit": "Available in public and transit areas", "betweenT123": "Between T1, T2, T3"}, "shopAndDine": {"title": "Shop and Dine", "info": "Shop Duty-Free Items Before Flight | Shop in Terminal | Dine in Terminal"}, "transitServices": {"title": "At Transit", "airlineLounges": "Airline Lounges", "showerSpa": "Shower & Spa"}}}, "flightJourney": {"header": {"title": "Flight Journey"}, "buttons": {"tryAgain": "Try again", "close": "Close"}, "error": {"unableToLoad": "Unable to load flight tracker and location.\nPlease try again later.", "trackingNotAvailable": "Tracking is not available for this flight."}}, "flightInfo": {"showDetails": "+ Show Details", "transitError": "Unable to load flight information", "weatherError": "Unable to load", "retry": "Retry", "timeline": {"date": "23 Feb 2025", "departureTime": "01:40", "offset": "+1", "arrivalTime": "11:40"}, "location": {"showDetails": "+ Show Details", "terminal": "Terminal {{terminal}}", "gate": "Gate {{gate}}", "baggageBelt": "Baggage Belt {{baggage<PERSON>elt}}", "checkinRow": "Check-In Row {{checkinRow}}", "departure": {"shortName": "SIN", "fullName": "Singapore"}, "arrival": {"shortName": "SIN", "fullName": "Singapore"}}, "checkin": {"label": "Check-in:", "info": "T1, Row 12", "gateLabel": "Gate:", "baggageLabel": "Baggage:", "gateInfo": "D12", "transport": "Take skytrain", "fbTime": "Est time: {{time}}"}, "dropOff": {"label": "Drop-off:"}, "pickUp": {"label": "Pick-up:"}, "carPark": {"title": "Nearest Car Park:", "label": "Nearest to Car Park {{carpark}}", "labelT1DEP": "Nearest to {{carpark}}", "labelT1": "Nearest Car Park {{carpark}}"}, "flightInfoMoreDetail": {"carPark": "Car park: 3A", "fastCheckin": "FAST Check-In"}}, "facilitiesTab": {"allTerminal": "All Terminals", "descAllTerminal": "Facilities and services at the terminal will be updated once assigned.", "titleWithTerminal": "Terminal {{terminal}}", "descWithTerminal": "Explore facilities and services available at the terminal.", "seeAll": "See all"}, "saveFlightPopup": {"flightJourney": "Flight Journey", "destinationWeather": "Destination Weather", "destionationAirportDetails": "Destination Airport Details", "title": "Save this flight to receive alerts and\naccess more information:", "titleSelectProfile": "Select your profile", "descSelectProfile": "For a personalised travel itinerary\nsuited to your needs"}, "weatherFlightCard": {"today": "Today", "tomorrow": "Tomorrow", "seeMore": "See More", "weatherAtDestination": "Weather at {{name}}", "unavailable": "Unavailable"}}, "dineShopScreen": {"loggedInTextCopy": "Earn perks to redeem exclusive premiums and events when you spend at Changi and Jewel today.", "nonLoggedInTextCopy": "Login to earn perks for exclusive premiums and events when you spend at Changi and Jewel.", "tabBar": {"dine": "<PERSON>e", "shop": "Shop", "marketplace": "Marketplace"}, "Other_Available_Perks": "OTHER AVAILABLE PERKS", "cmeMillionaireExperienceChances": "Millionaire Experience Chances", "cmeGrandDrawPrizeChances": "Grand Draw Prize Chances", "earnedFor": "Earned for", "todayText": "Today, {{date}}", "dineTitle": "Looks like something went wrong.\nLet’s try again in a few minutes.", "dineReload": "Refresh to reload", "title": "Spend & Redeem", "shopAll": "Shop all"}, "onboarding": {"dep": {"title_1": "Your flight details", "title_2": "has a", "title_3": "new", "title_4": "look!", "desc_1": "See all flight information", "desc_2": "Save your flight for full\ndestination details and\nflight notifications!", "desc_3": "Access relevant services based on your profile", "see_flight_detail": "See the new flight details"}}, "parkingLanding": {"forIU": "For IU", "freeParkingPromo": {"title": "Parking Promotions{{amount}}", "description": "Limited to 1 redemption per promotion per day. Rewards Card must be scanned at the point of purchase transaction. Terms apply.", "error": "We’re currently experiencing technical difficulties. \nPlease try again later. ", "tag": {"credited": "Credited", "fullyRedeemed": "<PERSON>y Redeemed", "notAvailable": "Not Available", "limitedSlots": {"one": "{{count}} slot left", "other": "{{count}} slots left", "zero": "{{count}} slot left"}}}, "vehicleDetails": {"title": "Vehicle Details", "guideTitle": "Link your vehicle IU to enjoy parking benefits & promotions", "guideMessage": "Please note that each IU number may be linked to only one <PERSON><PERSON> profile at any given time."}, "eCoupons": {"titleAEM": "Save up to 74% with Parking eCoupons", "contentAEM": "Preferential parking rates for travellers and airport community.", "titleError": "Parking eCoupons", "contentError": "We’re currently experiencing technical difficulties.", "pleaseTryAgain": "Please try again later."}, "moreServices": {"titleAEM": "More Services", "contentAEM": "Explore other useful facilities and services to support your journey.", "titleError": "More Services", "contentError": "We’re currently experiencing technical difficulties.", "pleaseTryAgain": "Please try again later."}, "crCatalogue": {"button": "Redeem", "title": "Redeem parking rebates with your Changi Rewards Points"}, "parkLocationPrefix": "Parked at:", "parkNFlyLabel": "Park & Fly eCoupons", "parkNWorkLabel": "Park & Work eCoupons", "totalDuration": {"hour": {"one": "hr", "other": "hrs", "zero": "hr"}, "minute": {"one": "min", "other": "mins", "zero": "min"}}, "monarchParkingEntitlement": {"tierTitle": "Monarch", "title": "Exclusive Benefits", "nonIUDescription": "Link your vehicle IU to enjoy your parking benefits.", "option": {"ayrFreeParking": "Free parking all year around", "selectedReservedLots": "Selected reserved parking lots"}, "ctaBtn": {"addVehicleIU": "Add Vehicle IU", "viewDetails": "View Details"}}, "csatSurvey": "Found this helpful? Tell us what you think!", "quickLinks": {"error": {"description": "Unable to load details"}}, "nonLoggedInBenefits": {"title": "Active Parking Benefits", "message": "View and earn parking benefits with your Changi account!", "button": "Login or Sign Up"}, "errors": {"refreshToReload": "Refresh to reload", "unableToRetrieveBenefits": "We’re unable to retrieve your parking benefits.", "unableToRetrieveBenefitsMessage": "Please try again later or contact us if the problem persists.", "noInternetConnection": "No internet connection", "noInternetMessage": "Please check your network connectivity or switch to a different one.", "systemMaintenance": "System Maintenance", "maintenanceMessage": "We’re currently upgrading our system to serve you better. Please check back soon. Thank you for your patience!"}}, "parkingLandingBenefit": {"headerTitle": "Your Parking Benefits", "tabFilter": {"active": "Active", "in-active": "Inactive", "expired": "Expired/Used"}, "inactive": {"title": "Parking eCoupons", "description": "Please activate your eCoupon within 24 hours before exiting the car park.", "button": "Activate", "title_empty": "No Parking eCoupons", "content_empty": "Enjoy preferential rates for parking at Changi Airport and save up to 74%!", "findOutMore": "Find Out More", "title_error": "We’re unable to retrieve your parking coupons.", "content_error": "Please try again later or contact us if the problem persists."}, "active": {"title": "Parking Benefits", "FAQ": "View FAQ", "parked_at": "Parked at:", "title_empty": "No Parking Benefits", "content_empty": "You currently have no applicable parking benefits.\n \nUse your Changi Rewards points to redeem parking rebates or enjoy free parking with a minimum spend.", "notApplicable": "Not Applicable", "notApplicable_content": "The following parking benefits cannot be used for your current parking session.", "totalSection": {"title": "Total", "parked": {"title": "Free parking available for your current parking session", "description": "Based on prevailing car park rates for cars and vans  >"}, "notParked": {"title": "Free parking available", "description": "Indicative based on rate of S$0.65/15 mins for cars and vans at T1-Jewel (General - B3 to B5 only), T2, T3, T4 carpark(s)  >"}}}, "missingBenefit": {"title": "Missing Benefit(s)?", "message": "or visit our Customer Service Counter at Terminal 3, Basement 2, from 10am to 10pm.", "button": "Contact us"}, "expiredUsed": {"hasCouponMsg": "You can view used or expired benefits from the past 30 days", "noCouponMsg": "No used or expired benefits from the past 30 days"}, "errorOverlay": {"title": "We’re unable to retrieve your parking coupons.", "description": "Please try again later or contact us if the problem persists."}}, "parkingBenefitsMonarch": {"title": "Your Parking Benefits", "benefitCard": {"title": "Reserved Free Parking", "description": "Enjoy year long complimentary parking with selected reserved Monarch carpark lots at T1/Jewel (General lots - B3 to B5 only), T2, T3 and T4 car parks. Terms & Conditions apply.", "ctaButton": "View Locations"}, "faq": {"title": "FAQ"}, "errorOverlay": {"title": "No Internet Connection", "description": "Please check your network connectivity or switch to a different one."}}, "faqLanding": {"titleHeader": "Parking FAQ", "next": "Next"}, "promoCodes": {"title": "Promo Codes", "emptyContent": {"title": "No Promo Codes", "description": "When new promo codes are available for you, they will show up here"}, "promoCodeLabel": "Use code at checkout", "toast": {"copied": "Promo code copied!"}}, "dealsPromosScreen": {"title": "Deals & Promos", "subtitle": "Discover amazing offers and exclusive deals"}, "dealsPromosListing": {"filterBar": {"categoryPill": {"newlyAdded": "Newly Added", "dining": "Dining", "shopping": "Shopping", "services": "Services", "iShopChangi": "iShopChangi", "limitedOffer": "Limited Offer"}, "area": {"public": "Public", "transit": "Transit"}, "sortBy": {"az": "A-Z"}, "locationText": {"default": "Location: All", "publicArea": "Public area", "transitArea": "Transit area", "publicNTransitAreas": "Public & Transit areas", "allTerminals": "all terminals", "displayFormat": "{{area}} in {{terminal}}"}}, "filterBs": {"title": "Filters", "sectionLocation": "Location", "sectionCategories": "Categories", "sectionSortBy": "Sort By", "location": {"title": "Location", "publicArea": "Public Area", "transitArea": "Transit Area", "all": "All", "jewel": "Jewel", "terminal1": "T1", "terminal2": "T2", "terminal3": "T3", "terminal4": "T4"}, "category": {"title": "Category", "newlyAdded": "Newly Added", "dining": "Dining", "shopping": "Shopping", "iShopChangiOffers": "iShopChangi Offers", "services": "Services", "limitedOffers": "Limited Offers"}, "area": {"public": "Public", "transit": "Transit"}, "clearBtn": "Clear", "clearAllBtn": "Clear All", "applyFiltersBtn": "Apply Filters"}, "item": {"limitedOffer": "Limited Offer", "newlyAdded": "New", "fromDateMsg": "From {{date}}"}}, "errorAirport": {"title": "Sorry!", "content": "We are currently unable to retrieve our airport services and information.", "subContent": "Please try again later. If the problem persists, contact <EMAIL>"}}