import React from "react"
import { View, StyleSheet, Dimensions } from "react-native"

const { width, height } = Dimensions.get("window") 

const HoleView = (props) => {
  const {
    backgroundColor = "rgba(0, 0, 0, 0.4)",
    top = 100,
    width = 300,
    height = 200
  } = props

  const markStyle = { flex: 1, backgroundColor }
  const topBox = { position: "absolute", top: 0, width: "100%", height: top, backgroundColor }
  const leftBox = { position: "absolute", left: 0, width: width - , height: "100%", backgroundColor }

  return (
    <View style={styles.container}>
      <View style={styles.holeViewStyle}>
        <View style={styles.topBox}/>
        <View style={styles.leftBox}/>
        <View style={styles.box}>
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    justifyContent: "center",
    ...StyleSheet.absoluteFillObject,
  },
  holeViewStyle: {
    height: "100%",
    position: "relative",
    width: "100%",
  },
  topBox: {
    position: "absolute",
    top: 0,
    width: "100%",
    height: 100,
    backgroundColor: 'orange'
  },
  leftBox: {
    position: "absolute",
    left: 0,
    width: 100,
    height: "100%",
    backgroundColor: 'blue'
  },
  box: {
    position: "absolute",
    top: 100,
    width: 300,
    height: 200,
    alignItems: "center",
    backgroundColor: 'red'
  },
})

export default HoleView
