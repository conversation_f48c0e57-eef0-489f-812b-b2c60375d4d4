import BaseImage from 'app/elements/base-image/base-image';
import { color } from 'app/theme/color';
import React, { useRef, useState, useEffect } from 'react';
import Animated, { useSharedValue, useAnimatedStyle, withTiming, runOnJS, cancelAnimation } from 'react-native-reanimated';
import { StyleSheet, View, Dimensions, TouchableOpacity, Platform } from 'react-native';
import Carousel from "react-native-reanimated-carousel"
import { handleImageUrl } from "app/utils/media-helper"
import { Text } from 'app/elements/text';
import { typography } from 'app/theme';
import { useHandleNavigation, NavigationPageSource } from "app/utils/navigation-helper"
import { Video } from 'react-native-video';
import LottieView from 'lottie-react-native';
import { useSelector } from 'react-redux';
import { AemGroupTwoSelectors } from 'app/redux/aemGroupTwo';
import { trackAction, AdobeTagName } from 'app/services/adobe';
import { isEmpty } from 'lodash'
import { checkLoginState } from 'app/utils/authentication'

const ratio = 187 / 375
const { width } = Dimensions.get("window")
const DURATION = 5000;

const ViewImageSnapCarousel = React.memo((props: any) => {
  const { handleNavigation } = useHandleNavigation("EXPLORE_SCREEN_V2")
  const carouselRef = useRef(null)
  const videoRefs = useRef<any>([]);
  const { dataMathead, isErrorMathead, isLoadingMathead, isFocused, onProgressChange } = props;
  const isDarkApp = useSelector(AemGroupTwoSelectors.isDarkApp)

  const [active, setActive] = useState(0)
  const progress = useSharedValue(0);
  const currentDuration = useSharedValue(0)

  const nextSlide = () => {
    carouselRef.current?.next?.();
  };

  const startProgress = (duration = DURATION) => {
    currentDuration.value = duration
    progress.value = withTiming(1, { duration: duration }, (isFinished) => {
      if (isFinished) {
        runOnJS(nextSlide)();
        progress.value = 0;
      }
    });
  }

  useEffect(() => {
    if (dataMathead && dataMathead.length > 1 && isFocused) {
      if (dataMathead[active]?.videoUrl) {
        const duration = dataMathead[active]?.videoDuration && dataMathead[active]?.videoDuration > 5 ? dataMathead[active]?.videoDuration : 5;
        videoRefs.current[active]?.resume();
        startProgress(duration * 1000)
      } else {
        startProgress()
      }
    }
  }, [active, dataMathead, progress, isFocused]);

  useEffect(() => {
    if (!isFocused) {
      videoRefs.current[active]?.pause();
    }
  }, [isFocused, active])

  const handleOnclick = (item: any) => {
    trackAction(AdobeTagName.CAppHomeExploreMasthead, {
      [AdobeTagName.CAppHomeExploreMasthead]: `${active + 1} | ${item?.title}`,
    })

    const { type, value } = item?.cta?.navigation ?? {}
    const { redirect } = item?.cta ?? {}
    if (!type || !value) return
    handleNavigation(type, value, {
      ...redirect,
      ...{
        aaTag: `Explore | Masthead | ${item.title}`,
        isLoggedInAtTriggerTime: checkLoginState(),
        pageSource: NavigationPageSource.Masthead, 
      }
    })
  }

  const renderCarouselItem = ({ item, index }) => {
    return (
      <TouchableOpacity
        style={styles.viewItem}
        activeOpacity={0.9}
        onPress={() => handleOnclick(item)}
        disabled={isEmpty(item?.cta)}
      >
        {item?.videoUrl ? (
          <Video
            ref={(ref) => {
              videoRefs.current[index] = ref
            }}
            paused={true}
            source={{ uri: handleImageUrl(item?.videoUrl) }}
            style={styles.viewItemVideo}
            repeat={true}
            resizeMode="cover"
            poster={{
              source: { uri: handleImageUrl(item?.image) },
              resizeMode: "cover",
            }}
            muted={true}
            disableFocus={true}
          />
        ) : item?.lottie ? (
          <LottieView
            style={styles.lottieStyle}
            source={{ uri: handleImageUrl(item?.lottie) }}
            autoPlay
            loop
            resizeMode="contain"
          />
        ) : (
          <BaseImage source={{ uri: handleImageUrl(item?.image) }} style={styles.viewItem} />
        )}
        {item?.videoUrl && Platform.OS === 'android' && <TouchableOpacity style={styles.viewButtonVideo} activeOpacity={0} onPress={() => handleOnclick(item)}/>}
      </TouchableOpacity>
    )
  }

  const progressAnimStyle = useAnimatedStyle(() => ({
    width: `${progress.value * 100}%`,
  }));

  if(isDarkApp){
    return null
  }

  if (isLoadingMathead) {
    return <View style={styles.containerError} />
  }

  if (isErrorMathead) {
    return (
      <View style={styles.containerError}>
        <Text tx="exploreScreenV2.titleErrorMathead" style={styles.txtTitleError} />
        <Text tx="exploreScreenV2.contentErrorMathead" style={styles.txtContentError} />
      </View>
    )
  }

  if (!isLoadingMathead && (!dataMathead || dataMathead.length === 0) && !isErrorMathead) {
    return null
  }

  return (
    <View style={styles.container}>
      {/* @ts-ignore */}
      <Carousel
        ref={carouselRef}
        autoPlayReverse={true}
        data={dataMathead}
        style={styles.container}
        renderItem={renderCarouselItem}
        width={width + 1}
        minScrollDistancePerSwipe={1}
        onConfigurePanGesture={(panGesture) => {
          panGesture.activeOffsetX([-8, 8])
          panGesture.onBegin(e => {
            cancelAnimation(progress)
            videoRefs.current[active]?.pause();
          }).onFinalize(() => {
            videoRefs.current[active]?.resume();
            startProgress((1 - progress.value / 1)  * currentDuration.value)
          })
          .runOnJS(true)
        }}
        
        onScrollEnd={(index) => {
          setActive(prev => {
            if(prev !== index){
              progress.value = 0
              videoRefs.current[active]?.seek(0);
              videoRefs.current[active]?.pause();
            }
            return index
          })
        }}
        onProgressChange={onProgressChange}
        scrollAnimationDuration={200}
      />
      <View style={styles.viewRow}>
        {dataMathead?.length > 1 && <View
          style={styles.viewBlur}
        />}
        {dataMathead?.length > 1 && dataMathead?.map((item, index) => {
          return (
            <View
              key={index}
              style={active === index ? styles.viewActive : styles.viewInActive}
            >
              {active === index ? <Animated.View style={[styles.progressBar, progressAnimStyle]} /> : null}
            </View>
          )
        })}
      </View>
    </View>
  )
});

const styles = StyleSheet.create({
  viewBlur: { position: 'absolute', borderRadius: 99, top: 0, left: 0, right: 0, bottom: 0, backgroundColor: "rgba(18, 18, 18, 0.2)" },
  containerError: {
    width: '100%',
    height: width * ratio,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    backgroundColor: color.palette.lightestGrey,
    justifyContent: 'center',
    alignItems: 'center'
  },
  container: {
    width: '100%',
    height: width * ratio,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    backgroundColor: color.palette.almostBlackGrey30
  },
  viewItem: {
    flex: 1,
  },
  viewItemVideo: {
    flex: 1,
    backgroundColor: 'black'
  },
  viewButtonVideo: {
    flex: 1,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  lottieStyle: {
    width: '100%',
    height: width * ratio,
  },
  viewRow: {
    position: 'absolute',
    bottom: 16,
    right: 12,
    padding: 4,
    gap: 4,
    flexDirection: 'row'
  },
  progressBar: {
    position: 'absolute',
    left: 0,
    bottom: 0,
    height: 4,
    backgroundColor: color.palette.whiteGrey,
    borderRadius: 99,
  },
  viewInActive: {
    width: 8,
    height: 4,
    borderRadius: 99,
    backgroundColor: '#FCFCFC99',
  },
  viewActive: {
    width: 20,
    height: 4,
    borderRadius: 99,
    backgroundColor: '#FCFCFC99',
  },
  txtTitleError: {
    fontFamily: typography.bold,
    color: color.palette.almostBlackGrey,
    fontSize: 11,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 14,
  },
  txtContentError: {
    fontFamily: typography.bold,
    color: color.palette.darkGrey999,
    fontSize: 11,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 14,
    marginTop: 4
  }
})

export { ViewImageSnapCarousel }