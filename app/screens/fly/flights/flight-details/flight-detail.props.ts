import { FlyCheckInProps } from "app/components/flight-details-card/flight-details-card.props"
import { NavigationType } from "app/components/menu-option"
import { FlightNavigationType } from "../flight-props"
import { IInsertMytravelFlight } from "app/redux/mytravelRedux"

export enum SectionTagNameEnum {
  GTTD = "gttd",
  TRANSFER_IN_TERMINAL = "transfer-in-terminal",
  FLIGHT_CHECK_IN = "flight-check-in",
  ATOM = "atoms",
  CMS_FLIGHT_CARD = "cms-flight-card",
  WEATHER = "weather",
}

export enum SectionTileTagNameEnum {
  SHORT_DESIGN = "short-design",
  LONG_DESIGN = "long-design",
  WITH_SUB_CATEGORY = "with-sub-category",
}

export enum UserProfileTagNameEnum {
  TRAVELLER = "traveller",
  MEETERS_AND_GREETERS = "meeters-and-greeters",
}

export enum DirectionTagNameEnum {
  DEPARTURE = "departure",
  ARRIVAL = "arrival",
}

export enum ExpandCollapseEnum {
  EXPAND = "expand",
  COLLAPSE = "collapse",
}

export interface SectionTypeProps {
  name: string
  tagTitle: string
  tagName: SectionTagNameEnum
  sequenceNumber: number
  childTags: any[] // check type later
}

export interface SectionTilesTypeProps {
  tagTitle: string
  tagName: SectionTileTagNameEnum
  sequenceNumber: number
  childTags: any[] // check type later
}

export interface SectionTilesNavigationProps {
  type: NavigationType
  value: string
}

export interface RedirectProps {
  redirectTarget: string | null
  utmParameters: {
    content: string | null
    campaign: string | null
    medium: string | null
    term: string | null
  }
}

export interface SectionTilesProps {
  type: SectionTilesTypeProps
  icon: string
  title: string
  description?: string | null
  navigation: SectionTilesNavigationProps
  showOnExploreTimelineTile: boolean
  sequenceNumber: string
  imageSummaryTimeline?: string | null
  titleSummaryTimeline?: string | null
  linkTextSummaryTimeline?: string | null
  redirect?: RedirectProps | null
  componentKey?: {
    tagTitle: string
    tagName: string
  }
}

export interface SectionProps {
  sectionId: string
  type: SectionTypeProps
  title: string
  enableExpandCollapse: boolean
  expandCollapse: ExpandCollapseEnum
  sequenceNumber: string
  tiles: SectionTilesProps[]
}

export interface UserProfileProps {
  tagTitle: "Traveller" | "Meeters & Greeters"
  tagName: UserProfileTagNameEnum
  filterType: string
  sequenceNumber: number
  childTags: any[] // check type later
}

export interface FlightDetailSectionCFProps {
  direction: DirectionTagNameEnum
  userProfile: UserProfileProps
  sections: SectionProps[]
}

export interface IBannerAEMResponse {
  success: boolean,
  data?: IBannerAEM[] | null
  error?: any;
}

export interface IBannerAEM {
  image: string;
  navigation: SectionTilesNavigationProps
  redirect?: any;
  title?: string;
  type?: TagChildProps;
}
export interface IFlightBanner {
  bannerAEMResponse: IBannerAEMResponse;
  isBannerAEMLoading: boolean;
  onPressReloadBanner: () => void;
  flyFlightDetailsPayload: FlightDetailsPayloadProps;
  isSaved?: boolean;
  onPressBanner?: () => void;
  insertFlightPayload: IInsertMytravelFlight;
}
export enum TypeFocusInformationHub {
  TRAVEL = "travel",
  FACILITIES = "facilities",
}

export interface TagChildProps {
  tagTitle: string | null
  tagName: string | null
  filterType?: string
  sequenceNumber: number
  childTags: any[] // check type later
}

export interface FacilitiesServicesProps {
  contentId: string
  image: string
  title: string
  locationDisplayText: string
  navigation: SectionTilesNavigationProps
  cagOverride: boolean
  sequenceNumber: string
  startDate: string
  endDate: string
  expiryDate: string
  tagName: string[]
  customerEligibility: TagChildProps[]
  userGroup: TagChildProps[]
  flow: TagChildProps[]
  locationDescription: TagChildProps[]
  location: TagChildProps[]
  area: TagChildProps[]
  redirect: RedirectProps
  locationText: string
  image_url: string
}

export interface AirlineDetailsProps {
  code: string
  name: string
  name_zh: string
  name_zh_hant: string
  transfer_counters: string
  transit: string
  logo_url: string
  eligible_fast_checkin: boolean
}

export interface StatusMappingProps {
  show_gate: boolean
  listing_status_en: string
  listing_status_zh: string
  details_status_en: string
  details_status_zh: string
  status_text_color: string
  belt_status_en: string
}

export interface AirportDetailsProps {
  code: string
  name: string
  name_zh: string
  name_zh_hant: string
  lat: string
  lng: string
  country_code: string
  country: string
}

export interface ViaAirportDetailsProps {
  code: string
  name: string
  name_zh: string
  name_zh_hant: string
  lat: string
  lng: string
  country_code: string
}

export interface TravelInfoProps {
  airportCode: string
  city: string
}

export interface FlightInsurance {
  bannerType: string
}

export interface FlightDetailsProps {
  bagStatus?: string | null
  checkInRow?: string | null
  displayBelt?: string
  displayGate?: string
  displayTerminal?: string
  displayTerminalDisclaimer?: string
  estimatedDate?: string
  estimatedTime?: string
  logo?: string
  codeShares?: string[]
  slaves?: string[]
  airline?: string
  airlineDetails?: AirlineDetailsProps
  departingCode?: string
  departingPlace?: string
  destinationCode?: string
  destinationPlace?: string
  flightDate?: string | null
  flightNumber?: string
  flightStatus?: string
  flightTime?: string | null
  flightUniqueId?: string
  scheduledDate?: string
  scheduledTime?: string
  direction?: string
  flightDirection?: string
  transits?: string[]
  via?: string | null
  viaAirportDetails?: ViaAirportDetailsProps
  nearestCarpark?: string
  actualTimestamp?: string
  displayTimestamp?: string
  estimatedTimestamp?: string
  statusMapping?: StatusMappingProps
  airport?: string
  airportDetails?: AirportDetailsProps
  technicalFlightStatus1?: string
  baggageTracking?: boolean
  flightPax?: string | null
  isSaved?: boolean
  recordSource?: string | null
  isPassenger?: boolean
  type?: string
  logoUrl?: string
  status?: string
  terminal?: string
  gate?: string
  baggageBelt?: string
  baggageBeltStatus?: string | null
  onlineCheckIn?: FlyCheckInProps
  country?: string
  groundTransport?: string | null
  travelInfo?: TravelInfoProps[]
  earlyCheckIn?: FlyCheckInProps
  flyFlightDetailsError?: boolean
  flightNavigationType?: FlightNavigationType
  dropOffDoor?: string
  title?: string
  originDepDate?: string
  upcomingStatusMapping?: string
  flightInsurance?: FlightInsurance
  estFbTime?: string
}

export interface FlightDetailsPayloadProps {
  flightDetailsData: FlightDetailsProps
  heroImageData: FlightDetailsProps
  errorFlag: boolean
  errorPayload: any[]
  flightRequestType: string
}
export interface ITravelChecklistAEM {
  success: boolean;
  data: FlightDetailSectionCFProps;
  error: string | null;
}

export interface IFlyItemProps {
  logo: string;
  flightNumber: string;
  departingCode: string;
  destinationCode: string;
  flightDate: string;
  scheduledDate: string;
  state: string;
  codeShare: any[]; // check type later
  destinationPlace: string;
  departingPlace: string;
  timeOfFlight: string;
  flightStatus: string;
  flightStatusMapping: string;
  beltStatusMapping: string;
  statusColor: string;
  showGate: boolean;
  isSaved: boolean;
  flightUniqueId: string;
  estimatedTimestamp: string;
  actualTimestamp: string;
  direction: string;
  terminal: string;
  checkInRow: string | null;
  displayBelt: string;
  displayTimestamp: string;
  viaAirportDetails: any | null; // check type later
  country: string;
  isFirstFlight: boolean;
  upcomingStatusMapping: string;
  technicalFlightStatus1: string;
  technicalFlightStatus2: string;
  isMSError?: boolean;
  scheduledTime?: string;
  flightNavigationType?: FlightNavigationType;
}

export type SAVE_STEPS = "NONE"
  | "WAITING_FOR_SELECT_TRAVEL_OPTION"
  | "WAITING_FOR_SAVE_CONNECTING_FLIGHT"
  | "WAITING_FOR_OPEN_APPSCAPADE_SHEET"
  | "WAITING_FOR_OPEN_NATIVE_SHARE"
  | "WAITING_FOR_OPEN_APPSCAPADE_CHANCES"

export interface IntoCityOrAirportProps {
  title?: string
  label1?: string
  link1?: string
  label2?: string
  link2?: string
  label3?: string
  link3?: string
  data1?: string
  data2?: string
  data3?: string
}

export interface ISavedFlightBackgroundImageProps {
  image: string | null
  airportCodes: SectionTypeProps[]
  defaultSavedFlightImage: boolean
}

export enum FlyProfileEnum {
  flying = "flying",
  nonFlying = 'non-flying',
}

export enum TravelOption {
  iAmTravelling = "dropdownSelectCard.imTravellingOnThisFlight",
  iAmPicking = "dropdownSelectCard.imDroppingOffSomeone",
}

export interface DailyForecastProps {
  date: string
  minimumTemperature: number
  maximumTemperature: number
  dayIconUrl: string
  dayIconPhrase: string
  nightIconUrl: string
  nightIconPhrase: string
}
export interface WeatherFlightProps {
  isDayTime: boolean
  currentTemperature: number
  dailyForecasts: DailyForecastProps[]
}

export interface FlightJourneyPositionProps {
  lat: number
  long: number
  date: string
}

export interface FlightJourneyProps {
  equipment_name: string
  current_long: number
  current_lat: number
  current_position_date: string
  positions: FlightJourneyPositionProps[]
}