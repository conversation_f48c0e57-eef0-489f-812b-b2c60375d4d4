import React, { useMemo, useRef } from "react"
import { TouchableOpacity, View } from "react-native"
import { FlightInfoProps, TypeTimelineHeader } from "./flight-info.props"
import FlightLocationPlace from "./components/flight-location-place"
import FlightFastCheckIn from "./components/flight-fast-checkin"
import { FlightDetailsProps } from "../flight-detail.props"
import { handleCondition } from "app/utils"
import { FlightDirection } from "../../flight-props"
import { FlightDetailsCardState } from "app/components/flight-details-card/flight-details-card.props"
import FlightCheckinInfo from "./components/flight-checkin-info"
import FlightPickUpDropOff from "./components/flight-pickup-dropoff"
import FlightCarpark from "./components/flight-carpark"
import useFlightInfoV2 from "./useFlightInfoV2"
import FlightInfoLoadingViewDep from "./flight-info-loading-dep"
import FlightInfoLoadingViewArr from "./flight-info-loading-arr"
import FlightInfoHeaderLoading from "./flight-info-header-loading"
import FlightInfoTitleLoading from "./flight-info-title-loading"
import FlightInfoTransitLoading from "./flight-info-transit-loading"
import { styles } from "./flight-info.styles"
import BaseImage from "app/elements/base-image/base-image"
import { newPresets, Text } from "app/elements/text"
import { translate } from "app/i18n"
import { FlightStatusTagV2 } from "../flight-status-tag-v2/flight-status-tag-v2"
import { isEmpty } from "lodash"
import { handleTimeLeft } from "app/utils/flight-detail-v2-helper"
import TimelineHeaderItem, { HEIGHT_NORMAL_FLIGHT, HEIGHT_TRANSIT_FOREIGN_FLIGHT, GAP_8, HEIGHT_FAST_CHECKIN, HEIGHT_NORMAL_FLIGHT_TRANSIT, HEIGHT_NORMAL_FLIGHT_FASTCHECKIN } from "./components/timeline-header-item"
import { DateFormats, toDate } from "app/utils/date-time/date-time"
import BlurViewFlightInfo from "./components/blur-view"
import FlightTransitError from './components/flight-transit-error';

const maxSharecodeFlightNumber = 12;

export const FlightInfo = ({
  flyFlightDetailsPayload,
  flyLastUpdatedTimeStamp,
  shouldShowSQArrivalTerminalInfo,
  inf22,
  handleMap,
  isLoadingDetailFlight,
  intoCityOrAirportPayload,
  marginTop,
  isSaved,
  onSaveFlight,
  getMyTripData,
  isLoadingGetMyTripData,
  isErrorGetMyTrip,
  onRetryGetMyTripData,
  directionFromParams
}: FlightInfoProps) => {
  const travelInfo = flyFlightDetailsPayload?.heroImageData?.travelInfo
  const flightDetailsData: FlightDetailsProps = flyFlightDetailsPayload?.flightDetailsData
  const {
    direction,
    airlineDetails,
    flightNumber,
    slaves,
    statusMapping,
    scheduledTime,
    originDepDate,
    airport,
    airportDetails,
    via,
    viaAirportDetails,
  } = flightDetailsData || {}
  const formatOriginDepDate = handleCondition(originDepDate, toDate(originDepDate, DateFormats.DayMonthYear), undefined)
  const { processFlightTime, processFlightDate, checkFlightCanSave, processFlightForeignTransitDateTime } = useFlightInfoV2({
    flightDetailsData,
  })
  const hasFastCheckIn = !!airlineDetails?.eligible_fast_checkin;

  const transitsData: Array<{ id: string; code: string; fullName: string }> = handleCondition(
    !!via && !!viaAirportDetails,
    [{ id: via, code: via, fullName: viaAirportDetails?.name }],
    null,
  )
  const { reTimeFlag, numberDaysDiff, mainTime } = processFlightTime()
  const { flightScheduledDate } = processFlightDate()
  const heightEndDEPRef = useRef<number>(0)
  const heightStartARRRef = useRef<number>(0)

  const foreignARRDateTime = processFlightForeignTransitDateTime(
    getMyTripData?.scheduledDepartureLocalDate,
    getMyTripData?.actualDepartureLocalDate ||
      getMyTripData?.estimatedDepartureLocalDate ||
      getMyTripData?.scheduledDepartureLocalDate,
    isSaved,
  )
  const transitARRDateTime = processFlightForeignTransitDateTime(
    getMyTripData?.transit?.scheduledDepartureLocalDate,
    getMyTripData?.transit?.actualDepartureLocalDate ||
      getMyTripData?.transit?.estimatedDepartureLocalDate ||
      getMyTripData?.transit?.scheduledDepartureLocalDate,
    isSaved,
  )
  const foreignDEPDateTime = processFlightForeignTransitDateTime(
    getMyTripData?.scheduledArrivalLocalDate,
    getMyTripData?.actualArrivalLocalDate ||
      getMyTripData?.estimatedArrivalLocalDate ||
      getMyTripData?.scheduledArrivalLocalDate,
    isSaved,
  )
  const transitDEPDateTime = processFlightForeignTransitDateTime(
    getMyTripData?.transit?.scheduledArrivalLocalDate,
    getMyTripData?.transit?.actualArrivalLocalDate ||
      getMyTripData?.transit?.estimatedArrivalLocalDate ||
      getMyTripData?.transit?.scheduledArrivalLocalDate,
    isSaved,
  )

  const isHasTransit = useMemo(() => {
    return !!transitsData?.length
  }, [transitsData?.length])

  const isHasTransitData = useMemo(() => {
    return isHasTransit && !!foreignDEPDateTime
  }, [isHasTransit, foreignDEPDateTime])

  const isHasTransitDataAndDestinationEmpty = useMemo(() => {
    return isHasTransitData && (transitsData?.length > 0 ? !transitDEPDateTime : !foreignDEPDateTime)
  }, [isHasTransitData, transitsData?.length, transitDEPDateTime, foreignDEPDateTime])

  const onPressSaveFlight = () => {
    const priorActionLabel = "Show Detail";
    onSaveFlight?.(priorActionLabel)
  }

  const renderShowButtonDetails = () => {
    if (!isSaved && !!checkFlightCanSave()) {
      return (
        <TouchableOpacity onPress={onPressSaveFlight}>
          <Text tx="flightDetailV2.flightInfo.showDetails" style={styles.buttonShowDetails} />
        </TouchableOpacity>
      )
    }
    return null;
  }

  const renderShowInfoFlightDEP = ({ terminal, gate, baggageClaim, updateFields }) => {
    if (!isSaved) return null;

    const flightInfo = [
      { label: "Terminal", value: terminal, key: "arrivalTerminal" },
      { label: "Gate", value: gate, key: "arrivalGate" },
      { label: "Baggage Belt", value: baggageClaim, key: "arrivalBaggageClaim" },
    ];

    const getStyledText = (label, value, key) => (
      <Text
        testID={`${key}_text_DEP`}
        accessibilityLabel={`${key}_text_DEP`}
        style={[
          newPresets.caption2Bold,
          styles.transitText,
        ]}
        text={`${label} ${value || '-'}`}
      />
    );

    return (
      <View style={styles.foreignTransitDetails}>
        {flightInfo.map(({ label, value, key }) => (
          <View key={key}>
            {getStyledText(label, value, key)}
          </View>
        ))}
      </View>
    );
  };

  const renderShowInfoFlightARR = ({ terminal, gate, checkinDesk, updateFields }) => {
    if (!isSaved) return null;

    const flightInfo = [
      { label: "Terminal", value: terminal, key: "departureTerminal" },
      { label: "Check-In Row", value: checkinDesk, key: "departureCheckInDesk" },
      { label: "Gate", value: gate, key: "departureGate" },
    ];

    const getStyledText = (label, value, key) => (
      <Text
        style={[
          newPresets.caption2Bold,
          styles.transitText,
        ]}
        text={`${label} ${value || '-'}`}
      />
    );

    return (
      <View style={styles.foreignTransitDetails}>
        {flightInfo.map(({ label, value, key }) => (
          <View key={key}>
            {getStyledText(label, value, key)}
          </View>
        ))}
      </View>
    );
  };

  const renderSlaves = (slaves) => {
    return (
      <View style={styles.containerSlavesTextStyle}>
        {!!slaves?.length && (
          <Text style={styles.slavesTextStyle} preset="caption2Bold">
            {slaves?.slice(0, maxSharecodeFlightNumber).join(", ")}
          </Text>
        )}
      </View>
    )
  }

  const renderHeader = () => (
    <View style={[styles.containerHeader, { marginTop }]}>
      {isLoadingDetailFlight ? (
        <FlightInfoHeaderLoading />
      ) : (
        <>
          <View style={styles.containerFlightNumber}>
            <View style={styles.wrapImageStyle}>
              <BaseImage source={{ uri: airlineDetails?.logo_url }} style={styles.imageStyle} />
            </View>
            <Text style={styles.mainFlightCodeText} preset="h4" text={flightNumber} />
          </View>
          {renderSlaves(slaves)}
          {shouldShowSQArrivalTerminalInfo && (
            <Text
              text={inf22?.informativeText}
              style={styles.informativeTextStyle}
              preset="XSmallBold"
            />
          )}
        </>
      )}
    </View>
  )

  const contentTimeLeft = () => {
    const statusNeedIgnore = ["cancelled", "go to info counter", "diverted"]
    if (statusNeedIgnore?.includes(flightDetailsData?.upcomingStatusMapping?.toLowerCase())) {
      return null
    }
    const timeLeft = handleTimeLeft(flightDetailsData)
    return (
      !isEmpty(timeLeft) && (
        <Text
          text={timeLeft}
          preset="caption1Black"
          numberOfLines={1}
          style={styles.contentTimeLeft}
        />
      )
    )
  }

  const renderFlightInfoTitle = () => (
    <View style={styles.containerFlightInfoTitle}>
      {isLoadingDetailFlight ? <FlightInfoTitleLoading/> : <>
      {travelInfo && (
        <>
          <Text
            style={styles.flightNumberText}
            preset="caption2Bold"
            text={translate("flightDetails.flightInfo.updatedSystemTime") + " " + flyLastUpdatedTimeStamp}
          />
          <Text
            style={styles.flightNumberText}
            preset="h6"
            text={translate("flightDetails.flightInfo.cityTitle", {
              dep: travelInfo[0].city,
              arr: travelInfo[travelInfo.length - 1].city,
            })}
          />
        </>
      )}
      <View style={styles.containerFlightStatusTag}>
        {isSaved && contentTimeLeft()}
        {handleCondition(
          !isEmpty(statusMapping?.details_status_en),
          <FlightStatusTagV2 statusMapping={statusMapping} />,
          null,
        )}
      </View>
      </>}
    </View>
  )

  const renderTimelineHeaderItemARR = () => {
    if (!isSaved) {
      return (
        <TimelineHeaderItem
          height={isSaved ? handleCondition(
            transitsData?.length,
            HEIGHT_TRANSIT_FOREIGN_FLIGHT + GAP_8,
            HEIGHT_TRANSIT_FOREIGN_FLIGHT - GAP_8,
          ) : HEIGHT_TRANSIT_FOREIGN_FLIGHT + GAP_8}
          type={TypeTimelineHeader.ArrDateTimeEnd}
          flightScheduledDate={formatOriginDepDate}
        />
      )
    }
    return handleCondition(
      !!foreignARRDateTime,
      <TimelineHeaderItem
        height={handleCondition(
          transitsData?.length,
          HEIGHT_TRANSIT_FOREIGN_FLIGHT,
          HEIGHT_TRANSIT_FOREIGN_FLIGHT - GAP_8,
        )}
        type={TypeTimelineHeader.ArrDateTimeEnd}
        flightScheduledDate={foreignARRDateTime?.scheduledDate}
        mainTime={foreignARRDateTime?.mainTime}
        scheduledTime={foreignARRDateTime?.scheduledTime}
        reTimeFlag={foreignARRDateTime?.reTimeFlag}
        numberDaysDiff={foreignARRDateTime?.numberDaysDiff}
        gapEnd={handleCondition(foreignARRDateTime?.reTimeFlag, GAP_8 * 4, GAP_8 * 2)}
      />,
      <TimelineHeaderItem
        height={HEIGHT_TRANSIT_FOREIGN_FLIGHT + GAP_8}
        type={TypeTimelineHeader.ArrDateTimeEnd}
        flightScheduledDate={formatOriginDepDate}
      />,
    )
  }

  const renderFlightInfoARR = () => {
    if (isLoadingDetailFlight) return <FlightInfoLoadingViewArr />;
    return (
      <View style={styles.container}>
        <BlurViewFlightInfo />
        <View style={styles.containerFlightInfo}>
          {/* Destination ARR */}
          <View style={[styles.row, { height: HEIGHT_TRANSIT_FOREIGN_FLIGHT }]}>
          <View style={styles.timeColumn}>{renderTimelineHeaderItemARR()}</View>
            {isLoadingGetMyTripData ? <View style={styles.viewMarginTop}><FlightInfoTransitLoading /></View> : <View style={[styles.destinationInfo, styles.rowGap4]}>
              <FlightLocationPlace
                shortName={airport}
                fullName={airportDetails?.name}
                type={FlightDetailsCardState.departure}
              />
              {isErrorGetMyTrip && !getMyTripData && isSaved ? <FlightTransitError onRetry={onRetryGetMyTripData} /> : <View style={styles.containerBodyInfo}>
                {renderShowButtonDetails()}
                {renderShowInfoFlightARR({
                  terminal: getMyTripData?.departureTerminal,
                  checkinDesk: getMyTripData?.departureCheckInDesk,
                  gate: getMyTripData?.departureGate,
                  updateFields: getMyTripData?.updateFields || [],
                })}
              </View>}
            </View>}
          </View>

          {/* Transits ARR */}
          {transitsData?.map((item, index) => (
            <View
              key={item?.id}
              style={[styles.row, { height: HEIGHT_TRANSIT_FOREIGN_FLIGHT }]}
            >
              <View style={styles.timeColumn}>
                {
                  handleCondition(
                    !!transitARRDateTime,
                    <TimelineHeaderItem
                      height={handleCondition(
                        transitsData?.length,
                        HEIGHT_TRANSIT_FOREIGN_FLIGHT,
                        HEIGHT_TRANSIT_FOREIGN_FLIGHT - GAP_8,
                      )}
                      type={TypeTimelineHeader.ArrDateTimeMiddle}
                      flightScheduledDate={transitARRDateTime?.scheduledDate}
                      mainTime={transitARRDateTime?.mainTime}
                      scheduledTime={transitARRDateTime?.scheduledTime}
                      reTimeFlag={transitARRDateTime?.reTimeFlag}
                      numberDaysDiff={transitARRDateTime?.numberDaysDiff}
                      gapEnd={handleCondition(transitARRDateTime?.reTimeFlag, GAP_8, 0)}
                    />,
                    <TimelineHeaderItem
                      type={TypeTimelineHeader.ArrCircleMiddle}
                      height={HEIGHT_TRANSIT_FOREIGN_FLIGHT}
                    />
                  )
                }

              </View>
              {isLoadingGetMyTripData ? <View style={styles.viewMarginTop}><FlightInfoTransitLoading /></View> : <View style={[styles.destinationInfo, styles.rowGap4]}>
                <FlightLocationPlace
                  shortName={item?.code}
                  fullName={item?.fullName}
                  type={FlightDetailsCardState.departure}
                />
                {isErrorGetMyTrip && !getMyTripData && isSaved ? <FlightTransitError onRetry={onRetryGetMyTripData} /> : <View style={styles.containerBodyInfo}>
                  {renderShowButtonDetails()}
                  {getMyTripData && renderShowInfoFlightARR({
                    terminal: getMyTripData?.transit?.departureTerminal,
                    checkinDesk: getMyTripData?.transit?.departureCheckInDesk,
                    gate: getMyTripData?.transit?.departureGate,
                    updateFields: getMyTripData?.transit?.updateFields || [],
                  })}
                </View>}
              </View>}
            </View>
          ))}

          {/* Start ARR */}
          <View
            onLayout={(event) => {
              const componentHeight = event.nativeEvent.layout.height || 0
              heightStartARRRef.current = componentHeight
            }}
            style={styles.row}>
            <View style={styles.timeColumn}>
              <TimelineHeaderItem
                height={heightStartARRRef.current}
                type={TypeTimelineHeader.ArrDateTimeStart}
                flightScheduledDate={flightScheduledDate}
                mainTime={mainTime}
                scheduledTime={scheduledTime}
                reTimeFlag={reTimeFlag}
                numberDaysDiff={numberDaysDiff}
              />
            </View>
            <View style={styles.destinationInfo}>
              <FlightLocationPlace
                shortName={'SIN'}
                fullName={'Singapore'}
                type={FlightDetailsCardState.arrival}
              />
              <View style={styles.containerBodyInfo}>
                <View style={styles.containerBodyInfoWrapper}>
                  <FlightCheckinInfo
                    onPressGate={handleMap}
                    onPressBaggageBelt={handleMap}
                    onPressCheckinRow={handleMap}
                    flightDetailsData={flightDetailsData}
                    isSaved={isSaved}
                  />
                  <FlightPickUpDropOff
                    onPressAccessiblePickupOrDropOff={handleMap}
                    onPressPickupOrDropOff={handleMap}
                    intoCityOrAirportPayload={intoCityOrAirportPayload}
                    flightDetailsData={{
                      direction: flightDetailsData?.direction
                    }}
                  />
                  <FlightCarpark
                    onPressCarpark={handleMap}
                    intoCityOrAirportPayload={intoCityOrAirportPayload}
                    flightDetailsData={{
                      terminal: flightDetailsData?.terminal,
                      direction: flightDetailsData?.direction,
                    }}
                  />
                </View>
              </View>
            </View>
          </View>
        </View>
      </View>
    )
  }

  const renderFlightInfoDEP = () => {
    const flightItemHeight = handleCondition(hasFastCheckIn,
      HEIGHT_NORMAL_FLIGHT_FASTCHECKIN + HEIGHT_FAST_CHECKIN,
      handleCondition(transitsData?.length,
        HEIGHT_NORMAL_FLIGHT_TRANSIT,
        HEIGHT_NORMAL_FLIGHT
      )
    )
    if (isLoadingDetailFlight) return <FlightInfoLoadingViewDep />;
    const needCircleEnd = isHasTransit && !foreignDEPDateTime ? false : !!transitDEPDateTime || !!foreignDEPDateTime
    return (
      <View style={styles.container}>
        <BlurViewFlightInfo />
        <View style={[styles.containerFlightInfo, { justifyContent: 'flex-start' }]}>
          {/* Start DEP */}
          <View
            style={styles.row}
          >
            <View style={styles.timeColumn} >
              <TimelineHeaderItem
                height={flightItemHeight + GAP_8}
                type={TypeTimelineHeader.DepDateTimeStart}
                flightScheduledDate={flightScheduledDate}
                mainTime={mainTime}
                scheduledTime={scheduledTime}
                reTimeFlag={reTimeFlag}
                numberDaysDiff={numberDaysDiff}
                needCircleEnd={needCircleEnd}
              />
            </View>
            <View style={styles.destinationInfo}>
              <FlightLocationPlace
                shortName={'SIN'}
                fullName={'Singapore'}
                type={FlightDetailsCardState.departure}
              />
              <View style={[styles.containerBodyInfo, styles.rowGap8]}>
                {handleCondition(
                  hasFastCheckIn,
                  <FlightFastCheckIn />,
                  null,
                )}
                <View style={styles.containerBodyInfoWrapper}>
                  <FlightCheckinInfo
                    onPressGate={handleMap}
                    onPressBaggageBelt={handleMap}
                    onPressCheckinRow={handleMap}
                    flightDetailsData={flightDetailsData}
                  />
                  <FlightPickUpDropOff
                    onPressAccessiblePickupOrDropOff={handleMap}
                    onPressPickupOrDropOff={handleMap}
                    intoCityOrAirportPayload={intoCityOrAirportPayload}
                    flightDetailsData={{
                      direction: flightDetailsData?.direction
                    }}
                  />
                  <FlightCarpark
                    onPressCarpark={handleMap}
                    intoCityOrAirportPayload={intoCityOrAirportPayload}
                    flightDetailsData={{
                      terminal: flightDetailsData?.terminal,
                      direction: flightDetailsData?.direction,
                    }}
                  />
                </View>
              </View>
            </View>
          </View>

          {/* Transits DEP */}
          {transitsData?.map((item, index) => (
            <View
              key={index}
              style={[styles.row, { height: HEIGHT_TRANSIT_FOREIGN_FLIGHT }]}
            >
              <View style={styles.timeColumn}>
                {
                  handleCondition(
                    foreignDEPDateTime,
                    <TimelineHeaderItem
                      height={isHasTransitDataAndDestinationEmpty ? HEIGHT_TRANSIT_FOREIGN_FLIGHT : heightEndDEPRef.current}
                      type={TypeTimelineHeader.DepDateTimeMiddle}
                      flightScheduledDate={foreignDEPDateTime?.scheduledDate}
                      mainTime={foreignDEPDateTime?.mainTime}
                      scheduledTime={foreignDEPDateTime?.scheduledTime}
                      reTimeFlag={foreignDEPDateTime?.reTimeFlag}
                      numberDaysDiff={foreignDEPDateTime?.numberDaysDiff}
                      gapEnd={handleCondition(foreignDEPDateTime?.reTimeFlag, GAP_8, 0)}
                      isHasTransitDataAndDestinationEmpty={isHasTransitDataAndDestinationEmpty}
                    />,
                    <TimelineHeaderItem
                      type={TypeTimelineHeader.DepCircleMiddle}
                      height={HEIGHT_TRANSIT_FOREIGN_FLIGHT}
                    />
                  )
                }
              </View>
              {isLoadingGetMyTripData ? <View><FlightInfoTransitLoading /></View> : <View style={styles.destinationInfo}>
                <FlightLocationPlace
                  shortName={item?.code}
                  fullName={item?.fullName}
                  type={FlightDetailsCardState.arrival}
                />
                {isErrorGetMyTrip && !getMyTripData && isSaved ? <FlightTransitError onRetry={onRetryGetMyTripData} /> : <View style={styles.containerBodyInfo}>
                  {renderShowButtonDetails()}
                  {getMyTripData && renderShowInfoFlightDEP({
                    terminal: getMyTripData?.arrivalTerminal,
                    gate: getMyTripData?.arrivalGate,
                    baggageClaim: getMyTripData?.arrivalBaggageClaim,
                    updateFields: getMyTripData?.updateFields || [],
                  })}
                </View>}
              </View>}
            </View>
          ))}

          {/* Destination DEP */}
          <View
            onLayout={(event) => {
              const componentHeight = event.nativeEvent.layout.height || 0
              heightEndDEPRef.current = componentHeight
            }}
            style={styles.row}
          >
            <View style={styles.timeColumn}>
              {
                handleCondition(
                  transitsData?.length > 0 ? transitDEPDateTime : foreignDEPDateTime,
                  <TimelineHeaderItem
                    height={heightEndDEPRef.current}
                    type={TypeTimelineHeader.DepDateTimeEnd}
                    flightScheduledDate={transitsData?.length > 0 ? transitDEPDateTime?.scheduledDate : foreignDEPDateTime?.scheduledDate}
                    mainTime={transitsData?.length > 0 ? transitDEPDateTime?.mainTime : foreignDEPDateTime?.mainTime}
                    scheduledTime={transitsData?.length > 0 ? transitDEPDateTime?.scheduledTime : foreignDEPDateTime?.scheduledTime}
                    reTimeFlag={transitsData?.length > 0 ? transitDEPDateTime?.reTimeFlag : foreignDEPDateTime?.reTimeFlag}
                    numberDaysDiff={transitsData?.length > 0 ? transitDEPDateTime?.numberDaysDiff : foreignDEPDateTime?.numberDaysDiff}
                    needCircleDepDateTimeEnd={isHasTransit && !foreignDEPDateTime}
                  />,
                  handleCondition(
                    isHasTransitData,
                    null,
                    <TimelineHeaderItem
                      height={heightEndDEPRef.current}
                      type={TypeTimelineHeader.DepCircleEnd}
                    />,
                  ),
                )
              }
            </View>
            {isLoadingGetMyTripData ? <View><FlightInfoTransitLoading /></View> : <View style={styles.destinationInfo}>
              <FlightLocationPlace
                shortName={airport}
                fullName={airportDetails?.name}
                type={FlightDetailsCardState.arrival}
              />
              {isErrorGetMyTrip && !getMyTripData && isSaved ? <FlightTransitError onRetry={onRetryGetMyTripData} /> : <View style={styles.containerBodyInfo}>
                {renderShowButtonDetails()}
                {getMyTripData && renderShowInfoFlightDEP({
                  terminal: transitsData?.length > 0 ? getMyTripData?.transit?.arrivalTerminal : getMyTripData?.arrivalTerminal,
                  gate: transitsData?.length > 0 ? getMyTripData?.transit?.arrivalGate : getMyTripData?.arrivalGate,
                  baggageClaim: transitsData?.length > 0 ? getMyTripData?.transit?.arrivalBaggageClaim : getMyTripData?.arrivalBaggageClaim,
                  updateFields: transitsData?.length > 0 ? getMyTripData?.transit?.updateFields : getMyTripData?.updateFields || [],
                })}
              </View>}
            </View>}
          </View>
        </View>
      </View>
    )
  }

  return (
    <>
      {renderHeader()}
      {renderFlightInfoTitle()}
      {
        handleCondition(
          directionFromParams === FlightDirection.arrival || direction === FlightDirection.arrival,
          renderFlightInfoARR(),
          renderFlightInfoDEP(),
        )
      }
    </>
  )
}
