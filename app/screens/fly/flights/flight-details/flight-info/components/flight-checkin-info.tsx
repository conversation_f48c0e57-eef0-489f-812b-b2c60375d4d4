import { StyleSheet, TouchableOpacity, View } from 'react-native'
import React, { useContext, useMemo } from 'react'
import { newPresets, Text } from 'app/elements/text'
import { FlightSkyTrainV2, LocationV2, WheeledLuggage } from 'assets/icons'
import { FlightDetailsProps } from '../../flight-detail.props'
import { color } from 'app/theme'
import { FlightDirection } from '../../../flight-props'
import { handleCondition } from 'app/utils'
import { isFlagOnCondition } from 'app/services/firebase/remote-config'
import { FLY_CONTEXT } from 'app/services/context/fly'
import { TypePressDetailFlightCard } from '../../flight-details'
import { SKYTRAIN_GATES_OBJ } from 'app/utils/constants'
import moment from 'moment'

const FlightCheckinInfo = ({
  onPressGate,
  onPressBaggageBelt,
  onPressCheckinRow,
  flightDetailsData,
  isSaved,
}: {
  onPressGate: (type: string) => void
  onPressBaggageBelt: (type: string) => void
  onPressCheckinRow: (type: string) => void
  flightDetailsData: FlightDetailsProps,
  isSaved: boolean
}) => {
  const { gate, direction, baggageBelt, terminal, statusMapping, checkInRow } = flightDetailsData || {}
  const flightTerminalDisclaimerText = useMemo(
    () => flightDetailsData?.displayTerminalDisclaimer,
    [flightDetailsData?.displayTerminalDisclaimer],
  )

  const isShowGate = useMemo(() => {
    if (direction === FlightDirection.arrival) {
      return !!statusMapping?.show_gate && !!gate
    }
    return !!gate
  }, [statusMapping?.show_gate, gate, direction])

  if (direction === FlightDirection.departure) {
    return (
      <View style={stylesDEP.containerCheckinInfo}>
        {/* Checkin layout */}
        <View style={stylesDEP.containerCheckin}>
          <Text
            tx="flightDetailV2.flightInfo.checkin.label"
            style={stylesDEP.checkinLabel}
            testID="Text_baggageBelt"
            accessibilityLabel="Text_baggageBelt"
          />
          <TouchableOpacity
            style={stylesDEP.containerLocationCheckin}
            disabled={!checkInRow}
            onPress={() => onPressCheckinRow(TypePressDetailFlightCard.CHECK_IN_ROW)}
            testID="TouchableOpacity_baggageBelt"
            accessibilityLabel="TouchableOpacity_baggageBelt"
          >
            {(!!terminal || !!checkInRow) ? (
              <Text
                text={`${!!terminal ? `T${terminal}` : '-'}, ${!!checkInRow ? `Row ${checkInRow}` : 'Row -'}`}
                style={stylesDEP.checkinInfoLabel}
                testID="Text_terminalRow"
                accessibilityLabel="Text_terminalRow"
              />
            ) : (
              <Text
                text={"-"}
                style={stylesDEP.checkinInfoLabel}
                testID="Text_terminalRow"
                accessibilityLabel="Text_terminalRow"
              />
            )}
            {!!terminal && !!checkInRow && <LocationV2 width={12} height={12} />}
          </TouchableOpacity>
          {!!flightTerminalDisclaimerText && (
            <Text
              text={flightTerminalDisclaimerText}
              style={stylesDEP.disclaimerText}
              testID="Text_disclaimerText"
              accessibilityLabel="Text_disclaimerText"
            />
          )}
        </View>
        {/* Gate layout */}
        <View style={stylesDEP.containerCheckin}>
          <Text
            tx="flightDetailV2.flightInfo.checkin.gateLabel"
            style={stylesDEP.checkinLabel}
            testID="Text_gateLabel"
            accessibilityLabel="Text_gateLabel"
          />
          <TouchableOpacity
            style={stylesDEP.containerLocationCheckin}
            disabled={!isShowGate}
            onPress={() => onPressGate(TypePressDetailFlightCard.GATE)}
            testID="TouchableOpacity_gateLabel"
            accessibilityLabel="TouchableOpacity_gateLabel"
          >
            {
              handleCondition(
                isShowGate,
                <>
                  <Text
                    text={gate}
                    style={stylesDEP.checkinInfoLabel}
                    testID="Text_checkinInfoLabel"
                    accessibilityLabel="Text_checkinInfoLabel"
                  />
                  <LocationV2 width={12} height={12} />
                </>,
                <>
                  <Text
                    text={"-"}
                    style={stylesDEP.checkinInfoLabel}
                    testID="Text_checkinInfoLabel"
                    accessibilityLabel="Text_checkinInfoLabel"
                  />
                </>
              )
            }
          </TouchableOpacity>
          {
            handleCondition(
              !!statusMapping?.show_gate && !!SKYTRAIN_GATES_OBJ[gate],
              <View style={stylesDEP.containerSkyTrain}>
                <FlightSkyTrainV2 />
                <Text tx='flightDetailV2.flightInfo.checkin.transport' style={styles.transportLabel} />
              </View>,
              null
            )
          }
        </View>
      </View>
    )
  }

  return (
    <View style={styles.containerCheckinInfo}>
      {/* Gate layout */}
      <View style={styles.containerCheckin}>
        <Text
          tx="flightDetailV2.flightInfo.checkin.gateLabel"
          style={styles.checkinLabel}
          testID="Text_gateLabel"
          accessibilityLabel="Text_gateLabel"
        />
        <TouchableOpacity
          style={styles.containerLocationCheckin}
          disabled={!isShowGate}
          onPress={() => onPressGate(TypePressDetailFlightCard.GATE)}
          testID="TouchableOpacity_gateLabel"
          accessibilityLabel="TouchableOpacity_gateLabel"
        >
          {
            handleCondition(
              isShowGate,
              <>
                <Text
                  text={gate}
                  style={styles.checkinInfoLabel}
                  testID="Text_checkinInfoLabel"
                  accessibilityLabel="Text_checkinInfoLabel"
                />
                <LocationV2 width={12} height={12} />
              </>,
              <>
                <Text
                  text={"-"}
                  style={styles.checkinInfoLabel}
                  testID="Text_checkinInfoLabel"
                  accessibilityLabel="Text_checkinInfoLabel"
                />
              </>
            )
          }
        </TouchableOpacity>
      </View>
      {/* Baggage layout */}
      <View style={[styles.containerCheckin, { flex: 2 }]}>
        <Text
          tx="flightDetailV2.flightInfo.checkin.baggageLabel"
          style={styles.checkinLabel}
          testID="Text_baggageBelt"
          accessibilityLabel="Text_baggageBelt"
        />
        <TouchableOpacity
          style={styles.containerLocationCheckin}
          disabled={!baggageBelt}
          onPress={() => onPressBaggageBelt(TypePressDetailFlightCard.BAGGAGE_BELT)}
          testID="TouchableOpacity_baggageBelt"
          accessibilityLabel="TouchableOpacity_baggageBelt"
        >
          {(!!terminal || !!baggageBelt) ? (
            <Text
              text={`${!!terminal ? `T${terminal}` : '-'}, ${!!baggageBelt ? `Belt ${baggageBelt}` : '-'}`}
              style={styles.checkinInfoLabel}
              testID="Text_terminalBaggage"
              accessibilityLabel="Text_terminalBaggage"
            />
          ) : (
            <Text
              text={"-"}
              style={styles.checkinInfoLabel}
              testID="Text_terminalBaggage"
              accessibilityLabel="Text_terminalBaggage"
            />
          )}
          {!!terminal && !!baggageBelt && <LocationV2 width={12} height={12} />}
        </TouchableOpacity>
        {isSaved && flightDetailsData?.estFbTime && 
          <View style={styles.estFbContainer}>
            <WheeledLuggage width={14} height={14} color={color.palette.lighterPurple} />
            <Text
              tx={"flightDetailV2.flightInfo.checkin.fbTime"}
              txOptions={{ time: moment(flightDetailsData?.estFbTime).format("HH:mm")}}
              style={styles.estFbTime}
            />
          </View>
        }
        {!!flightTerminalDisclaimerText && (
          <Text
            text={flightTerminalDisclaimerText}
            style={styles.disclaimerText}
            testID="Text_disclaimerText"
            accessibilityLabel="Text_disclaimerText"
          />
        )}
      </View>
    </View>
  )
}

export default FlightCheckinInfo

const styles = StyleSheet.create({
  containerCheckinInfo: {
    flexDirection: "row",
    width: '100%',
    columnGap: 4,
    flexWrap: 'wrap',
    rowGap: 4,
  },
  containerCheckin: {
    padding: 8,
    backgroundColor: color.palette.backgroundFlightCheckin,
    borderRadius: 8,
    flex: 1,
    rowGap: 4,
  },
  checkinLabel: {
    ...newPresets.caption2Regular,
    color: color.palette.midGrey,
  },
  containerLocationCheckin: {
    flexDirection: "row",
    alignItems: "center",
  },
  checkinInfoLabel: {
    ...newPresets.bodyTextBold,
    color: color.palette.whiteGrey,
    marginRight: 4,
  },
  estFbContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 2,
    marginTop: 4,
  },
  estFbTime: {
    ...newPresets.bodyTextRegular,
    fontSize: 12,
    color: color.palette.midGrey,
  },
  transportLabel: {
    ...newPresets.caption2Regular,
    color: color.palette.midGrey,
    marginLeft: 4,
  },
  disclaimerText: {
    ...newPresets.caption2Regular,
    color: color.palette.midGrey,
  }
})

const stylesDEP = StyleSheet.create({
  containerCheckinInfo: {
    flexDirection: "row",
    width: '100%',
    columnGap: 4,
    flexWrap: 'wrap',
    rowGap: 4,
  },
  containerCheckin: {
    padding: 8,
    backgroundColor: color.palette.backgroundFlightCheckin,
    borderRadius: 8,
    flex: 1,
    rowGap: 4,
  },
  checkinLabel: {
    ...newPresets.caption2Regular,
    color: color.palette.midGrey,
  },
  containerLocationCheckin: {
    flexDirection: "row",
    alignItems: "center",
  },
  checkinInfoLabel: {
    ...newPresets.bodyTextBold,
    color: color.palette.whiteGrey,
    marginRight: 4,
  },
  transportLabel: {
    ...newPresets.caption2Regular,
    color: color.palette.midGrey,
    marginLeft: 4,
  },
  disclaimerText: {
    ...newPresets.caption2Regular,
    color: color.palette.midGrey,
  },
  containerSkyTrain: {
    flexDirection: 'row',
    alignItems: 'center',
    columnGap: 2,
  }
})