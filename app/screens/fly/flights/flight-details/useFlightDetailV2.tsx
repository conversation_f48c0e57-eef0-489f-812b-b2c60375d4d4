import { useContext, useEffect, useMemo, useRef, useState } from "react"
import { useFlightDetail } from "./useFlightDetail"
import { useDispatch, useSelector } from "react-redux"
import { FLY_CONTEXT } from "app/services/context/fly"
import {
  getFeatureFlagInit,
  isFlagON,
  isFlagOnCondition,
  REMOTE_CONFIG_FLAGS,
} from "app/services/firebase/remote-config"
import { FlyCreators, FlySelectors } from "app/redux/flyRedux"
import { NativeAuthSelectors as AuthSelectors } from "app/redux/nativeAuthRedux"
import {
  MaintananceTickerBandType,
  useTickerbandMaintanance,
} from "app/hooks/useTickerbandMaintanence"
import { AdobeTagName, commonTrackingScreen, getExperienceCloudId, trackAction } from "app/services/adobe"
import { FlightDirection, FlightNavigationType } from "../flight-props"
import {
  DirectionTagNameEnum,
  FlightDetailSectionCFProps,
  FlightDetailsPayloadProps,
  IFlyItemProps,
  IntoCityOrAirportProps,
  ISavedFlightBackgroundImageProps,
  ITravelChecklistAEM,
  UserProfileTagNameEnum,
} from "./flight-detail.props"
import AemActions, { AEM_PAGE_NAME, AemSelectors } from "app/redux/aemRedux"
import { MytravelCreators, MytravelSelectors } from "app/redux/mytravelRedux"
import NetInfo from "@react-native-community/netinfo"
import moment from "moment"
import { useIsFocused, useNavigation } from "@react-navigation/native"
import { translate } from "app/i18n"
import Share, { ShareOptions } from "react-native-share"
import { NavigationConstants, SOURCE_SYSTEM, StateCode } from "app/utils/constants"
import { putFlightInfoToAdobeAnalyticAfterSaveUnSave } from "app/utils/screen-helper"
import { save, StorageKey } from "app/utils/storage"
import { TravelOption } from "../save-flight-travel-option/save-flight-travel-option"
import { ProfileSelectors } from "app/redux/profileRedux"
import { env } from "app/config/env-params"
import { isEmpty, size } from "lodash"
import { DURATION, FeedBackToast } from "app/components/feedback-toast"
import { TypeGetIntoAirport, TypePressDetailFlightCard, TypeTransport } from "./flight-details"
import { getDeepLinkV2, getLocationMapping } from "app/sagas/pageConfigSaga"
import { fetchFlightDetails, fetchIntoCityOrAirportV2 } from "app/services/api/flight/flightApi"
import restApi from "app/services/api/request"
import { flyModuleUpdatedTime } from "app/utils/date-time/date-time"
import { Alert, InteractionManager } from "react-native"
import { getPreviousScreen, setCurrentScreenActive } from "app/utils/screen-hook"
import { FE_LOG_PREFIX, dtManualActionEvent } from "app/services/firebase"
import { AlertTypes } from "app/components/alert-app/alert-app.props"
import { graphqlOperation } from "aws-amplify"
import { getMyTripQuery } from "app/models/queries"
import { useFlightSaveErrorHandling } from "app/hooks/useFlightSaveErrorHandling"
import { handleImageUrl } from "app/utils/media-helper"
import { handleNavigationFlightDetail } from "app/components/flight-details-card/flight-details-card"
import { handleUpdateUpcomingAndSavedFlight } from "app/utils/flight-detail-v2-helper"
import { useModal } from "app/hooks/useModal"
import { getMMKVdata, setMMKVdata, ENUM_STORAGE_MMKV, ENUM_STORAGE_TYPE, getLastSavedFlightTime, setLastSavedFlightTime } from "app/utils/storage/mmkv-storage"
import ModalManagerActions from "app/redux/modalManagerRedux"
import SearchActions from "app/redux/searchRedux"
import { EnumBannerType, EnumInputStateCode, ITspCSMInput } from "app/utils/type"
import { WebViewHeaderTypes } from "app/models/enum"
import SystemActions from "app/redux/systemRedux"
import Animated from "react-native-reanimated"

export const FLIGHT_TYPE = {
  CONNECTING: "connecting",
  RETURN: "return",
}
let intervalRefreshFlight: any // for clear interval
const FLY_APPSCAPADE_TYPE = "general_entry_point"
const sourceSystem = SOURCE_SYSTEM.FLIGHTS
export const useFlightDetailV2 = ({
  flyItem,
  direction,
  isFromScanBoardingPass,
  referrer,
  toastForSavedFlight,
  toastForRemoveFlight,
  isFromUpcomingEvent,
  priorActionRef,
  scrollViewRef,
}: {
  flyItem: IFlyItemProps
  direction: string
  isFromScanBoardingPass: boolean
  referrer?: string
  toastForSavedFlight?: React.RefObject<FeedBackToast>
  toastForRemoveFlight?: React.RefObject<FeedBackToast>
  isFromUpcomingEvent?: boolean
  priorActionRef?: React.MutableRefObject<string | null>
  scrollViewRef: React.RefObject<Animated.ScrollView>
}) => {
  const flightDetailV1Hook = useFlightDetail()
  const dispatch = useDispatch()
  const navigation = useNavigation<any>()
  const isFocused = useIsFocused()
  const tickerbandMaintananceHook = useTickerbandMaintanance(MaintananceTickerBandType.FLY_DETAILS)
  const dataCommonAEM = useSelector(AemSelectors.getAemConfig("AEM_COMMON_DATA"))
  const inf22 = dataCommonAEM?.data?.informatives?.find((e) => e?.code === "INF22")
  const msg47 = dataCommonAEM?.data?.messages?.find((e) => e?.code === "MSG47")
  const msg48 = dataCommonAEM?.data?.messages?.find((e) => e?.code === "MSG48")
  const msg58 = dataCommonAEM?.data?.messages?.find((e) => e?.code === "MSG58")
  const msg65 = dataCommonAEM?.data?.messages?.find((e) => e?.code === "MSG65")

  const iconUrl = handleImageUrl(msg47?.icon)

  const {
    fly_eci_dynamic_display,
    projectFirstFlightJourney,
    flyLandingFeatureFlag,
    flyDelayBenefitFeatureFlag
  } = useContext(FLY_CONTEXT)?.Handlers

  const enableEciDynamicDisplay = isFlagOnCondition(fly_eci_dynamic_display)
  const enableFlightJourney = isFlagOnCondition(projectFirstFlightJourney)
  const enableFlySavePrompt = getFeatureFlagInit(REMOTE_CONFIG_FLAGS.FLIGHT_SAVEPROMPT)

  const isLoggedIn = useSelector(AuthSelectors.isLoggedIn)
  const flyShowTickerBand = useSelector(FlySelectors.flyShowTickerBand)
  const flyLastUpdatedTimeStamp = useSelector(FlySelectors.flyLastUpdatedTimeStamp)
  const myTravelFlightsPayload = useSelector(MytravelSelectors.myTravelFlightsPayload)
  const insertFlightPayload = useSelector(MytravelSelectors.insertFlightPayload) //list of saved flights
  const removeFlightPayload = useSelector(MytravelSelectors.removeFlightPayload) //info of flight is unsaved
  const profilePayload = useSelector(ProfileSelectors.profilePayload)
  const flyCodesPayload = useSelector(FlySelectors.flyCodesPayload)
  const connectingFlightPayload = useSelector(FlySelectors.connectingFlightPayload)

  const callAEMData = useSelector(AemSelectors.getAemConfig(AEM_PAGE_NAME.FLY_APPSCAPADE))

  const flyAppscapadeData = callAEMData?.data

  const loadingShareRef = useRef(false)
  const isSharing = useRef(false)
  const refRetryAction = useRef(null)
  const toastForRefresh = useRef(null)
  const mapUnavailable = useRef(null)
  const unableToLoadLocationRef = useRef(null)

  const initSelectedTravelOption =
    direction === FlightDirection.departure ? TravelOption.iAmTravelling : TravelOption.iAmPicking
  const directionText = flyItem?.direction === FlightDirection.departure ? "Departure" : "Arrival"

  const [showNoInternetError, setShowNoInternetError] = useState(false)
  const [loadingFlightMap, setLoadingFlightMap] = useState(false)
  const [isNoInternetConnection, setNoInterConnection] = useState(false)
  const [loadingSaveFlight, setLoadingSaveFlight] = useState(false)
  const [loadingSaveFlightOverlay, setLoadingSaveFlightOverlay] = useState(false)
  const [selectedTravelOption, setSelectedTravelOption] = useState(initSelectedTravelOption)
  const {
    isModalVisible: isModalVisibleConnectingFlight,
    openModal: openModalConnectingFlight,
    closeModal: closeModalConnectingFlight,
  } = useModal("saveConnectingFlightDetail")
  const {
    isModalVisible: isModalVisibleTravelOption,
    openModal: openModalTravelOption,
    closeModal: closeModalTravelOption,
  } = useModal("flightDetailSaveTravelOption")
  const [isRefereeModalEverShown, setRefereeModalEverShown] = useState(false)
  const [flyFlightDetailsPayload, setFlyFlightDetailsPayload] =
    useState<FlightDetailsPayloadProps>(null)
  const [flyFlightDetailsError, setFlyFlightDetailsError] = useState(null)
  const [intoCityOrAirportPayload, setIntoCityOrAirportPayload] =
    useState<IntoCityOrAirportProps>(null)
  const [isLoadingDetailFlight, setIsLoadingDetailFlight] = useState(true)
  const [isLoadingIntoCityOrAirport, setIsIntoCityOrAirport] = useState(true)
  const [travelChecklistAEM, setTravelChecklistAEM] = useState<ITravelChecklistAEM>(null)
  const [isTravelChecklistAEMLoading, setIsTravelChecklistAEMLoading] = useState<boolean>(true)
  const [showRemoveFlightAlert, setShowRemoveFlightAlert] = useState(false)
  const [mapRMFlag, setMapRMFlag] = useState(false)
  const [selectedFlightType, setSelectedFlightType] = useState(null)
  const [showCalendarModal, setShowCalendarModal] = useState(false)
  const [getMyTripData, setGetMyTripData] = useState<any>(null)
  const [isLoadingGetMyTripData, setIsLoadingGetMyTripData] = useState<boolean>(false)
  const [isErrorGetMyTrip, setIsErrorGetMyTrip] = useState<boolean>(false)
  const [firstTimeLoadFD, setFirstTimeLoadDF] = useState(false)
  const [firstTimeLoadGMT, setFirstTimeLoadGMT] = useState(false)
  // Track if we need to trigger layout3 after save
  const [pendingShowFlightJourney, setPendingShowFlightJourney] = useState<
    "pending" | "finished" | null
  >(null)
  const [statusSaveAndShare, setStatusSaveAndShare] = useState<"START FOLLOW" | "SAVE SUCCESS" | "STARTING SHARE" | null>(null)
  const [loadingFreeFlightDelay, setLoadingFreeFlightDelay] = useState<boolean>(false)
  const [isBannerAEMLoading, setIsBannerAEMLoading] = useState<boolean>(false)
  const {
    isModalVisible: isModalSaveAndShare,
    openModal: openModalSaveAndShare,
    closeModal: closeModalSaveAndShare,
  } = useModal("modalSaveAndShare")
  const {
    closeModal: closeModalFlightSaver,
  } = useModal("approvalForInformationSharing")
  const isFlyLandingEnabled = getFeatureFlagInit(
    REMOTE_CONFIG_FLAGS.FLY_LANDING,
    flyLandingFeatureFlag,
  )
  const [floatSaveButtonHeight, setFloatSaveButtonHeight] = useState(0)

  const attendanceType = translate(selectedTravelOption)
  
  const isShowBanner = useMemo(() => {
    const isFlightDelayBenefit = getFeatureFlagInit(
      REMOTE_CONFIG_FLAGS.FLIGHT_DELAY_BENEFIT,
      flyDelayBenefitFeatureFlag
    )

    return (
      flyItem?.direction === FlightDirection.departure &&
      isFlightDelayBenefit &&
      flyFlightDetailsPayload?.flightDetailsData?.flightInsurance?.bannerType &&
      selectedTravelOption === TravelOption.iAmTravelling
    )
  }, [flyItem?.direction, flyDelayBenefitFeatureFlag, flyFlightDetailsPayload, selectedTravelOption])

  const clearFlyDetailData = () => {
    dispatch(MytravelCreators.flyClearInsertFlightPayload())
    dispatch(FlyCreators.flyShowModalConfirmSaveFly(false))
    dispatch(FlyCreators.flyCheckInOnlineLoadFailed(false))
    dispatch(FlyCreators.getAppscapadeBannerReset())
  }

  const getBackgroundAirportAEM = async ({ airport }: { airport: string }) => {
    try {
      const response: { data: ISavedFlightBackgroundImageProps } = await restApi({
        url: env()?.AEM_URL + `/bin/ichangi/fly/flight-background-images.${airport}.json`,
        method: "get",
      })
      if (response?.data && response.data.image) {
        return response.data.image
      }
      return ""
    } catch (error) {
      return ""
    }
  }

  const setErrorhandleGetMyTrip = () => {
    if (firstTimeLoadGMT) {
      toastForRefresh?.current?.show(DURATION.LENGTH_MAX)
    } else {
      setIsErrorGetMyTrip(true)
    }
  }

  const handleGetMyTrip = async (input: { flight_number; departure_date; airport }) => {
    if (!input.departure_date) {
      setErrorhandleGetMyTrip()
      setIsLoadingGetMyTripData(false)
      return
    }
    try {
      const response = await restApi({
        url: env()?.APPSYNC_GRAPHQL_URL,
        method: "post",
        data: graphqlOperation(getMyTripQuery, { input }),
        parameters: {},
        headers: {
          "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
        },
      })
      const result = response?.data?.data?.getMyTrip
      if (result && response?.statusCode === 200 && result?.status !== "ERROR") {
        setIsErrorGetMyTrip(false)
        setFirstTimeLoadGMT(true)
        setGetMyTripData(result?.data)
      } else {
        setErrorhandleGetMyTrip()
      }
    } catch (error) {
      setErrorhandleGetMyTrip()
    } finally {
      setIsLoadingGetMyTripData(false)
    }
  }

  // isHideLoadingDetail for hide loading skeleton when app auto reload interval
  const onRetryGetMyTripData = ({ isHideLoadingDetail }: { isHideLoadingDetail?: boolean }) => {
    if (isSaved) {
      setIsLoadingGetMyTripData(!isHideLoadingDetail)
      const { flightNumber, scheduledDate, airport, direction, originDepDate } =
        flyFlightDetailsPayload?.flightDetailsData || {}
      handleGetMyTrip({
        flight_number: flightNumber,
        departure_date: direction === "ARR" ? originDepDate : scheduledDate,
        airport: direction === "ARR" ? airport : "SIN",
      })
    }
  }

  useEffect(() => {
    if (removeFlightPayload?.isRemovedSuccessFully) {
      putFlightInfoToAdobeAnalyticAfterSaveUnSave({
        data: removeFlightPayload,
        isSuccess: true,
        tag: AdobeTagName.CAppFlyFlightDetailRemoveFlight,
        flyProfile: "flying",
        pageName: AdobeTagName.CAppFlyFlightDetail,
        isSaveFlight: false,
      })
      toastForSavedFlight?.current?.closeNow()
      toastForRemoveFlight?.current?.show(DURATION.LENGTH_SHORT)
      setSelectedTravelOption(
        direction === FlightDirection.departure
          ? TravelOption.iAmTravelling
          : TravelOption.iAmPicking,
      )
      // setSelectedTopTravelOption(direction === FlightDirection.departure ? TravelOption.iAmTravelling : TravelOption.iAmPicking) //do later
      dispatch(MytravelCreators.flyClearInsertFlightPayload())
    }
  }, [removeFlightPayload])

  useEffect(() => {
    if (removeFlightPayload?.isRemovedSuccessFully) {
      try {
        const savedFlightPriorActions = getMMKVdata(
          ENUM_STORAGE_MMKV.SAVED_FLIGHT_PRIOR_ACTIONS,
          ENUM_STORAGE_TYPE.string,
        ) as string
        if (savedFlightPriorActions) {
          const updatedSavedFlightPriorActions = JSON.parse(savedFlightPriorActions).filter(
            (action) =>
              action?.uid === profilePayload?.id &&
              action?.direction === direction &&
              action?.flightNumber === flyItem?.flightNumber &&
              action?.scheduledDate === flyItem?.scheduledDate,
          )
          setMMKVdata(
            ENUM_STORAGE_MMKV.SAVED_FLIGHT_PRIOR_ACTIONS,
            JSON.stringify(updatedSavedFlightPriorActions),
          )
        }
      } catch (error) {
        console.error(`Error to update saved flight prior actions:`, error)
      }
    }
  }, [removeFlightPayload?.isRemovedSuccessFully, profilePayload?.id])

  useEffect(() => {
    try {
      const savedFlightPriorActions = getMMKVdata(
        ENUM_STORAGE_MMKV.SAVED_FLIGHT_PRIOR_ACTIONS,
        ENUM_STORAGE_TYPE.string,
      ) as string
      if (savedFlightPriorActions) {
        const getMyTravelFlightDetails = myTravelFlightsPayload?.getMyTravelFlightDetails || []
        const savedFlightPriorActionsArray = JSON.parse(savedFlightPriorActions)
        const updatedSavedFlightPriorActions = savedFlightPriorActionsArray?.filter?.((item) => {
          if (item?.uid === profilePayload?.id) {
            const flightDetails = getMyTravelFlightDetails?.find?.(
              (flight) =>
                flight?.flightNumber === item?.flightNumber &&
                flight?.direction === item?.direction &&
                flight?.scheduledDate === item?.scheduledDate,
            )
            return !!flightDetails
          } else {
            return true
          }
        })
        setMMKVdata(
          ENUM_STORAGE_MMKV.SAVED_FLIGHT_PRIOR_ACTIONS,
          JSON.stringify(updatedSavedFlightPriorActions),
        )
      }
    } catch (error) {
      console.error("Error to update saved flight prior actions:", error)
    }
  }, [myTravelFlightsPayload?.getMyTravelFlightDetails, profilePayload?.id])

  useEffect(() => {
    const fetchAtomRMConfig = () => {
      const mapFlagEnable = isFlagON(REMOTE_CONFIG_FLAGS.ATOMS_MAP)
      setMapRMFlag(mapFlagEnable)
    }
    fetchAtomRMConfig()
  }, [])

  const handleMap = async (
    type,
    item = null,
    skytrainItem?: { type: TypeTransport; terminal: number },
  ) => {
    const isConnection = await checkInternetConnection()
    if (!isConnection) {
      refRetryAction.current = {
        type,
        item,
      }
      setShowNoInternetError(true)
      return
    }
    setShowNoInternetError(false)
    refRetryAction.current = null
    if (!mapRMFlag) {
      return mapUnavailable?.current.show()
    }
    const { flightDetailsData } = flyFlightDetailsPayload
    setLoadingFlightMap(true)
    const input = {
      category: "",
      terminal: flightDetailsData.displayTerminal,
      direction: flyItem?.direction === "DEP" ? "Departure" : "Arrival",
      name: "",
      gate: undefined,
    }
    let isFocusToArea = false // For navigating to Terminal in ATOM Map
    switch (type) {
      case TypePressDetailFlightCard.GATE:
        input.category = TypePressDetailFlightCard.GATE
        input.name = flightDetailsData.displayGate
        break
      case TypePressDetailFlightCard.CHECK_IN_ROW:
        input.category = TypePressDetailFlightCard.CHECK_IN_ROW
        input.name = flightDetailsData.checkInRow
        break
      case TypePressDetailFlightCard.BAGGAGE_BELT:
        input.category = TypePressDetailFlightCard.BAGGAGE_BELT
        input.name = flightDetailsData.baggageBelt
        break
      case TypePressDetailFlightCard.TERMINAL:
        input.category = TypePressDetailFlightCard.TERMINAL
        input.name = ""
        isFocusToArea = true
        break
      case TypePressDetailFlightCard.SKYTRAIN:
        input.category = TypePressDetailFlightCard.SKYTRAIN
        input.name = `T${skytrainItem.terminal} ${skytrainItem?.type}`
        input.gate = flightDetailsData.displayGate
        break
      case TypeGetIntoAirport.LINK1:
        input.category = flyItem?.direction === "DEP" ? "Drop-off" : "Pick-up"
        input.name = item
        break
      case TypeGetIntoAirport.LINK3:
        input.category =
          flyItem?.direction === "DEP" ? "Accessible Drop-off" : "Accessibility Pick-up"
        input.name = item
        break
      case TypeGetIntoAirport.LINK2:
        input.category = "Nearest Car Park"
        input.name = flightDetailsData.nearestCarpark
        break
    }
    trackAction(AdobeTagName.CAppNavigationMapsEnter, {
      [AdobeTagName.CAppNavigationMapsEnter]: `${AdobeTagName.CAppFlyFlightDetail}|Fly`,
    })

    const fetchData = await getLocationMapping({ input: input })

    trackAction(AdobeTagName.CAppATOMSEntryClick, {
      [AdobeTagName.CAppATOMSEntryClick]: `${input.direction} Detail Page|${input.name}|${fetchData.local_ref}`,
    })
    navigation.navigate(NavigationConstants.changiMap, {
      localRef: fetchData.local_ref,
      isFocusToArea,
    })
    setLoadingFlightMap(false)
  }

  const handleGetTravelChecklistAEM = async () => {
    setIsTravelChecklistAEMLoading(true)
    const isDeparture = direction === FlightDirection.departure
    const isTraveller = selectedTravelOption === TravelOption.iAmTravelling
    const { flightNumber, airline, displayTerminal, destinationCode, departingCode } =
      flyFlightDetailsPayload.flightDetailsData
    const res = await flightDetailV1Hook.getTravelChecklistAEM({
      flight_no: flightNumber,
      user_profile: isTraveller
        ? UserProfileTagNameEnum.TRAVELLER
        : UserProfileTagNameEnum.MEETERS_AND_GREETERS,
      direction: isDeparture ? DirectionTagNameEnum.DEPARTURE : DirectionTagNameEnum.ARRIVAL,
      airport: isDeparture ? destinationCode : departingCode,
      airline: airline,
      terminal: displayTerminal,
    })
    setTravelChecklistAEM(res)
    setIsTravelChecklistAEMLoading(false)
  }

  const flightDetailSectionData: FlightDetailSectionCFProps = useMemo(() => {
    if (!travelChecklistAEM?.success || !travelChecklistAEM?.data?.sections?.length) return null
    return {
      ...travelChecklistAEM.data,
      sections: travelChecklistAEM.data.sections.sort(
        (a, b) => Number(a.sequenceNumber) - Number(b.sequenceNumber),
      ),
    }
  }, [travelChecklistAEM])

  const checkReloadTravelChecklistAEM = useMemo(() => {
    if (!flyFlightDetailsPayload?.flightDetailsData) {
      return ""
    }
    return `${flyFlightDetailsPayload?.flightDetailsData?.flightNumber}-${flyFlightDetailsPayload?.flightDetailsData?.scheduledDate}-${direction}`
  }, [
    flyFlightDetailsPayload?.flightDetailsData?.flightNumber,
    flyFlightDetailsPayload?.flightDetailsData?.scheduledDate,
    direction,
  ])

  useEffect(() => {
    if (checkReloadTravelChecklistAEM) {
      handleGetTravelChecklistAEM()
    }
  }, [checkReloadTravelChecklistAEM, selectedTravelOption])

  useFlightSaveErrorHandling(isSharing.current, statusSaveAndShare ? true : false)

  useEffect(() => {
    setCurrentScreenActive(`Flight_Detail_${flyItem?.flightNumber}`)
    commonTrackingScreen(`Flight_Detail_${flyItem?.flightNumber}`, getPreviousScreen(), isLoggedIn)
    setIntervalRefeshFlight()

    return () => {
      clearInterval(intervalRefreshFlight)
    }
  }, [isLoggedIn, flyItem?.flightNumber, firstTimeLoadFD, firstTimeLoadGMT])

  useEffect(() => {
    dispatch(MytravelCreators.flyClearInsertFlightPayload())
  }, [isLoggedIn, flyItem?.flightNumber])

  const filterDataForFly = (data) => {
    if (isEmpty(data?.list)) return null
    return data?.list?.find((e) => e?.type === FLY_APPSCAPADE_TYPE)
  }
  useEffect(() => {
    if (!callAEMData?.data) {
      dispatch(
        AemActions.getAemConfigData({
          name: AEM_PAGE_NAME.FLY_APPSCAPADE,
          pathName: "getFlyAppscapade",
          callBackAfterSuccess: filterDataForFly,
        }),
      )
    }
  }, [callAEMData?.data])

  const setIntervalRefeshFlight = () => {
    const refreshInterval = env()?.FLIGHT_REFRESH_INTERVAL
    clearInterval(intervalRefreshFlight)
    intervalRefreshFlight = setInterval(() => {
      if (!isFocused) {
        return
      }
      if (!showNoInternetError) {
        InteractionManager.runAfterInteractions(() => {
          // hide skeleton loading when auto refresh
          refreshFlightDetails()
        })
      }
    }, refreshInterval)
  }

  const getTickerBand = () => {
    dispatch(
      AemActions.getAemConfigData({
        name: AEM_PAGE_NAME.TICKER_BAND_FLIGHT,
        pathName: "getTickerbandFly",
        forceRequest: true,
      }),
    )
  }

  // refreshAllData true when user refresh this screen
  const refreshFlightDetails = async (refreshAllData?: boolean) => {
    const isConnection = await checkInternetConnection()
    if (!isConnection) {
      return toastForRefresh?.current?.show(DURATION.LENGTH_MAX)
    }
    if (!isEmpty(refRetryAction.current)) {
      handleMap(refRetryAction.current?.type, refRetryAction.current?.item)
      return
    }
    if (
      isModalVisibleTravelOption || //when use is selecting travel option
      isModalVisibleConnectingFlight ||
      showRemoveFlightAlert ||
      insertFlightPayload?.loading ||
      removeFlightPayload?.loading
    ) {
      return
    }
    setIsBannerAEMLoading(refreshAllData)
    toastForRefresh?.current?.closeNow()
    dispatch(MytravelCreators.flyClearInsertFlightPayload())
    getTickerBand()
    await getFlightDetails(!refreshAllData)
    if (refreshAllData) {
      await Promise.all([handleGetTravelChecklistAEM()])
    }
    onRetryGetMyTripData({
      isHideLoadingDetail: !refreshAllData,
    })
    getIntoCityOrAirport()
    setShowNoInternetError(false)
    setIsBannerAEMLoading(false)
  }

  const onPressFlightCardLinks = (titleCardLink: string) => {
    const flightNumber = flyItem?.flightNumber
    const flightDate = flyItem?.flightDate
    trackAction(AdobeTagName.CAppFlyFlightDetailFlightCardLinks, {
      [AdobeTagName.CAppFlyFlightDetailFlightCardLinks]: `${titleCardLink}|${flightNumber}|${flightDate}`,
    })
  }

  // isHideLoadingDetail for hide loading skeleton when app auto reload interval
  const getFlightDetails = async (isHideLoadingDetail?: boolean) => {
    setIsLoadingDetailFlight(!isHideLoadingDetail)
    const result = await fetchFlightDetails({
      direction,
      flightNumber: flyItem?.flightNumber,
      scheduledDate: flyItem?.flightDate,
      flightStatus: flyItem?.flightStatus,
      airlineCode: flyItem?.flightNumber?.substring(0, 2),
      isFromScanBoardingPass,
      flyCodes: flyCodesPayload,
    })
    if (result.success) {
      setFlyFlightDetailsError(null)
      setFirstTimeLoadDF(true)
      dispatch(FlyCreators.flyLastUpdatedTimeStamp(flyModuleUpdatedTime()))
      setIsLoadingDetailFlight(false)
      result.data && setFlyFlightDetailsPayload(result.data)
      dispatch(FlyCreators.flightDetailPerkRequest())
      if (isFromUpcomingEvent) {
        const flightDetailsResult = result?.data?.flightDetailsData
        handleUpdateUpcomingAndSavedFlight(flightDetailsResult)
      }
    } else {
      if (firstTimeLoadFD) {
        setIsLoadingDetailFlight(false)
      }
      setFlyFlightDetailsError(result.error)
      toastForRefresh?.current?.show(DURATION.LENGTH_MAX)
      setIsTravelChecklistAEMLoading(false)
    }
  }

  const getIntoCityOrAirport = async () => {
    setIsIntoCityOrAirport(true)
    const fetchData = await fetchIntoCityOrAirportV2({
      direction,
      flightUniqueId: `${flyItem?.flightNumber}_${flyItem?.flightDate}`,
    })
    setIsIntoCityOrAirport(false)
    if (fetchData.success) {
      setIntoCityOrAirportPayload(fetchData.data)
    }
  }

  useEffect(() => {
    if (flyItem?.flightNumber && flyItem?.flightDate) {
      getFlightDetails()
      getIntoCityOrAirport()
    }
  }, [flyItem?.flightNumber, flyItem?.flightDate])

  useEffect(() => {
    return () => {
      clearFlyDetailData()
    }
  }, [])

  const checkInternetConnection = async () => {
    const { isConnected } = await NetInfo.fetch()
    return isConnected
  }

  useEffect(() => {
    dispatch(FlyCreators.flyLastUpdatedTimeStamp(flyModuleUpdatedTime()))
    checkInternetConnection().then((isConnection) => {
      if (!isConnection) {
        setShowNoInternetError(true)
      }
    })
  }, [])

  /**
   * Save flight success response
   */
  useEffect(() => {
    if (insertFlightPayload?.insertFlightData?.success || insertFlightPayload?.recordExist) {
      putFlightInfoToAdobeAnalyticAfterSaveUnSave({
        data: insertFlightPayload,
        isSuccess: true,
        tag: AdobeTagName.CAppFlyFlightDetailSaveFlight,
        flyProfile: "flying",
        pageName: AdobeTagName.CAppFlyFlightDetail,
        isSaveFlight: true,
      })
      if (insertFlightPayload?.isInsertSuccessfully && isFocused) {
        getFlightDetails()
        if (insertFlightPayload?.isFlightSaver) {
          setLoadingSaveFlightOverlay(false)
          onProceedFreeFlightDelay();
        } else {
          setLoadingSaveFlight(false)
          const timeStamp = new Date().getTime()
          // if user hasnt save any flight within 24hours or user has only 1 saved flight
          // show save connecting flight modal
          // else show native share popup and finish save
          const showConnectingFlight =
            (getLastSavedFlightTime() + env()?.FLIGHT_SHOW_POPUP_ADD_RETURN < timeStamp ||
              size(myTravelFlightsPayload?.getMyTravelFlightDetails) === 1) &&
            insertFlightPayload?.flightData?.isPassenger
          if (!showConnectingFlight) {
            if (statusSaveAndShare) {
              setStatusSaveAndShare("SAVE SUCCESS")
              closeModalSaveAndShare()
            } else {
              closeModalTravelOption()
              toastForSavedFlight?.current?.show(DURATION.LENGTH_LONG)
              if (pendingShowFlightJourney === "pending") {
                setPendingShowFlightJourney("finished")
              }
            }
          } else {
            if (statusSaveAndShare) {
              setStatusSaveAndShare("SAVE SUCCESS")
              closeModalSaveAndShare()
            } else {
              dispatch(SearchActions.resetAutoCompleteFlight())
              openModalConnectingFlight()
              setLastSavedFlightTime(0)
            }
            save(StorageKey.isSaveFlightTriggered, true)
          }
        }
      }
    }
    if (insertFlightPayload?.errorFlag) {
      setLoadingSaveFlight(false)
      setLoadingSaveFlightOverlay(false)
      closeModalTravelOption()
    }
  }, [insertFlightPayload, statusSaveAndShare])

  const trackAAWhenBack = () => {
    const flightDirection = flyItem?.direction
    const flightNumber = flyItem?.flightNumber
    const flightDate = flyItem?.flightNumber
    trackAction(AdobeTagName.CAppFlyFlightDetailTopNavigationMenu, {
      [AdobeTagName.CAppFlyFlightDetailTopNavigationMenu]: `${flightDirection}|${flightNumber}|${flightDate}`,
    })
  }

  const shouldShowShareButton = useMemo(
    () =>
      !tickerbandMaintananceHook.isShowMaintenance,
    [
      tickerbandMaintananceHook.isShowMaintenance,
    ],
  )

  const flightTerminalDisclaimerText = useMemo(
    () => flyFlightDetailsPayload?.flightDetailsData?.displayTerminalDisclaimer,
    [flyFlightDetailsPayload],
  )

  const shouldShowSQArrivalTerminalInfo = useMemo(
    () =>
      !!flightTerminalDisclaimerText &&
      !!inf22?.informativeText,
    [inf22?.informativeText, flightTerminalDisclaimerText],
  )

  const { isSaved, isPassenger } = useMemo(() => {
    let isPassenger = false
    let isSaved = false
    const data = flyFlightDetailsPayload?.flightDetailsData
    if (!!myTravelFlightsPayload?.getMyTravelFlightDetails && !!data) {
      const idx = myTravelFlightsPayload.getMyTravelFlightDetails.findIndex((savedFlight) => {
        return (
          savedFlight.flightNumber === data.flightNumber &&
          savedFlight.scheduledDate === data.scheduledDate &&
          (savedFlight.flightDirection || savedFlight.direction) ===
            (data.flightDirection || data.direction)
        )
      })
      if (idx >= 0) {
        isSaved = true
        isPassenger = myTravelFlightsPayload.getMyTravelFlightDetails[idx].isPassenger
      }
    }
    return {
      isSaved: isSaved,
      isPassenger: isPassenger,
    }
  }, [flyFlightDetailsPayload?.flightDetailsData, myTravelFlightsPayload?.getMyTravelFlightDetails])

  useEffect(() => {
    if (isSaved) {
      setIsLoadingGetMyTripData(true)
      const { flightNumber, scheduledDate, airport, direction, originDepDate } =
        flyFlightDetailsPayload?.flightDetailsData || {}
      handleGetMyTrip({
        flight_number: flightNumber,
        departure_date: direction === "ARR" ? originDepDate : scheduledDate,
        airport: direction === "ARR" ? airport : "SIN",
      })
    }
  }, [isSaved, flyFlightDetailsPayload?.flightDetailsData])

  useEffect(() => {
    if (!isSaved) return
    if (!isPassenger) {
      // setSelectedTopTravelOption(TravelOption.iAmPicking) // do later
      setSelectedTravelOption(TravelOption.iAmPicking)
    } else {
      // setSelectedTopTravelOption(TravelOption.iAmTravelling) // do later
      setSelectedTravelOption(TravelOption.iAmTravelling)
    }
  }, [isSaved, isPassenger])

  const isFlightAfter24h = useMemo(() => {
    if (flyFlightDetailsPayload?.flightDetailsData) {
      const { scheduledDate, scheduledTime, actualTimestamp, displayTimestamp } =
        flyFlightDetailsPayload?.flightDetailsData
      const priorityTime =
        actualTimestamp || displayTimestamp || `${scheduledDate} ${scheduledTime}`
      if (priorityTime) {
        const formatedScheduledTime = moment.tz(priorityTime, "YYYY-MM-DD HH:mm", "Asia/Singapore")
        const currentTimeToUTC = moment().tz("Asia/Singapore")
        const isFlighterAfter24h = moment(currentTimeToUTC).add(1, "day") <= formatedScheduledTime
        return isFlighterAfter24h
      }
      return false
    }
    return false
  }, [flyFlightDetailsPayload?.flightDetailsData])


  /**
   * Display referree popup
   * if flight details is loaded and flight is not saved
   * flight number from referrer link must be the same as in screen
   * flow is from referrer link and referrer is different from referree
   * has appscapade data ready
   * flight is eligible (DEP + after 24h)
   * shown for the first time
   */
  useEffect(() => {
    if (
      flyFlightDetailsPayload?.flightDetailsData?.flightNumber &&
      flyItem?.flightNumber === flyFlightDetailsPayload?.flightDetailsData?.flightNumber &&
      referrer &&
      referrer !== profilePayload?.id &&
      flyAppscapadeData &&
      flyFlightDetailsPayload?.flightDetailsData &&
      !isSaved &&
      isFlightAfter24h &&
      !isRefereeModalEverShown
    ) {
      setRefereeModalEverShown(true)
    }
  }, [
    flyFlightDetailsPayload?.flightDetailsData?.flightNumber,
    isSaved,
    flyItem?.flightNumber,
    referrer,
    flyAppscapadeData,
    isFlightAfter24h,
  ])

  const handleMessage48 = (message, number, place) => {
    if (message) {
      return message.replace("<Flight No.>", number).replace("<country>", place)
    }
    return message
  }

  const handleMessage58 = (message) => {
    if (message) {
      let status = flyFlightDetailsPayload?.flightDetailsData?.flightStatus?.toLowerCase()
      if (status?.includes("cancelled")) {
        status = `been ${status}`
      }
      return message
        .replace("<Flight No.>", flyItem?.flightNumber)
        .replace("<departed/landed/been cancelled>", status)
    }
    return message
  }

  const checkFlightCanSave = (statusTag: string, newDirection: string) => {
    const status = statusTag?.toLowerCase()
    const priorityTime =
      flyFlightDetailsPayload?.flightDetailsData?.actualTimestamp ||
      flyFlightDetailsPayload?.flightDetailsData?.estimatedTimestamp ||
      `${flyFlightDetailsPayload?.flightDetailsData?.scheduledDate} ${flyFlightDetailsPayload?.flightDetailsData?.scheduledTime}`
    const currentTimeToUTC = moment().tz("Asia/Singapore")
    const flightTime = moment.tz(priorityTime, "YYYY-MM-DD HH:mm", "Asia/Singapore")
    if (newDirection === FlightDirection.departure) {
      switch (true) {
        case /departed/gim.test(status):
        case /cancelled/gim.test(status):
          return false
        default:
          return true
      }
    } else {
      switch (true) {
        case /cancelled/gim.test(status):
        case /landed/gim.test(status) &&
          moment(flightTime).add(1, "hours").format("YYYY-MM-DD HH:mm") <
            currentTimeToUTC.format("YYYY-MM-DD HH:mm"):
          return false
        default:
          return true
      }
    }
  }

  const notAbleToSaveAlert = (alertApp) => {
    const temp =
      flyFlightDetailsPayload?.flightDetailsData?.flightStatus?.split(" ") ||
      flyItem?.flightStatus?.split(" ")
    const status = temp?.length > 0 ? temp[0] : ""
    const message =
      handleMessage58(msg58?.message) ||
      `${translate("flightLanding.flight")} ${flyItem?.flightNumber} ${translate(
        "flightLanding.has",
      )} ${status} ${translate("flightLanding.notSaveMessage")}`
    alertApp?.current?.show({
      title: msg58?.title || translate("flightLanding.alert"),
      description: message,
      labelAccept: msg58?.firstButton || translate("flightLanding.okay"),
      onAccept: () => null,
      type: AlertTypes.ALERT,
    })
  }

  const onSaveFlight = async (isRemove: boolean, statusPassenger, alertApp, payload) => {
    if (
      !checkFlightCanSave(
        flyFlightDetailsPayload?.flightDetailsData?.flightStatus || flyItem?.flightStatus,
        direction,
      ) &&
      !isRemove
    ) {
      notAbleToSaveAlert(alertApp)
      return null
    }
    const data = {
      enterpriseUserId: profilePayload?.email,
      countryOfResidence: profilePayload?.residentialCountry,
      flightNumber: flyItem?.flightNumber,
      flightScheduledDate:
        flyFlightDetailsPayload?.flightDetailsData?.scheduledDate || flyItem?.scheduledDate,
      flightDirection: direction,
      flightPax: statusPassenger,
    }
    if (isRemove && isLoggedIn) {
      setShowRemoveFlightAlert(true)
      Alert.alert(
        msg48?.title,
        handleMessage48(
          msg48?.message,
          flyItem?.flightNumber,
          flyFlightDetailsPayload?.flightDetailsData?.destinationPlace,
        ),
        [
          {
            text: msg48?.firstButton,
            onPress: () => setShowRemoveFlightAlert(false),
          },
          {
            text: msg48?.secondButton,
            style: "cancel",
            onPress: () => {
              const dtAction = dtManualActionEvent(`${FE_LOG_PREFIX}App__flight-detail-unsave`)
              dtAction.reportStringValue(
                "flight-detail-unsave-press",
                `${flyItem?.flightNumber}-${
                  flyFlightDetailsPayload?.flightDetailsData?.scheduledDate ||
                  flyItem?.scheduledDate
                }-${direction}-${statusPassenger}`,
              )
              setShowRemoveFlightAlert(false)
              const payloadRemove = {...payload}
              payloadRemove.item.scheduledDate = flyFlightDetailsPayload?.flightDetailsData?.scheduledDate
              dispatch(MytravelCreators.flyMyTravelRemoveFlightRequest(data, payloadRemove))
              dtAction.leaveAction()
            },
          },
        ],
      )
    } else {
      if (isLoggedIn) {
        openModalTravelOption()
      } else {
        navigation.navigate(NavigationConstants.authScreen, {
          sourceSystem,
          callBackAfterLoginSuccess: () => {
            getFlightDetails()
            openModalTravelOption()
          },
          callBackAfterLoginCancel: () => null,
        })
      }
    }
  }

  const savedFlightTravelOptionsOnModalHide = () => {
    if (!isSaved) {
      setSelectedTravelOption(initSelectedTravelOption)
    }
  }

  const onClosedTravelOptionSheet = () => {
    if (!loadingSaveFlight) {
      closeModalTravelOption()
      trackAction(AdobeTagName.CAppFlightPersonalizationSaveFlight, {
        [AdobeTagName.CAppFlightPersonalizationSaveFlight]: `${flyItem?.flightNumber} | ${directionText} | ${attendanceType} | ${priorActionRef.current} | Close`,
      })
      priorActionRef.current = "null"
    }
  }

  const onCloseConfirmPopUpSavedFlight = () => {
    closeModalConnectingFlight()
    if (pendingShowFlightJourney === "pending") {
      setPendingShowFlightJourney("finished")
    }
  }

  const updateSavedFlightPriorActions = () => {
    try {
      const savedFlightPriorActions = getMMKVdata(
        ENUM_STORAGE_MMKV.SAVED_FLIGHT_PRIOR_ACTIONS,
        ENUM_STORAGE_TYPE.string,
      ) as string
      let savedFlightPriorActionsArray = []
      if (savedFlightPriorActions) {
        try {
          savedFlightPriorActionsArray = JSON.parse(savedFlightPriorActions)
        } catch (e) {
          savedFlightPriorActionsArray = []
        }
        const priorActionIndex = savedFlightPriorActionsArray?.findIndex?.(
          (action) =>
            action?.uid === profilePayload?.id &&
            action?.flightNumber === flyItem?.flightNumber &&
            action?.direction === direction &&
            action?.scheduledDate === flyItem?.scheduledDate,
        )
        if (priorActionIndex >= 0) {
          const updatedSavedFlightPriorActions = savedFlightPriorActionsArray.map((action, index) => {
            if (index === priorActionIndex) {
              return {
                ...action,
                priorAction: priorActionRef?.current || action?.priorAction,
              }
            } else {
              return action
            }
          })
          setMMKVdata(
            ENUM_STORAGE_MMKV.SAVED_FLIGHT_PRIOR_ACTIONS,
            JSON.stringify(updatedSavedFlightPriorActions),
          )
        } else {
          setMMKVdata(
            ENUM_STORAGE_MMKV.SAVED_FLIGHT_PRIOR_ACTIONS,
            JSON.stringify([
              ...savedFlightPriorActionsArray,
              {
                direction,
                uid: profilePayload?.id,
                flightNumber: flyItem?.flightNumber,
                scheduledDate: flyItem?.scheduledDate,
                priorAction: priorActionRef?.current || "null",
              },
            ]),
          )
        }
      } else {
        const storeFlightPriorActions = [{
          direction,
          uid: profilePayload?.id,
          flightNumber: flyItem?.flightNumber,
          scheduledDate: flyItem?.scheduledDate,
          priorAction: priorActionRef?.current || "null",
        }]
        setMMKVdata(
          ENUM_STORAGE_MMKV.SAVED_FLIGHT_PRIOR_ACTIONS,
          JSON.stringify(storeFlightPriorActions),
        )
      }

      priorActionRef.current = "null"
    } catch (error) {
      console.error("Error updating saved flight prior actions:", error)
    }
  }

  const onPressSavedFlightOnPress = (e) => {
    isSharing.current = false
    savedFlightOnPress({}, e)

    trackAction(AdobeTagName.CAppFlightPersonalizationSaveFlight, {
      [AdobeTagName.CAppFlightPersonalizationSaveFlight]: `${flyItem?.flightNumber} | ${directionText} | ${attendanceType} | ${priorActionRef.current} | Save`,
    })
    updateSavedFlightPriorActions()
  }

  useEffect(() => {
    return () => setSelectedFlightType(null)
  }, [])

  const onButtonPressedConfirmConnectingFlight = () => {
    setSelectedFlightType(FLIGHT_TYPE.RETURN)
    onCloseConfirmPopUpSavedFlight()
  }

  const openSearchDepartureFlightModal = () => {
    dispatch(ModalManagerActions.openModal("searchDepartureFlight"))
  }

  const showToastForSaveFlightSuccess = () => {
    toastForSavedFlight?.current?.show(DURATION.LENGTH_LONG)
  }

  const onModalHideConfirmConnectingFlight = () => {
    dispatch(MytravelCreators.flyClearInsertFlightPayload())
    const timeStamp = new Date().getTime()
    setLastSavedFlightTime(timeStamp)
    if (selectedFlightType) {
      if (
        flyFlightDetailsPayload.flightDetailsData.direction === FlightDirection.arrival &&
        selectedFlightType === FLIGHT_TYPE.CONNECTING &&
        isFlyLandingEnabled
      ) {
        openSearchDepartureFlightModal()
      } else {
        setShowCalendarModal(true)
      }
      setSelectedFlightType(null)
    } else {
      showToastForSaveFlightSuccess()
    }
  }

  const onButtonPressedConfirmSaveFlight = (payload) => {
    const data = {
      enterpriseUserId: profilePayload?.email,
      countryOfResidence: profilePayload?.residentialCountry,
      flightNumber: flyItem?.flightNumber,
      flightScheduledDate: flyItem?.scheduledDate,
      flightDirection: direction,
      flightPax: selectedTravelOption === TravelOption.iAmTravelling,
    }
    payload.item.flightDate = flyFlightDetailsPayload?.flightDetailsData?.scheduledDate
    payload.item.scheduledDate = flyFlightDetailsPayload?.flightDetailsData?.scheduledDate
    const dtAction = dtManualActionEvent(`${FE_LOG_PREFIX}App__flight-detail-save-confirm`)
    dtAction.reportStringValue(
      "flight-detail-save-confirm-press-flightNumber",
      `${flyItem?.flightNumber}`,
    )
    dtAction.reportStringValue(
      "flight-detail-save-confirm-press-scheduledDate",
      `${flyFlightDetailsPayload?.flightDetailsData?.scheduledDate || flyItem?.scheduledDate}`,
    )
    dtAction.reportStringValue("flight-detail-save-confirm-press-direction", `${direction}`)
    dtAction.reportStringValue(
      "flight-detail-save-confirm-press-selectedTravelOption",
      `${selectedTravelOption}`,
    )
    dispatch(MytravelCreators.flyMyTravelInsertFlightRequest(data, payload))
    dispatch(FlyCreators.flyPendingSaveFlight(true))
    dispatch(FlyCreators.flyShowModalConfirmSaveFly(false))
    dtAction.leaveAction()
  }

  const onCloseConfirmSaveFlight = () => {
    dispatch(FlyCreators.flyShowModalConfirmSaveFly(false))
  }

  const handleConnectingFlightOnPress = () => {
    const connectingFlightPayloadData = {
      isConnecting: true,
      flightConnecting: {
        ...flyFlightDetailsPayload?.flightDetailsData,
        flightDate: flyFlightDetailsPayload?.flightDetailsData?.scheduledDate,
        scheduledDate: flyFlightDetailsPayload.flightDetailsData?.scheduledDate,
      },
    }
    dispatch(FlyCreators.setConnectingFlightPayload(connectingFlightPayloadData))
    setSelectedFlightType(FLIGHT_TYPE.CONNECTING)
    onCloseConfirmPopUpSavedFlight()
  }

  const onClosedCalendarModal = () => {
    showToastForSaveFlightSuccess()
    setShowCalendarModal(false)
    const connectingFlightPayloadToClear = {
      isConnecting: false,
      flightConnecting: null,
    }
    dispatch(FlyCreators.setConnectingFlightPayload(connectingFlightPayloadToClear))
  }

  const onDateSelectedAddReturnCalendar = (dateString: string) => {
    const date = moment(dateString)
    setShowCalendarModal(false)
    dispatch(FlyCreators.setFlightSearchDate(date.format("YYYY-MM-DD")))
    if (isFlyLandingEnabled) {
      const country = flyFlightDetailsPayload?.flightDetailsData?.country || ""
      navigation.navigate(NavigationConstants.searchFlightsV2Result, {
        keyword: country,
        date,
        direction:
          direction === FlightDirection.departure
            ? FlightDirection.arrival
            : FlightDirection.departure,
      })
    } else {
      const country = connectingFlightPayload.isConnecting
        ? "Singapore"
        : flyFlightDetailsPayload?.flightDetailsData?.country || ""
      navigation.navigate("flightResultLandingScreen", {
        screen: "Tabs",
        selectedDate: date,
        country: country,
        params: {
          screen:
            direction === FlightDirection.departure
              ? "arrivalResultScreen"
              : "departureResultScreen",
          selectedDate: date,
          country: country,
        },
      })
    }
  }

  const savedFlightOnPress = async (
    _,
    travelOption?: TravelOption,
    isFlightSaver?: boolean,
    flightSaverPayload?: {
      optIn: boolean
      alreadySaved: boolean
    },
  ) => {
    const dtAction = dtManualActionEvent(`${FE_LOG_PREFIX}App__flight-detailv2-save`)
    dtAction.reportStringValue(
      "flight-detailv2-save-press-flightNumber",
      `${flyItem?.flightNumber}`,
    )
    dtAction.reportStringValue(
      "flight-detailv2-save-press-scheduledDate",
      `${flyFlightDetailsPayload?.flightDetailsData?.scheduledDate || flyItem?.scheduledDate}`,
    )
    dtAction.reportStringValue("flight-detailv2-save-press-direction", `${direction}`)
    dtAction.reportStringValue("flight-detailv2-save-press-travelOption", `${travelOption}`)
    // call save
    if (!isFlightSaver) {
      setLoadingSaveFlight(true)
    } else {
      setLoadingSaveFlightOverlay(true)
    }
    const data = {
      enterpriseUserId: profilePayload?.email,
      countryOfResidence: profilePayload?.residentialCountry,
      flightNumber: flyItem?.flightNumber,
      flightScheduledDate:
        flyFlightDetailsPayload?.flightDetailsData?.scheduledDate || flyItem?.scheduledDate,
      flightDirection: direction,
      flightPax: travelOption
        ? travelOption === TravelOption.iAmTravelling
        : selectedTravelOption === TravelOption.iAmTravelling,
    }

    if (isLoggedIn) {
      let payloadForSave = {
        item: flyItem,
        referrer,
        flightNavigationType: FlightNavigationType.FLightSearchDetail,
        isFlightSaver,
      } as any
      if (!payloadForSave?.item?.scheduledTime) {
        payloadForSave = {
          ...payloadForSave,
          item: {
            ...payloadForSave.item,
            scheduledTime: flyFlightDetailsPayload?.flightDetailsData?.scheduledTime,
            flightDate: flyFlightDetailsPayload?.flightDetailsData?.scheduledDate,
            scheduledDate: flyFlightDetailsPayload?.flightDetailsData?.scheduledDate,
          },
        }
      }
      if (referrer) {
        payloadForSave.referrer = referrer
      }
      // enter flight details via FDL
      if (!payloadForSave?.item?.departingCode) {
        payloadForSave.item.departingCode =
          flyFlightDetailsPayload?.flightDetailsData?.departingCode
      }
      if (!payloadForSave?.item?.destinationCode) {
        payloadForSave.item.destinationCode =
          flyFlightDetailsPayload?.flightDetailsData?.destinationCode
      }
      if (!payloadForSave?.item?.direction) {
        payloadForSave.item.direction = direction
      }
      if (!payloadForSave?.item?.terminal) {
        payloadForSave.item.terminal = flyFlightDetailsPayload?.flightDetailsData?.displayTerminal
      }
      if (!payloadForSave?.item?.flightNavigationType) {
        payloadForSave.flightNavigationType =
          flyFlightDetailsPayload?.flightDetailsData?.flightNavigationType ||
          FlightNavigationType.FLightSearchDetail
      }
      if (flightSaverPayload) {
        payloadForSave = {...payloadForSave, ...flightSaverPayload}
      } else {
        payloadForSave = {...payloadForSave, optIn: null, alreadySaved: false}
      }
      dispatch(MytravelCreators.flyMyTravelInsertFlightRequest(data, payloadForSave))
    } else {
      navigation.navigate(NavigationConstants.authScreen, { sourceSystem })
    }
    dtAction.leaveAction()
  }

  const onOpenFlightShareSheet = async (isSavedSuccess?: boolean) => {
    if (loadingFlightMap || loadingShareRef.current) {
      return
    }
    loadingShareRef.current = true
    setLoadingFlightMap(true)
    if (!isSavedSuccess) {
      const isConnected = await checkInternetConnection()
      setNoInterConnection(!isConnected)
      if (!isConnected) {
        setLoadingFlightMap(false)
        loadingShareRef.current = false
        return
      }
    }
    const {
      flightNumber,
      departingCode,
      destinationCode,
      scheduledDate,
      scheduledTime,
    } = flyFlightDetailsPayload.flightDetailsData
    const isDeparture = direction === FlightDirection.departure

    let options: ShareOptions = {
      title: "I would like to share a flight's information with you...",
      message: "I would like to share a flight's information with you...",
      failOnCancel: true
    }

    const shareLinkResponse = await flightDetailV1Hook.getDeeplinkShare({
      mode: "flight_details",
      flightNo: flightNumber,
      direction: direction,
      scheduledDate: scheduledDate,
      scheduledTime: scheduledTime,
      promoCode: "",
      isPassenger: isPassenger,
    })

    setLoadingFlightMap(false)
    loadingShareRef.current = false
    if (!shareLinkResponse.success) {
      return
    }

    if (isDeparture) {
      options = {
        title: "I would like to share a flight's information with you...",
        message: translate("flightDetails.share.shareMessageNonEligible", {
          flightNumber: flightNumber,
          departingCode: "SIN",
          destinationCode: destinationCode,
          scheduledDate: `${moment(`${scheduledDate}`, "YYYY-MM-DD", "Asia/Singapore").format(
            "MMM DD, YYYY",
          )}, at ${moment(scheduledTime, "HH:mm").format("HH:mm")}`,
          actualTimestamp: ` (Updated ${moment()
            .tz("Asia/Singapore")
            .format("HH:mm, DD MMM YYYY")})`,
          flightDetailFDL: shareLinkResponse.data,
        }),
      }
    } else {
      options = {
        title: "I would like to share a flight's information with you...",
        message: translate("flightDetails.share.shareMessageNonEligible", {
          flightNumber: flightNumber,
          departingCode: departingCode,
          destinationCode: destinationCode,
          scheduledDate: `${moment(`${scheduledDate}`, "YYYY-MM-DD", "Asia/Singapore").format(
            "MMM DD, YYYY",
          )}, at ${moment(scheduledTime, "HH:mm").format("HH:mm")}`,
          actualTimestamp: ` (Updated ${moment()
            .tz("Asia/Singapore")
            .format("HH:mm, DD MMM YYYY")})`,
          flightDetailFDL: shareLinkResponse.data,
        }),
      }
    }
    InteractionManager.runAfterInteractions(async() => {
      try {
        const shareResponse = await Share.open(options)
        isSharing.current = false
        loadingShareRef.current = false
        if (shareResponse.success) {
          statusSaveAndShare && finishThreadSaveAndShareModal()
        }
      } catch (error) {
        statusSaveAndShare && finishThreadSaveAndShareModal()
      }
    })
  }

  const onSharePress = () => {
    onOpenFlightShareSheet()
    trackAction(AdobeTagName.CAppFlyFlightDetailTopNavigationMenu, {
      [AdobeTagName.CAppFlyFlightDetailTopNavigationMenu]: `${direction} | ${flyItem?.flightNumber} | Share Button`,
    })
  }

  const onOpenSaveAndShareModal = () => {
    openModalSaveAndShare()
    setStatusSaveAndShare("START FOLLOW")
  }

  const checkSaveAndShare = () => {
    if (enableFlySavePrompt && checkFlightCanSave(
      flyFlightDetailsPayload?.flightDetailsData?.flightStatus || flyItem?.flightStatus,
      direction,
    ) && !tickerbandMaintananceHook.isShowMaintenance) {
      onOpenSaveAndShareModal()
    } else {
      onSharePress()
    }
  }

  const handleSharePress = () => {
    if (isLoggedIn) {
      !isSaved ? checkSaveAndShare() : onSharePress()
    } else {
      clearInterval(intervalRefreshFlight)
      navigation.navigate(NavigationConstants.authScreen, {
        callBackAfterLoginSuccess: checkSaveAndShare,
        callBackAfterLoginCancel: () => null,
      })
    }
  }

  const isButtonSaveHidden = useMemo(() => {
    if (!isLoggedIn) {
      return false
    }
    if (!flyFlightDetailsPayload?.flightDetailsData) {
      return true
    }
    if (tickerbandMaintananceHook.isShowMaintenance) {
      return false
    }
    return flyItem?.isMSError || isSaved
  }, [
    isLoggedIn,
    flyItem?.isMSError,
    isSaved,
    myTravelFlightsPayload?.getMyTravelFlightDetails,
    flyFlightDetailsPayload?.flightDetailsData,
  ])

  const closeBottomSheetError = () => {
    dispatch(FlyCreators.flyCheckInOnlineLoadFailed(false))
  }

  const onButtonPressBottomSheetError = () => {
    if (isLoggedIn) {
      handleNavigationFlightDetail(
        flyFlightDetailsPayload?.flightDetailsData?.onlineCheckIn?.link,
        isButtonSaveHidden,
        navigation,
        dispatch,
      )
      return null
    }
    dispatch(FlyCreators.flyPendingCheckInOnline(true))
    navigation.navigate(NavigationConstants.authScreen, { sourceSystem })
  }

  const onCloseModalSaveAndShare = () => {
    closeModalSaveAndShare()
    setStatusSaveAndShare(null)
  }

  const onShareOnlyPress = () => {
    closeModalSaveAndShare()
    setStatusSaveAndShare("STARTING SHARE")
    onSharePress()
  }

  const finishThreadSaveAndShareModal = () => {
    if (statusSaveAndShare === "SAVE SUCCESS") {
      showToastForSaveFlightSuccess()
    }
    setStatusSaveAndShare(null)
  }

  const onProceedFreeFlightDelay = async () => {
    const { bannerType } = flyFlightDetailsPayload?.flightDetailsData?.flightInsurance
    setLoadingFreeFlightDelay(true)
    const ecid = await getExperienceCloudId()
    const inputData: ITspCSMInput = {
      ecid: ecid,
      stateCode:
        bannerType === EnumBannerType.OPT_IN || bannerType === EnumBannerType.NOT_SAVED || !isSaved
          ? EnumInputStateCode.PROMOLISTING_OPTIN
          : EnumInputStateCode.PROMOLISTING_OPTOUT,
      staff: profilePayload?.airportStaff ? 1 : 0,
    }
    if (flyItem?.flightDate) { 
      inputData.flightDt = flyItem?.flightDate
    }
    if (flyItem?.flightNumber) {
      inputData.flightNo = flyItem?.flightNumber
    }
    if (flyFlightDetailsPayload?.flightDetailsData?.airportDetails?.country_code) {
      inputData.route = flyFlightDetailsPayload?.flightDetailsData?.airportDetails?.country_code
    }
    try {
      const response = await getDeepLinkV2(
        {
          stateCode: StateCode.CSM_TSP,
          params: "",
          input: inputData,
        },
        true,
      )
      if (response?.redirectUri) {
        setLoadingFreeFlightDelay(false)
        navigation.navigate(NavigationConstants.playpassWebview, {
          uri: response?.redirectUri,
          needBackButton: true,
          needCloseButton: true,
          headerType: WebViewHeaderTypes.default,
          basicAuthCredential: response?.basicAuth,
        })
      } else {
        throw response
      }
    } catch (error) {
      setLoadingFreeFlightDelay(false)
      setTimeout(() => {
        dispatch(SystemActions.setBottomSheetErrorData({ visible: true }))
      }, 300)
    }
  }

  const onProceedSharing = (alertApp) => {
    if (loadingFlightMap || loadingFreeFlightDelay || loadingSaveFlightOverlay) {
      return;
    }
    closeModalFlightSaver();

    if (
      !checkFlightCanSave(
        flyFlightDetailsPayload?.flightDetailsData?.flightStatus || flyItem?.flightStatus,
        direction,
      ) &&
      !isSaved
    ) {
      notAbleToSaveAlert(alertApp)
      return
    }

    const { bannerType } = flyFlightDetailsPayload?.flightDetailsData?.flightInsurance

    savedFlightOnPress({}, TravelOption.iAmTravelling, true, {
      optIn: bannerType === EnumBannerType.OPT_IN || bannerType === EnumBannerType.NOT_SAVED || !isSaved,
      alreadySaved: isSaved,
    })
  }

  const scrollToTop = () => {
    scrollViewRef.current.scrollTo({
      y: 0,
      animated: true,
    })
  }

  useEffect(() => {
    if (statusSaveAndShare && insertFlightPayload?.errorFlag) {
      onShareOnlyPress()
    }
  }, [insertFlightPayload?.errorFlag])

  useEffect(() => {
    if(statusSaveAndShare === "SAVE SUCCESS"){
      onSharePress();
    }
  },[statusSaveAndShare])

  return {
    flightDetailV1Hook,
    tickerbandMaintananceHook,
    trackAAWhenBack,
    shouldShowShareButton,
    isLoggedIn,
    flyShowTickerBand,
    flyFlightDetailsPayload,
    flyLastUpdatedTimeStamp,
    shouldShowSQArrivalTerminalInfo,
    inf22,
    showNoInternetError,
    handleSharePress,
    loadingSaveFlight,
    insertFlightPayload,
    isNoInternetConnection,
    onOpenFlightShareSheet,
    loadingFlightMap,
    handleMap,
    isLoadingDetailFlight,
    isLoadingIntoCityOrAirport,
    intoCityOrAirportPayload,
    getBackgroundAirportAEM,
    isSaved,
    isButtonSaveHidden,
    checkFlightCanSave,
    travelChecklistAEM,
    isTravelChecklistAEMLoading,
    handleGetTravelChecklistAEM,
    unableToLoadLocationRef,
    mapUnavailable,
    refreshFlightDetails,
    toastForRefresh,
    flyFlightDetailsError,
    enableEciDynamicDisplay,
    flightDetailSectionData,
    onPressFlightCardLinks,
    setSelectedTravelOption,
    selectedTravelOption,
    onSaveFlight,
    savedFlightTravelOptionsOnModalHide,
    modalTravelOptionVisible: isModalVisibleTravelOption,
    onClosedTravelOptionSheet,
    savedFlightOnPress,
    isModalVisible: isModalVisibleConnectingFlight,
    isFocused,
    onCloseConfirmPopUpSavedFlight,
    msg47,
    onPressSavedFlightOnPress,
    onButtonPressedConfirmConnectingFlight,
    onModalHideConfirmConnectingFlight,
    handleConnectingFlightOnPress,
    showCalendarModal,
    onClosedCalendarModal,
    onDateSelectedAddReturnCalendar,
    removeFlightPayload,
    isMSError: flyItem?.isMSError,
    handleGetMyTrip,
    getMyTripData,
    onRetryGetMyTripData,
    isLoadingGetMyTripData,
    isErrorGetMyTrip,
    iconUrl,
    onButtonPressedConfirmSaveFlight,
    onCloseConfirmSaveFlight,
    msg65,
    closeBottomSheetError,
    onButtonPressBottomSheetError,
    enableFlightJourney,
    pendingShowFlightJourney,
    setPendingShowFlightJourney,
    isFlyLandingEnabled,
    isModalSaveAndShare,
    onCloseModalSaveAndShare,
    onShareOnlyPress,
    showToastForSaveFlightSuccess,
    onProceedFreeFlightDelay,
    loadingFreeFlightDelay,
    isShowBanner,
    onProceedSharing,
    loadingSaveFlightOverlay,
    setIsBannerAEMLoading,
    isBannerAEMLoading,
    floatSaveButtonHeight, 
    setFloatSaveButtonHeight,
    scrollToTop,
  }
}
