import React, { useState, useEffect, useRef } from "react"
import {
  View,
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Animated,
  ViewStyle,
  Platform,
} from "react-native"
import { presets, Text } from "app/elements/text"
import { color } from "app/theme"
import { useFocusEffect, useIsFocused, useNavigation } from "@react-navigation/native"
import moment from "moment"
import { DateFormats } from "app/utils/date-time/date-time"
import { translate } from "app/i18n"
import { useSelector } from "react-redux"
import { LoadingOverlay } from "app/components/loading-modal"
import { AemSelectors } from "app/redux/aemRedux"
import { commonTrackingScreen } from "app/services/adobe"
import { NativeAuthSelectors as AuthSelectors } from "app/redux/nativeAuthRedux"
import LinearGradient from "react-native-linear-gradient"
import { choosePictureFromGallery } from "app/utils/media-helper"
import { get, isEmpty } from "lodash"
import RNQRGenerator from "rn-qr-generator"
import BackButtonSvg from "../backbutton"
import { RNHoleView } from "react-native-hole-view"
import { FlashModeOff, FlashModeOn } from "ichangi-fe/assets/icons"
import { checkFlightAvailable, checkLuckyDrawEligible } from "app/sagas/flySaga"
import { NavigationTypeEnum } from "app/redux/types/explore/navigation-type"
import { NavigationValueDeepLink, useHandleNavigation } from "app/utils/navigation-helper"
import { handleCondition } from "app/utils"
import { getPreviousScreen, useCurrentScreenActiveAndPreviousScreenHook } from "app/utils/screen-hook"

const TIMEOUT_OFFSET = 30000
const { width, height } = Dimensions.get("window")

const MASK_WIDTH = 327
const MASK_HEIGHT = 380
const MASK_POSITION_X = 150
const HOLE_VIEW_BACKGROUND = "rgba(0, 0, 0, 0.4)"
const MASK_PADDING_LEFT = Math.round(width / 2) - 163
const DURATION = 500
let timer: any

const mappingFlightData = (checkFlight) => {
  const newScheduledDate =
    checkFlight?.direction === "DEP" ? checkFlight?.scheduled_date : checkFlight?.origin_dep_date
  return {
    logo: checkFlight?.airline_details?.logo_url,
    flightNumber: checkFlight?.flight_number,
    departingCode: "SIN",
    destinationCode: "PER",
    flightDate: newScheduledDate,
    scheduledDate: newScheduledDate,
    state: "default",
    codeShare: checkFlight?.slave_flights,
    destinationPlace: "Perth",
    departingPlace: "Singapore",
    timeOfFlight: checkFlight?.scheduled_time,
    flightStatus: checkFlight?.flight_status,
    flightStatusMapping: checkFlight?.status_mapping?.listing_status_en,
    beltStatusMapping: null,
    statusColor: "Red",
    showGate: !!checkFlight?.status_mapping?.show_gate,
    isSaved: false,
    flightUniqueId: `${checkFlight?.flight_number}_${newScheduledDate}`,
    estimatedTimestamp: null,
    actualTimestamp: null,
    direction: checkFlight?.direction,
    terminal: checkFlight?.terminal,
    checkInRow: checkFlight?.check_in_row,
    displayBelt: null,
    displayTimestamp: "2023-09-22 00:05",
    viaAirportDetails: null,
  }
}

const mappingWithCamelCaseFlightData = (checkFlight) => {
  return {
    logo: checkFlight?.airlineDetails?.logo_url,
    flightNumber: checkFlight?.flightNumber,
    departingCode: "SIN",
    destinationCode: checkFlight?.destinationCode,
    flightDate: checkFlight?.scheduledDate,
    scheduledDate: checkFlight?.scheduledDate,
    state: "default",
    codeShares: checkFlight?.codeShares,
    destinationPlace: checkFlight?.destinationPlace,
    departingPlace: checkFlight?.departingPlace,
    timeOfFlight: checkFlight?.scheduledTime,
    flightStatus: checkFlight?.flightStatus,
    flightStatusMapping: checkFlight?.statusMapping?.listing_status_en,
    beltStatusMapping: null,
    boardingGate: "B1",
    airportDetails: {
      code: "SUB",
      name: "Surabaya",
      name_zh: "泗水",
      name_zh_hant: "泗水",
      lat: "-7.379829884",
      lng: "112.7870026",
      country_code: "ID",
    },
    statusColor: "Red",
    showGate: !!checkFlight?.status_mapping?.show_gate,
    isSaved: false,
    flightUniqueId: `${checkFlight?.flightNumber}_${checkFlight?.scheduledDate}`,
    estimatedTimestamp: null,
    actualTimestamp: null,
    direction: checkFlight?.direction,
    terminal: checkFlight?.terminal,
    checkInRow: checkFlight?.checkInRow,
    displayBelt: null,
    displayTimestamp: checkFlight?.displayTimestamp,
    viaAirportDetails: null,
  }
}

export enum AppscapadeEgilibleStatus {
  success = "success",
  passAlreadyScanned = "pass_already_scanned",
  notSavedInAdvance = "not_saved_in_advance",
  noSavedFlights = "no_saved_flights",
  notDepartureFlight = "not_departure_flight",
  notTravellingOnFlight = "not_travelling_on_flight",
  maxEntriesSubmitted = "max_entries_submitted",
  entryAlreadyExists = "entry_already_exists",
}
const AppscapadeScanBoadingPassScreenV1 = ({ route }) => {
  const navigation = useNavigation()
  const { isFromFlightDetail } = route?.params || false
  const refScanner = useRef(null)
  const [needScan, setNeedScan] = useState(true)
  const [readyScan, setReadyScan] = useState(false)
  const [onFlash, setOnFlash] = useState(false)
  const [time, setTime] = useState(0)
  const [loadingDectectBoardingPass, setLoadingDectectBoardingPass] = useState(false)
  const isFocused = useIsFocused()

  const isLoggedIn = useSelector(AuthSelectors.isLoggedIn)
  const [isCheckingFlightAvailable, setIsCheckingFlightAvailable] = useState(false)

  const dataCommonAEM = useSelector(AemSelectors.getAemConfig("AEM_COMMON_DATA"))
  const msg52 = dataCommonAEM?.data?.messages?.find((e) => e?.code === "MSG52")
  const msg53 = dataCommonAEM?.data?.messages?.find((e) => e?.code === "MSG53")
  const msg54 = dataCommonAEM?.data?.messages?.find((e) => e?.code === "MSG54")

  const positionAnimatedLine = useRef(new Animated.Value(0)).current
  const { handleNavigation } = useHandleNavigation("SCAN_BOADING_PASS")

  const flightDetailDataRef = useRef(null)

  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(positionAnimatedLine, {
          toValue: MASK_HEIGHT - 60,
          duration: DURATION,
          useNativeDriver: true,
        }),
        Animated.timing(positionAnimatedLine, {
          toValue: 0,
          duration: DURATION,
          useNativeDriver: true,
        }),
      ]),
    ).start()
  }, [])

  useFocusEffect(
    React.useCallback(() => {
      resetState()
    }, []),
  )

  const animatedLineStyle: ViewStyle = {
    transform: [{ translateY: positionAnimatedLine }],
  }

  const resetState = () => {
    setNeedScan(true)
    setOnFlash(false)
    setTime(0)
    flightDetailDataRef.current = null
  }

  useCurrentScreenActiveAndPreviousScreenHook("Explore_ScanCode")

  useEffect(() => {
    const unsubscribeFocus = navigation.addListener("focus", () => {
      commonTrackingScreen(
        "Explore_ScanCode",
        getPreviousScreen(),
        isLoggedIn,
      )
    })
    return unsubscribeFocus
  }, [navigation])

  useEffect(() => {
    if (isFocused) {
      resetState()
      flightDetailDataRef.current = null
      timer = setInterval(() => {
        setTime((prevTime) => prevTime + 1000)
      }, 1000)
    }
    return () => {
      clearInterval(timer)
    }
  }, [isFocused])

  const alertCanNotDetect = () => {
    Alert.alert(
      handleCondition(
        msg54?.title,
        msg54?.title,
        translate("scanCode.noBoardingPassDetected.title"),
      ),
      handleCondition(
        msg54?.message,
        msg54?.message,
        translate("scanCode.noBoardingPassDetected.description"),
      ),
      [
        {
          text: handleCondition(
            msg54?.firstButton,
            msg54?.firstButton,
            translate("scanCode.noBoardingPassDetected.firstButton"),
          ),
          style: "cancel",
          onPress: () => {
            setNeedScan(true)
            handleGoBack()
          },
        },
        {
          text: handleCondition(
            msg54?.secondButton,
            msg54?.secondButton,
            translate("scanCode.noBoardingPassDetected.secondButton"),
          ),
          onPress: () => {
            setNeedScan(true)
          },
        },
      ],
    )
  }

  useEffect(() => {
    if (
      handleCondition(
        readyScan && needScan && time && time % TIMEOUT_OFFSET === 0 && isFocused,
        true,
        false,
      )
    ) {
      setNeedScan(false)
      alertCanNotDetect()
    }
  }, [time])

  useEffect(() => {
    if (!isFocused) {
      clearInterval(timer)
    }
  }, [isFocused])

  // Handle after get full permission and start scan
  const dayToDate = (day) => {
    const year = new Date().getFullYear()
    const date = new Date(year, 0, day)
    return moment(new Date(date)).format(DateFormats.YearMonthDay)
  }

  const handleGoToFlight = async (flyItem) => {
    const flightDataMapped = mappingFlightData(flightDetailDataRef.current)
    clearInterval(timer)
    navigation.navigate("flightDetails", {
      payload: {
        item: {
          ...flightDataMapped,
          departingCode: flyItem.departingCode,
          destinationCode: flyItem.destinationCode,
        },
        itemIndex: 0,
        sectionIndex: 1,
        flightNavigationType: "departureLanding",
      },
      isFromScanBoardingPass: true,
      direction: flightDetailDataRef.current?.direction,
    })
  }

  const handleResponseForEligiblePass = (dataPass, flyItem) => {
    const { status, chance_id: chanceId } = dataPass || ""
    switch (status) {
      case AppscapadeEgilibleStatus.success:
        if (!chanceId) break
        handleNavigation(
          NavigationTypeEnum.deepLink,
          NavigationValueDeepLink.appscapadePlayAndWin,
          {
            chanceId,
          },
        )
        break
      case AppscapadeEgilibleStatus.passAlreadyScanned:
        Alert.alert(
          translate("scanBoardingPass.passAlreadyScanned.title"),
          translate("scanBoardingPass.passAlreadyScanned.description"),
          [
            {
              text: translate("scanBoardingPass.passAlreadyScanned.buttonCancelLabel"),
              style: "cancel",
              onPress: () => {
                setNeedScan(true)
                handleGoBack()
              },
            },
          ],
        )
        break
      case AppscapadeEgilibleStatus.notSavedInAdvance:
      case AppscapadeEgilibleStatus.noSavedFlights:
        Alert.alert(
          translate("scanBoardingPass.notSavedInAdvance.title"),
          translate("scanBoardingPass.notSavedInAdvance.description"),
          [
            {
              text: translate("scanBoardingPass.notSavedInAdvance.buttonCancelLabel"),
              style: "cancel",
              onPress: () => {
                setNeedScan(true)
                handleGoBack()
              },
            },
            {
              text: translate("scanBoardingPass.notSavedInAdvance.buttonAcceptLabel"),
              onPress: () => {
                handleGoToFlight(flyItem)
              },
            },
          ],
        )
        break
      case AppscapadeEgilibleStatus.notDepartureFlight:
        Alert.alert(
          translate("scanBoardingPass.notDepartureFlight.title"),
          translate("scanBoardingPass.notDepartureFlight.description"),
          [
            {
              text: translate("scanBoardingPass.notDepartureFlight.buttonCancelLabel"),
              style: "cancel",
              onPress: () => {
                setNeedScan(true)
                handleGoBack()
              },
            },
            {
              text: translate("scanBoardingPass.notDepartureFlight.buttonAcceptLabel"),
              onPress: () => {
                handleGoToFlight(flyItem)
              },
            },
          ],
        )
        break
      case AppscapadeEgilibleStatus.notTravellingOnFlight:
        Alert.alert(
          translate("scanBoardingPass.notTravellingOnFlight.title"),
          translate("scanBoardingPass.notTravellingOnFlight.description"),
          [
            {
              text: translate("scanBoardingPass.notTravellingOnFlight.buttonCancelLabel"),
              style: "cancel",
              onPress: () => {
                setNeedScan(true)
                handleGoBack()
              },
            },
            {
              text: translate("scanBoardingPass.notTravellingOnFlight.buttonAcceptLabel"),
              onPress: () => {
                handleGoToFlight(flyItem)
              },
            },
          ],
        )
        break
      case AppscapadeEgilibleStatus.maxEntriesSubmitted:
        Alert.alert(
          translate("scanBoardingPass.maxEntriesSubmitted.title"),
          translate("scanBoardingPass.maxEntriesSubmitted.description"),
          [
            {
              text: translate("scanBoardingPass.maxEntriesSubmitted.buttonCancelLabel"),
              style: "cancel",
              onPress: () => {
                setNeedScan(true)
                handleGoBack()
              },
            },
          ],
        )
        break
      case AppscapadeEgilibleStatus.entryAlreadyExists:
        Alert.alert(
          translate("scanBoardingPass.entryAlreadyExists.title"),
          translate("scanBoardingPass.entryAlreadyExists.description"),
          [
            {
              text: translate("scanBoardingPass.entryAlreadyExists.buttonCancelLabel"),
              style: "cancel",
              onPress: () => {
                setNeedScan(true)
                handleGoBack()
              },
            },
            {
              text: translate("scanBoardingPass.entryAlreadyExists.buttonAcceptLabel"),
              onPress: () => {
                handleGoToFlight(flyItem)
              },
            },
          ],
        )
        break
      default:
        setNeedScan(true)
        break
    }
  }

  const readDataFromBoardingPass = async (data) => {
    const dataSplit = data.replace(/\s+/g, " ").trim()?.split(" ")
    const indexOfFlightNumber = dataSplit.findIndex(
      (newData) => newData.length === 4 && !isNaN(newData),
    )
    let flyItem: any
    if (indexOfFlightNumber > 0) {
      const placeInformation = dataSplit[indexOfFlightNumber - 1]
      const timeInformation = dataSplit[indexOfFlightNumber + 1]
      if (placeInformation && placeInformation.length === 8) {
        const departingCode = placeInformation.substring(0, 3)
        const destinationCode = placeInformation.substring(3, 6)
        const checkInSeqNumber = timeInformation.slice(-4)
        const airlineCode = placeInformation.substring(6, 8)
        flyItem = {
          departingCode,
          destinationCode,
          airlineCode,
          direction: handleCondition(departingCode === "SIN", "DEP", "ARR"),
          flightNumber: `${airlineCode}${Number(dataSplit[indexOfFlightNumber])}`,
          checkInSeqNumber: Number(checkInSeqNumber),
        }
      } else {
        flyItem = false
      }
      if (timeInformation && !isNaN(timeInformation?.substring(0, 3))) {
        const scheduleDateTime = dayToDate(timeInformation?.substring(0, 3))
        flyItem = {
          ...flyItem,
          flightDate: scheduleDateTime,
          flightUniqueId: `${flyItem?.flightNumber}_${scheduleDateTime}`,
        }
      } else {
        flyItem = false
      }
    }
    if (!flyItem && isFocused) {
      Alert.alert(
        handleCondition(
          msg53?.title,
          msg53?.title,
          translate("scanCode.invalidBoardingPass.title"),
        ),
        handleCondition(
          msg53?.message,
          msg53?.message,
          translate("scanCode.invalidBoardingPass.description"),
        ),
        [
          {
            text: handleCondition(
              msg53?.firstButton,
              msg53?.firstButton,
              translate("scanCode.invalidBoardingPass.firstButton"),
            ),
            style: "cancel",
            onPress: () => {
              setNeedScan(true)
              handleGoBack()
            },
          },
          {
            text:
              (msg53?.secondButton,
              msg53?.secondButton,
              translate("scanCode.invalidBoardingPass.secondButton")),
            onPress: () => {
              setNeedScan(true)
            },
          },
        ],
      )
    } else {
      setIsCheckingFlightAvailable(true)
      const checkFlight = await checkFlightAvailable({
        direction: flyItem?.direction,
        flightNumber: flyItem?.flightNumber,
        scheduledDate: flyItem?.flightDate,
        airlineCode: flyItem?.flightNumber?.substring(0, 2),
        flightStatus: null,
      })
      flightDetailDataRef.current = checkFlight
      setIsCheckingFlightAvailable(false)
      const invalidStatus = ["departed", "landed"]
      if (
        (!checkFlight?.flight_number ||
          invalidStatus.includes(checkFlight.flight_status?.toLowerCase())) &&
        isFocused
      ) {
        Alert.alert(
          handleCondition(msg52?.title, msg52?.title, translate("scanCode.flightNotFound.title")),
          handleCondition(
            msg52?.message,
            msg52?.message,
            translate("scanCode.flightNotFound.description"),
          ),
          [
            {
              text: handleCondition(
                msg52?.firstButton,
                msg52?.firstButton,
                translate("scanCode.flightNotFound.firstButton"),
              ),
              style: "cancel",
              onPress: () => {
                setNeedScan(true)
                handleGoBack()
              },
            },
            {
              text: handleCondition(
                msg52?.secondButton,
                msg52?.secondButton,
                translate("scanCode.flightNotFound.secondButton"),
              ),
              onPress: () => {
                setNeedScan(true)
              },
            },
          ],
        )
        return
      }
      setIsCheckingFlightAvailable(true)
      const eligibleLuckyDraw = await checkLuckyDrawEligible({
        direction: flyItem?.direction,
        flightNumber: flyItem?.flightNumber,
        scheduledDate: flyItem?.flightDate,
        airportCode: flyItem?.destinationCode,
        checkInSeqNumber: flyItem?.checkInSeqNumber,
      })
      setIsCheckingFlightAvailable(false)
      handleResponseForEligiblePass(eligibleLuckyDraw, flyItem)
    }
  }

  const handleGoBack = () => {
    resetState()
    clearInterval(timer)
    if (isFromFlightDetail) {
      navigation.replace("flightDetails", {
        payload: {
          item: {
            ...mappingWithCamelCaseFlightData(isFromFlightDetail),
          },
        },
        isFromScanBoardingPass: true,
        direction: handleCondition(isFromFlightDetail?.departingCode === "SIN", "DEP", "ARR"),
      })
    } else {
      navigation.goBack()
    }
  }

  const readFromCamera = (barcodes, type) => {
    if (type === "barcode" && barcodes?.[0]?.dataRaw) {
      setNeedScan(false)
      readDataFromBoardingPass(barcodes?.[0]?.dataRaw)
    }
  }

  const toggleFlashMode = () => {
    setOnFlash(!onFlash)
  }

  const uploadBoardingPassFromGallery = () => {
    setNeedScan(false)
    setTimeout(async () => {
      try {
        const imageData: any = await choosePictureFromGallery({ multiple: false })
        const uriImage = get(imageData, "uri")
        if (uriImage) {
          setLoadingDectectBoardingPass(true)
          RNQRGenerator.detect({
            uri: uriImage,
          })
            .then((response) => {
              const { values } = response
              if (!isEmpty(values)) {
                setLoadingDectectBoardingPass(false)
                readDataFromBoardingPass(values?.[0])
              } else {
                setLoadingDectectBoardingPass(false)
                alertCanNotDetect()
              }
            })
            .catch((error) => {
              setNeedScan(true)
              console.log("Cannot detect QR code in image", error)
            })
        }
      } catch (error) {
        setNeedScan(true)
        console.log("Error while selecting the picture from gallery", error)
      }
    }, 200)
  }

  const headerScan = () => {
    return (
      <View style={styles.wrapHeaderScan}>
        <TouchableOpacity onPress={() => handleGoBack()}>
          <BackButtonSvg />
        </TouchableOpacity>
        <View style={styles.wrapRightAction}></View>
      </View>
    )
  }

  const uploadBoardingPassBtn = () => {
    return (
      <View style={styles.wrapUploadBoardingPassBtn}>
        <TouchableOpacity onPress={uploadBoardingPassFromGallery}>
          <LinearGradient
            style={styles.touchableUploadBoardingPassStyle}
            start={{ x: 0, y: 1 }}
            end={{ x: 1, y: 0 }}
            colors={[color.palette.gradientColor1Start, color.palette.gradientColor1End]}
          >
            <Text text="Upload E-Boarding Pass" style={styles.textBtnUploadBoardingPassStyle} />
          </LinearGradient>
        </TouchableOpacity>
      </View>
    )
  }
  const footerScan = () => {
    return (
      <View style={styles.wrapFooterScan}>
        <TouchableOpacity onPress={() => toggleFlashMode()}>
          {handleCondition(onFlash, <FlashModeOn />, <FlashModeOff />)}
        </TouchableOpacity>
      </View>
    )
  }
  return (
    <View style={{ height: height, width: width }}>
      {/* <RNCamera
        ref={refScanner}
        style={styles.preview}
        captureAudio={false}
        type={RNCamera.Constants.Type.back}
        flashMode={handleCondition(
          onFlash,
          RNCamera.Constants.FlashMode.torch,
          RNCamera.Constants.FlashMode.off,
        )}
        onCameraReady={() => setReadyScan(true)}
        onGoogleVisionBarcodesDetected={
          needScan ? ({ barcodes, type }) => readFromCamera(barcodes, type) : null
        }
        onResponderReject={() => console.log("reject")}
        notAuthorizedView={<></>}
      /> */}
      <RNHoleView
        style={styles.holeViewStyle}
        holes={[
          {
            x: MASK_PADDING_LEFT,
            y: MASK_POSITION_X,
            width: MASK_WIDTH,
            height: MASK_HEIGHT,
            borderRadius: 12,
          },
        ]}
      >
        {headerScan()}
        <View style={styles.bottomStyle}>
          <Text tx={"scanCode.description"} style={styles.description} preset={"caption1Bold"} />
          {uploadBoardingPassBtn()}
          {footerScan()}
        </View>
      </RNHoleView>
      <Animated.View style={[styles.animatedLine, animatedLineStyle]} />
      <LoadingOverlay
        visible={handleCondition(
          isCheckingFlightAvailable || loadingDectectBoardingPass,
          true,
          false,
        )}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  animatedLine: {
    backgroundColor: color.palette.lightPurple,
    height: 4,
    left: MASK_PADDING_LEFT - 1,
    position: "absolute",
    top: MASK_POSITION_X + 20,
    width: MASK_WIDTH,
  },
  bottomStyle: {
    alignItems: "center",
    paddingHorizontal: 52,
    position: "absolute",
    top: MASK_POSITION_X + MASK_HEIGHT + 16,
    width: "100%",
  },
  description: {
    ...presets.caption1Bold,
    color: color.palette.whiteGrey,
    letterSpacing: 0.06,
    lineHeight: 18,
    marginBottom: 16,
    textAlign: "center",
    textAlignVertical: "center",
  },
  holeViewStyle: {
    backgroundColor: Platform.OS === 'android' ? 'clear' : HOLE_VIEW_BACKGROUND,
    height: "100%",
    position: "absolute",
    width: "100%",
  },
  preview: {
    alignItems: "center",
    flex: 1,
    justifyContent: "flex-end",
  },
  textBtnUploadBoardingPassStyle: {
    ...presets.caption1Bold,
    color: color.palette.whiteGrey,
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 2,
    textAlignVertical: "center",
  },
  touchableUploadBoardingPassStyle: {
    borderRadius: 60,
    paddingHorizontal: 16,
    paddingVertical: 4,
  },
  wrapFooterScan: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    marginTop: 24,
    paddingHorizontal: 20,
  },
  wrapHeaderScan: {
    alignItems: "center",
    backgroundColor: color.transparent,
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    position: "absolute",
    top: 60,
    width: "100%",
  },
  wrapRightAction: {
    flexDirection: "row",
  },
  wrapUploadBoardingPassBtn: {
    alignItems: "center",
  },
})
export default AppscapadeScanBoadingPassScreenV1
