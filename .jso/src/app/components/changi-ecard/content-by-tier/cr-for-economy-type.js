  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _lodash = _$$_REQUIRE(_dependencyMap[8]);
  var _backgrounds = _$$_REQUIRE(_dependencyMap[9]);
  var _changiEcardModal = _$$_REQUIRE(_dependencyMap[10]);
  var _icons = _$$_REQUIRE(_dependencyMap[11]);
  var _changiRewardsECard = _$$_REQUIRE(_dependencyMap[12]);
  var _adobe = _$$_REQUIRE(_dependencyMap[13]);
  var _reactNativeQrcodeSvg = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _baseImageBackground = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[16]);
  var _account = _$$_REQUIRE(_dependencyMap[17]);
  var _crCardV = _$$_REQUIRE(_dependencyMap[18]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[19]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var CRForEconomyType = function CRForEconomyType(_ref) {
    var _contentBannerCard$da;
    var selectedPresetTier = _ref.selectedPresetTier,
      closeScreen = _ref.closeScreen,
      profilePayload = _ref.profilePayload,
      rewardDetails = _ref.rewardDetails,
      onPressCopy = _ref.onPressCopy,
      scanToPay = _ref.scanToPay,
      newNavigation = _ref.newNavigation,
      contentBannerCard = _ref.contentBannerCard,
      navigationRoute = _ref.navigationRoute,
      isGetRewardCardV2 = _ref.isGetRewardCardV2;
    var _useContext = (0, _react.useContext)(_account.AccountContext),
      myRewardCardV2 = _useContext.myRewardCardV2;
    var isMyRewardCardV2 = (0, _remoteConfig.getFeatureFlagInit)(_remoteConfig.REMOTE_CONFIG_FLAGS.FLIGHT_PAID_INSURANCE, myRewardCardV2);
    var contentEconomy = contentBannerCard == null || (_contentBannerCard$da = contentBannerCard.data) == null || (_contentBannerCard$da = _contentBannerCard$da.list) == null ? undefined : _contentBannerCard$da[0];
    var boxShadowColor = {
      shadowColor: selectedPresetTier.linearGradientColor,
      shadowOffset: {
        width: 0,
        height: 2
      },
      shadowOpacity: 0.23,
      shadowRadius: 2.62,
      elevation: 4
    };
    return (0, _jsxRuntime.jsxs)(_reactNativeLinearGradient.default, {
      start: {
        x: 1,
        y: 0
      },
      end: {
        x: 0,
        y: 1
      },
      colors: selectedPresetTier.linearGradientColor,
      style: _changiEcardModal.styles.gradientStyle,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _changiEcardModal.styles.wrapHeader,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _changiEcardModal.styles.leftPart
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          tx: "changiRewardsEcard.header",
          preset: "subTitleBold",
          style: _changiEcardModal.styles.headerText
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _changiEcardModal.styles.rightPart,
          children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: closeScreen,
            children: (0, _jsxRuntime.jsx)(_icons.CrossWithBackground, {})
          })
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: {
          flex: 1
        },
        children: (0, _jsxRuntime.jsx)(_reactNative2.ScrollView, {
          showsVerticalScrollIndicator: false,
          contentContainerStyle: _changiEcardModal.styles.contentScroll,
          children: (0, _jsxRuntime.jsxs)(_reactNative2.SafeAreaView, {
            style: _changiEcardModal.styles.safeAreaViewStyle,
            children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              activeOpacity: 1,
              children: (0, _jsxRuntime.jsx)(_baseImageBackground.default, {
                style: _changiEcardModal.styles.wrapBenzelSize,
                source: profilePayload != null && profilePayload.airportStaff ? _backgrounds.ChangiStaff : "",
                resizeMode: "stretch",
                children: (0, _jsxRuntime.jsxs)(_baseImageBackground.default, {
                  style: _changiEcardModal.styles.wrapBenzelSizeLevel2,
                  source: profilePayload != null && profilePayload.airportStaff ? _backgrounds.ChangiStaff : "",
                  resizeMode: "stretch",
                  children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
                    style: [_changiEcardModal.styles.wrapQRSize, boxShadowColor],
                    children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
                      style: _changiEcardModal.styles.overlayHiddenCorner,
                      children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
                        style: _changiEcardModal.styles.wrapQR,
                        children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
                          style: _changiEcardModal.styles.scaleQR,
                          children: !(0, _lodash.isEmpty)(rewardDetails == null ? undefined : rewardDetails.barCode) && (0, _jsxRuntime.jsx)(_reactNativeQrcodeSvg.default, {
                            value: rewardDetails == null ? undefined : rewardDetails.barCode,
                            size: 160
                          })
                        })
                      })
                    })
                  }, "wrapQRSize"), (profilePayload == null ? undefined : profilePayload.airportStaff) && (0, _jsxRuntime.jsx)(_baseImageBackground.default, {
                    source: _backgrounds.GradientTextStaff,
                    style: _changiEcardModal.styles.wrapStaffText,
                    children: (0, _jsxRuntime.jsx)(_text.Text, {
                      text: "STAFF",
                      style: _changiEcardModal.styles.textStaff
                    })
                  })]
                }, "wrapBenzelSizeLevel2")
              }, "wrapBenzelSize")
            }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
              activeOpacity: 1,
              children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: _changiEcardModal.styles.wrapAccountName,
                children: (0, _jsxRuntime.jsx)(_text.Text, {
                  text: profilePayload == null ? undefined : profilePayload.firstName,
                  style: _changiEcardModal.styles.accountTextName,
                  numberOfLines: 1,
                  preset: "h1"
                })
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                text: rewardDetails == null ? undefined : rewardDetails.tierCode,
                style: _changiEcardModal.styles.tierCodeText
              }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: _changiEcardModal.styles.wrapCardNo,
                children: [(0, _jsxRuntime.jsx)(_text.Text, {
                  text: rewardDetails == null ? undefined : rewardDetails.cardNo,
                  style: _changiEcardModal.styles.cardNoText
                }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                  onPress: onPressCopy,
                  testID: `TouchableCopy`,
                  accessibilityLabel: `TouchableCopy`,
                  children: (0, _jsxRuntime.jsx)(_icons.Copy, {
                    width: 20,
                    height: 20,
                    style: _changiEcardModal.styles.copyStyle,
                    fill: _theme.color.palette.whiteGrey
                  })
                })]
              })]
            }), isMyRewardCardV2 ? (0, _jsxRuntime.jsx)(_crCardV.CRCardV2, {
              isMonarch: false,
              navigationRoute: navigationRoute,
              newNavigation: newNavigation,
              contentEconomy: contentEconomy,
              closeScreen: closeScreen,
              profilePayload: profilePayload,
              isGetRewardCardV2: isGetRewardCardV2
            }) : (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
              children: contentEconomy ? (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: _changiEcardModal.styles.wrapBannerRewardCatalog,
                children: (0, _jsxRuntime.jsx)(_changiRewardsECard.BannerRewardsCatalog, {
                  navigation: newNavigation,
                  contentBannerCard: contentEconomy,
                  closeWhenPress: function closeWhenPress() {
                    (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppRewardsCard, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppRewardsCard, contentEconomy == null ? undefined : contentEconomy.title));
                    closeScreen();
                  }
                })
              }) : (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: _changiEcardModal.styles.viewEmptyAEM
              })
            }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
              onPress: scanToPay,
              testID: `TouchableCopy`,
              accessibilityLabel: `TouchableCopy`,
              style: _changiEcardModal.styles.touchableScanToPay,
              children: [(0, _jsxRuntime.jsx)(_icons.ScanToPay, {}), (0, _jsxRuntime.jsx)(_text.Text, {
                tx: "changiRewardsEcard.scanToPay",
                style: _changiEcardModal.styles.textScanToPay
              })]
            })]
          })
        })
      })]
    });
  };
  var _default = exports.default = CRForEconomyType;
