  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.CrCardItemV2 = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _lottieReactNative = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _icons = _$$_REQUIRE(_dependencyMap[9]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _walletSaga = _$$_REQUIRE(_dependencyMap[11]);
  var _utils = _$$_REQUIRE(_dependencyMap[12]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var COMPONENT_NAME = "CrRewardCardV2";
  var width_screen = _reactNative2.Dimensions.get("window").width;
  var CrCardItemV2 = exports.CrCardItemV2 = _react.default.memo(function (props) {
    var item = props.item,
      index = props.index,
      onPressView = props.onPressView,
      profilePayload = props.profilePayload,
      isFullWidth = props.isFullWidth;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isLoadingClaim = _useState2[0],
      setIsLoadingClaim = _useState2[1];
    var _useState3 = (0, _react.useState)(item == null ? undefined : item.cta_button),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      status = _useState4[0],
      setStatus = _useState4[1];
    var onPressClaim = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* () {
        try {
          setIsLoadingClaim(true);
          var reqBody = {
            memberId: profilePayload == null ? undefined : profilePayload.id,
            cardNo: profilePayload == null ? undefined : profilePayload.cardNo,
            voucherTypeCode: item == null ? undefined : item.voucher_type_code,
            promoCodeList: item == null ? undefined : item.remarks,
            quantity: item == null ? undefined : item.quantity,
            eligibleOutlet: item == null ? undefined : item.eligible_outlet,
            redemptionMode: item == null ? undefined : item.redemption_mode
          };
          var response = yield (0, _walletSaga.redeemYourReward)(reqBody);
          setIsLoadingClaim(false);
          if ((0, _utils.ifOneTrue)([!response, (response == null ? undefined : response.status) === false, (response == null ? undefined : response.return_status) === 0])) {
            setStatus(item == null ? undefined : item.cta_button);
          } else if ((0, _utils.ifOneTrue)([(response == null ? undefined : response.status) === true, (response == null ? undefined : response.return_status) === 1])) {
            setStatus("View");
          } else {
            setStatus(item == null ? undefined : item.cta_button);
          }
        } catch (error) {
          setStatus(item == null ? undefined : item.cta_button);
          setIsLoadingClaim(false);
        }
      });
      return function onPressClaim() {
        return _ref.apply(this, arguments);
      };
    }();
    var renderButtonStatus = function renderButtonStatus() {
      if (isLoadingClaim) {
        return (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.viewLoading,
          children: (0, _jsxRuntime.jsx)(_lottieReactNative.default, {
            source: _$$_REQUIRE(_dependencyMap[14]),
            autoPlay: true,
            loop: true,
            style: styles.lottieImage
          })
        });
      } else {
        if (status === "Claim") {
          return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: styles.buttonClaim,
            onPress: onPressClaim,
            accessibilityLabel: `${COMPONENT_NAME}__ButtonClaim`,
            testID: `${COMPONENT_NAME}__ButtonClaim`,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.txtClaim,
              children: "Claim"
            })
          });
        } else {
          return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
            style: styles.buttonViewDetail,
            onPress: onPressView,
            accessibilityLabel: `${COMPONENT_NAME}__ButtonView`,
            testID: `${COMPONENT_NAME}__ButtonView`,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.txtClaim,
              children: "View"
            }), (0, _jsxRuntime.jsx)(_icons.AccordionRight, {
              width: 20,
              height: 20
            })]
          });
        }
      }
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      style: [styles.container, {
        marginLeft: index === 0 ? 16 : 0,
        width: isFullWidth ? width_screen - 32 : 311
      }],
      activeOpacity: 1,
      children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
        style: styles.image,
        source: {
          uri: item == null ? undefined : item.voucher_image
        },
        resizeMode: "cover",
        accessibilityLabel: `${COMPONENT_NAME}__Icon`,
        testID: `${COMPONENT_NAME}__Icon`
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.viewContent,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          numberOfLines: 3,
          style: styles.txtContent,
          accessibilityLabel: `${COMPONENT_NAME}__voucher_title`,
          testID: `${COMPONENT_NAME}__voucher_title`,
          children: item == null ? undefined : item.voucher_title
        })
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.viewStatus,
        children: renderButtonStatus()
      })]
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    container: {
      width: 311,
      height: 84,
      borderRadius: 12,
      backgroundColor: _theme.color.palette.whiteGrey,
      padding: 12,
      flexDirection: "row",
      alignItems: "center"
    },
    image: {
      width: 68,
      height: 45
    },
    viewContent: {
      flex: 1,
      marginLeft: 12
    },
    viewStatus: {
      paddingLeft: 12
    },
    viewLoading: {
      width: 64,
      height: 28,
      borderWidth: 1,
      borderColor: _theme.color.palette.greyCCCCCC,
      borderRadius: 60,
      justifyContent: "center",
      alignItems: "center"
    },
    lottieImage: {
      width: 56,
      height: 20
    },
    buttonClaim: {
      paddingHorizontal: 12,
      paddingVertical: 5,
      borderWidth: 1,
      borderColor: _theme.color.palette.purpleD5BBEA,
      borderRadius: 60
    },
    txtClaim: {
      color: _theme.color.palette.lightPurple,
      fontSize: 14,
      fontFamily: _theme.typography.bold,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 18
    },
    txtContent: {
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 16,
      fontFamily: _theme.typography.bold,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 20
    },
    buttonViewDetail: {
      flexDirection: "row",
      alignItems: "center",
      gap: 4
    }
  });
