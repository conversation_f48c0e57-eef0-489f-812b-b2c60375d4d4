  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _lodash = _$$_REQUIRE(_dependencyMap[6]);
  var _backgrounds = _$$_REQUIRE(_dependencyMap[7]);
  var _changiEcardModal = _$$_REQUIRE(_dependencyMap[8]);
  var _icons = _$$_REQUIRE(_dependencyMap[9]);
  var _reactNativeQrcodeSvg = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _baseImageBackground = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _utils = _$$_REQUIRE(_dependencyMap[13]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[14]);
  var _staffPerkPromotionDetailController = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[16]);
  var _account = _$$_REQUIRE(_dependencyMap[17]);
  var _crCardV = _$$_REQUIRE(_dependencyMap[18]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[19]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var CRForMonarchType = function CRForMonarchType(_ref) {
    var _contentBannerCard$da;
    var selectedPresetTier = _ref.selectedPresetTier,
      closeScreen = _ref.closeScreen,
      profilePayload = _ref.profilePayload,
      rewardDetails = _ref.rewardDetails,
      onPressCopy = _ref.onPressCopy,
      scanToPay = _ref.scanToPay,
      newNavigation = _ref.newNavigation,
      contentBannerCard = _ref.contentBannerCard,
      navigationRoute = _ref.navigationRoute,
      isGetRewardCardV2 = _ref.isGetRewardCardV2;
    var _useContext = (0, _react.useContext)(_account.AccountContext),
      myRewardCardV2 = _useContext.myRewardCardV2;
    var isMyRewardCardV2 = (0, _remoteConfig.getFeatureFlagInit)(_remoteConfig.REMOTE_CONFIG_FLAGS.FLIGHT_PAID_INSURANCE, myRewardCardV2);
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("customNavigation", newNavigation),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var contentMonarchConcierge = contentBannerCard == null || (_contentBannerCard$da = contentBannerCard.data) == null || (_contentBannerCard$da = _contentBannerCard$da.list) == null ? undefined : _contentBannerCard$da[0];
    var boxShadowColor = {
      shadowColor: selectedPresetTier.linearGradientColor,
      shadowOffset: {
        width: 0,
        height: 2
      },
      shadowOpacity: 0.23,
      shadowRadius: 2.62,
      elevation: 4
    };
    var handlePressConcierge = function handlePressConcierge() {
      closeScreen();
      var _handleCondition = (0, _utils.handleCondition)(!!(contentMonarchConcierge != null && contentMonarchConcierge.navigation), contentMonarchConcierge == null ? undefined : contentMonarchConcierge.navigation, ""),
        type = _handleCondition.type,
        value = _handleCondition.value;
      var _ref2 = contentMonarchConcierge || "",
        redirect = _ref2.redirect;
      if (!type || !value) return;
      _staffPerkPromotionDetailController.default.hideModal();
      handleNavigation(type, value, redirect);
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.ImageBackground, {
      source: _backgrounds.ImageBackgroundStaffMonarchCRCard,
      style: _changiEcardModal.styles.gradientMonarchStyle,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _changiEcardModal.styles.wrapMonarchHeader,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _changiEcardModal.styles.leftPart
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          tx: "changiRewardsEcard.header",
          preset: "subTitleBold",
          style: _changiEcardModal.styles.headerText
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _changiEcardModal.styles.rightPart,
          children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: closeScreen,
            children: (0, _jsxRuntime.jsx)(_icons.CrossWithBackground, {})
          })
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: {
          flex: 1
        },
        children: (0, _jsxRuntime.jsx)(_reactNative2.ScrollView, {
          showsVerticalScrollIndicator: false,
          contentContainerStyle: _changiEcardModal.styles.contentScroll,
          children: (0, _jsxRuntime.jsxs)(_reactNative2.SafeAreaView, {
            style: _changiEcardModal.styles.safeAreaViewStyle,
            children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              activeOpacity: 1,
              children: (0, _jsxRuntime.jsxs)(_baseImageBackground.default, {
                style: _changiEcardModal.styles.wrapBenzelSizeForMonarch,
                source: profilePayload != null && profilePayload.airportStaff ? _backgrounds.ChangiMonarchStaff : "",
                resizeMode: "stretch",
                children: [(0, _jsxRuntime.jsx)(_reactNative2.ImageBackground, {
                  source: _backgrounds.ImageWrapMonarchQRCodeCRCard,
                  style: [_changiEcardModal.styles.wrapQRSize, boxShadowColor],
                  children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
                    style: _changiEcardModal.styles.overlayHiddenCornerForMonarch,
                    children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
                      style: _changiEcardModal.styles.wrapQR,
                      children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
                        style: _changiEcardModal.styles.scaleQR,
                        children: !(0, _lodash.isEmpty)(rewardDetails == null ? undefined : rewardDetails.barCode) && (0, _jsxRuntime.jsx)(_reactNativeQrcodeSvg.default, {
                          value: rewardDetails == null ? undefined : rewardDetails.barCode,
                          size: 140
                        })
                      }, "scaleQR")
                    }, "wrapQR")
                  }, "overlayHiddenCornerForMonarch")
                }, "imageWrapMonarchQRCodeCRCard"), (profilePayload == null ? undefined : profilePayload.airportStaff) && (0, _jsxRuntime.jsx)(_baseImageBackground.default, {
                  source: _backgrounds.GradientTextStaff,
                  style: _changiEcardModal.styles.wrapStaffTextForMonarch,
                  children: (0, _jsxRuntime.jsx)(_text.Text, {
                    text: "STAFF",
                    style: _changiEcardModal.styles.textStaff
                  })
                })]
              }, "wrapBenzelSize")
            }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
              activeOpacity: 1,
              children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: _changiEcardModal.styles.wrapAccountName,
                children: (0, _jsxRuntime.jsx)(_text.Text, {
                  text: profilePayload == null ? undefined : profilePayload.firstName,
                  style: _changiEcardModal.styles.accountTextName,
                  numberOfLines: 1,
                  preset: "h1"
                })
              }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: _changiEcardModal.styles.wrapTierCodeText,
                children: [(0, _jsxRuntime.jsx)(_text.Text, {
                  text: rewardDetails == null ? undefined : rewardDetails.tierCode,
                  style: _changiEcardModal.styles.tierMonarchCodeText
                }), (0, _jsxRuntime.jsx)(_icons.MonarchTierIcon, {
                  width: 12,
                  height: 12
                })]
              }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: _changiEcardModal.styles.wrapCardNo,
                children: [(0, _jsxRuntime.jsx)(_text.Text, {
                  text: rewardDetails == null ? undefined : rewardDetails.cardNo,
                  style: _changiEcardModal.styles.cardNoText
                }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                  onPress: onPressCopy,
                  testID: `TouchableCopy`,
                  accessibilityLabel: `TouchableCopy`,
                  children: (0, _jsxRuntime.jsx)(_icons.CopyCRECard, {
                    width: 16,
                    height: 16,
                    style: _changiEcardModal.styles.copyStyle,
                    fill: _theme.color.palette.whiteGrey
                  })
                })]
              })]
            }), isMyRewardCardV2 ? (0, _jsxRuntime.jsx)(_crCardV.CRCardV2, {
              isMonarch: true,
              navigationRoute: navigationRoute,
              contentMonarchConcierge: contentMonarchConcierge,
              handlePressConcierge: handlePressConcierge,
              profilePayload: profilePayload,
              isGetRewardCardV2: isGetRewardCardV2,
              closeScreen: closeScreen
            }) : (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
              children: !(0, _lodash.isEmpty)(contentMonarchConcierge) ? (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
                style: _changiEcardModal.styles.touchableConciergeSection,
                activeOpacity: 0.5,
                onPress: handlePressConcierge,
                children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
                  source: {
                    uri: (0, _utils.mappingUrlAem)(contentMonarchConcierge == null ? undefined : contentMonarchConcierge.image)
                  },
                  style: _changiEcardModal.styles.imageConciergeSection
                }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                  style: _changiEcardModal.styles.wrapTextConciergeSection,
                  children: [(0, _jsxRuntime.jsx)(_text.Text, {
                    text: contentMonarchConcierge == null ? undefined : contentMonarchConcierge.title,
                    style: _changiEcardModal.styles.titleConciergeSection
                  }), (0, _jsxRuntime.jsx)(_text.Text, {
                    text: contentMonarchConcierge == null ? undefined : contentMonarchConcierge.subCopyText,
                    style: _changiEcardModal.styles.subTextConciergeSection
                  })]
                })]
              }) : (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: _changiEcardModal.styles.viewEmptyAEM
              })
            }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
              onPress: scanToPay,
              testID: `TouchableCopy`,
              accessibilityLabel: `TouchableCopy`,
              style: _changiEcardModal.styles.touchableScanToPayForMonarch,
              children: [(0, _jsxRuntime.jsx)(_icons.ScanToPay, {}), (0, _jsxRuntime.jsx)(_text.Text, {
                tx: "changiRewardsEcard.scanToPay",
                style: _changiEcardModal.styles.textScanToPay
              })]
            })]
          })
        })
      })]
    });
  };
  var _default = exports.default = CRForMonarchType;
