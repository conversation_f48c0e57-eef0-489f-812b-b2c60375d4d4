  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.CRCardV2 = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _crCardItemV = _$$_REQUIRE(_dependencyMap[7]);
  var _theme = _$$_REQUIRE(_dependencyMap[8]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _constants = _$$_REQUIRE(_dependencyMap[10]);
  var _validate = _$$_REQUIRE(_dependencyMap[11]);
  var _changiEcardModal = _$$_REQUIRE(_dependencyMap[12]);
  var _changiRewardsECard = _$$_REQUIRE(_dependencyMap[13]);
  var _adobe = _$$_REQUIRE(_dependencyMap[14]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _utils = _$$_REQUIRE(_dependencyMap[16]);
  var _text = _$$_REQUIRE(_dependencyMap[17]);
  var _lodash = _$$_REQUIRE(_dependencyMap[18]);
  var _envParams = _$$_REQUIRE(_dependencyMap[19]);
  var _apis = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[20]));
  var _request = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[21]));
  var _native = _$$_REQUIRE(_dependencyMap[22]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[23]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var LOADING_COLORS = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var width = _reactNative2.Dimensions.get("window").width;
  var CRCardV2 = exports.CRCardV2 = _react.default.memo(function (props) {
    var navigation = (0, _native.useNavigation)();
    var isMonarch = props.isMonarch,
      navigationRoute = props.navigationRoute,
      newNavigation = props.newNavigation,
      contentEconomy = props.contentEconomy,
      closeScreen = props.closeScreen,
      contentMonarchConcierge = props.contentMonarchConcierge,
      handlePressConcierge = props.handlePressConcierge,
      profilePayload = props.profilePayload,
      isGetRewardCardV2 = props.isGetRewardCardV2;
    var _useState = (0, _react.useState)([]),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      data = _useState2[0],
      setData = _useState2[1];
    var _useState3 = (0, _react.useState)(true),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isLoading = _useState4[0],
      setIsLoading = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isError = _useState6[0],
      setIsError = _useState6[1];
    var getData = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* () {
        setIsLoading(true);
        try {
          var _env, _env2, _response$data;
          var inputData = {
            card_no: profilePayload == null ? undefined : profilePayload.cardNo
          };
          var paramsArray = _apis.default.getRewardCards.split(" ");
          var url = ((_env = (0, _envParams.env)()) == null ? undefined : _env.API_GATEWAY_URL) + paramsArray[1];
          var response = yield (0, _request.default)({
            url: url,
            method: paramsArray[0],
            data: inputData,
            headers: {
              "x-api-key": (_env2 = (0, _envParams.env)()) == null ? undefined : _env2.X_API_KEY
            }
          });
          if ((response == null || (_response$data = response.data) == null || (_response$data = _response$data.list) == null ? undefined : _response$data.length) > 0) {
            var _response$data2;
            setData(response == null || (_response$data2 = response.data) == null ? undefined : _response$data2.list);
            setIsError(false);
          } else {
            setIsError(true);
          }
        } catch (error) {
          setIsError(true);
        } finally {
          setIsLoading(false);
        }
      });
      return function getData() {
        return _ref.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      if (isGetRewardCardV2) {
        getData();
      }
    }, [isGetRewardCardV2]);
    var onNavigateVoucherPrize = function onNavigateVoucherPrize() {
      navigation.navigate(_constants.NavigationConstants.vouchersPrizesRedemptionsScreen);
      closeScreen();
    };
    if (isLoading) {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: stylesContainer.viewLoading,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: stylesContainer.viewContentLoading,
          children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            navigationRoute: navigationRoute,
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: LOADING_COLORS,
            shimmerStyle: stylesContainer.imageLoading
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: {
              flex: 1
            },
            children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              navigationRoute: navigationRoute,
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: LOADING_COLORS,
              shimmerStyle: stylesContainer.titleLoading
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              navigationRoute: navigationRoute,
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: LOADING_COLORS,
              shimmerStyle: stylesContainer.contentLoading
            })]
          })]
        })
      });
    }
    if (isError) {
      if (isMonarch) {
        return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
          children: !(0, _lodash.isEmpty)(contentMonarchConcierge) ? (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
            style: _changiEcardModal.styles.touchableConciergeSection,
            activeOpacity: 0.5,
            onPress: handlePressConcierge,
            children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
              source: {
                uri: (0, _utils.mappingUrlAem)(contentMonarchConcierge == null ? undefined : contentMonarchConcierge.image)
              },
              style: _changiEcardModal.styles.imageConciergeSection
            }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _changiEcardModal.styles.wrapTextConciergeSection,
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                text: contentMonarchConcierge == null ? undefined : contentMonarchConcierge.title,
                style: _changiEcardModal.styles.titleConciergeSection
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                text: contentMonarchConcierge == null ? undefined : contentMonarchConcierge.subCopyText,
                style: _changiEcardModal.styles.subTextConciergeSection
              })]
            })]
          }) : (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: stylesContainer.viewEmptyAEM
          })
        });
      } else {
        return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
          children: contentEconomy ? (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _changiEcardModal.styles.wrapBannerRewardCatalog,
            children: (0, _jsxRuntime.jsx)(_changiRewardsECard.BannerRewardsCatalog, {
              navigation: newNavigation,
              contentBannerCard: contentEconomy,
              closeWhenPress: function closeWhenPress() {
                (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppRewardsCard, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppRewardsCard, contentEconomy == null ? undefined : contentEconomy.title));
                closeScreen();
              }
            })
          }) : (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: stylesContainer.viewEmptyAEM
          })
        });
      }
    }
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: stylesContainer.container,
      children: (0, _jsxRuntime.jsx)(_reactNative2.ScrollView, {
        horizontal: true,
        showsHorizontalScrollIndicator: false,
        children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: stylesContainer.viewContent,
          children: (0, _validate.isArray)(data) && (data == null ? undefined : data.map(function (item, index) {
            return (0, _jsxRuntime.jsx)(_crCardItemV.CrCardItemV2, {
              item: item,
              index: index,
              onPressView: onNavigateVoucherPrize,
              profilePayload: profilePayload,
              isFullWidth: (data == null ? undefined : data.length) === 1
            }, index);
          }))
        })
      })
    });
  });
  var stylesContainer = _reactNative2.StyleSheet.create({
    container: {
      width: "100%",
      height: 84,
      marginTop: 24,
      marginBottom: 40
    },
    viewLoading: {
      width: "100%",
      height: 84,
      marginTop: 24,
      marginBottom: 40,
      flexDirection: "row"
    },
    viewContent: {
      flexDirection: "row",
      gap: 12
    },
    viewContentLoading: {
      width: width - 32,
      height: 84,
      borderRadius: 12,
      backgroundColor: _theme.color.palette.whiteGrey,
      marginLeft: 16,
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: 12
    },
    imageLoading: {
      width: 68,
      height: 45,
      marginRight: 12
    },
    titleLoading: {
      width: 74,
      height: 12,
      borderRadius: 4
    },
    contentLoading: {
      width: "100%",
      height: 12,
      borderRadius: 4,
      marginTop: 4
    },
    viewEmptyAEM: {
      height: 24
    }
  });
