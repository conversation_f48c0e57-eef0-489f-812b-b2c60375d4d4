  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _bottomSheetError = _$$_REQUIRE(_dependencyMap[0]);
  var _systemRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[2]);
  var _i18n = _$$_REQUIRE(_dependencyMap[3]);
  var _icons = _$$_REQUIRE(_dependencyMap[4]);
  var _react = _$$_REQUIRE(_dependencyMap[5]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var CommonErrorBottomSheet = function CommonErrorBottomSheet() {
    var dispatch = (0, _reactRedux.useDispatch)();
    var bottomSheetErrorData = (0, _reactRedux.useSelector)(_systemRedux.SystemSelectors.bottomSheetErrorData);
    return (0, _jsxRuntime.jsx)(_bottomSheetError.BottomSheetError, {
      icon: (0, _jsxRuntime.jsx)(_icons.InfoRed, {}),
      visible: Boolean(bottomSheetErrorData == null ? undefined : bottomSheetErrorData.visible),
      title: (0, _i18n.translate)("popupError.somethingWrongOneline"),
      errorMessage: (0, _i18n.translate)("popupError.networkErrorMessage"),
      onClose: function onClose() {
        dispatch(_systemRedux.default.resetBottomSheetErrorData());
      },
      onButtonPressed: function onButtonPressed() {
        dispatch(_systemRedux.default.resetBottomSheetErrorData());
      },
      buttonText: (0, _i18n.translate)("subscription.close"),
      testID: `Root__BottomSheetErrorSomethingWrong__Default`,
      accessibilityLabel: `Root__BottomSheetErrorSomethingWrong__Default`,
      enableHtmlContent: true
    });
  };
  var _default = exports.default = (0, _react.memo)(CommonErrorBottomSheet);
