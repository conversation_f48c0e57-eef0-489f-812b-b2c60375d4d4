  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.MyTravelRemoveFlight = exports.MyTravelInsertFlight = exports.GetMyTravelFlightDetails = exports.GetIntoCityOrAirport = exports.FlySearch = exports.FlyList = exports.FlyLanding = exports.FlyDetailSearch = exports.FlyCodes = exports.FlightTimelineTiles = exports.FlightDetailsCRT = exports.FlightDetails = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _possibleConstructorReturn2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _getPrototypeOf2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _inherits2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _flightListingCard = _$$_REQUIRE(_dependencyMap[7]);
  var _flightDetailsCard = _$$_REQUIRE(_dependencyMap[8]);
  var _flightHeroImage = _$$_REQUIRE(_dependencyMap[9]);
  var _flightTimelineSectionProps = _$$_REQUIRE(_dependencyMap[10]);
  var _dateTime = _$$_REQUIRE(_dependencyMap[11]);
  var _file = _$$_REQUIRE(_dependencyMap[12]);
  var _data = _$$_REQUIRE(_dependencyMap[13]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _momentTimezone = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _flyBaseClass = _$$_REQUIRE(_dependencyMap[16]);
  var _lodash = _$$_REQUIRE(_dependencyMap[17]);
  var _cardWithLinks = _$$_REQUIRE(_dependencyMap[18]);
  var _sectionImage = _$$_REQUIRE(_dependencyMap[19]);
  var _timelineFlyTiles = _$$_REQUIRE(_dependencyMap[20]);
  var _savedFlightCard = _$$_REQUIRE(_dependencyMap[21]);
  var _benefitsCard = _$$_REQUIRE(_dependencyMap[22]);
  var _flightProps = _$$_REQUIRE(_dependencyMap[23]);
  var _utils = _$$_REQUIRE(_dependencyMap[24]);
  var _FlightDetails, _GetMyTravelFlightDetails;
  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
  var returnDateGroupSort = function returnDateGroupSort(groupArrays) {
    return groupArrays.sort(function (a, b) {
      return (0, _moment.default)(a.sortedDate, _dateTime.DateFormats.DayMonthYearWithSlash).diff((0, _moment.default)(b.sortedDate, _dateTime.DateFormats.DayMonthYearWithSlash));
    });
  };
  var convertDtoForFly = function convertDtoForFly(data, type, _flyCodes, isSaved, isMSError, isFirstFlight) {
    if (Object.keys(data).length > 0) {
      var _data$airline_details, _departingCodeIndex, _destinationCodeIndex, _destinationCodeIndex2, _departingCodeIndex2, _data$status_mapping, _data$status_mapping2, _data$status_mapping3, _data$status_mapping4, _data$airport_details5, _data$status_mapping5;
      var departingCodeIndex = {
        code: "",
        name: ""
      };
      var destinationCodeIndex = {
        code: "",
        name: ""
      };
      if ((data == null ? undefined : data.direction) === "ARR") {
        var _data$airport_details, _data$airport_details2;
        departingCodeIndex = {
          code: data == null || (_data$airport_details = data.airport_details) == null ? undefined : _data$airport_details.code,
          name: data == null || (_data$airport_details2 = data.airport_details) == null ? undefined : _data$airport_details2.name
        };
        destinationCodeIndex = {
          code: "SIN",
          name: "Singapore"
        };
      } else if ((data == null ? undefined : data.direction) === "DEP") {
        var _data$airport_details3, _data$airport_details4;
        destinationCodeIndex = {
          code: data == null || (_data$airport_details3 = data.airport_details) == null ? undefined : _data$airport_details3.code,
          name: data == null || (_data$airport_details4 = data.airport_details) == null ? undefined : _data$airport_details4.name
        };
        departingCodeIndex = {
          code: "SIN",
          name: "Singapore"
        };
      }
      return {
        actualTimestamp: data == null ? undefined : data.actual_timestamp,
        estimatedTimestamp: data == null ? undefined : data.estimated_timestamp,
        logo: data == null || (_data$airline_details = data.airline_details) == null ? undefined : _data$airline_details.logo_url,
        flightNumber: data.flight_number,
        departingCode: (_departingCodeIndex = departingCodeIndex) == null ? undefined : _departingCodeIndex.code,
        destinationCode: (_destinationCodeIndex = destinationCodeIndex) == null ? undefined : _destinationCodeIndex.code,
        flightDate: data.scheduled_date,
        scheduledDate: data.scheduled_date,
        state: type,
        codeShare: data.slave_flights,
        destinationPlace: (_destinationCodeIndex2 = destinationCodeIndex) == null ? undefined : _destinationCodeIndex2.name,
        departingPlace: (_departingCodeIndex2 = departingCodeIndex) == null ? undefined : _departingCodeIndex2.name,
        timeOfFlight: data.scheduled_time,
        flightStatus: data.flight_status === "hide" ? "" : data.flight_status,
        isSaved: isSaved,
        isMSError: isMSError,
        transits: undefined,
        flightStatusMapping: (_data$status_mapping = data.status_mapping) == null ? undefined : _data$status_mapping.listing_status_en,
        beltStatusMapping: data == null || (_data$status_mapping2 = data.status_mapping) == null ? undefined : _data$status_mapping2.belt_status_en,
        statusColor: (_data$status_mapping3 = data.status_mapping) == null ? undefined : _data$status_mapping3.status_text_color,
        showGate: (_data$status_mapping4 = data.status_mapping) == null ? undefined : _data$status_mapping4.show_gate,
        direction: data == null ? undefined : data.direction,
        flightUniqueId: `${data.flight_number}_${data.scheduled_date}`,
        terminal: data == null ? undefined : data.terminal,
        checkInRow: data == null ? undefined : data.check_in_row,
        displayBelt: data == null ? undefined : data.display_belt,
        displayTimestamp: data == null ? undefined : data.display_timestamp_mapping,
        viaAirportDetails: data == null ? undefined : data.via_airport_details,
        country: data == null || (_data$airport_details5 = data.airport_details) == null ? undefined : _data$airport_details5.country,
        isFirstFlight: isFirstFlight,
        upcomingStatusMapping: (_data$status_mapping5 = data.status_mapping) == null ? undefined : _data$status_mapping5.details_status_en
      };
    }
    return {
      actualTimestamp: undefined,
      estimatedTimestamp: undefined,
      state: type,
      logo: undefined,
      flightNumber: undefined,
      flightStatus: undefined,
      codeShare: undefined,
      departingCode: undefined,
      departingPlace: undefined,
      destinationCode: undefined,
      destinationPlace: undefined,
      timeOfFlight: undefined,
      transits: undefined,
      onPressed: undefined,
      onSaved: undefined,
      isSaved: undefined,
      flightDate: undefined,
      scheduledDate: undefined,
      flightStatusMapping: undefined,
      beltStatusMapping: undefined,
      statusColor: undefined,
      showGate: undefined,
      direction: undefined,
      flightUniqueId: undefined,
      terminal: undefined,
      checkInRow: undefined,
      displayBelt: undefined,
      displayTimestamp: undefined,
      viaAirportDetails: undefined,
      country: undefined
    };
  };
  var enumerateDaysBetweenDates = function enumerateDaysBetweenDates(startDate, endDate) {
    var date = [];
    while ((0, _moment.default)(startDate) <= (0, _moment.default)(endDate)) {
      date.push(startDate);
      startDate = (0, _moment.default)(startDate).add(1, "days").format("YYYY-MM-DD");
    }
    return date;
  };
  var FlyLanding = exports.FlyLanding = /*#__PURE__*/function (_FlyBaseClass) {
    function FlyLanding(data, errorFlag, errorPayload, nextToken, flightRequestType, type, previousToken) {
      var _this;
      (0, _classCallCheck2.default)(this, FlyLanding);
      _this = _callSuper(this, FlyLanding);
      _this.data = data;
      _this.errorFlag = errorFlag;
      _this.errorPayload = errorPayload;
      _this.nextToken = nextToken;
      _this.flightRequestType = flightRequestType || _flightProps.FlightRequestType.FlightDefault;
      _this.type = type;
      _this.previousToken = previousToken;
      return _this;
    }
    (0, _inherits2.default)(FlyLanding, _FlyBaseClass);
    return (0, _createClass2.default)(FlyLanding, [{
      key: "failure",
      value: function failure(data) {
        var flightRequestType = data.flightRequestType;
        var payload = [{
          date: "",
          flightLanding: (0, _toConsumableArray2.default)(Array.from(Array(5)).map(function () {
            return FlyLanding.convertDto({}, _flightListingCard.FlightListingState.loading);
          }))
        }];
        return new FlyLanding(payload, true, [], null, flightRequestType, _flightListingCard.FlightListingState.loading);
      }
    }, {
      key: "request",
      value: function request() {
        var payload = [{
          date: "",
          flightLanding: (0, _toConsumableArray2.default)(Array.from(Array(5)).map(function () {
            return FlyLanding.convertDto({}, _flightListingCard.FlightListingState.loading);
          }))
        }];
        return new FlyLanding(payload, false, [], null, null, _flightListingCard.FlightListingState.loading);
      }
    }, {
      key: "success",
      value: function success(action, flyCodes, savedFlights) {
        var _action$payload = action.payload,
          data = _action$payload.data,
          errors = _action$payload.errors;
        if ((data == null ? undefined : data.getFlights) === null && (errors == null ? undefined : errors.length) > 0) {
          var extractErrors = FlyLanding.checkErrors("getFlights", errors);
          var payload = [{
            date: "",
            flightLanding: (0, _toConsumableArray2.default)(Array.from(Array(5)).map(function () {
              return FlyLanding.convertDto({}, _flightListingCard.FlightListingState.loading);
            }))
          }];
          return new FlyLanding(payload, extractErrors[0], extractErrors[1], null);
        } else {
          var _data$getFlights, _data$getFlights2, _data$getFlights3;
          var today = (0, _moment.default)().format(_dateTime.DateFormats.YearMonthDay);
          var tomorrow = (0, _moment.default)(today).locale(_dateTime.Locales.en).add(1, "day").format(_dateTime.DateFormats.YearMonthDay);
          var dataArray = [];
          dataArray = (0, _toConsumableArray2.default)(((data == null || (_data$getFlights = data.getFlights) == null ? undefined : _data$getFlights.flights) || []).map(function (item, index) {
            var _savedFlights$getMyTr;
            var indexSaveFlight = savedFlights == null || (_savedFlights$getMyTr = savedFlights.getMyTravelFlightDetails) == null ? undefined : _savedFlights$getMyTr.findIndex(function (flight) {
              return `${flight == null ? undefined : flight.flightNumber}_${flight == null ? undefined : flight.scheduledDate}` === `${item == null ? undefined : item.flight_number}_${item == null ? undefined : item.scheduled_date}`;
            });
            var isFirstFlight = index === 0;
            return FlyLanding.convertDto(item, _flightListingCard.FlightListingState.default, flyCodes, isNaN(indexSaveFlight) ? false : indexSaveFlight !== -1, savedFlights == null ? undefined : savedFlights.errorFlag, isFirstFlight);
          }));
          var _payload = [];
          if ((action == null ? undefined : action.featureFlag) === 'v1') {
            _payload = FlyLanding.dateGroupV1(dataArray, today, tomorrow);
          } else {
            _payload = FlyLanding.dateGroup(dataArray, today, tomorrow);
          }
          return new FlyLanding(_payload, false, [], data == null || (_data$getFlights2 = data.getFlights) == null ? undefined : _data$getFlights2.next_token, null, _flightListingCard.FlightListingState.default, data == null || (_data$getFlights3 = data.getFlights) == null ? undefined : _data$getFlights3.previous_token);
        }
      }
    }]);
  }(_flyBaseClass.FlyBaseClass);
  FlyLanding.dateGroupV1 = function (data, today, _tomorrow) {
    var _Object$keys;
    var groups = data.reduce(function (groupsummary, game) {
      var _game$flightDate;
      var date = (_game$flightDate = game.flightDate) == null ? undefined : _game$flightDate.split("T")[0];
      if (!groupsummary[date]) {
        groupsummary[date] = [];
      }
      groupsummary[date].push(game);
      return groupsummary;
    }, {});
    if (!data.length && !((_Object$keys = Object.keys(groups)) != null && _Object$keys.includes(today))) {
      groups[today] = [];
    }
    var groupArrays = Object.keys(groups).map(function (date) {
      return {
        date: (0, _dateTime.flyModuleDateFormatting)((0, _moment.default)(date)),
        flightLanding: groups[date],
        sortedDate: (0, _moment.default)(date).format(_dateTime.DateFormats.DayMonthYearWithSlash)
      };
    });
    return returnDateGroupSort(groupArrays);
  };
  FlyLanding.dateGroup = function (data, today, _tomorrow) {
    var _Object$keys2;
    var groups = data.reduce(function (groupSummary, game) {
      var _game$flightDate2;
      var date = (_game$flightDate2 = game.flightDate) == null ? undefined : _game$flightDate2.split("T")[0];
      if (!groupSummary[date]) {
        groupSummary[date] = [];
      }
      groupSummary[date].push(game);
      return groupSummary;
    }, {});
    if (!((_Object$keys2 = Object.keys(groups)) != null && _Object$keys2.includes(today))) {
      groups[today] = [];
    }
    var listMomentDateGroup = Object.keys(groups).map(function (d) {
      return (0, _moment.default)(d);
    });
    var minDate = _moment.default.min(listMomentDateGroup).format(_dateTime.DateFormats.YearMonthDay);
    var maxDate = _moment.default.max(listMomentDateGroup).format(_dateTime.DateFormats.YearMonthDay);
    enumerateDaysBetweenDates(minDate, maxDate).forEach(function (date) {
      if (!groups[date]) {
        groups[date] = [];
      }
    });
    var groupArrays = Object.keys(groups).map(function (date) {
      return {
        date: (0, _dateTime.flyModuleDateFormatting)((0, _moment.default)(date)),
        flightLanding: groups[date],
        sortedDate: (0, _moment.default)(date).format(_dateTime.DateFormats.DayMonthYearWithSlash),
        currentDate: (0, _moment.default)(date).format(_dateTime.DateFormats.YearMonthDay)
      };
    });
    return returnDateGroupSort(groupArrays);
  };
  FlyLanding.convertDto = function (data, type, _flyCodes, isSaved, isMSError, isFirstFlight) {
    if (Object.keys(data).length > 0) {
      var _data$airline_details2, _departingCodeIndex3, _destinationCodeIndex3, _destinationCodeIndex4, _departingCodeIndex4, _data$status_mapping6, _data$status_mapping7, _data$status_mapping8, _data$status_mapping9, _data$airport_details0, _data$status_mapping0;
      var departingCodeIndex = {
        code: "",
        name: ""
      };
      var destinationCodeIndex = {
        code: "",
        name: ""
      };
      if ((data == null ? undefined : data.direction) === "ARR") {
        var _data$airport_details6, _data$airport_details7;
        departingCodeIndex = {
          code: data == null || (_data$airport_details6 = data.airport_details) == null ? undefined : _data$airport_details6.code,
          name: data == null || (_data$airport_details7 = data.airport_details) == null ? undefined : _data$airport_details7.name
        };
        destinationCodeIndex = {
          code: "SIN",
          name: "Singapore"
        };
      } else if ((data == null ? undefined : data.direction) === "DEP") {
        var _data$airport_details8, _data$airport_details9;
        destinationCodeIndex = {
          code: data == null || (_data$airport_details8 = data.airport_details) == null ? undefined : _data$airport_details8.code,
          name: data == null || (_data$airport_details9 = data.airport_details) == null ? undefined : _data$airport_details9.name
        };
        departingCodeIndex = {
          code: "SIN",
          name: "Singapore"
        };
      }
      var newObject = {
        logo: ((_data$airline_details2 = data.airline_details) == null ? undefined : _data$airline_details2.logo_url) || `https://d15qtai6ju84a6.cloudfront.net/airlines/${data.airline}.gif`,
        flightNumber: data.flight_number,
        departingCode: (_departingCodeIndex3 = departingCodeIndex) == null ? undefined : _departingCodeIndex3.code,
        destinationCode: (_destinationCodeIndex3 = destinationCodeIndex) == null ? undefined : _destinationCodeIndex3.code,
        flightDate: data.scheduled_date,
        scheduledDate: data.scheduled_date,
        state: type,
        codeShare: data.slave_flights,
        destinationPlace: (_destinationCodeIndex4 = destinationCodeIndex) == null ? undefined : _destinationCodeIndex4.name,
        departingPlace: (_departingCodeIndex4 = departingCodeIndex) == null ? undefined : _departingCodeIndex4.name,
        timeOfFlight: data.scheduled_time,
        flightStatus: data.flight_status === "hide" ? "" : data.flight_status,
        flightStatusMapping: (_data$status_mapping6 = data.status_mapping) == null ? undefined : _data$status_mapping6.listing_status_en,
        beltStatusMapping: (_data$status_mapping7 = data.status_mapping) == null ? undefined : _data$status_mapping7.belt_status_en,
        statusColor: (_data$status_mapping8 = data.status_mapping) == null ? undefined : _data$status_mapping8.status_text_color,
        showGate: (_data$status_mapping9 = data.status_mapping) == null ? undefined : _data$status_mapping9.show_gate,
        isSaved: isSaved,
        isMSError: isMSError,
        transits: undefined,
        flightUniqueId: `${data.flight_number}_${data.scheduled_date}`,
        estimatedTimestamp: data == null ? undefined : data.estimated_timestamp,
        actualTimestamp: data == null ? undefined : data.estimated_timestamp,
        direction: data == null ? undefined : data.direction,
        terminal: data == null ? undefined : data.terminal,
        checkInRow: data == null ? undefined : data.check_in_row,
        displayBelt: data == null ? undefined : data.display_belt,
        displayTimestamp: data == null ? undefined : data.display_timestamp_mapping,
        viaAirportDetails: data == null ? undefined : data.via_airport_details,
        country: data == null || (_data$airport_details0 = data.airport_details) == null ? undefined : _data$airport_details0.country,
        isFirstFlight: isFirstFlight,
        upcomingStatusMapping: (_data$status_mapping0 = data.status_mapping) == null ? undefined : _data$status_mapping0.details_status_en,
        technicalFlightStatus1: data == null ? undefined : data.technical_flight_status1,
        technicalFlightStatus2: data == null ? undefined : data.technical_flight_status2,
        displayGate: data == null ? undefined : data.display_gate
      };
      return newObject;
    } else {
      return {
        state: type,
        logo: undefined,
        flightNumber: undefined,
        flightStatus: undefined,
        flightStatusMapping: undefined,
        beltStatusMapping: undefined,
        codeShare: undefined,
        departingCode: undefined,
        departingPlace: undefined,
        destinationCode: undefined,
        destinationPlace: undefined,
        timeOfFlight: undefined,
        transits: undefined,
        onPressed: undefined,
        onSaved: undefined,
        isSaved: undefined,
        flightDate: undefined,
        scheduledDate: undefined,
        flightUniqueId: undefined,
        estimatedTimestamp: undefined,
        actualTimestamp: undefined,
        statusColor: undefined,
        showGate: undefined,
        direction: undefined,
        terminal: undefined,
        checkInRow: undefined,
        displayBelt: undefined,
        displayTimestamp: undefined,
        viaAirportDetails: undefined,
        country: undefined,
        isFirstFlight: undefined
      };
    }
  };
  var FlyList = exports.FlyList = /*#__PURE__*/function (_FlyBaseClass2) {
    function FlyList(data, errorFlag, errorPayload, nextToken, previousToken, flightRequestType, type) {
      var _this2;
      (0, _classCallCheck2.default)(this, FlyList);
      _this2 = _callSuper(this, FlyList);
      _this2.data = data;
      _this2.errorFlag = errorFlag;
      _this2.errorPayload = errorPayload;
      _this2.nextToken = nextToken;
      _this2.previousToken = previousToken;
      _this2.flightRequestType = flightRequestType || _flightProps.FlightRequestType.FlightDefault;
      _this2.type = type;
      return _this2;
    }
    (0, _inherits2.default)(FlyList, _FlyBaseClass2);
    return (0, _createClass2.default)(FlyList, [{
      key: "request",
      value: function request() {
        var payload = [{
          date: "",
          flightListingData: []
        }];
        return new FlyList(payload, false);
      }
    }, {
      key: "failure",
      value: function failure(data) {
        var flightRequestType = data.flightRequestType;
        return new FlyList([], true, undefined, undefined, flightRequestType);
      }
    }, {
      key: "success",
      value: function success(action, flyCodes, savedFlights) {
        var _action$payload2 = action.payload,
          data = _action$payload2.data,
          errors = _action$payload2.errors;
        if ((data == null ? undefined : data.getFlights) === null && (errors == null ? undefined : errors.length) > 0) {
          var extractErrors = FlyList.checkErrors("getFlights", errors);
          var payload = [{
            date: "",
            flightLanding: (0, _toConsumableArray2.default)(Array.from(Array(5)).map(function () {
              return FlyList.convertDto({}, _flightListingCard.FlightListingState.loading);
            }))
          }];
          return new FlyList(payload, extractErrors[0], extractErrors[1], null);
        } else {
          var _data$getFlights4;
          if (data != null && (_data$getFlights4 = data.getFlights) != null && (_data$getFlights4 = _data$getFlights4.flights) != null && _data$getFlights4.length) {
            var _data$getFlights5, _data$getFlights6, _data$getFlights7;
            var dataArray = [];
            dataArray = (0, _toConsumableArray2.default)(data == null || (_data$getFlights5 = data.getFlights) == null ? undefined : _data$getFlights5.flights.map(function (item) {
              var _savedFlights$getMyTr2;
              var index = savedFlights == null || (_savedFlights$getMyTr2 = savedFlights.getMyTravelFlightDetails) == null ? undefined : _savedFlights$getMyTr2.findIndex(function (flight) {
                return `${flight == null ? undefined : flight.flightNumber}_${flight == null ? undefined : flight.scheduledDate}` === `${item == null ? undefined : item.flight_number}_${item == null ? undefined : item.scheduled_date}`;
              });
              return FlyList.convertDto(item, _flightListingCard.FlightListingState.default, flyCodes, isNaN(index) ? false : index !== -1, savedFlights == null ? undefined : savedFlights.errorFlag);
            }));
            var _payload2 = FlyList.dateGroup(dataArray);
            return new FlyList(_payload2, false, [], data == null || (_data$getFlights6 = data.getFlights) == null ? undefined : _data$getFlights6.next_token, data == null || (_data$getFlights7 = data.getFlights) == null ? undefined : _data$getFlights7.previous_token);
          } else {
            return new FlyList([], false, [], null);
          }
        }
      }
    }]);
  }(_flyBaseClass.FlyBaseClass);
  FlyList.convertDto = function (data, type, _flyCodes, isSaved, isMSError, isFirstFlight) {
    if (Object.keys(data).length > 0) {
      var _data$airline_details3, _departingCodeIndex5, _destinationCodeIndex5, _destinationCodeIndex6, _departingCodeIndex6, _data$status_mapping1, _data$status_mapping10, _data$status_mapping11, _data$status_mapping12, _data$airport_details13, _data$status_mapping13;
      var departingCodeIndex = {
        code: "",
        name: ""
      };
      var destinationCodeIndex = {
        code: "",
        name: ""
      };
      if ((data == null ? undefined : data.direction) === "ARR") {
        var _data$airport_details1, _data$airport_details10;
        departingCodeIndex = {
          code: data == null || (_data$airport_details1 = data.airport_details) == null ? undefined : _data$airport_details1.code,
          name: data == null || (_data$airport_details10 = data.airport_details) == null ? undefined : _data$airport_details10.name
        };
        destinationCodeIndex = {
          code: "SIN",
          name: "Singapore"
        };
      } else if ((data == null ? undefined : data.direction) === "DEP") {
        var _data$airport_details11, _data$airport_details12;
        destinationCodeIndex = {
          code: data == null || (_data$airport_details11 = data.airport_details) == null ? undefined : _data$airport_details11.code,
          name: data == null || (_data$airport_details12 = data.airport_details) == null ? undefined : _data$airport_details12.name
        };
        departingCodeIndex = {
          code: "SIN",
          name: "Singapore"
        };
      }
      return {
        actualTimestamp: data == null ? undefined : data.actual_timestamp,
        estimatedTimestamp: data == null ? undefined : data.estimated_timestamp,
        logo: data == null || (_data$airline_details3 = data.airline_details) == null ? undefined : _data$airline_details3.logo_url,
        flightNumber: data.flight_number,
        departingCode: (_departingCodeIndex5 = departingCodeIndex) == null ? undefined : _departingCodeIndex5.code,
        destinationCode: (_destinationCodeIndex5 = destinationCodeIndex) == null ? undefined : _destinationCodeIndex5.code,
        flightDate: data.scheduled_date,
        scheduledDate: data.scheduled_date,
        state: type,
        codeShare: data.slave_flights,
        destinationPlace: (_destinationCodeIndex6 = destinationCodeIndex) == null ? undefined : _destinationCodeIndex6.name,
        departingPlace: (_departingCodeIndex6 = departingCodeIndex) == null ? undefined : _departingCodeIndex6.name,
        timeOfFlight: data.scheduled_time,
        flightStatus: data.flight_status === "hide" ? "" : data.flight_status,
        isSaved: isSaved,
        isMSError: isMSError,
        transits: undefined,
        flightStatusMapping: (_data$status_mapping1 = data.status_mapping) == null ? undefined : _data$status_mapping1.listing_status_en,
        beltStatusMapping: (_data$status_mapping10 = data.status_mapping) == null ? undefined : _data$status_mapping10.belt_status_en,
        statusColor: (_data$status_mapping11 = data.status_mapping) == null ? undefined : _data$status_mapping11.status_text_color,
        showGate: (_data$status_mapping12 = data.status_mapping) == null ? undefined : _data$status_mapping12.show_gate,
        flightUniqueId: `${data.flight_number}_${data.scheduled_date}`,
        direction: data == null ? undefined : data.direction,
        terminal: data == null ? undefined : data.terminal,
        checkInRow: data == null ? undefined : data.check_in_row,
        displayBelt: data == null ? undefined : data.display_belt,
        displayTimestamp: (data == null ? undefined : data.display_timestamp_mapping) || (data == null ? undefined : data.display_timestamp),
        viaAirportDetails: data == null ? undefined : data.via_airport_details,
        country: data == null || (_data$airport_details13 = data.airport_details) == null ? undefined : _data$airport_details13.country,
        isFirstFlight: isFirstFlight,
        upcomingStatusMapping: (_data$status_mapping13 = data.status_mapping) == null ? undefined : _data$status_mapping13.details_status_en,
        displayGate: data == null ? undefined : data.display_gate,
        airportDetails: data == null ? undefined : data.airport_details
      };
    }
    return {
      actualTimestamp: undefined,
      estimatedTimestamp: undefined,
      state: type,
      logo: undefined,
      flightNumber: undefined,
      flightStatus: undefined,
      codeShare: undefined,
      departingCode: undefined,
      departingPlace: undefined,
      destinationCode: undefined,
      destinationPlace: undefined,
      timeOfFlight: undefined,
      transits: undefined,
      onPressed: undefined,
      onSaved: undefined,
      isSaved: undefined,
      flightDate: undefined,
      scheduledDate: undefined,
      flightStatusMapping: undefined,
      beltStatusMapping: undefined,
      statusColor: undefined,
      showGate: undefined,
      flightUniqueId: undefined,
      terminal: undefined,
      checkInRow: undefined,
      displayBelt: undefined,
      displayTimestamp: undefined,
      viaAirportDetails: undefined,
      country: undefined,
      isFirstFlight: undefined,
      displayGate: undefined,
      airportDetails: undefined
    };
  };
  FlyList.dateGroup = function (data) {
    var groups = data.reduce(function (groupsummary, game) {
      var date = game.flightDate.split("T")[0];
      if (!groupsummary[date]) {
        groupsummary[date] = [];
      }
      groupsummary[date].push(game);
      return groupsummary;
    }, {});
    var groupArrays = Object.keys(groups).map(function (date) {
      return {
        date: (0, _dateTime.flyModuleDateFormatting)((0, _moment.default)(date), _dateTime.DateFormats.DateWithDayMonthYear),
        flightListingData: groups[date],
        sortedDate: (0, _moment.default)(date).format(_dateTime.DateFormats.YearMonthDay),
        currentDate: (0, _moment.default)(date).format(_dateTime.DateFormats.YearMonthDay)
      };
    });
    return returnDateGroupSort(groupArrays);
  };
  var FlyDetailSearch = exports.FlyDetailSearch = /*#__PURE__*/function (_FlyBaseClass3) {
    function FlyDetailSearch(data, loading, query, errorFlag, errorPayload, errorPaging, shouldNavigateToDepartureTab) {
      var _this3;
      (0, _classCallCheck2.default)(this, FlyDetailSearch);
      _this3 = _callSuper(this, FlyDetailSearch);
      _this3.data = data;
      _this3.loading = loading;
      _this3.query = query;
      _this3.errorFlag = errorFlag;
      _this3.errorPayload = errorPayload;
      _this3.errorPaging = errorPaging;
      _this3.shouldNavigateToDepartureTab = shouldNavigateToDepartureTab;
      return _this3;
    }
    (0, _inherits2.default)(FlyDetailSearch, _FlyBaseClass3);
    return (0, _createClass2.default)(FlyDetailSearch, [{
      key: "request",
      value: function request(action) {
        var data = action.data;
        return new FlyDetailSearch((0, _toConsumableArray2.default)(data), true, {}, false, null, false);
      }
    }, {
      key: "failure",
      value: function failure(action) {
        var isPagingRequest = (0, _lodash.get)(action, "error.query.isPagingRequest");
        return new FlyDetailSearch([], false, {}, true, null, isPagingRequest);
      }
    }, {
      key: "reset",
      value: function reset() {
        return new FlyDetailSearch(null, false, {}, false, null, false);
      }
    }, {
      key: "success",
      value: function success(action, flyCodes, savedFlights) {
        var _action$payload3 = action.payload,
          data = _action$payload3.data,
          errors = _action$payload3.errors,
          _action$payload3$curr = _action$payload3.currentData,
          currentData = _action$payload3$curr === undefined ? [] : _action$payload3$curr,
          query = _action$payload3.query,
          shouldNavigateToDepartureTab = _action$payload3.shouldNavigateToDepartureTab;
        var items = (0, _lodash.get)(data, "flights.items", []);
        var total = (0, _lodash.get)(data, "flights.total", 0);
        var pageNumber = (0, _lodash.get)(query, "pageNumber", 1);
        var isPagingRequest = (0, _lodash.get)(query, "isPagingRequest");
        var pageSize = (0, _lodash.get)(query, "pageSize", 5);
        var hasLoadMore = total > pageSize * pageNumber;
        var payload = [];
        if ((errors == null ? undefined : errors.length) > 0) {
          var extractErrors = FlyDetailSearch.checkErrors("flights", errors);
          var flightData = isPagingRequest ? currentData : [];
          return new FlyDetailSearch(flightData, false, query, extractErrors[0], extractErrors[1], isPagingRequest);
        }
        if (items != null && items.length) {
          payload = items.map(function (item, index) {
            var _savedFlights$getMyTr3;
            var indexSaveFlight = savedFlights == null || (_savedFlights$getMyTr3 = savedFlights.getMyTravelFlightDetails) == null ? undefined : _savedFlights$getMyTr3.findIndex(function (flight) {
              return `${flight == null ? undefined : flight.flightNumber}_${flight == null ? undefined : flight.scheduledDate}` === `${item == null ? undefined : item.flight_number}_${item == null ? undefined : item.scheduled_date}` && (flight == null ? undefined : flight.direction) === (item == null ? undefined : item.direction);
            });
            var isFirstFlight = index === 0;
            return convertDtoForFly(item, _flightListingCard.FlightListingState.default, flyCodes, isNaN(indexSaveFlight) ? false : indexSaveFlight !== -1, savedFlights == null ? undefined : savedFlights.errorFlag, isFirstFlight);
          });
        }
        return new FlyDetailSearch(currentData.concat(payload), false, Object.assign({}, query, {
          total: total,
          hasLoadMore: hasLoadMore
        }), false, null, false, shouldNavigateToDepartureTab);
      }
    }]);
  }(_flyBaseClass.FlyBaseClass);
  var FlySearch = exports.FlySearch = /*#__PURE__*/function (_FlyBaseClass4) {
    function FlySearch(data, errorFlag, errorPayload) {
      var _this4;
      (0, _classCallCheck2.default)(this, FlySearch);
      _this4 = _callSuper(this, FlySearch);
      _this4.data = data;
      _this4.errorFlag = errorFlag;
      _this4.errorPayload = errorPayload;
      return _this4;
    }
    (0, _inherits2.default)(FlySearch, _FlyBaseClass4);
    return (0, _createClass2.default)(FlySearch, [{
      key: "request",
      value: function request() {
        return new FlySearch({}, false);
      }
    }, {
      key: "failure",
      value: function failure() {
        return new FlySearch({}, true, undefined);
      }
    }, {
      key: "reset",
      value: function reset() {
        return new FlySearch({}, false, null);
      }
    }, {
      key: "success",
      value: function success(action, flyCodes, savedFlights) {
        var _action$payload4 = action.payload,
          data = _action$payload4.data,
          errors = _action$payload4.errors;
        if ((errors == null ? undefined : errors.length) > 0) {
          var extractErrors = FlySearch.checkErrors("flights", errors);
          return new FlySearch({}, extractErrors[0], extractErrors[1]);
        }
        if (!flyCodes) {
          flyCodes = _data.CityData;
        }
        var airlinesItems = (0, _lodash.get)(data, "airlines.items", []);
        var citiesItems = (0, _lodash.get)(data, "cities.items", []);
        var totalFlight = (0, _lodash.get)(data, "flights.total", 0);
        var isDepartureFightData = (0, _lodash.get)(data, "flights.isDepartureFightData");
        var flights = FlySearch.convertFlights(data, flyCodes, savedFlights);
        var airlines = FlySearch.convertCitiesOrAirt(airlinesItems);
        var cities = FlySearch.convertCitiesOrAirt(citiesItems);
        return new FlySearch({
          flights: flights,
          cities: cities,
          airlines: airlines,
          totalFlight: totalFlight,
          isDepartureFightData: isDepartureFightData
        }, false, []);
      }
    }], [{
      key: "convertCitiesOrAirt",
      value: function convertCitiesOrAirt(items) {
        if (Array.isArray(items) && items.length > 0) {
          return items == null ? undefined : items.map(function (item) {
            return {
              code: item == null ? undefined : item.code,
              logo: item == null ? undefined : item.logo_url,
              name: item == null ? undefined : item.name,
              transferCounters: item == null ? undefined : item.transfer_counters
            };
          });
        }
        return [];
      }
    }, {
      key: "convertFlights",
      value: function convertFlights(data, flyCodes, savedFlights) {
        var items = (0, _lodash.get)(data, "flights.items", []);
        if (Array.isArray(items) && (items == null ? undefined : items.length) > 0) {
          return items == null ? undefined : items.map(function (item) {
            var _savedFlights$getMyTr4;
            var index = savedFlights == null || (_savedFlights$getMyTr4 = savedFlights.getMyTravelFlightDetails) == null ? undefined : _savedFlights$getMyTr4.findIndex(function (flight) {
              return `${flight == null ? undefined : flight.flightNumber}_${flight == null ? undefined : flight.scheduledDate}` === `${item == null ? undefined : item.flight_number}_${item == null ? undefined : item.scheduled_date}` && (flight == null ? undefined : flight.direction) === (item == null ? undefined : item.direction);
            });
            return convertDtoForFly(item, _flightListingCard.FlightListingState.default, flyCodes, isNaN(index) ? false : index !== -1, savedFlights == null ? undefined : savedFlights.errorFlag);
          });
        }
        return [];
      }
    }]);
  }(_flyBaseClass.FlyBaseClass);
  var FlyCodes = exports.FlyCodes = /*#__PURE__*/function (_FlyBaseClass5) {
    function FlyCodes(data) {
      var _this5;
      (0, _classCallCheck2.default)(this, FlyCodes);
      _this5 = _callSuper(this, FlyCodes);
      _this5.errorFlag = undefined;
      _this5.errorPayload = undefined;
      _this5.data = data;
      return _this5;
    }
    (0, _inherits2.default)(FlyCodes, _FlyBaseClass5);
    return (0, _createClass2.default)(FlyCodes, [{
      key: "failure",
      value: function failure() {
        return new FlyCodes(_data.CityData);
      }
    }, {
      key: "request",
      value: function request() {
        return new FlyCodes(_data.CityData);
      }
    }, {
      key: "success",
      value: function success(action) {
        var _data$data;
        var data = action.payload.data;
        if ((data == null || (_data$data = data.data) == null || (_data$data = _data$data.getAirports) == null ? undefined : _data$data.length) > 0) {
          var _data$data2, _data$data3;
          (0, _file.writeFile)(_file.StaticFiles.city, data == null || (_data$data2 = data.data) == null ? undefined : _data$data2.getAirports);
          return data == null || (_data$data3 = data.data) == null ? undefined : _data$data3.getAirports;
        } else {
          return new FlyCodes(_data.CityData);
        }
      }
    }]);
  }(_flyBaseClass.FlyBaseClass);
  var FlightDetails = exports.FlightDetails = /*#__PURE__*/function (_FlyBaseClass6) {
    function FlightDetails(flightDetailsData, heroImageData, errorFlag, errorPayload, flightRequestType) {
      var _this6;
      (0, _classCallCheck2.default)(this, FlightDetails);
      _this6 = _callSuper(this, FlightDetails);
      _this6.flightDetailsData = flightDetailsData;
      _this6.heroImageData = heroImageData;
      _this6.errorFlag = errorFlag;
      _this6.errorPayload = errorPayload;
      _this6.flightRequestType = flightRequestType || _flightProps.FlightRequestType.FlightDefault;
      return _this6;
    }
    (0, _inherits2.default)(FlightDetails, _FlyBaseClass6);
    return (0, _createClass2.default)(FlightDetails, [{
      key: "failure",
      value: function failure(data) {
        var flightRequestType = data.flightRequestType;
        return new FlightDetails(FlightDetails.convertFlightDetails({}, _flightDetailsCard.FlightDetailsCardType.loading), FlightDetails.convertHeroImage({}, _flightHeroImage.FlightHeroImageType.loading), true, [], flightRequestType);
      }
    }, {
      key: "request",
      value: function request() {
        return new FlightDetails(FlightDetails.convertFlightDetails({}, _flightDetailsCard.FlightDetailsCardType.loading), FlightDetails.convertHeroImage({}, _flightHeroImage.FlightHeroImageType.loading), false, []);
      }
    }, {
      key: "success",
      value: function success(action, flyCodes) {
        var _data$getFlights8, _data$getFlights9, _dataHandled$getFligh, _dataHandled$getFligh2;
        var _action$payload5 = action.payload,
          data = _action$payload5.data,
          errors = _action$payload5.errors;
        var dataHandled = FlightDetails.formatResponseFlightDetails(!(0, _lodash.isEmpty)(data == null || (_data$getFlights8 = data.getFlights) == null ? undefined : _data$getFlights8.flights) ? data == null || (_data$getFlights9 = data.getFlights) == null ? undefined : _data$getFlights9.flights[0] : {}, flyCodes, data == null ? undefined : data.getEarlyCheckin, data == null ? undefined : data.getOnlineCheckin);
        if ((dataHandled == null ? undefined : dataHandled.getFlightDetail) === null && (errors == null ? undefined : errors.length) > 0) {
          var extractErrors = FlightDetails.checkErrors("getFlightDetail", errors);
          return new FlightDetails(FlightDetails.convertFlightDetails({}, _flightDetailsCard.FlightDetailsCardType.loading), FlightDetails.convertHeroImage({}, _flightHeroImage.FlightHeroImageType.loading), true, extractErrors[0]);
        }
        return new FlightDetails(FlightDetails.convertFlightDetails(dataHandled, _flightDetailsCard.FlightDetailsCardType.default), FlightDetails.convertHeroImage(Object.assign({}, dataHandled == null || (_dataHandled$getFligh = dataHandled.getFlightDetail) == null ? undefined : _dataHandled$getFligh.flightMoreDetail, dataHandled == null || (_dataHandled$getFligh2 = dataHandled.getFlightDetail) == null ? undefined : _dataHandled$getFligh2.flightMainInfo), _flightHeroImage.FlightHeroImageType.default, flyCodes), false, []);
      }
    }]);
  }(_flyBaseClass.FlyBaseClass);
  _FlightDetails = FlightDetails;
  FlightDetails.getEstimatedTime = function (data) {
    var _data$flightStatus;
    var status = data == null || (_data$flightStatus = data.flightStatus) == null ? undefined : _data$flightStatus.toLowerCase();
    if (status === _flightDetailsCard.FlightStatus.Delayed && (data == null ? undefined : data.scheduledTime) === (data == null ? undefined : data.estimatedTime) || status === _flightDetailsCard.FlightStatus.Cancelled) {
      return "";
    }
    return data == null ? undefined : data.estimatedTime;
  };
  FlightDetails.getTerminalValue = function (data) {
    var _data$flightStatus2;
    var status = data == null || (_data$flightStatus2 = data.flightStatus) == null ? undefined : _data$flightStatus2.toLowerCase();
    if (status === _flightDetailsCard.FlightStatus.Cancelled) {
      return "";
    }
    return data == null ? undefined : data.displayTerminal;
  };
  FlightDetails.convertFlightDetails = function (data, type) {
    var _data$getFlightDetail, _data$getFlightDetail2, _data$getFlightDetail3;
    var getFlightDetail = Object.assign({}, data == null || (_data$getFlightDetail = data.getFlightDetail) == null ? undefined : _data$getFlightDetail.flightMoreDetail, data == null || (_data$getFlightDetail2 = data.getFlightDetail) == null ? undefined : _data$getFlightDetail2.flightMainInfo, data == null || (_data$getFlightDetail3 = data.getFlightDetail) == null ? undefined : _data$getFlightDetail3.myTravelInfo);
    if (getFlightDetail && Object.keys(getFlightDetail).length > 0) {
      var _getFlightDetail$airp, _data$getFlightDetail4, _data$getFlightDetail5;
      var newObject = {
        type: type,
        slaves: getFlightDetail == null ? undefined : getFlightDetail.codeShares,
        logoUrl: getFlightDetail == null ? undefined : getFlightDetail.logo,
        status: (getFlightDetail == null ? undefined : getFlightDetail.flightStatus) === "hide" ? "" : getFlightDetail.flightStatus,
        terminal: _FlightDetails.getTerminalValue(getFlightDetail),
        gate: getFlightDetail == null ? undefined : getFlightDetail.displayGate,
        baggageBelt: getFlightDetail == null ? undefined : getFlightDetail.displayBelt,
        baggageBeltStatus: getFlightDetail == null ? undefined : getFlightDetail.bagStatus,
        estimatedTime: _FlightDetails.getEstimatedTime(getFlightDetail),
        onlineCheckIn: data == null ? undefined : data.getOnlineCheckin,
        earlyCheckIn: data == null ? undefined : data.getEarlyCheckin,
        statusMapping: getFlightDetail == null ? undefined : getFlightDetail.statusMapping,
        country: getFlightDetail == null || (_getFlightDetail$airp = getFlightDetail.airportDetails) == null ? undefined : _getFlightDetail$airp.country,
        groundTransport: data == null || (_data$getFlightDetail4 = data.getFlightDetail) == null ? undefined : _data$getFlightDetail4.groundTransport,
        flightInsurance: data == null || (_data$getFlightDetail5 = data.getFlightDetail) == null ? undefined : _data$getFlightDetail5.flightInsurance
      };
      return Object.assign({}, getFlightDetail, newObject);
    }
    return {
      flightNumber: undefined,
      scheduledDate: undefined,
      scheduledTime: undefined,
      estimatedDate: undefined,
      estimatedTime: undefined,
      logoUrl: undefined,
      status: undefined,
      slaves: undefined,
      isFlightSaved: undefined,
      onButtonPressed: undefined,
      type: type,
      state: undefined,
      terminal: undefined,
      checkInRow: undefined,
      gate: undefined,
      checkInRowCoordinates: undefined,
      gateCoordinates: undefined,
      onlineCheckIn: undefined,
      earlyCheckIn: undefined,
      baggageBelt: undefined,
      baggageBeltCoordinates: undefined,
      baggageBeltStatus: undefined,
      statusMapping: undefined,
      isSaved: false,
      country: undefined,
      groundTransport: undefined,
      flightInsurance: undefined
    };
  };
  FlightDetails.getCityNameFromCode = function (code, flyCodes) {
    var _codeIndex$name;
    var dataFlyCodes = (0, _lodash.isArray)(flyCodes) ? flyCodes : flyCodes == null ? undefined : flyCodes.data;
    var codeIndex = dataFlyCodes && (dataFlyCodes == null ? undefined : dataFlyCodes.find(function (item) {
      return item.code === code;
    }));
    return (_codeIndex$name = codeIndex == null ? undefined : codeIndex.name) != null ? _codeIndex$name : "";
  };
  FlightDetails.convertHeroImage = function (data, type, flyCodes) {
    if (!flyCodes) {
      flyCodes = _data.CityData;
    }
    if (data && Object.keys(data).length > 0) {
      var _data$transits;
      var transits = [];
      data == null || (_data$transits = data.transits) == null || _data$transits.map(function (item) {
        return transits.push({
          airportCode: item == null ? undefined : item.code,
          city: _FlightDetails.getCityNameFromCode(item == null ? undefined : item.code, flyCodes)
        });
      });
      var newObject = {
        type: type,
        imageUrl: data == null ? undefined : data.heroImage,
        travelInfo: [{
          airportCode: data.departingCode,
          city: data == null ? undefined : data.departingPlace
        }].concat(transits, [{
          airportCode: data.destinationCode,
          city: data == null ? undefined : data.destinationPlace
        }])
      };
      return Object.assign({}, data, newObject);
    }
    return {
      type: type,
      state: undefined,
      imageUrl: "",
      travelInfo: undefined
    };
  };
  FlightDetails.formatResponseFlightDetails = function (response, _flyCodes, getEarlyCheckin, getOnlineCheckin) {
    var _departingCodeIndex7, _departingCodeIndex8, _destinationCodeIndex7, _destinationCodeIndex8, _response$status_mapp, _response$status_mapp2, _response$status_mapp3, _response$estimated_t, _response$estimated_t2;
    var departingCodeIndex = null;
    var destinationCodeIndex = null;
    if ((response == null ? undefined : response.direction) === "ARR") {
      var _response$airport_det, _response$airport_det2;
      departingCodeIndex = {
        code: response == null || (_response$airport_det = response.airport_details) == null ? undefined : _response$airport_det.code,
        name: response == null || (_response$airport_det2 = response.airport_details) == null ? undefined : _response$airport_det2.name
      };
      destinationCodeIndex = {
        code: "SIN",
        name: "Singapore"
      };
    } else if ((response == null ? undefined : response.direction) === "DEP") {
      var _response$airport_det3, _response$airport_det4;
      destinationCodeIndex = {
        code: response == null || (_response$airport_det3 = response.airport_details) == null ? undefined : _response$airport_det3.code,
        name: response == null || (_response$airport_det4 = response.airport_details) == null ? undefined : _response$airport_det4.name
      };
      departingCodeIndex = {
        code: "SIN",
        name: "Singapore"
      };
    }
    return {
      getEarlyCheckin: getEarlyCheckin,
      getFlightDetail: {
        flightMainInfo: {
          logo: response.logo || `https://d2xm3qu3nwgbrt.cloudfront.net/airlines/${response == null ? undefined : response.airline}.gif`,
          codeShares: response == null ? undefined : response.slave_flights,
          slaves: response == null ? undefined : response.slave_flights,
          airline: response == null ? undefined : response.airline,
          airlineDetails: response == null ? undefined : response.airline_details,
          departingCode: (_departingCodeIndex7 = departingCodeIndex) == null ? undefined : _departingCodeIndex7.code,
          departingPlace: (_departingCodeIndex8 = departingCodeIndex) == null ? undefined : _departingCodeIndex8.name,
          destinationCode: (_destinationCodeIndex7 = destinationCodeIndex) == null ? undefined : _destinationCodeIndex7.code,
          destinationPlace: (_destinationCodeIndex8 = destinationCodeIndex) == null ? undefined : _destinationCodeIndex8.name,
          flightDate: null,
          flightNumber: response == null ? undefined : response.flight_number,
          flightStatus: response == null ? undefined : response.flight_status,
          flightTime: null,
          flightUniqueId: `${response == null ? undefined : response.flight_number}_${response == null ? undefined : response.scheduled_date}`,
          scheduledDate: response == null ? undefined : response.scheduled_date,
          scheduledTime: response == null ? undefined : response.scheduled_time,
          direction: response == null ? undefined : response.direction,
          transits: response != null && response.via ? [{
            code: response == null ? undefined : response.via,
            id: 1
          }] : [],
          via: response == null ? undefined : response.via,
          viaAirportDetails: response == null ? undefined : response.via_airport_details,
          originDepDate: response == null ? undefined : response.origin_dep_date,
          nearestCarpark: response == null ? undefined : response.nearest_carpark,
          actualTimestamp: response == null ? undefined : response.actual_timestamp,
          estimatedTimestamp: response == null ? undefined : response.estimatedTimestamp,
          displayTimestamp: response == null ? undefined : response.display_timestamp_mapping,
          statusMapping: response == null ? undefined : response.status_mapping,
          airport: response == null ? undefined : response.airport,
          airportDetails: response == null ? undefined : response.airport_details,
          technicalFlightStatus1: response == null ? undefined : response.technical_flight_status1,
          baggageTracking: response == null ? undefined : response.baggageTracking,
          upcomingStatusMapping: response == null || (_response$status_mapp = response.status_mapping) == null ? undefined : _response$status_mapp.details_status_en,
          displayGate: response == null ? undefined : response.display_gate,
          showGate: (_response$status_mapp2 = response.status_mapping) == null ? undefined : _response$status_mapp2.show_gate,
          beltStatusMapping: response == null || (_response$status_mapp3 = response.status_mapping) == null ? undefined : _response$status_mapp3.belt_status_en,
          estFbTime: response == null ? undefined : response.est_fb_time
        },
        flightMoreDetail: {
          bagStatus: null,
          checkInRow: response == null ? undefined : response.check_in_row,
          displayBelt: response == null ? undefined : response.display_belt,
          displayGate: response == null ? undefined : response.display_gate,
          displayTerminal: response == null ? undefined : response.terminal,
          displayTerminalDisclaimer: response == null ? undefined : response.terminal_disclaimer,
          estimatedDate: response == null || (_response$estimated_t = response.estimated_timestamp) == null || (_response$estimated_t = _response$estimated_t.split(" ")) == null ? undefined : _response$estimated_t[0],
          estimatedTime: response == null || (_response$estimated_t2 = response.estimated_timestamp) == null || (_response$estimated_t2 = _response$estimated_t2.split(" ")) == null ? undefined : _response$estimated_t2[1],
          heroImage: response == null ? undefined : response.hero_image
        },
        myTravelInfo: {
          flightPax: null,
          isSaved: response == null ? undefined : response.isSaved,
          recordSource: null,
          isPassenger: response == null ? undefined : response.isPassenger
        },
        groundTransport: response == null ? undefined : response.groundTransport,
        flightInsurance: response == null ? undefined : response.flightInsurance
      },
      getOnlineCheckin: getOnlineCheckin
    };
  };
  var FlightTimelineTiles = exports.FlightTimelineTiles = /*#__PURE__*/function (_FlyBaseClass7) {
    function FlightTimelineTiles(timelineTileData, errorFlag, errorPayload) {
      var _this7;
      (0, _classCallCheck2.default)(this, FlightTimelineTiles);
      _this7 = _callSuper(this, FlightTimelineTiles);
      _this7.timelineTileData = timelineTileData;
      _this7.errorFlag = errorFlag;
      _this7.errorPayload = errorPayload;
      return _this7;
    }
    (0, _inherits2.default)(FlightTimelineTiles, _FlyBaseClass7);
    return (0, _createClass2.default)(FlightTimelineTiles, [{
      key: "failure",
      value: function failure() {
        return new FlightTimelineTiles(FlightTimelineTiles.convertDto([], _flightTimelineSectionProps.TimelineTileSectionType.default, _sectionImage.SectionImageComponentType.defaultDarkGradient), true, []);
      }
    }, {
      key: "request",
      value: function request() {
        return new FlightTimelineTiles(FlightTimelineTiles.convertDto([], _flightTimelineSectionProps.TimelineTileSectionType.loading, _sectionImage.SectionImageComponentType.loading), false, []);
      }
    }, {
      key: "success",
      value: function success(action) {
        var _action$payload6 = action.payload,
          data = _action$payload6.data,
          errors = _action$payload6.errors;
        var appSettingsData = action == null ? undefined : action.env;
        if ((errors == null ? undefined : errors.length) > 0) {
          var extractErrors = FlightTimelineTiles.checkErrors("getTravelChecklists", errors);
          return new FlightTimelineTiles(FlightTimelineTiles.convertDto([], _flightTimelineSectionProps.TimelineTileSectionType.default, _sectionImage.SectionImageComponentType.defaultDarkGradient, appSettingsData), true, extractErrors[0]);
        }
        return new FlightTimelineTiles(FlightTimelineTiles.convertDto(data == null ? undefined : data.getTravelChecklists, _flightTimelineSectionProps.TimelineTileSectionType.default, _sectionImage.SectionImageComponentType.defaultDarkGradient, appSettingsData), false, []);
      }
    }]);
  }(_flyBaseClass.FlyBaseClass);
  FlightTimelineTiles.convertDto = function (data, type, sectionImageType) {
    var appSettingsData = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;
    if (data && Array.isArray(data) && data.length > 0) {
      var sectionData = [];
      data == null || data.map(function (item) {
        var _item$variations;
        var tileData = [];
        item == null || (_item$variations = item.variations) == null || _item$variations.map(function (tile) {
          if ((tile == null ? undefined : tile.type) === _timelineFlyTiles.TimelineFlyTilesVariations.variation1) {
            var _tile$link1Navigation, _tile$link1Navigation2;
            return tileData.push(Object.assign({}, tile, {
              image: (0, _utils.mappingUrlAem)(tile == null ? undefined : tile.image, appSettingsData == null ? undefined : appSettingsData.AEM_URL),
              firstLink: {
                label: tile == null ? undefined : tile.link1Text,
                subLabel: tile == null ? undefined : tile.link1Label,
                navigationType: tile == null || (_tile$link1Navigation = tile.link1Navigation) == null ? undefined : _tile$link1Navigation.type,
                navigationValue: tile == null || (_tile$link1Navigation2 = tile.link1Navigation) == null ? undefined : _tile$link1Navigation2.value
              }
            }));
          }
          if ((tile == null ? undefined : tile.type) === _timelineFlyTiles.TimelineFlyTilesVariations.variation2) {
            var _tile$link1Navigation3, _tile$link1Navigation4, _tile$link2Navigation, _tile$link2Navigation2;
            return tileData.push(Object.assign({}, tile, {
              image: (0, _utils.mappingUrlAem)(tile == null ? undefined : tile.image, appSettingsData == null ? undefined : appSettingsData.AEM_URL),
              firstLink: {
                label: tile == null ? undefined : tile.link1Text,
                subLabel: tile == null ? undefined : tile.link1Label,
                navigationType: tile == null || (_tile$link1Navigation3 = tile.link1Navigation) == null ? undefined : _tile$link1Navigation3.type,
                navigationValue: tile == null || (_tile$link1Navigation4 = tile.link1Navigation) == null ? undefined : _tile$link1Navigation4.value
              },
              secondLink: {
                label: tile == null ? undefined : tile.link2Text,
                subLabel: tile == null ? undefined : tile.link2Label,
                navigationType: tile == null || (_tile$link2Navigation = tile.link2Navigation) == null ? undefined : _tile$link2Navigation.type,
                navigationValue: tile == null || (_tile$link2Navigation2 = tile.link2Navigation) == null ? undefined : _tile$link2Navigation2.value
              }
            }));
          }
          if ((tile == null ? undefined : tile.type) === _timelineFlyTiles.TimelineFlyTilesVariations.variation3) {
            var _tile$contents;
            return tileData.push(Object.assign({}, tile, {
              contents: tile == null || (_tile$contents = tile.contents) == null ? undefined : _tile$contents.map(function (itemContent) {
                var _itemContent$navigati, _itemContent$navigati2;
                return Object.assign({}, itemContent, {
                  imageUrl: (0, _utils.mappingUrlAem)(itemContent == null ? undefined : itemContent.image, appSettingsData == null ? undefined : appSettingsData.AEM_URL),
                  titleName: itemContent == null ? undefined : itemContent.title,
                  navigationType: itemContent == null || (_itemContent$navigati = itemContent.navigation) == null ? undefined : _itemContent$navigati.type,
                  navigationValue: itemContent == null || (_itemContent$navigati2 = itemContent.navigation) == null ? undefined : _itemContent$navigati2.value
                });
              })
            }));
          }
          return tileData.push(Object.assign({}, tile));
        });
        return sectionData == null ? undefined : sectionData.push(Object.assign({}, item, {
          tileItems: tileData
        }));
      });
      return {
        type: type,
        sectionImageType: sectionImageType,
        timelineTiles: sectionData
      };
    }
    return {
      type: type,
      sectionImageType: sectionImageType,
      timelineTiles: undefined
    };
  };
  var GetIntoCityOrAirport = exports.GetIntoCityOrAirport = /*#__PURE__*/function (_FlyBaseClass8) {
    function GetIntoCityOrAirport(getIntoCityOrAirport, errorFlag, errorPayload) {
      var _this8;
      (0, _classCallCheck2.default)(this, GetIntoCityOrAirport);
      _this8 = _callSuper(this, GetIntoCityOrAirport);
      _this8.getIntoCityOrAirport = getIntoCityOrAirport;
      _this8.errorFlag = errorFlag;
      _this8.errorPayload = errorPayload;
      return _this8;
    }
    (0, _inherits2.default)(GetIntoCityOrAirport, _FlyBaseClass8);
    return (0, _createClass2.default)(GetIntoCityOrAirport, [{
      key: "failure",
      value: function failure() {
        return new GetIntoCityOrAirport(GetIntoCityOrAirport.convertDtoForGetIntoCityOrAirport({}), true, []);
      }
    }, {
      key: "request",
      value: function request() {
        return new GetIntoCityOrAirport(GetIntoCityOrAirport.convertDtoForGetIntoCityOrAirport({}), false, []);
      }
    }, {
      key: "success",
      value: function success(action) {
        var _action$payload7 = action.payload,
          data = _action$payload7.data,
          errors = _action$payload7.errors;
        if ((data == null ? undefined : data.getIntoCityOrAirport) === null && (errors == null ? undefined : errors.length) > 0) {
          var extractErrors = GetIntoCityOrAirport.checkErrors("getIntoCityOrAirport", errors);
          return new GetIntoCityOrAirport(GetIntoCityOrAirport.convertDtoForGetIntoCityOrAirport({}), true, extractErrors[0]);
        }
        return new GetIntoCityOrAirport(GetIntoCityOrAirport.convertDtoForGetIntoCityOrAirport(data == null ? undefined : data.getIntoCityOrAirport), false, []);
      }
    }]);
  }(_flyBaseClass.FlyBaseClass);
  GetIntoCityOrAirport.convertDtoForGetIntoCityOrAirport = function (item) {
    if (item && (Object == null ? undefined : Object.keys(item).length) > 0) {
      return {
        state: _cardWithLinks.CardWithLinksState.withIcons,
        type: _cardWithLinks.CardWithLinksType.default,
        title: item.title,
        label1: item.label1,
        text1: item.link1 ? item.link1 : "N/A",
        link1: item.link1 ? item.link1 : "N/A",
        label2: item.label2,
        text2: item.link2 ? item.link2 : "N/A",
        link2: item.link2 ? item.link2 : "N/A",
        label3: item.label3,
        text3: item.link3 ? item.link3 : "N/A",
        link3: item.link3 ? item.link3 : "N/A",
        data1: item.data1,
        data2: item.data2,
        data3: item.data3
      };
    }
    return {
      state: _cardWithLinks.CardWithLinksState.withIcons,
      type: _cardWithLinks.CardWithLinksType.loading,
      title: undefined,
      label1: undefined,
      text1: undefined,
      link1: undefined,
      label2: undefined,
      text2: undefined,
      link2: undefined,
      label3: undefined,
      text3: undefined,
      link3: undefined,
      data1: undefined,
      data2: undefined,
      data3: undefined
    };
  };
  var FlightDetailsCRT = exports.FlightDetailsCRT = /*#__PURE__*/function (_FlyBaseClass9) {
    function FlightDetailsCRT(type, flightDetailsCRTData, errorFlag, errorPayload) {
      var _this9;
      (0, _classCallCheck2.default)(this, FlightDetailsCRT);
      _this9 = _callSuper(this, FlightDetailsCRT);
      _this9.type = type;
      _this9.flightDetailsCRTData = flightDetailsCRTData;
      _this9.errorFlag = errorFlag;
      _this9.errorPayload = errorPayload;
      return _this9;
    }
    (0, _inherits2.default)(FlightDetailsCRT, _FlyBaseClass9);
    return (0, _createClass2.default)(FlightDetailsCRT, [{
      key: "failure",
      value: function failure() {
        return new FlightDetailsCRT(null, FlightDetailsCRT.convertDtoForFlightDetailsCRT({}), true, []);
      }
    }, {
      key: "request",
      value: function request() {
        return new FlightDetailsCRT(_benefitsCard.BenefitsCardType.loading, FlightDetailsCRT.convertDtoForFlightDetailsCRT({
          type: _benefitsCard.BenefitsCardType.loading,
          state: undefined,
          title: undefined,
          data: undefined,
          url: undefined,
          icon: undefined
        }), false, []);
      }
    }, {
      key: "success",
      value: function success(action) {
        var _action$payload8 = action.payload,
          data = _action$payload8.data,
          errors = _action$payload8.errors;
        if ((data == null ? undefined : data.getCRTBenefitCard) === null && (errors == null ? undefined : errors.length) > 0) {
          var extractErrors = FlightDetailsCRT.checkErrors("getCRTBenefitCard", errors);
          return new FlightDetailsCRT(null, FlightDetailsCRT.convertDtoForFlightDetailsCRT({}), true, extractErrors[0]);
        }
        return new FlightDetailsCRT(_benefitsCard.BenefitsCardType.default, FlightDetailsCRT.convertDtoForFlightDetailsCRT(data == null ? undefined : data.getCRTBenefitCard), false, []);
      }
    }]);
  }(_flyBaseClass.FlyBaseClass);
  FlightDetailsCRT.convertDtoForFlightDetailsCRT = function (item) {
    if (item === null || item === undefined || item && (Object == null ? undefined : Object.keys(item).length) === 0) {
      return null;
    }
    if (item && (Object == null ? undefined : Object.keys(item).length) > 0) {
      return {
        type: _benefitsCard.BenefitsCardType.default,
        state: _benefitsCard.BenefitsCardState.crtBenefits,
        title: item.title,
        data: item.highlights ? Array.from(item.highlights).map(function (value, index) {
          return {
            id: index,
            checkIconText: value
          };
        }) : [],
        url: item.url,
        icon: item.icon
      };
    }
    return {
      type: _benefitsCard.BenefitsCardType.loading,
      state: undefined,
      title: undefined,
      data: undefined,
      url: undefined,
      icon: undefined
    };
  };
  var MyTravelInsertFlight = exports.MyTravelInsertFlight = /*#__PURE__*/function (_FlyBaseClass0) {
    function MyTravelInsertFlight(insertFlightData, errorFlag, errorPayload, loading, recordExist, isInsertSuccessfully) {
      var _this0;
      (0, _classCallCheck2.default)(this, MyTravelInsertFlight);
      _this0 = _callSuper(this, MyTravelInsertFlight);
      _this0.insertFlightData = insertFlightData;
      _this0.errorFlag = errorFlag;
      _this0.errorPayload = errorPayload;
      _this0.loading = loading;
      _this0.recordExist = recordExist;
      _this0.isInsertSuccessfully = isInsertSuccessfully;
      return _this0;
    }
    (0, _inherits2.default)(MyTravelInsertFlight, _FlyBaseClass0);
    return (0, _createClass2.default)(MyTravelInsertFlight, [{
      key: "failure",
      value: function failure() {
        return new MyTravelInsertFlight({}, true, [], false, false, false);
      }
    }, {
      key: "request",
      value: function request() {
        return new MyTravelInsertFlight({}, false, [], true, false, false);
      }
    }, {
      key: "success",
      value: function success(action) {
        var _data$insertMyTravelF;
        var _action$payload9 = action.payload,
          data = _action$payload9.data,
          errors = _action$payload9.errors;
        if ((data == null ? undefined : data.insertMyTravelFlightDetail) === null && (errors == null ? undefined : errors.length) > 0) {
          var extractErrors = MyTravelInsertFlight.checkErrors("insertMyTravelFlightDetail", errors);
          return new MyTravelInsertFlight({}, true, extractErrors[0], false, false, false);
        }
        var status = data == null || (_data$insertMyTravelF = data.insertMyTravelFlightDetail) == null ? undefined : _data$insertMyTravelF.status;
        if ((status == null ? undefined : status.message) === _savedFlightCard.SavedFlightErrorCodes.RecordAlreadyExist) {
          return new MyTravelInsertFlight({}, false, [], false, true, true);
        }
        if (status != null && status.success) {
          return new MyTravelInsertFlight(status, false, [], false, false, true);
        }
        return new MyTravelInsertFlight({}, true, [], false, false, false);
      }
    }]);
  }(_flyBaseClass.FlyBaseClass);
  var GetMyTravelFlightDetails = exports.GetMyTravelFlightDetails = /*#__PURE__*/function (_FlyBaseClass1) {
    function GetMyTravelFlightDetails(getMyTravelFlightDetails, errorFlag, errorPayload, loading) {
      var _this1;
      (0, _classCallCheck2.default)(this, GetMyTravelFlightDetails);
      _this1 = _callSuper(this, GetMyTravelFlightDetails);
      _this1.getMyTravelFlightDetails = getMyTravelFlightDetails;
      _this1.errorFlag = errorFlag;
      _this1.errorPayload = errorPayload;
      _this1.loading = loading;
      return _this1;
    }
    (0, _inherits2.default)(GetMyTravelFlightDetails, _FlyBaseClass1);
    return (0, _createClass2.default)(GetMyTravelFlightDetails, [{
      key: "failure",
      value: function failure() {
        return new GetMyTravelFlightDetails([], true, [], false);
      }
    }, {
      key: "request",
      value: function request() {
        return new GetMyTravelFlightDetails([], false, [], true);
      }
    }, {
      key: "mappingFlightItem",
      value: function mappingFlightItem(item) {
        var isLanded = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
        var priorityTime = (item == null ? undefined : item.actualTimeStamp) || (item == null ? undefined : item.estimatedTimestamp) || `${item == null ? undefined : item.scheduledDate} ${item == null ? undefined : item.scheduledTime}`;
        var currentTimeToUTC = (0, _momentTimezone.default)().tz("Asia/Singapore");
        var flightTime = (0, _moment.default)(priorityTime, "YYYY-MM-DD HH:mm", "Asia/Singapore");
        if (currentTimeToUTC.format("YYYY-MM-DD HH:mm") > flightTime.format("YYYY-MM-DD HH:mm") && !isLanded || currentTimeToUTC.format("YYYY-MM-DD HH:mm") > (0, _moment.default)(flightTime).add(1, "hours").format("YYYY-MM-DD HH:mm") && isLanded) {
          return Object.assign({}, item, {
            needShowInSavedFlight: false
          });
        }
        return Object.assign({}, item, {
          needShowInSavedFlight: true
        });
      }
    }, {
      key: "success",
      value: function success(action) {
        var _data$getMyTravelFlig;
        var _action$payload0 = action == null ? undefined : action.payload,
          data = _action$payload0.data,
          errors = _action$payload0.errors;
        if ((data == null ? undefined : data.getMyTravelFlightDetails) === null && (errors == null ? undefined : errors.length) > 0) {
          var extractErrors = GetMyTravelFlightDetails.checkErrors("getMyTravelFlightDetails", errors);
          return new GetMyTravelFlightDetails([], true, extractErrors[0], true);
        }
        var payload = [];
        if ((0, _lodash.isArray)(data == null || (_data$getMyTravelFlig = data.getMyTravelFlightDetails) == null ? undefined : _data$getMyTravelFlig.data)) {
          var _data$getMyTravelFlig2;
          data == null || (_data$getMyTravelFlig2 = data.getMyTravelFlightDetails) == null || (_data$getMyTravelFlig2 = _data$getMyTravelFlig2.data) == null || _data$getMyTravelFlig2.forEach(function (item) {
            payload.push(GetMyTravelFlightDetails.convertDto(Object.assign({}, item, {
              needShowInSavedFlight: true
            }), _savedFlightCard.SavedFlightCardState.default));
          });
        }
        return new GetMyTravelFlightDetails(payload, false, [], false);
      }
    }]);
  }(_flyBaseClass.FlyBaseClass);
  _GetMyTravelFlightDetails = GetMyTravelFlightDetails;
  GetMyTravelFlightDetails.getCityNameFromCode = function (code, flyCodes) {
    var _dataFlyCodes$data, _codeIndex$name2;
    var dataFlyCodes = (0, _lodash.isArray)(flyCodes) ? flyCodes : flyCodes == null ? undefined : flyCodes.data;
    var codeIndex = (0, _lodash.isArray)(flyCodes) ? dataFlyCodes == null ? undefined : dataFlyCodes.find(function (item) {
      return item.code === code;
    }) : dataFlyCodes == null || (_dataFlyCodes$data = dataFlyCodes.data) == null ? undefined : _dataFlyCodes$data.find(function (item) {
      return item.code === code;
    });
    return (_codeIndex$name2 = codeIndex == null ? undefined : codeIndex.name) != null ? _codeIndex$name2 : "";
  };
  GetMyTravelFlightDetails.getCityNameAndCode = function (airport) {
    var _departing, _departing2, _destination, _destination2;
    var departing = {};
    var destination = {};
    if ((airport == null ? undefined : airport.flightDirection) === "ARR") {
      var _airport$airportDetai, _airport$airportDetai2;
      departing = {
        code: airport == null || (_airport$airportDetai = airport.airportDetails) == null ? undefined : _airport$airportDetai.code,
        name: airport == null || (_airport$airportDetai2 = airport.airportDetails) == null ? undefined : _airport$airportDetai2.name
      };
      destination = {
        code: "SIN",
        name: "Singapore"
      };
    } else if ((airport == null ? undefined : airport.flightDirection) === "DEP") {
      var _airport$airportDetai3, _airport$airportDetai4;
      destination = {
        code: airport == null || (_airport$airportDetai3 = airport.airportDetails) == null ? undefined : _airport$airportDetai3.code,
        name: airport == null || (_airport$airportDetai4 = airport.airportDetails) == null ? undefined : _airport$airportDetai4.name
      };
      departing = {
        code: "SIN",
        name: "Singapore"
      };
    }
    return {
      departingCode: (_departing = departing) == null ? undefined : _departing.code,
      departingPlace: (_departing2 = departing) == null ? undefined : _departing2.name,
      destinationCode: (_destination = destination) == null ? undefined : _destination.code,
      destinationPlace: (_destination2 = destination) == null ? undefined : _destination2.name
    };
  };
  GetMyTravelFlightDetails.convertDto = function (data, type) {
    if (data && (Object == null ? undefined : Object.keys(data).length) > 0) {
      var flight = data == null ? undefined : data.flightMainInfo;
      var dataCityAndAiportCode = _GetMyTravelFlightDetails.getCityNameAndCode(data);
      return Object.assign({
        state: type
      }, data, flight, dataCityAndAiportCode, {
        direction: data == null ? undefined : data.flightDirection
      });
    }
    return {
      flightDate: undefined,
      state: type,
      logo: null,
      flightNumber: null,
      status: null,
      codeShare: null,
      departingCode: null,
      departingPlace: null,
      destinationCode: null,
      destinationPlace: null,
      timeOfFlight: null,
      transitCodes: null,
      onPressed: null,
      onCloseButtonPressed: null
    };
  };
  var MyTravelRemoveFlight = exports.MyTravelRemoveFlight = /*#__PURE__*/function (_FlyBaseClass10) {
    function MyTravelRemoveFlight(removeFlightData, errorFlag, errorPayload, loading, recordRemoved, isRemovedSuccessFully) {
      var _this10;
      (0, _classCallCheck2.default)(this, MyTravelRemoveFlight);
      _this10 = _callSuper(this, MyTravelRemoveFlight);
      _this10.removeFlightData = removeFlightData;
      _this10.errorFlag = errorFlag;
      _this10.errorPayload = errorPayload;
      _this10.loading = loading;
      _this10.recordRemoved = recordRemoved;
      _this10.isRemovedSuccessFully = isRemovedSuccessFully;
      return _this10;
    }
    (0, _inherits2.default)(MyTravelRemoveFlight, _FlyBaseClass10);
    return (0, _createClass2.default)(MyTravelRemoveFlight, [{
      key: "failure",
      value: function failure() {
        return new MyTravelRemoveFlight({}, true, [], false, false, false);
      }
    }, {
      key: "request",
      value: function request() {
        return new MyTravelRemoveFlight({}, false, [], true, false, false);
      }
    }, {
      key: "success",
      value: function success(action) {
        var _data$deleteMyTravelF;
        var _action$payload1 = action.payload,
          data = _action$payload1.data,
          errors = _action$payload1.errors;
        if ((data == null ? undefined : data.deleteMyTravelFlightDetail) === null && (errors == null ? undefined : errors.length) > 0) {
          var extractErrors = MyTravelRemoveFlight.checkErrors("deleteMyTravelFlightDetail", errors);
          return new MyTravelRemoveFlight({}, true, extractErrors[0], false, false, false);
        }
        var status = data == null || (_data$deleteMyTravelF = data.deleteMyTravelFlightDetail) == null ? undefined : _data$deleteMyTravelF.status;
        if ((status == null ? undefined : status.message) === _savedFlightCard.SavedFlightErrorCodes.RecordAlreadyRemoved) {
          return new MyTravelRemoveFlight({}, false, [], false, true, true);
        }
        if (status != null && status.success) {
          return new MyTravelRemoveFlight(status, false, [], false, false, true);
        }
        return new MyTravelRemoveFlight({}, true, [], false, false, false);
      }
    }]);
  }(_flyBaseClass.FlyBaseClass);
