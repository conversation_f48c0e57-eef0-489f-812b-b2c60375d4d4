  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _elements = _$$_REQUIRE(_dependencyMap[2]);
  var _theme = _$$_REQUIRE(_dependencyMap[3]);
  var _lottieReactNative = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativeWebview = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _Dimensions$get = _reactNative.Dimensions.get("screen"),
    width = _Dimensions$get.width,
    height = _Dimensions$get.height;
  var safeView = {
    flex: 1,
    backgroundColor: _theme.color.palette.almostWhiteGrey
  };
  var webViewStyle = {
    flex: 1
  };
  var lottieView = {
    height: 150,
    width: '100%',
    marginLeft: 10
  };
  var chatbotUrl = "https://flow.botdistrikt.com/webchat?webchat_app_id=2&domain=https://ichangi2023.ichangi.com&is_placement=1";
  var AskMaxView = function AskMaxView() {
    var _useState = (0, _react.useState)(true),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      loading = _useState2[0],
      setLoading = _useState2[1];
    var headerHeight = (0, _elements.useHeaderHeight)();
    var overlayLoadingView = {
      position: "absolute",
      alignItems: "center",
      justifyContent: "center",
      width: width,
      height: headerHeight ? height - headerHeight : height
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.SafeAreaView, {
      style: safeView,
      children: [(0, _jsxRuntime.jsx)(_reactNativeWebview.default, {
        source: {
          uri: chatbotUrl
        },
        style: webViewStyle,
        onLoadEnd: function onLoadEnd() {
          return setLoading(false);
        }
      }), loading && (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: overlayLoadingView,
        children: (0, _jsxRuntime.jsx)(_lottieReactNative.default, {
          source: _$$_REQUIRE(_dependencyMap[9]),
          autoPlay: true,
          loop: true,
          style: lottieView
        })
      })]
    });
  };
  var _default = exports.default = AskMaxView;
