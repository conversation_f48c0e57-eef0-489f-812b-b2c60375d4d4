  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.DineScreenV2 = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _styles = _$$_REQUIRE(_dependencyMap[3]);
  var _component = _$$_REQUIRE(_dependencyMap[4]);
  var _ViewQuickLinks = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _aemQuicklinkCategories = _$$_REQUIRE(_dependencyMap[6]);
  var _errorSectionV = _$$_REQUIRE(_dependencyMap[7]);
  var _useFunction = _$$_REQUIRE(_dependencyMap[8]);
  var _native = _$$_REQUIRE(_dependencyMap[9]);
  var _utils = _$$_REQUIRE(_dependencyMap[10]);
  var _brandOffer = _$$_REQUIRE(_dependencyMap[11]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[12]);
  var _adobe = _$$_REQUIRE(_dependencyMap[13]);
  var _dineShopContext = _$$_REQUIRE(_dependencyMap[14]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[15]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var DineScreenV2 = exports.DineScreenV2 = function DineScreenV2(_ref) {
    var _dataDealsPromos$prom;
    var route = _ref.route,
      overallScrollRef = _ref.overallScrollRef;
    var navigation = (0, _native.useNavigation)();
    var _useSafeAreaInsets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)(),
      bottom = _useSafeAreaInsets.bottom;
    var _useFunctions = (0, _useFunction.useFunctions)(),
      dataDineAndShopQuicklinks = _useFunctions.dataDineAndShopQuicklinks,
      isLoadingDineAndShopQuicklinks = _useFunctions.isLoadingDineAndShopQuicklinks,
      dataDealsPromos = _useFunctions.dataDealsPromos,
      isLoadingDealsPromos = _useFunctions.isLoadingDealsPromos,
      isErrorDealsPromos = _useFunctions.isErrorDealsPromos,
      getDataDealsPromos = _useFunctions.getDataDealsPromos,
      isJustForYouV2 = _useFunctions.isJustForYouV2,
      dataJustForYou = _useFunctions.dataJustForYou,
      isLoadingJustForYou = _useFunctions.isLoadingJustForYou,
      isErrorJustForYou = _useFunctions.isErrorJustForYou,
      dataDineGuides = _useFunctions.dataDineGuides,
      isLoadingDineGuides = _useFunctions.isLoadingDineGuides,
      isErrorDineGuides = _useFunctions.isErrorDineGuides,
      handleClickBrandOffer = _useFunctions.handleClickBrandOffer,
      showNoInternetError = _useFunctions.showNoInternetError,
      checkInternet = _useFunctions.checkInternet,
      isShopDineDealPromo = _useFunctions.isShopDineDealPromo,
      handleRefetchQuicklinks = _useFunctions.handleRefetchQuicklinks,
      isQuickLinksTransformEnabled = _useFunctions.isQuickLinksTransformEnabled;
    var shopHandlers = (0, _react.useContext)(_dineShopContext.DineShopContext).Handlers.shop;
    shopHandlers.overallScrollRef = overallScrollRef;
    shopHandlers.routeParams = route == null ? undefined : route.params;
    return (0, _jsxRuntime.jsx)(_reactNative.ScrollView, {
      showsVerticalScrollIndicator: false,
      style: _styles.styles.background,
      children: (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: _styles.styles.container,
        children: showNoInternetError ? (0, _jsxRuntime.jsx)(_errorSectionV.ErrorSectionV2, {
          onReload: function onReload() {
            checkInternet();
            handleRefetchQuicklinks();
          },
          containerStyle: {
            paddingBottom: 120 + bottom
          },
          reloadButtonStyleOverride: _styles.styles.noInternetReloadButtonStyle,
          reloadButtonTextStyleOverride: _styles.styles.reloadButtonTextStyle
        }) : (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [!isQuickLinksTransformEnabled ? (0, _jsxRuntime.jsx)(_component.QuickLink, {}) : (0, _jsxRuntime.jsx)(_ViewQuickLinks.default, {
            data: dataDineAndShopQuicklinks == null ? undefined : dataDineAndShopQuicklinks.shortcutLinks,
            loading: isLoadingDineAndShopQuicklinks && !(dataDineAndShopQuicklinks != null && dataDineAndShopQuicklinks.shortcutLinks),
            errorShortcutLinks: false,
            skeletonContainerStyle: _styles.styles.skeletonContainer,
            containerStyle: _styles.styles.quickLinksContainer,
            anchorCategoryTitle: _aemQuicklinkCategories.AllQuickLinksAnchorTitle.DineAndShop,
            trackingEvar: _adobe.AdobeTagName.DineQuicklinksRevamped
          }), (0, _jsxRuntime.jsx)(_component.HeroBanner, {}), isShopDineDealPromo && (0, _jsxRuntime.jsx)(_component.DealsPromos, {
            data: dataDealsPromos,
            isLoading: isLoadingDealsPromos,
            isError: isErrorDealsPromos || !isLoadingDealsPromos && !(dataDealsPromos != null && (_dataDealsPromos$prom = dataDealsPromos.promos) != null && _dataDealsPromos$prom.length),
            navigation: navigation,
            onReload: getDataDealsPromos
          }), (0, _utils.handleCondition)(isJustForYouV2, (0, _jsxRuntime.jsx)(_component.ViewJustForYouV2, {
            dataJustForYou: dataJustForYou,
            isLoadingJustForYou: isLoadingJustForYou,
            isErrorJustForYou: isErrorJustForYou
          }), (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: _styles.styles.viewJustForYou,
            children: (0, _jsxRuntime.jsx)(_brandOffer.BrandOffer, {
              styleContainerProps: _styles.styles.marginBottom,
              handleClickProps: handleClickBrandOffer,
              isShowError: false
            })
          })), (0, _jsxRuntime.jsx)(_component.DineGuides, {
            dataDineGuides: dataDineGuides,
            isLoadingDineGuides: isLoadingDineGuides,
            isErrorDineGuides: isErrorDineGuides
          }), (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: _styles.styles.viewBottom
          })]
        })
      })
    });
  };
