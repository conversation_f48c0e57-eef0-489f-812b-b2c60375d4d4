  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useFunctions = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _$$_REQUIRE(_dependencyMap[3]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _request = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _envParams = _$$_REQUIRE(_dependencyMap[6]);
  var _awsAmplify = _$$_REQUIRE(_dependencyMap[7]);
  var _queries = _$$_REQUIRE(_dependencyMap[8]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[9]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[10]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[11]);
  var _adobe = _$$_REQUIRE(_dependencyMap[12]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[13]);
  var _sha2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _utils = _$$_REQUIRE(_dependencyMap[15]);
  var _shopRedux = _$$_REQUIRE(_dependencyMap[16]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[17]);
  var _forYouRedux = _$$_REQUIRE(_dependencyMap[18]);
  var _aemQuicklinkCategories = _$$_REQUIRE(_dependencyMap[19]);
  var _useFetchQuickLinks2 = _$$_REQUIRE(_dependencyMap[20]);
  var useFunctions = exports.useFunctions = function useFunctions() {
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)(),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var isShopDineV2JustForYou = (0, _remoteConfig.getFeatureFlagInit)(_remoteConfig.REMOTE_CONFIG_FLAGS.SHOPDINE_V2_JUSTFORYOU);
    var isShopDineDealPromo = (0, _remoteConfig.getFeatureFlagInit)(_remoteConfig.REMOTE_CONFIG_FLAGS.SHOPDINE_DEALPROMO);
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var recommendedProductsPayload = (0, _reactRedux.useSelector)(_shopRedux.ShopSelectors.recommendedProductsPayload);
    var isJustForYouV2 = isShopDineV2JustForYou && isLoggedIn;
    var rewardsData = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.rewardsData);
    var _useState = (0, _react.useState)(null),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      dataDealsPromos = _useState2[0],
      setDataDealsPromos = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isLoadingDealsPromos = _useState4[0],
      setIsLoadingDealsPromos = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isErrorDealsPromos = _useState6[0],
      setIsErrorDealsPromos = _useState6[1];
    var _useState7 = (0, _react.useState)(null),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      dataJustForYou = _useState8[0],
      setDataJustForYou = _useState8[1];
    var _useState9 = (0, _react.useState)(false),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      isLoadingJustForYou = _useState0[0],
      setIsLoadingJustForYou = _useState0[1];
    var _useState1 = (0, _react.useState)(false),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      isErrorJustForYou = _useState10[0],
      setIsErrorJustForYou = _useState10[1];
    var _useState11 = (0, _react.useState)(null),
      _useState12 = (0, _slicedToArray2.default)(_useState11, 2),
      dataDineGuides = _useState12[0],
      setDataDineGuides = _useState12[1];
    var _useState13 = (0, _react.useState)(false),
      _useState14 = (0, _slicedToArray2.default)(_useState13, 2),
      isLoadingDineGuides = _useState14[0],
      setIsLoadingDineGuides = _useState14[1];
    var _useState15 = (0, _react.useState)(false),
      _useState16 = (0, _slicedToArray2.default)(_useState15, 2),
      isErrorDineGuides = _useState16[0],
      setIsErrorDineGuides = _useState16[1];
    var _useState17 = (0, _react.useState)(false),
      _useState18 = (0, _slicedToArray2.default)(_useState17, 2),
      showNoInternetError = _useState18[0],
      setShowNoInternetError = _useState18[1];
    var _useFetchQuickLinks = (0, _useFetchQuickLinks2.useFetchQuickLinks)(_aemQuicklinkCategories.ScreenType.DineAndShop),
      dataDineAndShopQuicklinks = _useFetchQuickLinks.data,
      isLoadingDineAndShopQuicklinks = _useFetchQuickLinks.loading,
      isErrorDineAndShopQuicklinks = _useFetchQuickLinks.error,
      handleRefetchQuicklinks = _useFetchQuickLinks.refetch,
      isQuickLinksTransformEnabled = _useFetchQuickLinks.isQuickLinksTransformEnabled;
    (0, _react.useEffect)(function () {
      checkInternet();
    }, [profilePayload == null ? undefined : profilePayload.airportStaff]);
    var checkInternet = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        if (!isConnected) {
          setShowNoInternetError(true);
          // stop loading indicators if any in progress
          setIsLoadingDealsPromos(false);
          setIsLoadingJustForYou(false);
          setIsLoadingDineGuides(false);
          return;
        }
        setShowNoInternetError(false);
        // Fetch all data when internet is available
        getDataDealsPromos();
        getDataJustForYou();
        getDataDineGuides();
      });
      return function checkInternet() {
        return _ref.apply(this, arguments);
      };
    }();
    var getDataJustForYou = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        if (isJustForYouV2) {
          var _sha, _profilePayload$email;
          setIsErrorJustForYou(false);
          setIsLoadingJustForYou(true);
          var response = yield (0, _adobe.adobeRetrieveLocationContent)(Object.assign({
            login_status: profilePayload ? 0 : 1,
            uid: profilePayload == null ? undefined : profilePayload.id,
            hashed_email: profilePayload != null && profilePayload.email ? (_sha = (0, _sha2.default)(profilePayload == null || (_profilePayload$email = profilePayload.email) == null ? undefined : _profilePayload$email.toLowerCase())) == null ? undefined : _sha.toString() : undefined
          }, (0, _utils.simpleCondition)({
            condition: recommendedProductsPayload == null ? undefined : recommendedProductsPayload.profile_parameters,
            ifValue: recommendedProductsPayload == null ? undefined : recommendedProductsPayload.profile_parameters,
            elseValue: {}
          })), (0, _utils.simpleCondition)({
            condition: recommendedProductsPayload == null ? undefined : recommendedProductsPayload.parameters,
            ifValue: recommendedProductsPayload == null ? undefined : recommendedProductsPayload.parameters,
            elseValue: {}
          }), recommendedProductsPayload == null ? undefined : recommendedProductsPayload.m_box_name);
          if (response && response !== _adobe.DEFAULT_LOCATION_CONTENT) {
            try {
              var jsonObject = JSON.parse(response);
              setIsLoadingJustForYou(false);
              if (jsonObject != null && jsonObject.code) {
                setIsErrorJustForYou(true);
              } else {
                setDataJustForYou(jsonObject);
              }
            } catch (error) {
              setIsLoadingJustForYou(false);
              setIsErrorJustForYou(true);
            }
          } else {
            setIsLoadingJustForYou(false);
            setDataJustForYou(null);
          }
        }
      });
      return function getDataJustForYou() {
        return _ref2.apply(this, arguments);
      };
    }();
    var getDataDealsPromos = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* () {
        try {
          var _env, _env2;
          setIsLoadingDealsPromos(true);
          var input = {
            page_number: 1,
            page_size: 8,
            is_airport_staff: !!(profilePayload != null && profilePayload.airportStaff)
          };
          var response = yield (0, _request.default)({
            url: (_env = (0, _envParams.env)()) == null ? undefined : _env.APPSYNC_GRAPHQL_URL,
            method: "post",
            data: (0, _awsAmplify.graphqlOperation)(_queries.getDealsPromos, {
              input: input
            }),
            parameters: {},
            headers: {
              "x-api-key": (_env2 = (0, _envParams.env)()) == null ? undefined : _env2.APPSYNC_GRAPHQL_API_KEY
            }
          });
          setIsLoadingDealsPromos(false);
          if (response != null && response.data) {
            var _response$data;
            setDataDealsPromos(response == null || (_response$data = response.data) == null || (_response$data = _response$data.data) == null ? undefined : _response$data.getDealsPromosListing);
          } else {
            setIsErrorDealsPromos(true);
          }
        } catch (error) {
          setIsLoadingDealsPromos(false);
          setIsErrorDealsPromos(true);
        }
      });
      return function getDataDealsPromos() {
        return _ref3.apply(this, arguments);
      };
    }();
    var getDataDineGuides = /*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* () {
        try {
          var _env3, _env4, _response$data2;
          setIsLoadingDineGuides(true);
          var response = yield (0, _request.default)({
            url: (_env3 = (0, _envParams.env)()) == null ? undefined : _env3.APPSYNC_GRAPHQL_URL,
            method: "post",
            data: (0, _awsAmplify.graphqlOperation)(_queries.listAEMDine),
            parameters: {},
            headers: {
              "x-api-key": (_env4 = (0, _envParams.env)()) == null ? undefined : _env4.APPSYNC_GRAPHQL_API_KEY
            }
          });
          setIsLoadingDineGuides(false);
          if (response != null && (_response$data2 = response.data) != null && (_response$data2 = _response$data2.data) != null && _response$data2.listAEMDine) {
            var _response$data3;
            setDataDineGuides(response == null || (_response$data3 = response.data) == null || (_response$data3 = _response$data3.data) == null ? undefined : _response$data3.listAEMDine);
          } else {
            setIsErrorDineGuides(true);
          }
        } catch (error) {
          setIsLoadingDineGuides(false);
          setIsErrorDineGuides(true);
        }
      });
      return function getDataDineGuides() {
        return _ref4.apply(this, arguments);
      };
    }();
    var handleClickBrandOffer = function handleClickBrandOffer(item, index) {
      var _ref5 = item || {},
        navigationType = _ref5.navigationType,
        navigationValue = _ref5.navigationValue,
        redirect = _ref5.redirect;
      if (!navigationType || !navigationValue) return;
      handleNavigation(navigationType, navigationValue, redirect);
    };
    return {
      dataDineAndShopQuicklinks: dataDineAndShopQuicklinks,
      isLoadingDineAndShopQuicklinks: isLoadingDineAndShopQuicklinks,
      isErrorDineAndShopQuicklinks: isErrorDineAndShopQuicklinks,
      dataDealsPromos: dataDealsPromos,
      isLoadingDealsPromos: isLoadingDealsPromos,
      isErrorDealsPromos: isErrorDealsPromos,
      getDataDealsPromos: getDataDealsPromos,
      dataDineGuides: dataDineGuides,
      isLoadingDineGuides: isLoadingDineGuides,
      isErrorDineGuides: isErrorDineGuides,
      isJustForYouV2: isJustForYouV2,
      dataJustForYou: dataJustForYou,
      isLoadingJustForYou: isLoadingJustForYou,
      isErrorJustForYou: isErrorJustForYou,
      handleClickBrandOffer: handleClickBrandOffer,
      showNoInternetError: showNoInternetError,
      checkInternet: checkInternet,
      isShopDineDealPromo: isShopDineDealPromo,
      handleRefetchQuicklinks: handleRefetchQuicklinks,
      rewardsData: rewardsData,
      profilePayload: profilePayload,
      isQuickLinksTransformEnabled: isQuickLinksTransformEnabled
    };
  };
