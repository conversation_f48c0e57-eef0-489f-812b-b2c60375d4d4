  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _color = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var widthScreen = _reactNative.Dimensions.get("window").width;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    background: {
      backgroundColor: _color.color.palette.whiteGrey
    },
    container: {
      width: widthScreen,
      backgroundColor: _color.color.palette.whiteGrey,
      paddingTop: 40
    },
    viewJustForYou: {
      width: "100%"
    },
    viewBottom: {
      marginBottom: 100
    },
    marginBottom: {
      marginBottom: 0,
      marginTop: 50
    },
    noInternetReloadButtonStyle: {
      width: "100%"
    },
    reloadButtonTextStyle: {
      lineHeight: 20
    },
    skeletonContainer: {
      paddingHorizontal: 25,
      marginBottom: 32
    },
    quickLinksContainer: {
      marginBottom: 16,
      marginTop: -16,
      backgroundColor: "transparent"
    }
  });
