  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ViewJustForYouV2 = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _color = _$$_REQUIRE(_dependencyMap[4]);
  var _typography = _$$_REQUIRE(_dependencyMap[5]);
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[8]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _miffyGamificationBanner = _$$_REQUIRE(_dependencyMap[10]);
  var _constants = _$$_REQUIRE(_dependencyMap[11]);
  var _adobe = _$$_REQUIRE(_dependencyMap[12]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  var ViewJustForYouV2Loading = _react.default.memo(function () {
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: styles.viewLoading,
      children: [1, 2, 3].map(function (item) {
        return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.viewItemLoading,
          children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
            shimmerStyle: styles.imageLoading
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
            shimmerStyle: styles.loadingFirst
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
            shimmerStyle: styles.loadingFirst
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
            shimmerStyle: [styles.loadingFirst, {
              width: 60
            }]
          })]
        }, item);
      })
    });
  });
  var ViewJustForYouV2 = exports.ViewJustForYouV2 = function ViewJustForYouV2(props) {
    var dataJustForYou = props.dataJustForYou,
      isLoadingJustForYou = props.isLoadingJustForYou,
      isErrorJustForYou = props.isErrorJustForYou;
    if (isErrorJustForYou || !isLoadingJustForYou && !(dataJustForYou != null && dataJustForYou.length)) {
      return null;
    }
    var renderContent = function renderContent() {
      if (isLoadingJustForYou) {
        return (0, _jsxRuntime.jsx)(ViewJustForYouV2Loading, {});
      }
      return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {});
    };
    var onPressSeeAll = function onPressSeeAll() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppDineLanding, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppDineLanding, `Just for You | Shop All`));
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.container,
      children: [(dataJustForYou || isLoadingJustForYou) && (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.viewTitle,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          tx: "dineScreenV2.justForYou.title",
          style: styles.txtTitle
        }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
          style: styles.viewRow,
          onPress: onPressSeeAll,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            tx: "dineScreenV2.justForYou.seeAll",
            style: styles.txtButton
          }), (0, _jsxRuntime.jsx)(_icons.ArrowRightV2, {
            color: _color.color.palette.lightPurple
          })]
        })]
      }), renderContent()]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    container: {
      width: '100%'
    },
    viewTitle: {
      paddingHorizontal: 20,
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginTop: 50
    },
    viewRow: {
      flexDirection: "row",
      alignItems: "center"
    },
    txtTitle: {
      fontFamily: _typography.typography.bold,
      color: _color.color.palette.darkestGrey,
      fontSize: 12,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 16
    },
    txtButton: {
      fontFamily: _typography.typography.bold,
      color: _color.color.palette.lightPurple,
      fontSize: 12,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 16,
      marginRight: 8
    },
    viewLoading: {
      width: '100%',
      marginTop: 12,
      flexDirection: 'row',
      gap: 8,
      paddingLeft: 20
    },
    viewItemLoading: {
      height: 220,
      borderWidth: 1,
      borderRadius: 8,
      borderColor: _color.color.palette.lightGrey,
      padding: 12
    },
    imageLoading: {
      width: 96,
      height: 80,
      marginBottom: 8
    },
    loadingFirst: {
      width: 96,
      height: 12,
      borderRadius: 4,
      marginBottom: 12
    }
  });
