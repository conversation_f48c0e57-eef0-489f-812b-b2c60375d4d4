  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.HeroBanner = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _imageCarousel = _$$_REQUIRE(_dependencyMap[6]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[7]);
  var _mainPromo = _$$_REQUIRE(_dependencyMap[8]);
  var _constants = _$$_REQUIRE(_dependencyMap[9]);
  var _theme = _$$_REQUIRE(_dependencyMap[10]);
  var _envParams = _$$_REQUIRE(_dependencyMap[11]);
  var _request = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _awsAmplify = _$$_REQUIRE(_dependencyMap[13]);
  var _queries = _$$_REQUIRE(_dependencyMap[14]);
  var _native = _$$_REQUIRE(_dependencyMap[15]);
  var _adobe = _$$_REQUIRE(_dependencyMap[16]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[17]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var PADDING_VERTICAL = 16;
  var IMAGE_RATIO = 1.5779816513761469;
  var imageWidth = width - 32;
  var HeroBanner = exports.HeroBanner = function HeroBanner() {
    var navigation = (0, _native.useNavigation)();
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)(),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var _useState = (0, _react.useState)(true),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isLoading = _useState2[0],
      setIsLoading = _useState2[1];
    var _useState3 = (0, _react.useState)([]),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      promotions = _useState4[0],
      setPromotions = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      hasError = _useState6[0],
      setHasError = _useState6[1];
    var carouselType = isLoading ? "loading" : "default";
    var onPress = function onPress(item) {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppDineLanding, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppDineLanding, `Banner | ${item == null ? undefined : item.promoTitle}`));
      var _ref = item || "",
        navigationType = _ref.navigationType,
        navigationValue = _ref.navigationValue,
        redirect = _ref.redirect;
      if (!navigationType || !navigationValue) return null;
      if (item.navigationType === _mainPromo.NavigationType.inapp && item.navigationValue === "offerDetails") {
        navigation.navigate(_constants.NavigationConstants.dineShopOfferDetailsScreen, {
          offerId: item == null ? undefined : item.offerId
        });
        return;
      }
      handleNavigation(navigationType, navigationValue, redirect);
    };
    (0, _react.useEffect)(function () {
      var getMainPromo = /*#__PURE__*/function () {
        var _ref2 = (0, _asyncToGenerator2.default)(function* () {
          setIsLoading(true);
          try {
            var _env, _env2, _response$data, _response$data2;
            var response = yield (0, _request.default)({
              url: (_env = (0, _envParams.env)()) == null ? undefined : _env.APPSYNC_GRAPHQL_URL,
              method: "post",
              data: (0, _awsAmplify.graphqlOperation)(_queries.mainPromoQuery, {
                type: ""
              }),
              parameters: {},
              headers: {
                "x-api-key": (_env2 = (0, _envParams.env)()) == null ? undefined : _env2.APPSYNC_GRAPHQL_API_KEY
              }
            });
            var data = (response == null || (_response$data = response.data) == null || (_response$data = _response$data.data) == null ? undefined : _response$data.getMainPromoDineShop) || [];
            if (response != null && (_response$data2 = response.data) != null && (_response$data2 = _response$data2.data) != null && _response$data2.getMainPromoDineShop) {
              var sortedPromotions = data.sort(function (a, b) {
                return a.orderId - b.orderId;
              });
              setPromotions(sortedPromotions);
            }
          } catch (error) {
            setHasError(true);
          } finally {
            setIsLoading(false);
          }
        });
        return function getMainPromo() {
          return _ref2.apply(this, arguments);
        };
      }();
      _reactNative.InteractionManager.runAfterInteractions(function () {
        getMainPromo();
      });
    }, []);
    if (hasError || !isLoading && !(promotions != null && promotions.length)) {
      return null;
    }
    return (0, _jsxRuntime.jsx)(_imageCarousel.ImageCarousel, {
      onPressed: onPress,
      type: carouselType,
      promotions: promotions,
      containerStyle: styles.carouselContainer,
      backgroundImageStyle: styles.backgroundImageStyle,
      carouselContainerStyle: styles.carouselContainerStyle
    });
  };
  var styles = _reactNative.StyleSheet.create({
    carouselContainer: {
      backgroundColor: _theme.color.palette.whiteGrey
    },
    carouselContainerStyle: {
      paddingHorizontal: 16,
      backgroundColor: _theme.color.palette.whiteGrey
    },
    backgroundImageStyle: {
      width: imageWidth,
      height: imageWidth / IMAGE_RATIO
    }
  });
