  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = exports.loadingElementsLayout = exports.lightGreyLoadingColors = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _Dimensions$get = _reactNative.Dimensions.get("screen"),
    width = _Dimensions$get.width;
  var PADDING_HORIZONTAL_CAROUSEL = 16;
  var WIDTH_IMAGE = width - 32;
  var RATIO = 1.5779816513761469; // Follow base ratio from figma: Width: 344 and Height: 218
  var HEIGHT_IMAGE = WIDTH_IMAGE / RATIO;
  var loadingElementsLayout = exports.loadingElementsLayout = [{
    borderRadius: 16,
    height: 218,
    width: "100%"
  }, {
    height: 16,
    borderRadius: 4,
    width: `${240 / WIDTH_IMAGE * 100}%`
  }];
  var lightGreyLoadingColors = exports.lightGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var styles = exports.styles = _reactNative.StyleSheet.create({
    container: {
      paddingHorizontal: PADDING_HORIZONTAL_CAROUSEL
    },
    backgroundImageStyle: {
      borderRadius: 16,
      height: HEIGHT_IMAGE,
      width: WIDTH_IMAGE
    },
    carouselContainer: {
      height: HEIGHT_IMAGE,
      backgroundColor: _theme.color.palette.whiteGrey
    },
    containerStyle: {
      backgroundColor: _theme.color.palette.whiteGrey
    },
    contentCarouselContainer: {
      marginTop: 12,
      width: "100%",
      paddingHorizontal: 4
    },
    contentContainerLayout: {
      justifyContent: "center",
      marginEnd: 24,
      marginStart: 24,
      marginTop: -102
    },
    descriptionStyle: {
      color: _theme.color.palette.almostBlackGrey,
      textTransform: "none"
    },
    lowerCaseTitle: {
      letterSpacing: 0,
      textTransform: "none"
    },
    paginationStyle: {
      bottom: 12,
      position: "absolute",
      right: 25
    },
    paginationContent: {
      paddingHorizontal: 16,
      paddingVertical: 4,
      backgroundColor: "rgba(18, 18, 18, 0.8)",
      borderRadius: 16,
      flex: 1,
      alignSelf: "flex-start"
    },
    paginationTextStyle: {
      color: _theme.color.palette.whiteGrey
    },
    subCopyStyle: {
      color: _theme.color.palette.almostBlackGrey
    },
    titleStyle: {
      color: _theme.color.palette.almostBlackGrey,
      marginBottom: 4,
      textTransform: "none"
    }
  });
