  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ImageCarousel = ImageCarousel;
  exports.NavigationType = exports.MainPromoType = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var React = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNativeReanimatedCarousel = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _constants = _$$_REQUIRE(_dependencyMap[8]);
  var _imageCarousel = _$$_REQUIRE(_dependencyMap[9]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _Dimensions$get = _reactNative2.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var COMPONENT_NAME = "MainPromo";
  var PaginationIndicator = function PaginationIndicator(_ref) {
    var label = _ref.label;
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: _imageCarousel.styles.paginationContent,
      children: (0, _jsxRuntime.jsx)(_text.Text, {
        preset: "caption2Bold",
        style: _imageCarousel.styles.paginationTextStyle,
        children: label
      })
    });
  };
  var loadingView = function loadingView() {
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _imageCarousel.styles.container,
      children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        shimmerColors: _imageCarousel.lightGreyLoadingColors,
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerStyle: _imageCarousel.loadingElementsLayout[0]
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _imageCarousel.styles.contentCarouselContainer,
        children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          shimmerColors: _imageCarousel.lightGreyLoadingColors,
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerStyle: _imageCarousel.loadingElementsLayout[1]
        })
      })]
    });
  };
  var contentView = function contentView(sortedPromotions, onPressed, testID, accessibilityLabel) {
    var itemWidth = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : width - 32;
    var _React$useState = React.useState(0),
      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),
      index = _React$useState2[0],
      setIndex = _React$useState2[1];
    var pagingText = index + 1 + "/" + (sortedPromotions == null ? undefined : sortedPromotions.length);
    var onSnapToItem = function onSnapToItem(index) {
      setIndex(index);
    };
    var renderCarouselItem = function renderCarouselItem(_ref2) {
      var item = _ref2.item,
        indexs = _ref2.index;
      return (0, _jsxRuntime.jsxs)(_reactNative.Pressable, {
        onPress: function onPress() {
          return onPressed == null ? undefined : onPressed(item);
        },
        testID: `${testID}__PressablePromotions__${indexs}`,
        accessibilityLabel: `${accessibilityLabel}__PressablePromotions__${indexs}`,
        accessible: false,
        children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
          style: _imageCarousel.styles.backgroundImageStyle,
          source: {
            uri: item == null ? undefined : item.image
          },
          resizeMode: "cover",
          testID: `${COMPONENT_NAME}__Image`,
          accessibilityLabel: item == null ? undefined : item.image
        }), (sortedPromotions == null ? undefined : sortedPromotions.length) > 1 && (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _imageCarousel.styles.paginationStyle,
          children: (0, _jsxRuntime.jsx)(PaginationIndicator, {
            label: pagingText
          })
        })]
      }, `${item == null ? undefined : item.orderId}-${indexs}`);
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _imageCarousel.styles.container,
      children: [(0, _jsxRuntime.jsx)(_reactNativeReanimatedCarousel.default, {
        loop: true,
        data: sortedPromotions,
        style: _imageCarousel.styles.carouselContainer,
        renderItem: renderCarouselItem,
        width: itemWidth,
        testID: `${testID}__Carousel`,
        onSnapToItem: onSnapToItem
        // Lower the swipe threshold to make switching items easier
        ,
        minScrollDistancePerSwipe: 3,
        onConfigurePanGesture: function onConfigurePanGesture(panGesture) {
          // Favor horizontal swipes; don't force failure on small vertical jitters
          panGesture.activeOffsetX([-8, 8]);
        }
      }), sortedPromotions.length > 0 && (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: _imageCarousel.styles.contentCarouselContainer,
        onPress: function onPress() {
          return onPressed == null ? undefined : onPressed(sortedPromotions[index]);
        },
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          preset: "bodyTextBold",
          style: _imageCarousel.styles.titleStyle,
          numberOfLines: 2,
          text: sortedPromotions[index].promoTitle,
          testID: `${COMPONENT_NAME}__Title`,
          accessibilityLabel: sortedPromotions[index].promoTitle
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "caption1Regular",
          style: _imageCarousel.styles.descriptionStyle,
          numberOfLines: 1,
          text: sortedPromotions[index].subCopy,
          testID: `${COMPONENT_NAME}__SubCopy`,
          accessibilityLabel: sortedPromotions[index].subCopy
        })]
      })]
    });
  };
  var NavigationType = exports.NavigationType = /*#__PURE__*/function (NavigationType) {
    NavigationType["inapp"] = "in-app";
    NavigationType["external"] = "external";
    return NavigationType;
  }({});
  var MainPromoType = exports.MainPromoType = /*#__PURE__*/function (MainPromoType) {
    MainPromoType["default"] = "default";
    MainPromoType["loading"] = "loading";
    return MainPromoType;
  }({});
  function ImageCarousel(props) {
    var promotions = props.promotions,
      type = props.type,
      onPressed = props.onPressed,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "MainPromo" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "MainPromo" : _props$accessibilityL,
      containerStyle = props.containerStyle;
    var isLoading = type === MainPromoType.loading;
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: [_imageCarousel.styles.containerStyle, containerStyle],
      children: isLoading ? loadingView() : contentView(promotions, onPressed, testID, accessibilityLabel)
    });
  }
