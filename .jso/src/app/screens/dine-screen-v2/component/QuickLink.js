  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.QuickLink = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _icons = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _i18n = _$$_REQUIRE(_dependencyMap[8]);
  var _text = _$$_REQUIRE(_dependencyMap[9]);
  var _theme = _$$_REQUIRE(_dependencyMap[10]);
  var _native = _$$_REQUIRE(_dependencyMap[11]);
  var _constants = _$$_REQUIRE(_dependencyMap[12]);
  var _adobe = _$$_REQUIRE(_dependencyMap[13]);
  var _pageConfigSaga = _$$_REQUIRE(_dependencyMap[14]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[16]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var idData = /*#__PURE__*/function (idData) {
    idData[idData["QLdineShop"] = 1] = "QLdineShop";
    idData[idData["QLShopping"] = 2] = "QLShopping";
    idData[idData["QLtraveller"] = 3] = "QLtraveller";
    idData[idData["QLnonTraveller"] = 4] = "QLnonTraveller";
    return idData;
  }(idData || {});
  var lightGreyLoadingColors = [_theme.color.palette.lightGrey, _theme.color.background, _theme.color.palette.lightGrey];
  var lighterGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var data = [{
    id: idData.QLdineShop,
    icon: (0, _jsxRuntime.jsx)(_icons.QLdineShop, {}),
    title: (0, _i18n.translate)("dineScreenV2.quickLink.dsDirectory")
  }, {
    id: idData.QLShopping,
    icon: (0, _jsxRuntime.jsx)(_icons.QLShopping, {}),
    title: (0, _i18n.translate)("dineScreenV2.quickLink.spConcierge"),
    value: "https://changi.me/sccaf"
  }, {
    id: idData.QLtraveller,
    icon: (0, _jsxRuntime.jsx)(_icons.QLtraveller, {}),
    title: (0, _i18n.translate)("dineScreenV2.quickLink.traveller"),
    value: "https://www.ishopchangi.com/en?cmode=TRDEP",
    fullParams: "/en?cmode=TRDEP&utm_source=ichangi_app&utm_medium=dine-shop-icon&utm_content=isc-tr&utm_campaign=alwayson&utm_term=general"
  }, {
    id: idData.QLnonTraveller,
    icon: (0, _jsxRuntime.jsx)(_icons.QLnonTraveller, {}),
    title: (0, _i18n.translate)("dineScreenV2.quickLink.nonTraveller"),
    value: "https://www.ishopchangi.com/en?cmode=NTDEL",
    fullParams: "/en?cmode=NTDEL&utm_source=ichangi_app&utm_medium=dine-shop-icon&utm_content=isc-nt&utm_campaign=alwayson&utm_term=general"
  }];
  var QuickLink = exports.QuickLink = _react.default.memo(function () {
    var navigation = (0, _native.useNavigation)();
    var _useState = (0, _react.useState)(true),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isLoading = _useState2[0],
      setIsLoading = _useState2[1];
    (0, _react.useEffect)(function () {
      var timer = setTimeout(function () {
        return setIsLoading(false);
      }, 2000);
      return function () {
        return clearTimeout(timer);
      };
    }, []);
    var navigateFromCSM = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* (type, item) {
        try {
          var payload = {
            stateCode: _constants.StateCode == null ? undefined : _constants.StateCode.ISHOPCHANGI,
            input: {
              redirectTarget: type,
              ecid: yield (0, _adobe.getExperienceCloudId)()
            }
          };
          var getLink = yield (0, _pageConfigSaga.getDeepLinkV2)(payload, true);
          navigation.navigate(_constants.NavigationConstants.webview, {
            uri: getLink == null ? undefined : getLink.redirectUri,
            basicAuthCredential: getLink == null ? undefined : getLink.basicAuth
          });
        } catch (error) {
          navigation.navigate(_constants.NavigationConstants.webview, {
            uri: item == null ? undefined : item.value
          });
        }
      });
      return function navigateFromCSM(_x, _x2) {
        return _ref.apply(this, arguments);
      };
    }();
    var _onPress = function onPress(item) {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppDineLanding, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppDineLanding, `Quicklinks | ${item == null ? undefined : item.title}`));
      if ((item == null ? undefined : item.id) === idData.QLdineShop) {
        navigation.navigate(_constants.NavigationConstants.DineShopDirectory);
      } else if ((item == null ? undefined : item.id) === idData.QLShopping) {
        _reactNative2.Linking.openURL(item == null ? undefined : item.value);
      } else {
        navigateFromCSM(item == null ? undefined : item.fullParams, item);
      }
    };
    if (isLoading) {
      var placeholders = new Array(5).fill(0);
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.container,
        children: placeholders.map(function (_, index) {
          return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.skeletonItemContainer,
            children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: lightGreyLoadingColors,
              shimmerStyle: styles.skeletonIcon
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: lighterGreyLoadingColors,
              shimmerStyle: styles.skeletonText
            })]
          }, index);
        })
      });
    }
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: styles.container,
      children: data.map(function (item) {
        return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
          style: {
            alignItems: "center"
          },
          onPress: function onPress() {
            return _onPress(item);
          },
          children: [item.icon, (0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.txtTitle,
            children: item.title
          })]
        }, item.id);
      })
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    container: {
      width: "100%",
      flexDirection: "row",
      paddingHorizontal: 25,
      marginBottom: 32,
      justifyContent: "space-around"
    },
    skeletonItemContainer: {
      alignItems: "center"
    },
    skeletonIcon: {
      width: 24,
      height: 24,
      borderRadius: 8
    },
    skeletonText: {
      width: 48,
      height: 12,
      borderRadius: 4,
      marginTop: 8
    },
    txtTitle: {
      fontFamily: _theme.typography.regular,
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 10,
      fontWeight: _reactNative2.Platform.select({
        ios: "400",
        android: "normal"
      }),
      lineHeight: 12,
      textAlign: "center",
      marginTop: 8
    }
  });
