  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.toastTextStyle = exports.toastStyle = exports.toastButtonStyle = exports.sortByTextStyle = exports.scrollViewParentContainer = exports.radioTextStyle = exports.radioOptionsTextStyle = exports.radioOptionsOuterContainerStyle = exports.radioOptionsInnerContainerStyle = exports.positionStyle = exports.pillsContainer = exports.parentContainer = exports.outerRadioRingStyle = exports.outerRadioGrayStyle = exports.noResultsText = exports.noResultsMessageText = exports.noResultsMessageContainer = exports.noResultsContainer = exports.innerRadioCircleStyle = exports.imageEmptyState = exports.headerSortBy = exports.flatListStyle = exports.flatListItemsStyle = exports.flatListContentContainerStyle = exports.emptyStateButtonStyle = exports.emptyResultMessage = exports.dividerStyle = exports.container = exports.closeIcon = exports.chipParentContainer = exports.buttonResults = exports.buttonReselect = exports.buttonGradient = exports.bottomSheetStyle = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNativeSizeMatters = _$$_REQUIRE(_dependencyMap[3]);
  var _Dimensions$get = _reactNative.Dimensions.get("screen"),
    width = _Dimensions$get.width;
  var container = exports.container = {
    backgroundColor: _theme.color.palette.whiteGrey,
    flex: 1
  };
  var parentContainer = exports.parentContainer = {
    backgroundColor: _theme.color.palette.lightestGrey,
    flex: 1
  };
  var pillsContainer = exports.pillsContainer = {
    height: 28,
    alignItems: "center",
    justifyContent: "flex-start",
    marginLeft: 0,
    marginTop: 28
  };
  var scrollViewParentContainer = exports.scrollViewParentContainer = {
    width: "100%",
    alignItems: "flex-start"
  };
  var chipParentContainer = exports.chipParentContainer = {
    height: "100%",
    justifyContent: "center",
    marginRight: 12,
    marginLeft: 24,
    alignSelf: "flex-start"
  };
  var outerRadioRingStyle = exports.outerRadioRingStyle = {
    height: 24,
    width: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: _theme.color.palette.lightPurple,
    alignItems: "center",
    justifyContent: "center"
  };
  var outerRadioGrayStyle = exports.outerRadioGrayStyle = {
    height: 24,
    width: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: _theme.color.palette.darkGrey,
    alignItems: "center",
    justifyContent: "center"
  };
  var innerRadioCircleStyle = exports.innerRadioCircleStyle = {
    height: 12,
    width: 12,
    borderRadius: 6,
    backgroundColor: _theme.color.palette.lightPurple
  };
  var sortByTextStyle = exports.sortByTextStyle = {
    textAlign: "center",
    lineHeight: 28,
    fontStyle: "normal"
  };
  var radioOptionsOuterContainerStyle = exports.radioOptionsOuterContainerStyle = {
    marginHorizontal: 24,
    marginTop: 21,
    marginBottom: 60
  };
  var radioOptionsInnerContainerStyle = exports.radioOptionsInnerContainerStyle = {
    flexDirection: "row",
    paddingVertical: 18,
    borderBottomWidth: 1,
    borderBottomColor: _theme.color.palette.lighterGrey
  };
  var radioOptionsTextStyle = exports.radioOptionsTextStyle = {
    marginLeft: 16
  };
  var radioTextStyle = exports.radioTextStyle = {
    color: _theme.color.palette.almostBlackGrey,
    fontSize: 16,
    fontWeight: "400",
    fontFamily: "Lato",
    fontStyle: "normal"
  };
  var headerSortBy = exports.headerSortBy = {
    marginTop: 21,
    justifyContent: "space-between",
    alignItems: "center",
    flexDirection: "row",
    paddingHorizontal: 24
  };
  var closeIcon = exports.closeIcon = {
    justifyContent: "center"
  };
  var bottomSheetStyle = exports.bottomSheetStyle = {
    height: "auto",
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    backgroundColor: _theme.color.palette.whiteGrey
  };
  var flatListStyle = exports.flatListStyle = {
    paddingTop: 10,
    paddingHorizontal: 24
  };
  var dividerStyle = exports.dividerStyle = {
    height: 1,
    backgroundColor: _theme.color.palette.lighterGrey
  };
  var flatListItemsStyle = exports.flatListItemsStyle = {
    paddingVertical: 16
  };
  var flatListContentContainerStyle = exports.flatListContentContainerStyle = {
    paddingBottom: 24
  };
  var noResultsContainer = exports.noResultsContainer = {
    marginTop: (0, _reactNativeSizeMatters.scale)(40),
    justifyContent: "center",
    alignItems: "center"
  };
  var noResultsText = exports.noResultsText = Object.assign({}, _text.presets.h2Tabs, {
    lineHeight: 28,
    marginBottom: 16
  });
  var noResultsMessageContainer = exports.noResultsMessageContainer = {
    marginBottom: 16,
    marginHorizontal: 24
  };
  var noResultsMessageText = exports.noResultsMessageText = Object.assign({}, _text.presets.caption1Regular, {
    textAlign: "center"
  });
  var buttonResults = exports.buttonResults = {
    color: _theme.color.palette.almostWhiteGrey
  };
  var toastStyle = exports.toastStyle = {
    backgroundColor: _theme.color.palette.black,
    width: "95%",
    height: 60,
    borderRadius: 8,
    marginBottom: 20,
    alignItems: "flex-start"
  };
  var toastButtonStyle = exports.toastButtonStyle = Object.assign({}, _text.presets.textLink, {
    fontWeight: "normal",
    color: _theme.color.palette.lightBlue,
    alignItems: "flex-end"
  });
  var toastTextStyle = exports.toastTextStyle = Object.assign({}, _text.presets.bodyTextRegular, {
    color: _theme.color.palette.whiteGrey,
    width: "80%"
  });
  var positionStyle = exports.positionStyle = {
    bottom: 30
  };
  var emptyStateButtonStyle = exports.emptyStateButtonStyle = {
    // width: scale(156),
    width: "100%",
    marginVertical: 24
  };
  var imageEmptyState = exports.imageEmptyState = {
    width: 327,
    height: 140,
    marginBottom: 19
  };
  var buttonGradient = exports.buttonGradient = {
    backgroundColor: _theme.color.palette.basePurple,
    borderRadius: 60,
    width: width - 48
  };
  var buttonReselect = exports.buttonReselect = {
    borderColor: "transparent"
  };
  var emptyResultMessage = exports.emptyResultMessage = {
    margin: 24,
    color: _theme.color.palette.almostBlackGrey
  };
