  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ChipFilterResult = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _chip = _$$_REQUIRE(_dependencyMap[3]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  var flatListItemStyle = {
    marginEnd: 12
  };
  var renderFlatListData = function renderFlatListData(item, onPress, testID, accessibilityLabel, index) {
    item.text = item.tagTitle;
    item.onPressed = function () {
      return onPress(item);
    };
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: flatListItemStyle,
      children: (0, _jsxRuntime.jsx)(_chip.Chip, Object.assign({}, item, {
        testID: `${testID}__ItemFlatList__${index}`,
        accessibilityLabel: `${accessibilityLabel}__ItemFlatList__${index}`
      }))
    });
  };
  var ChipFilterResult = exports.ChipFilterResult = function ChipFilterResult(props) {
    var data = props.data,
      onPress = props.onPress,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "ChipFilterResult" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "ChipFilterResult" : _props$accessibilityL;
    return (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
      data: data,
      renderItem: function renderItem(_ref) {
        var item = _ref.item,
          index = _ref.index;
        return renderFlatListData(item, onPress, testID, accessibilityLabel, index);
      },
      showsHorizontalScrollIndicator: false,
      horizontal: true,
      scrollEnabled: false,
      keyExtractor: function keyExtractor(_, index) {
        return index.toString();
      },
      testID: `${testID}__FlatListChipFilterResult`,
      accessibilityLabel: `${accessibilityLabel}__FlatListChipFilterResult`
    });
  };
