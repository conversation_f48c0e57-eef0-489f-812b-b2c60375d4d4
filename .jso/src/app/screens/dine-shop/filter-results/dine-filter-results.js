  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _isEmpty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _get3 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _chipFilterResults = _$$_REQUIRE(_dependencyMap[8]);
  var _chip = _$$_REQUIRE(_dependencyMap[9]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[10]));
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _radioButton = _$$_REQUIRE(_dependencyMap[12]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[13]);
  var _dineRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[14]));
  var _tenantListingHorizontal = _$$_REQUIRE(_dependencyMap[15]);
  var _radioOptions = _$$_REQUIRE(_dependencyMap[16]);
  var _chip2 = _$$_REQUIRE(_dependencyMap[17]);
  var _i18n = _$$_REQUIRE(_dependencyMap[18]);
  var _constants = _$$_REQUIRE(_dependencyMap[19]);
  var _native = _$$_REQUIRE(_dependencyMap[20]);
  var _error = _$$_REQUIRE(_dependencyMap[21]);
  var _button = _$$_REQUIRE(_dependencyMap[22]);
  var _feedbackToastProps = _$$_REQUIRE(_dependencyMap[23]);
  var _feedbackToast = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[24]));
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[25]));
  var _theme = _$$_REQUIRE(_dependencyMap[26]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[27]);
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[28]);
  var _utils = _$$_REQUIRE(_dependencyMap[29]);
  var _icons = _$$_REQUIRE(_dependencyMap[30]);
  var _headerComponent = _$$_REQUIRE(_dependencyMap[31]);
  var _dineFilters = _$$_REQUIRE(_dependencyMap[32]);
  var _adobe = _$$_REQUIRE(_dependencyMap[33]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[34]);
  var _dineReservation = _$$_REQUIRE(_dependencyMap[35]);
  var _text2 = _$$_REQUIRE(_dependencyMap[36]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[37]));
  var _footerLoading = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[38]));
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[39]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[40]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[41]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var filterObj = {};
  var defaultFilterData = [];
  var wrapAvailableReservationMark = {
    flexDirection: "row",
    alignItems: "center",
    alignSelf: "center",
    marginTop: 20
  };
  var textAvailableReservationMark = Object.assign({}, _text2.presets.caption1Bold, {
    color: _theme.color.palette.darkGrey,
    fontSize: 12,
    lineHeight: 16,
    marginLeft: 3
  });
  var DEFAULT_PAGE_SIZE = 20;
  var DineFilterResults = function DineFilterResults() {
    var _dineFilterResultPayl, _dineFilterResultPayl6;
    var filteredData = (0, _reactRedux.useSelector)(function (state) {
      return _dineRedux.DineSelectors.filterItems(state);
    });
    var dispatch = (0, _reactRedux.useDispatch)();
    var navigation = (0, _native.useNavigation)();
    var toast = (0, _react.useRef)(null);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      showFilterModal = _useState2[0],
      setShowFilterModal = _useState2[1];
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var filteredDataTitles = (0, _reactRedux.useSelector)(function (state) {
      return _dineRedux.DineSelectors.filterResultTitles(state);
    });
    var dataCommonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var ehr13 = dataCommonAEM == null ? undefined : dataCommonAEM.find(function (e) {
      return (e == null ? undefined : e.code) === "EHR13";
    });
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      showAvailableReservationMark = _useState4[0],
      setShowAvailableReservationMark = _useState4[1];
    var _useState5 = (0, _react.useState)({
        id: 1,
        text: (0, _i18n.translate)("filterResult.nameA_Z"),
        orderBy: [{
          title: "ASC"
        }]
      }),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      radioSelected = _useState6[0],
      setRadioSelected = _useState6[1];
    var currentPage = (0, _react.useRef)(1);
    var dineFilterResultPayload = (0, _reactRedux.useSelector)(function (state) {
      return _dineRedux.DineSelectors.dineFilterResultPayloadData(state);
    });
    var canRequestFilter = (0, _reactRedux.useSelector)(function (state) {
      return _dineRedux.DineSelectors.canRequestFilter(state);
    });
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("DINE_FILTER_RESULTS"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var isEmptyResultPayload = (dineFilterResultPayload == null ? undefined : dineFilterResultPayload.totalCount) === 0 && (dineFilterResultPayload == null || (_dineFilterResultPayl = dineFilterResultPayload.data) == null ? undefined : _dineFilterResultPayl.length) === 0;
    var hasReservationItem = function hasReservationItem() {
      var isExist = false;
      filteredDataTitles == null || filteredDataTitles.forEach(function (e) {
        if ((e == null ? undefined : e.tagName) === _dineReservation.ReservationValues.reservations) {
          isExist = true;
        }
      });
      return isExist;
    };
    (0, _react.useEffect)(function () {
      if (hasReservationItem()) {
        setShowAvailableReservationMark(true);
      } else {
        setShowAvailableReservationMark(false);
      }
    }, [filteredDataTitles]);
    var _useState7 = (0, _react.useState)(false),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      isModalVisible = _useState8[0],
      setModalVisible = _useState8[1];
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)("DineFilter_Result");
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)("DineFilter_Result", (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribeFocus;
    }, [navigation]);
    function radioClick(value) {
      setModalVisible(false);
      setRadioSelected(value);
      dispatch(_dineRedux.default.startRequestFilter(true));
    }
    var _onClosedSheet = function onClosedSheet() {
      setModalVisible(false);
    };
    filterObj.filter = (filteredData == null ? undefined : filteredData.length) > 0 ? filteredData : defaultFilterData;
    filterObj.orderBy = radioSelected.orderBy;
    filterObj.endCursor = "";
    filterObj.pageNumber = 1;
    filterObj.size = DEFAULT_PAGE_SIZE;
    var getFilteredData = _react.default.useCallback(function () {
      dispatch(_dineRedux.default.dineFilterResultRequest(filterObj));
    }, [filterObj]);
    (0, _react.useEffect)(function () {
      if (canRequestFilter) {
        currentPage.current = 1;
        getFilteredData();
      }
    }, [filterObj, radioSelected.text, canRequestFilter]);
    (0, _react.useEffect)(function () {
      return function () {
        dispatch(_dineRedux.default.dineFilterPaginationReset());
        dispatch(_dineRedux.default.resetValueFilter());
      };
    }, []);
    var loadMoreFilterResultData = function loadMoreFilterResultData() {
      var _dineFilterResultPayl2;
      var hasNextPage = (dineFilterResultPayload == null || (_dineFilterResultPayl2 = dineFilterResultPayload.data) == null ? undefined : _dineFilterResultPayl2.length) < (dineFilterResultPayload == null ? undefined : dineFilterResultPayload.totalCount);
      if (hasNextPage) {
        var _dineFilterResultPayl3, _dineFilterResultPayl4;
        toast.current.close(1);
        filterObj.endCursor = dineFilterResultPayload == null || (_dineFilterResultPayl3 = dineFilterResultPayload.pageInfo) == null ? undefined : _dineFilterResultPayl3.endCursor;
        dispatch(_dineRedux.default.startRequestFilter(true));
        if (currentPage.current * (filterObj == null ? undefined : filterObj.size) <= (dineFilterResultPayload == null || (_dineFilterResultPayl4 = dineFilterResultPayload.data) == null ? undefined : _dineFilterResultPayl4.length)) {
          currentPage.current += 1;
        }
        filterObj.pageNumber = currentPage.current;
        dispatch(_dineRedux.default.dineFilterPaginationRequest(filterObj));
      }
    };
    function removePillObject(value) {
      currentPage.current = 1;
      dispatch(_dineRedux.default.removeDineFilterTitles(value.main, {
        tagName: value.tagName,
        tagTitle: value.tagTitle
      }));
      getFilteredData();
    }
    var renderFlatListData = function renderFlatListData(_ref) {
      var item = _ref.item,
        index = _ref.index;
      item.onPressed = function () {
        navigation.navigate(_constants.NavigationConstants.restaurantDetailScreen, {
          tenantId: item == null ? undefined : item.tenentId,
          name: item == null ? undefined : item.tenantName
        });
      };
      var getLocationContent = function getLocationContent() {
        return [item == null ? undefined : item.locationDisplay, item == null ? undefined : item.areaDisplay].filter(function (value) {
          return !(0, _isEmpty2.default)(value);
        }).join(` ${(0, _constants.getDotUnicode)()} `);
      };
      var getCategoryContent = function getCategoryContent() {
        var _item$categories;
        if (!(item != null && (_item$categories = item.categories) != null && _item$categories.length)) return "";
        return item.categories.slice(0, 3).join(", ");
      };
      var getDietaryData = function getDietaryData() {
        var _item$dietary, _item$dietary2;
        if (!(item != null && (_item$dietary = item.dietary) != null && _item$dietary.length)) return undefined;
        var dietaryItem = item == null || (_item$dietary2 = item.dietary) == null ? undefined : _item$dietary2[0];
        var result = {
          content: dietaryItem == null ? undefined : dietaryItem.tagTitle,
          icon: undefined
        };
        switch (dietaryItem == null ? undefined : dietaryItem.tagTitle) {
          case _constants.DINE_DIETARY_TYPE.GLUTEN_FREE:
          case _constants.DINE_DIETARY_TYPE.GLUTEN_FREE_OPTIONS:
            result.icon = (0, _jsxRuntime.jsx)(_icons.DietaryGlutenFreeOptionsIcon, {});
            break;
          case _constants.DINE_DIETARY_TYPE.HALAL:
            result.icon = (0, _jsxRuntime.jsx)(_icons.DietaryHalalIcon, {});
            break;
          case _constants.DINE_DIETARY_TYPE.VEGAN:
          case _constants.DINE_DIETARY_TYPE.VEGAN_OPTIONS:
            result.icon = (0, _jsxRuntime.jsx)(_icons.DietaryVeganOptionsIcon, {});
            break;
          case _constants.DINE_DIETARY_TYPE.VEGETARIAN:
          case _constants.DINE_DIETARY_TYPE.VEGETARIAN_FRIENDLY:
            result.icon = (0, _jsxRuntime.jsx)(_icons.DietaryVegetarianFriendlyIcon, {});
            break;
          default:
            break;
        }
        return result;
      };
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.flatListItemsStyle,
        children: (0, _jsxRuntime.jsx)(_tenantListingHorizontal.TenantListingHorizontal, Object.assign({}, item, {
          accessibilityLabel: `${_constants.DINE_SHOP_COMPONENT_NAME.DINE}__TenantListingHorizontal__${index}`,
          categoryContent: getCategoryContent(),
          componentType: _constants.DINE_SHOP_COMPONENT_NAME.DINE,
          dietaryData: getDietaryData(),
          itemIndex: index,
          location: getLocationContent(),
          testID: `${_constants.DINE_SHOP_COMPONENT_NAME.DINE}__TenantListingHorizontal__${index}`
        }))
      }, index);
    };
    var pillText = function pillText() {
      return radioSelected.text === "" ? (0, _i18n.translate)("filterResult.sortBy") : radioSelected.text;
    };
    var getRadioItemStyle = function getRadioItemStyle(index) {
      return {
        borderBottomWidth: index === (_radioOptions.radioOptions == null ? undefined : _radioOptions.radioOptions.length) - 1 ? 0 : 1
      };
    };
    var footerComponent = function footerComponent() {
      var _dineFilterResultPayl5;
      var hasNextPage = (dineFilterResultPayload == null || (_dineFilterResultPayl5 = dineFilterResultPayload.data) == null ? undefined : _dineFilterResultPayl5.length) < (dineFilterResultPayload == null ? undefined : dineFilterResultPayload.totalCount);
      if (!hasNextPage) return null;
      return (0, _jsxRuntime.jsx)(_footerLoading.default, {});
    };
    var showErrorToastMessage = function showErrorToastMessage() {
      return (0, _jsxRuntime.jsx)(_feedbackToast.default, {
        ref: toast,
        style: styles.toastStyle,
        textButtonStyle: styles.toastButtonStyle,
        position: "custom",
        positionValue: styles.positionStyle,
        textStyle: styles.toastTextStyle,
        type: _feedbackToastProps.FeedBackToastType.fullWidthFeedBackWithCTA,
        text: (0, _i18n.translate)("filterResult.errorToastMessage"),
        buttonText: (0, _i18n.translate)("filterResult.retry"),
        onPress: loadMoreFilterResultData,
        testID: `${_constants.DINE_SHOP_COMPONENT_NAME.DINE}__FeedBackToast`,
        accessibilityLabel: `${_constants.DINE_SHOP_COMPONENT_NAME.DINE}__FeedBackToast`
      });
    };
    if (dineFilterResultPayload != null && dineFilterResultPayload.hasPaginationError) {
      var _toast$current;
      toast == null || (_toast$current = toast.current) == null || _toast$current.show(_constants.TOAST_MESSAGE_DURATION);
    }
    var renderSeparator = function renderSeparator() {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.dividerStyle
      });
    };
    var arrRightIcon = [{
      Icon: function Icon() {
        return (0, _jsxRuntime.jsx)(_icons.Filter, {
          color: _theme.color.palette.lightPurple,
          height: 20,
          width: 20
        });
      },
      onPress: function onPress() {
        setShowFilterModal(true);
      }
    }];
    var availableReservationMark = function availableReservationMark() {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: wrapAvailableReservationMark,
        children: [(0, _jsxRuntime.jsx)(_icons.CalendarCheckOutline, {}), (0, _jsxRuntime.jsx)(_text.Text, {
          tx: "filterResult.reservationAvailable",
          preset: "caption1Bold",
          style: textAvailableReservationMark
        })]
      });
    };
    var handlePressFilter = function handlePressFilter() {
      var _get2 = (0, _get3.default)(ehr13, "navigationFirst", {}),
        type = _get2.type,
        value = _get2.value;
      if (type) {
        handleNavigation(type, value, ehr13 == null ? undefined : ehr13.redirectFirst);
        return;
      }
      setShowFilterModal(true);
    };
    var renderEmptyResultSection = (0, _react.useMemo)(function () {
      return !!(filteredData != null && filteredData.length) ? (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.noResultsContainer,
        children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
          source: (0, _utils.handleCondition)(ehr13 == null ? undefined : ehr13.icon, {
            uri: (0, _mediaHelper.handleImageUrl)(ehr13 == null ? undefined : ehr13.icon)
          }, _$$_REQUIRE(_dependencyMap[42])),
          style: styles.imageEmptyState
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.noResultsText,
          children: (ehr13 == null ? undefined : ehr13.header) || (0, _i18n.translate)("filterResult.noResults")
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.noResultsMessageContainer,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.noResultsMessageText,
            children: (ehr13 == null ? undefined : ehr13.subHeader) || (0, _i18n.translate)("filterResult.noResultsMessage")
          })
        }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
          style: styles.buttonGradient,
          start: {
            x: 1,
            y: 0
          },
          end: {
            x: 0,
            y: 1
          },
          colors: [_theme.color.palette.gradientColor1End, _theme.color.palette.gradientColor1Start],
          children: (0, _jsxRuntime.jsx)(_button.Button, {
            textPreset: "buttonLarge",
            typePreset: "secondary",
            statePreset: "default",
            backgroundPreset: "dark",
            text: (ehr13 == null ? undefined : ehr13.buttonLabel) || (0, _i18n.translate)("filterResult.reselectFilters"),
            onPress: handlePressFilter,
            textStyle: styles.buttonResults,
            testID: `${_constants.DINE_SHOP_COMPONENT_NAME.DINE}__ButtonResults`,
            accessibilityLabel: `${_constants.DINE_SHOP_COMPONENT_NAME.DINE}__ButtonResults`,
            style: styles.buttonReselect
          })
        })]
      }) : (0, _jsxRuntime.jsx)(_text.Text, {
        preset: "caption1Regular",
        style: styles.emptyResultMessage,
        tx: "dineScreen.unableToDisplayRestaurants"
      });
    }, [filteredData == null ? undefined : filteredData.length]);
    return (0, _jsxRuntime.jsxs)(_reactNative2.SafeAreaView, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsx)(_reactNative2.StatusBar, {
        translucent: true,
        backgroundColor: "transparent",
        barStyle: "dark-content"
      }), (0, _jsxRuntime.jsx)(_headerComponent.HeaderComponent, {
        title: (0, _i18n.translate)("filterResult.dine"),
        rightIconArray: arrRightIcon
      }), dineFilterResultPayload != null && dineFilterResultPayload.errorFlag ? (0, _jsxRuntime.jsx)(_error.ErrorScreen, {
        onReload: function onReload() {
          return getFilteredData();
        },
        testID: `${_constants.DINE_SHOP_COMPONENT_NAME.DINE}__ErrorScreen`,
        accessibilityLabel: `${_constants.DINE_SHOP_COMPONENT_NAME.DINE}__ErrorScreen`
      }) : (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.parentContainer,
        children: [(0, _jsxRuntime.jsx)(_bottomSheet.default, {
          isModalVisible: isModalVisible,
          onClosedSheet: function onClosedSheet() {
            return _onClosedSheet();
          },
          containerStyle: styles.bottomSheetStyle,
          stopDragCollapse: true,
          onBackPressHandle: _onClosedSheet,
          animationInTiming: 200,
          animationOutTiming: 200,
          children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: styles.headerSortBy,
              children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {}), (0, _jsxRuntime.jsx)(_text.Text, {
                preset: "h2",
                text: (0, _i18n.translate)("filterResult.sortBy"),
                style: styles.sortByTextStyle
              }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                onPress: _onClosedSheet,
                testID: `${_constants.DINE_SHOP_COMPONENT_NAME.DINE}__ButtonClosePopup`,
                accessibilityLabel: `${_constants.DINE_SHOP_COMPONENT_NAME.DINE}__ButtonClosePopup`,
                style: styles.closeIcon,
                hitSlop: {
                  top: 4,
                  bottom: 4,
                  left: 4,
                  right: 4
                },
                children: (0, _jsxRuntime.jsx)(_icons.CloseAdvisoriesPopupIcon, {
                  height: "12",
                  width: "12"
                })
              })]
            }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: styles.radioOptionsOuterContainerStyle,
              children: _radioOptions.radioOptions.map(function (val, index) {
                return (0, _jsxRuntime.jsx)(_reactNative2.View, {
                  style: [styles.radioOptionsInnerContainerStyle, getRadioItemStyle(index)],
                  children: (0, _jsxRuntime.jsx)(_radioButton.RadioButton, {
                    onPress: function onPress() {
                      return radioClick(val);
                    },
                    outerContainer: (0, _utils.handleCondition)(val.id === radioSelected.id, styles.outerRadioRingStyle, styles.outerRadioGrayStyle),
                    innerContainer: val.id === radioSelected.id ? styles.innerRadioCircleStyle : {},
                    text: val.text,
                    preset: val.id === radioSelected.id ? "bodyTextBold" : "bodyTextRegular",
                    textStyle: styles.radioTextStyle,
                    testID: `${_constants.DINE_SHOP_COMPONENT_NAME.DINE}__RadioButton__${index}`,
                    accessibilityLabel: `${_constants.DINE_SHOP_COMPONENT_NAME.DINE}__RadioButton__${index}`,
                    customStyle: {
                      width: "100%"
                    }
                  })
                }, val.id);
              })
            })]
          })
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.scrollViewParentContainer,
          children: (0, _jsxRuntime.jsxs)(_reactNative2.ScrollView, {
            contentContainerStyle: styles.pillsContainer,
            horizontal: true,
            showsHorizontalScrollIndicator: false,
            testID: `${_constants.DINE_SHOP_COMPONENT_NAME.DINE}__ScrollViewChip`,
            accessibilityLabel: `${_constants.DINE_SHOP_COMPONENT_NAME.DINE}__ScrollViewChip`,
            children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: styles.chipParentContainer,
              children: (0, _jsxRuntime.jsx)(_chip2.Chip, {
                text: pillText(),
                type: _chip.ChipType.sortBy,
                onPressed: function onPressed() {
                  return setModalVisible(true);
                },
                testID: `${_constants.DINE_SHOP_COMPONENT_NAME.DINE}__ChipPillText`,
                accessibilityLabel: `${_constants.DINE_SHOP_COMPONENT_NAME.DINE}__ChipPillText`
              })
            }), (0, _jsxRuntime.jsx)(_chipFilterResults.ChipFilterResult, {
              data: filteredDataTitles,
              type: _chip.ChipType.withIcon,
              screen: "Dine",
              onPress: function onPress(item) {
                return removePillObject(item);
              },
              testID: `${_constants.DINE_SHOP_COMPONENT_NAME.DINE}__ChipFilterResult`,
              accessibilityLabel: `${_constants.DINE_SHOP_COMPONENT_NAME.DINE}__ChipFilterResult`
            })]
          })
        }), (0, _utils.handleCondition)(isEmptyResultPayload, renderEmptyResultSection, (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
          children: (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
            style: styles.flatListStyle,
            data: dineFilterResultPayload == null ? undefined : dineFilterResultPayload.data,
            renderItem: function renderItem(item) {
              return renderFlatListData(item);
            },
            ItemSeparatorComponent: renderSeparator,
            showsVerticalScrollIndicator: false,
            contentContainerStyle: styles.flatListContentContainerStyle,
            onEndReachedThreshold: 0.1,
            onEndReached: loadMoreFilterResultData,
            keyExtractor: function keyExtractor(_, index) {
              return index.toString();
            },
            testID: `${_constants.DINE_SHOP_COMPONENT_NAME.DINE}__FlatListFilterResultData`,
            accessibilityLabel: `${_constants.DINE_SHOP_COMPONENT_NAME.DINE}__FlatListFilterResultData`,
            ListHeaderComponent: function ListHeaderComponent() {
              return showAvailableReservationMark && availableReservationMark();
            },
            ListFooterComponent: function ListFooterComponent() {
              return footerComponent();
            }
          })
        }))]
      }), showErrorToastMessage(), (0, _jsxRuntime.jsx)(_dineFilters.DineFilter, {
        showFilterModal: showFilterModal,
        setShowFilterModal: setShowFilterModal,
        isNotFilterable: !(dineFilterResultPayload != null && (_dineFilterResultPayl6 = dineFilterResultPayload.data) != null && _dineFilterResultPayl6.length)
      })]
    });
  };
  var _default = exports.default = DineFilterResults;
