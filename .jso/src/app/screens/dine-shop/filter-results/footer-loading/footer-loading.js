  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _lottieReactNative = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _loading = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  var FooterLoading = function FooterLoading() {
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.container,
      children: (0, _jsxRuntime.jsx)(_lottieReactNative.default, {
        style: styles.lottieStyle,
        source: _loading.default,
        autoPlay: true,
        loop: true
      })
    });
  };
  var styles = _reactNative.StyleSheet.create({
    container: {
      alignItems: "center"
    },
    lottieStyle: {
      height: 60,
      width: _reactNative.Dimensions.get("window").width
    }
  });
  var _default = exports.default = FooterLoading;
