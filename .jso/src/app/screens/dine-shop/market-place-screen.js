  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[6]);
  var _navigationUtilities = _$$_REQUIRE(_dependencyMap[7]);
  var _marketPlaceView = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _theme = _$$_REQUIRE(_dependencyMap[9]);
  var _constants = _$$_REQUIRE(_dependencyMap[10]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[11]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[12]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[13]);
  var _native = _$$_REQUIRE(_dependencyMap[14]);
  var _airportLandingRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[15]));
  var _loadingModal = _$$_REQUIRE(_dependencyMap[16]);
  var _bottomSheetVerifyEmail = _$$_REQUIRE(_dependencyMap[17]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[18]));
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[19]);
  var _marketPlaceRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[20]));
  var _adobe = _$$_REQUIRE(_dependencyMap[21]);
  var _errorUnplannedMaintenance = _$$_REQUIRE(_dependencyMap[22]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[23]);
  var _lodash = _$$_REQUIRE(_dependencyMap[24]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[25]);
  var _bottomTabs = _$$_REQUIRE(_dependencyMap[26]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[27]);
  var _suspend = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[28]));
  var _dineShopV = _$$_REQUIRE(_dependencyMap[29]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[30]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SCREEN_NAME = "MarketPlaceScreen";
  var MarketPlaceScreen = function MarketPlaceScreen(_ref) {
    var route = _ref.route,
      navigation = _ref.navigation,
      onLayout = _ref.onLayout;
    var dispatch = (0, _reactRedux.useDispatch)();
    var isFocused = (0, _native.useIsFocused)();
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("MARKET_PLACE"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var userProfile = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var isUserVerified = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.isAccountVerified);
    var getCarParkUrlFetching = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.getCarParkUrlFetching);
    var getCarParkUrlPayload = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.getCarParkUrlPayload);
    var _useState = (0, _react.useState)(null),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      targetScreen = _useState2[0],
      setTargetScreen = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      showResend = _useState4[0],
      setShowResend = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isNoConnection = _useState6[0],
      setNoConnection = _useState6[1];
    var _useDineShopFlags = (0, _dineShopV.useDineShopFlags)(),
      isShopDineEpicV2On = _useDineShopFlags.isShopDineEpicV2On;
    var dataErrorMaintenanceConfig = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.GET_ERROR_MAINTENANCE));
    var listErrorMaintenance = (0, _lodash.get)(dataErrorMaintenanceConfig, "data.list", []);
    var screenMaintenanceObj = (0, _react.useMemo)(function () {
      var data = listErrorMaintenance == null ? undefined : listErrorMaintenance.find(function (obj) {
        return obj.screenName === _constants.screenTagName.marketplace;
      });
      return data || {};
    }, [listErrorMaintenance]);
    var _useHandleScroll = (0, _navigationUtilities.useHandleScroll)(),
      handleScroll = _useHandleScroll.handleScroll,
      isTabVisible = _useHandleScroll.isTabVisible;
    var bottomTabHeight = (0, _bottomTabs.useBottomTabBarHeight)();
    var onScroll = function onScroll(event) {
      var _route$params;
      handleScroll(event);
      route == null || (_route$params = route.params) == null || _route$params.setOptions == null || _route$params.setOptions({
        tabBarVisible: isTabVisible.current
      });
    };
    var contentContainerStyle = {
      paddingBottom: (0, _reactNativeSafeAreaContext.useSafeAreaInsets)().bottom + bottomTabHeight
    };
    var onReloadData = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        if (isConnected) {
          setNoConnection(false);
          dispatch(_marketPlaceRedux.default.getListMarketPlaceRequest());
        }
      });
      return function onReloadData() {
        return _ref2.apply(this, arguments);
      };
    }();
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)("MarketPlace");
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)("MarketPlace", (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribeFocus;
    }, [navigation]);
    (0, _react.useEffect)(function () {
      var checkInternet = /*#__PURE__*/function () {
        var _ref3 = (0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch2.isConnected;
          if (!isConnected) {
            setNoConnection(true);
          }
        });
        return function checkInternet() {
          return _ref3.apply(this, arguments);
        };
      }();
      checkInternet();
    }, []);
    (0, _react.useEffect)(function () {
      if (getCarParkUrlPayload) {
        navigation.navigate(_constants.NavigationConstants.playpassWebview, {
          uri: getCarParkUrlPayload == null ? undefined : getCarParkUrlPayload.redirectUri,
          needCloseButton: true,
          needBackButton: true,
          basicAuthCredential: getCarParkUrlPayload == null ? undefined : getCarParkUrlPayload.basicAuth
        });
        dispatch(_airportLandingRedux.default.getCarParkUrlReset());
      }
    }, [getCarParkUrlPayload]);
    (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
      // If have saved screen
      if (targetScreen) {
        var screenName = targetScreen;
        // Clear target screen
        setTargetScreen(null);
        if (_constants.RequiredLoggedScreens.includes(screenName)) {
          if (isLoggedIn) {
            // Not verified user
            if (!isUserVerified) {
              setShowResend(true);
              return;
            }
            navigation.navigate(screenName);
          }
        }
      }
    }, [targetScreen, isLoggedIn, isUserVerified]));
    var handleItemMarketPlaceOnClick = function handleItemMarketPlaceOnClick(item) {
      var _item$navigation = item.navigation,
        type = _item$navigation.type,
        value = _item$navigation.value;
      var _ref4 = item || "",
        redirect = _ref4.redirect;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppMarketplaceTiles, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppMarketplaceTiles, (item == null ? undefined : item.title) || ""));
      if (!type || !value) return;
      handleNavigation(type, value, redirect);
    };
    var handleButtonOnPress = /*#__PURE__*/function () {
      var _ref5 = (0, _asyncToGenerator2.default)(function* (navigationObj, redirect) {
        var type = navigationObj.type,
          value = navigationObj.value;
        if (!type || !value) return;
        handleNavigation(type, value, redirect);
      });
      return function handleButtonOnPress(_x, _x2) {
        return _ref5.apply(this, arguments);
      };
    }();
    if (!(0, _lodash.isEmpty)(screenMaintenanceObj) && screenMaintenanceObj != null && screenMaintenanceObj.enableMode) {
      return (0, _jsxRuntime.jsx)(_errorUnplannedMaintenance.ErrorUnplannedMaintenance, {
        header: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.header,
        subHeader: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.subHeader,
        icon: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.icon,
        buttonLabel: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.buttonLabel,
        buttonLabel2: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.buttonLabel2,
        onFirstButtonPress: function onFirstButtonPress() {
          return handleButtonOnPress(screenMaintenanceObj == null ? undefined : screenMaintenanceObj.navigationFirst, screenMaintenanceObj == null ? undefined : screenMaintenanceObj.redirectFirst);
        },
        onSecondButtonPress: function onSecondButtonPress() {
          return handleButtonOnPress(screenMaintenanceObj == null ? undefined : screenMaintenanceObj.navigationSecond, screenMaintenanceObj == null ? undefined : screenMaintenanceObj.redirectSecond);
        },
        testID: `${SCREEN_NAME}__ErrorUnplannedMaintenance`,
        accessibilityLabel: `${SCREEN_NAME}__ErrorUnplannedMaintenance`
      });
    }
    if (isNoConnection) {
      return (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        header: false,
        headerBackgroundColor: "transparent",
        hideScreenHeader: false,
        visible: true,
        testID: `${SCREEN_NAME}__ErrorOverlayNoConnection`,
        accessibilityLabel: `${SCREEN_NAME}__ErrorOverlayNoConnection`,
        onReload: onReloadData,
        noInternetOverlayStyle: styles.overlayStyle
      });
    }
    return (0, _jsxRuntime.jsx)(_suspend.default, {
      freeze: !isFocused,
      children: (0, _jsxRuntime.jsxs)(_reactNative.SafeAreaView, {
        style: styles.container,
        children: [(0, _jsxRuntime.jsx)(_reactNative.ScrollView, {
          onScroll: onScroll,
          scrollEventThrottle: 1,
          showsVerticalScrollIndicator: false,
          style: styles.container,
          contentContainerStyle: contentContainerStyle,
          testID: `${SCREEN_NAME}__ScrollView`,
          accessibilityLabel: `${SCREEN_NAME}__ScrollView`,
          scrollEnabled: !isShopDineEpicV2On,
          children: (0, _jsxRuntime.jsx)(_marketPlaceView.default, {
            onLayout: onLayout,
            onItemPress: handleItemMarketPlaceOnClick
          })
        }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
          visible: getCarParkUrlFetching
        }), (0, _jsxRuntime.jsx)(_bottomSheetVerifyEmail.BottomSheetVerifyEmail, {
          visible: showResend,
          email: userProfile == null ? undefined : userProfile.email,
          onHide: function onHide() {
            return setShowResend(false);
          },
          testID: `${SCREEN_NAME}BottomSheetVerifyEmailVerifyEmail`
        })]
      })
    });
  };
  var styles = _reactNative.StyleSheet.create({
    container: {
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1
    },
    overlayStyle: {
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1
    }
  });
  var _default = exports.default = MarketPlaceScreen;
