  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.RestaurantLoadingDetailsScreen = undefined;
  var _hero = _$$_REQUIRE(_dependencyMap[1]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _constants = _$$_REQUIRE(_dependencyMap[4]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[7]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var RestaurantLoadingDetailsScreen = exports.RestaurantLoadingDetailsScreen = function RestaurantLoadingDetailsScreen() {
    return (0, _jsxRuntime.jsxs)(_reactNative.ScrollView, {
      showsVerticalScrollIndicator: false,
      style: {
        backgroundColor: _theme.color.palette.whiteGrey
      },
      children: [(0, _jsxRuntime.jsx)(_hero.Hero, {
        type: _hero.HeroType.loading,
        logoImageUrl: undefined,
        heroImagesUrl: undefined
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.viewContainerStyle[0],
        children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
          children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: styles.lighterGreyLoadingColors,
            shimmerStyle: styles.skeletonLayout[0]
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.viewContainerStyle[1],
            children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: styles.lighterGreyLoadingColors,
              shimmerStyle: styles.skeletonLayout[1]
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: styles.lighterGreyLoadingColors,
              shimmerStyle: styles.skeletonLayout[1]
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: styles.lighterGreyLoadingColors,
              shimmerStyle: styles.skeletonLayout[1]
            })]
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: styles.lighterGreyLoadingColors,
            shimmerStyle: styles.skeletonLayout[2]
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.viewContainerStyle[1],
            children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: styles.lighterGreyLoadingColors,
              shimmerStyle: styles.skeletonLayout[3]
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: styles.lighterGreyLoadingColors,
              shimmerStyle: styles.skeletonLayout[4]
            })]
          }), (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: styles.viewContainerStyle[6],
            children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: styles.lighterGreyLoadingColors,
              shimmerStyle: styles.skeletonLayout[5]
            })
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.viewContainerStyle[1],
            children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: styles.lighterGreyLoadingColors,
              shimmerStyle: styles.skeletonLayout[3]
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: styles.lighterGreyLoadingColors,
              shimmerStyle: styles.skeletonLayout[6]
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: styles.lighterGreyLoadingColors,
              shimmerStyle: styles.skeletonLayout[7]
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.viewContainerStyle[2],
            children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: styles.lighterGreyLoadingColors,
              shimmerStyle: styles.skeletonLayout[6]
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: styles.lighterGreyLoadingColors,
              shimmerStyle: styles.skeletonLayout[7]
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.viewContainerStyle[3],
            children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: styles.lighterGreyLoadingColors,
              shimmerStyle: styles.skeletonLayout[6]
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: styles.lighterGreyLoadingColors,
              shimmerStyle: styles.skeletonLayout[7]
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.viewContainerStyle[4],
            children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: styles.lighterGreyLoadingColors,
              shimmerStyle: styles.skeletonLayout[6]
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: styles.lighterGreyLoadingColors,
              shimmerStyle: styles.skeletonLayout[7]
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.viewContainerStyle[1],
            children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: styles.lighterGreyLoadingColors,
              shimmerStyle: styles.skeletonLayout[3]
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: styles.lighterGreyLoadingColors,
              shimmerStyle: styles.skeletonLayout[6]
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.viewContainerStyle[5],
            children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: styles.lighterGreyLoadingColors,
              shimmerStyle: styles.skeletonLayout[3]
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: styles.lighterGreyLoadingColors,
              shimmerStyle: styles.skeletonLayout[6]
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: styles.lighterGreyLoadingColors,
              shimmerStyle: styles.skeletonLayout[7]
            })]
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: styles.lighterGreyLoadingColors,
            shimmerStyle: styles.skeletonLayout[8]
          })]
        })
      })]
    });
  };
