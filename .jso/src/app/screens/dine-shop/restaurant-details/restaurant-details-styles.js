  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.wrapInformationDetail = exports.visibileStyle = exports.viewContainerStyle = exports.txtColor = exports.topErrorComponentStyle = exports.titleText = exports.timingsText = exports.timingsInfoViewStyle = exports.timingsInfoDayText = exports.timingStyle = exports.text = exports.temporaryClosedStyle = exports.tagHorizonralStyle = exports.statusTextStyle = exports.spoonIconStyle = exports.skeletonLayout = exports.shareButtonStyle = exports.separator = exports.safeAreaContainer = exports.rewardTitleStyle = exports.rewardContainer = exports.phoneNumberStyle = exports.phoneIconStyle = exports.menuLinkStyles = exports.menuLinkContainer = exports.loadMoreShowLessView = exports.loadMoreShowLessTextStyle = exports.loadMoreShowLessPaddingView = exports.loadMoreShowLessIconStyle = exports.linkView = exports.linkTestStyle = exports.lighterGreyLoadingColors = exports.iscCTAText = exports.iscCTAButton = exports.infoBannerView = exports.headeriOSViewStyle = exports.headerViewStyle = exports.headerStyle = exports.headerAndroidViewStyle = exports.header = exports.getDirectionStyleDisabled = exports.getDirectionStyle = exports.flexStyleHeader = exports.flexStyle = exports.flatListItemStyle = exports.directionTextStyle = exports.directionIconstyle = exports.ctaButtonStyle = exports.content = exports.container = exports.clockIconStyle = exports.changiEatsViewStyle = exports.buttonView = exports.blogsViewStyle = exports.blogsTextStyle = exports.blogsFlatListStyle = exports.backButtonStyle = exports.arrowIconStyle = exports.areaInfoViewStyle = exports.aboutViewText = exports.aboutViewStyle = exports.aboutViewHeaderText = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNativeSizeMatters = _$$_REQUIRE(_dependencyMap[2]);
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var lighterGreyLoadingColors = exports.lighterGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var skeletonLayout = exports.skeletonLayout = [{
    width: (0, _reactNativeSizeMatters.scale)(250),
    height: 36,
    marginTop: 23,
    borderRadius: 4,
    marginBottom: 12,
    marginRight: 24
  }, {
    width: (0, _reactNativeSizeMatters.scale)(50),
    height: 30,
    borderRadius: 8,
    marginRight: 12,
    marginBottom: 22
  }, {
    width: (0, _reactNativeSizeMatters.scale)(310),
    height: 36,
    marginBottom: 26,
    marginRight: 24,
    borderRadius: 8
  }, {
    width: 20,
    height: 20,
    borderRadius: 16,
    marginRight: 17,
    marginBottom: 22
  }, {
    width: (0, _reactNativeSizeMatters.scale)(265),
    height: 18,
    borderRadius: 4,
    marginRight: 12,
    marginBottom: 6
  }, {
    width: (0, _reactNativeSizeMatters.scale)(100),
    height: 18,
    borderRadius: 4,
    marginLeft: 36,
    bottom: 18
  }, {
    width: (0, _reactNativeSizeMatters.scale)(100),
    height: 18,
    borderRadius: 4,
    marginRight: 12,
    marginBottom: 6,
    top: 1
  }, {
    width: (0, _reactNativeSizeMatters.scale)(100),
    height: 18,
    borderRadius: 4,
    marginLeft: 50
  }, {
    width: (0, _reactNativeSizeMatters.scale)(310),
    height: 36,
    marginBottom: 26,
    marginRight: 24,
    borderRadius: 16
  }];
  var viewContainerStyle = exports.viewContainerStyle = [{
    marginLeft: 24,
    marginBottom: 5
  }, {
    flexDirection: "row"
  }, {
    flexDirection: "row",
    marginLeft: 36,
    bottom: 15
  }, {
    flexDirection: "row",
    marginLeft: 36,
    bottom: 13
  }, {
    flexDirection: "row",
    marginLeft: 36,
    bottom: 11,
    marginBottom: 24
  }, {
    flexDirection: "row",
    marginBottom: 24
  }, {
    marginBottom: 24
  }];
  var titleText = exports.titleText = {
    color: _theme.color.palette.almostBlackGrey,
    fontWeight: _reactNative.Platform.OS === "android" ? "normal" : "700",
    marginLeft: 24
  };
  var header = exports.header = {
    flex: 1,
    backgroundColor: _theme.color.palette.whiteGrey,
    marginTop: 22
  };
  var separator = exports.separator = {
    height: 1,
    backgroundColor: _theme.color.palette.lighterGrey
  };
  var text = exports.text = {
    color: _theme.color.palette.darkestGrey,
    marginRight: 10
  };
  var timingsInfoDayText = exports.timingsInfoDayText = Object.assign({}, text, {
    flex: 0.45
  });
  var timingsText = exports.timingsText = Object.assign({}, timingsInfoDayText, {
    flex: 0.55,
    alignItems: "flex-start",
    textAlign: "left"
  });
  var content = exports.content = {
    backgroundColor: _theme.color.palette.whiteGrey
  };
  var tagHorizonralStyle = exports.tagHorizonralStyle = {
    paddingLeft: 24
  };
  var rewardContainer = exports.rewardContainer = {
    marginTop: 12,
    borderWidth: 1,
    borderColor: _theme.color.palette.lightestPurple,
    backgroundColor: _theme.color.palette.whiteGrey,
    borderRadius: 8,
    height: 30,
    marginRight: 8,
    justifyContent: "center",
    alignItems: "center"
  };
  var rewardTitleStyle = exports.rewardTitleStyle = {
    color: _theme.color.palette.lightPurple,
    paddingHorizontal: 12,
    fontWeight: _reactNative.Platform.OS === "android" ? "normal" : "700"
  };
  var flexStyle = exports.flexStyle = {
    flexDirection: "row",
    marginBottom: 24
  };
  var flexStyleHeader = exports.flexStyleHeader = {
    flexDirection: "row"
  };
  var headerStyle = exports.headerStyle = Object.assign({}, flexStyle, {
    flex: 1
  });
  var wrapInformationDetail = exports.wrapInformationDetail = {
    paddingHorizontal: 24
  };
  var txtColor = exports.txtColor = {
    color: _theme.color.palette.almostBlackGrey
  };
  var directionIconstyle = exports.directionIconstyle = {};
  var directionTextStyle = exports.directionTextStyle = {
    marginLeft: 52,
    bottom: 12,
    marginBottom: 12,
    fontWeight: _reactNative.Platform.OS === "android" ? "normal" : "700"
  };
  var linkTestStyle = exports.linkTestStyle = {
    marginLeft: 15,
    fontWeight: _reactNative.Platform.OS === "android" ? "normal" : "700"
  };
  var clockIconStyle = exports.clockIconStyle = {};
  var phoneNumberStyle = exports.phoneNumberStyle = {
    marginLeft: 15
  };
  var phoneIconStyle = exports.phoneIconStyle = Object.assign({}, clockIconStyle);
  var spoonIconStyle = exports.spoonIconStyle = Object.assign({}, phoneIconStyle);
  var areaInfoViewStyle = exports.areaInfoViewStyle = {
    marginLeft: 15,
    paddingRight: 10
  };
  var timingsInfoViewStyle = exports.timingsInfoViewStyle = {
    marginLeft: 15,
    flex: 1
  };
  var statusTextStyle = exports.statusTextStyle = {
    // marginTop: 2,
  };
  var visibileStyle = exports.visibileStyle = {
    overflow: "hidden"
  };
  var ctaButtonStyle = exports.ctaButtonStyle = {
    justifyContent: "center",
    height: 44
  };
  var arrowIconStyle = exports.arrowIconStyle = {
    alignItems: "flex-end"
  };
  var timingStyle = exports.timingStyle = {
    flexDirection: "row",
    justifyContent: "space-between"
  };
  var linkView = exports.linkView = {
    flexDirection: "row",
    marginBottom: 25,
    alignItems: "center"
  };
  var buttonView = exports.buttonView = {
    paddingBottom: 24,
    paddingHorizontal: 16
  };
  var container = exports.container = {
    flex: 1,
    backgroundColor: _theme.color.palette.whiteGrey
  };
  var safeAreaContainer = exports.safeAreaContainer = {
    flex: 1,
    backgroundColor: _theme.color.palette.whiteGrey
  };
  var headeriOSViewStyle = exports.headeriOSViewStyle = {
    position: "absolute",
    zIndex: 1,
    width: "100%",
    marginTop: 40,
    flexDirection: "row"
  };
  var headerAndroidViewStyle = exports.headerAndroidViewStyle = Object.assign({}, headeriOSViewStyle, {
    marginTop: 24
  });
  var backButtonStyle = exports.backButtonStyle = {
    flex: 1,
    marginStart: 14
  };
  var shareButtonStyle = exports.shareButtonStyle = {
    flex: 1,
    alignItems: "flex-end",
    marginEnd: 14
  };
  var blogsFlatListStyle = exports.blogsFlatListStyle = {
    marginTop: 23
  };
  var blogsTextStyle = exports.blogsTextStyle = {
    paddingBottom: 12
  };
  var infoBannerView = exports.infoBannerView = {
    marginLeft: 24,
    marginRight: 24,
    marginTop: 24
  };
  var blogsViewStyle = exports.blogsViewStyle = Object.assign({}, infoBannerView, {
    marginTop: 50
  });
  var changiEatsViewStyle = exports.changiEatsViewStyle = Object.assign({}, blogsViewStyle, {
    marginTop: 26
  });
  var aboutViewStyle = exports.aboutViewStyle = Object.assign({}, infoBannerView, {
    marginTop: 50
  });
  var aboutViewHeaderText = exports.aboutViewHeaderText = {
    paddingBottom: 12
  };
  var aboutViewText = exports.aboutViewText = {
    color: _theme.color.palette.darkestGrey,
    lineHeight: 24
  };
  var loadMoreShowLessView = exports.loadMoreShowLessView = {
    flexDirection: "row"
  };
  var loadMoreShowLessTextStyle = exports.loadMoreShowLessTextStyle = {
    color: _theme.color.palette.gradientColor1Start,
    marginTop: 13
  };
  var loadMoreShowLessIconStyle = exports.loadMoreShowLessIconStyle = {
    marginLeft: 10,
    top: 13
  };
  var flatListItemStyle = exports.flatListItemStyle = {
    paddingHorizontal: 24,
    marginBottom: 29
  };
  var headerViewStyle = exports.headerViewStyle = {
    position: "absolute",
    zIndex: 1,
    width: "100%",
    marginTop: 24,
    flexDirection: "row"
  };
  var loadMoreShowLessPaddingView = exports.loadMoreShowLessPaddingView = Object.assign({}, loadMoreShowLessView, {
    marginBottom: 10
  });
  var topErrorComponentStyle = exports.topErrorComponentStyle = {
    marginTop: 100
  };
  var getDirectionStyle = exports.getDirectionStyle = {
    color: _theme.color.palette.lightPurple,
    paddingVertical: 8
  };
  var getDirectionStyleDisabled = exports.getDirectionStyleDisabled = {
    color: "#ccc",
    paddingVertical: 8
  };
  var temporaryClosedStyle = exports.temporaryClosedStyle = {
    fontSize: 16,
    fontFamily: _theme.typography.bold,
    fontWeight: _reactNative.Platform.select({
      ios: "700",
      android: 'normal'
    }),
    lineHeight: 20,
    marginBottom: 24
  };
  var menuLinkStyles = exports.menuLinkStyles = {
    marginLeft: 12,
    flex: 1
  };
  var menuLinkContainer = exports.menuLinkContainer = {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 24
  };
  var iscCTAButton = exports.iscCTAButton = {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: _theme.color.palette.lightPurple,
    borderWidth: 2,
    borderRadius: 60,
    gap: 12,
    paddingVertical: 10,
    marginHorizontal: 24,
    marginTop: 24
  };
  var iscCTAText = exports.iscCTAText = Object.assign({}, _text.newPresets.bodyTextBold, {
    color: _theme.color.palette.lightPurple
  });
