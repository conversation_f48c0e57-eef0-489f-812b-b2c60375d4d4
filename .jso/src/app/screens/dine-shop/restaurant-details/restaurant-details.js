  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.RestaurantDetailsScreen = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _lodash = _$$_REQUIRE(_dependencyMap[8]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[9]));
  var _restuarantDetailsLoadingView = _$$_REQUIRE(_dependencyMap[10]);
  var _text = _$$_REQUIRE(_dependencyMap[11]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _constants = _$$_REQUIRE(_dependencyMap[13]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[14]);
  var _dineRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[15]));
  var _i18n = _$$_REQUIRE(_dependencyMap[16]);
  var _icons = _$$_REQUIRE(_dependencyMap[17]);
  var _infoBanner = _$$_REQUIRE(_dependencyMap[18]);
  var _hero = _$$_REQUIRE(_dependencyMap[19]);
  var _error = _$$_REQUIRE(_dependencyMap[20]);
  var _error2 = _$$_REQUIRE(_dependencyMap[21]);
  var _adobe = _$$_REQUIRE(_dependencyMap[22]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[23]);
  var _native = _$$_REQUIRE(_dependencyMap[24]);
  var _bottomSheetUnableLoadLocation = _$$_REQUIRE(_dependencyMap[25]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[26]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[27]);
  var _cusineInDetail = _$$_REQUIRE(_dependencyMap[28]);
  var _theme = _$$_REQUIRE(_dependencyMap[29]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[30]);
  var _readMore = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[31]));
  var _availableStaffDiscounts = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[32]));
  var _availableDealsPromos = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[33]));
  var _staffPerkSaga = _$$_REQUIRE(_dependencyMap[34]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[35]);
  var _bottomSheetMapUnavailable = _$$_REQUIRE(_dependencyMap[36]);
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[37]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[38]);
  var _pageConfigSaga = _$$_REQUIRE(_dependencyMap[39]);
  var _enum = _$$_REQUIRE(_dependencyMap[40]);
  var _deeplinkParameter = _$$_REQUIRE(_dependencyMap[41]);
  var _useModal2 = _$$_REQUIRE(_dependencyMap[42]);
  var _bottomSheetError = _$$_REQUIRE(_dependencyMap[43]);
  var _file = _$$_REQUIRE(_dependencyMap[44]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[45]));
  var _overlayStickyHeader = _$$_REQUIRE(_dependencyMap[46]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[47]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var paddingContentScrollView = {
    paddingBottom: 50
  };
  var loadingType = "loading";
  var SCREEN_NAME = "RestaurantDetailsScreen";
  var safeViewHeaderForAndroid = function safeViewHeaderForAndroid(inset) {
    var _safeViewHeaderForAndroid = {
      marginTop: _reactNative2.Platform.OS === "android" && inset != null && inset.top ? inset == null ? undefined : inset.top : 0,
      marginStart: 14
    };
    return _safeViewHeaderForAndroid;
  };
  var getDay = function getDay(day) {
    return day || (0, _i18n.translate)("restaurantDetails.daily");
  };
  var touchableReserve = {
    flexDirection: "row",
    justifyContent: "center",
    borderWidth: 2,
    borderColor: _theme.color.palette.lightPurple,
    paddingVertical: 10,
    borderRadius: 60
  };
  var wrapChope = {
    marginHorizontal: 24,
    paddingTop: 16
  };
  var touchableTextChope = {
    fontSize: 16,
    lineHeight: 24,
    marginLeft: 14,
    color: _theme.color.palette.lightPurple
  };
  var getMapName = function getMapName(item, dotUnicode) {
    var _item$openCloseStatus;
    if (item != null && item.mapName && (_item$openCloseStatus = item.openCloseStatus) != null && _item$openCloseStatus.status && !(item != null && item.hourComment)) {
      return item.mapName + `${"  "}` + dotUnicode + `${"  "}`;
    }
    return item == null ? undefined : item.mapName;
  };
  var renderCloseStatus = function renderCloseStatus(item) {
    if (item.openCloseStatus) {
      return (0, _jsxRuntime.jsx)(_text.Text, {
        preset: "bodyTextBold",
        style: [{
          color: item.openCloseStatus.colorCode
        }, styles.statusTextStyle],
        children: item.openCloseStatus.status
      });
    }
    return null;
  };
  var getTextNumber = function getTextNumber(item, number) {
    return (item ? ", " : "") + number;
  };
  var ExpandableComponent = function ExpandableComponent(_ref) {
    var _item$locationDetails3, _item$locationDetails4, _item$timingsInfo, _item$timingsInfo2, _item$phoneDetails, _item$locationDetails5;
    var item = _ref.item,
      onClickFunction = _ref.onClickFunction,
      key = _ref.key,
      tenantName = _ref.tenantName,
      _setLoading = _ref._setLoading,
      dialCall = _ref.dialCall,
      dotUnicode = _ref.dotUnicode,
      setConnection = _ref.setConnection,
      refRetryAction = _ref.refRetryAction,
      index = _ref.index;
    var _useState = (0, _react.useState)(0),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      layoutHeight = _useState2[0],
      setLayoutHeight = _useState2[1];
    var navigation = (0, _native.useNavigation)();
    var unableToLoadLocationRef = (0, _react.useRef)(null);
    var mapUnavailable = (0, _react.useRef)(null);
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      mapRMFlag = _useState4[0],
      setMapRMFlag = _useState4[1];
    var hourComment = item == null ? undefined : item.hourComment;
    (0, _react.useEffect)(function () {
      if (item.isExpanded) {
        setLayoutHeight(null);
        return;
      }
      setLayoutHeight(0);
    }, [item.isExpanded]);
    var handleSpace = function handleSpace(indx, size) {
      if (indx < size) return {
        marginBottom: 8
      };
      return {};
    };
    (0, _react.useEffect)(function () {
      var fetchAtomRMConfig = function fetchAtomRMConfig() {
        var mapFlagEnable = (0, _remoteConfig.isFlagON)(_remoteConfig.REMOTE_CONFIG_FLAGS.ATOMS_MAP);
        setMapRMFlag(mapFlagEnable);
      };
      fetchAtomRMConfig();
    }, []);
    var _handleFindDirection = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        if (isConnected) {
          var _item$locationDetails, _item$locationDetails2;
          refRetryAction.current = null;
          setConnection(true);
          if (!mapRMFlag) {
            return mapUnavailable == null ? undefined : mapUnavailable.current.show();
          }
          (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppATOMSEntryClick, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppATOMSEntryClick, `Dine Details|Get Directions|${item == null || (_item$locationDetails = item.locationDetails) == null ? undefined : _item$locationDetails[index].localRef}`));
          (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppDineDetail, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppDineDetail, `Dine Information | Get Direction`));
          navigation.push(_constants.NavigationConstants.changiMap, {
            localRef: item == null || (_item$locationDetails2 = item.locationDetails) == null ? undefined : _item$locationDetails2[index].localRef
          });
          return;
        }
        refRetryAction.current = _handleFindDirection;
        setConnection(false);
      });
      return function handleFindDirection() {
        return _ref2.apply(this, arguments);
      };
    }();
    var viewMenuOnPress = function viewMenuOnPress(menuLink) {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppDineDetailDineInformationViewMenu, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppDineDetailDineInformationViewMenu, tenantName));
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppDineDetail, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppDineDetail, `Dine Information | ${(0, _i18n.translate)("restaurantDetails.viewMenu")}`));
      var isPDF = (0, _file.isPdf)(menuLink);
      if (isPDF) {
        navigation.navigate(_constants.NavigationConstants.pdfView, {
          uri: menuLink,
          title: (0, _i18n.translate)("restaurantDetails.viewMenu")
        });
        return;
      } else {
        navigation.navigate(_constants.NavigationConstants.webview, {
          uri: menuLink
        });
      }
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.wrapInformationDetail,
      children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        style: styles.header,
        onPress: onClickFunction,
        testID: `${SCREEN_NAME}__TouchableExpand__${key}`,
        accessibilityLabel: `${SCREEN_NAME}__TouchableExpand__${key}`,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.flexStyleHeader,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.headerStyle,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              preset: "h4",
              style: styles.txtColor,
              text: getMapName(item, dotUnicode)
            }), !hourComment && renderCloseStatus(item)]
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.arrowIconStyle,
            children: item.isExpanded ? (0, _jsxRuntime.jsx)(_icons.TopArrow, {}) : (0, _jsxRuntime.jsx)(_icons.DownArrow, {})
          })]
        })
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: [{
          height: layoutHeight
        }, styles.visibileStyle],
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.content,
          children: [hourComment && (0, _jsxRuntime.jsx)(_text.Text, {
            numberOfLines: 3,
            style: styles.temporaryClosedStyle,
            children: hourComment
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.flexStyle,
            children: [(0, _jsxRuntime.jsx)(_icons.DirectionsOutline, {
              style: styles.directionIconstyle
            }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: styles.areaInfoViewStyle,
              children: [(0, _jsxRuntime.jsxs)(_text.Text, {
                numberOfLines: 2,
                preset: "bodyTextRegular",
                style: styles.text,
                children: [item.terminal, " " + item.description ? " " + item.description + ", " : " ", item.unitNo ? "#" + item.unitNo : " ", ",", " ", item.area + (0, _i18n.translate)("restaurantDetails.area")]
              }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                onPress: _handleFindDirection,
                disabled: (0, _lodash.isEmpty)(item == null || (_item$locationDetails3 = item.locationDetails) == null || (_item$locationDetails3 = _item$locationDetails3[index]) == null ? undefined : _item$locationDetails3.localRef),
                children: (0, _jsxRuntime.jsx)(_text.Text, {
                  preset: "bodyTextBold",
                  style: (0, _lodash.isEmpty)(item == null || (_item$locationDetails4 = item.locationDetails) == null || (_item$locationDetails4 = _item$locationDetails4[index]) == null ? undefined : _item$locationDetails4.localRef) ? styles.getDirectionStyleDisabled : styles.getDirectionStyle,
                  children: "Get directions"
                })
              })]
            })]
          }), ((_item$timingsInfo = item.timingsInfo) == null ? undefined : _item$timingsInfo.length) > 0 ? (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.flexStyle,
            children: [(0, _jsxRuntime.jsx)(_icons.ClockOutline, {
              style: styles.clockIconStyle
            }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: styles.timingsInfoViewStyle,
              children: (_item$timingsInfo2 = item.timingsInfo) == null ? undefined : _item$timingsInfo2.map(function (res, keyVal) {
                var _item$timingsInfo3;
                return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                  style: [styles.timingStyle, handleSpace(keyVal, item == null || (_item$timingsInfo3 = item.timingsInfo) == null ? undefined : _item$timingsInfo3.length)],
                  children: [(0, _jsxRuntime.jsx)(_text.Text, {
                    preset: "bodyTextRegular",
                    style: styles.timingsInfoDayText,
                    children: getDay(res.day)
                  }), (0, _jsxRuntime.jsx)(_text.Text, {
                    preset: "bodyTextRegular",
                    style: styles.timingsText,
                    children: res.timings
                  })]
                }, keyVal);
              })
            })]
          }) : null, ((_item$phoneDetails = item.phoneDetails) == null ? undefined : _item$phoneDetails.length) > 0 ? (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.flexStyle,
            children: [(0, _jsxRuntime.jsx)(_icons.PhoneOutline, {
              style: styles.phoneIconStyle
            }), item.phoneDetails.map(function (number, i) {
              return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                onPress: function onPress() {
                  return dialCall(number);
                },
                testID: `${SCREEN_NAME}__TouchableDialCall__${key}`,
                accessibilityLabel: `${SCREEN_NAME}__TouchableDialCall__${key}`,
                children: (0, _jsxRuntime.jsx)(_text.Text, {
                  preset: "textLink",
                  style: styles.phoneNumberStyle,
                  children: getTextNumber(i, number)
                }, i)
              }, i);
            })]
          }) : null, !(0, _lodash.isEmpty)(item == null || (_item$locationDetails5 = item.locationDetails) == null || (_item$locationDetails5 = _item$locationDetails5[index]) == null ? undefined : _item$locationDetails5.menuLink) && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: function onPress() {
              var _item$locationDetails6;
              return viewMenuOnPress(item == null || (_item$locationDetails6 = item.locationDetails) == null || (_item$locationDetails6 = _item$locationDetails6[index]) == null ? undefined : _item$locationDetails6.menuLink);
            },
            children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: styles.menuLinkContainer,
              children: [(0, _jsxRuntime.jsx)(_icons.FoodMenuIcon, {}), (0, _jsxRuntime.jsx)(_text.Text, {
                tx: "restaurantDetails.viewMenu",
                preset: "textLink",
                style: styles.menuLinkStyles,
                numberOfLines: 1
              })]
            })
          })]
        }, "content")
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.separator
      }), (0, _jsxRuntime.jsx)(_bottomSheetUnableLoadLocation.BottomSheetUnableLoadLocation, {
        ref: unableToLoadLocationRef
      }), (0, _jsxRuntime.jsx)(_bottomSheetMapUnavailable.BottomSheetMapUnavailable, {
        ref: mapUnavailable
      })]
    });
  };
  var _worklet_10084365685729_init_data = {
    code: "function restaurantDetailsTsx1(event){const{scrollY}=this.__closure;scrollY.value=event.contentOffset.y;}"
  };
  var RestaurantDetailsScreen = exports.RestaurantDetailsScreen = function RestaurantDetailsScreen(_ref3) {
    var navigation = _ref3.navigation,
      route = _ref3.route;
    var tenantId = (0, _react.useRef)("");
    tenantId.current = route.params.tenantId;
    var name = route.params.name || "";
    var dotUnicode = (0, _constants.getDotUnicode)();
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      loading = _useState6[0],
      setLoading = _useState6[1];
    var dispatch = (0, _reactRedux.useDispatch)();
    var inset = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var restaurantData = (0, _reactRedux.useSelector)(function (data) {
      return _dineRedux.DineSelectors.restaurantDetailsData(data);
    });
    var changiEatsPayload = (0, _lodash.get)(restaurantData, "changiEatsPayload");
    var changiRewardsPayload = (0, _lodash.get)(restaurantData, "changiRewardsPayload");
    var nameTagLocationPayload = (0, _lodash.get)(restaurantData, "nameTagLocationPayload");
    var blogsAndReviewsPayload = (0, _lodash.get)(restaurantData, "blogsAndReviewsPayload");
    var _useState7 = (0, _react.useState)(true),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      isConnection = _useState8[0],
      setConnection = _useState8[1];
    var restaurantDetailsData = nameTagLocationPayload == null ? undefined : nameTagLocationPayload.data;
    var rewardInfoData = changiRewardsPayload == null ? undefined : changiRewardsPayload.data;
    var changiEatsData = changiEatsPayload == null ? undefined : changiEatsPayload.data;
    var blogsAndReviewsData = blogsAndReviewsPayload == null ? undefined : blogsAndReviewsPayload.data;
    var locationDetails = restaurantDetailsData == null ? undefined : restaurantDetailsData.locationDetails;
    var _useState9 = (0, _react.useState)([]),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      listDataSource = _useState0[0],
      setListDataSource = _useState0[1];
    var profile = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var isStaff = profile == null ? undefined : profile.airportStaff;
    var _useState1 = (0, _react.useState)([]),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      availableDiscounts = _useState10[0],
      setAvailableDiscounts = _useState10[1];
    var _useState11 = (0, _react.useState)([]),
      _useState12 = (0, _slicedToArray2.default)(_useState11, 2),
      availableDealsPromos = _useState12[0],
      setAvailableDealsPromos = _useState12[1];
    var refRetryAction = (0, _react.useRef)(null);
    var _useModal = (0, _useModal2.useModal)("dineDetailError"),
      isModalVisible = _useModal.isModalVisible,
      openModal = _useModal.openModal,
      closeModal = _useModal.closeModal;
    var scrollY = (0, _reactNativeReanimated.useSharedValue)(0);
    var scrollHandler = (0, _reactNativeReanimated.useAnimatedScrollHandler)({
      onScroll: function () {
        var restaurantDetailsTsx1 = function restaurantDetailsTsx1(event) {
          scrollY.value = event.contentOffset.y;
        };
        restaurantDetailsTsx1.__closure = {
          scrollY: scrollY
        };
        restaurantDetailsTsx1.__workletHash = 10084365685729;
        restaurantDetailsTsx1.__initData = _worklet_10084365685729_init_data;
        return restaurantDetailsTsx1;
      }()
    });
    var onPressBackButton = function onPressBackButton() {
      navigation.goBack();
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppDineDetail, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppDineDetail, "Back to Previous Page"));
    };
    var getStaffAvailableDiscount = /*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* () {
        var featureFlagEnable = (0, _remoteConfig.isFlagON)(_remoteConfig.REMOTE_CONFIG_FLAGS.TENANTDETAILS_STAFFPERKS);
        if (!featureFlagEnable || !isLoggedIn || !isStaff) {
          return;
        }
        var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch2.isConnected;
        if (isConnected) {
          var staffDiscount = yield (0, _staffPerkSaga.getStaffPerkAvailableDiscount)({
            tenantId: tenantId.current
          });
          if (!(0, _lodash.isEmpty)(staffDiscount)) {
            setAvailableDiscounts(staffDiscount);
          }
        }
      });
      return function getStaffAvailableDiscount() {
        return _ref4.apply(this, arguments);
      };
    }();
    var getRestaurantDetails = _react.default.useCallback(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      var _yield$NetInfo$fetch3 = yield _netinfo.default.fetch(),
        isConnected = _yield$NetInfo$fetch3.isConnected;
      if (isConnected) {
        setConnection(true);
        dispatch(_dineRedux.default.restaurantDetailsRequest(tenantId.current));
        dispatch(_dineRedux.default.restaurantBlogsAndReviewsRequest(tenantId.current));
        dispatch(_dineRedux.default.restaurantChangiEatsRequest(tenantId.current));
        dispatch(_dineRedux.default.restaurantRewardInfoRequest(tenantId.current));
        if (isStaff && !(0, _lodash.isEmpty)(tenantId.current)) {
          getStaffAvailableDiscount();
        }
        // Fetch Deals & Promos tenant details
        var isShopDineV2Enabled = (0, _remoteConfig.getFeatureFlagInit)(_remoteConfig.REMOTE_CONFIG_FLAGS.SHOPDINE_V2);
        if (isShopDineV2Enabled) {
          var promos = yield (0, _staffPerkSaga.getDealsPromosAvailableDiscount)({
            tenantId: tenantId.current
          });
          if (!(0, _lodash.isEmpty)(promos)) {
            setAvailableDealsPromos(promos);
          }
        }
        return;
      }
      setConnection(false);
    }), []);
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)(`Dine_Detail_${name}`);
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)(`Dine_Detail_${name}`, (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribeFocus;
    }, [navigation]);
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      getRestaurantDetails();
    }, []));
    if (_reactNative2.Platform.OS === "android") {
      _reactNative2.UIManager.setLayoutAnimationEnabledExperimental(true);
    }
    _react.default.useEffect(function () {
      setListDataSource(locationDetails);
    });
    var updateLayout = function updateLayout(index) {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppDineDetail, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppDineDetail, `${_adobe.AdobeValueByTagName.CAppDineDetailDineInformation}View Information`));
      _reactNative2.LayoutAnimation.configureNext(Object.assign({}, _reactNative2.LayoutAnimation.Presets.easeInEaseOut, {
        duration: 100
      }));
      var array = (0, _toConsumableArray2.default)(listDataSource);
      array.forEach(function (_value, placeindex) {
        if (placeindex === index) {
          array[placeindex].isExpanded = !array[placeindex].isExpanded;
        } else {
          array[placeindex].isExpanded = false;
        }
      });
      setListDataSource(array);
    };
    var transformPhoneNumber = function transformPhoneNumber(phone) {
      if (_reactNative2.Platform.OS === "android") {
        return `tel:${phone}`;
      }
      return `telprompt:${phone}`;
    };
    var dialCall = function dialCall(num) {
      var phoneNumber = transformPhoneNumber(num);
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppDineDetail, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppDineDetail, `${_adobe.AdobeValueByTagName.CAppDineDetailDineInformation}Phone Number`));
      _reactNative2.Linking.openURL(phoneNumber);
    };
    var handleReadMorePress = function handleReadMorePress(handlePress) {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppDineDetail, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppDineDetail, `${_adobe.AdobeValueByTagName.CAppDineDetailAbout}Read more`));
      handlePress == null || handlePress();
    };
    var handleReserveChope = function handleReserveChope() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppDineDetailDineInformationReserve, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppDineDetailDineInformationReserve, `${(0, _i18n.translate)("restaurantDetails.reserve")}`));
      navigation.navigate(_constants.NavigationConstants.playpassWebview, {
        uri: restaurantDetailsData == null ? undefined : restaurantDetailsData.chopeUrl,
        needBackButton: true,
        needCloseButton: true
      });
    };
    var readMore = function readMore(handlePress) {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: changiEatsData && (blogsAndReviewsData == null ? undefined : blogsAndReviewsData.length) > 0 ? styles.loadMoreShowLessView : styles.loadMoreShowLessPaddingView,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          preset: "bodyTextBold",
          style: styles.loadMoreShowLessTextStyle,
          onPress: function onPress() {
            return handleReadMorePress(handlePress);
          },
          children: "Read more"
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: function onPress() {
            return handleReadMorePress(handlePress);
          },
          testID: `${SCREEN_NAME}__TouchableReadMore`,
          accessibilityLabel: `${SCREEN_NAME}__TouchableReadMore`,
          children: (0, _jsxRuntime.jsx)(_icons.DownArrow, {
            style: styles.loadMoreShowLessIconStyle
          })
        })]
      });
    };
    var showLess = function showLess(handlePress) {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: changiEatsData && (blogsAndReviewsData == null ? undefined : blogsAndReviewsData.length) > 0 ? styles.loadMoreShowLessView : styles.loadMoreShowLessPaddingView,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          preset: "bodyTextBold",
          style: styles.loadMoreShowLessTextStyle,
          onPress: handlePress,
          children: "Show less"
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: handlePress,
          testID: `${SCREEN_NAME}__TouchableShowLess`,
          accessibilityLabel: `${SCREEN_NAME}__TouchableShowLess`,
          children: (0, _jsxRuntime.jsx)(_icons.TopArrow, {
            style: styles.loadMoreShowLessIconStyle
          })
        })]
      });
    };
    var renderTagLocation = function renderTagLocation() {
      if ((nameTagLocationPayload == null ? undefined : nameTagLocationPayload.type) === loadingType) {
        return (0, _jsxRuntime.jsx)(_restuarantDetailsLoadingView.RestaurantLoadingDetailsScreen, {});
      }
      if (nameTagLocationPayload != null && nameTagLocationPayload.hasError) {
        return (0, _jsxRuntime.jsx)(_error.ErrorComponent, {
          style: styles.topErrorComponentStyle,
          type: _error.ErrorComponentType.standard,
          onPressed: function onPressed() {
            dispatch(_dineRedux.default.restaurantDetailsRequest(tenantId.current));
          },
          testID: `${SCREEN_NAME}__ErrorComponentRestaurantDetailsRequest`,
          accessibilityLabel: `${SCREEN_NAME}__ErrorComponentRestaurantDetailsRequest`
        });
      }
      if (restaurantDetailsData) {
        return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [(0, _jsxRuntime.jsx)(_hero.Hero, {
            heroImagesUrl: restaurantDetailsData.heroCarouselImage,
            logoImageUrl: restaurantDetailsData.heroCarouselLogo,
            type: nameTagLocationPayload.type,
            testID: `${SCREEN_NAME}__HeroCarouselImage`,
            accessibilityLabel: `${SCREEN_NAME}__HeroCarouselImage`,
            showWave: false,
            onSwiperChange: function onSwiperChange() {
              (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppDineDetail, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppDineDetail, "Carousel"));
            }
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              preset: "h1",
              style: styles.titleText,
              children: restaurantDetailsData.title
            }), !(0, _lodash.isEmpty)(availableDiscounts) && (0, _jsxRuntime.jsx)(_availableStaffDiscounts.default, {
              availableDiscounts: availableDiscounts,
              navigation: navigation,
              storeName: restaurantDetailsData.title
            }), !(0, _lodash.isEmpty)(availableDealsPromos) && (0, _jsxRuntime.jsx)(_availableDealsPromos.default, {
              dealsPromos: availableDealsPromos,
              navigation: navigation,
              storeName: restaurantDetailsData.title
            }), (0, _jsxRuntime.jsx)(_reactNative2.ScrollView, {
              testID: `${SCREEN_NAME}__ScrollViewListDataSource`,
              accessibilityLabel: `${SCREEN_NAME}__ScrollViewListDataSource`,
              children: listDataSource == null ? undefined : listDataSource.map(function (item, key) {
                return (0, _jsxRuntime.jsx)(ExpandableComponent, {
                  onClickFunction: function onClickFunction() {
                    updateLayout(key);
                  },
                  item: item,
                  index: key,
                  tenantName: name,
                  dialCall: dialCall,
                  dotUnicode: dotUnicode,
                  _setLoading: setLoading,
                  setConnection: setConnection,
                  refRetryAction: refRetryAction
                }, key);
              })
            })]
          })]
        });
      }
      return null;
    };
    var renderInfoBanner = function renderInfoBanner() {
      if (changiRewardsPayload != null && changiRewardsPayload.hasError) {
        return (0, _jsxRuntime.jsx)(_error.ErrorComponent, {
          type: _error.ErrorComponentType.standard,
          onPressed: function onPressed() {
            dispatch(_dineRedux.default.restaurantRewardInfoRequest(tenantId.current));
          }
        });
      }
      if (rewardInfoData) {
        return (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.infoBannerView,
          children: (0, _jsxRuntime.jsx)(_infoBanner.InfoBanner, {
            title: rewardInfoData.rewardTitle,
            state: rewardInfoData.rewardState,
            type: rewardInfoData.type,
            text: rewardInfoData.rewardText,
            linkText: rewardInfoData.rewardLinkText,
            link: rewardInfoData.rewardLink,
            onPressed: function onPressed() {
              (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppDineDetailRewardsTab, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppDineDetailRewardsTab, rewardInfoData == null ? undefined : rewardInfoData.rewardTitle));
              navigation.navigate(_constants.NavigationConstants.webview, {
                uri: rewardInfoData.rewardLink
              });
            }
          })
        });
      }
      return null;
    };
    var renderAboutRestaurant = function renderAboutRestaurant() {
      if (restaurantDetailsData != null && restaurantDetailsData.description) {
        return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.aboutViewStyle,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            preset: "h4",
            style: styles.aboutViewHeaderText,
            children: (0, _i18n.translate)("restaurantDetails.about")
          }), (0, _jsxRuntime.jsx)(_readMore.default, {
            numberOfLines: 5,
            renderTruncatedFooter: readMore,
            renderRevealedFooter: showLess,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              preset: "bodyTextRegular",
              style: styles.aboutViewText,
              text: restaurantDetailsData.description
            })
          })]
        });
      }
      return null;
    };
    var renderChope = function renderChope() {
      return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
        children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: wrapChope,
          children: (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
            style: touchableReserve,
            onPress: handleReserveChope,
            children: [(0, _jsxRuntime.jsx)(_icons.CheckDate, {}), (0, _jsxRuntime.jsx)(_text.Text, {
              tx: "restaurantDetails.reserve",
              style: touchableTextChope,
              preset: "caption1Bold"
            })]
          })
        })
      });
    };
    var renderContent = function renderContent() {
      return (0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.ScrollView, {
        showsVerticalScrollIndicator: false,
        testID: `${SCREEN_NAME}__ScrollViewTagLocation`,
        accessibilityLabel: `${SCREEN_NAME}__ScrollViewTagLocation`,
        contentContainerStyle: paddingContentScrollView,
        onScroll: scrollHandler,
        children: [renderTagLocation(), !(0, _lodash.isEmpty)(restaurantDetailsData == null ? undefined : restaurantDetailsData.chopeUrl) && renderChope(), renderInfoBanner(), renderIscCtaButton(), renderAboutRestaurant(), (0, _jsxRuntime.jsx)(_cusineInDetail.CuisineInDetail, {
          data: restaurantDetailsData == null ? undefined : restaurantDetailsData.exploreCategories,
          screen: "DINE"
        })]
      });
    };
    var hasPageError = (nameTagLocationPayload == null ? undefined : nameTagLocationPayload.hasError) && (changiRewardsPayload == null ? undefined : changiRewardsPayload.hasError) && (changiEatsPayload == null ? undefined : changiEatsPayload.hasError) && (blogsAndReviewsPayload == null ? undefined : blogsAndReviewsPayload.hasError);
    var onRetryError = function onRetryError() {
      if (refRetryAction.current) {
        refRetryAction.current == null || refRetryAction.current();
        return;
      }
      getRestaurantDetails();
    };
    var handleNavigateCSMIShopchangi = /*#__PURE__*/function () {
      var _ref6 = (0, _asyncToGenerator2.default)(function* (url) {
        var ecid = yield (0, _adobe.getExperienceCloudId)();
        var target = (0, _deeplinkParameter.getISCLinkRedirectTarget)(url);
        var payload = {
          stateCode: _constants.StateCode.ISHOPCHANGI,
          input: Object.assign({}, target, {
            ecid: ecid
          })
        };
        try {
          var response = yield (0, _pageConfigSaga.getDeepLinkV2)(payload, true);
          if (response != null && response.redirectUri) {
            navigation.navigate(_constants.NavigationConstants.playpassWebview, {
              uri: response == null ? undefined : response.redirectUri,
              needBackButton: true,
              needCloseButton: true,
              headerType: _enum.WebViewHeaderTypes.default,
              basicAuthCredential: response == null ? undefined : response.basicAuth
            });
          } else {
            throw response;
          }
        } catch (error) {
          openModal();
        }
      });
      return function handleNavigateCSMIShopchangi(_x) {
        return _ref6.apply(this, arguments);
      };
    }();
    var renderIscCtaButton = function renderIscCtaButton() {
      var _nameTagLocationPaylo;
      if ((0, _lodash.isEmpty)(nameTagLocationPayload == null || (_nameTagLocationPaylo = nameTagLocationPayload.data) == null || (_nameTagLocationPaylo = _nameTagLocationPaylo.aemTenantDetails) == null ? undefined : _nameTagLocationPaylo.iscurl)) {
        return null;
      }
      return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: styles.iscCTAButton,
        onPress: function onPress() {
          var _nameTagLocationPaylo2;
          return handleNavigateCSMIShopchangi(nameTagLocationPayload == null || (_nameTagLocationPaylo2 = nameTagLocationPayload.data) == null || (_nameTagLocationPaylo2 = _nameTagLocationPaylo2.aemTenantDetails) == null ? undefined : _nameTagLocationPaylo2.iscurl);
        },
        children: [(0, _jsxRuntime.jsx)(_icons.ShopBagIcon, {}), (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.iscCTAText,
          tx: "shopDetailScreen.shopOnIshopChangi"
        })]
      });
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.container,
      testID: "ShopDetailsScreen",
      children: [(0, _jsxRuntime.jsx)(_overlayStickyHeader.OverlayStickyHeader, {
        height: 100,
        showAtOffset: 100,
        scrollY: scrollY,
        onBackPress: onPressBackButton,
        title: restaurantDetailsData == null ? undefined : restaurantDetailsData.title
      }), (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsx)(_reactNative2.SafeAreaView, {
          style: styles.headerViewStyle,
          children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: onPressBackButton,
            style: safeViewHeaderForAndroid(inset),
            testID: `${SCREEN_NAME}__TouchableBackButton`,
            accessibilityLabel: `${SCREEN_NAME}__TouchableBackButton`,
            children: (0, _jsxRuntime.jsx)(_icons.BackButton, {})
          })
        }), hasPageError ? (0, _jsxRuntime.jsx)(_error2.ErrorScreen, {
          onReload: function onReload() {
            return getRestaurantDetails();
          },
          testID: `${SCREEN_NAME}__ErrorScreenGetRestaurantDetails`,
          accessibilityLabel: `${SCREEN_NAME}__ErrorScreenGetRestaurantDetails`
        }) : renderContent()]
      }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
        visible: loading
      }), (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        header: true,
        hideScreenHeader: false,
        headerBackgroundColor: "transparent",
        visible: !isConnection,
        onBack: function onBack() {
          navigation.goBack();
        },
        onReload: onRetryError
      }), (0, _jsxRuntime.jsx)(_bottomSheetError.BottomSheetError, {
        openPendingModal: true,
        icon: (0, _jsxRuntime.jsx)(_icons.InfoRed, {}),
        visible: isModalVisible,
        shouldFitContentHeight: true,
        animationInTiming: 500,
        animationOutTiming: 500,
        title: (0, _i18n.translate)("popupError.somethingWrongOneline"),
        colorMsg: _theme.color.palette.almostBlackGrey,
        errorMessage: (0, _i18n.translate)("popupError.networkErrorMessage"),
        onClose: closeModal,
        buttonText: (0, _i18n.translate)("common.close"),
        testID: `${SCREEN_NAME}__BottomSheetErrorISC`,
        accessibilityLabel: `${SCREEN_NAME}__BottomSheetErrorISC`
      })]
    });
  };
