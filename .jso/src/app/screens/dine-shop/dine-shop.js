  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.screenTabBarOption = exports.DineShopScreen = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _materialTopTabs = _$$_REQUIRE(_dependencyMap[2]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _color = _$$_REQUIRE(_dependencyMap[6]);
  var _dineScreen = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _shopScreen = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _marketPlaceScreen = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _HeaderTabNavBar = _$$_REQUIRE(_dependencyMap[10]);
  var _adobe = _$$_REQUIRE(_dependencyMap[11]);
  var _dineShopContext = _$$_REQUIRE(_dependencyMap[12]);
  var _native = _$$_REQUIRE(_dependencyMap[13]);
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[14]);
  var _constants = _$$_REQUIRE(_dependencyMap[15]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[16]);
  var Tab = (0, _materialTopTabs.createMaterialTopTabNavigator)();
  var styles = _reactNative.StyleSheet.create({
    containerStyle: {
      backgroundColor: _color.color.palette.whiteGrey,
      flex: 1
    },
    tabContainerStyle: {
      margin: 0,
      padding: 0,
      width: null
    },
    tabIndicatorContainerStyle: {
      marginLeft: 10,
      width: 120
    },
    tabIndicatorStyle: {
      backgroundColor: _color.color.palette.lightPurple,
      borderRadius: 56,
      height: 4,
      width: 55
    },
    tabLabelStyle: Object.assign({}, _text.presets.h2Tabs),
    tabMainStyle: {
      elevation: 0,
      margin: 0,
      marginLeft: 15,
      padding: 0,
      paddingTop: 20,
      shadowOffset: {
        width: 0,
        height: 0
      },
      shadowOpacity: 0,
      shadowRadius: 6,
      width: null
    },
    tabStyle: {
      backgroundColor: _color.color.palette.transparent,
      height: null,
      margin: 0,
      padding: 0,
      width: 75
    }
  });
  var screenTabBarOption = exports.screenTabBarOption = {
    tabBarInactiveTintColor: _color.color.palette.darkestGrey,
    tabBarActiveTintColor: _color.color.palette.lightPurple,
    tabBarLabelStyle: styles.tabLabelStyle,
    tabBarItemStyle: styles.tabStyle,
    tabBarContentContainerStyle: styles.tabContainerStyle,
    tabBarStyle: styles.tabMainStyle,
    tabBarIndicatorContainerStyle: styles.tabIndicatorContainerStyle,
    tabBarIndicatorStyle: styles.tabIndicatorStyle,
    lazy: true
  };
  var SCREEN_NAME = "DineShopScreen";
  var DineShopScreen = exports.DineShopScreen = function DineShopScreen(_ref) {
    var navigation = _ref.navigation,
      route = _ref.route;
    var _ref2 = (route == null ? undefined : route.params) || {},
      initActiveTabKey = _ref2.initActiveTabKey;
    var initialTabRouteName = initActiveTabKey || "dine";
    _dineShopContext.Handlers.shop.routeParams = route == null ? undefined : route.params;
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      _reactNative.InteractionManager.runAfterInteractions(function () {
        (0, _mmkvStorage.setMMKVdata)(_mmkvStorage.ENUM_STORAGE_MMKV.IS_FIRST_APP, true);
      });
      return function () {
        navigation == null || navigation.setParams == null || navigation.setParams({
          screen: null,
          section: null,
          params: null
        });
      };
    }, []));
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.containerStyle,
      testID: "DineShopScreen",
      children: [(0, _jsxRuntime.jsx)(_reactNative.StatusBar, {
        translucent: true,
        backgroundColor: "transparent",
        barStyle: "light-content"
      }), (0, _jsxRuntime.jsx)(_dineShopContext.DineShopContext.Provider, {
        value: {
          Handlers: _dineShopContext.Handlers
        },
        children: (0, _jsxRuntime.jsxs)(Tab.Navigator, {
          initialRouteName: initialTabRouteName,
          tabBar: function tabBar(props) {
            return (0, _jsxRuntime.jsx)(_HeaderTabNavBar.HeaderTabNavBar, {
              props: Object.assign({}, props),
              testID: `${SCREEN_NAME}__FlyLandingHeaderTabNavBar`,
              accessibilityLabel: `${SCREEN_NAME}__FlyLandingHeaderTabNavBar`
            });
          },
          children: [(0, _jsxRuntime.jsx)(Tab.Screen, {
            name: _constants.DINE_SHOP_TAB_SCREENS.dine,
            initialParams: navigation,
            component: _dineScreen.default,
            options: Object.assign({}, screenTabBarOption, {
              tabBarLabel: "Dine",
              tabBarTestID: `${SCREEN_NAME}__TabDine`,
              tabBarAccessibilityLabel: `${SCREEN_NAME}__TabDine`
            }),
            listeners: {
              tabPress: function tabPress() {
                (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppShopDineTopMenuToggle, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppShopDineTopMenuToggle, "Dine"));
              }
            }
          }), (0, _jsxRuntime.jsx)(Tab.Screen, {
            name: _constants.DINE_SHOP_TAB_SCREENS.shop,
            initialParams: navigation,
            component: _shopScreen.default,
            options: Object.assign({}, screenTabBarOption, {
              tabBarLabel: "Shop",
              tabBarTestID: `${SCREEN_NAME}__TabShop`,
              tabBarAccessibilityLabel: `${SCREEN_NAME}__TabShop`
            }),
            listeners: {
              tabPress: function tabPress() {
                (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppShopDineTopMenuToggle, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppShopDineTopMenuToggle, "Shop"));
              }
            }
          }), (0, _jsxRuntime.jsx)(Tab.Screen, {
            name: _constants.DINE_SHOP_TAB_SCREENS.marketplace,
            initialParams: navigation,
            component: _marketPlaceScreen.default,
            options: Object.assign({}, screenTabBarOption, {
              tabBarLabel: "Marketplace",
              tabBarTestID: `${SCREEN_NAME}__TabMarketPlace`,
              tabBarAccessibilityLabel: `${SCREEN_NAME}__TabMarketPlaceScreen`
            }),
            listeners: {
              tabPress: function tabPress() {
                (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppShopDineTopMenuToggle, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppShopDineTopMenuToggle, "Marketplace"));
              }
            }
          })]
        })
      })]
    });
  };
