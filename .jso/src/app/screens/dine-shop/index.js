  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.DineShopScreenWrapper = undefined;
  var _dineShop = _$$_REQUIRE(_dependencyMap[0]);
  var _dineScreenV = _$$_REQUIRE(_dependencyMap[1]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[2]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[3]);
  var DineShopScreenWrapper = exports.DineShopScreenWrapper = function DineShopScreenWrapper(props) {
    var isShopDineV2 = (0, _remoteConfig.getFeatureFlagInit)(_remoteConfig.REMOTE_CONFIG_FLAGS.SHOPDINE_V2);
    if (isShopDineV2) return (0, _jsxRuntime.jsx)(_dineScreenV.DineScreenV2, Object.assign({}, props));
    return (0, _jsxRuntime.jsx)(_dineShop.DineShopScreen, Object.assign({}, props));
  };
