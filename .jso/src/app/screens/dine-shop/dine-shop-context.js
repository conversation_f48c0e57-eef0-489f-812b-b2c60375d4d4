  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.Handlers = exports.DineShopContext = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var Handlers = exports.Handlers = {
    dine: {
      handleDineComponentClick: null,
      handleTenantComponentClick: null
    },
    shop: {
      routeParams: null,
      overallScrollRef: null,
      handleTenantComponentClick: null,
      handleMasonryComponentClick: null,
      handleShopSpinComponentClick: null,
      handleNotToBeMissedComponentClick: null,
      handleSpotlightBrandOfferComponentClick: null
    }
  };
  var DineShopContext = exports.DineShopContext = _react.default.createContext({
    Handlers: Handlers
  });
