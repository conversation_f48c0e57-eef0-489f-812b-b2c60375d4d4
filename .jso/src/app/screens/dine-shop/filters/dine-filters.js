  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.DineFilter = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _button = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _checkbox = _$$_REQUIRE(_dependencyMap[4]);
  var _chip = _$$_REQUIRE(_dependencyMap[5]);
  var _chip2 = _$$_REQUIRE(_dependencyMap[6]);
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _i18n = _$$_REQUIRE(_dependencyMap[8]);
  var _dineRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[9]));
  var _theme = _$$_REQUIRE(_dependencyMap[10]);
  var _constants = _$$_REQUIRE(_dependencyMap[11]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[12]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[13]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[15]);
  var _error = _$$_REQUIRE(_dependencyMap[16]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[17]));
  var _lodash = _$$_REQUIRE(_dependencyMap[18]);
  var _adobe = _$$_REQUIRE(_dependencyMap[19]);
  var _utils = _$$_REQUIRE(_dependencyMap[20]);
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[21]));
  var _native = _$$_REQUIRE(_dependencyMap[22]);
  var _icons = _$$_REQUIRE(_dependencyMap[23]);
  var _text2 = _$$_REQUIRE(_dependencyMap[24]);
  var _dineShopFilter2 = _$$_REQUIRE(_dependencyMap[25]);
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[26]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[27]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var closeIconStyles = {
    color: _theme.color.palette.lightPurple
  };
  var myParams = ["locations", "areas"];
  var COMPONENT_NAME = "DineFilter";
  var DineFilter = exports.DineFilter = function DineFilter(props) {
    var showFilterModal = props.showFilterModal,
      setShowFilterModal = props.setShowFilterModal;
    var dispatch = (0, _reactRedux.useDispatch)();
    var navigation = (0, _native.useNavigation)();
    var filterState = (0, _reactRedux.useSelector)(function (state) {
      return _dineRedux.DineSelectors.filterItems(state);
    });
    var filterTitles = (0, _reactRedux.useSelector)(function (state) {
      return _dineRedux.DineSelectors.filterTitles(state);
    });
    if (!filterState) {
      filterState = [];
    }
    (0, _react.useEffect)(function () {
      _reactNative2.InteractionManager.runAfterInteractions(function () {
        dispatch(_dineRedux.default.dineFilterParametersRequest());
      });
    }, []);
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      return function () {
        _reactNative2.InteractionManager.runAfterInteractions(function () {
          (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
          setShowFilterModal(false);
        });
      };
    }, []));
    var handleClearAllFilters = function handleClearAllFilters() {
      dispatch(_dineRedux.default.dineResetFilterItems());
    };
    var dineFilterParameters = (0, _reactRedux.useSelector)(function (state) {
      return _dineRedux.DineSelectors.filtersParametersData(state);
    });
    var filterPillsPayload = (0, _reactRedux.useSelector)(function (state) {
      return _dineRedux.DineSelectors.filterPillsPayload(state);
    });
    var isNotFilterable = !dineFilterParameters || (dineFilterParameters == null ? undefined : dineFilterParameters.length) === 0;
    var filterableStyles = (0, _react.useMemo)(function () {
      return isNotFilterable ? {
        label: {
          color: _theme.color.palette.darkGrey
        },
        wrapper: {
          backgroundColor: _theme.color.palette.lightGrey
        },
        closeIcon: {
          color: _theme.color.palette.darkestGrey
        }
      } : {};
    }, [isNotFilterable]);
    var hasError = filterPillsPayload == null ? undefined : filterPillsPayload.errorFlag;
    var handleAllToggle = function handleAllToggle(item, childTags) {
      dispatch(_dineRedux.default.dineHandleCheckboxFilterItems(item, childTags));
    };
    var handleState = function handleState(item, child) {
      dispatch(_dineRedux.default.dineHandleFilterDetails(item, child));
    };
    var getContainerDineFilterStyle = function getContainerDineFilterStyle(item) {
      if ((item == null ? undefined : item.tagName) === myParams[0]) {
        return styles.flexColumn;
      }
      if (myParams.includes("area")) {
        return styles.firstStyle;
      }
      return styles.secondStyle;
    };
    var getContainerCheckBoxStyle = function getContainerCheckBoxStyle(item, index) {
      var firstStyle = (item == null ? undefined : item.tagName) === myParams[1] ? styles.areaCheckboxContainer : styles.locationCheckboxContainer;
      var secondStyle = index > 0 && (item == null ? undefined : item.tagName) === myParams[1] ? styles.marginForArea : null;
      return [firstStyle, secondStyle];
    };
    var getValueCheckBox = function getValueCheckBox(item, childTag, checkifExists) {
      var isActiveCheckBox = item.childTags.length === (checkifExists == null ? undefined : checkifExists.child.length) ? item : null;
      return (checkifExists == null ? undefined : checkifExists.child.indexOf(childTag.tagName)) >= 0 || isActiveCheckBox;
    };
    var renderCheckBoxAll = function renderCheckBoxAll(item, index, checkifExists) {
      if ((item == null ? undefined : item.tagName) === myParams[0] && index === 0) {
        return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.allCheckContainer,
            children: (0, _jsxRuntime.jsx)(_checkbox.Checkbox, {
              onToggle: function onToggle() {
                return handleAllToggle(item.tagName, item.childTags);
              },
              value: item.childTags.length === (checkifExists == null ? undefined : checkifExists.child.length),
              testID: `${COMPONENT_NAME}__CheckBoxAllToggle__${index}`,
              accessibilityLabel: `${COMPONENT_NAME}__CheckBoxAllToggle__${index}`,
              outlineStyle: {
                borderColor: _theme.color.palette.darkGrey
              },
              text: (0, _i18n.translate)("dineShopFilter.all"),
              textStyle: Object.assign({}, _text2.presets.bodyTextRegular, styles.checkboxTitle)
            })
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.lineGrey
          })]
        });
      }
      return null;
    };
    var renderCheckBoxList = function renderCheckBoxList(item, ind, checkifExists, childTag) {
      var _item$tagName;
      var isColumnCheckBox = item == null || (_item$tagName = item.tagName) == null ? undefined : _item$tagName.includes("area");
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: isColumnCheckBox ? styles.checkboxColumnContainer : styles.checkboxContainer,
        children: [renderCheckBoxAll(item, ind, checkifExists), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: getContainerCheckBoxStyle(item, ind),
          children: (0, _jsxRuntime.jsx)(_checkbox.Checkbox, {
            value: getValueCheckBox(item, childTag, checkifExists),
            onToggle: function onToggle() {
              return handleState(item.tagName, childTag);
            },
            testID: `${COMPONENT_NAME}__CheckBoxHandleState__${ind}`,
            accessibilityLabel: `${COMPONENT_NAME}__CheckBoxHandleState__${ind}`,
            outlineStyle: {
              borderColor: _theme.color.palette.darkGrey
            },
            text: childTag.tagTitle,
            textStyle: Object.assign({}, _text2.presets.bodyTextRegular, styles.checkboxTitle)
          })
        }, ind), (item == null ? undefined : item.tagName) !== myParams[1] && (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.lineGrey
        })]
      }, ind);
    };
    var renderChip = function renderChip(item, ind, checkifExists, childTag) {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.chipsContainer,
        children: (0, _jsxRuntime.jsx)(_chip.Chip, {
          onPressed: function onPressed() {
            return handleState(item.tagName, childTag);
          },
          text: childTag.tagTitle,
          type: (checkifExists == null ? undefined : checkifExists.child.indexOf(childTag.tagName)) >= 0 ? _chip2.ChipType.selected : _chip2.ChipType.unSelected,
          testID: `${COMPONENT_NAME}__ChipHandleState__${ind}`,
          accessibilityLabel: `${COMPONENT_NAME}__ChipHandleState__${ind}`
        })
      }, ind);
    };
    var renderDineFilter = function renderDineFilter(item) {
      var _item$childTags;
      if (!item.childTags) {
        return null;
      }
      return (_item$childTags = item.childTags) == null ? undefined : _item$childTags.map(function (childTag, ind) {
        var stringMatch = myParams.includes(item.tagName);
        var checkifExists = filterState.find(function (i) {
          return (i == null ? undefined : i.main) === item.tagName;
        });
        return stringMatch ? renderCheckBoxList(item, ind, checkifExists, childTag) : renderChip(item, ind, checkifExists, childTag);
      });
    };
    var getMarginSection = function getMarginSection(index) {
      if (index !== 0) {
        return {
          marginTop: 50
        };
      }
    };
    var renderContent = function renderContent() {
      return (0, _jsxRuntime.jsxs)(_reactNative2.ScrollView, {
        scrollEnabled: true,
        contentContainerStyle: styles.contentContainer,
        style: styles.scrollContainer,
        testID: `${COMPONENT_NAME}__ScrollViewDineFilter`,
        accessibilityLabel: `${COMPONENT_NAME}__ScrollViewDineFilter`,
        children: [isNotFilterable && (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "caption1Regular",
          style: styles.unableToDisplayStyle,
          tx: "dineScreen.unableToDisplayFilters"
        }), !(0, _lodash.isEmpty)(dineFilterParameters) && !isNotFilterable ? dineFilterParameters == null ? undefined : dineFilterParameters.map(function (item, index) {
          return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: [styles.viewContent, getMarginSection(index)],
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              preset: "subTitleBold",
              text: item.tagTitle,
              style: styles.titleStyle
            }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: getContainerDineFilterStyle(item),
              children: renderDineFilter(item)
            })]
          }, index);
        }) : null]
      });
    };
    var onClosedFilterModal = function onClosedFilterModal() {
      (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
      setShowFilterModal(false);
    };
    var setPreviousData = function setPreviousData() {
      var previousValue = _dineShopFilter2.FilterValueDine.getFilterValueDine();
      try {
        var filterItemValues = JSON.parse(previousValue == null ? undefined : previousValue.filterValueItems);
        var filterItemTitles = JSON.parse(previousValue == null ? undefined : previousValue.filterValueTitles);
        dispatch(_dineRedux.default.setCurrentValueFilter({
          filterValueItems: !(0, _lodash.isEmpty)(filterItemValues) ? filterItemValues : [],
          filterValueTitles: !(0, _lodash.isEmpty)(filterItemTitles) ? filterItemTitles : []
        }));
      } catch (err) {
        dispatch(_dineRedux.default.setCurrentValueFilter({
          filterValueItems: [],
          filterValueTitles: []
        }));
      }
    };
    var onApplyFilter = _react.default.useCallback(function () {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppDineFilterOptions, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppDineFilterOptions, (0, _utils.handleCondition)(filterTitles && (filterTitles == null ? undefined : filterTitles.length) > 0, filterTitles.map(function (item) {
        return item == null ? undefined : item.tagTitle;
      }).join("|"), "1")));
      dispatch(_dineRedux.default.dineSetFilterTitles(filterTitles));
      (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
      setShowFilterModal(false);
      dispatch(_dineRedux.default.startRequestFilter(true));
      //@ts-ignore
      _reactNative2.InteractionManager.runAfterInteractions(function () {
        navigation.navigate(_constants.NavigationConstants.dineFilterResultsScreen);
      });
    }, [filterTitles, filterState]);
    return (0, _jsxRuntime.jsx)(_bottomSheet.default, {
      isModalVisible: showFilterModal,
      containerStyle: styles.bottomSheetContainer,
      onClosedSheet: onClosedFilterModal,
      stopDragCollapse: true,
      onBackPressHandle: onClosedFilterModal,
      animationInTiming: 100,
      animationOutTiming: 100,
      onModalHide: setPreviousData,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.bottomShetFilterContainer,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.headerFilter,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            text: (0, _i18n.translate)("dineShopFilter.titleHeader"),
            style: styles.filterTitle
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: onClosedFilterModal,
            style: styles.btnCloseStyles,
            testID: `${COMPONENT_NAME}__CloseIcon`,
            accessibilityLabel: `${COMPONENT_NAME}__CloseIcon`,
            children: (0, _jsxRuntime.jsx)(_icons.CloseCross, {
              width: 24,
              height: 24,
              fill: "currentColor",
              style: (filterableStyles == null ? undefined : filterableStyles.closeIcon) || closeIconStyles
            })
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.container,
          children: (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
            children: hasError ? (0, _jsxRuntime.jsx)(_error.ErrorScreen, {
              testID: `${COMPONENT_NAME}__ErrorScreen`,
              accessibilityLabel: `${COMPONENT_NAME}__ErrorScreen`,
              onReload: function onReload() {
                return null;
              }
            }) : (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
              children: [renderContent(), (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: styles.buttonContainer,
                children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                  style: styles.flexRow,
                  children: [(0, _utils.handleCondition)(filterState.length > 0 && !isNotFilterable, (0, _jsxRuntime.jsx)(_button.Button, {
                    onPress: handleClearAllFilters,
                    style: styles.clearAllButton,
                    sizePreset: "large",
                    statePreset: "default",
                    typePreset: "secondary",
                    text: (0, _i18n.translate)("dineShopFilter.clearAll"),
                    textStyle: styles.purpleTextColor,
                    textPreset: "buttonLarge",
                    testID: `${COMPONENT_NAME}__ButtonClearAllFilters`,
                    accessibilityLabel: `${COMPONENT_NAME}__ButtonClearAllFilters`
                  }), null), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
                    style: styles.buttonGradient,
                    start: {
                      x: 1,
                      y: 0
                    },
                    end: {
                      x: 0,
                      y: 1
                    },
                    colors: [_theme.color.palette.gradientColor1End, _theme.color.palette.gradientColor1Start],
                    children: (0, _jsxRuntime.jsx)(_button.Button, {
                      sizePreset: "large",
                      statePreset: "default",
                      typePreset: "primary",
                      disabled: isNotFilterable,
                      text: (0, _i18n.translate)("dineShopFilter.applyFilters"),
                      textPreset: "buttonLarge",
                      onPress: onApplyFilter,
                      style: filterableStyles == null ? undefined : filterableStyles.wrapper,
                      textStyle: filterableStyles == null ? undefined : filterableStyles.label,
                      testID: `${COMPONENT_NAME}__ButtonApplyFilters`,
                      accessibilityLabel: `${COMPONENT_NAME}__ButtonApplyFilters`
                    })
                  })]
                })
              })]
            })
          })
        })]
      })
    });
  };
