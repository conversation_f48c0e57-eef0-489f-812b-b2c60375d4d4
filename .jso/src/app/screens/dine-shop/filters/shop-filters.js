  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ShopFilter = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _button = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _checkbox = _$$_REQUIRE(_dependencyMap[4]);
  var _chip = _$$_REQUIRE(_dependencyMap[5]);
  var _chip2 = _$$_REQUIRE(_dependencyMap[6]);
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _i18n = _$$_REQUIRE(_dependencyMap[8]);
  var _shopRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[9]));
  var _theme = _$$_REQUIRE(_dependencyMap[10]);
  var _constants = _$$_REQUIRE(_dependencyMap[11]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[12]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[13]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[15]);
  var _error = _$$_REQUIRE(_dependencyMap[16]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[17]));
  var _lodash = _$$_REQUIRE(_dependencyMap[18]);
  var _adobe = _$$_REQUIRE(_dependencyMap[19]);
  var _utils = _$$_REQUIRE(_dependencyMap[20]);
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[21]));
  var _native = _$$_REQUIRE(_dependencyMap[22]);
  var _icons = _$$_REQUIRE(_dependencyMap[23]);
  var _text2 = _$$_REQUIRE(_dependencyMap[24]);
  var _dineShopFilter2 = _$$_REQUIRE(_dependencyMap[25]);
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[26]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[27]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var closeIconStyles = {
    color: _theme.color.palette.lightPurple
  };
  var COMPONENT_NAME = "ShopFilter";
  var myParams = ["locations", "areas"];
  var ShopFilter = exports.ShopFilter = function ShopFilter(props) {
    var showFilterModal = props.showFilterModal,
      setShowFilterModal = props.setShowFilterModal;
    var dispatch = (0, _reactRedux.useDispatch)();
    var navigation = (0, _native.useNavigation)();
    var filterState = (0, _reactRedux.useSelector)(function (state) {
      return _shopRedux.ShopSelectors.filterItems(state);
    });
    var filterTitles = (0, _reactRedux.useSelector)(function (state) {
      return _shopRedux.ShopSelectors.filterTitles(state);
    });
    var shopFilterParameters = (0, _reactRedux.useSelector)(function (state) {
      return _shopRedux.ShopSelectors.filtersParametersData(state);
    });
    var filterPillsPayload = (0, _reactRedux.useSelector)(function (state) {
      return _shopRedux.ShopSelectors.filterPillsPayload(state);
    });
    var isNotFilterable = !shopFilterParameters || (shopFilterParameters == null ? undefined : shopFilterParameters.length) === 0;
    var filterableStyles = (0, _react.useMemo)(function () {
      return isNotFilterable ? {
        label: {
          color: _theme.color.palette.darkGrey
        },
        wrapper: {
          backgroundColor: _theme.color.palette.lightGrey
        },
        closeIcon: {
          color: _theme.color.palette.darkestGrey
        }
      } : {};
    }, [isNotFilterable]);
    _react.default.useEffect(function () {
      dispatch(_shopRedux.default.shopFilterParametersRequest());
    }, []);
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      return function () {
        _reactNative2.InteractionManager.runAfterInteractions(function () {
          (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
          setShowFilterModal(false);
        });
      };
    }, []));
    var hasError = filterPillsPayload == null ? undefined : filterPillsPayload.errorFlag;
    var handleClearAllFilters = function handleClearAllFilters() {
      dispatch(_shopRedux.default.shopResetFilterItems());
    };
    var handleAllToggle = function handleAllToggle(item, childTags) {
      dispatch(_shopRedux.default.shopHandleCheckboxFilterItems(item, childTags));
    };
    var handleState = function handleState(item, child) {
      dispatch(_shopRedux.default.shopHandleFilterDetails(item, child));
    };
    var getContainerDineFilterStyle = function getContainerDineFilterStyle(item) {
      if ((item == null ? undefined : item.tagName) === myParams[0]) {
        return styles.flexColumn;
      }
      if (myParams.includes("area")) {
        return styles.firstStyle;
      }
      return styles.secondStyle;
    };
    var getContainerCheckBoxStyle = function getContainerCheckBoxStyle(item, index) {
      var firstStyle = (item == null ? undefined : item.tagName) === myParams[1] ? styles.areaCheckboxContainer : styles.locationCheckboxContainer;
      var secondStyle = index > 0 && (item == null ? undefined : item.tagName) === myParams[1] ? styles.marginForArea : null;
      return [firstStyle, secondStyle];
    };
    var getValueCheckBox = function getValueCheckBox(item, checkifExists) {
      return item.childTags.length === (checkifExists == null ? undefined : checkifExists.child.length);
    };
    var renderCheckBoxAll = function renderCheckBoxAll(item, index, checkifExists) {
      if ((item == null ? undefined : item.tagName) === myParams[0] && index === 0) {
        return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.allCheckContainer,
            children: (0, _jsxRuntime.jsx)(_checkbox.Checkbox, {
              onToggle: function onToggle() {
                return handleAllToggle(item.tagName, item.childTags);
              },
              value: getValueCheckBox(item, checkifExists),
              testID: `${COMPONENT_NAME}__CheckBoxAllToggle__${index}`,
              accessibilityLabel: `${COMPONENT_NAME}__CheckBoxAllToggle__${index}`,
              outlineStyle: {
                borderColor: _theme.color.palette.darkGrey
              },
              text: (0, _i18n.translate)("dineShopFilter.all"),
              textStyle: Object.assign({}, _text2.presets.bodyTextRegular, styles.checkboxTitle)
            })
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.lineGrey
          })]
        });
      }
      return null;
    };
    var renderChip = function renderChip(item, ind, checkifExists, childTag) {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.chipsContainer,
        children: (0, _jsxRuntime.jsx)(_chip.Chip, {
          onPressed: function onPressed() {
            return handleState(item.tagName, childTag);
          },
          text: childTag.tagTitle,
          type: (checkifExists == null ? undefined : checkifExists.child.indexOf(childTag.tagName)) >= 0 ? _chip2.ChipType.selected : _chip2.ChipType.unSelected,
          testID: `${COMPONENT_NAME}__Chip__${ind}`,
          accessibilityLabel: `${COMPONENT_NAME}__Chip__${ind}`
        })
      }, ind);
    };
    var renderCheckBoxList = function renderCheckBoxList(item, ind, checkifExists, childTag) {
      var _item$tagName;
      var isColumnCheckBox = item == null || (_item$tagName = item.tagName) == null ? undefined : _item$tagName.includes("area");
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: isColumnCheckBox ? styles.checkboxColumnContainer : styles.checkboxContainer,
        children: [renderCheckBoxAll(item, ind, checkifExists), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: getContainerCheckBoxStyle(item, ind),
          children: (0, _jsxRuntime.jsx)(_checkbox.Checkbox, {
            value: (checkifExists == null ? undefined : checkifExists.child.indexOf(childTag.tagName)) >= 0 ? item : null,
            onToggle: function onToggle() {
              return handleState(item.tagName, childTag);
            },
            testID: `${COMPONENT_NAME}__CheckboxShopFilter__${ind}`,
            accessibilityLabel: `${COMPONENT_NAME}__CheckboxShopFilter__${ind}`,
            outlineStyle: {
              borderColor: _theme.color.palette.darkGrey
            },
            text: childTag.tagTitle,
            textStyle: Object.assign({}, _text2.presets.bodyTextRegular, styles.checkboxTitle)
          })
        }, ind), (item == null ? undefined : item.tagName) !== myParams[1] && (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.lineGrey
        })]
      }, ind);
    };
    var renderShopFilter = function renderShopFilter(item) {
      var _item$childTags;
      if (!item.childTags) {
        return null;
      }
      return (_item$childTags = item.childTags) == null ? undefined : _item$childTags.map(function (childTag, ind) {
        var stringMatch = myParams.includes(item.tagName);
        var checkifExists = filterState.find(function (i) {
          return (i == null ? undefined : i.main) === item.tagName;
        });
        return stringMatch ? renderCheckBoxList(item, ind, checkifExists, childTag) : renderChip(item, ind, checkifExists, childTag);
      });
    };
    var getMarginSection = function getMarginSection(index) {
      if (index !== 0) {
        return {
          marginTop: 50
        };
      }
    };
    var renderContent = function renderContent() {
      return (0, _jsxRuntime.jsxs)(_reactNative2.ScrollView, {
        scrollEnabled: true,
        contentContainerStyle: styles.contentContainer,
        style: styles.scrollContainer,
        testID: `${COMPONENT_NAME}__ScrollViewShopFilter`,
        accessibilityLabel: `${COMPONENT_NAME}__ScrollViewShopFilter`,
        children: [isNotFilterable && (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "caption1Regular",
          style: styles.unableToDisplayStyle,
          tx: "shopScreen.unableToDisplayFilters"
        }), !(0, _lodash.isEmpty)(shopFilterParameters) && !isNotFilterable && (shopFilterParameters == null ? undefined : shopFilterParameters.map(function (item, index) {
          return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: [styles.viewContent, getMarginSection(index)],
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              preset: "subTitleBold",
              text: item.tagTitle,
              style: styles.titleStyle
            }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: getContainerDineFilterStyle(item),
              children: renderShopFilter(item)
            })]
          }, index);
        }))]
      });
    };
    var onClosedFilterModal = function onClosedFilterModal() {
      (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
      setShowFilterModal(false);
    };
    var setPreviousData = function setPreviousData() {
      var previousValue = _dineShopFilter2.FilterValueShop.getFilterValueShop();
      try {
        var filterItemValues = JSON.parse(previousValue == null ? undefined : previousValue.filterValueItems);
        var filterItemTitles = JSON.parse(previousValue == null ? undefined : previousValue.filterValueTitles);
        dispatch(_shopRedux.default.setCurrentValueFilter({
          filterValueItems: !(0, _lodash.isEmpty)(filterItemValues) ? filterItemValues : [],
          filterValueTitles: !(0, _lodash.isEmpty)(filterItemTitles) ? filterItemTitles : []
        }));
      } catch (err) {
        dispatch(_shopRedux.default.setCurrentValueFilter({
          filterValueItems: [],
          filterValueTitles: []
        }));
      }
    };
    var onApplyFilter = function onApplyFilter() {
      (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppShopFilterOptions, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppShopFilterOptions, (0, _utils.handleCondition)(filterTitles && (filterTitles == null ? undefined : filterTitles.length) > 0, filterTitles.map(function (item) {
        return item == null ? undefined : item.tagTitle;
      }).join("|"), "1")));
      setShowFilterModal(false);
      dispatch(_shopRedux.default.shopSetFilterTitles(filterTitles));
      dispatch(_shopRedux.default.startRequestFilter(true));
      //@ts-ignore
      _reactNative2.InteractionManager.runAfterInteractions(function () {
        navigation.navigate(_constants.NavigationConstants.shopFilterResultsScreen);
      });
    };
    return (0, _jsxRuntime.jsx)(_bottomSheet.default, {
      isModalVisible: showFilterModal,
      containerStyle: styles.bottomSheetContainer,
      onClosedSheet: onClosedFilterModal,
      stopDragCollapse: true,
      onBackPressHandle: onClosedFilterModal,
      animationInTiming: 100,
      animationOutTiming: 100,
      onModalHide: setPreviousData,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.bottomShetFilterContainer,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.headerFilter,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            text: (0, _i18n.translate)("dineShopFilter.titleHeader"),
            style: styles.filterTitle
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: onClosedFilterModal,
            style: styles.btnCloseStyles,
            testID: `${COMPONENT_NAME}__CloseIcon`,
            accessibilityLabel: `${COMPONENT_NAME}__CloseIcon`,
            children: (0, _jsxRuntime.jsx)(_icons.CloseCross, {
              width: 24,
              height: 24,
              fill: "currentColor",
              style: (filterableStyles == null ? undefined : filterableStyles.closeIcon) || closeIconStyles
            })
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.container,
          children: (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
            children: (0, _utils.handleCondition)(hasError, (0, _jsxRuntime.jsx)(_error.ErrorScreen, {
              onReload: function onReload() {
                return null;
              },
              testID: `${COMPONENT_NAME}__ErrorScreenShopFilter`,
              accessibilityLabel: `${COMPONENT_NAME}__ErrorScreenShopFilter`
            }), (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
              children: [renderContent(), (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: styles.buttonContainer,
                children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                  style: styles.flexRow,
                  children: [(0, _utils.handleCondition)(filterState.length > 0 && !isNotFilterable, (0, _jsxRuntime.jsx)(_button.Button, {
                    onPress: function onPress() {
                      return handleClearAllFilters();
                    },
                    style: styles.clearAllButton,
                    sizePreset: "large",
                    statePreset: "default",
                    typePreset: "secondary",
                    text: (0, _i18n.translate)("dineShopFilter.clearAll"),
                    textStyle: styles.purpleTextColor,
                    textPreset: "buttonLarge",
                    testID: `${COMPONENT_NAME}__ClearAllFilters`,
                    accessibilityLabel: `${COMPONENT_NAME}__ClearAllFilters`
                  }), null), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
                    style: styles.buttonGradient,
                    start: {
                      x: 1,
                      y: 0
                    },
                    end: {
                      x: 0,
                      y: 1
                    },
                    colors: [_theme.color.palette.gradientColor1End, _theme.color.palette.gradientColor1Start],
                    children: (0, _jsxRuntime.jsx)(_button.Button, {
                      sizePreset: "large",
                      statePreset: "default",
                      typePreset: "primary",
                      disabled: isNotFilterable,
                      text: (0, _i18n.translate)("dineShopFilter.applyFilters"),
                      textPreset: "buttonLarge",
                      onPress: onApplyFilter,
                      style: filterableStyles == null ? undefined : filterableStyles.wrapper,
                      textStyle: filterableStyles == null ? undefined : filterableStyles.label,
                      testID: `${COMPONENT_NAME}__ApplyFilters`,
                      accessibilityLabel: `${COMPONENT_NAME}__ApplyFilters`
                    })
                  })]
                })
              })]
            }))
          })
        })]
      })
    });
  };
