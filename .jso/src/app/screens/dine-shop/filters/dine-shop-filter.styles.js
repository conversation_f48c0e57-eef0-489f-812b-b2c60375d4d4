  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.viewContent = exports.unableToDisplayStyle = exports.titleStyle = exports.textContainer = exports.secondStyle = exports.scrollContainer = exports.purpleTextColor = exports.marginForArea = exports.locationCheckboxContainer = exports.lineGrey = exports.headerFilter = exports.flexRow = exports.flexColumn = exports.firstStyle = exports.filterTitle = exports.elementMarginTop = exports.dividerStyle = exports.contentContainer = exports.container = exports.clearAllButton = exports.chipsContainer = exports.checkboxTitle = exports.checkboxContainer = exports.checkboxColumnContainer = exports.buttonGradient = exports.buttonContainer = exports.btnCloseStyles = exports.bottomShetFilterContainer = exports.bottomSheetContainer = exports.areaCheckboxContainer = exports.allCheckContainer = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var _responsive = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var container = exports.container = {
    backgroundColor: _theme.color.transparent,
    flex: 1
  };
  var contentContainer = exports.contentContainer = {
    paddingBottom: 32
  };
  var scrollContainer = exports.scrollContainer = {};
  var dividerStyle = exports.dividerStyle = {
    borderWidth: 1,
    height: 8,
    borderColor: _theme.color.palette.lighterGrey,
    width: "100%",
    marginVertical: 24,
    backgroundColor: _theme.color.palette.lighterGrey
  };
  var firstStyle = exports.firstStyle = {
    flexDirection: "row"
  };
  var viewContent = exports.viewContent = {
    paddingHorizontal: 24
  };
  var secondStyle = exports.secondStyle = {
    marginTop: 12,
    flexDirection: "row",
    width: "100%",
    flexWrap: "wrap"
  };
  var titleStyle = exports.titleStyle = {
    color: _theme.color.palette.almostBlackGrey,
    fontSize: _responsive.default.getFontSize(20)
  };
  var flexColumn = exports.flexColumn = {
    flexDirection: "column"
  };
  var checkboxColumnContainer = exports.checkboxColumnContainer = {
    marginRight: 6,
    width: "30%"
  };
  var checkboxContainer = exports.checkboxContainer = {
    marginRight: 6
  };
  var allCheckContainer = exports.allCheckContainer = {
    flexDirection: "row",
    marginTop: 16,
    alignItems: "center"
  };
  var checkboxTitle = exports.checkboxTitle = {
    color: _theme.color.palette.almostBlackGrey
  };
  var textContainer = exports.textContainer = {
    marginLeft: 16
  };
  var locationCheckboxContainer = exports.locationCheckboxContainer = {
    flexDirection: "row",
    marginTop: 16,
    alignItems: "center",
    alignSelf: "flex-start"
  };
  var areaCheckboxContainer = exports.areaCheckboxContainer = {
    flexDirection: "row",
    alignItems: "center",
    alignSelf: "flex-start"
  };
  var marginForArea = exports.marginForArea = {
    marginLeft: 20
  };
  var chipsContainer = exports.chipsContainer = {
    marginRight: 8,
    marginBottom: 8
  };
  var buttonContainer = exports.buttonContainer = Object.assign({
    paddingHorizontal: 24,
    paddingVertical: 16
  }, _theme.shadow.primaryShadow, {
    backgroundColor: _theme.color.palette.whiteGrey
  });
  var flexRow = exports.flexRow = {
    flexDirection: "row"
  };
  var clearAllButton = exports.clearAllButton = {
    flex: 1,
    marginRight: 15
  };
  var purpleTextColor = exports.purpleTextColor = {
    color: _theme.color.palette.lightPurple
  };
  var buttonGradient = exports.buttonGradient = {
    flex: 1,
    backgroundColor: _theme.color.palette.basePurple,
    borderRadius: 60,
    marginBottom: 7
  };
  var lineGrey = exports.lineGrey = {
    borderBottomColor: _theme.color.palette.lighterGrey,
    borderBottomWidth: 1,
    marginTop: 16
  };
  var elementMarginTop = exports.elementMarginTop = {
    marginTop: 24
  };
  var bottomShetFilterContainer = exports.bottomShetFilterContainer = {
    backgroundColor: _theme.color.palette.lightestGrey,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    height: "100%",
    width: "100%"
  };
  var headerFilter = exports.headerFilter = {
    flexDirection: "row",
    justifyContent: "center",
    marginHorizontal: 24,
    marginVertical: 21
  };
  var filterTitle = exports.filterTitle = Object.assign({}, _text.presets.subTitleBold, {
    color: _theme.color.palette.almostBlackGrey
  });
  var btnCloseStyles = exports.btnCloseStyles = {
    position: "absolute",
    right: 0
  };
  var bottomSheetContainer = exports.bottomSheetContainer = {
    height: "90%",
    margin: 0,
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0
  };
  var unableToDisplayStyle = exports.unableToDisplayStyle = {
    marginTop: 24,
    marginHorizontal: 24,
    color: _theme.color.palette.almostBlackGrey
  };
