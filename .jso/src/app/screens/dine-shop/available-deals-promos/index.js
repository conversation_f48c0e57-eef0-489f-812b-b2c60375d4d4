  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _dealsPromosLabel = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _styles = _$$_REQUIRE(_dependencyMap[7]);
  var _staffPerkPromotionDetailController = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _dateTime = _$$_REQUIRE(_dependencyMap[9]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  var AvailableDealsPromos = function AvailableDealsPromos(_ref) {
    var dealsPromos = _ref.dealsPromos,
      navigation = _ref.navigation,
      customContainerStyle = _ref.customContainerStyle,
      _ref$storeName = _ref.storeName,
      storeName = _ref$storeName === undefined ? "" : _ref$storeName;
    var separatorItem = function separatorItem() {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _styles.styles.separatorItemStyle
      });
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: Object.assign({}, _styles.styles.container, customContainerStyle),
      children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _styles.styles.wrapLabel,
        children: (0, _jsxRuntime.jsx)(_dealsPromosLabel.default, {})
      }), (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
        data: dealsPromos,
        renderItem: function renderItem(_ref2) {
          var item = _ref2.item;
          return (0, _jsxRuntime.jsx)(AvailableDealsPromosItem, {
            item: item,
            navigation: navigation,
            storeName: storeName
          });
        },
        keyExtractor: function keyExtractor(_, index) {
          return index.toString();
        },
        ItemSeparatorComponent: separatorItem
      })]
    });
  };
  var AvailableDealsPromosItem = function AvailableDealsPromosItem(_ref3) {
    var item = _ref3.item,
      navigation = _ref3.navigation,
      storeName = _ref3.storeName;
    var _ref4 = item || {},
      title = _ref4.title,
      campaignStartDate = _ref4.campaignStartDate,
      campaignEndDate = _ref4.campaignEndDate;
    var handlePress = function handlePress() {
      _staffPerkPromotionDetailController.default.showModal(navigation, {
        item: item
      }, true);
    };
    return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
      activeOpacity: 0.5,
      onPress: handlePress,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _styles.styles.contentItemStyle,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _styles.styles.leftContentItemStyle,
          children: (0, _jsxRuntime.jsx)(_icons.StaffDiscountTag, {})
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _styles.styles.midContentItemStyle,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            text: title,
            numberOfLines: 2,
            style: _styles.styles.titleStyle
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            text: (0, _dateTime.formatCampaignDateRange)(campaignStartDate, campaignEndDate),
            style: _styles.styles.dateStyle
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _styles.styles.rightContentItemStyle,
          children: (0, _jsxRuntime.jsx)(_icons.CaretRight, {})
        })]
      })
    });
  };
  var _default = exports.default = AvailableDealsPromos;
