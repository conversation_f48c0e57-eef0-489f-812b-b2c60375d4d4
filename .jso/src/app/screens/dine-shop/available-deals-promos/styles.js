  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    contentItemStyle: {
      flexDirection: "row",
      justifyContent: "space-between",
      paddingBottom: 16,
      paddingTop: 12
    },
    leftContentItemStyle: {
      marginLeft: 12,
      marginRight: 16,
      marginTop: 8
    },
    midContentItemStyle: {
      flex: 1
    },
    rightContentItemStyle: {
      marginLeft: 16,
      marginRight: 12,
      marginTop: 16
    },
    separatorItemStyle: {
      height: 1,
      backgroundColor: _theme.color.palette.lighterGrey
    },
    container: {
      borderColor: _theme.color.palette.lighterGrey,
      borderRadius: 12,
      borderWidth: 1,
      marginHorizontal: 24,
      marginTop: 16
    },
    dateStyle: Object.assign({}, _text.presets.caption2Regular, {
      color: _theme.color.palette.almostBlackGrey
    }),
    titleStyle: Object.assign({}, _text.presets.caption1Bold, {
      color: _theme.color.palette.almostBlackGrey,
      marginBottom: 4,
      textAlign: "left"
    }),
    wrapLabel: {
      marginLeft: -1,
      marginTop: -1
    }
  });
