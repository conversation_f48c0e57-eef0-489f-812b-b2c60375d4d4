  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var React = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[0]));
  var _reactNativeSvg = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[2]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var StaffPerkLabel = function StaffPerkLabel(props) {
    return (0, _jsxRuntime.jsxs)(_reactNativeSvg.default, Object.assign({
      width: 103,
      height: 28,
      viewBox: "0 0 103 28",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }, props, {
      children: [(0, _jsxRuntime.jsx)(_reactNativeSvg.Path, {
        d: "M0 19.2C0 12.4794 0 9.11905 1.30792 6.55211C2.4584 4.29417 4.29417 2.4584 6.55211 1.30792C9.11905 0 12.4794 0 19.2 0H89C96.732 0 103 6.26801 103 14C103 21.732 96.732 28 89 28H0V19.2Z",
        fill: "url(#paint0_linear_6335_38863)"
      }), (0, _jsxRuntime.jsx)(_reactNativeSvg.Path, {
        d: "M17.6006 11.902C17.5566 11.99 17.5046 12.052 17.4446 12.088C17.3886 12.124 17.3206 12.142 17.2406 12.142C17.1606 12.142 17.0706 12.112 16.9706 12.052C16.8706 11.988 16.7526 11.918 16.6166 11.842C16.4806 11.766 16.3206 11.698 16.1366 11.638C15.9566 11.574 15.7426 11.542 15.4946 11.542C15.2706 11.542 15.0746 11.57 14.9066 11.626C14.7426 11.678 14.6026 11.752 14.4866 11.848C14.3746 11.944 14.2906 12.06 14.2346 12.196C14.1786 12.328 14.1506 12.474 14.1506 12.634C14.1506 12.838 14.2066 13.008 14.3186 13.144C14.4346 13.28 14.5866 13.396 14.7746 13.492C14.9626 13.588 15.1766 13.674 15.4166 13.75C15.6566 13.826 15.9006 13.908 16.1486 13.996C16.4006 14.08 16.6466 14.18 16.8866 14.296C17.1266 14.408 17.3406 14.552 17.5286 14.728C17.7166 14.9 17.8666 15.112 17.9786 15.364C18.0946 15.616 18.1526 15.922 18.1526 16.282C18.1526 16.674 18.0846 17.042 17.9486 17.386C17.8166 17.726 17.6206 18.024 17.3606 18.28C17.1046 18.532 16.7906 18.732 16.4186 18.88C16.0466 19.024 15.6206 19.096 15.1406 19.096C14.8646 19.096 14.5926 19.068 14.3246 19.012C14.0566 18.96 13.7986 18.884 13.5506 18.784C13.3066 18.684 13.0766 18.564 12.8606 18.424C12.6486 18.284 12.4586 18.128 12.2906 17.956L12.7586 17.182C12.8026 17.126 12.8546 17.08 12.9146 17.044C12.9786 17.004 13.0486 16.984 13.1246 16.984C13.2246 16.984 13.3326 17.026 13.4486 17.11C13.5646 17.19 13.7006 17.28 13.8566 17.38C14.0166 17.48 14.2026 17.572 14.4146 17.656C14.6306 17.736 14.8886 17.776 15.1886 17.776C15.6486 17.776 16.0046 17.668 16.2566 17.452C16.5086 17.232 16.6346 16.918 16.6346 16.51C16.6346 16.282 16.5766 16.096 16.4606 15.952C16.3486 15.808 16.1986 15.688 16.0106 15.592C15.8226 15.492 15.6086 15.408 15.3686 15.34C15.1286 15.272 14.8846 15.198 14.6366 15.118C14.3886 15.038 14.1446 14.942 13.9046 14.83C13.6646 14.718 13.4506 14.572 13.2626 14.392C13.0746 14.212 12.9226 13.988 12.8066 13.72C12.6946 13.448 12.6386 13.114 12.6386 12.718C12.6386 12.402 12.7006 12.094 12.8246 11.794C12.9526 11.494 13.1366 11.228 13.3766 10.996C13.6206 10.764 13.9186 10.578 14.2706 10.438C14.6226 10.298 15.0246 10.228 15.4766 10.228C15.9886 10.228 16.4606 10.308 16.8926 10.468C17.3246 10.628 17.6926 10.852 17.9966 11.14L17.6006 11.902ZM25.9782 10.324V11.65H23.3682V19H21.7542V11.65H19.1322V10.324H25.9782ZM34.228 19H32.98C32.84 19 32.724 18.966 32.632 18.898C32.544 18.826 32.48 18.738 32.44 18.634L31.792 16.864H28.198L27.55 18.634C27.518 18.726 27.454 18.81 27.358 18.886C27.266 18.962 27.152 19 27.016 19H25.762L29.17 10.324H30.82L34.228 19ZM28.612 15.724H31.378L30.322 12.838C30.274 12.71 30.22 12.56 30.16 12.388C30.104 12.212 30.048 12.022 29.992 11.818C29.936 12.022 29.88 12.212 29.824 12.388C29.772 12.564 29.72 12.718 29.668 12.85L28.612 15.724ZM41.0735 10.324V11.608H37.2275V14.176H40.4735V15.466H37.2275V19H35.6015V10.324H41.0735ZM48.2918 10.324V11.608H44.4458V14.176H47.6918V15.466H44.4458V19H42.8198V10.324H48.2918ZM54.5064 15.946V19H52.8924V10.324H55.7184C56.2984 10.324 56.8004 10.392 57.2244 10.528C57.6484 10.664 57.9984 10.854 58.2744 11.098C58.5504 11.342 58.7544 11.634 58.8864 11.974C59.0184 12.314 59.0844 12.686 59.0844 13.09C59.0844 13.51 59.0144 13.896 58.8744 14.248C58.7344 14.596 58.5244 14.896 58.2444 15.148C57.9644 15.4 57.6144 15.596 57.1944 15.736C56.7744 15.876 56.2824 15.946 55.7184 15.946H54.5064ZM54.5064 14.686H55.7184C56.0144 14.686 56.2724 14.65 56.4924 14.578C56.7124 14.502 56.8944 14.396 57.0384 14.26C57.1824 14.12 57.2904 13.952 57.3624 13.756C57.4344 13.556 57.4704 13.334 57.4704 13.09C57.4704 12.858 57.4344 12.648 57.3624 12.46C57.2904 12.272 57.1824 12.112 57.0384 11.98C56.8944 11.848 56.7124 11.748 56.4924 11.68C56.2724 11.608 56.0144 11.572 55.7184 11.572H54.5064V14.686ZM66.2904 10.324V11.608H62.4444V14.014H65.4744V15.256H62.4444V17.71H66.2904V19H60.8184V10.324H66.2904ZM69.8336 15.61V19H68.2196V10.324H70.8656C71.4576 10.324 71.9636 10.386 72.3836 10.51C72.8076 10.63 73.1536 10.8 73.4216 11.02C73.6936 11.24 73.8916 11.504 74.0156 11.812C74.1436 12.116 74.2076 12.452 74.2076 12.82C74.2076 13.112 74.1636 13.388 74.0756 13.648C73.9916 13.908 73.8676 14.144 73.7036 14.356C73.5436 14.568 73.3436 14.754 73.1036 14.914C72.8676 15.074 72.5976 15.202 72.2936 15.298C72.4976 15.414 72.6736 15.58 72.8216 15.796L74.9936 19H73.5416C73.4016 19 73.2816 18.972 73.1816 18.916C73.0856 18.86 73.0036 18.78 72.9356 18.676L71.1116 15.898C71.0436 15.794 70.9676 15.72 70.8836 15.676C70.8036 15.632 70.6836 15.61 70.5236 15.61H69.8336ZM69.8336 14.452H70.8416C71.1456 14.452 71.4096 14.414 71.6336 14.338C71.8616 14.262 72.0476 14.158 72.1916 14.026C72.3396 13.89 72.4496 13.73 72.5216 13.546C72.5936 13.362 72.6296 13.16 72.6296 12.94C72.6296 12.5 72.4836 12.162 72.1916 11.926C71.9036 11.69 71.4616 11.572 70.8656 11.572H69.8336V14.452ZM78.1769 13.96H78.5549C78.7069 13.96 78.8329 13.94 78.9329 13.9C79.0329 13.856 79.1189 13.788 79.1909 13.696L81.5849 10.666C81.6849 10.538 81.7889 10.45 81.8969 10.402C82.0089 10.35 82.1489 10.324 82.3169 10.324H83.7089L80.7869 13.93C80.6949 14.042 80.6069 14.136 80.5229 14.212C80.4389 14.288 80.3489 14.35 80.2529 14.398C80.3849 14.446 80.5029 14.514 80.6069 14.602C80.7149 14.69 80.8189 14.802 80.9189 14.938L83.9309 19H82.5029C82.3109 19 82.1669 18.974 82.0709 18.922C81.9789 18.866 81.9009 18.786 81.8369 18.682L79.3829 15.478C79.3029 15.374 79.2129 15.3 79.1129 15.256C79.0129 15.208 78.8709 15.184 78.6869 15.184H78.1769V19H76.5629V10.318H78.1769V13.96ZM90.0178 11.902C89.9738 11.99 89.9218 12.052 89.8618 12.088C89.8058 12.124 89.7378 12.142 89.6578 12.142C89.5778 12.142 89.4878 12.112 89.3878 12.052C89.2878 11.988 89.1698 11.918 89.0338 11.842C88.8978 11.766 88.7378 11.698 88.5538 11.638C88.3738 11.574 88.1598 11.542 87.9118 11.542C87.6878 11.542 87.4918 11.57 87.3238 11.626C87.1598 11.678 87.0198 11.752 86.9038 11.848C86.7918 11.944 86.7078 12.06 86.6518 12.196C86.5958 12.328 86.5678 12.474 86.5678 12.634C86.5678 12.838 86.6238 13.008 86.7358 13.144C86.8518 13.28 87.0038 13.396 87.1918 13.492C87.3798 13.588 87.5938 13.674 87.8338 13.75C88.0738 13.826 88.3178 13.908 88.5658 13.996C88.8178 14.08 89.0638 14.18 89.3038 14.296C89.5438 14.408 89.7578 14.552 89.9458 14.728C90.1338 14.9 90.2838 15.112 90.3958 15.364C90.5118 15.616 90.5698 15.922 90.5698 16.282C90.5698 16.674 90.5018 17.042 90.3658 17.386C90.2338 17.726 90.0378 18.024 89.7778 18.28C89.5218 18.532 89.2078 18.732 88.8358 18.88C88.4638 19.024 88.0378 19.096 87.5578 19.096C87.2818 19.096 87.0098 19.068 86.7418 19.012C86.4738 18.96 86.2158 18.884 85.9678 18.784C85.7238 18.684 85.4938 18.564 85.2778 18.424C85.0658 18.284 84.8758 18.128 84.7078 17.956L85.1758 17.182C85.2198 17.126 85.2718 17.08 85.3318 17.044C85.3958 17.004 85.4658 16.984 85.5418 16.984C85.6418 16.984 85.7498 17.026 85.8658 17.11C85.9818 17.19 86.1178 17.28 86.2738 17.38C86.4338 17.48 86.6198 17.572 86.8318 17.656C87.0478 17.736 87.3058 17.776 87.6058 17.776C88.0658 17.776 88.4218 17.668 88.6738 17.452C88.9258 17.232 89.0518 16.918 89.0518 16.51C89.0518 16.282 88.9938 16.096 88.8778 15.952C88.7658 15.808 88.6158 15.688 88.4278 15.592C88.2398 15.492 88.0258 15.408 87.7858 15.34C87.5458 15.272 87.3018 15.198 87.0538 15.118C86.8058 15.038 86.5618 14.942 86.3218 14.83C86.0818 14.718 85.8678 14.572 85.6798 14.392C85.4918 14.212 85.3398 13.988 85.2238 13.72C85.1118 13.448 85.0558 13.114 85.0558 12.718C85.0558 12.402 85.1178 12.094 85.2418 11.794C85.3698 11.494 85.5538 11.228 85.7938 10.996C86.0378 10.764 86.3358 10.578 86.6878 10.438C87.0398 10.298 87.4418 10.228 87.8938 10.228C88.4058 10.228 88.8778 10.308 89.3098 10.468C89.7418 10.628 90.1098 10.852 90.4138 11.14L90.0178 11.902Z",
        fill: "white"
      }), (0, _jsxRuntime.jsx)(_reactNativeSvg.Defs, {
        children: (0, _jsxRuntime.jsxs)(_reactNativeSvg.LinearGradient, {
          id: "paint0_linear_6335_38863",
          x1: 123.831,
          y1: -22.9444,
          x2: 91.1712,
          y2: 62.7483,
          gradientUnits: "userSpaceOnUse",
          children: [(0, _jsxRuntime.jsx)(_reactNativeSvg.Stop, {
            stopColor: "#7E1BCC"
          }), (0, _jsxRuntime.jsx)(_reactNativeSvg.Stop, {
            offset: 1,
            stopColor: "#E74391"
          })]
        })
      })]
    }));
  };
  var _default = exports.default = StaffPerkLabel;
