  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ErrorScreen = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _icons = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _button = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  var containerStyle = {
    alignItems: "center",
    justifyContent: "center",
    height: "100%",
    backgroundColor: _theme.color.palette.lightestGrey
  };
  var titleStyle = {
    marginTop: 40
  };
  var textStyle = {
    marginTop: 12,
    paddingHorizontal: 62,
    textAlign: "center",
    color: _theme.color.palette.darkestGrey
  };
  var buttonStyle = {
    borderRadius: 60,
    paddingHorizontal: 24,
    marginTop: 34
  };
  var ErrorScreen = exports.ErrorScreen = function ErrorScreen(_ref) {
    var onReload = _ref.onReload,
      _ref$testID = _ref.testID,
      testID = _ref$testID === undefined ? "ErrorScreen" : _ref$testID,
      _ref$accessibilityLab = _ref.accessibilityLabel,
      accessibilityLabel = _ref$accessibilityLab === undefined ? "ErrorScreen" : _ref$accessibilityLab;
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: containerStyle,
      children: [(0, _jsxRuntime.jsx)(_reactNative.StatusBar, {
        barStyle: "light-content"
      }), (0, _jsxRuntime.jsx)(_icons.ErrorCloud, {
        width: "191",
        height: "104"
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        preset: "h2",
        tx: "screenError.oops",
        style: titleStyle
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        preset: "caption1Regular",
        tx: "screenError.somethingWrong",
        style: textStyle
      }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
        style: buttonStyle,
        start: {
          x: 0,
          y: 1
        },
        end: {
          x: 1,
          y: 0
        },
        colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
        children: (0, _jsxRuntime.jsx)(_button.Button, {
          sizePreset: "large",
          textPreset: "buttonLarge",
          typePreset: "secondary",
          statePreset: "default",
          backgroundPreset: "light",
          tx: "screenError.reload",
          onPress: onReload,
          testID: `${testID}__TouchableClose`,
          accessibilityLabel: `${accessibilityLabel}__TouchableCrossClose`
        })
      })]
    });
  };
