  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = ExpandableComponent;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _icons = _$$_REQUIRE(_dependencyMap[8]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _i18n = _$$_REQUIRE(_dependencyMap[10]);
  var _htmlRichtext = _$$_REQUIRE(_dependencyMap[11]);
  var _constants = _$$_REQUIRE(_dependencyMap[12]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[13]));
  var _utils = _$$_REQUIRE(_dependencyMap[14]);
  var _bottomSheetUnableLoadLocation = _$$_REQUIRE(_dependencyMap[15]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[16]);
  var _bottomSheetMapUnavailable = _$$_REQUIRE(_dependencyMap[17]);
  var _adobe = _$$_REQUIRE(_dependencyMap[18]);
  var _lodash = _$$_REQUIRE(_dependencyMap[19]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[20]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var getDay = function getDay(day) {
    return day || (0, _i18n.translate)("dineShopOfferDetails.daily");
  };
  var getTextNumber = function getTextNumber(item, number) {
    return (item ? ", " : "") + number;
  };
  var getWebsiteText = function getWebsiteText(item, dotUnicode) {
    if (item != null && item.viewMenuLink) {
      return (0, _i18n.translate)("dineShopOfferDetails.viewWebsite") + "   " + `${dotUnicode}` + "   ";
    }
    return (0, _i18n.translate)("dineShopOfferDetails.viewWebsite");
  };
  var getTitle = function getTitle(index, item, dotUnicode) {
    if (index === 0) {
      var _item$openCloseStatus;
      return item.locationMainTitle && (_item$openCloseStatus = item.openCloseStatus) != null && _item$openCloseStatus.status ? item.locationMainTitle + `${"  "}` + dotUnicode + `${"  "}` : item.locationMainTitle;
    }
    return item == null ? undefined : item.title;
  };
  function ExpandableComponent(_ref) {
    var item = _ref.item,
      onClickFunction = _ref.onClickFunction,
      navigation = _ref.navigation,
      index = _ref.index,
      _ref$testID = _ref.testID,
      testID = _ref$testID === undefined ? "ExpandableComponent" : _ref$testID,
      _ref$accessibilityLab = _ref.accessibilityLabel,
      accessibilityLabel = _ref$accessibilityLab === undefined ? "ExpandableComponent" : _ref$accessibilityLab,
      refRetryAction = _ref.refRetryAction,
      setConnection = _ref.setConnection;
    var _useState = (0, _react.useState)(0),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      layoutHeight = _useState2[0],
      setLayoutHeight = _useState2[1];
    var dotUnicode = (0, _constants.getDotUnicode)();
    var title = getTitle(index, item, dotUnicode);
    var unableToLoadLocationRef = (0, _react.useRef)(null);
    var mapUnavailable = (0, _react.useRef)(null);
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      mapRMFlag = _useState4[0],
      setMapRMFlag = _useState4[1];
    (0, _react.useEffect)(function () {
      if (item.isExpanded) {
        setLayoutHeight(null);
        return;
      }
      setLayoutHeight(0);
    }, [item.isExpanded]);
    var transformPhoneNumber = function transformPhoneNumber(phone) {
      if (_reactNative2.Platform.OS === "android") {
        return `tel:${phone}`;
      }
      return `telprompt:${phone}`;
    };
    var dialCall = function dialCall(num) {
      var phoneNumber = transformPhoneNumber(num);
      _reactNative2.Linking.openURL(phoneNumber);
    };
    (0, _react.useEffect)(function () {
      var fetchAtomRMConfig = function fetchAtomRMConfig() {
        var mapFlagEnable = (0, _remoteConfig.isFlagON)(_remoteConfig.REMOTE_CONFIG_FLAGS.ATOMS_MAP);
        setMapRMFlag(mapFlagEnable);
      };
      fetchAtomRMConfig();
    }, []);
    var _handleDirection = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        if (isConnected) {
          refRetryAction.current = null;
          setConnection(true);
          if (!item.localRef) {
            var _unableToLoadLocation;
            return unableToLoadLocationRef == null || (_unableToLoadLocation = unableToLoadLocationRef.current) == null ? undefined : _unableToLoadLocation.show();
          }
          if (!mapRMFlag) {
            return mapUnavailable == null ? undefined : mapUnavailable.current.show();
          }
          (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppATOMSEntryClick, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppATOMSEntryClick, `L3/CF Details Page|Get Directions|${item.localRef}`));
          navigation.navigate(_constants.NavigationConstants.changiMap, {
            localRef: item.localRef
          });
          return;
        }
        refRetryAction.current = _handleDirection;
        setConnection(false);
      });
      return function handleDirection() {
        return _ref2.apply(this, arguments);
      };
    }();
    var visibleGetdirections = {};
    var renderContent = function renderContent() {
      var _item$openCloseStatus2, _item$openCloseStatus3, _item$openingHours, _item$openingHours2, _item$contactNumber;
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: styles.header,
          onPress: onClickFunction,
          testID: `${testID}__TouchableExpand__${index}`,
          accessibilityLabel: `${accessibilityLabel}__TouchableExpand__${index}`,
          children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.flexStyle,
            children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: styles.headerStyle,
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                preset: "h4",
                style: styles.txtColor,
                text: title
              }), (0, _utils.handleCondition)(item.openCloseStatus, (0, _jsxRuntime.jsx)(_text.Text, {
                preset: "bodyTextBold",
                style: [{
                  color: item == null || (_item$openCloseStatus2 = item.openCloseStatus) == null ? undefined : _item$openCloseStatus2.colorCode
                }, styles.statusTextStyle],
                children: item == null || (_item$openCloseStatus3 = item.openCloseStatus) == null ? undefined : _item$openCloseStatus3.status
              }), null)]
            }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: styles.arrowIconStyle,
              children: (0, _utils.handleCondition)(item.isExpanded, (0, _jsxRuntime.jsx)(_icons.TopArrow, {}), (0, _jsxRuntime.jsx)(_icons.DownArrow, {}))
            })]
          })
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: [{
            height: layoutHeight
          }, styles.visibileStyle],
          children: index === 0 ? (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.content,
            children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: styles.flexStyle,
              children: [(0, _jsxRuntime.jsx)(_icons.DirectionsOutline, {
                style: styles.directionIconstyle
              }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: styles.areaInfoViewStyle,
                children: (0, _jsxRuntime.jsx)(_text.Text, {
                  numberOfLines: 2,
                  preset: "bodyTextRegular",
                  style: styles.text,
                  children: item.location
                })
              })]
            }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: styles.flexStyle,
              children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                testID: `${testID}__TouchableGetDirection__${index}`,
                accessibilityLabel: `${accessibilityLabel}__TouchableGetDirection__${index}`,
                onPress: _handleDirection,
                style: visibleGetdirections,
                disabled: (0, _lodash.isEmpty)(item == null ? undefined : item.localRef),
                children: (0, _jsxRuntime.jsx)(_text.Text, {
                  preset: "textLink",
                  text: (0, _i18n.translate)("dineShopOfferDetails.getDirection"),
                  style: (0, _utils.handleCondition)((0, _lodash.isEmpty)(item == null ? undefined : item.localRef), styles.directionTextDisabledStyle, styles.directionTextStyle)
                })
              })
            }), (0, _utils.handleCondition)(((_item$openingHours = item.openingHours) == null ? undefined : _item$openingHours.length) > 0, (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: styles.flexStyle,
              children: [(0, _jsxRuntime.jsx)(_icons.ClockOutline, {
                style: styles.clockIconStyle
              }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: styles.timingsInfoViewStyle,
                children: (_item$openingHours2 = item.openingHours) == null ? undefined : _item$openingHours2.map(function (res, keyVal) {
                  return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                    style: styles.timingStyle,
                    children: [(0, _jsxRuntime.jsx)(_text.Text, {
                      preset: "bodyTextRegular",
                      style: styles.timingsInfoDayText,
                      children: getDay(res.day)
                    }), (0, _jsxRuntime.jsx)(_text.Text, {
                      preset: "bodyTextRegular",
                      style: styles.timingsText,
                      children: res.timings
                    })]
                  }, keyVal);
                })
              })]
            }), null), (0, _utils.handleCondition)(((_item$contactNumber = item.contactNumber) == null ? undefined : _item$contactNumber.length) > 0, (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: styles.flexStyle,
              children: [(0, _jsxRuntime.jsx)(_icons.PhoneOutline, {
                style: styles.phoneIconStyle
              }), item.contactNumber.map(function (number, i) {
                return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                  onPress: function onPress() {
                    return dialCall(number);
                  },
                  testID: `${testID}__TouchableDialCall__${i}`,
                  accessibilityLabel: `${accessibilityLabel}__TouchableDialCall__${i}`,
                  children: (0, _jsxRuntime.jsx)(_text.Text, {
                    preset: "textLink",
                    style: styles.phoneNumberStyle,
                    children: getTextNumber(i, number)
                  }, i)
                }, i);
              })]
            }), null), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: styles.linkView,
              children: [(0, _utils.handleCondition)(item.websiteViewLink || item.viewMenuLink, (0, _jsxRuntime.jsx)(_icons.FoodMenuOutline, {
                style: styles.spoonIconStyle
              }), null), (0, _utils.handleCondition)(item.websiteViewLink, (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                onPress: function onPress() {
                  return navigation.navigate(_constants.NavigationConstants.webview, {
                    uri: item.websiteViewLink
                  });
                },
                testID: `${testID}__ViewWebsite`,
                accessibilityLabel: `${accessibilityLabel}__ViewWebsite`,
                children: (0, _jsxRuntime.jsx)(_text.Text, {
                  preset: "textLink",
                  text: getWebsiteText(item, dotUnicode),
                  style: styles.linkTestStyle
                })
              }), null), (0, _utils.handleCondition)(item.viewMenuLink, (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                onPress: function onPress() {
                  return navigation.navigate(_constants.NavigationConstants.webview, {
                    uri: item.viewMenuLink
                  });
                },
                testID: `${testID}__ViewMenu`,
                accessibilityLabel: `${accessibilityLabel}__ViewMenu`,
                children: (0, _jsxRuntime.jsx)(_text.Text, {
                  preset: "textLink",
                  text: (0, _i18n.translate)("dineShopOfferDetails.viewMenu"),
                  style: styles.linkTestStyle
                })
              }), null)]
            })]
          }, "content") : (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.richTextContainerStyle,
            children: (0, _jsxRuntime.jsx)(_htmlRichtext.HtmlRichtext, {
              value: item.description,
              style: styles.richTextStyle
            })
          })
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.separator
        })]
      });
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      children: [title ? renderContent() : null, (0, _jsxRuntime.jsx)(_bottomSheetUnableLoadLocation.BottomSheetUnableLoadLocation, {
        ref: unableToLoadLocationRef
      }), (0, _jsxRuntime.jsx)(_bottomSheetMapUnavailable.BottomSheetMapUnavailable, {
        ref: mapUnavailable
      })]
    });
  }
