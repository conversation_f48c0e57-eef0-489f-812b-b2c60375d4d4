  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.DineShopOfferDetailsScreen = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[7]));
  var _text = _$$_REQUIRE(_dependencyMap[8]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[9]);
  var _dineRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[10]));
  var _shopRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[11]));
  var _button = _$$_REQUIRE(_dependencyMap[12]);
  var _i18n = _$$_REQUIRE(_dependencyMap[13]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _theme = _$$_REQUIRE(_dependencyMap[15]);
  var _icons = _$$_REQUIRE(_dependencyMap[16]);
  var _hero = _$$_REQUIRE(_dependencyMap[17]);
  var _ExpandableComponent = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[18]));
  var _loadingModal = _$$_REQUIRE(_dependencyMap[19]);
  var _error = _$$_REQUIRE(_dependencyMap[20]);
  var _utils = _$$_REQUIRE(_dependencyMap[21]);
  var _adobe = _$$_REQUIRE(_dependencyMap[22]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[23]);
  var _constants = _$$_REQUIRE(_dependencyMap[24]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[25]);
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[26]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[27]));
  var _screenHook = _$$_REQUIRE(_dependencyMap[28]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[29]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var loadingType = "loading";
  var safeViewHeaderForAndroid = function safeViewHeaderForAndroid(inset) {
    var _safeViewHeaderForAndroid = {
      marginTop: _reactNative2.Platform.OS === "android" && inset != null && inset.top ? inset == null ? undefined : inset.top : 0,
      marginStart: 14
    };
    return _safeViewHeaderForAndroid;
  };
  var SCREEN_NAME = "DineShopOfferDetailsScreen";
  var DineShopOfferDetailsScreen = exports.DineShopOfferDetailsScreen = function DineShopOfferDetailsScreen(_ref) {
    var _offerDetailsPayload$, _offerDetailsPayload$2, _offerDetailsPayload$3, _offerDetailsPayload$4, _offerDetailsPayload$5, _offerDetailsPayload$7;
    var navigation = _ref.navigation,
      route = _ref.route;
    var offerId = route.params.offerId;
    var isDineScreen = route.params.isDineScreen;
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      loading = _useState2[0],
      _setLoading = _useState2[1];
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var offerDetailsPayload = (0, _reactRedux.useSelector)(function (data) {
      return isDineScreen ? _dineRedux.DineSelectors.dineOfferDetailsPayload(data) : _shopRedux.ShopSelectors.shopOfferDetailsPayload(data);
    });
    var getOfferDetails = _react.default.useCallback(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
        isConnected = _yield$NetInfo$fetch.isConnected;
      if (isConnected) {
        setConnection(true);
        isDineScreen ? dispatch(_dineRedux.default.dineOfferDetailsRequest(offerId)) : dispatch(_shopRedux.default.shopOfferDetailsRequest(offerId));
        return;
      }
      setConnection(false);
    }), []);
    var heroCarouselImages = offerDetailsPayload == null || (_offerDetailsPayload$ = offerDetailsPayload.data) == null ? undefined : _offerDetailsPayload$.heroCarouselImage;
    var heroCarouselLogo = offerDetailsPayload == null || (_offerDetailsPayload$2 = offerDetailsPayload.data) == null ? undefined : _offerDetailsPayload$2.heroCarouselLogo;
    var title = offerDetailsPayload == null || (_offerDetailsPayload$3 = offerDetailsPayload.data) == null ? undefined : _offerDetailsPayload$3.title;
    var tag = offerDetailsPayload == null || (_offerDetailsPayload$4 = offerDetailsPayload.data) == null ? undefined : _offerDetailsPayload$4.tags;
    var tagArray = tag ? tag.split(";") : [];
    var locationDetails = offerDetailsPayload == null || (_offerDetailsPayload$5 = offerDetailsPayload.data) == null ? undefined : _offerDetailsPayload$5.locationDetails;
    var _useState3 = (0, _react.useState)([]),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      listDataSource = _useState4[0],
      setListDataSource = _useState4[1];
    var hasError = offerDetailsPayload == null ? undefined : offerDetailsPayload.hasError;
    var inset = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    var _useState5 = (0, _react.useState)(true),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isConnection = _useState6[0],
      setConnection = _useState6[1];
    var refRetryAction = (0, _react.useRef)(null);
    _react.default.useEffect(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        (0, _screenHook.setCurrentScreenActive)(`Promotion_Detail_${title}`);
        (0, _adobe.commonTrackingScreen)(`Promotion_Detail_${title}`, (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribeFocus;
    }, [navigation, title]);
    _react.default.useEffect(function () {
      var unsubscribeBlur = navigation.addListener("blur", function () {
        (0, _screenHook.setPreviousScreen)(`Promotion_Detail_${title}`);
      });
      return unsubscribeBlur;
    }, [navigation, title]);
    _react.default.useEffect(function () {
      getOfferDetails();
    }, []);
    if (_reactNative2.Platform.OS === "android") {
      _reactNative2.UIManager.setLayoutAnimationEnabledExperimental(true);
    }
    _react.default.useEffect(function () {
      setListDataSource(locationDetails);
    });
    var handlePressButton = function handlePressButton() {
      var _offerDetailsPayload$6;
      navigation.navigate(_constants.NavigationConstants.playpassWebview, {
        uri: offerDetailsPayload == null || (_offerDetailsPayload$6 = offerDetailsPayload.data) == null ? undefined : _offerDetailsPayload$6.buttonLink,
        needBackButton: true,
        needCloseButton: true
      });
    };
    var updateLayout = function updateLayout(index) {
      _reactNative2.LayoutAnimation.configureNext(Object.assign({}, _reactNative2.LayoutAnimation.Presets.easeInEaseOut, {
        duration: 100
      }));
      var array = (0, _toConsumableArray2.default)(listDataSource);
      array.forEach(function (_value, placeindex) {
        if (placeindex === index) {
          array[placeindex].isExpanded = !array[placeindex].isExpanded;
        } else {
          array[placeindex].isExpanded = false;
        }
      });
      setListDataSource(array);
    };
    var onReload = function onReload() {
      getOfferDetails();
    };
    var onRetryError = function onRetryError() {
      if (refRetryAction.current) {
        refRetryAction.current == null || refRetryAction.current();
        return;
      }
      getOfferDetails();
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.mainContainerStyle,
      testID: "DineShopOfferDetailsScreen",
      children: [(0, _utils.handleCondition)((offerDetailsPayload == null ? undefined : offerDetailsPayload.type) === loadingType, (0, _jsxRuntime.jsx)(_loadingModal.LoadingModal, {
        visible: true
      }), (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
        children: (0, _utils.handleCondition)(hasError, (0, _jsxRuntime.jsx)(_error.ErrorScreen, {
          onReload: onReload,
          testID: `${SCREEN_NAME}__ErrorScreen`,
          accessibilityLabel: `${SCREEN_NAME}__ErrorScreen`
        }), (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [(0, _jsxRuntime.jsx)(_reactNative2.SafeAreaView, {
            style: styles.headerViewStyle,
            children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              onPress: function onPress() {
                navigation.goBack();
              },
              style: safeViewHeaderForAndroid(inset),
              testID: `${SCREEN_NAME}__TouchableBack`,
              accessibilityLabel: `${SCREEN_NAME}__TouchableBack`,
              children: (0, _jsxRuntime.jsx)(_icons.BackButton, {})
            })
          }), (0, _jsxRuntime.jsxs)(_reactNative2.ScrollView, {
            showsVerticalScrollIndicator: false,
            testID: `${SCREEN_NAME}__ScrollView`,
            accessibilityLabel: `${SCREEN_NAME}__ScrollView`,
            children: [(0, _jsxRuntime.jsx)(_hero.Hero, {
              type: _hero.HeroType.default,
              logoImageUrl: heroCarouselLogo,
              heroImagesUrl: heroCarouselImages,
              testID: `${SCREEN_NAME}__Hero`,
              accessibilityLabel: `${SCREEN_NAME}__Hero`,
              showWave: false
            }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: styles.pageHeaderStyle,
                children: [title && (0, _jsxRuntime.jsx)(_text.Text, {
                  preset: "h1",
                  style: styles.titleText,
                  children: title
                }), (0, _utils.handleCondition)(tagArray.length > 0, (0, _jsxRuntime.jsx)(_reactNative2.ScrollView, {
                  horizontal: true,
                  contentContainerStyle: styles.tagsScrollViewStyle,
                  showsHorizontalScrollIndicator: false,
                  testID: `${SCREEN_NAME}__ScrollViewTagArray`,
                  accessibilityLabel: `${SCREEN_NAME}__ScrollViewTagArray`,
                  children: tagArray.map(function (tags, ind) {
                    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
                      style: styles.rewardContainer,
                      children: (0, _jsxRuntime.jsx)(_text.Text, {
                        style: styles.rewardTitleStyle,
                        preset: "caption1Bold",
                        children: tags
                      })
                    }, ind);
                  })
                }), null)]
              }), (0, _jsxRuntime.jsx)(_reactNative2.ScrollView, {
                contentContainerStyle: styles.scrollViewStyle,
                testID: `${SCREEN_NAME}__ScrollViewListDataSource`,
                accessibilityLabel: `${SCREEN_NAME}__ScrollViewListDataSource`,
                children: listDataSource == null ? undefined : listDataSource.map(function (item, key) {
                  return (0, _jsxRuntime.jsx)(_ExpandableComponent.default, {
                    onClickFunction: function onClickFunction() {
                      updateLayout(key);
                    },
                    index: key,
                    item: item,
                    navigation: navigation,
                    testID: `${SCREEN_NAME}__ExpandableComponent`,
                    accessibilityLabel: `${SCREEN_NAME}__ExpandableComponent`,
                    setLoading: function setLoading(status) {
                      return _setLoading(status);
                    },
                    setConnection: setConnection,
                    refRetryAction: refRetryAction
                  }, Math.random() * 12);
                })
              })]
            })]
          }), (0, _jsxRuntime.jsx)(_reactNative2.SafeAreaView, {
            children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: styles.buttonContainerStyle,
              children: (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
                style: styles.buttonGradient,
                start: {
                  x: 1,
                  y: 0
                },
                end: {
                  x: 0,
                  y: 1
                },
                colors: [_theme.color.palette.gradientColor1End, _theme.color.palette.gradientColor1Start],
                children: (0, _jsxRuntime.jsx)(_button.Button, {
                  sizePreset: "large",
                  statePreset: "default",
                  typePreset: "primary",
                  text: (offerDetailsPayload == null || (_offerDetailsPayload$7 = offerDetailsPayload.data) == null ? undefined : _offerDetailsPayload$7.buttonLabel) || (0, _i18n.translate)("dineShopOfferDetails.addVoucherToWallet"),
                  textPreset: "buttonLarge",
                  onPress: handlePressButton,
                  testID: `${SCREEN_NAME}__ButtonAddVoucherToWallet`,
                  accessibilityLabel: `${SCREEN_NAME}__ButtonAddVoucherToWallet`
                })
              })
            })
          })]
        }))
      })), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
        visible: loading
      }), (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        header: true,
        hideScreenHeader: false,
        headerBackgroundColor: "transparent",
        visible: !isConnection,
        onBack: function onBack() {
          navigation.goBack();
        },
        onReload: onRetryError
      })]
    });
  };
