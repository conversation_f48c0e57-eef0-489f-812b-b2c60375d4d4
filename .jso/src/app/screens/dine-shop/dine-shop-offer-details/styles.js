  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.visibileStyle = exports.viewContainerStyle = exports.txtColor = exports.titleText = exports.timingsText = exports.timingsInfoViewStyle = exports.timingsInfoDayText = exports.timingStyle = exports.text = exports.tagsScrollViewStyle = exports.statusTextStyle = exports.spoonIconStyle = exports.shareButtonStyle = exports.separator = exports.scrollViewStyle = exports.safeAreaContainer = exports.richTextStyle = exports.richTextContainerStyle = exports.rewardTitleStyle = exports.rewardContainer = exports.phoneNumberStyle = exports.phoneIconStyle = exports.pageHeaderStyle = exports.mainContainerStyle = exports.loadMoreShowLessView = exports.loadMoreShowLessTextStyle = exports.loadMoreShowLessIconStyle = exports.linkView = exports.linkTestStyle = exports.lighterGreyLoadingColors = exports.infoBannerView = exports.headeriOSViewStyle = exports.headerViewStyle = exports.headerStyle = exports.headerAndroidViewStyle = exports.header = exports.flexStyle = exports.directionTextStyle = exports.directionTextDisabledStyle = exports.directionIconstyle = exports.content = exports.clockIconStyle = exports.changiEatsViewStyle = exports.buttonStyle = exports.buttonGradient = exports.buttonContainerStyle = exports.blogsViewStyle = exports.blogsTextStyle = exports.blogsFlatListStyle = exports.backButtonStyle = exports.arrowIconStyle = exports.areaInfoViewStyle = exports.activityIndicatorStyle = exports.aboutViewText = exports.aboutViewStyle = exports.aboutViewHeaderText = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var mainContainerStyle = exports.mainContainerStyle = {
    flex: 1,
    backgroundColor: _theme.color.palette.whiteGrey
  };
  var headerViewStyle = exports.headerViewStyle = {
    position: "absolute",
    zIndex: 1,
    marginTop: 24,
    flexDirection: "row"
  };
  var backButtonStyle = exports.backButtonStyle = {
    flex: 1,
    marginLeft: 14
  };
  var shareButtonStyle = exports.shareButtonStyle = {
    flex: 1,
    alignItems: "flex-end",
    marginEnd: 14
  };
  var buttonStyle = exports.buttonStyle = {
    justifyContent: "center",
    height: 44
  };
  var buttonGradient = exports.buttonGradient = {
    flex: 1,
    backgroundColor: _theme.color.palette.basePurple,
    borderRadius: 60,
    marginHorizontal: 16,
    marginTop: 24,
    marginBottom: 37
  };
  var buttonContainerStyle = exports.buttonContainerStyle = Object.assign({
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: _theme.color.palette.almostWhiteGrey
  }, _theme.shadow.primaryShadow);
  var scrollViewStyle = exports.scrollViewStyle = {
    marginBottom: 105
  };
  var richTextContainerStyle = exports.richTextContainerStyle = {
    marginBottom: 24,
    marginHorizontal: 25
  };
  var richTextStyle = exports.richTextStyle = {
    color: _theme.color.palette.darkestGrey
  };
  var activityIndicatorStyle = exports.activityIndicatorStyle = {
    position: "absolute",
    top: 0,
    left: 0,
    bottom: 0,
    right: 0
  };
  var lighterGreyLoadingColors = exports.lighterGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var viewContainerStyle = exports.viewContainerStyle = [{
    marginLeft: 24,
    marginBottom: 5
  }, {
    flexDirection: "row"
  }, {
    flexDirection: "row",
    marginLeft: 36,
    bottom: 15
  }, {
    flexDirection: "row",
    marginLeft: 36,
    bottom: 13
  }, {
    flexDirection: "row",
    marginLeft: 36,
    bottom: 11,
    marginBottom: 24
  }, {
    flexDirection: "row",
    marginBottom: 24
  }, {
    marginBottom: 24
  }];
  var titleText = exports.titleText = {
    color: _theme.color.palette.almostBlackGrey,
    fontWeight: _reactNative.Platform.OS === "android" ? "normal" : "700"
  };
  var header = exports.header = {
    flex: 1,
    backgroundColor: _theme.color.palette.whiteGrey,
    marginLeft: 24,
    marginTop: 22,
    marginBottom: 24,
    marginRight: 24
  };
  var separator = exports.separator = {
    height: 1,
    backgroundColor: _theme.color.palette.lighterGrey,
    marginLeft: 24,
    marginRight: 24
  };
  var text = exports.text = {
    color: _theme.color.palette.darkestGrey,
    paddingLeft: 10,
    paddingTop: 9,
    paddingBottom: 6
  };
  var timingsInfoDayText = exports.timingsInfoDayText = Object.assign({}, text, {
    paddingBottom: 1,
    paddingLeft: 5,
    flex: 0.5
  });
  var timingsText = exports.timingsText = Object.assign({}, timingsInfoDayText, {
    marginLeft: 1,
    flex: 0.6
  });
  var content = exports.content = {
    paddingLeft: 10,
    paddingRight: 10,
    backgroundColor: _theme.color.palette.whiteGrey
  };
  var pageHeaderStyle = exports.pageHeaderStyle = {
    marginLeft: 24
  };
  var rewardContainer = exports.rewardContainer = {
    marginTop: 12,
    borderWidth: 1,
    borderColor: _theme.color.palette.lightestPurple,
    backgroundColor: _theme.color.palette.whiteGrey,
    borderRadius: 8,
    height: 30,
    marginRight: 8,
    justifyContent: "center",
    alignItems: "center"
  };
  var rewardTitleStyle = exports.rewardTitleStyle = {
    color: _theme.color.palette.lightPurple,
    paddingHorizontal: 12,
    fontWeight: _reactNative.Platform.OS === "android" ? "normal" : "700"
  };
  var flexStyle = exports.flexStyle = {
    flexDirection: "row"
  };
  var tagsScrollViewStyle = exports.tagsScrollViewStyle = {
    flexDirection: "row",
    padding: 10
  };
  var headerStyle = exports.headerStyle = Object.assign({}, flexStyle, {
    flex: 1
  });
  var txtColor = exports.txtColor = {
    color: _theme.color.palette.almostBlackGrey,
    maxWidth: "80%"
  };
  var directionIconstyle = exports.directionIconstyle = {
    marginLeft: 14,
    marginRight: 5
  };
  var directionTextDisabledStyle = exports.directionTextDisabledStyle = {
    marginLeft: 52,
    bottom: 12,
    marginBottom: 12,
    color: "#ccc",
    fontWeight: _reactNative.Platform.OS === "android" ? "normal" : "700"
  };
  var directionTextStyle = exports.directionTextStyle = {
    marginLeft: 52,
    bottom: 12,
    marginBottom: 12,
    fontWeight: _reactNative.Platform.OS === "android" ? "normal" : "700"
  };
  var linkTestStyle = exports.linkTestStyle = {
    top: 16,
    marginLeft: 4,
    fontWeight: _reactNative.Platform.OS === "android" ? "normal" : "700",
    paddingBottom: 24
  };
  var clockIconStyle = exports.clockIconStyle = {
    marginLeft: 12,
    marginRight: 12
  };
  var phoneNumberStyle = exports.phoneNumberStyle = {
    marginTop: 15,
    marginBottom: 22
  };
  var phoneIconStyle = exports.phoneIconStyle = Object.assign({}, clockIconStyle, {
    marginLeft: 16,
    marginTop: 6,
    top: 10
  });
  var spoonIconStyle = exports.spoonIconStyle = Object.assign({}, phoneIconStyle, {
    marginLeft: 12
  });
  var areaInfoViewStyle = exports.areaInfoViewStyle = {
    bottom: 12,
    marginRight: 24,
    flex: 1
  };
  var timingsInfoViewStyle = exports.timingsInfoViewStyle = {
    bottom: 10,
    flex: 1
  };
  var statusTextStyle = exports.statusTextStyle = {
    marginTop: 2
  };
  var visibileStyle = exports.visibileStyle = {
    overflow: "hidden"
  };
  var arrowIconStyle = exports.arrowIconStyle = {
    alignItems: "flex-end"
  };
  var timingStyle = exports.timingStyle = {
    flexDirection: "row",
    marginRight: 24
  };
  var linkView = exports.linkView = {
    bottom: 10,
    flexDirection: "row",
    marginBottom: 7
  };
  var safeAreaContainer = exports.safeAreaContainer = {
    flex: 1,
    backgroundColor: _theme.color.palette.whiteGrey
  };
  var headeriOSViewStyle = exports.headeriOSViewStyle = {
    position: "absolute",
    zIndex: 1,
    width: "100%",
    marginTop: 40,
    flexDirection: "row"
  };
  var headerAndroidViewStyle = exports.headerAndroidViewStyle = Object.assign({}, headeriOSViewStyle, {
    marginTop: 24
  });
  var blogsFlatListStyle = exports.blogsFlatListStyle = {
    marginTop: 23
  };
  var blogsTextStyle = exports.blogsTextStyle = {
    paddingBottom: 12
  };
  var infoBannerView = exports.infoBannerView = {
    marginLeft: 24,
    marginRight: 24,
    marginTop: 24
  };
  var blogsViewStyle = exports.blogsViewStyle = Object.assign({}, infoBannerView, {
    marginTop: 50
  });
  var changiEatsViewStyle = exports.changiEatsViewStyle = Object.assign({}, blogsViewStyle, {
    marginTop: 26
  });
  var aboutViewStyle = exports.aboutViewStyle = Object.assign({}, infoBannerView, {
    marginTop: 50
  });
  var aboutViewHeaderText = exports.aboutViewHeaderText = {
    paddingBottom: 12
  };
  var aboutViewText = exports.aboutViewText = {
    color: _theme.color.palette.darkestGrey,
    lineHeight: 24
  };
  var loadMoreShowLessView = exports.loadMoreShowLessView = {
    flexDirection: "row"
  };
  var loadMoreShowLessTextStyle = exports.loadMoreShowLessTextStyle = {
    color: _theme.color.palette.gradientColor1Start,
    marginTop: 13
  };
  var loadMoreShowLessIconStyle = exports.loadMoreShowLessIconStyle = {
    marginLeft: 10,
    top: 13
  };
