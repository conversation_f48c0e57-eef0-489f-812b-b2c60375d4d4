  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.formatPrice = exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _icons = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _reactNativeDynamicRender = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[9]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _search = _$$_REQUIRE(_dependencyMap[11]);
  var _search2 = _$$_REQUIRE(_dependencyMap[12]);
  var _navigationUtilities = _$$_REQUIRE(_dependencyMap[13]);
  var _shopRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _sections = _$$_REQUIRE(_dependencyMap[15]);
  var _color = _$$_REQUIRE(_dependencyMap[16]);
  var _constants = _$$_REQUIRE(_dependencyMap[17]);
  var _dineShopContext = _$$_REQUIRE(_dependencyMap[18]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[19]);
  var _pageConfigRedux = _$$_REQUIRE(_dependencyMap[20]);
  var _adobe = _$$_REQUIRE(_dependencyMap[21]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[22]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[23]);
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[24]);
  var _shopFilters = _$$_REQUIRE(_dependencyMap[25]);
  var _errorUnplannedMaintenance = _$$_REQUIRE(_dependencyMap[26]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[27]);
  var _lodash = _$$_REQUIRE(_dependencyMap[28]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[29]);
  var _native = _$$_REQUIRE(_dependencyMap[30]);
  var _bottomTabs = _$$_REQUIRE(_dependencyMap[31]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[32]);
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[33]);
  var _suspend = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[34]));
  var _dineShopV = _$$_REQUIRE(_dependencyMap[35]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[36]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var container = {
    flex: 1,
    backgroundColor: _color.color.palette.lightestGrey
  };
  var searchBarContainer = {
    flexDirection: "row",
    paddingLeft: 24,
    alignItems: "center",
    paddingTop: 13,
    backgroundColor: _color.color.palette.lightestGrey
  };
  var filterIconContainer = {
    position: "absolute",
    right: 26,
    top: 26
  };
  var whiteContainer = {
    backgroundColor: _color.color.transparent
  };
  var overlayStyle = {
    backgroundColor: _color.color.palette.lightestGrey,
    flex: 1
  };
  var formatPrice = exports.formatPrice = function formatPrice(price) {
    if (price) {
      return Number(price.replace(/[^0-9\.-]+/g, ""));
    }
    return "-";
  };
  var SCREEN_NAME = "ShopScreen";
  var ShopScreen = function ShopScreen(_ref) {
    var navigation = _ref.navigation,
      route = _ref.route,
      onLayout = _ref.onLayout,
      overallScrollRef = _ref.overallScrollRef;
    // This object is responsible to define the component thats needs to be loaded
    var Components = {
      view: _reactNative2.View,
      mainPromo: _sections.MainPromoSection,
      exploreShopAtChangi: _sections.Cuisine,
      newlyOpened: _sections.NewlyOpened,
      recommendedForYou: _sections.RecommendedForYou,
      notToBeMissed: _sections.NotToBeMissed,
      youMayAlsoLike: _sections.YouMayAlsoLike,
      shopAndSpin: _sections.ShopAndSpin,
      brandOffer: _sections.BrandOffer
    };
    var dispatch = (0, _reactRedux.useDispatch)();
    var isFocused = (0, _native.useIsFocused)();
    var shopScreenRef = (0, _react.useRef)(null);
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("SHOP_SCREEN"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      showFilterModal = _useState2[0],
      setShowFilterModal = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isNoConnection = _useState4[0],
      setNoConnection = _useState4[1];
    var bottomTabHeight = (0, _bottomTabs.useBottomTabBarHeight)();
    var _useDineShopFlags = (0, _dineShopV.useDineShopFlags)(),
      isShopDineEpicV2On = _useDineShopFlags.isShopDineEpicV2On;
    var dataErrorMaintenanceConfig = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.GET_ERROR_MAINTENANCE));
    var listErrorMaintenance = (0, _lodash.get)(dataErrorMaintenanceConfig, "data.list", []);
    var screenMaintenanceObj = (0, _react.useMemo)(function () {
      var data = listErrorMaintenance == null ? undefined : listErrorMaintenance.find(function (obj) {
        return obj.screenName === _constants.screenTagName.shop;
      });
      return data || {};
    }, [listErrorMaintenance]);
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var _useHandleScroll = (0, _navigationUtilities.useHandleScroll)(),
      handleScroll = _useHandleScroll.handleScroll,
      isTabVisible = _useHandleScroll.isTabVisible;
    var shopHandlers = (0, _react.useContext)(_dineShopContext.DineShopContext).Handlers.shop;
    var handleShopComponentClick = function handleShopComponentClick(item) {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppShopNewlyOpened, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppShopNewlyOpened, (item == null ? undefined : item.tenantName) || ""));
      navigation.navigate(_constants.NavigationConstants.shopDetailsScreen, {
        tenantId: item == null ? undefined : item.id,
        name: item == null ? undefined : item.tenantName
      });
    };
    var handleMasonryComponentClick = function handleMasonryComponentClick(item) {
      return item;
    };
    var handleComponentClick = function handleComponentClick(item) {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppShopContentSwimlaneTile, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppShopContentSwimlaneTile, (item == null ? undefined : item.tenantName) || ""));
      if (item != null && item.linkURL) {
        navigation.navigate(_constants.NavigationConstants.webview, {
          uri: item.linkURL
        });
      }
    };
    var handleShopSpinComponentClick = function handleShopSpinComponentClick(item) {
      handleComponentClick(item);
    };
    var handleNotToBeMissedComponentClick = function handleNotToBeMissedComponentClick(item) {
      handleComponentClick(item);
    };
    var handleSpotlightBrandOfferComponentClick = function handleSpotlightBrandOfferComponentClick(item, index) {
      var userId = profilePayload == null ? undefined : profilePayload.id;
      if (item != null && item.productName) {
        var _item$salePrice, _item$originalPrice;
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppShopTenantPromoSwimlane, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppShopTenantPromoSwimlane, `Shop | ${index + 1} | ${item == null ? undefined : item.productName} | ${item != null && item.tenantName ? item == null ? undefined : item.tenantName : "-"} | ${formatPrice((item == null || (_item$salePrice = item.salePrice) == null || (_item$salePrice = _item$salePrice[0]) == null ? undefined : _item$salePrice.value) || (item == null || (_item$originalPrice = item.originalPrice) == null || (_item$originalPrice = _item$originalPrice[0]) == null ? undefined : _item$originalPrice.value))} | ${userId ? userId : "-"}`));
      }
      var _ref2 = item || {},
        navigationType = _ref2.navigationType,
        navigationValue = _ref2.navigationValue,
        redirect = _ref2.redirect;
      if (!navigationType || !navigationValue) return;
      handleNavigation(navigationType, navigationValue, redirect);
    };
    if (isShopDineEpicV2On) {
      shopHandlers.routeParams = route == null ? undefined : route.params;
    }
    shopHandlers.overallScrollRef = isShopDineEpicV2On ? overallScrollRef : shopScreenRef;
    shopHandlers.handleTenantComponentClick = handleShopComponentClick;
    shopHandlers.handleMasonryComponentClick = handleMasonryComponentClick;
    shopHandlers.handleShopSpinComponentClick = handleShopSpinComponentClick;
    shopHandlers.handleNotToBeMissedComponentClick = handleNotToBeMissedComponentClick;
    shopHandlers.handleSpotlightBrandOfferComponentClick = handleSpotlightBrandOfferComponentClick;
    var getShopDataFromAEM = (0, _react.useCallback)(function () {
      dispatch(_shopRedux.default.shopRequest());
    }, []);
    var pageConfigPayload = (0, _reactRedux.useSelector)(function (data) {
      return _pageConfigRedux.PageConfigSelectors.pageConfigShopPayload(data);
    });
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)("Shop_Landing");
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      (0, _adobe.commonTrackingScreen)("Shop_Landing", (0, _screenHook.getPreviousScreen)(), isLoggedIn);
    }, []));
    (0, _react.useEffect)(function () {
      var checkInternet = /*#__PURE__*/function () {
        var _ref3 = (0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch.isConnected;
          if (!isConnected) {
            setNoConnection(true);
          } else {
            getShopDataFromAEM();
          }
        });
        return function checkInternet() {
          return _ref3.apply(this, arguments);
        };
      }();
      checkInternet();
    }, []);
    var onScroll = function onScroll(event) {
      var _route$params;
      handleScroll(event);
      route == null || (_route$params = route.params) == null || _route$params.setOptions == null || _route$params.setOptions({
        tabBarVisible: isTabVisible.current
      });
    };
    var contentContainerStyle = {
      paddingBottom: ((0, _reactNativeSafeAreaContext.useSafeAreaInsets)().bottom + _reactNative2.Platform.OS === "ios" ? 18 : 50) + bottomTabHeight
    };
    var handleButtonOnPress = /*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* (navigationObj, redirect) {
        var type = navigationObj.type,
          value = navigationObj.value;
        if (!type || !value) return;
        handleNavigation(type, value, redirect);
      });
      return function handleButtonOnPress(_x, _x2) {
        return _ref4.apply(this, arguments);
      };
    }();
    var onSearchPressed = function onSearchPressed() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppShopSearch, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppShopSearch, _adobe.AdobeTagName.CAppShopLanding));
      navigation.navigate("search", {
        module: "SHOP",
        sourcePage: _adobe.AdobeTagName.CAppShopLanding
      });
    };
    var onFilterPressed = function onFilterPressed() {
      (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(true);
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppShopSearchFilter, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppShopSearchFilter, "1"));
      setShowFilterModal(true);
    };
    if (!(0, _lodash.isEmpty)(screenMaintenanceObj) && screenMaintenanceObj != null && screenMaintenanceObj.enableMode) {
      return (0, _jsxRuntime.jsx)(_errorUnplannedMaintenance.ErrorUnplannedMaintenance, {
        header: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.header,
        subHeader: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.subHeader,
        icon: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.icon,
        buttonLabel: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.buttonLabel,
        buttonLabel2: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.buttonLabel2,
        onFirstButtonPress: function onFirstButtonPress() {
          return handleButtonOnPress(screenMaintenanceObj == null ? undefined : screenMaintenanceObj.navigationFirst, screenMaintenanceObj == null ? undefined : screenMaintenanceObj.redirectFirst);
        },
        onSecondButtonPress: function onSecondButtonPress() {
          return handleButtonOnPress(screenMaintenanceObj == null ? undefined : screenMaintenanceObj.navigationSecond, screenMaintenanceObj == null ? undefined : screenMaintenanceObj.redirectSecond);
        },
        testID: `${SCREEN_NAME}__ErrorUnplannedMaintenance`,
        accessibilityLabel: `${SCREEN_NAME}__ErrorUnplannedMaintenance`
      });
    }
    if (isNoConnection) {
      return (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        header: false,
        headerBackgroundColor: "transparent",
        visible: true,
        testID: `${SCREEN_NAME}__ErrorOverlayNoConnection`,
        onReload: function onReload() {
          var checkConnection = /*#__PURE__*/function () {
            var _ref5 = (0, _asyncToGenerator2.default)(function* () {
              var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
                isConnected = _yield$NetInfo$fetch2.isConnected;
              if (isConnected) {
                getShopDataFromAEM();
                setNoConnection(false);
              }
            });
            return function checkConnection() {
              return _ref5.apply(this, arguments);
            };
          }();
          checkConnection();
        },
        noInternetOverlayStyle: overlayStyle
      });
    }
    return (0, _jsxRuntime.jsx)(_suspend.default, {
      freeze: !isFocused,
      children: (0, _jsxRuntime.jsx)(_reactNative2.ScrollView, {
        ref: shopScreenRef,
        onScroll: onScroll,
        scrollEventThrottle: 1,
        showsVerticalScrollIndicator: false,
        contentContainerStyle: contentContainerStyle,
        style: container,
        testID: `${SCREEN_NAME}__ScrollView`,
        accessibilityLabel: `${SCREEN_NAME}__ScrollView`,
        scrollEnabled: !isShopDineEpicV2On,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          onLayout: onLayout,
          style: whiteContainer,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: searchBarContainer,
            children: [(0, _jsxRuntime.jsx)(_search.Search, {
              type: _search2.SearchBarVariations.mainPage,
              placeholderTx: "shopScreen.searchBarText",
              onPressed: onSearchPressed,
              testID: `${SCREEN_NAME}__SearchBar`,
              accessibilityLabel: `${SCREEN_NAME}__SearchBar`
            }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: filterIconContainer,
              children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                onPress: onFilterPressed,
                testID: `${SCREEN_NAME}__TouchableFilter`,
                accessibilityLabel: `${SCREEN_NAME}__TouchableFilter`,
                children: (0, _jsxRuntime.jsx)(_icons.Filter, {
                  color: _color.color.palette.lightPurple
                })
              })
            })]
          }), (0, _jsxRuntime.jsx)(_shopFilters.ShopFilter, {
            showFilterModal: showFilterModal,
            setShowFilterModal: setShowFilterModal
          }), (0, _jsxRuntime.jsx)(_sections.ChipFilterSection, {
            screen: "SHOP"
          }), (0, _jsxRuntime.jsx)(_reactNativeDynamicRender.default, Object.assign({}, pageConfigPayload, {
            mapComponents: Components
          }))]
        })
      })
    });
  };
  var _default = exports.default = ShopScreen;
