  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.shopDetailsPayload = exports.sectionsPayloadForShop = exports.sectionsPayloadForDine = exports.DineHeroData = undefined;
  var _masonryListing = _$$_REQUIRE(_dependencyMap[0]);
  var _tenantOffer = _$$_REQUIRE(_dependencyMap[1]);
  // This is the temp payload of each sections

  var sectionsPayloadForDine = exports.sectionsPayloadForDine = {
    newlyOpened: {
      default: [{
        logoUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Logos/Restaurant+logos/lukes-lobster-logo.jpg",
        imageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/Tenant+component/Dine/lukes-lobster-01.jpeg",
        tenantName: "Luke’s Lobster",
        location: "Jewel, L2"
      }, {
        logoUrl: "https://s3-alpha-sig.figma.com/img/c041/d2dc/408386f5644e2e99304353eac95da28c?Expires=**********&Signature=Urg6~MF1VVE2IYpKFKetQ09rzQ18DFSOxS4fteWplT79AvvvkdXiCWaDLbgGQu68~W4PASxkW3TVXfqMIEriOD6aMUkgXMkuyfGX1X8~7w~TrvNg4s2PWV~O44-RPAEHIS~p~lsnTRqeUyw9uUbOEHm7eW7W7bgZsP4oqybqUYbIYcBzYBu7jUPmZSmZqHSNF8XNYnFulAFKmaSJrgSubBnYdCFdMkxsAxsuidq0P28o~KI~nQAi~cz~6KbDVAna9SJ3l-6v8wkxOAR-1WlBBgD07nVxJhJb04rjPMc6pm~taibVlpHcvri63~3s7uofPK2FLd4sKZz4xB5FyrsaUg__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA",
        imageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/Tenant+component/Dine/Nectar.jpg",
        tenantName: "Nectar",
        location: "Jewel, L3"
      }, {
        logoUrl: "https://s3-alpha-sig.figma.com/img/f771/b321/e12a15de620be97563612cb6cc01faec?Expires=**********&Signature=MY4Jy8DOdR44O2CQzvJBywEWkKMNW24Lxvd8CVWHawDgS0BBMsG2TfF20U8BW-~FNB2TOerEKzXMCNl5Y-sYaqxwkUF9-LzftfV56bMZPx91DyOTQHzSvK0C8EUwA7xJzzse5Rcju-FN3PHSjQSewpg6yYFKs3AOFkL8Ls1JHOCIRiu0IMj4EZo2FM9dkA5-qFbAWZf5S8FQcPNZUA26cKHAFw1-ryLROv~a5EY8goZO2YgkSu~h4d6iIWQ8uwlMhlnqOL9GcD~Gz0pLBDroPuaDJde0iSdC1HEM0F5MU1miheTqP4Z6QXAySEQ-GYmJ1TVncmncQKpUu4N0YPO-Mw__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA",
        imageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/Tenant+component/Dine/El-Fuego.jpg",
        tenantName: "Elfuego™ by COLLIN’S®",
        location: "Jewel, L2"
      }],
      loading: [{}, {}, {}]
    },
    exploreMore: {
      default: [{
        imageUrlArray: [],
        logoUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Logos/Restaurant+logos/_-Arabica-logo.jpg",
        tenantName: "% Arabica",
        location: "Jewel L1 · Coffee, Bakery · $$",
        rewardTitle: "2 x Changi Reward Points"
      }, {
        imageUrlArray: [],
        logoUrl: "https://s3-alpha-sig.figma.com/img/29c7/9782/2a7a2e5fbbf6ca7529494d1fa0a04d5d?Expires=**********&Signature=gmyjAAhEMwplK8Rda7DtuhTASkZyWc~hunvMx4yIwu4-VeNnPo3ptzYa81zmXSd3u8F14t803m6abGsxx48yg-VV91Xh7ZFFNvSF9i7-iZj~GMGofmbt4ScVxB13ohmgf2Lu2X9jle7umDDL7OBSeeCG48PkrCsntLF02KUiTSYlh3zp7K66Y3DdUUFzD14caJ~qTnT39F9IElHBFjBfOhIav70ij0OGayNuiGGm0auGUm6ehYjYaVlvQmZkVx0yGp0DOLyJvzYOL7H49CZsHGU3i9F~KXWFbE9qwh3KTXoXfDru7JbRqJSYIZmPJa~PHTbi2t-H4pDSuZdePNx77g__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA",
        tenantName: "108 Matcha Saro",
        location: "Jewel L1 · Coffee, Bakery · $$",
        rewardTitle: "2 x Changi Reward Points"
      }, {
        imageUrlArray: [],
        logoUrl: "https://s3-alpha-sig.figma.com/img/a3dc/23d5/246ec6e60a96354fab338cb403735cac?Expires=**********&Signature=Ku9iBCaH5254Fhd~4ifXxl4ohlVvSVm02EEdV4R0j18aGnQBGmISTw8Y28jWEWa-HoZoAcxW1i5b8tNO07X89pa~KYk24ZofmwncXSvjUmug2-A1lVREeQTEBCeMsGjNEYF88SLjmzbRC4ltPsctj6uEvg6uEvM-7auxa9T2m45DR-zJS74yrCck5jljXxUGxScbcy-X~yscqElaZam6Yz5VioZrSD8YuuNBhybjqRsKTIa0dQJHpiLLMcqRhWQ61vjYCG8IXgkiBLk-Vm-3s~wzonUYwm0k~Gs-lezSALGimJ3bERQHqVyk7PRF0TuRLZ5W9ws3iq47vjTy3Ocp9g__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA",
        tenantName: "AC Kafe",
        location: "Jewel L4 · Western, Pasta · $",
        rewardTitle: ""
      }, {
        imageUrlArray: ["https://s3-alpha-sig.figma.com/img/5cc5/e70e/17ff6fadc128baf35c60e0ca27238863?Expires=**********&Signature=TAJe1JzoO3W23hurG0ghbLkPyUP4RMjEBdwD7P1RlzNGDPlaNaiHAGhzix~V~n9YNCU4i5HUVoGkyX7fCTeyORjyNAttAKPRIoR0qN9IN-NRCYWuwufHzlexfoY2y-TcHKglZqYPbwIxSKgD8wO6-1YpkCzEjwnxGi6ikimg3tEuhCtDfnTi7wnKrFM4XRMwrGiueRU6VA-ExDxKOGFsPrRs7A1aK5-7EjsF1Wsh4EyTDu0FJLdOe07BlI0~IE0gR9MUiBFJKQ9tbHTRhiVAlX0W2M9Wg-ZDZ7jWgwbp0pWORU-xXp4COx8AGghEEU01UG0T2uDA634A4gunrOX9ig__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA", "https://s3-alpha-sig.figma.com/img/1f09/7465/00a0eb12a64f92312be823a1918740a9?Expires=**********&Signature=UP~JKooUJPcMzl8qR6ecBsCyEAxKFY3RXz~GyHGGVk57nK-7ioRS-d0Pq0jMtHatjOKV6nxQzSJ6NkzZCTGTRE8JMH5sT3O8m7iH5gUeFtwB1C3bN38qM588HvHJFDTEOSydINkXk~JYr0Ly36UKwraW~s6EsN599PmYGKPn5SarkpidRrpff8orltZ7fzG~ZdyovLeBiDvqfaJrLdg8S1tG~BOAucSy9Mv55rRp10breHyei1G2ZjQFtkpB0pJw1QJNwh3GjgLe1Cr4DgrtNLhFTXjNnc8UrS1p9CoB74ef4o8ZuezEfF2RTqC8bKsKuR8RYRQ1UU4~oBur3SFU6w__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA", "https://s3-alpha-sig.figma.com/img/b2d7/e019/aa179bb6492a78ef1b9c3b67fc9cba3f?Expires=**********&Signature=StOg2jC~am8YME~0UdpJryjkBQrPrzLf4pxUWG44VOmyQHYUBDkqdjmGHSNl1FLndAvJ-ChI9QRAAwO41RVgvOOCfvbFakSOKQ7-nVQohtsrhO-P8hkIgIY9jTsCaY0uy8w9pfnMmBU4H5-f10I72i-kLjbklanm6DIzszI6igXJscp83CdDVfgOm44Tf5xA~wvu3GOO5vOV-9Mn3vPKZWZAYGtUhNKXAGBrCxqs0aZhclMgegSWz0tq6ued4qzzKDlqG4S05oeQBC5BB8IX0uQUQ3aMB66lnCxW68xDqlV-8ncIhPxGaOpK0WWDBlQlkLL-RInsGKUcIP7bno-YIw__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA"],
        logoUrl: "https://s3-alpha-sig.figma.com/img/629f/cb46/c13a6137f75c82f1d314325453081772?Expires=**********&Signature=ZTxM-DwepuAwYthesLi7yJg~euKuQRsB1fw8IkfPIcMkOl2te6r4x0MPu4RdmzeElL5kKc6VHFQojMhqjU3vmiaXX17F1naMkUu3hAEpxC8b6AkPUmSucrjfVqAnVXuS0KFNSSf38ktlcU6v4BuTNSwRA5CwrjwhuzWDPKFz7GJ~qVzSok6OYnWTf0SLuifnOHxyHLkCyjZgKKiDk-PTRDEZXeFqZnLWV8BMIs4IWiqAPULSK0KZDe3KnQSF5PaYkrjpKudM5Fc1rnnDNb0LWnTm47KwvvqB7jLOhUDXtsSFGyfrNA5mA~8HbkOSjvfWd2~D2HveqXJpf591MGQ~mA__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA",
        tenantName: "Aloha Poké",
        location: "Jewel L4 · Healthy, Seafood · $$$",
        rewardTitle: "2 x Changi Reward Points"
      }],
      loading: [{}, {}, {}, {}]
    },
    contentGuide: {
      default: [{
        imageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/Content+guides/listing-image.jpeg",
        title: "Cafés in Changi every coffee lover should know",
        onPressed: "Cliked 1"
      }, {
        imageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/Content+guides/hub_spoke_listing+image.jpeg",
        title: "Enhanced Covid-19 precaution measures in place",
        onPressed: "Clicked 2"
      }, {
        imageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/Content+guides/FAQs.jpg",
        title: "A title will go here that can overflow on 2 lines more than this",
        onPressed: "Clicked 3"
      }],
      loading: [{}, {}, {}]
    },
    cuisine: {
      default: [{
        imageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/Categories/Cuisine+categories/local.jpeg",
        labelCopy: "Local"
      }, {
        imageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/Categories/Cuisine+categories/chinese.jpeg",
        labelCopy: "Chinese"
      }, {
        imageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/Categories/Cuisine+categories/japanese.jpeg",
        labelCopy: "Japanese"
      }, {
        imageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/Categories/Cuisine+categories/western.jpg",
        labelCopy: "Indonesian"
      }],
      loading: [{}, {}, {}, {}]
    },
    chips: {
      default: [{
        id: 1,
        text: "Local",
        isSelected: false
      }, {
        id: 2,
        text: "Chinese",
        isSelected: false
      }, {
        id: 3,
        text: "Japanese",
        isSelected: false
      }, {
        id: 4,
        text: "Halal",
        isSelected: false
      }, {
        id: 5,
        text: "Korean",
        isSelected: false
      }, {
        id: 6,
        text: "Desserts",
        isSelected: false
      }],
      loading: [{
        id: 1
      }, {
        id: 2
      }, {
        id: 3
      }, {
        id: 4
      }]
    },
    mainPromo: {
      default: [{
        orderId: 1,
        backgroundImageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/Main+promo/Main-promo-1.jpg",
        title: "Weekday lunch at Tonito",
        subcopy: "Valid till 31 Aug 2021"
      }, {
        orderId: 3,
        backgroundImageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/Main+promo/Main-promo-2.jpg",
        title: "Large prawn noodle",
        subcopy: "Valid till end of June"
      }, {
        orderId: 2,
        backgroundImageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/Main+promo/Main-promo-3.jpg",
        title: "Weekday tea at Tonito",
        subcopy: "Valid till Aug 2021"
      }],
      loading: [{}, {}, {}, {}]
    },
    instagram: {
      default: {
        imageUrls: [{
          id: 1,
          imageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/instagram/ramen.jpeg"
        }, {
          id: 2,
          imageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/instagram/fries.jpeg"
        }, {
          id: 3,
          imageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/instagram/hello-kitty.jpeg"
        }, {
          id: 4,
          imageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/instagram/shake-shack.jpeg"
        }],
        hashtags: [{
          id: 1,
          text: "@ChangiAirport"
        }, {
          id: 2,
          text: "#EatAtChangiAirport"
        }, {
          id: 3,
          text: "@ChangiAirport"
        }]
      },
      loading: [{}, {}, {}, {}]
    },
    recommendedForYou: {
      vouchers: {
        default: [{
          id: 1,
          imageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/Main+promo/Main-promo-1.jpg",
          tenantName: "Weekday lunch at Tonito",
          offerTitleDescription: "Offer title 1 line only",
          validTillDate: "Valid till 31 Aug 2021"
        }, {
          id: 2,
          imageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/Vouchers/aloha-poke.jpeg",
          tenantName: "Weekday lunch at Tonito",
          offerTitleDescription: "Offer title 1 line only",
          validTillDate: "Valid till 31 Aug 2021"
        }, {
          id: 3,
          imageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/Vouchers/bobbi-brown.png",
          tenantName: "Weekday lunch at Tonito",
          offerTitleDescription: "Offer title 1 line only",
          validTillDate: "Valid till 31 Aug 2021"
        }, {
          id: 4,
          imageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/Vouchers/burger-lobster.jpg",
          tenantName: "Weekday lunch at Tonito",
          offerTitleDescription: "Offer title 1 line only",
          validTillDate: "Valid till 31 Aug 2021"
        }],
        loading: [{}, {}, {}]
      },
      tenantListing: {
        default: [{
          imageUrlArray: [],
          logoUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Logos/Restaurant+logos/_-Arabica-logo.jpg",
          tenantName: "% Arabica",
          location: "Jewel L1 · Coffee, Bakery · $$",
          rewardTitle: "2 x Changi Reward Points"
        }, {
          imageUrlArray: [],
          logoUrl: "https://s3-alpha-sig.figma.com/img/29c7/9782/2a7a2e5fbbf6ca7529494d1fa0a04d5d?Expires=**********&Signature=gmyjAAhEMwplK8Rda7DtuhTASkZyWc~hunvMx4yIwu4-VeNnPo3ptzYa81zmXSd3u8F14t803m6abGsxx48yg-VV91Xh7ZFFNvSF9i7-iZj~GMGofmbt4ScVxB13ohmgf2Lu2X9jle7umDDL7OBSeeCG48PkrCsntLF02KUiTSYlh3zp7K66Y3DdUUFzD14caJ~qTnT39F9IElHBFjBfOhIav70ij0OGayNuiGGm0auGUm6ehYjYaVlvQmZkVx0yGp0DOLyJvzYOL7H49CZsHGU3i9F~KXWFbE9qwh3KTXoXfDru7JbRqJSYIZmPJa~PHTbi2t-H4pDSuZdePNx77g__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA",
          tenantName: "108 Matcha Saro",
          location: "Jewel L1 · Coffee, Bakery · $$",
          rewardTitle: "2 x Changi Reward Points"
        }, {
          imageUrlArray: [],
          logoUrl: "https://s3-alpha-sig.figma.com/img/a3dc/23d5/246ec6e60a96354fab338cb403735cac?Expires=**********&Signature=Ku9iBCaH5254Fhd~4ifXxl4ohlVvSVm02EEdV4R0j18aGnQBGmISTw8Y28jWEWa-HoZoAcxW1i5b8tNO07X89pa~KYk24ZofmwncXSvjUmug2-A1lVREeQTEBCeMsGjNEYF88SLjmzbRC4ltPsctj6uEvg6uEvM-7auxa9T2m45DR-zJS74yrCck5jljXxUGxScbcy-X~yscqElaZam6Yz5VioZrSD8YuuNBhybjqRsKTIa0dQJHpiLLMcqRhWQ61vjYCG8IXgkiBLk-Vm-3s~wzonUYwm0k~Gs-lezSALGimJ3bERQHqVyk7PRF0TuRLZ5W9ws3iq47vjTy3Ocp9g__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA",
          tenantName: "AC Kafe",
          location: "Jewel L4 · Western, Pasta · $",
          rewardTitle: "2 x Changi Reward Points"
        }],
        loading: [{}, {}, {}]
      }
    }
  };
  var sectionsPayloadForShop = exports.sectionsPayloadForShop = {
    newlyOpened: {
      default: [{
        logoUrl: "https://s3-alpha-sig.figma.com/img/8214/b41e/7c9992272d8a0e44dbe40624b3ad99a6?Expires=1624233600&Signature=BKhv4WlodOoUO5~4QoQFvaVhd7z9-elA9ZzSmFB~4ACslvsBMcL-EBZnoPKRftFXEyMNjXzQ~4fsinOJ4nUlYH~oJ7AScJMrrBmSQ-dlQxZ8OXa1TVmavdC6xx~pt-AjNO66yYhc6i1Ezk83L7EY71G98jMVZMh2WSa5kCS2yy2buDkj0i~nENmwuj8ZVEyzSvfHatqXktgKwHfGe~c29OL3xCKq2GbbOW0jWv-DmjqgxbcC9C84uvdU0ZOSWVQnL0IwvqYdmhFaVkAWcgvgzwNvMxHUiPtnLIgf~0pi7EW-Fc7QK7P2G9X03fqVCSJhh4aRxKMG-5LVA-odqip-EQ__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA",
        imageUrl: "https://s3-alpha-sig.figma.com/img/9115/0c37/0841c714d56a7145a12b16d7904e9145?Expires=1624233600&Signature=IlNUCQmXj3FVj8Dz3RbumdWHMaHpgFM~lsjtBx5-gqok6htv1k1MY~jaCFS7ipKyJDGtv2TKKaIP4RqGo-FO5T-rmkb0zUV7tDW0O9ZvwKKDxpubgrkV5UcH01Ggbw6Wg5f~Q3osontwDY0Z9mz5yOiHm5l4Rclg4U9KMzbWdjnrz3l6Ynf9tyFtLba1u493U-UTmOesQV8Y3jcNa7OxiARDUWMfE4~2UPWKh~d5DKlig2NCBdItFOL2~ESFwQkzU9FkL6IgJSf4Uz6Chc3MlzE9giRM9aA02G9fDaax0fzaAr6bFzmVaHQ4y7oupy8-vfWiO18XRTOhoNVu3vN61w__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA",
        tenantName: "American Tourister",
        location: "Jewel, B1"
      }, {
        logoUrl: "https://s3-alpha-sig.figma.com/img/65ce/0f79/f98dff967b3f563ed6084c92635e0b0e?Expires=1624233600&Signature=Kc0qFDUPtP1AMKXZwvl-ZhIIw1uGlmvUWbUWopczKxK3VEK9F-FyhYkQbnTFV~KXT1hkI4~V29kQPyO6vvfaYYHpJDNQg78JImvDeN0clNInn20aJMqI~LIZTNMGoc-C0dIWv7VnjRkrIEyMOzUTE0LZmqXBaTBq-srcVqj3phM1~eYgetjTpXFyg-zFkM66SOPCrR7QQX47RsN7FSXY6j5tYrZbSeNzVRNI7npLO6RItXIdNG-I1h2BnB9d8G7NnbmF~fVzr4ZOHzR5BoTz6NgZIPkq~VCEZsuIDciut-t1b~VgS74JF4kU4WZkxuWjIiH4xXCTRejYkrhsXMEsuw__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA",
        imageUrl: "https://s3-alpha-sig.figma.com/img/3bd7/52a5/b38fb1ddb583fd762a565fd3737eb17c?Expires=1624233600&Signature=C9Tv5YrMJgq~0nA8LU3uvIcOMTWRs9v3623L2BWby8k6tfnyZ9kMmxsxJNh9kuVJgTTZK~oBuA~byV~VvR2NMrbks9VoBB~HjptZVgMlqJhFIrSZBVgIQ~3y~LNLDBCi7CI80t9-c3JveZFMg1f1oL36CHznWY0SMsvMKLiPtdTsBU4C2okTcF7cybyGaO2geBlgmlfA-FehSCX94GUW-SlN2vt63zq3tweNFVFuz6cHyIAS-rlxiYVsJgZrHLO2E8UKKA4idZxWoXXy~SdXSgQdWK128PbAJSoL6GYrEXlPtapv2x8BU3saaAmT0l-ONOGXs5oq059aQLT24RP1RA__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA",
        tenantName: "Nike",
        location: "Jewel, L2"
      }, {
        logoUrl: "https://s3-alpha-sig.figma.com/img/0c1e/8262/3f0c112fdc7a8fc59fd8163945d84d7f?Expires=1624233600&Signature=X3PZhQpgPOwafj25sWhrPDJcQXikeRs4Sb1gLbNOp2DHTCchxtLAtz-epJOAsqUfLRy2N-ZFuAma0ItSYAh7ZhNhizKz-ZNHWgICxI3on1G6QUKnlo4vyHjJ-rcvP1hVTijnn-TUv23D4sSR0pwrpLG79lJF-x56pytxK45j8l1A17oIBdM-7qY9YepNykzL6bIU5NkxXtuOvY6PwFzp754iBSQSVqihjVDk6iW7mMqOn4pQbCQ48pHKCMXKortd0YN5uvBzeMbvHIadkf0KoTSgabD71jpElicKXX5Ba8ixEsF5VgjA7HrEeV5OeIUirVEwMlTuZip8~9gmYz-A0g__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA",
        imageUrl: "https://s3-alpha-sig.figma.com/img/4678/c565/20f758e14b8122db307360f783599df2?Expires=1624233600&Signature=OmMKWGPsA54JqkzDYc3Ci8FV~rnbOGynzO5tx1ZIsIdB9A6gAlS1ILUojdRZ~WRZhmq6~5o57RtTaVUPWpWha0Y8i-T4dV0fLEgmR1nOvEFxuqXypOOeL81x~H1Nl1~0N9Asv3ZyKvTHq58t7ZG2HaFOYKtYL6s0F~DvDidZrfPNjk~OnjVbWhthk4OTU-i6CCIvl1mOyavdK-cKuKzg7ZIUJ~EBIONwjrEayzTe0xx~Ai6BBgcM~idXJkW2f6AI~~0z-06meEY37oG6Ls-8EjSX~FNT7AY~s-x6rq3KC7gsGD9Nmcz6Zu7AvgH6l7l0hHyGQVAgeoP1py60GwQoSQ__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA",
        tenantName: "Ace Bags & Luggage",
        location: "Jewel, L2"
      }],
      loading: [{}, {}, {}]
    },
    cuisine: {
      default: [{
        imageUrl: "https://s3-alpha-sig.figma.com/img/de8c/ec9a/8f1983bc47931623295e8283c2e96ffd?Expires=1624233600&Signature=DWbBbFc5wjRERq8x1js9ccbg6-7VLGQZW8AI7xppuaAucKlhEaSHIDqoKX7jl5FjWmwxqTqUEBENP~dcUCfbieTZS4~HOSIimVBxZyhnVpFxuK4-11Dz91rfEzzkES5hr62MQ6MDMqyJiLYXnwmcFqQ-tpAh5qxJH8~wnvgqsbLYF23y2dKkxAnZANKS7j-xV8Ng77iVjih~gh32E783ZSj3pZxmZcoM4ig9gigHSskWE~GmfGA5nXL84VhhQNiG9TcUICrII7j7ooaYabCj45xHfikneAixu43q3ScRPdL~y0duFwjW0t4xSzcFLKk4EEv0vTP3ShoCS-ltzii4BQ__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA",
        labelCopy: "Local"
      }, {
        imageUrl: "https://s3-alpha-sig.figma.com/img/d6e9/3048/2ece7aac7cd51ba18e0c7deffa551e46?Expires=1624233600&Signature=DK~e3MU1gURhSoMdQFtYzwU2EsxVD3RbRkHY5DnzEjjRB2x-jbtZVEMtA47CZduj~vzrjBN7MG3dfkUhDEN2DHdQvjm18-udNb8B5xEjOA-uLHZHPHGIvcgWrUBHOt-G3qy5clfwr7YX~dpIyrPemFAKXjrXzR-qfyYaKJjFLua9Lg~j6stUB1v9ev4olGJ1uThRwo-LjDEKG0PIaE9o53AztmmiY7p1JhfpQtjPBfh10Lyawr8dqunJny6W9fPa99IibB2tGXcztaTzktObAT9PN92Gd6P9hy6NhZvpyUwnQUR-7nWeEfb39fqfHw0OeqVjxieS6JxNvX4oYNcweA__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA",
        labelCopy: "Beauty"
      }, {
        imageUrl: "https://s3-alpha-sig.figma.com/img/2946/9899/93a6a54bde77b8632be45e2b2c81ba37?Expires=1624233600&Signature=c3LOv1njMg14QgaUOneOwnCn2w~jpVy45H9lViWjs4O6IPTWdOepjzuG5hxUH9yNlYWeV2l6uqfdalONi-MrRyiUppNjL6wV1DElnDiT0Xtz~8IDj83S-RY~ET7fwNqNMpgRkiuVbpCUzuN6Pk40EiP~2rEqBoAmZUC1AKnjUdAdA0-8Lled5b0oGpmnvesctfQGEPA9nFg8jxh74--x089mBDtNCqeqFipEaAgpL4TEs67NAC5-HgnMR7pKp6lk0YdjIbzCesYa08CYiGP~MYS7lzgNJUiwhochQH~5OGMqP250zQe38ndYXD-MvwVuS-rHdalD8wXcwGcbIiaeGw__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA",
        labelCopy: "Electronics"
      }, {
        imageUrl: "https://s3-alpha-sig.figma.com/img/0449/b307/8b158a3d5a87b95cb48b88119252df84?Expires=1624233600&Signature=gEcLl-vWc5thUXCFwnD3W1mvGwiRajNEUAPg-2UDmqMKQfK6EB5w11nNc-3K6jW~FcO0DWYq8dl~mEWoD5qiud0C8nZeFyi9srODpVTyDYHCHIJp7HiX1Bp9ujAKlzhStAWum6SLHJeVOqvq3amUCcZPOFbJb7yItSQpVBbFXBWtIF3DeVjTteLqeZ8QEg7zJjaJOD1l7h-t4fn0ezPV9h6luQc--2HVkpvSB3~kPYHfdul2vpZAE-knO-ex-1y~n-Dou0lVO-bNawtCDXzx8RvD-1xN-LXoc1eFanfWxVL3GiNrUyFxc5ybQaoFk5y0wrvW8V8~VRd76p4rEpmNBw__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA",
        labelCopy: "Fragrance"
      }],
      loading: [{}, {}, {}, {}]
    },
    chips: {
      default: [{
        id: 1,
        text: "Alcohol",
        isSelected: false
      }, {
        id: 2,
        text: "Clothing",
        isSelected: false
      }, {
        id: 3,
        text: "Beauty",
        isSelected: false
      }, {
        id: 4,
        text: "Electronices",
        isSelected: false
      }],
      loading: [{
        id: 1
      }, {
        id: 2
      }, {
        id: 3
      }, {
        id: 4
      }]
    },
    mainPromo: {
      default: [{
        orderId: 1,
        backgroundImageUrl: "https://s3-alpha-sig.figma.com/img/9074/30ac/50a8dd9458bbc627ad3dd7d305075bc5?Expires=1624233600&Signature=XeBSLNafeGP1hxxZbMyWyJevpZMvDaAqNbOl6DlP-EjAhJquJkQ3SpeWCHMR1flbE4W-FYKDtwQVJyX4FS1hSpbysIJgQZLlUs~SaParq3L87TAlqXAXMeHvfd4VIGNDfKxp8iyLXpquA0p08DoxeLa4MWpCOx~n0nMDChZ1Zg4GDHltsCCAe~-YgH536FN7Jk0xSv0wt6BkKEulLDbm06fWuSh-QKnSr0hV~hOXlg-BeTxC7O-8byPyrP2iOTvQZ7ubNMSXxe7JJM06BCl4B5JPWJ24Fct~wy776sDVO4DSqguDiDQ8AmIpJhOtwamoeSxE6brwQcLLUA2EUEVC7Q__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA",
        title: "SHOP DUTY-FREE ONLINE",
        subcopy: "Enjoy tax-absorbed shopping without flying!"
      }, {
        orderId: 3,
        backgroundImageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/Main+promo/Main-promo-2.jpg",
        title: "Large prawn noodle",
        subcopy: "Valid till end of June"
      }, {
        orderId: 2,
        backgroundImageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/Main+promo/Main-promo-3.jpg",
        title: "Weekday tea at Tonito",
        subcopy: "Valid till Aug 2021"
      }],
      loading: [{}, {}, {}, {}]
    },
    recommendedForYou: {
      vouchers: {
        default: [{
          id: 1,
          imageUrl: "https://s3-alpha-sig.figma.com/img/da8a/272f/b0ab1901698ea1f2423a7a5addb08257?Expires=1624233600&Signature=QphyEHHyuFsm~8TQMMxW-SvZrzB802sYWTYhBHWzYZfGi8HO2gszexjDf2QRkcXUEaaosZOutOuzIUh~VSednNlqW7waxTWCkPAMIdmaCjv0o~-hxkquYqzNTURa-ec25G022hg4DcLcDpp0gULLzwUapN67alMgZmRIqZ2IMtLhR4nVTckNrJWwGGztMPdS33rmCZKj8zbLq~1TC8iUjQ1KohT1SuZeYESgaxA0FtkY6HBHjNat8dU7jrPA1B-4KGTsHI3OG4p3sCfU4M-1xUjoF9V9kwNSBDk0ZKoaL48VC4TkKznrEJRbJuxQ9WYRbLB3KcmFyDqcq0NUguvxQw__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA",
          tenantName: "L’ÉCLAIR",
          offerTitleDescription: "5% OFF NDP Gift Box",
          validTillDate: "Valid till 31 Aug 2021"
        }, {
          id: 2,
          imageUrl: "https://s3-alpha-sig.figma.com/img/d605/d703/e2ea0545ec5210a9de48466ac938ff16?Expires=1624233600&Signature=TS6aA553VdBChqFrHjqfW5z1FQ31s9ic-I6w-UWfenyZFloqto-Nl-1-ro-E0JSFM6DQW~b6Z0kiYHaWiLVlCTkaKCXkes92jHqyebW~6iEyYkP8hJKi~5YY0tIB~jF-pr~-lgDhpBkU1vfp3NP4ZNHajwmC6sPpsUkDGYIlAxaiaS-QS9WFtogGs4UUUtdIC2Cru-VJSFl1E~MTTOlsUVME8IMMA-Igr1RLzRLo8Xd0yRNMPq9PnHh-nlT3iSjk3XJWZJ2MoQKRFoQzWx4DqLuSY~F7vJ~f7xN-HTg84QBsn4CfHp-UJwaaHKW0tXw1XfCX0xzhGpq9JIheKtOI9g__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA",
          tenantName: "Offer title 1 line only",
          offerTitleDescription: "Weekday lunch at Tonito",
          validTillDate: "Valid till 31 Aug 2021"
        }, {
          id: 3,
          imageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/Vouchers/bobbi-brown.png",
          tenantName: "Offer title 1 line only",
          offerTitleDescription: "Weekday lunch at Tonito",
          validTillDate: "Valid till 31 Aug 2021"
        }, {
          id: 4,
          imageUrl: "https://s3-alpha-sig.figma.com/img/5657/a20a/cc64fdf91f2e2edcfcd3a58e23c2372b?Expires=1624233600&Signature=BczUf-uH8cPs2~5ZzCyzrK2vZwildzM9hwvQmB856m8umbcGIqF3bD7N7ABA6diqsiACm0C~ct7Q4Hshb9maVO~FgadjZSxPVwizbWd0Ok3DwXttCZmue75cdLhCimyvlrVYwCSi9scyhd5mWXNWWIwxrBEAa7JqsuybD3HpjCGUJt0hjoFPH1XdIdmKqYRLAb-fdaVI1LCitRLYnGrIxp5epVrY8s6rAuhYOcn9Fxb7i6Uxm6HZVJdmB3-QT-r-VsPy3A5Uazrn9YhRLuqGmsT6T2xI5Fxsx3YFqhT0xqARkLgRyBwOT17MC1JA7HiTgd5BNdAwl9TLkeMwDGTHbQ__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA",
          tenantName: "Brand name",
          offerTitleDescription: "Offer title 1 line only",
          validTillDate: "Valid till 30 Nov 2021"
        }],
        loading: [{}, {}, {}]
      },
      tenantListing: {
        default: [{
          imageUrlArray: [],
          logoUrl: "https://s3-alpha-sig.figma.com/img/4705/2ce3/35daa882b66832ac3c71080635f4c935?Expires=1624233600&Signature=UWpmBP5WnrEqM8E2wNRHeDj5aSIf6zHwjAkuUjMtiNd-~Hvu9KvzRoi~pc3T8z-W4v8DlGEsaMKEEt~Io4ruBvlSoKVptOOCfuhS~tY-Ei5X24on7viLyonrTZH8WoMujp32p48VMlqJ~E94p0z70z2Uv1Z7Aq7TxqVTknNBze9A9W8xQFE5f2eXjaS9BQQ3L0UuQDbHhHYHAutSaYare5lyg0L1fIlSAHOYs56f1AE4eo0ilQhjNCM85OuEODIn9TWP~oh2irSz6Ke0YV0m7OVlz~G8HPEBlzIYXzcWsG8lmcD7Q9a75cqwioX~kRaKRSoVSqIIknmeFaDNP5qARg__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA",
          tenantName: "1855 The Bottle Shop",
          location: "Jewel L2 · Alcohol, Wine · $$",
          rewardTitle: "2 x Changi Reward Points"
        }, {
          imageUrlArray: [],
          logoUrl: "https://s3-alpha-sig.figma.com/img/ca73/1b24/a7fddcb6b926985764d854dbc05efef8?Expires=1624233600&Signature=d~ClCEHvxpIPuMk9RFojiQXnzUL8bPsG1KmZCX3Dm6nHndLIn1FlHnuSKAb-apLctzGHEkAdCzs7zCXJYGsiecyHsNP43F8-Rl1POiIAC4eJVLFQ1-6qiYbxFro4ShMNuh2Ns-UW7IBp93Dh29HQqozZ6V~aY5xVEYrHnNBZakiNDLyHeQKu2nF5Y2s~ZZiNLLGBblxemz27rvVxcNVgAolBuG~nBfpqifOMfdqqXfSsm0YUuUjSFK-7N3C5faSp8Iyn6w9dQS5FtG1pgVbCM~J5M14CWIJyYmS1EaYKg-ojDFx62355lKnjQ3aUooj4JaMaoH8UbDjaOhjdZKLZjQ__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA",
          tenantName: "Candy Empire",
          location: "Jewel L3 · Sweets · $",
          rewardTitle: "2 x Changi Reward Points"
        }, {
          imageUrlArray: [],
          logoUrl: "https://s3-alpha-sig.figma.com/img/a891/fb41/943f9eb61777fc5a2d32d56b895224c3?Expires=1624233600&Signature=RTqLogvApXglkgNjfSmS2YI5MWLlJM4TeIYI1~r3egloJhk8EziWPMBMY3bQCyDMiWz6Ctj~oJxIrFENXNmCiXu-dw6O2XBWWTMCHPtDxCaK7W9Z6ngp22QSGTAL~~qNqUlqxkRb3ECHWanjyUKPjOlcKaq4pRzJPARJlIqsoHbvYsW4JMFINs2etHhSLsiQAYw4KTUGJ4HUwrROFvN9nckoYRsZPoCM~JZD3U74Mv5HLZ3uNmqR4uaqUX7HkGm8pgWUbmbmUbMH1jOqbnTtEmw0an1Sj-ZVF2z9R4U~Y-XF~EKkxaQ~VCOv6mk~FDcbWzF2VbT0rANzwPjt9Gqw1w__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA",
          tenantName: "SK Jewellery",
          location: "Jewel B1 · Jewellery · $$",
          rewardTitle: "2 x Changi Reward Points"
        }],
        loading: [{}, {}, {}]
      }
    },
    notToBeMissed: {
      default: [{
        offerText: "PROMO",
        tenantName: "TENANT NAME",
        offerTitle: "Offer title",
        validityDate: "Valid till 31 Aug 2021",
        freeText: "",
        remainingQuantity: "",
        totalQuantity: "",
        imageUrl: "https://sg-cag-images.s3.ap-southeast-1.amazonaws.com/Components/Masonry+listing/g-star-01.jpg"
      }, {
        offerText: "PROMO",
        tenantName: "TENANT NAME",
        offerTitle: "Offer title",
        validityDate: "Valid till 31 Aug 2021",
        freeText: "",
        remainingQuantity: "",
        totalQuantity: "",
        imageUrl: "https://sg-cag-images.s3.ap-southeast-1.amazonaws.com/Components/Masonry+listing/g-star-01.jpg"
      }],
      loading: [{}, {}, {}]
    },
    masonryListing: {
      default: [{
        id: 1,
        imageUrl: "https://sg-cag-images.s3.ap-southeast-1.amazonaws.com/Components/Masonry+listing/ningspa.jpg",
        titleName: "Ningspa",
        tenantType: _masonryListing.MasonryListingTenantType.short,
        location: "Jewel, L4"
      }, {
        id: 2,
        imageUrl: "https://sg-cag-images.s3.ap-southeast-1.amazonaws.com/Components/Masonry+listing/adidas-01.jpeg",
        titleName: "Adidas",
        tenantType: _masonryListing.MasonryListingTenantType.long,
        location: "Jewel, L"
      }, {
        id: 3,
        imageUrl: "https://sg-cag-images.s3.ap-southeast-1.amazonaws.com/Components/Masonry+listing/American-tourister-01.jpeg",
        titleName: "American Tourister",
        tenantType: _masonryListing.MasonryListingTenantType.short,
        location: "Jewel, L4"
      }, {
        id: 4,
        imageUrl: "https://sg-cag-images.s3.ap-southeast-1.amazonaws.com/Components/Masonry+listing/g-star-01.jpg",
        titleName: "G-star",
        tenantType: _masonryListing.MasonryListingTenantType.short,
        location: "Jewel, L2"
      }, {
        id: 5,
        imageUrl: "https://sg-cag-images.s3.ap-southeast-1.amazonaws.com/Components/Masonry+listing/obermain.jpg",
        titleName: "Obermain",
        tenantType: _masonryListing.MasonryListingTenantType.long,
        location: "Jewel, L4"
      }, {
        id: 6,
        imageUrl: "https://sg-cag-images.s3.ap-southeast-1.amazonaws.com/Components/Masonry+listing/orient-crown-01.jpeg",
        titleName: "Orient-crown",
        tenantType: _masonryListing.MasonryListingTenantType.short,
        location: "Jewel, L2"
      }],
      loading: [{
        id: 1
      }, {
        id: 2
      }, {
        id: 3
      }, {
        id: 4
      }, {
        id: 5
      }, {
        id: 6
      }]
    },
    tenantOffer: {
      default: [{
        offerText: "Ribbon Title",
        offerMechanic: _tenantOffer.TenantOfferMechanicEnum.freeText,
        tenantName: "Tenant Name",
        offerTitle: "Offer description to go here",
        imageUrl: "https://sg-cag-images.s3.ap-southeast-1.amazonaws.com/Components/Tenant+offer/onitsuka-tiger-offer.jpeg"
      }, {
        offerText: "Ribbon Title",
        offerMechanic: _tenantOffer.TenantOfferMechanicEnum.freeText,
        tenantName: "Tenant Name",
        offerTitle: "Offer description to go here",
        remainingQuantity: 42,
        imageUrl: "https://sg-cag-images.s3.ap-southeast-1.amazonaws.com/Components/Tenant+offer/l-eclair-offer.jpeg"
      }, {
        offerText: "Ribbon Title",
        offerMechanic: _tenantOffer.TenantOfferMechanicEnum.freeText,
        tenantName: "Tenant Name",
        offerTitle: "Offer description to go here",
        imageUrl: "https://sg-cag-images.s3.ap-southeast-1.amazonaws.com/Components/Tenant+offer/ishop-offer.jpg"
      }],
      loading: [{}, {}, {}]
    },
    brandOffer: {
      specificBrandData: {
        specificBrandLogo: "https://s3-alpha-sig.figma.com/img/65ce/0f79/f98dff967b3f563ed6084c92635e0b0e?Expires=1624838400&Signature=SFWRF5OOCw25UkqkdzQIwQJetuF3G1wN~bEjMlThM0BhiVfu3sdnH7hRqmnKl3BLloMZMi1iardy13i4xpusxthBSXqomFeuA10mWaPEEHdiLtaln1bpMDGVaX3o~7BjoiJtT9-GB3gEI5NKqN6IPZn5oZm5CrUQRdZptJqLOzALTYSJeGDYp9B-uI0h4fWG61Cvc7fJ3tgkdTBPO39appSJ2skKyOYFu1ZCMxGZg1u010E3lluWxc4YKyau3SvVmP6HxcfzgprED36AZ1n06OAijI-wUhrvJEMKz5PWUTsclUguhQZYOARpXZwwDfs-MGVt3-VfWkvnKO67L~MfZw__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA",
        title: "Nike end of season sale",
        subTitle: "All shoes and the Graphic T’s collection"
      },
      default: [{
        id: 1,
        imageUrl: "https://s3-alpha-sig.figma.com/img/19ea/8982/a525db0b321b747ab95aaf8bd6343901?Expires=1624838400&Signature=WdEg--Q9Wltd4ikDSk4I6ztrmj9PtjQiWxyj1tsY~0929Ane9DHS5Eoyfj-D7XVqpuLXQuhBDOq-9-MMA1XSz90xs1YK766dZtA-b6yx3SVYfLuNWMO42UQvOkcy2vqqpLTIk1GZgzxIR6fBWCY94x2VJsiy9lrEHiQVKEkZMH8rCHikQ1EVdwzM5rOGt~01QBmIyDa8jh~qrSj6y1A9rXR0Me-nFKjd3mZ4JEk3z8ZgAjOn88UG0qx5rrzyFUXeyQSKjcF-yfkj6kHSHlP7LL4juNYO9jTQ-~6T~eSRTXsJQe19kXmSqsZSZMTDKMGescwFBB6b1eQ0EaFu-R0zrw__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA",
        tenantName: "",
        ribbonText: "28% OFF",
        productName: "Sportswear T-Shirt",
        salePrice: 29.9,
        originalPrice: 42.0
      }, {
        id: 2,
        imageUrl: "https://s3-alpha-sig.figma.com/img/e7bc/ca50/1b0e0ff4be9b92b30bdea0b1200ee3c1?Expires=1624838400&Signature=dZ57EI9fCVwT9ubZdQrLAcJWenvkZBPxN51-ZAaHBM4jUMNldlykSfmdazwfK5rEPsXayT9s23UrdcDKTKmphYJW4~gmR0FlChSuKxutL1mXdGlUVail4wJtbyMwxuVXS5j785ZA8bvra6RrhxNn3iX~SVo1vSKGcwzlSpePnlRQlm2wXB0p6zVHIHSjUlenMk8GScu7OGi7UlBV8dQrUq5onvAGmfD9wxyhLvX1~0KL2SyMNQFr7BwXvqwncgseY~rGNRPpJfviQa663ubvO-GtT~JSKyb7adaMo3MI02WpSDc0txM6KXGKiTHZ3kTeghqGUPCZpz8iM61ADuZCZA__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA",
        tenantName: "",
        ribbonText: "28% OFF",
        productName: "High-vis TechKnit Ultra Top",
        salePrice: 59.0,
        originalPrice: 89.0
      }, {
        id: 3,
        imageUrl: "https://s3-alpha-sig.figma.com/img/19ea/8982/a525db0b321b747ab95aaf8bd6343901?Expires=1624838400&Signature=WdEg--Q9Wltd4ikDSk4I6ztrmj9PtjQiWxyj1tsY~0929Ane9DHS5Eoyfj-D7XVqpuLXQuhBDOq-9-MMA1XSz90xs1YK766dZtA-b6yx3SVYfLuNWMO42UQvOkcy2vqqpLTIk1GZgzxIR6fBWCY94x2VJsiy9lrEHiQVKEkZMH8rCHikQ1EVdwzM5rOGt~01QBmIyDa8jh~qrSj6y1A9rXR0Me-nFKjd3mZ4JEk3z8ZgAjOn88UG0qx5rrzyFUXeyQSKjcF-yfkj6kHSHlP7LL4juNYO9jTQ-~6T~eSRTXsJQe19kXmSqsZSZMTDKMGescwFBB6b1eQ0EaFu-R0zrw__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA",
        tenantName: "",
        ribbonText: "28% OFF",
        productName: "High-vis TechKnit Ultra Top",
        salePrice: 59.0,
        originalPrice: 89.0
      }, {
        id: 4,
        imageUrl: "https://s3-alpha-sig.figma.com/img/e7bc/ca50/1b0e0ff4be9b92b30bdea0b1200ee3c1?Expires=1624838400&Signature=dZ57EI9fCVwT9ubZdQrLAcJWenvkZBPxN51-ZAaHBM4jUMNldlykSfmdazwfK5rEPsXayT9s23UrdcDKTKmphYJW4~gmR0FlChSuKxutL1mXdGlUVail4wJtbyMwxuVXS5j785ZA8bvra6RrhxNn3iX~SVo1vSKGcwzlSpePnlRQlm2wXB0p6zVHIHSjUlenMk8GScu7OGi7UlBV8dQrUq5onvAGmfD9wxyhLvX1~0KL2SyMNQFr7BwXvqwncgseY~rGNRPpJfviQa663ubvO-GtT~JSKyb7adaMo3MI02WpSDc0txM6KXGKiTHZ3kTeghqGUPCZpz8iM61ADuZCZA__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA",
        tenantName: "",
        ribbonText: "28% OFF",
        productName: "High-vis TechKnit Ultra Top",
        salePrice: 59.0,
        originalPrice: 89.0
      }],
      loading: [{}, {}, {}]
    }
  };
  var DineHeroData = exports.DineHeroData = {
    heroImagesUrl: [{
      orderId: 1,
      backgroundImageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/Hero+image/burger-lobster-01.jpeg"
    }, {
      orderId: 2,
      backgroundImageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/Hero+image/burgerlobster-hero.jpg"
    }, {
      orderId: 3,
      backgroundImageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/Hero+image/burger-lobster-01.jpeg"
    }, {
      orderId: 4,
      backgroundImageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/Hero+image/burgerlobster-hero.jpg"
    }, {
      orderId: 5,
      backgroundImageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/Hero+image/burger-lobster-01.jpeg"
    }],
    logoImageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Logos/Restaurant+logos/Burger_Lobster-logo.png",
    type: "default"
  };
  var shopDetailsPayload = exports.shopDetailsPayload = {
    heroData: {
      heroImages: [{
        orderId: 1,
        backgroundImageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/Hero+image/burger-lobster-01.jpeg"
      }, {
        orderId: 2,
        backgroundImageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/Hero+image/burgerlobster-hero.jpg"
      }, {
        orderId: 3,
        backgroundImageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/Hero+image/burger-lobster-01.jpeg"
      }, {
        orderId: 4,
        backgroundImageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/Hero+image/burgerlobster-hero.jpg"
      }, {
        orderId: 5,
        backgroundImageUrl: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Components/Hero+image/burger-lobster-01.jpeg"
      }],
      heroLogo: "https://sg-cag-images.s3-ap-southeast-1.amazonaws.com/Logos/Restaurant+logos/Burger_Lobster-logo.png"
    }
  };
