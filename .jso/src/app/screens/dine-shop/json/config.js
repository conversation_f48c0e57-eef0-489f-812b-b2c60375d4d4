  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.pageLayoutComponentStructureForShop = exports.pageLayoutComponentStructureForDine = undefined;
  var _chipFilter = _$$_REQUIRE(_dependencyMap[0]);
  var _contentGuide = _$$_REQUIRE(_dependencyMap[1]);
  var _cuisineCategory = _$$_REQUIRE(_dependencyMap[2]);
  var _instagramTrending = _$$_REQUIRE(_dependencyMap[3]);
  var _masonryListing = _$$_REQUIRE(_dependencyMap[4]);
  var _productOffer = _$$_REQUIRE(_dependencyMap[5]);
  var _tenantListingHorizontal = _$$_REQUIRE(_dependencyMap[6]);
  var _tenantOffer = _$$_REQUIRE(_dependencyMap[7]);
  var _tenant = _$$_REQUIRE(_dependencyMap[8]);
  var _i18n = _$$_REQUIRE(_dependencyMap[9]);
  var _sectionsData = _$$_REQUIRE(_dependencyMap[10]);
  var MainPromoType = /*#__PURE__*/function (MainPromoType) {
    MainPromoType["default"] = "default";
    MainPromoType["loading"] = "loading";
    return MainPromoType;
  }(MainPromoType || {});
  var pageLayoutComponentStructureForDine = exports.pageLayoutComponentStructureForDine = {
    name: "view",
    _uid: "1",
    children: [
    // This is the default structure to load the children components or sections
    {
      name: "chipFilter",
      _uid: "1",
      props: {
        type: _chipFilter.ChipFilterType.loading,
        screen: "DINE"
      }
    }, {
      name: "mainPromo",
      _uid: "2",
      props: {
        type: MainPromoType.loading,
        promotions: _sectionsData.sectionsPayloadForDine.mainPromo.loading,
        screen: "DINE"
      }
    }, {
      name: "newlyOpened",
      _uid: "3",
      props: {
        type: _tenant.TenantType.loading,
        screen: "DINE",
        title: (0, _i18n.translate)("dineScreen.newlyOpened")
      }
    }, {
      name: "recommendedForYou",
      _uid: "4",
      props: {
        voucherData: _sectionsData.sectionsPayloadForDine.recommendedForYou.vouchers.loading,
        tenantData: _sectionsData.sectionsPayloadForDine.recommendedForYou.tenantListing.loading,
        type: _tenantListingHorizontal.TenantListingHorizontalType.loading,
        title: (0, _i18n.translate)("dineScreen.recommendedForYou"),
        optionalTitle: (0, _i18n.translate)("dineScreen.mostPopularAtChange"),
        screen: "DINE"
      }
    }, {
      name: "contentGuide",
      _uid: "5",
      props: {
        type: _contentGuide.ContentGuideType.loading,
        title: (0, _i18n.translate)("dineScreen.contentGuide")
      }
    }, {
      name: "instagramTrending",
      _uid: "6",
      props: {
        type: _instagramTrending.InstagramTrendingType.loading,
        title: (0, _i18n.translate)("dineScreen.seeWhatsTrending"),
        imageUrls: _sectionsData.sectionsPayloadForDine.instagram.default.imageUrls,
        hashtags: _sectionsData.sectionsPayloadForDine.instagram.default.hashtags
      }
    }, {
      name: "specialPromo",
      _uid: "7",
      props: {
        type: MainPromoType.loading,
        promotions: _sectionsData.sectionsPayloadForDine.mainPromo.loading
      }
    }, {
      name: "exploreMore",
      _uid: "8",
      props: {
        type: _tenantListingHorizontal.TenantListingHorizontalType.loading,
        data: _sectionsData.sectionsPayloadForDine.exploreMore.loading,
        title: (0, _i18n.translate)("dineScreen.exploreMore")
      }
    }, {
      name: "cuisine",
      _uid: "9",
      props: {
        title: (0, _i18n.translate)("dineScreen.cuisineSection"),
        type: _cuisineCategory.CuisineCategoryType.loading,
        data: _sectionsData.sectionsPayloadForDine.cuisine.loading,
        screen: "DINE"
      }
    }]
  };
  var pageLayoutComponentStructureForShop = exports.pageLayoutComponentStructureForShop = {
    name: "view",
    _uid: "1",
    children: [
    // This is the default structure to load the children components or sections
    {
      name: "chipFilter",
      _uid: "1",
      props: {
        type: _chipFilter.ChipFilterType.loading,
        data: _sectionsData.sectionsPayloadForShop.chips.loading,
        screen: "SHOP"
      }
    }, {
      name: "mainPromo",
      _uid: "2",
      props: {
        type: MainPromoType.loading,
        promotions: _sectionsData.sectionsPayloadForShop.mainPromo.loading
      }
    }, {
      name: "notToBeMissed",
      _uid: "3",
      props: {
        title: (0, _i18n.translate)("shopScreen.notToBeMissed"),
        offerMechanic: _tenantOffer.TenantOfferMechanicEnum.cta,
        type: _tenantOffer.TenantTypeEnum.loading
      }
    }, {
      name: "brandOffer",
      _uid: "4",
      props: {
        type: _productOffer.ProductOfferType.loading
      }
    }, {
      name: "recommendedForYou",
      _uid: "5",
      props: {
        voucherData: _sectionsData.sectionsPayloadForShop.recommendedForYou.vouchers.loading,
        tenantData: _sectionsData.sectionsPayloadForShop.recommendedForYou.tenantListing.loading,
        type: _tenantListingHorizontal.TenantListingHorizontalType.loading,
        title: (0, _i18n.translate)("dineScreen.recommendedForYou"),
        loggedOutTitle: (0, _i18n.translate)("dineScreen.mostPopularAtChange"),
        screen: "SHOP"
      }
    }, {
      name: "shopAndSpin",
      _uid: "6",
      props: {
        titleText: "Spin to win S$100,000 worth of sure-win prizes when you spend S$55*",
        logo: "https://sg-cag-images.s3.ap-southeast-1.amazonaws.com/Logos/Shop+logos/puma.jpg",
        type: _tenantOffer.TenantTypeEnum.loading,
        data: _sectionsData.sectionsPayloadForShop.tenantOffer.loading,
        title: (0, _i18n.translate)("shopScreen.shopAndSpin")
      }
    }, {
      name: "newlyOpened",
      _uid: "7",
      props: {
        screen: "SHOP",
        title: (0, _i18n.translate)("shopScreen.newlyOpened")
      }
    }, {
      name: "youMayAlsoLike",
      _uid: "8",
      props: {
        title: (0, _i18n.translate)("shopScreen.youMayAlsoLike"),
        type: _masonryListing.MasonryListingState.loading,
        data: _sectionsData.sectionsPayloadForShop.masonryListing.loading
      }
    }, {
      name: "exploreShopAtChangi",
      _uid: "9",
      props: {
        title: (0, _i18n.translate)("shopScreen.exploreShopsAtChangiSection"),
        type: _cuisineCategory.CuisineCategoryType.loading,
        data: _sectionsData.sectionsPayloadForShop.cuisine.loading,
        screen: "SHOP"
      }
    }]
  };
