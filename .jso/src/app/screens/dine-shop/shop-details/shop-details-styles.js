  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.wrapInformationDetail = exports.webIconStyle = exports.visibilityStyle = exports.viewContainerStyle = exports.txtColor = exports.titleText = exports.timingsText = exports.timingsInfoViewStyle = exports.timingsInfoDayText = exports.timingStyle = exports.timingContainerStyle = exports.text = exports.temporaryClosedStyle = exports.tagHorizonralStyle = exports.statusTextStyle = exports.skeletonLayout = exports.shareButtonStyle = exports.separatorNotExpanded = exports.separatorExpanded = exports.rewardTitleStyle = exports.rewardContainer = exports.phoneNumberTextStyleAndroid = exports.phoneNumberTextStyle = exports.phoneIconStyle = exports.phoneContainerStyle = exports.pageHeaderStyle = exports.loadMoreShowLessView = exports.loadMoreShowLessTextStyle = exports.loadMoreShowLessIconStyle = exports.linkView = exports.linkTestStyleAndroid = exports.linkTestStyle = exports.lighterGreyLoadingColors = exports.iscCTAText = exports.iscCTAButton = exports.infoBannerView = exports.headerViewStyle = exports.headerStyle = exports.header = exports.getDirectionStyleDisabled = exports.getDirectionStyle = exports.flexStyle = exports.dotUnicodeStyle = exports.directionTouchableOpacityStyle = exports.directionTextStyleAndroidDisabled = exports.directionTextStyleAndroid = exports.directionIconStyle = exports.directionContainerView = exports.ctaButtonStyle = exports.content = exports.container = exports.clockIconStyle = exports.buttonView = exports.backButtonStyle = exports.arrowIconStyle = exports.areaInfoViewStyle = exports.aboutViewTextAndroid = exports.aboutViewText = exports.aboutViewStyle = exports.aboutViewHeaderText = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNativeSizeMatters = _$$_REQUIRE(_dependencyMap[2]);
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var lighterGreyLoadingColors = exports.lighterGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var skeletonLayout = exports.skeletonLayout = [{
    width: (0, _reactNativeSizeMatters.scale)(250),
    height: 36,
    marginTop: 23,
    borderRadius: 4,
    marginBottom: 12,
    marginRight: 24
  }, {
    width: (0, _reactNativeSizeMatters.scale)(50),
    height: 30,
    borderRadius: 8,
    marginRight: 12,
    marginBottom: 22
  }, {
    width: (0, _reactNativeSizeMatters.scale)(310),
    height: 36,
    marginBottom: 26,
    marginRight: 24,
    borderRadius: 8
  }, {
    width: 20,
    height: 20,
    borderRadius: 16,
    marginRight: 17,
    marginBottom: 22
  }, {
    width: (0, _reactNativeSizeMatters.scale)(265),
    height: 18,
    borderRadius: 4,
    marginRight: 12,
    marginBottom: 6
  }, {
    width: (0, _reactNativeSizeMatters.scale)(100),
    height: 18,
    borderRadius: 4,
    marginLeft: 36,
    bottom: 18
  }, {
    width: (0, _reactNativeSizeMatters.scale)(100),
    height: 18,
    borderRadius: 4,
    marginRight: 12,
    marginBottom: 6,
    top: 1
  }, {
    width: (0, _reactNativeSizeMatters.scale)(100),
    height: 18,
    borderRadius: 4,
    marginLeft: 50
  }, {
    width: (0, _reactNativeSizeMatters.scale)(310),
    height: 36,
    marginBottom: 26,
    marginRight: 24,
    borderRadius: 16
  }];
  var viewContainerStyle = exports.viewContainerStyle = [{
    marginLeft: 24,
    marginBottom: 5
  }, {
    flexDirection: "row"
  }, {
    flexDirection: "row",
    marginLeft: 36,
    bottom: 15
  }, {
    flexDirection: "row",
    marginLeft: 36,
    bottom: 13
  }, {
    flexDirection: "row",
    marginLeft: 36,
    bottom: 11,
    marginBottom: 24
  }, {
    flexDirection: "row",
    marginBottom: 24
  }, {
    marginBottom: 24
  }];
  var titleText = exports.titleText = {
    color: _theme.color.palette.almostBlackGrey,
    fontWeight: _reactNative.Platform.OS === "android" ? "normal" : "700",
    marginLeft: 24
  };
  var wrapInformationDetail = exports.wrapInformationDetail = {
    marginHorizontal: 24
  };
  var header = exports.header = {
    flex: 1,
    backgroundColor: _theme.color.palette.whiteGrey,
    marginTop: 22,
    marginBottom: 24
  };
  var separatorExpanded = exports.separatorExpanded = {
    height: 1,
    backgroundColor: _theme.color.palette.lighterGrey,
    marginTop: 24
  };
  var separatorNotExpanded = exports.separatorNotExpanded = Object.assign({}, separatorExpanded, {
    marginTop: 0
  });
  var text = exports.text = {
    color: _theme.color.palette.darkestGrey,
    marginStart: 15
  };
  var timingsInfoDayText = exports.timingsInfoDayText = Object.assign({}, text, {
    flex: 0.45
  });
  var timingsText = exports.timingsText = Object.assign({}, timingsInfoDayText, {
    flex: 0.55,
    textAlign: "left"
  });
  var content = exports.content = {
    backgroundColor: _theme.color.palette.whiteGrey
  };
  var pageHeaderStyle = exports.pageHeaderStyle = {
    marginLeft: 24
  };
  var tagHorizonralStyle = exports.tagHorizonralStyle = {
    paddingLeft: 24
  };
  var rewardContainer = exports.rewardContainer = {
    marginTop: 12,
    borderWidth: 1,
    borderColor: _theme.color.palette.lightestPurple,
    backgroundColor: _theme.color.palette.whiteGrey,
    borderRadius: 8,
    height: 30,
    marginRight: 8,
    justifyContent: "center",
    alignItems: "center"
  };
  var rewardTitleStyle = exports.rewardTitleStyle = {
    color: _theme.color.palette.lightPurple,
    paddingHorizontal: 12,
    fontWeight: _reactNative.Platform.OS === "android" ? "normal" : "700"
  };
  var flexStyle = exports.flexStyle = {
    flexDirection: "row"
  };
  var headerStyle = exports.headerStyle = Object.assign({}, flexStyle, {
    flex: 1
  });
  var txtColor = exports.txtColor = {
    color: _theme.color.palette.almostBlackGrey
  };
  var directionIconStyle = exports.directionIconStyle = {
    // alignSelf: "center",
  };
  var areaInfoViewStyle = exports.areaInfoViewStyle = {
    flex: 1
  };
  var directionContainerView = exports.directionContainerView = {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 5
  };
  var directionTouchableOpacityStyle = exports.directionTouchableOpacityStyle = {
    marginLeft: 52,
    display: "none"
  };
  var directionTextStyleAndroid = exports.directionTextStyleAndroid = {
    fontWeight: "normal"
  };
  var directionTextStyleAndroidDisabled = exports.directionTextStyleAndroidDisabled = {
    fontWeight: "normal",
    color: _theme.color.palette.almostBlackGrey
  };
  var phoneNumberTextStyle = exports.phoneNumberTextStyle = {
    flex: 1
  };
  var phoneNumberTextStyleAndroid = exports.phoneNumberTextStyleAndroid = Object.assign({}, phoneNumberTextStyle, {
    fontWeight: "normal"
  });
  var phoneContainerStyle = exports.phoneContainerStyle = {
    marginTop: 24,
    flexDirection: "row",
    width: "100%"
  };
  var phoneIconStyle = exports.phoneIconStyle = {
    marginRight: 15
  };
  var clockIconStyle = exports.clockIconStyle = {};
  var webIconStyle = exports.webIconStyle = {
    marginEnd: 15
  };
  var timingsInfoViewStyle = exports.timingsInfoViewStyle = {
    flex: 1
  };
  var statusTextStyle = exports.statusTextStyle = {
    marginTop: 2
  };
  var visibilityStyle = exports.visibilityStyle = {
    overflow: "hidden"
  };
  var ctaButtonStyle = exports.ctaButtonStyle = {
    justifyContent: "center",
    height: 44
  };
  var arrowIconStyle = exports.arrowIconStyle = {
    alignItems: "flex-end"
  };
  var timingStyle = exports.timingStyle = {
    flexDirection: "row"
  };
  var timingContainerStyle = exports.timingContainerStyle = {
    marginTop: 24,
    flexDirection: "row",
    flex: 1
  };
  var linkView = exports.linkView = {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 24
  };
  var dotUnicodeStyle = exports.dotUnicodeStyle = {
    color: _theme.color.palette.darkestGrey
  };
  var linkTestStyle = exports.linkTestStyle = {
    marginLeft: 5
  };
  var linkTestStyleAndroid = exports.linkTestStyleAndroid = Object.assign({}, linkTestStyle, {
    fontWeight: "normal"
  });
  var buttonView = exports.buttonView = {
    paddingBottom: 24,
    paddingHorizontal: 16
  };
  var container = exports.container = {
    flex: 1,
    backgroundColor: _theme.color.palette.whiteGrey
  };
  var headerViewStyle = exports.headerViewStyle = {
    position: "absolute",
    zIndex: 1,
    width: "100%",
    marginTop: 24,
    flexDirection: "row"
  };
  var backButtonStyle = exports.backButtonStyle = {
    flex: 1,
    marginStart: 14
  };
  var shareButtonStyle = exports.shareButtonStyle = {
    flex: 1,
    alignItems: "flex-end",
    marginEnd: 14
  };
  var aboutViewStyle = exports.aboutViewStyle = {
    marginTop: 40,
    marginStart: 23,
    marginEnd: 25,
    marginBottom: 19
  };
  var aboutViewHeaderText = exports.aboutViewHeaderText = {
    paddingBottom: 12
  };
  var aboutViewText = exports.aboutViewText = {
    color: _theme.color.palette.darkestGrey,
    lineHeight: 24
  };
  var aboutViewTextAndroid = exports.aboutViewTextAndroid = Object.assign({}, aboutViewText, {
    fontWeight: "normal"
  });
  var loadMoreShowLessView = exports.loadMoreShowLessView = {
    flexDirection: "row"
  };
  var loadMoreShowLessTextStyle = exports.loadMoreShowLessTextStyle = {
    color: "#7A35B0",
    marginTop: 13
  };
  var loadMoreShowLessIconStyle = exports.loadMoreShowLessIconStyle = {
    marginLeft: 10,
    top: 13
  };
  var infoBannerView = exports.infoBannerView = {
    marginLeft: 24,
    marginRight: 24,
    marginTop: 24
  };
  var getDirectionStyle = exports.getDirectionStyle = {
    color: _theme.color.palette.lightPurple,
    paddingVertical: 8,
    marginLeft: 15
  };
  var getDirectionStyleDisabled = exports.getDirectionStyleDisabled = {
    color: "#ccc",
    paddingVertical: 8,
    marginLeft: 15
  };
  var temporaryClosedStyle = exports.temporaryClosedStyle = {
    fontSize: 16,
    fontFamily: _theme.typography.bold,
    fontWeight: _reactNative.Platform.select({
      ios: '700',
      android: 'normal'
    }),
    lineHeight: 20,
    marginBottom: 24
  };
  var iscCTAButton = exports.iscCTAButton = {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: _theme.color.palette.lightPurple,
    borderWidth: 2,
    borderRadius: 60,
    gap: 12,
    paddingVertical: 10,
    marginHorizontal: 24,
    marginTop: 24
  };
  var iscCTAText = exports.iscCTAText = Object.assign({}, _text.newPresets.bodyTextBold, {
    color: _theme.color.palette.lightPurple
  });
