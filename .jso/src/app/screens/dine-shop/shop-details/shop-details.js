  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ShopDetailsScreen = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _shopDetailsLoadingView = _$$_REQUIRE(_dependencyMap[8]);
  var _hero = _$$_REQUIRE(_dependencyMap[9]);
  var _icons = _$$_REQUIRE(_dependencyMap[10]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[11]));
  var _text = _$$_REQUIRE(_dependencyMap[12]);
  var _constants = _$$_REQUIRE(_dependencyMap[13]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[14]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _lodash = _$$_REQUIRE(_dependencyMap[16]);
  var _adobe = _$$_REQUIRE(_dependencyMap[17]);
  var _shopRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[18]));
  var _readMore = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[19]));
  var _i18n = _$$_REQUIRE(_dependencyMap[20]);
  var _error = _$$_REQUIRE(_dependencyMap[21]);
  var _native = _$$_REQUIRE(_dependencyMap[22]);
  var _error2 = _$$_REQUIRE(_dependencyMap[23]);
  var _infoBanner = _$$_REQUIRE(_dependencyMap[24]);
  var _utils = _$$_REQUIRE(_dependencyMap[25]);
  var _bottomSheetUnableLoadLocation = _$$_REQUIRE(_dependencyMap[26]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[27]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[28]);
  var _cusineInDetail = _$$_REQUIRE(_dependencyMap[29]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[30]);
  var _availableStaffDiscounts = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[31]));
  var _availableDealsPromos = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[32]));
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[33]);
  var _staffPerkSaga = _$$_REQUIRE(_dependencyMap[34]);
  var _bottomSheetMapUnavailable = _$$_REQUIRE(_dependencyMap[35]);
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[36]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[37]);
  var _pageConfigSaga = _$$_REQUIRE(_dependencyMap[38]);
  var _deeplinkParameter = _$$_REQUIRE(_dependencyMap[39]);
  var _enum = _$$_REQUIRE(_dependencyMap[40]);
  var _bottomSheetError = _$$_REQUIRE(_dependencyMap[41]);
  var _useModal2 = _$$_REQUIRE(_dependencyMap[42]);
  var _theme = _$$_REQUIRE(_dependencyMap[43]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[44]));
  var _overlayStickyHeader = _$$_REQUIRE(_dependencyMap[45]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[46]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SCREEN_NAME = "ShopDetailsScreen";
  var getDay = function getDay(day) {
    return day || (0, _i18n.translate)("shopDetailScreen.daily");
  };
  var getMapName = function getMapName(item, dotUnicode) {
    var _item$openCloseStatus;
    if (item != null && item.mapName && (_item$openCloseStatus = item.openCloseStatus) != null && _item$openCloseStatus.status && !(item != null && item.hourComment)) {
      return item.mapName + `${"  "}` + dotUnicode + `${"  "}`;
    }
    return item == null ? undefined : item.mapName;
  };
  var handleSpace = function handleSpace(indx, size) {
    if (indx < size) return {
      marginBottom: 8
    };
    return {};
  };
  var renderCloseStatus = function renderCloseStatus(item) {
    if (item.openCloseStatus) {
      return (0, _jsxRuntime.jsx)(_text.Text, {
        preset: "bodyTextBold",
        style: [{
          color: item.openCloseStatus.colorCode
        }, styles.statusTextStyle],
        children: item.openCloseStatus.status
      });
    }
    return null;
  };
  var getTextNumber = function getTextNumber(item, number) {
    return (item ? ", " : "") + number;
  };
  var ExpandableComponent = function ExpandableComponent(_ref) {
    var _item$locationDetails3, _item$locationDetails4, _item$timingsInfo, _item$timingsInfo2;
    var item = _ref.item,
      onClickFunction = _ref.onClickFunction,
      key = _ref.key,
      dotUnicode = _ref.dotUnicode,
      dialCall = _ref.dialCall,
      refRetryAction = _ref.refRetryAction,
      setConnection = _ref.setConnection,
      index = _ref.index;
    var _useState = (0, _react.useState)(0),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      layoutHeight = _useState2[0],
      setLayoutHeight = _useState2[1];
    var navigation = (0, _native.useNavigation)();
    var unableToLoadLocationRef = (0, _react.useRef)(null);
    var mapUnavailable = (0, _react.useRef)(null);
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      mapRMFlag = _useState4[0],
      setMapRMFlag = _useState4[1];
    var hourComment = item == null ? undefined : item.hourComment;
    (0, _react.useEffect)(function () {
      if (item.isExpanded) {
        setLayoutHeight(null);
        return;
      }
      setLayoutHeight(0);
    }, [item.isExpanded]);
    var phoneNumberStyle = _reactNative2.Platform.OS === "ios" ? styles.phoneNumberTextStyle : styles.phoneNumberTextStyleAndroid;
    (0, _react.useEffect)(function () {
      var fetchAtomRMConfig = function fetchAtomRMConfig() {
        var mapFlagEnable = (0, _remoteConfig.isFlagON)(_remoteConfig.REMOTE_CONFIG_FLAGS.ATOMS_MAP);
        setMapRMFlag(mapFlagEnable);
      };
      fetchAtomRMConfig();
    }, []);
    var _handleFindDirection = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        if (isConnected) {
          var _item$locationDetails, _item$locationDetails2;
          refRetryAction.current = null;
          setConnection(true);
          if (!mapRMFlag) {
            return mapUnavailable == null ? undefined : mapUnavailable.current.show();
          }
          (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppATOMSEntryClick, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppATOMSEntryClick, `Shop Details|Get Directions|${item == null || (_item$locationDetails = item.locationDetails) == null ? undefined : _item$locationDetails[index].localRef}`));
          //@ts-ignore
          navigation.push(_constants.NavigationConstants.changiMap, {
            localRef: (_item$locationDetails2 = item.locationDetails) == null ? undefined : _item$locationDetails2[index].localRef
          });
          return;
        }
        refRetryAction.current = _handleFindDirection;
        setConnection(false);
      });
      return function handleFindDirection() {
        return _ref2.apply(this, arguments);
      };
    }();
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.wrapInformationDetail,
      children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        style: styles.header,
        onPress: onClickFunction,
        testID: `${SCREEN_NAME}__TouchableExpand__${key}`,
        accessibilityLabel: `${SCREEN_NAME}__TouchableExpand__${key}`,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.flexStyle,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.headerStyle,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              preset: "h4",
              style: styles.txtColor,
              text: getMapName(item, dotUnicode)
            }), !hourComment && renderCloseStatus(item)]
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.arrowIconStyle,
            children: (0, _utils.handleCondition)(item.isExpanded, (0, _jsxRuntime.jsx)(_icons.TopArrow, {}), (0, _jsxRuntime.jsx)(_icons.DownArrow, {}))
          })]
        })
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: [{
          height: layoutHeight
        }, styles.visibilityStyle],
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.content,
          children: [hourComment && (0, _jsxRuntime.jsx)(_text.Text, {
            numberOfLines: 3,
            style: styles.temporaryClosedStyle,
            children: hourComment
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.flexStyle,
            children: [(0, _jsxRuntime.jsx)(_icons.DirectionsOutline, {
              style: styles.directionIconStyle
            }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: styles.areaInfoViewStyle,
              children: [(0, _jsxRuntime.jsxs)(_text.Text, {
                numberOfLines: 2,
                preset: "bodyTextRegular",
                style: styles.text,
                children: [(0, _utils.handleCondition)(item.terminal, item.terminal + " ", ""), (0, _utils.handleCondition)(item.description, item.description + ", ", ""), (0, _utils.handleCondition)(item.unitNo, "#" + item.unitNo + ", ", ""), (0, _utils.handleCondition)(item.area, item.area + (0, _i18n.translate)("shopDetailScreen.area"), "")]
              }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                onPress: _handleFindDirection,
                disabled: (0, _lodash.isEmpty)(item == null || (_item$locationDetails3 = item.locationDetails) == null || (_item$locationDetails3 = _item$locationDetails3[index]) == null ? undefined : _item$locationDetails3.localRef),
                children: (0, _jsxRuntime.jsx)(_text.Text, {
                  preset: "bodyTextBold",
                  style: (0, _lodash.isEmpty)(item == null || (_item$locationDetails4 = item.locationDetails) == null || (_item$locationDetails4 = _item$locationDetails4[index]) == null ? undefined : _item$locationDetails4.localRef) ? styles.getDirectionStyleDisabled : styles.getDirectionStyle,
                  children: "Get directions"
                })
              })]
            })]
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.directionContainerView,
            children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              style: styles.directionTouchableOpacityStyle,
              testID: `${SCREEN_NAME}__TouchableGetDirections__${key}`,
              accessibilityLabel: `${SCREEN_NAME}__TouchableGetDirections__${key}`,
              onPress: _handleFindDirection,
              disabled: !item.localRef,
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                preset: "textLink",
                text: (0, _i18n.translate)("shopDetailScreen.getDirections"),
                style: item.localRef ? (0, _utils.handleCondition)(_reactNative2.Platform.OS === "android", styles.directionTextStyleAndroid, null) : (0, _utils.handleCondition)(_reactNative2.Platform.OS === "android", styles.directionTextStyleAndroidDisabled, null)
              })
            })
          }), (0, _utils.handleCondition)(((_item$timingsInfo = item.timingsInfo) == null ? undefined : _item$timingsInfo.length) > 0, (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.flexStyle,
            children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: styles.timingContainerStyle,
              children: [(0, _jsxRuntime.jsx)(_icons.ClockOutline, {
                style: styles.clockIconStyle
              }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: styles.timingsInfoViewStyle,
                children: (_item$timingsInfo2 = item.timingsInfo) == null ? undefined : _item$timingsInfo2.map(function (res, keyVal) {
                  var _item$timingsInfo3;
                  return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                    style: [styles.timingStyle, handleSpace(keyVal, item == null || (_item$timingsInfo3 = item.timingsInfo) == null ? undefined : _item$timingsInfo3.length)],
                    children: [(0, _jsxRuntime.jsx)(_text.Text, {
                      preset: "bodyTextRegular",
                      style: styles.timingsInfoDayText,
                      children: getDay(res.day)
                    }), (0, _jsxRuntime.jsx)(_text.Text, {
                      preset: "bodyTextRegular",
                      style: styles.timingsText,
                      children: res.timings
                    })]
                  }, keyVal);
                })
              })]
            })
          }), null), (0, _utils.handleCondition)(item.phoneDetails.length > 0, (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.flexStyle,
            children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: styles.phoneContainerStyle,
              children: [(0, _jsxRuntime.jsx)(_icons.PhoneOutline, {
                style: styles.phoneIconStyle
              }), item.phoneDetails.map(function (number, i) {
                return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                  onPress: function onPress() {
                    return dialCall(number);
                  },
                  testID: `${SCREEN_NAME}__TouchableDialCall__${key}`,
                  accessibilityLabel: `${SCREEN_NAME}__TouchableDialCall__${key}`,
                  children: (0, _jsxRuntime.jsx)(_text.Text, {
                    preset: "textLink",
                    numberOfLines: 2,
                    style: phoneNumberStyle,
                    children: getTextNumber(i, number)
                  })
                }, i);
              })]
            })
          }), null)]
        }, "content")
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: (0, _utils.handleCondition)(item.isExpanded, styles.separatorExpanded, styles.separatorNotExpanded)
      }), (0, _jsxRuntime.jsx)(_bottomSheetUnableLoadLocation.BottomSheetUnableLoadLocation, {
        ref: unableToLoadLocationRef
      }), (0, _jsxRuntime.jsx)(_bottomSheetMapUnavailable.BottomSheetMapUnavailable, {
        ref: mapUnavailable
      })]
    });
  };
  var safeViewHeaderForAndroid = function safeViewHeaderForAndroid(inset) {
    var _safeViewHeaderForAndroid = {
      marginTop: _reactNative2.Platform.OS === "android" && inset != null && inset.top ? inset == null ? undefined : inset.top : 0,
      marginStart: 14
    };
    return _safeViewHeaderForAndroid;
  };
  var _worklet_7748468227624_init_data = {
    code: "function shopDetailsTsx1(event){const{scrollY}=this.__closure;scrollY.value=event.contentOffset.y;}"
  };
  var ShopDetailsScreen = exports.ShopDetailsScreen = function ShopDetailsScreen(props) {
    var _props$route$params, _props$route$params2;
    var loading = "loading";
    var navigation = (0, _native.useNavigation)();
    var _useModal = (0, _useModal2.useModal)("shopDetailError"),
      isModalVisible = _useModal.isModalVisible,
      openModal = _useModal.openModal,
      closeModal = _useModal.closeModal;
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var tenantId = (_props$route$params = props.route.params) == null ? undefined : _props$route$params.tenantId;
    var name = ((_props$route$params2 = props.route.params) == null ? undefined : _props$route$params2.name) || "";
    var dotUnicode = (0, _constants.getDotUnicode)();
    var dispatch = (0, _reactRedux.useDispatch)();
    var _ref3 = (0, _reactRedux.useSelector)(function (data) {
        return _shopRedux.ShopSelectors.shopDetailScreenPayloadData(data);
      }) || {},
      shopDetailScreenPayload = _ref3.shopDetailScreenPayload,
      shopRewardInfoPayloadData = _ref3.shopRewardInfoPayloadData;
    var shopDetails = shopDetailScreenPayload == null ? undefined : shopDetailScreenPayload.data;
    var rewardInfoData = shopRewardInfoPayloadData == null ? undefined : shopRewardInfoPayloadData.data;
    var locationDetails = shopDetails == null ? undefined : shopDetails.locationDetails;
    var _useState5 = (0, _react.useState)([]),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      listDataSource = _useState6[0],
      setListDataSource = _useState6[1];
    var inset = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    var profile = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var isStaff = profile == null ? undefined : profile.airportStaff;
    var _useState7 = (0, _react.useState)([]),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      availableDiscounts = _useState8[0],
      setAvailableDiscounts = _useState8[1];
    var _useState9 = (0, _react.useState)([]),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      availableDealsPromos = _useState0[0],
      setAvailableDealsPromos = _useState0[1];
    var _useState1 = (0, _react.useState)(true),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      isConnection = _useState10[0],
      setConnection = _useState10[1];
    var refRetryAction = (0, _react.useRef)(null);
    var scrollY = (0, _reactNativeReanimated.useSharedValue)(0);
    var scrollHandler = (0, _reactNativeReanimated.useAnimatedScrollHandler)({
      onScroll: function () {
        var shopDetailsTsx1 = function shopDetailsTsx1(event) {
          scrollY.value = event.contentOffset.y;
        };
        shopDetailsTsx1.__closure = {
          scrollY: scrollY
        };
        shopDetailsTsx1.__workletHash = 7748468227624;
        shopDetailsTsx1.__initData = _worklet_7748468227624_init_data;
        return shopDetailsTsx1;
      }()
    });
    var getStaffAvailableDiscount = /*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* () {
        var featureFlagEnable = (0, _remoteConfig.isFlagON)(_remoteConfig.REMOTE_CONFIG_FLAGS.TENANTDETAILS_STAFFPERKS);
        if (!featureFlagEnable || !isLoggedIn || !isStaff) {
          return;
        }
        var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch2.isConnected;
        if (isConnected) {
          var staffDiscount = yield (0, _staffPerkSaga.getStaffPerkAvailableDiscount)({
            tenantId: tenantId
          });
          if (!(0, _lodash.isEmpty)(staffDiscount)) {
            setAvailableDiscounts(staffDiscount);
          }
        }
      });
      return function getStaffAvailableDiscount() {
        return _ref4.apply(this, arguments);
      };
    }();
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)(`Shop_Detail_${name}`);
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)(`Shop_Detail_${name}`, (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      // getShopNameTagLocation()
      return unsubscribeFocus;
    }, [navigation]);
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      getShopNameTagLocation();
    }, []));
    var getShopNameTagLocation = /*#__PURE__*/function () {
      var _ref5 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch3 = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch3.isConnected;
        if (isConnected) {
          setConnection(true);
          dispatch(_shopRedux.default.shopDetailScreenRequest(tenantId));
          dispatch(_shopRedux.default.shopRewardInfoRequest(tenantId));
          if (isStaff && !(0, _lodash.isEmpty)(tenantId)) {
            getStaffAvailableDiscount();
          }
          var isShopDineV2Enabled = (0, _remoteConfig.getFeatureFlagInit)(_remoteConfig.REMOTE_CONFIG_FLAGS.SHOPDINE_V2);
          if (isShopDineV2Enabled) {
            var promos = yield (0, _staffPerkSaga.getDealsPromosAvailableDiscount)({
              tenantId: tenantId
            });
            if (!(0, _lodash.isEmpty)(promos)) {
              setAvailableDealsPromos(promos);
            }
          }
          return;
        }
        setConnection(false);
      });
      return function getShopNameTagLocation() {
        return _ref5.apply(this, arguments);
      };
    }();
    _react.default.useEffect(function () {
      setListDataSource(locationDetails);
    });
    if (_reactNative2.Platform.OS === "android") {
      _reactNative2.UIManager.setLayoutAnimationEnabledExperimental(true);
    }
    var updateLayout = function updateLayout(index) {
      _reactNative2.LayoutAnimation.configureNext(Object.assign({}, _reactNative2.LayoutAnimation.Presets.easeInEaseOut, {
        duration: 100
      }));
      var array = (0, _toConsumableArray2.default)(listDataSource);
      array.forEach(function (_value, placeindex) {
        if (placeindex === index) {
          array[placeindex].isExpanded = !array[placeindex].isExpanded;
        } else {
          array[placeindex].isExpanded = false;
        }
      });
      setListDataSource(array);
    };
    var transformPhoneNumber = function transformPhoneNumber(phone) {
      if (_reactNative2.Platform.OS === "android") {
        return `tel:${phone}`;
      }
      return `telprompt:${phone}`;
    };
    var dialCall = function dialCall(num) {
      var phoneNumber = transformPhoneNumber(num);
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppShopDetailShopInformationPhoneNumber, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppShopDetailShopInformationPhoneNumber, num));
      _reactNative2.Linking.openURL(phoneNumber);
    };
    var handleReadMorePress = function handleReadMorePress(handlePress) {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppShopDetailAbout, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppShopDetailAbout, "1"));
      handlePress == null || handlePress();
    };
    var readMore = function readMore(handlePress) {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.loadMoreShowLessView,
        children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: function onPress() {
            return handleReadMorePress(handlePress);
          },
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "bodyTextBold",
            style: styles.loadMoreShowLessTextStyle,
            children: (0, _i18n.translate)("shopDetailScreen.readMore")
          })
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: function onPress() {
            return handleReadMorePress(handlePress);
          },
          testID: `${SCREEN_NAME}__TouchableReadMore`,
          accessibilityLabel: `${SCREEN_NAME}__TouchableReadMore`,
          children: (0, _jsxRuntime.jsx)(_icons.DownArrow, {
            style: styles.loadMoreShowLessIconStyle
          })
        })]
      });
    };
    var showLess = function showLess(handlePress) {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.loadMoreShowLessView,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          preset: "bodyTextBold",
          style: styles.loadMoreShowLessTextStyle,
          onPress: handlePress,
          children: (0, _i18n.translate)("shopDetailScreen.showLess")
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: handlePress,
          testID: `${SCREEN_NAME}__TouchableShowLess`,
          accessibilityLabel: `${SCREEN_NAME}__TouchableShowLess`,
          children: (0, _jsxRuntime.jsx)(_icons.TopArrow, {
            style: styles.loadMoreShowLessIconStyle
          })
        })]
      });
    };
    var onRetryError = function onRetryError() {
      if (refRetryAction.current) {
        refRetryAction.current == null || refRetryAction.current();
        return;
      }
      getShopNameTagLocation();
    };
    var handleNavigateCSMIShopchangi = /*#__PURE__*/function () {
      var _ref6 = (0, _asyncToGenerator2.default)(function* (url) {
        var ecid = yield (0, _adobe.getExperienceCloudId)();
        var target = (0, _deeplinkParameter.getISCLinkRedirectTarget)(url);
        var payload = {
          stateCode: _constants.StateCode.ISHOPCHANGI,
          input: Object.assign({}, target, {
            ecid: ecid
          })
        };
        try {
          var response = yield (0, _pageConfigSaga.getDeepLinkV2)(payload, true);
          if (response != null && response.redirectUri) {
            navigation.navigate(_constants.NavigationConstants.playpassWebview, {
              uri: response == null ? undefined : response.redirectUri,
              needBackButton: true,
              needCloseButton: true,
              headerType: _enum.WebViewHeaderTypes.default,
              basicAuthCredential: response == null ? undefined : response.basicAuth
            });
          } else {
            throw response;
          }
        } catch (error) {
          openModal();
        }
      });
      return function handleNavigateCSMIShopchangi(_x) {
        return _ref6.apply(this, arguments);
      };
    }();
    var renderIscCtaButton = function renderIscCtaButton() {
      var _shopDetails$aemTenan;
      if ((0, _lodash.isEmpty)(shopDetails == null || (_shopDetails$aemTenan = shopDetails.aemTenantDetails) == null ? undefined : _shopDetails$aemTenan.iscurl)) {
        return null;
      }
      return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: styles.iscCTAButton,
        onPress: function onPress() {
          var _shopDetails$aemTenan2;
          return handleNavigateCSMIShopchangi(shopDetails == null || (_shopDetails$aemTenan2 = shopDetails.aemTenantDetails) == null ? undefined : _shopDetails$aemTenan2.iscurl);
        },
        children: [(0, _jsxRuntime.jsx)(_icons.ShopBagIcon, {}), (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.iscCTAText,
          tx: "shopDetailScreen.shopOnIshopChangi"
        })]
      });
    };
    var customStaffAvailableDiscountStyle = {
      marginTop: 24
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.container,
      testID: "ShopDetailsScreen",
      children: [(0, _jsxRuntime.jsx)(_overlayStickyHeader.OverlayStickyHeader, {
        height: 100,
        showAtOffset: 100,
        scrollY: scrollY,
        onBackPress: props.navigation.goBack,
        title: shopDetails == null ? undefined : shopDetails.title
      }), (0, _utils.handleCondition)((shopDetailScreenPayload == null ? undefined : shopDetailScreenPayload.type) === loading, (0, _jsxRuntime.jsx)(_shopDetailsLoadingView.ShopDetailsLoadingScreen, {}), (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsx)(_reactNative2.SafeAreaView, {
          style: styles.headerViewStyle,
          children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: function onPress() {
              props.navigation.goBack();
            },
            style: safeViewHeaderForAndroid(inset),
            testID: `${SCREEN_NAME}__TouchableBackButton`,
            accessibilityLabel: `${SCREEN_NAME}__TouchableBackButton`,
            children: (0, _jsxRuntime.jsx)(_icons.BackButton, {})
          })
        }), (0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.ScrollView, {
          showsVerticalScrollIndicator: false,
          testID: `${SCREEN_NAME}__ScrollView`,
          accessibilityLabel: `${SCREEN_NAME}__ScrollView`,
          onScroll: scrollHandler,
          children: [(0, _utils.handleCondition)(shopDetails, (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
            children: [(0, _jsxRuntime.jsx)(_hero.Hero, {
              heroImagesUrl: shopDetails == null ? undefined : shopDetails.heroCarouselImage,
              logoImageUrl: shopDetails == null ? undefined : shopDetails.heroCarouselLogo,
              type: shopDetailScreenPayload == null ? undefined : shopDetailScreenPayload.type,
              testID: `${SCREEN_NAME}__Hero`,
              accessibilityLabel: `${SCREEN_NAME}__Hero`,
              showWave: false,
              onSwiperChange: function onSwiperChange() {
                (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppShopDetailCarouselImage, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppShopDetailCarouselImage, "1"));
              }
            }), (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
              children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
                children: (0, _jsxRuntime.jsx)(_text.Text, {
                  preset: "h1",
                  style: styles.titleText,
                  children: shopDetails == null ? undefined : shopDetails.title
                })
              }), !(0, _lodash.isEmpty)(availableDiscounts) && (0, _jsxRuntime.jsx)(_availableStaffDiscounts.default, {
                availableDiscounts: availableDiscounts,
                navigation: navigation,
                customContainerStyle: customStaffAvailableDiscountStyle,
                storeName: shopDetails == null ? undefined : shopDetails.title
              }), !(0, _lodash.isEmpty)(availableDealsPromos) && (0, _jsxRuntime.jsx)(_availableDealsPromos.default, {
                dealsPromos: availableDealsPromos,
                navigation: navigation,
                storeName: shopDetails == null ? undefined : shopDetails.title
              }), listDataSource == null ? undefined : listDataSource.map(function (item, key) {
                return (0, _jsxRuntime.jsx)(ExpandableComponent, {
                  onClickFunction: function onClickFunction() {
                    updateLayout(key);
                  },
                  item: item,
                  index: key,
                  dialCall: dialCall,
                  dotUnicode: dotUnicode,
                  setConnection: setConnection,
                  refRetryAction: refRetryAction
                }, key);
              }), renderIscCtaButton(), (0, _utils.handleCondition)(shopRewardInfoPayloadData == null ? undefined : shopRewardInfoPayloadData.hasError, (0, _jsxRuntime.jsx)(_error2.ErrorComponent, {
                type: _error2.ErrorComponentType.standard,
                onPressed: function onPressed() {
                  dispatch(_shopRedux.default.shopRewardInfoRequest(tenantId));
                }
              }), (0, _utils.handleCondition)(rewardInfoData, (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: styles.infoBannerView,
                children: (0, _jsxRuntime.jsx)(_infoBanner.InfoBanner, {
                  title: rewardInfoData == null ? undefined : rewardInfoData.rewardTitle,
                  state: rewardInfoData == null ? undefined : rewardInfoData.rewardState,
                  type: rewardInfoData == null ? undefined : rewardInfoData.type,
                  text: rewardInfoData == null ? undefined : rewardInfoData.rewardText,
                  linkText: rewardInfoData == null ? undefined : rewardInfoData.rewardLinkText,
                  link: rewardInfoData == null ? undefined : rewardInfoData.rewardLink,
                  onPressed: function onPressed() {
                    return (
                      //@ts-ignore
                      navigation.navigate(_constants.NavigationConstants.webview, {
                        uri: rewardInfoData == null ? undefined : rewardInfoData.rewardLink
                      })
                    );
                  }
                })
              }), null)), (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: styles.aboutViewStyle,
                children: (0, _utils.handleCondition)(shopDetails == null ? undefined : shopDetails.description, (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
                  children: [(0, _jsxRuntime.jsx)(_text.Text, {
                    preset: "h4",
                    style: styles.aboutViewHeaderText,
                    children: (0, _i18n.translate)("shopDetailScreen.about")
                  }), (0, _jsxRuntime.jsx)(_readMore.default, {
                    numberOfLines: 5,
                    renderTruncatedFooter: readMore,
                    renderRevealedFooter: showLess,
                    children: (0, _jsxRuntime.jsx)(_text.Text, {
                      preset: "bodyTextRegular",
                      style: (0, _utils.handleCondition)(_reactNative2.Platform.OS === "ios", styles.aboutViewText, styles.aboutViewTextAndroid),
                      children: shopDetails == null ? undefined : shopDetails.description
                    })
                  })]
                }), null)
              })]
            })]
          }), null), (0, _jsxRuntime.jsx)(_cusineInDetail.CuisineInDetail, {
            data: shopDetails == null ? undefined : shopDetails.exploreCategories,
            screen: "SHOP"
          })]
        })]
      })), (0, _utils.handleCondition)((shopDetailScreenPayload == null ? undefined : shopDetailScreenPayload.hasError) && (shopDetailScreenPayload == null ? undefined : shopDetailScreenPayload.type) !== loading, (0, _jsxRuntime.jsx)(_error.ErrorScreen, {
        onReload: function onReload() {
          return getShopNameTagLocation();
        },
        testID: `${SCREEN_NAME}__ErrorScreenGetShopNameTagLocation`,
        accessibilityLabel: `${SCREEN_NAME}__ErrorScreenGetShopNameTagLocation`
      }), null), (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        header: true,
        hideScreenHeader: false,
        headerBackgroundColor: "transparent",
        visible: !isConnection,
        onBack: function onBack() {
          navigation.goBack();
        },
        onReload: onRetryError
      }), (0, _jsxRuntime.jsx)(_bottomSheetError.BottomSheetError, {
        openPendingModal: true,
        icon: (0, _jsxRuntime.jsx)(_icons.InfoRed, {}),
        visible: isModalVisible,
        shouldFitContentHeight: true,
        animationInTiming: 500,
        animationOutTiming: 500,
        colorMsg: _theme.color.palette.almostBlackGrey,
        title: (0, _i18n.translate)("popupError.somethingWrongOneline"),
        errorMessage: (0, _i18n.translate)("popupError.networkErrorMessage"),
        onClose: closeModal,
        buttonText: (0, _i18n.translate)("common.close"),
        testID: `${SCREEN_NAME}__BottomSheetErrorISC`,
        accessibilityLabel: `${SCREEN_NAME}__BottomSheetErrorISC`
      })]
    });
  };
