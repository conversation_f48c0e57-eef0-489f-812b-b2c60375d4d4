  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _icons = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _reactNativeDynamicRender = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[10]);
  var _instagramTrending = _$$_REQUIRE(_dependencyMap[11]);
  var _search = _$$_REQUIRE(_dependencyMap[12]);
  var _search2 = _$$_REQUIRE(_dependencyMap[13]);
  var _navigationUtilities = _$$_REQUIRE(_dependencyMap[14]);
  var _dineRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _pageConfigRedux = _$$_REQUIRE(_dependencyMap[16]);
  var _sections = _$$_REQUIRE(_dependencyMap[17]);
  var _color = _$$_REQUIRE(_dependencyMap[18]);
  var _constants = _$$_REQUIRE(_dependencyMap[19]);
  var _dineShopContext = _$$_REQUIRE(_dependencyMap[20]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[21]);
  var _adobe = _$$_REQUIRE(_dependencyMap[22]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[23]);
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[24]);
  var _dineFilters = _$$_REQUIRE(_dependencyMap[25]);
  var _dineReservation = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[26]));
  var _errorUnplannedMaintenance = _$$_REQUIRE(_dependencyMap[27]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[28]);
  var _lodash = _$$_REQUIRE(_dependencyMap[29]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[30]);
  var _native = _$$_REQUIRE(_dependencyMap[31]);
  var _bottomTabs = _$$_REQUIRE(_dependencyMap[32]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[33]);
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[34]);
  var _suspend = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[35]));
  var _dineShopV = _$$_REQUIRE(_dependencyMap[36]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[37]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var container = {
    flex: 1,
    backgroundColor: _color.color.palette.lightestGrey
  };
  var searchBarContainer = {
    flexDirection: "row",
    paddingLeft: 24,
    alignItems: "center",
    paddingTop: 13,
    backgroundColor: _color.color.palette.lightestGrey
  };
  var filterIconContainer = {
    position: "absolute",
    right: 26,
    top: 26
  };
  var overlayStyle = {
    backgroundColor: _color.color.palette.lightestGrey,
    flex: 1
  };
  var whiteContainer = {
    backgroundColor: _color.color.transparent
  };
  var SCREEN_NAME = "DineScreen";
  var DineScreen = function DineScreen(_ref) {
    var navigation = _ref.navigation,
      route = _ref.route,
      onLayout = _ref.onLayout;
    // This object is responsible to define the component thats needs to be loaded
    var Components = {
      view: _reactNative2.View,
      contentGuide: _sections.GuideContent,
      mainPromo: _sections.MainPromoSection,
      instagramTrending: _instagramTrending.InstagramTrending,
      specialPromo: _sections.SectionPromo,
      cuisine: _sections.Cuisine,
      newlyOpened: _sections.NewlyOpened,
      recommendedForYou: _sections.RecommendedForYou,
      exploreMore: _sections.ExploreMore
    };
    var dispatch = (0, _reactRedux.useDispatch)();
    var isFocused = (0, _native.useIsFocused)();
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      showFilterModal = _useState2[0],
      setShowFilterModal = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isNoConnection = _useState4[0],
      setNoConnection = _useState4[1];
    var _useHandleScroll = (0, _navigationUtilities.useHandleScroll)(),
      handleScroll = _useHandleScroll.handleScroll,
      isTabVisible = _useHandleScroll.isTabVisible;
    var bottomTabHeight = (0, _bottomTabs.useBottomTabBarHeight)();
    var _useDineShopFlags = (0, _dineShopV.useDineShopFlags)(),
      isShopDineEpicV2On = _useDineShopFlags.isShopDineEpicV2On;
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("DINE_SCREEN"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var pageConfigPayload = (0, _reactRedux.useSelector)(function (data) {
      return _pageConfigRedux.PageConfigSelectors.pageConfigDinePayload(data);
    });
    var dataErrorMaintenanceConfig = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.GET_ERROR_MAINTENANCE));
    var listErrorMaintenance = (0, _lodash.get)(dataErrorMaintenanceConfig, "data.list", []);
    var screenMaintenanceObj = (0, _react.useMemo)(function () {
      var data = listErrorMaintenance == null ? undefined : listErrorMaintenance.find(function (obj) {
        return obj.screenName === _constants.screenTagName.dine;
      });
      return data || {};
    }, [listErrorMaintenance]);
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var dineHandlers = (0, _react.useContext)(_dineShopContext.DineShopContext).Handlers.dine;
    var handleDineComponentClick = function handleDineComponentClick(item) {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppDineContentSwimlaneTiles, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppDineContentSwimlaneTiles, (item == null ? undefined : item.title) || ""));
      navigation.navigate(_constants.NavigationConstants.webview, {
        uri: item.link
      });
    };
    var handleTenantComponentClick = function handleTenantComponentClick(item) {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppDineNewlyOpened, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppDineNewlyOpened, (item == null ? undefined : item.tenantName) || ""));
      navigation.navigate(_constants.NavigationConstants.restaurantDetailScreen, {
        tenantId: item == null ? undefined : item.id,
        name: item == null ? undefined : item.tenantName
      });
    };
    dineHandlers.handleDineComponentClick = handleDineComponentClick;
    dineHandlers.handleTenantComponentClick = handleTenantComponentClick;
    var getDineData = (0, _react.useCallback)(function () {
      dispatch(_dineRedux.default.dineRequest());
    }, []);
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)("Dine_Landing");
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      _reactNative2.InteractionManager.runAfterInteractions(function () {
        (0, _adobe.commonTrackingScreen)("Dine_Landing", (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
    }, []));
    var checkInternet = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        if (!isConnected) {
          setNoConnection(true);
        } else {
          getDineData();
        }
      });
      return function checkInternet() {
        return _ref2.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      checkInternet();
    }, []);
    var onScroll = function onScroll(event) {
      var _route$params;
      handleScroll(event);
      route == null || (_route$params = route.params) == null || _route$params.setOptions == null || _route$params.setOptions({
        tabBarVisible: isTabVisible.current
      });
    };
    var contentContainerStyle = {
      paddingBottom: ((0, _reactNativeSafeAreaContext.useSafeAreaInsets)().bottom + _reactNative2.Platform.OS === "ios" ? 18 : 50) + bottomTabHeight
    };
    var handleButtonOnPress = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* (navigationObj, redirect) {
        var type = navigationObj.type,
          value = navigationObj.value;
        if (!type || !value) return;
        handleNavigation(type, value, redirect);
      });
      return function handleButtonOnPress(_x, _x2) {
        return _ref3.apply(this, arguments);
      };
    }();
    var onSearchPressed = (0, _react.useCallback)(function () {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppDineSearch, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppDineSearch, _adobe.AdobeTagName.CAppDineLanding));
      navigation.navigate("search", {
        module: "DINE",
        sourcePage: _adobe.AdobeTagName.CAppDineLanding
      });
    }, []);
    var onFilterPressed = (0, _react.useCallback)(function () {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppDineSearchFilter, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppDineSearchFilter, "1"));
      (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(true);
      setShowFilterModal(true);
    }, []);
    if (isNoConnection) {
      return (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        header: false,
        headerBackgroundColor: "transparent",
        visible: true,
        testID: `${SCREEN_NAME}__ErrorOverlayNoConnection`,
        onReload: function onReload() {
          var checkConnection = /*#__PURE__*/function () {
            var _ref4 = (0, _asyncToGenerator2.default)(function* () {
              var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
                isConnected = _yield$NetInfo$fetch2.isConnected;
              if (isConnected) {
                getDineData();
                setNoConnection(false);
              }
            });
            return function checkConnection() {
              return _ref4.apply(this, arguments);
            };
          }();
          checkConnection();
        },
        noInternetOverlayStyle: overlayStyle
      });
    }
    if (!(0, _lodash.isEmpty)(screenMaintenanceObj) && screenMaintenanceObj != null && screenMaintenanceObj.enableMode) {
      return (0, _jsxRuntime.jsx)(_errorUnplannedMaintenance.ErrorUnplannedMaintenance, {
        header: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.header,
        subHeader: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.subHeader,
        icon: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.icon,
        buttonLabel: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.buttonLabel,
        buttonLabel2: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.buttonLabel2,
        onFirstButtonPress: function onFirstButtonPress() {
          return handleButtonOnPress(screenMaintenanceObj == null ? undefined : screenMaintenanceObj.navigationFirst, screenMaintenanceObj == null ? undefined : screenMaintenanceObj.redirectFirst);
        },
        onSecondButtonPress: function onSecondButtonPress() {
          return handleButtonOnPress(screenMaintenanceObj == null ? undefined : screenMaintenanceObj.navigationSecond, screenMaintenanceObj == null ? undefined : screenMaintenanceObj.redirectSecond);
        },
        testID: `${SCREEN_NAME}__ErrorUnplannedMaintenance`,
        accessibilityLabel: `${SCREEN_NAME}__ErrorUnplannedMaintenance`
      });
    }
    return (0, _jsxRuntime.jsx)(_suspend.default, {
      freeze: !isFocused,
      children: (0, _jsxRuntime.jsx)(_reactNative2.ScrollView, {
        onScroll: onScroll,
        scrollEventThrottle: 1,
        showsVerticalScrollIndicator: false,
        style: container,
        contentContainerStyle: contentContainerStyle,
        testID: `${SCREEN_NAME}__ScrollView`,
        accessibilityLabel: `${SCREEN_NAME}__ScrollView`,
        scrollEnabled: !isShopDineEpicV2On,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          onLayout: onLayout,
          style: whiteContainer,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: searchBarContainer,
            children: [(0, _jsxRuntime.jsx)(_search.Search, {
              type: _search2.SearchBarVariations.mainPage,
              placeholderTx: "dineScreen.searchBarText",
              onPressed: onSearchPressed,
              testID: `${SCREEN_NAME}__Search`,
              accessibilityLabel: `${SCREEN_NAME}__Search`
            }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: filterIconContainer,
              children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                onPress: onFilterPressed,
                testID: `${SCREEN_NAME}__TouchableFilter`,
                accessibilityLabel: `${SCREEN_NAME}__TouchableFilter`,
                children: (0, _jsxRuntime.jsx)(_icons.Filter, {
                  color: _color.color.palette.lightPurple
                })
              })
            })]
          }), (0, _jsxRuntime.jsx)(_dineFilters.DineFilter, {
            showFilterModal: showFilterModal,
            setShowFilterModal: setShowFilterModal
          }), (0, _jsxRuntime.jsx)(_sections.ChipFilterSection, {
            screen: "DINE"
          }), (0, _jsxRuntime.jsx)(_dineReservation.default, {}), pageConfigPayload && (0, _jsxRuntime.jsx)(_reactNativeDynamicRender.default, Object.assign({}, pageConfigPayload, {
            mapComponents: Components
          }))]
        })
      })
    });
  };
  var _default = exports.default = DineScreen;
