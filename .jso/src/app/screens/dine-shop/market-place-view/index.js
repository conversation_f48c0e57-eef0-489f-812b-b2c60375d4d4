  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _styles = _$$_REQUIRE(_dependencyMap[3]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[4]);
  var _marketPlaceRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _marketPlace = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _marketPlaceType = _$$_REQUIRE(_dependencyMap[9]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _constants = _$$_REQUIRE(_dependencyMap[11]);
  var _theme = _$$_REQUIRE(_dependencyMap[12]);
  var _dineShopV = _$$_REQUIRE(_dependencyMap[13]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[14]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SECTION_NAME = "MarketPlace__Section";
  var loadingData = Array(4).fill(null);
  var lighterGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var MarketPlaceView = function MarketPlaceView(props) {
    var dispatch = (0, _reactRedux.useDispatch)();
    var marketPlaceData = (0, _reactRedux.useSelector)(_marketPlaceRedux.MarketPlaceSelectors.marketPlaceData);
    var getListMarketPlaceLoading = (0, _reactRedux.useSelector)(_marketPlaceRedux.MarketPlaceSelectors.getListMarketPlaceLoading);
    var informativesCommon = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getInformativesCommon);
    var _useDineShopFlags = (0, _dineShopV.useDineShopFlags)(),
      isShopDineEpicV2On = _useDineShopFlags.isShopDineEpicV2On;
    var titleMarketPlace = informativesCommon == null ? undefined : informativesCommon.find(function (item) {
      return item.code === "INF10";
    });
    var listData = (0, _react.useMemo)(function () {
      return marketPlaceData.listMarketPlace;
    }, [marketPlaceData]);
    (0, _react.useEffect)(function () {
      dispatch(_marketPlaceRedux.default.getListMarketPlaceRequest());
    }, []);
    if (getListMarketPlaceLoading) {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _styles.styles.container,
        children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _styles.styles.titleLoadingContainer,
          children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: lighterGreyLoadingColors,
            shimmerStyle: _styles.styles.skeletonLayoutImage
          })
        }), (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
          data: loadingData,
          renderItem: function renderItem() {
            return (0, _jsxRuntime.jsx)(_reactNative.View, {
              style: _styles.styles.itemContainer,
              children: (0, _jsxRuntime.jsx)(_marketPlace.default, {
                type: _marketPlaceType.MarketPlaceType.loading,
                testID: SECTION_NAME
              })
            });
          },
          keyExtractor: function keyExtractor(_, index) {
            return index.toString();
          },
          scrollEnabled: !isShopDineEpicV2On
        })]
      });
    }
    var getDistanceItem = function getDistanceItem(index) {
      if (index === 0) {
        return {
          marginTop: 24
        };
      }
    };
    var _renderItem = function renderItem(item, index) {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: [_styles.styles.itemContainer, getDistanceItem(index)],
        children: (0, _jsxRuntime.jsx)(_marketPlace.default, Object.assign({
          testID: SECTION_NAME
        }, item, {
          onItemPress: props == null ? undefined : props.onItemPress,
          type: _marketPlaceType.MarketPlaceType.default
        }))
      });
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      onLayout: props == null ? undefined : props.onLayout,
      style: _styles.styles.container,
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: _styles.styles.titleContainer,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          text: (titleMarketPlace == null ? undefined : titleMarketPlace.title) || "",
          style: _styles.styles.titleStyles,
          testID: `${SECTION_NAME}__title`,
          accessibilityLabel: titleMarketPlace == null ? undefined : titleMarketPlace.title
        })
      }), (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
        data: listData,
        renderItem: function renderItem(_ref) {
          var item = _ref.item,
            index = _ref.index;
          return _renderItem(item, index);
        },
        keyExtractor: function keyExtractor(_, index) {
          return index.toString();
        },
        scrollEnabled: !isShopDineEpicV2On,
        contentContainerStyle: _styles.styles.contentContainerStyle,
        testID: `${SECTION_NAME}__flatlist`,
        accessibilityLabel: `${SECTION_NAME}__flatlist`
      })]
    });
  };
  var _default = exports.default = MarketPlaceView;
