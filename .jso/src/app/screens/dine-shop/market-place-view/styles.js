  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    container: {
      backgroundColor: _theme.color.palette.lightestGrey
    },
    contentContainerStyle: {
      paddingBottom: 50
    },
    itemContainer: {
      marginBottom: 16
    },
    skeletonLayoutImage: {
      borderRadius: 4,
      height: 17,
      marginBottom: 24,
      marginTop: 40,
      width: "85%"
    },
    titleContainer: {
      justifyContent: "center",
      marginTop: 40,
      width: "100%"
    },
    titleLoadingContainer: {
      alignItems: "center",
      justifyContent: "center",
      width: "100%"
    },
    titleStyles: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey,
      paddingHorizontal: 24
    })
  });
