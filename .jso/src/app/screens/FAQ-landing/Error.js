  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ErrorFAQ = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _icons = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  var ErrorFAQ = exports.ErrorFAQ = _react.default.memo(function () {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsx)(_icons.ErrorParkingBenefitInActive, {}), (0, _jsxRuntime.jsx)(_text.Text, {
        tx: "errorOverlay.variantSection.title",
        style: styles.txtTitle
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        tx: "errorOverlay.variant1.message",
        style: styles.txtContent
      })]
    });
  });
  var styles = _reactNative.StyleSheet.create({
    container: {
      paddingHorizontal: 24,
      alignItems: 'center'
    },
    txtTitle: {
      marginTop: 16,
      fontFamily: _theme.typography.black,
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 18,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      }),
      lineHeight: 22
    },
    txtContent: {
      fontFamily: _theme.typography.regular,
      color: _theme.color.palette.darkestGrey,
      fontSize: 14,
      fontWeight: _reactNative.Platform.select({
        ios: "400",
        android: "normal"
      }),
      lineHeight: 18,
      textAlign: 'center',
      marginTop: 8
    }
  });
