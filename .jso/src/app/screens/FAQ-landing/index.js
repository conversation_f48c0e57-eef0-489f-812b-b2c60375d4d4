  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[6]);
  var _Loading = _$$_REQUIRE(_dependencyMap[7]);
  var _Error = _$$_REQUIRE(_dependencyMap[8]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _reactNativeWebview = _$$_REQUIRE(_dependencyMap[10]);
  var _termsOfUsePolicy = _$$_REQUIRE(_dependencyMap[11]);
  var _utils = _$$_REQUIRE(_dependencyMap[12]);
  var _adobe = _$$_REQUIRE(_dependencyMap[13]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[14]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var FAQLanding = function FAQLanding(props) {
    var route = props.route;
    var _ref = (route == null ? undefined : route.params) || {},
      url = _ref.url;
    var _useState = (0, _react.useState)(true),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      loading = _useState2[0],
      setLoading = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      error = _useState4[0],
      setError = _useState4[1];
    var onMessageHandle = function onMessageHandle(message) {
      var messageData = (0, _utils.parseJsonWebviewMessage)(message);
      if (messageData != null && messageData.adobeAnalytics) {
        var _aaValue$split;
        var _ref2 = (messageData == null ? undefined : messageData.adobeAnalytics) || {},
          aaTag = _ref2.aaTag,
          aaValue = _ref2.aaValue;
        var questionContent = aaValue == null || aaValue.split == null || (_aaValue$split = aaValue.split(' | ')) == null ? undefined : _aaValue$split[0];
        if (questionContent && aaTag === _adobe.AdobeTagName.CAppL3FAQPage) {
          (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppL3FAQPage, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppL3FAQPage, `Parking | ${questionContent} | Link`));
        }
      }
    };
    (0, _react.useEffect)(function () {
      var checkInternet = /*#__PURE__*/function () {
        var _ref3 = (0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch.isConnected;
          if (!isConnected) {
            setError(true);
            setLoading(false);
          }
        });
        return function checkInternet() {
          return _ref3.apply(this, arguments);
        };
      }();
      checkInternet();
    }, []);
    if (error || !url) {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: [styles.container, styles.paddingT24],
        children: (0, _jsxRuntime.jsx)(_Error.ErrorFAQ, {})
      });
    }
    var checkEncodeURI = function checkEncodeURI(url) {
      return /%/i.test(url);
    };
    var onError = function onError() {
      setLoading(false);
      setError(true);
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      children: [loading && (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.paddingT24,
        children: (0, _jsxRuntime.jsx)(_Loading.LoadingFAQ, {})
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.content,
        children: url && (0, _jsxRuntime.jsx)(_reactNativeWebview.WebView, {
          containerStyle: [_termsOfUsePolicy.styles.containerStyleWebview, styles.background],
          source: {
            uri: _reactNative.Platform.OS === "ios" && !checkEncodeURI(url) ? encodeURI(url) : url
          },
          originWhitelist: ["*"],
          style: [_termsOfUsePolicy.styles.containerWebview, styles.background],
          automaticallyAdjustContentInsets: false,
          showsVerticalScrollIndicator: false,
          scrollEnabled: true,
          showsHorizontalScrollIndicator: false,
          javaScriptEnabled: true,
          onLoadEnd: function onLoadEnd() {
            setLoading(false);
          },
          onError: onError,
          onHttpError: onError,
          onMessage: onMessageHandle
        })
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      borderTopWidth: 1,
      borderColor: _theme.color.palette.lighterGrey
    },
    background: {
      backgroundColor: _theme.color.palette.almostWhiteGrey
    },
    paddingT24: {
      paddingTop: 24
    },
    viewError: {
      backgroundColor: _theme.color.palette.almostWhiteGrey
    },
    content: {
      flex: 1
    }
  });
  var _default = exports.default = FAQLanding;
