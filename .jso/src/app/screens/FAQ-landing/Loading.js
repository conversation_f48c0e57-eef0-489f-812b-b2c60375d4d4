  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.LoadingFAQ = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _miffyGamificationBanner = _$$_REQUIRE(_dependencyMap[3]);
  var _constants = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  var LoadingFAQ = exports.LoadingFAQ = _react.default.memo(function () {
    return [1, 2, 3].map(function (index) {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.containerStyle,
        children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.rightSideContainerStyle,
          children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
            shimmerStyle: styles.firstLoadingBarStyle
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
            shimmerStyle: styles.secondLoadingBarStyle
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
            shimmerStyle: styles.thirdLoadingBarStyle
          })]
        })
      }, index);
    });
  });
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    screenWidth = _Dimensions$get.width;
  var SCREEN_SIZE_RATE = screenWidth / 375;
  var styles = _reactNative.StyleSheet.create({
    containerStyle: {
      width: '100%',
      paddingHorizontal: 20,
      paddingBottom: 24,
      marginBottom: 16
    },
    rightSideContainerStyle: {
      gap: 8
    },
    firstLoadingBarStyle: {
      borderRadius: 4,
      height: 12,
      width: 120
    },
    secondLoadingBarStyle: {
      borderRadius: 4,
      height: 12,
      width: '100%'
    },
    thirdLoadingBarStyle: {
      borderRadius: 4,
      height: 12,
      width: 240 * SCREEN_SIZE_RATE
    }
  });
