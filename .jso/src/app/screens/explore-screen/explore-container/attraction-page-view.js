  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.AttractionPageView = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeWebview = _$$_REQUIRE(_dependencyMap[4]);
  var _core = _$$_REQUIRE(_dependencyMap[5]);
  var _constants = _$$_REQUIRE(_dependencyMap[6]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[7]);
  var _bottomSheetMapUnavailable = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var handleMessageType = {
    goBack: "goBack",
    directionMap: "directionMap"
  };
  var AttractionPageView = exports.AttractionPageView = function AttractionPageView(props) {
    var _props$route;
    var _ref = (props == null || (_props$route = props.route) == null ? undefined : _props$route.params) || {},
      uri = _ref.uri;
    var webViewRef = (0, _react.useRef)(null);
    var mapUnavailable = (0, _react.useRef)(null);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      mapRMFlag = _useState2[0],
      setMapRMFlag = _useState2[1];
    var navigation = (0, _core.useNavigation)();
    (0, _react.useEffect)(function () {
      var fetchAtomRMConfig = function fetchAtomRMConfig() {
        var mapFlagEnable = (0, _remoteConfig.isFlagON)(_remoteConfig.REMOTE_CONFIG_FLAGS.ATOMS_MAP);
        setMapRMFlag(mapFlagEnable);
      };
      fetchAtomRMConfig();
    }, []);
    var handleMessage = function handleMessage(message) {
      var location = {
        buildingRef: "",
        floorIndex: 0,
        latitude: 0,
        localRef: "",
        longitude: 0,
        properties: {},
        source: "MARKER",
        title: ""
      };
      try {
        var dataEvent = JSON.parse(message.nativeEvent.data);
        if (dataEvent) {
          switch (dataEvent.type) {
            case handleMessageType.goBack:
              navigation.goBack();
              break;
            case handleMessageType.directionMap:
              if (!mapRMFlag) {
                return mapUnavailable == null ? undefined : mapUnavailable.current.show();
              }
              location.localRef = dataEvent.value.properties.localRef;
              navigation.navigate(_constants.NavigationConstants.changiMap, {
                localRef: location.localRef
              });
              break;
            default:
              break;
          }
        }
      } catch (err) {}
    };
    var checkEncodeURI = function checkEncodeURI(url) {
      return /%/i.test(url);
    };
    var navigationHandler = function navigationHandler(newRequest) {
      if (newRequest != null && newRequest.url.startsWith("http://")) {
        var _webViewRef$current;
        var newUri = newRequest == null ? undefined : newRequest.url.replace(/^http:\/\//i, "https://");
        var redirectTo = 'window.location = "' + newUri + '"';
        (_webViewRef$current = webViewRef.current) == null || _webViewRef$current.injectJavaScript(redirectTo);
        return false;
      }
      return true;
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.SafeAreaView, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsx)(_reactNativeWebview.WebView, {
        ref: webViewRef,
        source: {
          uri: _reactNative.Platform.OS === "ios" && !checkEncodeURI(uri) ? encodeURI(uri) : uri
        },
        sharedCookiesEnabled: true,
        onShouldStartLoadWithRequest: navigationHandler,
        javaScriptEnabled: true,
        onLoadEnd: function onLoadEnd() {
          // TODO:
        },
        originWhitelist: ["*"],
        domStorageEnabled: true,
        onMessage: handleMessage,
        style: styles.webViewStyle
      }), (0, _jsxRuntime.jsx)(_bottomSheetMapUnavailable.BottomSheetMapUnavailable, {
        ref: mapUnavailable
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    container: {
      flex: 1
    },
    webViewStyle: {
      opacity: 0.99,
      overflow: "hidden"
    }
  });
