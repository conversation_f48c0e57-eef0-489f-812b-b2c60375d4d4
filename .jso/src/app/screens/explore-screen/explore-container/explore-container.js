  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ExploreContainer = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _core = _$$_REQUIRE(_dependencyMap[7]);
  var _exploreContainerProps = _$$_REQUIRE(_dependencyMap[8]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[9]);
  var _exploreRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[10]));
  var _exploreLoading = _$$_REQUIRE(_dependencyMap[11]);
  var _exploreChangiSection = _$$_REQUIRE(_dependencyMap[12]);
  var _exploreChangiItemProps = _$$_REQUIRE(_dependencyMap[13]);
  var _theme = _$$_REQUIRE(_dependencyMap[14]);
  var _constants = _$$_REQUIRE(_dependencyMap[15]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[16]);
  var _lodash = _$$_REQUIRE(_dependencyMap[17]);
  var _envParams = _$$_REQUIRE(_dependencyMap[18]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[19]);
  var _adobe = _$$_REQUIRE(_dependencyMap[20]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[21]));
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[22]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[23]);
  var _analytics = _$$_REQUIRE(_dependencyMap[24]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[25]);
  var _airportLandingRedux = _$$_REQUIRE(_dependencyMap[26]);
  var _exploreItemType = _$$_REQUIRE(_dependencyMap[27]);
  var _playpassWebview = _$$_REQUIRE(_dependencyMap[28]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[29]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[30]));
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[31]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[32]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var sourceSystem = _constants.SOURCE_SYSTEM.ANYTHING_IN_APP;
  var SCREEN_NAME = 'explore-container';
  var _worklet_2260807981792_init_data = {
    code: "function exploreContainerTsx1(){const{Platform,StatusBar,inset,screenHeight,heigtExploreChangiTab}=this.__closure;var _inset;const statusBarHeight=Platform.OS===\"android\"?StatusBar.currentHeight:(_inset=inset)===null||_inset===void 0?void 0:_inset.top;return{minHeight:screenHeight-heigtExploreChangiTab.value-statusBarHeight};}"
  };
  var ExploreContainer = exports.ExploreContainer = function ExploreContainer(props) {
    var scrollViewRef = props.scrollViewRef,
      onExploreContainerLayout = props.onExploreContainerLayout,
      playPassItemOnPressed = props.playPassItemOnPressed,
      showFilterModal = props.showFilterModal,
      heigtExploreChangiTab = props.heigtExploreChangiTab,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "ExploreContainer" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "ExploreContainer" : _props$accessibilityL;
    var dispatch = (0, _reactRedux.useDispatch)();
    var inset = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    var navigation = (0, _core.useNavigation)();
    var _useGeneratePlayPassU = (0, _screenHook.useGeneratePlayPassUrl)("viewExplorePlayPass"),
      getPlayPassUrl = _useGeneratePlayPassU.getPlayPassUrl;
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var exploreChangiFilter = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.exploreChangiFilter);
    var exploreSectionOffset = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.exploreSectionOffset);
    var selectedExploreCategory = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.selectedExploreCategory);
    var exploreCategoriesPayload = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.exploreCategoriesData);
    var isConnectInternet = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.isConnectInternet);
    var exploreCategories = (exploreCategoriesPayload == null ? undefined : exploreCategoriesPayload.data) || [];
    var isExploreCategoriesError = exploreCategoriesPayload == null ? undefined : exploreCategoriesPayload.hasError;
    var isExploreCategoriesLoading = exploreCategoriesPayload == null ? undefined : exploreCategoriesPayload.isLoading;
    var isExploreCategoriesEmpty = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.isExploreCategoriesEmpty);
    var _useState = (0, _react.useState)(0),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      currentSectionScrollOffset = _useState2[0],
      setCurrentSectionScrollOffset = _useState2[1];
    var _useState3 = (0, _react.useState)(selectedExploreCategory),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      currentSelectedCategory = _useState4[0],
      setCurrentSelectedCategory = _useState4[1];
    var tabSelectData = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.selectedExploreCategoryItem);
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var entryPoint = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.getPlayPassEntryPoint);
    var dataErrorMaintenanceConfig = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.GET_ERROR_MAINTENANCE));
    var listErrorMaintenance = (0, _lodash.get)(dataErrorMaintenanceConfig, "data.list", []);
    var sectionMaintenanceObj = (0, _react.useMemo)(function () {
      var data = listErrorMaintenance == null ? undefined : listErrorMaintenance.find(function (obj) {
        return obj.sectionName === _constants.sectionTagName.exploreChangi;
      });
      return data || {};
    }, [listErrorMaintenance]);
    var isFirstTabSelected = _react.default.useCallback(function () {
      return (exploreCategories == null ? undefined : exploreCategories.findIndex(function (item) {
        return (item == null ? undefined : item.text) === selectedExploreCategory;
      })) === 0;
    }, [exploreCategories, selectedExploreCategory]);
    var onContainerLayout = _react.default.useCallback(function (event) {
      var layoutOffset = (0, _screenHelper.getScreenScrollOffset)(event, _exploreContainerProps.ANDROID_SCROLL_OFFSET, _exploreContainerProps.IOS_SCROLL_OFFSET);
      setCurrentSectionScrollOffset(layoutOffset);
      onExploreContainerLayout(layoutOffset);
    }, []);
    var onSectionLayout = _react.default.useCallback(function () {
      if (currentSelectedCategory && selectedExploreCategory) {
        if (currentSelectedCategory !== selectedExploreCategory) {
          var shouldScroll = isFirstTabSelected() ? exploreSectionOffset > 0 : true;
          if (shouldScroll) {
            var scrollOffset = Math.max(currentSectionScrollOffset, exploreSectionOffset);
            (0, _screenHelper.scrollScreen)(scrollViewRef, scrollOffset, currentSectionScrollOffset >= exploreSectionOffset);
          }
        }
        setCurrentSelectedCategory(selectedExploreCategory);
      }
    }, [currentSelectedCategory, selectedExploreCategory, scrollViewRef, currentSectionScrollOffset, exploreSectionOffset]);
    var loadEventDetails = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* (eventDetailsItem, ids, itemPosition) {
        var viewerUID = yield (0, _screenHelper.getViewerUID)({
          shouldReturnNull: true
        });
        var trackPlaypassValue = `${tabSelectData == null ? undefined : tabSelectData.text} | ${(eventDetailsItem == null ? undefined : eventDetailsItem.title) || ""} | ${itemPosition} | ${viewerUID}`;
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppHomePlaypassLandingTiles, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppHomePlaypassLandingTiles, trackPlaypassValue));
        var isFirstApp = (0, _mmkvStorage.getMMKVdata)(_mmkvStorage.ENUM_STORAGE_MMKV.IS_FIRST_APP, _mmkvStorage.ENUM_STORAGE_TYPE.boolean);
        if (isFirstApp) {
          (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppHomePlaypassLandingFirst, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppHomePlaypassLandingFirst, "1"));
          (0, _mmkvStorage.setMMKVdata)(_mmkvStorage.ENUM_STORAGE_MMKV.IS_FIRST_APP, false);
        }
        if ((eventDetailsItem == null ? undefined : eventDetailsItem.source) === _exploreChangiItemProps.ExploreChangiItemType.PlayPass) {
          var _env;
          // CA30-81
          (0, _analytics.analyticsLogEvent)(_analytics.ANALYTICS_LOG_EVENT_NAME.PLAYPASS_LANDING, {
            value: eventDetailsItem == null ? undefined : eventDetailsItem.title
          });
          (0, _analytics.dtACtionLogEvent)(_analytics.ANALYTICS_LOG_EVENT_NAME.PLAYPASS_LANDING, {
            value: eventDetailsItem == null ? undefined : eventDetailsItem.title
          });
          (0, _analytics.dtBizEvent)(SCREEN_NAME, _analytics.ANALYTICS_LOG_EVENT_NAME.PLAYPASS_LANDING, 'App-Event', {
            pp_event_name: eventDetailsItem == null ? undefined : eventDetailsItem.title
          });
          if (isLoggedIn) {
            getPlayPassUrl(_constants.StateCode.PPEVENT, eventDetailsItem == null ? undefined : eventDetailsItem.packageCode, {
              entryPoint: entryPoint === _exploreItemType.PlayPassEntryPoint.LATEST_HAPPENING ? _exploreItemType.PlayPassEntryPoint.LATEST_HAPPENING : "Explore Changi",
              eventName: eventDetailsItem.title
            });
            return;
          }
          var params = `?app=3&package_code=${eventDetailsItem == null ? undefined : eventDetailsItem.packageCode}`;
          var uri = `${(_env = (0, _envParams.env)()) == null ? undefined : _env.PLAYPASS_URL_NONE_LOGIN}${params}`;
          var goToPlayPassPage = function goToPlayPassPage() {
            //@ts-ignore
            navigation.navigate(_constants.NavigationConstants.playpassWebview, {
              uri: uri,
              title: "",
              needBackButton: true,
              needCloseButton: true
            });
          };
          (0, _playpassWebview.setPPCookiesExploreChangi)({
            urlHost: uri,
            entryPoint: entryPoint === _exploreItemType.PlayPassEntryPoint.LATEST_HAPPENING ? _exploreItemType.PlayPassEntryPoint.LATEST_HAPPENING : "Explore Changi",
            eventName: eventDetailsItem.title
          }, goToPlayPassPage);
          return;
        }
        // CA30-82
        playPassItemOnPressed(eventDetailsItem);
      });
      return function loadEventDetails(_x, _x2, _x3) {
        return _ref.apply(this, arguments);
      };
    }();
    var onLoginPress = function onLoginPress() {
      //@ts-ignore
      navigation.navigate(_constants.NavigationConstants.authScreen, {
        sourceSystem: sourceSystem
      });
    };
    var onReloadData = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        if (isConnected) {
          dispatch(_exploreRedux.default.setNoInternetFilter(true));
          dispatch(_exploreRedux.default.exploreDataRequest({
            pageNumber: 1,
            category: tabSelectData == null ? undefined : tabSelectData.text,
            categoryCode: tabSelectData == null ? undefined : tabSelectData.value,
            email: profilePayload == null ? undefined : profilePayload.email,
            date: exploreChangiFilter == null ? undefined : exploreChangiFilter.date,
            locations: exploreChangiFilter == null ? undefined : exploreChangiFilter.locations
          }));
        }
      });
      return function onReloadData() {
        return _ref2.apply(this, arguments);
      };
    }();
    if (!(0, _lodash.isEmpty)(sectionMaintenanceObj) && sectionMaintenanceObj != null && sectionMaintenanceObj.enableMode) {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {});
    }
    if (!isConnectInternet) {
      return (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        header: false,
        headerBackgroundColor: "transparent",
        hideScreenHeader: false,
        visible: true,
        onReload: onReloadData,
        noInternetOverlayStyle: styles.overlayStyle
      });
    }
    var screenHeight = _reactNative.Dimensions.get('screen').height;
    var getContainerStyles = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var exploreContainerTsx1 = function exploreContainerTsx1() {
        var statusBarHeight = _reactNative.Platform.OS === "android" ? _reactNative.StatusBar.currentHeight : inset == null ? undefined : inset.top;
        return {
          minHeight: screenHeight - heigtExploreChangiTab.value - statusBarHeight
        };
      };
      exploreContainerTsx1.__closure = {
        Platform: _reactNative.Platform,
        StatusBar: _reactNative.StatusBar,
        inset: inset,
        screenHeight: screenHeight,
        heigtExploreChangiTab: heigtExploreChangiTab
      };
      exploreContainerTsx1.__workletHash = 2260807981792;
      exploreContainerTsx1.__initData = _worklet_2260807981792_init_data;
      return exploreContainerTsx1;
    }());
    (0, _react.useEffect)(function () {
      dispatch(_exploreRedux.default.exploreDataRequest({
        pageNumber: 1,
        category: tabSelectData == null ? undefined : tabSelectData.text,
        categoryCode: tabSelectData == null ? undefined : tabSelectData.value,
        email: profilePayload == null ? undefined : profilePayload.email,
        date: exploreChangiFilter == null ? undefined : exploreChangiFilter.date,
        locations: exploreChangiFilter == null ? undefined : exploreChangiFilter.locations
      }));
    }, [isLoggedIn]);
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [!isExploreCategoriesError && !isExploreCategoriesEmpty && (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
        onLayout: onContainerLayout,
        style: getContainerStyles,
        children: (0, _jsxRuntime.jsx)(_exploreChangiSection.ExploreChangiSection, {
          onLayout: onSectionLayout,
          onSelected: loadEventDetails,
          onLoginPress: onLoginPress,
          testID: `${testID}__ExploreChangiSection}`,
          accessibilityLabel: `${accessibilityLabel}__ExploreChangiSection}`,
          showFilterModal: showFilterModal
        })
      }), isExploreCategoriesLoading && (0, _jsxRuntime.jsx)(_exploreLoading.ExploreLoading, {})]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    overlayStyle: {
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1,
      marginBottom: 80
    }
  });
