  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _objectWithoutProperties2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _reactNativeCalendars = _$$_REQUIRE(_dependencyMap[8]);
  var _lodash = _$$_REQUIRE(_dependencyMap[9]);
  var _icons = _$$_REQUIRE(_dependencyMap[10]);
  var _dayItem = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  var _excluded = ["addMonth"];
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var dayNames = ["SUN", "MON", "TUE", "WED", "THU", "FRI", "SAT"];
  var CalendarFilter = function CalendarFilter(propsScreen) {
    var filterDate = propsScreen.filterDate,
      setFilterDate = propsScreen.setFilterDate,
      containerStyle = propsScreen.containerStyle,
      _propsScreen$initialM = propsScreen.initialMinDate,
      initialMinDate = _propsScreen$initialM === undefined ? (0, _moment.default)().format("YYYY-MM-DD") : _propsScreen$initialM;
    var renderHeader = function renderHeader(_ref) {
      var addMonth = _ref.addMonth,
        props = (0, _objectWithoutProperties2.default)(_ref, _excluded);
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.headerCalendar,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.titleCalendar,
            text: (0, _moment.default)((0, _lodash.get)(props, "month.0")).format("MMM YYYY")
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: function onPress() {
              return addMonth(-1);
            },
            style: styles.previousBtnStyles,
            children: (0, _jsxRuntime.jsx)(_icons.PreviosArrow, {
              height: 17,
              width: 10
            })
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: function onPress() {
              return addMonth(1);
            },
            style: styles.nextBtnStyles,
            children: (0, _jsxRuntime.jsx)(_icons.NextArrow, {
              height: 17,
              width: 10
            })
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.dayNameContainer,
          children: dayNames.map(function (item, index) {
            return (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: styles.dayNameItem,
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                text: item,
                style: styles.dayNameText,
                numberOfLines: 1
              })
            }, `${index}`);
          })
        })]
      });
    };
    var initialDate = (0, _react.useMemo)(function () {
      if ((0, _lodash.isEmpty)(filterDate)) {
        return (0, _moment.default)().format("YYYY-MM-DD");
      }
      return (0, _moment.default)(filterDate).format("YYYY-MM-DD");
    }, [filterDate]);
    function isNotBetween(dateString) {
      var dateCheck = (0, _moment.default)(dateString);
      var minDate = initialMinDate;
      var maxDate = (0, _moment.default)().add(1, "years").format("YYYY-MM-DD");
      return minDate != null && dateCheck.isBefore(minDate, "day") || maxDate != null && dateCheck.isAfter(maxDate, "day");
    }
    var checkPast = function checkPast(dateString) {
      return isNotBetween(dateString);
    };
    var setActiveDate = function setActiveDate(dateString) {
      if (dateString) {
        setFilterDate(dateString);
      }
    };
    var dayComponent = function dayComponent(_ref2) {
      var date = _ref2.date;
      return (0, _jsxRuntime.jsx)(_dayItem.default, {
        day: date == null ? undefined : date.day,
        isPast: checkPast(date == null ? undefined : date.dateString),
        dateString: date == null ? undefined : date.dateString,
        isActive: filterDate === (date == null ? undefined : date.dateString),
        onPress: setActiveDate
      });
    };
    return (0, _jsxRuntime.jsx)(_reactNativeCalendars.Calendar, {
      style: containerStyle || {},
      initialDate: initialDate,
      minDate: initialMinDate,
      maxDate: (0, _moment.default)().add(1, "years").format("YYYY-MM-DD"),
      customHeader: renderHeader,
      dayComponent: dayComponent,
      hideExtraDays: true,
      hideArrows: true
    });
  };
  var _default = exports.default = CalendarFilter;
  var styles = _reactNative2.StyleSheet.create({
    dayNameContainer: {
      flexDirection: "row"
    },
    dayNameItem: {
      alignItems: "center",
      flex: 1,
      justifyContent: "center"
    },
    dayNameText: Object.assign({}, _text.presets.fieldLabel, {
      color: _theme.color.palette.shortNameColor,
      fontWeight: "900",
      lineHeight: 18
    }),
    headerCalendar: {
      alignItems: "center",
      flexDirection: "row",
      height: 44,
      paddingHorizontal: 8
    },
    nextBtnStyles: {
      alignItems: "center",
      height: "100%",
      justifyContent: "center",
      marginLeft: 8,
      width: 24
    },
    previousBtnStyles: {
      alignItems: "center",
      height: "100%",
      justifyContent: "center",
      marginRight: 8,
      width: 24
    },
    titleCalendar: Object.assign({
      flex: 1
    }, _text.presets.h3, {
      color: _theme.color.palette.almostBlackGrey
    })
  });
