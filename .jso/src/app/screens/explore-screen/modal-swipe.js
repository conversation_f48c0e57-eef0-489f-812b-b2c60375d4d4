  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _baseImageBackground = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _Dimensions$get = _reactNative2.Dimensions.get("window"),
    height = _Dimensions$get.height;
  var SwipeUpDownModal = function SwipeUpDownModal(props) {
    var TIMING_CONFIG = {
      duration: props.duration ? props.duration : 450,
      easing: _reactNative2.Easing.inOut(_reactNative2.Easing.ease)
    };
    var dispatch = (0, _reactRedux.useDispatch)();
    var pan = (0, _react.useRef)(new _reactNative2.Animated.ValueXY()).current;
    var _useState = (0, _react.useState)(!!props.DisableHandAnimation),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isAnimating = _useState2[0],
      setIsAnimating = _useState2[1];
    var animatedValueX = 0;
    var animatedValueY = 0;
    var panResponder = (0, _react.useRef)(_reactNative2.PanResponder.create({
      // Ask to be the responder:
      onStartShouldSetPanResponder: function onStartShouldSetPanResponder() {
        return false;
      },
      onStartShouldSetPanResponderCapture: function onStartShouldSetPanResponderCapture() {
        return false;
      },
      onMoveShouldSetPanResponder: function onMoveShouldSetPanResponder(_evt, gestureState) {
        if (isAnimating) {
          return false;
        }
        if (gestureState.dy > 22) {
          return true;
        }
        return false;
      },
      onPanResponderGrant: function onPanResponderGrant() {
        pan.setOffset({
          x: animatedValueX,
          y: animatedValueY
        });
        pan.setValue({
          x: 0,
          y: 0
        }); // Initial value
      },
      onPanResponderMove: function onPanResponderMove(_evt, gestureState) {
        if (gestureState.dy > 0) {
          pan.setValue({
            x: 0,
            y: gestureState.dy
          });
        }
      },
      onPanResponderRelease: function onPanResponderRelease(_evt, gestureState) {
        if (gestureState.dy > 0 && gestureState.vy > 0) {
          if (gestureState.vy <= -0.7 || gestureState.dy <= -100) {
            setIsAnimating(true);
            _reactNative2.Animated.timing(pan, Object.assign({
              toValue: {
                x: 0,
                y: -height
              }
            }, TIMING_CONFIG, {
              useNativeDriver: false
            })).start(function () {
              setIsAnimating(false);
              props.onClose();
            });
          } else if (gestureState.vy >= 0.5 || gestureState.dy >= 100) {
            setIsAnimating(true);
            _reactNative2.Animated.timing(pan, Object.assign({
              toValue: {
                x: 0,
                y: height
              }
            }, TIMING_CONFIG, {
              useNativeDriver: false
            })).start(function () {
              setIsAnimating(false);
              props.onClose();
            });
          } else {
            setIsAnimating(true);
            _reactNative2.Animated.spring(pan, {
              toValue: 0,
              useNativeDriver: false
            }).start(function () {
              setIsAnimating(false);
            });
          }
        } else {
          setIsAnimating(true);
          _reactNative2.Animated.spring(pan, {
            toValue: 0,
            useNativeDriver: false
          }).start(function () {
            setIsAnimating(false);
          });
        }
      }
    })).current;
    (0, _react.useEffect)(function () {
      if (props.modalVisible) {
        animatedValueX = 0;
        animatedValueY = 0;
        pan.setOffset({
          x: animatedValueX,
          y: animatedValueY
        });
        pan.setValue({
          x: 0,
          y: props.OpenModalDirection === "up" ? -height : height
        }); // Initial value
        pan.x.addListener(function (value) {
          return animatedValueX = value.value;
        });
        pan.y.addListener(function (value) {
          return animatedValueY = value.value;
        });
      }
    }, [props.modalVisible]);
    (0, _react.useEffect)(function () {
      if (props.PressToanimate) {
        setIsAnimating(true);
        _reactNative2.Animated.timing(pan, Object.assign({
          toValue: {
            x: 0,
            y: props.PressToanimateDirection === "up" ? -height : height
          }
        }, TIMING_CONFIG, {
          useNativeDriver: false
        })).start(function () {
          setIsAnimating(false);
          props.onClose();
        });
      }
    }, [props.PressToanimate]);
    var handleGetStyle = function handleGetStyle(opacity) {
      return [[styles.container, {
        transform: [{
          translateX: pan.x
        }, {
          translateY: pan.y
        }],
        opacity: opacity
      }, [props.HeaderStyle]]];
    };
    var handleGetStyleBody = function handleGetStyleBody(opacity) {
      return [[styles.background, {
        transform: [{
          translateX: pan.x
        }, {
          translateY: pan.y
        }],
        opacity: opacity
      }], [props.ContentModalStyle]];
    };
    var handleMainBodyStyle = function handleMainBodyStyle(opacity) {
      return [[styles.ContainerModal, {
        opacity: opacity
      }], [props.MainContainerModal]];
    };
    var interpolateBackgroundOpacity = pan.y.interpolate({
      inputRange: [-height, 0, height],
      outputRange: [props.fade ? 0 : 1, 1, props.fade ? 0 : 1]
    });
    return (0, _jsxRuntime.jsx)(_reactNative2.Modal, {
      animationType: "none",
      transparent: true,
      visible: props.modalVisible,
      onDismiss: function onDismiss() {
        (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
      },
      onShow: function onShow() {
        (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(true);
        setIsAnimating(true);
        _reactNative2.Animated.timing(pan, Object.assign({}, TIMING_CONFIG, {
          toValue: {
            x: 0,
            y: 0
          },
          useNativeDriver: false
        })).start(function () {
          setIsAnimating(false);
        });
      },
      onRequestClose: props.onRequestClose,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.Animated.View, {
        style: handleMainBodyStyle(interpolateBackgroundOpacity),
        children: [(0, _jsxRuntime.jsx)(_reactNative2.Animated.View, {
          style: handleGetStyleBody(interpolateBackgroundOpacity),
          children: (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
            onPress: function onPress() {
              return _reactNative2.Keyboard.dismiss();
            },
            style: styles.TouchWithoutFeedBack,
            children: (0, _jsxRuntime.jsx)(_baseImageBackground.default, {
              source: props.ImageBackgroundModal ? props.ImageBackgroundModal : "",
              style: styles.ImageBackground,
              imageStyle: props.ImageBackgroundModalStyle ? props.ImageBackgroundModalStyle : {},
              children: props.ContentModal
            })
          })
        }), (0, _jsxRuntime.jsx)(_reactNative2.Animated.View, Object.assign({
          style: handleGetStyle(interpolateBackgroundOpacity)
        }, panResponder.panHandlers, {
          children: (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
            children: props.HeaderContent ? props.HeaderContent : (0, _jsxRuntime.jsx)(_reactNative2.View, {})
          })
        }))]
      })
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    ContainerModal: {
      backgroundColor: _theme.color.palette.overlayColor,
      flex: 1
    },
    ImageBackground: {
      height: "100%",
      width: "100%"
    },
    TouchWithoutFeedBack: {
      flex: 1
    },
    background: {
      flex: 1,
      justifyContent: "flex-end",
      marginTop: 55,
      opacity: 0
    },
    container: {
      marginTop: 50,
      position: "absolute",
      width: "100%"
    }
  });
  var _default = exports.default = SwipeUpDownModal;
