  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.handleGTTDIcon = exports.GTTDType = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _icons = _$$_REQUIRE(_dependencyMap[3]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  var styles = _reactNative.StyleSheet.create({
    gttdOtherItemImageStyle: {
      width: 40,
      height: 40
    }
  });
  var GTTDType = exports.GTTDType = /*#__PURE__*/function (GTTDType) {
    GTTDType["phv"] = "phv";
    GTTDType["taxi"] = "taxi";
    GTTDType["gtc"] = "gtc";
    GTTDType["generic"] = "generic";
    return GTTDType;
  }({});
  var handleGTTDIcon = exports.handleGTTDIcon = function handleGTTDIcon(type) {
    if (!type) return null;
    switch (true) {
      case type.includes(GTTDType.generic):
      case type.includes(GTTDType.gtc):
        return (0, _jsxRuntime.jsx)(_baseImage.default, {
          source: _icons.GTTDLimousine,
          style: styles.gttdOtherItemImageStyle
        });
      case type.includes(GTTDType.taxi):
        return (0, _jsxRuntime.jsx)(_baseImage.default, {
          source: _icons.GTTDTaxi,
          style: styles.gttdOtherItemImageStyle
        });
      case type.includes(GTTDType.phv):
        return (0, _jsxRuntime.jsx)(_baseImage.default, {
          source: _icons.GTTDCar,
          style: styles.gttdOtherItemImageStyle
        });
      default:
        return null;
    }
  };
