  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.TabBarContainer = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _color = _$$_REQUIRE(_dependencyMap[6]);
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _tabBar = _$$_REQUIRE(_dependencyMap[8]);
  var _i18n = _$$_REQUIRE(_dependencyMap[9]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[10]);
  var _exploreRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[11]));
  var _errorCloud = _$$_REQUIRE(_dependencyMap[12]);
  var _exploreDataItem = _$$_REQUIRE(_dependencyMap[13]);
  var _exploreHelper = _$$_REQUIRE(_dependencyMap[14]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[15]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[16]);
  var _adobe = _$$_REQUIRE(_dependencyMap[17]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[18]));
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[19]);
  var _lodash = _$$_REQUIRE(_dependencyMap[20]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[21]);
  var _constants = _$$_REQUIRE(_dependencyMap[22]);
  var _errorUnplannedMaintenance = _$$_REQUIRE(_dependencyMap[23]);
  var _icons = _$$_REQUIRE(_dependencyMap[24]);
  var _text2 = _$$_REQUIRE(_dependencyMap[25]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[26]));
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[27]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[28]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var errorContainerStyle = {
    height: 260,
    marginTop: 32,
    marginBottom: 170
  };
  var safeAreaView = {
    backgroundColor: _color.color.palette.lightestGrey
  };
  var TabBarContainer = exports.TabBarContainer = (0, _react.memo)(function (props) {
    var scrollOffset = props.scrollOffset,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "TabBarContainer" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "TabBarContainer" : _props$accessibilityL,
      setHeigtExploreChangiTab = props.setHeigtExploreChangiTab,
      showFilterModal = props.showFilterModal;
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("EXPLORE_CHANGI"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var exploreCategoriesPayload = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.exploreCategoriesData);
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var exploreCategories = (exploreCategoriesPayload == null ? undefined : exploreCategoriesPayload.data) || [];
    var selectedTab = (0, _exploreDataItem.getSelectedCategoryItemText)(exploreCategories);
    var errorData = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var isConnectInternet = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.isConnectInternet);
    var exploreChangiFilter = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.exploreChangiFilter);
    var exploreChangiLocation = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.exploreChangiLocation) || [];
    var dataErrorMaintenanceConfig = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.GET_ERROR_MAINTENANCE));
    var listErrorMaintenance = (0, _lodash.get)(dataErrorMaintenanceConfig, "data.list", []);
    var sectionMaintenanceObj = (0, _react.useMemo)(function () {
      var data = listErrorMaintenance == null ? undefined : listErrorMaintenance.find(function (obj) {
        return obj.sectionName === _constants.sectionTagName.exploreChangi;
      });
      return data || {};
    }, [listErrorMaintenance]);
    var loadExploreCategories = _react.default.useCallback(function () {
      dispatch(_exploreRedux.default.exploreCategoriesRequest({
        pageNumber: 1,
        category: "All",
        categoryCode: ["all"],
        email: profilePayload == null ? undefined : profilePayload.email,
        date: exploreChangiFilter == null ? undefined : exploreChangiFilter.date,
        locations: exploreChangiFilter == null ? undefined : exploreChangiFilter.locations
      }));
    }, []);
    (0, _react.useEffect)(function () {
      if (exploreCategoriesPayload !== null) return;
      loadExploreCategories();
    }, [loadExploreCategories, exploreCategoriesPayload]);
    var dateFilter = (0, _react.useMemo)(function () {
      if (!(0, _lodash.isEmpty)(exploreChangiFilter == null ? undefined : exploreChangiFilter.date)) {
        var currentYear = new Date().getFullYear();
        var filterYear = (0, _moment.default)(new Date(exploreChangiFilter == null ? undefined : exploreChangiFilter.date)).year();
        var formatDate = currentYear === filterYear ? "ddd, DD MMM" : "ddd, DD MMM YYYY";
        return (0, _moment.default)(new Date(exploreChangiFilter == null ? undefined : exploreChangiFilter.date)).format(formatDate);
      } else {
        return (0, _i18n.translate)("exploreScreen.allDates");
      }
    }, [exploreChangiFilter]);
    var locationFilter = (0, _react.useMemo)(function () {
      if ((0, _lodash.isEmpty)(exploreChangiFilter == null ? undefined : exploreChangiFilter.locations)) {
        return (0, _i18n.translate)("exploreScreen.allLocation");
      } else {
        var data = exploreChangiLocation == null ? undefined : exploreChangiLocation.filter(function (loc) {
          var _exploreChangiFilter$;
          return exploreChangiFilter == null || (_exploreChangiFilter$ = exploreChangiFilter.locations) == null ? undefined : _exploreChangiFilter$.includes(loc.tagName);
        });
        if (data.length === exploreChangiLocation.length) {
          return (0, _i18n.translate)("exploreScreen.allLocation");
        }
        return data.map(function (item) {
          return item == null ? undefined : item.tagTitle;
        }).join(", ");
      }
    }, [exploreChangiFilter]);
    var onExploreCategorySelected = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* (item) {
        dispatch(_exploreRedux.default.exploreDataReset());
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppHomePlaypassLandingCategory, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppHomePlaypassLandingCategory, (item == null ? undefined : item.text) || ""));
        var isFirstApp = (0, _mmkvStorage.getMMKVdata)(_mmkvStorage.ENUM_STORAGE_MMKV.IS_FIRST_APP, _mmkvStorage.ENUM_STORAGE_TYPE.boolean);
        if (isFirstApp) {
          (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppHomePlaypassLandingFirst, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppHomePlaypassLandingFirst, "1"));
          (0, _mmkvStorage.setMMKVdata)(_mmkvStorage.ENUM_STORAGE_MMKV.IS_FIRST_APP, false);
        }
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        dispatch(_exploreRedux.default.setNoInternetFilter(isConnected));
        (0, _exploreHelper.navigateToTab)(selectedTab, scrollOffset, item.text, dispatch);
        if (isConnected) {
          var _exploreChangiFilter$2;
          dispatch(_exploreRedux.default.exploreDataRequest({
            pageNumber: 1,
            category: item.text,
            categoryCode: item.value,
            email: profilePayload == null ? undefined : profilePayload.email,
            date: exploreChangiFilter == null ? undefined : exploreChangiFilter.date,
            locations: exploreChangiFilter == null || (_exploreChangiFilter$2 = exploreChangiFilter.locations) == null ? undefined : _exploreChangiFilter$2.map(function (newItem) {
              return newItem.toUpperCase();
            })
          }));
        }
      });
      return function onExploreCategorySelected(_x) {
        return _ref.apply(this, arguments);
      };
    }();
    if (exploreCategoriesPayload != null && exploreCategoriesPayload.hasError && isConnectInternet) {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.viewContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.padingLeft24,
          preset: "h4",
          children: (0, _i18n.translate)("exploreScreen.exploreChangi")
        }), (0, _jsxRuntime.jsx)(_errorCloud.ErrorCloudComponent, {
          errorData: errorData,
          style: errorContainerStyle,
          onPress: loadExploreCategories,
          testID: `${testID}__ErrorCloudComponent`,
          accessibilityLabel: `${accessibilityLabel}__ErrorCloudComponent`
        })]
      });
    }
    var handleButtonOnPress = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* (navigationObj, redirect) {
        var type = navigationObj.type,
          value = navigationObj.value;
        if (!type || !value) return;
        handleNavigation(type, value, redirect);
      });
      return function handleButtonOnPress(_x2, _x3) {
        return _ref2.apply(this, arguments);
      };
    }();
    if (!(0, _lodash.isEmpty)(sectionMaintenanceObj) && sectionMaintenanceObj != null && sectionMaintenanceObj.enableMode) {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.noInternetContainer,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          preset: "h4",
          style: styles.maintenaceErrorTitle,
          children: (0, _i18n.translate)("exploreScreen.exploreChangi")
        }), (0, _jsxRuntime.jsx)(_errorUnplannedMaintenance.ErrorUnplannedMaintenance, {
          header: sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.header,
          subHeader: sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.subHeader,
          icon: sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.icon,
          buttonLabel: sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.buttonLabel,
          buttonLabel2: sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.buttonLabel2,
          onFirstButtonPress: function onFirstButtonPress() {
            return handleButtonOnPress(sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.navigationFirst, sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.redirectFirst);
          },
          onSecondButtonPress: function onSecondButtonPress() {
            return handleButtonOnPress(sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.navigationSecond, sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.redirectSecond);
          },
          testID: `${testID}__ErrorUnplannedMaintenance`,
          accessibilityLabel: `${accessibilityLabel}__ErrorUnplannedMaintenance`
        })]
      });
    }
    if (!isConnectInternet) {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.noInternetContainer,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "h4",
          style: styles.padingLeft24,
          children: (0, _i18n.translate)("exploreScreen.exploreChangi")
        })
      });
    }
    return (0, _jsxRuntime.jsx)(_reactNativeSafeAreaContext.SafeAreaView, {
      style: safeAreaView,
      edges: ["top"],
      onLayout: setHeigtExploreChangiTab,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.viewContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          preset: "h4",
          style: styles.padingLeft24,
          children: (0, _i18n.translate)("exploreScreen.exploreChangi")
        }), (0, _jsxRuntime.jsx)(_tabBar.TabBar, {
          tabBarData: exploreCategories,
          contentStyle: styles.padingLeft24,
          onTabBarItemSelected: onExploreCategorySelected,
          testID: `${testID}__TabBar`,
          accessibilityLabel: `${accessibilityLabel}__TabBar`
        }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
          style: styles.filterContainer,
          onPress: showFilterModal,
          testID: `${testID}__TouchableOpacityLocations`,
          accessibilityLabel: `${accessibilityLabel}__TouchableOpacityLocations`,
          children: [(0, _jsxRuntime.jsx)(_icons.Filter, {
            color: _color.color.palette.lightPurple,
            height: 15.5,
            width: 18
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.rightFilterContainer,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.filterTextStyles,
              text: (0, _i18n.translate)("exploreScreen.locationFilter")
            }), (0, _jsxRuntime.jsxs)(_text.Text, {
              style: styles.locationTextStyles,
              numberOfLines: 1,
              children: [dateFilter, " at ", locationFilter]
            })]
          })]
        })]
      })
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    filterContainer: {
      alignItems: "center",
      flexDirection: "row",
      marginBottom: 12,
      marginHorizontal: 24,
      marginTop: 24
    },
    filterTextStyles: Object.assign({}, _text2.presets.caption2Regular, {
      color: _color.color.palette.darkestGrey
    }),
    locationTextStyles: Object.assign({}, _text2.presets.bodyTextBold, {
      color: _color.color.palette.lightPurple,
      marginTop: 4
    }),
    maintenaceErrorTitle: {
      marginBottom: 30,
      paddingHorizontal: 24
    },
    noInternetContainer: {
      backgroundColor: _color.color.palette.lightestGrey,
      marginBottom: 50,
      paddingTop: _reactNative2.Platform.OS === "ios" ? 50 : 32
    },
    padingLeft24: {
      paddingLeft: 24
    },
    rightFilterContainer: {
      flex: 1,
      marginLeft: 11
    },
    viewContainerStyle: {
      backgroundColor: _color.color.palette.lightestGrey
    }
  });
