  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _theme = _$$_REQUIRE(_dependencyMap[2]);
  var _constants = _$$_REQUIRE(_dependencyMap[3]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _styles = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  var LoadingView = function LoadingView() {
    var _ref;
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: (_ref = [1, 2, 3, 4]) == null ? undefined : _ref.map(function (index) {
        return (0, _jsxRuntime.jsx)(CardLoading, {}, index);
      })
    });
  };
  var _default = exports.default = LoadingView;
  var CardLoading = function CardLoading() {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: _styles.loadingScreen.containerLoading,
      children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: _styles.lightGreyLoadingColors,
        shimmerStyle: _styles.loadingScreen.imageStyles
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: _styles.loadingScreen.textContainerStyles,
        children: skeletonLayout.map(function (item, index) {
          return (0, _jsxRuntime.jsx)(_reactNative.View, {
            children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: _styles.lighterGreyLoadingColors,
              shimmerStyle: item
            })
          }, index);
        })
      })]
    });
  };
  var skeletonLayout = [{
    backgroundColor: _theme.color.palette.lighterGrey,
    width: 41,
    height: 13,
    borderRadius: 4
  }, {
    backgroundColor: _theme.color.palette.lighterGrey,
    width: 115,
    height: 13,
    marginTop: 14,
    borderRadius: 4
  }, {
    backgroundColor: _theme.color.palette.lighterGrey,
    width: 56,
    height: 13,
    marginTop: 13,
    borderRadius: 4
  }, {
    backgroundColor: _theme.color.palette.lighterGrey,
    width: 56,
    height: 13,
    marginTop: 13,
    borderRadius: 4,
    marginBottom: 20
  }];
