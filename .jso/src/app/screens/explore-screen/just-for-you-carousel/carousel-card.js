  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.Card = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _utils = _$$_REQUIRE(_dependencyMap[5]);
  var _constants = _$$_REQUIRE(_dependencyMap[6]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[8]);
  var _styles = _$$_REQUIRE(_dependencyMap[9]);
  var _adobe = _$$_REQUIRE(_dependencyMap[10]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _shopScreen = _$$_REQUIRE(_dependencyMap[12]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[13]);
  var _deeplinkParameter = _$$_REQUIRE(_dependencyMap[14]);
  var _pageConfigSaga = _$$_REQUIRE(_dependencyMap[15]);
  var _enum = _$$_REQUIRE(_dependencyMap[16]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[17]);
  var titleCase = function titleCase(str) {
    if (!str) {
      return str;
    }
    var splitStr = str.toLowerCase().split(" ");
    for (var i = 0; i < splitStr.length; i++) {
      if (splitStr[i].charAt(0) === "(") {
        splitStr[i] = splitStr[i].charAt(0) + splitStr[i].charAt(1).toUpperCase() + splitStr[i].substring(2);
      } else {
        splitStr[i] = splitStr[i].charAt(0).toUpperCase() + splitStr[i].substring(1);
      }
    }
    return splitStr.join(" ");
  };
  var Card = exports.Card = function Card(_ref) {
    var _item$salePrice, _item$mainPrice;
    var item = _ref.item,
      navigation = _ref.navigation,
      testID = _ref.testID,
      accessibilityLabel = _ref.accessibilityLabel,
      index = _ref.index,
      userId = _ref.userId,
      _ref$isNewStyle = _ref.isNewStyle,
      isNewStyle = _ref$isNewStyle === undefined ? false : _ref$isNewStyle;
    var salePrice = (item == null || (_item$salePrice = item.salePrice) == null ? undefined : _item$salePrice.find(function (element) {
      return (element == null ? undefined : element.currency) === "SGD";
    })) || (item == null ? undefined : item.salePrice[0]);
    var mainPrice = (item == null || (_item$mainPrice = item.mainPrice) == null ? undefined : _item$mainPrice.find(function (element) {
      return (element == null ? undefined : element.currency) === "SGD";
    })) || (item == null ? undefined : item.mainPrice[0]);
    var productName = isNewStyle ? titleCase(item == null ? undefined : item.productName) : item == null ? undefined : item.productName;
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("CARD_CAROUSEL_EXPLORE_LANDING"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var handleNavigateCSMIShopchangi = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* (url) {
        var ecid = yield (0, _adobe.getExperienceCloudId)();
        var target = (0, _deeplinkParameter.getISCInputParamsDeepLink)(url, ".com/content/cagishop/sg");
        var payload = {
          stateCode: _constants.StateCode.ISHOPCHANGI,
          input: Object.assign({}, target, {
            ecid: ecid
          })
        };
        try {
          var response = yield (0, _pageConfigSaga.getDeepLinkV2)(payload, true);
          if (response != null && response.redirectUri) {
            navigation.navigate(_constants.NavigationConstants.playpassWebview, {
              uri: response == null ? undefined : response.redirectUri,
              needBackButton: true,
              needCloseButton: true,
              headerType: _enum.WebViewHeaderTypes.default,
              basicAuthCredential: response == null ? undefined : response.basicAuth
            });
          } else {
            navigation.navigate(_constants.NavigationConstants.webview, {
              uri: url
            });
          }
        } catch (error) {
          navigation.navigate(_constants.NavigationConstants.webview, {
            uri: url
          });
        }
      });
      return function handleNavigateCSMIShopchangi(_x) {
        return _ref2.apply(this, arguments);
      };
    }();
    var handlePress = function handlePress() {
      var _item$salePrice2, _item$mainPrice2;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppShopTenantPromoSwimlane, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppShopTenantPromoSwimlane, `Explore | ${index + 1} | ${item == null ? undefined : item.productName} | ${item != null && item.brandName ? item == null ? undefined : item.brandName : "-"} | ${(0, _shopScreen.formatPrice)((item == null || (_item$salePrice2 = item.salePrice) == null || (_item$salePrice2 = _item$salePrice2[0]) == null ? undefined : _item$salePrice2.value) || (item == null || (_item$mainPrice2 = item.mainPrice) == null || (_item$mainPrice2 = _item$mainPrice2[0]) == null ? undefined : _item$mainPrice2.value))} | ${userId ? userId : "-"}`));
      if (!isNewStyle) {
        var _ref3 = item || {},
          navigationType = _ref3.navigationType,
          navigationValue = _ref3.navigationValue,
          redirect = _ref3.redirect;
        if (!navigationType || !navigationValue) return;
        handleNavigation(navigationType, navigationValue, redirect);
        return;
      } else {
        handleNavigateCSMIShopchangi(item == null ? undefined : item.url);
      }
    };
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: _styles.cardStyles.containerStyle,
      children: (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        onPress: handlePress,
        children: [isNewStyle ? (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _styles.cardStyles.containerImage,
          children: (0, _jsxRuntime.jsx)(_baseImage.default, {
            style: _styles.cardStyles.imageStyle,
            source: {
              uri: item == null ? undefined : item.imageUrl
            },
            resizeMode: "contain"
          })
        }) : (0, _jsxRuntime.jsx)(_baseImage.default, {
          style: _styles.cardStyles.imageStyle,
          source: {
            uri: item == null ? undefined : item.imageUrl
          },
          resizeMode: "cover"
        }), (item == null ? undefined : item.ribbonText) && (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _styles.cardStyles.ribbonStyle,
          children: (0, _jsxRuntime.jsx)(_text.Text, Object.assign({
            preset: "XSmallBold",
            text: item == null ? undefined : item.ribbonText,
            style: _styles.cardStyles.textRibbonStyle
          }, (0, _utils.accessibility)({
            testID: `${testID}__ribbonStyle`,
            accessibilityLabel: `${accessibilityLabel}__ribbonStyle`,
            OS: _reactNative2.Platform.OS
          })))
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _styles.cardStyles.textContainerStyles,
          children: [item != null && item.brandName ? (0, _jsxRuntime.jsx)(_text.Text, Object.assign({
            text: item == null ? undefined : item.brandName,
            preset: "caption2Bold",
            numberOfLines: 1,
            style: _styles.cardStyles.tenantNameStyle
          }, (0, _utils.accessibility)({
            testID: `${testID}__brandName`,
            accessibilityLabel: `${accessibilityLabel}__brandName`,
            OS: _reactNative2.Platform.OS
          }))) : (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "caption2Bold",
            numberOfLines: 1,
            style: _styles.cardStyles.tenantNameStyle
          }), (0, _jsxRuntime.jsx)(_text.Text, Object.assign({
            text: productName,
            preset: "caption1Regular",
            numberOfLines: 2,
            style: _styles.cardStyles.productNameStyle
          }, (0, _utils.accessibility)({
            testID: `${testID}__productName`,
            accessibilityLabel: `${accessibilityLabel}__productName`,
            OS: _reactNative2.Platform.OS
          }))), salePrice != null && salePrice.value ? (0, _jsxRuntime.jsx)(_text.Text, Object.assign({
            text: salePrice == null ? undefined : salePrice.value,
            preset: "subTitleBold",
            style: _styles.cardStyles.salePriceStyle
          }, (0, _utils.accessibility)({
            testID: `${testID}__salePrice`,
            accessibilityLabel: `${accessibilityLabel}__salePrice`,
            OS: _reactNative2.Platform.OS
          }))) : null, (0, _jsxRuntime.jsx)(_text.Text, Object.assign({
            text: mainPrice == null ? undefined : mainPrice.value,
            style: salePrice != null && salePrice.value ? _styles.cardStyles.originalPricePaddingBottomStyle : _styles.cardStyles.salePriceStyle
          }, (0, _utils.accessibility)({
            testID: `${testID}__mainPrice`,
            accessibilityLabel: `${accessibilityLabel}__mainPrice`,
            OS: _reactNative2.Platform.OS
          })))]
        })]
      })
    });
  };
  var _default = exports.default = _react.default.memo(Card);
