  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.mainScreenStyles = exports.loadingScreen = exports.lighterGreyLoadingColors = exports.lightGreyLoadingColors = exports.commonStyleText = exports.cardStyles = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var lightGreyLoadingColors = exports.lightGreyLoadingColors = [_theme.color.palette.lightGrey, _theme.color.background, _theme.color.palette.lightGrey];
  var lighterGreyLoadingColors = exports.lighterGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var commonStyleText = exports.commonStyleText = _reactNative.StyleSheet.create({
    boldText: {
      fontFamily: _theme.typography.bold,
      fontSize: 18,
      fontStyle: "normal",
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      letterSpacing: 1.08,
      lineHeight: 24
    }
  });
  var mainScreenStyles = exports.mainScreenStyles = _reactNative.StyleSheet.create({
    borderBottomRadius: {
      borderBottomLeftRadius: 40
    },
    container: {
      paddingVertical: 32
    },
    containerWithSeparator: {
      marginBottom: 50,
      paddingVertical: 32
    },
    contentContainer: {
      flexDirection: "row",
      paddingLeft: 24
    },
    errorContainer: {
      marginLeft: 24,
      marginTop: 34
    },
    title: {
      color: _theme.color.palette.whiteGrey,
      marginBottom: 12,
      marginLeft: 24
    }
  });
  var loadingScreen = exports.loadingScreen = _reactNative.StyleSheet.create({
    containerLoading: {
      marginRight: 8
    },
    imageStyles: {
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      height: 156,
      width: 156
    },
    textContainerStyles: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderBottomLeftRadius: 16,
      borderBottomRightRadius: 16,
      padding: 12
    },
    titleLoading: {
      borderRadius: 4,
      height: 18,
      marginBottom: 12,
      marginLeft: 24,
      width: 290
    }
  });
  var cardStyles = exports.cardStyles = _reactNative.StyleSheet.create({
    containerStyle: {
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      marginRight: 8,
      width: 164
    },
    containerImage: {
      backgroundColor: '#fff',
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16
    },
    imageStyle: {
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      height: 151,
      width: 164
    },
    originalPricePaddingBottomStyle: Object.assign({}, _text.presets.caption1Regular, {
      marginBottom: 13,
      marginTop: 4,
      textDecorationLine: "line-through"
    }),
    productNameStyle: {
      color: _theme.color.palette.almostBlackGrey,
      marginTop: 8
    },
    ribbonStyle: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.baseBlue,
      borderRadius: 4,
      height: 24,
      justifyContent: "center",
      left: 9,
      minWidth: 61,
      paddingHorizontal: 8,
      position: "absolute",
      top: 8
    },
    salePriceStyle: Object.assign({}, _text.presets.subTitleBold, {
      color: _theme.color.palette.almostBlackGrey,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      marginTop: 4
    }),
    tenantNameStyle: {
      color: _theme.color.palette.gradientColor1Start,
      fontFamily: _theme.typography.bold,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: 'normal'
      }),
      letterSpacing: 0.2,
      textTransform: "uppercase"
    },
    textContainerStyles: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderBottomLeftRadius: 16,
      borderBottomRightRadius: 16,
      height: 135,
      padding: 12,
      width: 164
    },
    textRibbonStyle: {
      color: _theme.color.palette.whiteGrey,
      fontFamily: _theme.typography.bold,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: 'normal'
      }),
      textAlign: "center"
    }
  });
