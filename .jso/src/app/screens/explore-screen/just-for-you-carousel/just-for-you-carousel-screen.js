  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.NewCarousel = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _native = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _i18n = _$$_REQUIRE(_dependencyMap[8]);
  var _constants = _$$_REQUIRE(_dependencyMap[9]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[10]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[11]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[13]);
  var _carouselCard = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _loadingScreen = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _lodash = _$$_REQUIRE(_dependencyMap[16]);
  var _styles = _$$_REQUIRE(_dependencyMap[17]);
  var _shopRedux = _$$_REQUIRE(_dependencyMap[18]);
  var _adobe = _$$_REQUIRE(_dependencyMap[19]);
  var _sha2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[20]));
  var _profileRedux = _$$_REQUIRE(_dependencyMap[21]);
  var _explore = _$$_REQUIRE(_dependencyMap[22]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[23]);
  var _theme = _$$_REQUIRE(_dependencyMap[24]);
  var _icons = _$$_REQUIRE(_dependencyMap[25]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[26]);
  var _navigationType = _$$_REQUIRE(_dependencyMap[27]);
  var _backgrounds = _$$_REQUIRE(_dependencyMap[28]);
  var _errorCloud = _$$_REQUIRE(_dependencyMap[29]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[30]);
  var _utils = _$$_REQUIRE(_dependencyMap[31]);
  var _request = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[32]));
  var _queries = _$$_REQUIRE(_dependencyMap[33]);
  var _awsAmplify = _$$_REQUIRE(_dependencyMap[34]);
  var _envParams = _$$_REQUIRE(_dependencyMap[35]);
  var _deeplinkParameter = _$$_REQUIRE(_dependencyMap[36]);
  var _pageConfigSaga = _$$_REQUIRE(_dependencyMap[37]);
  var _enum = _$$_REQUIRE(_dependencyMap[38]);
  var _firebase = _$$_REQUIRE(_dependencyMap[39]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[40]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var LINEAR_GRADIENT = {
    "#8A2AA2": "#8A2AA2",
    "#F7F7F7": "#F7F7F7",
    "#7A35B0": "#7A35B0"
  };
  var PositionShopAll = {
    top: "Top",
    last: "Last"
  };
  var lastItemEmptyStyle = {
    marginBottom: -50
  };
  var wrapHeaderJustForYou = {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 24,
    paddingBottom: 16
  };
  var wrapShopAllTextStyle = {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-end"
  };
  var mainTitleStyle = Object.assign({}, _text.presets.h4, {
    color: _theme.color.palette.whiteGrey
  });
  var shopAllTextStyle = Object.assign({}, _text.presets.caption1Bold, {
    color: _theme.color.palette.whiteGrey,
    marginRight: 4
  });
  var ISCBannerStyle = {
    width: 164,
    borderRadius: 16,
    marginRight: 16,
    alignItems: "center",
    justifyContent: "flex-end",
    paddingBottom: 28
  };
  var shopAllEndButtonStyle = {
    paddingVertical: 4,
    paddingHorizontal: 29,
    borderRadius: 60,
    height: 28
  };
  var textShopAllEndButtonStyle = Object.assign({}, _text.presets.caption1Bold, {
    color: _theme.color.palette.whiteGrey
  });
  var ReturnView = function ReturnView(_ref) {
    var _justForYouCarouselDa;
    var justForYouCarouselData = _ref.justForYouCarouselData,
      justForYouCarouselFetching = _ref.justForYouCarouselFetching,
      useSeparator = _ref.useSeparator,
      navigation = _ref.navigation,
      testID = _ref.testID,
      accessibilityLabel = _ref.accessibilityLabel,
      isFlagOn = _ref.isFlagOn,
      handleNavigation = _ref.handleNavigation,
      userId = _ref.userId;
    var handleNavigateCSMIShopchangi = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* (url) {
        var ecid = yield (0, _adobe.getExperienceCloudId)();
        var target = (0, _deeplinkParameter.getISCInputParamsDeepLink)(url);
        var payload = {
          stateCode: _constants.StateCode.ISHOPCHANGI,
          input: Object.assign({}, target, {
            ecid: ecid
          })
        };
        try {
          var response = yield (0, _pageConfigSaga.getDeepLinkV2)(payload, true);
          if (response != null && response.redirectUri) {
            navigation.navigate(_constants.NavigationConstants.playpassWebview, {
              uri: response == null ? undefined : response.redirectUri,
              needBackButton: true,
              needCloseButton: true,
              headerType: _enum.WebViewHeaderTypes.default,
              basicAuthCredential: response == null ? undefined : response.basicAuth
            });
          } else {
            navigation.navigate(_constants.NavigationConstants.webview, {
              uri: url
            });
          }
        } catch (error) {
          navigation.navigate(_constants.NavigationConstants.webview, {
            uri: url
          });
        }
      });
      return function handleNavigateCSMIShopchangi(_x) {
        return _ref2.apply(this, arguments);
      };
    }();
    var handleOpenISC = function handleOpenISC(type) {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppShopTenantPromoSwimlane, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppShopTenantPromoSwimlane, `Explore | ${type} | ${(0, _i18n.translate)("justForYouCarouselScreen.shopAll")}`));
      if (isFlagOn) {
        var allHomePage = "https://www.ishopchangi.com/en/home?utm_source=ichangi_app&utm_medium=at&utm_campaign=rec";
        handleNavigateCSMIShopchangi(allHomePage);
      } else {
        handleNavigation(_navigationType.NavigationTypeEnum.deepLink, _navigationHelper.NavigationValueDeepLink.ishopchangi);
      }
    };
    var backgroundLinearGradientParams = function backgroundLinearGradientParams() {
      return {
        colors: [LINEAR_GRADIENT["#7A35B0"], LINEAR_GRADIENT["#F7F7F7"]]
      };
    };
    var linearGradientStyle = function linearGradientStyle() {
      if (useSeparator) {
        return Object.assign({}, _styles.mainScreenStyles.containerWithSeparator);
      } else {
        return Object.assign({}, _styles.mainScreenStyles.container);
      }
    };
    return (0, _jsxRuntime.jsxs)(_reactNativeLinearGradient.default, Object.assign({
      style: linearGradientStyle()
    }, backgroundLinearGradientParams(), {
      children: [justForYouCarouselFetching ? (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: _styles.lightGreyLoadingColors,
        shimmerStyle: _styles.loadingScreen.titleLoading
      }) : (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: wrapHeaderJustForYou,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: mainTitleStyle,
          children: (justForYouCarouselData == null ? undefined : justForYouCarouselData.sectionTitle) || (0, _i18n.translate)("justForYouCarouselScreen.title")
        }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
          style: wrapShopAllTextStyle,
          onPress: function onPress() {
            return handleOpenISC(PositionShopAll.top);
          },
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            tx: "justForYouCarouselScreen.shopAll",
            style: shopAllTextStyle
          }), (0, _jsxRuntime.jsx)(_icons.CaretWhiteRight, {
            width: 20,
            height: 20
          })]
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative2.ScrollView, {
        horizontal: true,
        showsHorizontalScrollIndicator: false,
        children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _styles.mainScreenStyles.contentContainer,
          children: justForYouCarouselFetching ? (0, _jsxRuntime.jsx)(_loadingScreen.default, {}) : (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
            children: [justForYouCarouselData == null || (_justForYouCarouselDa = justForYouCarouselData.data) == null ? undefined : _justForYouCarouselDa.map(function (element, index) {
              return (0, _jsxRuntime.jsx)(_carouselCard.default, {
                item: element,
                navigation: navigation,
                testID: `${testID}__Card`,
                accessibilityLabel: `${accessibilityLabel}__Card__${index}`,
                userId: userId,
                index: index,
                isNewStyle: isFlagOn
              }, index);
            }), (0, _jsxRuntime.jsx)(_reactNative2.ImageBackground, {
              source: _backgrounds.ISCBanner,
              imageStyle: ISCBannerStyle,
              style: ISCBannerStyle,
              children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                onPress: function onPress() {
                  return handleOpenISC(PositionShopAll.last);
                },
                children: (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
                  style: shopAllEndButtonStyle,
                  colors: [LINEAR_GRADIENT["#8A2AA2"], LINEAR_GRADIENT["#7A35B0"]],
                  locations: [0.172, 0.8513],
                  useAngle: true,
                  angle: -281.74,
                  angleCenter: {
                    x: 0.5,
                    y: 0.5
                  },
                  children: (0, _jsxRuntime.jsx)(_text.Text, {
                    tx: "justForYouCarouselScreen.shopAll",
                    style: textShopAllEndButtonStyle
                  })
                })
              })
            })]
          })
        })
      })]
    }));
  };
  var OldCarousel = function OldCarousel(_ref3) {
    var _justForYouData$data;
    var _ref3$testID = _ref3.testID,
      testID = _ref3$testID === undefined ? "JustForYouCarousel" : _ref3$testID,
      _ref3$accessibilityLa = _ref3.accessibilityLabel,
      accessibilityLabel = _ref3$accessibilityLa === undefined ? "JustForYouCarousel" : _ref3$accessibilityLa,
      _ref3$useSeparator = _ref3.useSeparator,
      useSeparator = _ref3$useSeparator === undefined ? false : _ref3$useSeparator,
      isLastItem = _ref3.isLastItem,
      isFlagOn = _ref3.isFlagOn,
      setJustForYouDataFetching = _ref3.setJustForYouDataFetching;
    var _useState = (0, _react.useState)(null),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      justForYouData = _useState2[0],
      setJustForYouData = _useState2[1];
    var _useState3 = (0, _react.useState)(null),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      justForYouFetching = _useState4[0],
      setJustForYouFetching = _useState4[1];
    var navigation = (0, _native.useNavigation)();
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("JUST_FOR_YOU_EXPLORE_SCREEN"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var requestApi = _react.default.useCallback(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      var dtAction = (0, _firebase.dtManualActionEvent)(_firebase.DT_ANALYTICS_LOG_EVENT_NAME.DT_GET_JUST_FOR_YOU);
      dtAction.reportStringValue("feature_flag", "OFF");
      try {
        var _env, _env2, _response$data;
        var response = yield (0, _request.default)({
          url: (_env = (0, _envParams.env)()) == null ? undefined : _env.APPSYNC_GRAPHQL_URL,
          method: "post",
          data: (0, _awsAmplify.graphqlOperation)(_queries.getJustForYouCarousel),
          parameters: {},
          headers: {
            "x-api-key": (_env2 = (0, _envParams.env)()) == null ? undefined : _env2.APPSYNC_GRAPHQL_API_KEY
          }
        });
        var data = (response == null || (_response$data = response.data) == null || (_response$data = _response$data.data) == null ? undefined : _response$data.getExploreJustForYou) || null;
        if (data) {
          dtAction.reportStringValue('status', 'success');
          setJustForYouData(data);
          setJustForYouFetching(false);
          setJustForYouDataFetching(false);
        } else throw new Error("No Data");
      } catch (error) {
        dtAction.reportStringValue('status', 'failed');
        setJustForYouData({
          error: {
            message: error.message
          }
        });
        setJustForYouFetching(false);
        setJustForYouDataFetching(false);
      } finally {
        dtAction.leaveAction();
      }
    }), []);
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      _reactNative2.InteractionManager.runAfterInteractions(function () {
        setJustForYouDataFetching(true);
        setJustForYouFetching(true);
        requestApi();
      });
    }, []));
    if (!justForYouFetching && !(justForYouData != null && justForYouData.data) || justForYouData != null && justForYouData.error || !justForYouFetching && !(justForYouData != null && (_justForYouData$data = justForYouData.data) != null && _justForYouData$data.length)) {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {});
    }
    if ((0, _lodash.isEmpty)(justForYouData == null ? undefined : justForYouData.data)) {
      if (isLastItem) return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: lastItemEmptyStyle
      });
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {});
    }
    return (0, _jsxRuntime.jsx)(ReturnView, {
      justForYouCarouselData: justForYouData,
      justForYouCarouselFetching: justForYouFetching,
      useSeparator: useSeparator,
      navigation: navigation,
      testID: testID,
      accessibilityLabel: accessibilityLabel,
      isFlagOn: isFlagOn,
      handleNavigation: handleNavigation,
      userId: profilePayload == null ? undefined : profilePayload.id
    });
  };
  var NewCarousel = exports.NewCarousel = function NewCarousel(_ref5) {
    var _justForYouCarouselDa2;
    var _ref5$testID = _ref5.testID,
      testID = _ref5$testID === undefined ? "JustForYouCarousel" : _ref5$testID,
      _ref5$accessibilityLa = _ref5.accessibilityLabel,
      accessibilityLabel = _ref5$accessibilityLa === undefined ? "JustForYouCarousel" : _ref5$accessibilityLa,
      _ref5$useSeparator = _ref5.useSeparator,
      useSeparator = _ref5$useSeparator === undefined ? false : _ref5$useSeparator,
      isLastItem = _ref5.isLastItem,
      isFlagOn = _ref5.isFlagOn,
      setJustForYouDataFetching = _ref5.setJustForYouDataFetching;
    var navigation = (0, _native.useNavigation)();
    var _useState5 = (0, _react.useState)(null),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      justForYouCarouselData = _useState6[0],
      setJustForYou = _useState6[1];
    var _useState7 = (0, _react.useState)(null),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      justForYouCarouselFetching = _useState8[0],
      setJustForYouFetching = _useState8[1];
    var _useState9 = (0, _react.useState)(null),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      justForYouCarouselError = _useState0[0],
      setJustForYouError = _useState0[1];
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var recommendedProductsPayload = (0, _reactRedux.useSelector)(_shopRedux.ShopSelectors.recommendedProductsPayload);
    var _useHandleNavigation2 = (0, _navigationHelper.useHandleNavigation)("JUST_FOR_YOU_EXPLORE_SCREEN"),
      handleNavigation = _useHandleNavigation2.handleNavigation;
    var errorData = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var getNewJustForYouLanding = /*#__PURE__*/function () {
      var _ref6 = (0, _asyncToGenerator2.default)(function* (recommendedProducts, profile) {
        var _sha, _profile$email;
        var timeout = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;
        var dtAction = (0, _firebase.dtManualActionEvent)(_firebase.DT_ANALYTICS_LOG_EVENT_NAME.DT_GET_JUST_FOR_YOU);
        dtAction.reportStringValue("feature_flag", "ON");
        setJustForYouError(false);
        setJustForYouFetching(true);
        setJustForYouDataFetching(true);
        if (timeout) {
          yield new Promise(function (resolve) {
            return setTimeout(resolve, timeout);
          });
        }
        var response = yield (0, _adobe.adobeRetrieveLocationContent)(Object.assign({
          login_status: profile ? 0 : 1,
          uid: profile == null ? undefined : profile.id,
          hashed_email: profile != null && profile.email ? (_sha = (0, _sha2.default)(profile == null || (_profile$email = profile.email) == null ? undefined : _profile$email.toLowerCase())) == null ? undefined : _sha.toString() : undefined
        }, (0, _utils.simpleCondition)({
          condition: recommendedProducts == null ? undefined : recommendedProducts.profile_parameters,
          ifValue: recommendedProducts == null ? undefined : recommendedProducts.profile_parameters,
          elseValue: {}
        })), (0, _utils.simpleCondition)({
          condition: recommendedProducts == null ? undefined : recommendedProducts.parameters,
          ifValue: recommendedProducts == null ? undefined : recommendedProducts.parameters,
          elseValue: {}
        }), recommendedProducts == null ? undefined : recommendedProducts.m_box_name);
        // console.log("adobe__getJustForYouLanding__adobeRetrieveLocationContent__response", response)
        if (response && response !== _adobe.DEFAULT_LOCATION_CONTENT) {
          try {
            var jsonObject = JSON.parse(response);
            if (jsonObject != null && jsonObject.code) {
              setJustForYouError(true);
              dtAction.reportStringValue('status', 'failed');
            } else {
              setJustForYou(jsonObject);
              dtAction.reportStringValue('status', 'success');
            }
          } catch (error) {
            setJustForYouError(true);
            dtAction.reportStringValue('status', 'failed');
          }
        } else {
          setJustForYou(null);
          dtAction.reportStringValue('data', 'success_without_data');
        }
        dtAction.leaveAction();
        setJustForYouFetching(false);
        setJustForYouDataFetching(false);
      });
      return function getNewJustForYouLanding(_x2, _x3) {
        return _ref6.apply(this, arguments);
      };
    }();
    var onReload = function onReload() {
      getNewJustForYouLanding(recommendedProductsPayload, profilePayload, 1000);
    };
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      if (recommendedProductsPayload) {
        getNewJustForYouLanding(recommendedProductsPayload, profilePayload);
      }
    }, [recommendedProductsPayload, profilePayload == null ? undefined : profilePayload.id]));
    if (!justForYouCarouselFetching && !(justForYouCarouselData != null && (_justForYouCarouselDa2 = justForYouCarouselData.data) != null && _justForYouCarouselDa2.length)) {
      if (justForYouCarouselError) {
        return (0, _jsxRuntime.jsx)(_errorCloud.ErrorCloudComponent, {
          errorData: errorData,
          onPress: onReload,
          testID: `EXPLORE_JUST_FOR_YOU__ErrorCloudComponent`,
          accessibilityLabel: `EXPLORE_JUST_FOR_YOU__ErrorCloudComponent`
        });
      } else {
        if (isLastItem) return (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: lastItemEmptyStyle
        });
        return (0, _jsxRuntime.jsx)(_reactNative2.View, {});
      }
    }
    return (0, _jsxRuntime.jsx)(ReturnView, {
      justForYouCarouselData: justForYouCarouselData,
      justForYouCarouselFetching: justForYouCarouselFetching,
      useSeparator: useSeparator,
      navigation: navigation,
      testID: testID,
      accessibilityLabel: accessibilityLabel,
      isFlagOn: isFlagOn,
      handleNavigation: handleNavigation,
      userId: profilePayload == null ? undefined : profilePayload.id
    });
  };
  var JustForYouCarousel = function JustForYouCarousel(args) {
    var ExploreContextHandler = (0, _react.useContext)(_explore.ExploreContext);
    var isFlagOn = (0, _remoteConfig.isFlagOnCondition)(ExploreContextHandler == null ? undefined : ExploreContextHandler.exploreJustForYouFlag);
    if (!(ExploreContextHandler != null && ExploreContextHandler.exploreJustForYouFlag)) {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {});
    }
    if (isFlagOn) {
      return (0, _jsxRuntime.jsx)(NewCarousel, Object.assign({}, args, {
        isFlagOn: isFlagOn
      }));
    }
    return (0, _jsxRuntime.jsx)(OldCarousel, Object.assign({}, args, {
      isFlagOn: isFlagOn
    }));
  };
  var _default = exports.default = _react.default.memo(JustForYouCarousel);
