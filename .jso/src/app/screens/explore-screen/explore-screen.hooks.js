  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useBottomSheetError = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _systemRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[5]);
  var useBottomSheetError = exports.useBottomSheetError = function useBottomSheetError(_ref) {
    var route = _ref.route;
    var _ref2 = (route == null ? undefined : route.params) || {},
      bottomSheetErrorHash = _ref2.bottomSheetErrorHash;
    var dispatch = (0, _reactRedux.useDispatch)();
    var previousHashRef = (0, _react.useRef)(undefined);
    (0, _react.useEffect)(function () {
      _reactNative.AppState.addEventListener("change", /*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
        if (_reactNative.AppState.currentState.match(/inactive|background/)) {
          dispatch(_systemRedux.default.setBottomSheetErrorData({
            visible: false
          }));
        }
      }));
    }, []);
    (0, _react.useEffect)(function () {
      if (bottomSheetErrorHash !== (previousHashRef == null ? undefined : previousHashRef.current)) {
        dispatch(_systemRedux.default.setBottomSheetErrorData({
          visible: true
        }));
        previousHashRef.current = bottomSheetErrorHash;
      }
    }, [bottomSheetErrorHash]);
  };
