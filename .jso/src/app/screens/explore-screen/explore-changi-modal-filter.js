  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _icons = _$$_REQUIRE(_dependencyMap[9]);
  var _calendarFliter = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _theme = _$$_REQUIRE(_dependencyMap[11]);
  var _text = _$$_REQUIRE(_dependencyMap[12]);
  var _text2 = _$$_REQUIRE(_dependencyMap[13]);
  var _checkbox = _$$_REQUIRE(_dependencyMap[14]);
  var _lodash = _$$_REQUIRE(_dependencyMap[15]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[16]);
  var _exploreRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[17]));
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[18]));
  var _button = _$$_REQUIRE(_dependencyMap[19]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[20]));
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[21]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[22]);
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[23]));
  var _utils = _$$_REQUIRE(_dependencyMap[24]);
  var _modalRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[25]));
  var _systemRedux = _$$_REQUIRE(_dependencyMap[26]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[27]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _Dimensions$get = _reactNative2.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var closeIconStyles = {
    color: _theme.color.palette.lightPurple
  };
  var TAG_NAME_ALL = "all";
  var locationFilterAll = {
    filterType: "locations",
    sequenceNumber: 1,
    tagTitle: "All",
    tagName: TAG_NAME_ALL
  };
  var ExploreChangiModalFilter = function ExploreChangiModalFilter(props) {
    var _getCheckedState;
    var showFilterModal = props.showFilterModal,
      setShowFilterModal = props.setShowFilterModal,
      _props$initialFilterL = props.initialFilterLocation,
      initialFilterLocation = _props$initialFilterL === undefined ? "" : _props$initialFilterL,
      testID = props.testID,
      accessibilityLabel = props.accessibilityLabel,
      filterDate = props.filterDate,
      setFilterDate = props.setFilterDate,
      checkedLocationState = props.checkedLocationState,
      setCheckedLocationState = props.setCheckedLocationState;
    var dispatch = (0, _reactRedux.useDispatch)();
    var preCheckedState = (0, _react.useRef)({});
    var loading = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.getExploreChangiLocationLoading);
    var exploreChangiLocation = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.exploreChangiLocation) || [];
    var exploreChangiFilter = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.exploreChangiFilter);
    var tabSelectData = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.selectedExploreCategoryItem);
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var closeExploreFilter = (0, _reactRedux.useSelector)(_modalRedux.ModalSelectors.closeExploreFilter);
    var isOpenAppFromDeepLink = (0, _reactRedux.useSelector)(_systemRedux.SystemSelectors.isOpenAppFromDeepLink);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isNoConnection = _useState2[0],
      setNoConnection = _useState2[1];
    (0, _react.useEffect)(function () {
      if (initialFilterLocation) {
        handleOnCheckboxChange(initialFilterLocation, true);
      }
    }, [initialFilterLocation]);
    (0, _react.useEffect)(function () {
      if (showFilterModal) {
        var checkInternet = /*#__PURE__*/function () {
          var _ref = (0, _asyncToGenerator2.default)(function* () {
            var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
              isConnected = _yield$NetInfo$fetch.isConnected;
            if (!isConnected) {
              setNoConnection(true);
            } else {
              preCheckedState.current = checkedLocationState;
            }
          });
          return function checkInternet() {
            return _ref.apply(this, arguments);
          };
        }();
        checkInternet();
      }
    }, [showFilterModal]);
    var checkStateChecked = function checkStateChecked() {
      if (!(0, _lodash.isEmpty)(exploreChangiFilter == null ? undefined : exploreChangiFilter.locations)) {
        var _exploreChangiFilter$, _exploreChangiFilter$2;
        var newLocation = {};
        exploreChangiFilter == null || (_exploreChangiFilter$ = exploreChangiFilter.locations) == null || _exploreChangiFilter$.forEach(function (e) {
          newLocation[e] = true;
        });
        if ((exploreChangiFilter == null || (_exploreChangiFilter$2 = exploreChangiFilter.locations) == null ? undefined : _exploreChangiFilter$2.length) === 5) {
          newLocation.all = true;
        }
        setCheckedLocationState(newLocation);
      }
    };
    var handleOnCheckedAll = function handleOnCheckedAll(value) {
      var checkedAll = [locationFilterAll].concat((0, _toConsumableArray2.default)(exploreChangiLocation)).reduce(function (currentData, tag) {
        currentData[tag.tagName] = value;
        return currentData;
      }, {});
      setCheckedLocationState(checkedAll);
    };
    var handleOnCheckboxChange = function handleOnCheckboxChange(tagName, value) {
      var checkBoxState = Object.assign({}, checkedLocationState, (0, _defineProperty2.default)({}, tagName, value));
      var locationCheckedData = Object.entries(checkBoxState).filter(function (_ref2) {
        var _ref3 = (0, _slicedToArray2.default)(_ref2, 2),
          key = _ref3[0],
          newValue = _ref3[1];
        return newValue && key !== TAG_NAME_ALL;
      });
      var checkBoxAllValue = (locationCheckedData == null ? undefined : locationCheckedData.length) === (exploreChangiLocation == null ? undefined : exploreChangiLocation.length);
      setCheckedLocationState(Object.assign({}, checkBoxState, (0, _defineProperty2.default)({}, TAG_NAME_ALL, checkBoxAllValue)));
    };
    var getCheckedState = function getCheckedState(checkedState) {
      return Object.entries(checkedState).reduce(function (previousValue, _ref4) {
        var _ref5 = (0, _slicedToArray2.default)(_ref4, 2),
          key = _ref5[0],
          value = _ref5[1];
        if (value && key !== TAG_NAME_ALL) {
          // Recheck checked item existing filter list
          var isExistFilterList = exploreChangiLocation.some(function (item) {
            return item.tagName === key;
          });
          if (isExistFilterList) {
            previousValue.push(key);
          }
        }
        return previousValue;
      }, []);
    };
    var onClosedFilterModal = function onClosedFilterModal() {
      var preCheckedLocationState = (0, _lodash.get)(preCheckedState, "current", {});
      setFilterDate(exploreChangiFilter == null ? undefined : exploreChangiFilter.date);
      setCheckedLocationState(preCheckedLocationState);
      setShowFilterModal(false);
    };
    var selectionOnPress = /*#__PURE__*/function () {
      var _ref6 = (0, _asyncToGenerator2.default)(function* () {
        var checkedLocation = getCheckedState(checkedLocationState);
        var newExploreChangiFilter = {
          date: (0, _lodash.isEmpty)(filterDate) ? "" : (0, _moment.default)(filterDate).format("YYYY-MM-DD"),
          locations: checkedLocation
        };
        var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch2.isConnected;
        dispatch(_exploreRedux.default.setExploreChangiLocation(newExploreChangiFilter));
        if (isConnected) {
          var _newExploreChangiFilt;
          dispatch(_exploreRedux.default.resetExploreDataPageNumber());
          dispatch(_exploreRedux.default.exploreDataRequest({
            pageNumber: 1,
            category: tabSelectData == null ? undefined : tabSelectData.text,
            categoryCode: tabSelectData == null ? undefined : tabSelectData.value,
            email: profilePayload == null ? undefined : profilePayload.email,
            date: newExploreChangiFilter == null ? undefined : newExploreChangiFilter.date,
            locations: newExploreChangiFilter == null || (_newExploreChangiFilt = newExploreChangiFilter.locations) == null ? undefined : _newExploreChangiFilt.map(function (item) {
              return item.toUpperCase();
            })
          }));
          dispatch(_exploreRedux.default.setNoInternetFilter(true));
          setShowFilterModal(false);
        } else {
          dispatch(_exploreRedux.default.setNoInternetFilter(false));
          setShowFilterModal(false);
        }
      });
      return function selectionOnPress() {
        return _ref6.apply(this, arguments);
      };
    }();
    var onReloadData = function onReloadData() {
      var onReload = /*#__PURE__*/function () {
        var _ref7 = (0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch3 = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch3.isConnected;
          if (isConnected) {
            setNoConnection(false);
          }
        });
        return function onReload() {
          return _ref7.apply(this, arguments);
        };
      }();
      onReload();
    };
    (0, _react.useEffect)(function () {
      if (closeExploreFilter) {
        onClosedFilterModal();
        dispatch(_modalRedux.default.closeExploreFilterRequest(false));
      }
    }, [closeExploreFilter]);
    var renderLocationFilter = function renderLocationFilter() {
      if (isNoConnection) {
        return (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
          reload: true,
          header: false,
          headerBackgroundColor: "transparent",
          hideScreenHeader: false,
          visible: true,
          onReload: onReloadData,
          noInternetOverlayStyle: styles.overlayStyle
        });
      }
      return [locationFilterAll].concat((0, _toConsumableArray2.default)(exploreChangiLocation)).map(function (item, index) {
        return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.checkboxContainer,
          children: [(0, _jsxRuntime.jsx)(_checkbox.Checkbox, {
            value: (0, _lodash.get)(checkedLocationState, item.tagName, false),
            onToggle: function onToggle(value) {
              if (index === 0) {
                handleOnCheckedAll(value);
                return;
              }
              handleOnCheckboxChange(item.tagName, value);
            },
            text: item.tagTitle,
            textStyle: Object.assign({}, styles.textCheckboxStyles, (0, _lodash.get)(checkedLocationState, item.tagName, false) ? styles.activeCheckboxStyles : {}),
            outlineStyle: styles.outlineStyle,
            testID: `${testID}__LocationCheckBox`,
            accessibilityLabel: `${accessibilityLabel}__LocationCheckBox`
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.lineGrey
          })]
        }, index);
      });
    };
    return (0, _jsxRuntime.jsx)(_bottomSheet.default, {
      isModalVisible: showFilterModal,
      containerStyle: styles.container,
      onClosedSheet: onClosedFilterModal,
      stopDragCollapse: true,
      onBackPressHandle: onClosedFilterModal,
      animationInTiming: 100,
      animationOutTiming: 100,
      onModalWillShow: checkStateChecked,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.bottomShetFilterContainer,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.headerFilter,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            text: "Show listings for:",
            style: styles.filterTitle
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: onClosedFilterModal,
            style: styles.btnCloseStyles,
            testID: `${testID}__CloseIcon`,
            accessibilityLabel: `${accessibilityLabel}__CloseIcon`,
            children: (0, _jsxRuntime.jsx)(_icons.CloseCross, {
              width: 24,
              height: 24,
              fill: "currentColor",
              style: closeIconStyles
            })
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative2.ScrollView, {
          showsVerticalScrollIndicator: false,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: styles.calendarContainer,
              children: (0, _jsxRuntime.jsx)(_calendarFliter.default, {
                setFilterDate: setFilterDate,
                filterDate: filterDate,
                testID: `${testID}__CalendarComponent`,
                accessibilityLabel: `${accessibilityLabel}__CalendarComponent`
              })
            }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              onPress: function onPress() {
                return setFilterDate("");
              },
              testID: `${testID}__ClearDateButton`,
              accessibilityLabel: `${accessibilityLabel}__ClearDateButton`,
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                text: "Clear date selection",
                style: styles.clearDateFilter
              })
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.locationModalContainer,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.locationTitleStyles,
              children: "Locations"
            }), renderLocationFilter()]
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.locationFooter,
          children: [(0, _utils.handleCondition)(((_getCheckedState = getCheckedState(checkedLocationState)) == null ? undefined : _getCheckedState.length) > 0, (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.btnSelectContainer,
            children: (0, _jsxRuntime.jsx)(_button.Button, {
              onPress: function onPress() {
                setCheckedLocationState([]);
              },
              style: styles.clearAllButton,
              sizePreset: "large",
              statePreset: "default",
              typePreset: "secondary",
              text: "Clear all",
              textStyle: styles.purpleTextColor,
              textPreset: "buttonLarge",
              testID: `${testID}__ClearAllFilters`,
              accessibilityLabel: `${accessibilityLabel}__ClearAllFilters`
            })
          }), null), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
            style: styles.btnSelectContainer,
            start: {
              x: 0,
              y: 1
            },
            end: {
              x: 1,
              y: 0
            },
            colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
            children: (0, _jsxRuntime.jsx)(_button.Button, {
              text: "Apply filters",
              typePreset: "secondary",
              onPress: selectionOnPress,
              statePreset: "default",
              backgroundPreset: "dark",
              textPreset: "buttonLarge",
              textStyle: styles.textBtnSelectStyle,
              testID: `${testID}__FilterButton`,
              accessibilityLabel: `${accessibilityLabel}__FilterButton`
            })
          })]
        })]
      })
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    activeCheckboxStyles: {
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      })
    },
    bottomShetFilterContainer: {
      backgroundColor: _theme.color.palette.lightestGrey,
      borderTopLeftRadius: 10,
      borderTopRightRadius: 10,
      height: "100%",
      width: "100%"
    },
    btnCloseStyles: {
      position: "absolute",
      right: 0
    },
    btnSelectContainer: {
      borderRadius: 60,
      flex: 1,
      height: 44,
      marginTop: 16
    },
    calendarContainer: Object.assign({
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 13,
      marginHorizontal: 24,
      padding: 16
    }, _reactNative2.Platform.select({
      ios: {
        shadowRadius: 2,
        shadowOpacity: 0.1,
        shadowOffset: {
          width: 0,
          height: 3
        },
        backgroundColor: _theme.color.palette.whiteGrey
      },
      android: {
        elevation: 3,
        backgroundColor: _theme.color.palette.whiteGrey
      }
    })),
    checkboxContainer: {
      paddingTop: 16,
      width: "100%"
    },
    clearAllButton: {
      flex: 1,
      marginRight: 6.5
    },
    clearDateFilter: Object.assign({}, _text2.presets.textLink, {
      marginHorizontal: 24,
      marginTop: 25
    }),
    container: {
      height: "90%",
      margin: 0
    },
    filterTitle: Object.assign({}, _text2.presets.subTitleBold, {
      color: _theme.color.palette.almostBlackGrey
    }),
    headerFilter: {
      flexDirection: "row",
      justifyContent: "center",
      marginHorizontal: 24,
      marginVertical: 21
    },
    lineGrey: {
      borderBottomColor: _theme.color.palette.lighterGrey,
      borderBottomWidth: 1,
      marginTop: 16
    },
    locationFooter: Object.assign({
      flexDirection: "row",
      justifyContent: "center",
      paddingHorizontal: 24
    }, _reactNative2.Platform.select({
      ios: {
        shadowRadius: 2,
        shadowOpacity: 0.08,
        shadowOffset: {
          width: 0,
          height: -4
        },
        backgroundColor: _theme.color.palette.whiteGrey
      },
      android: {
        elevation: 3,
        backgroundColor: _theme.color.palette.whiteGrey
      }
    }), {
      paddingBottom: 25
    }),
    locationModalContainer: {
      flex: 1,
      marginTop: 50,
      paddingBottom: 40,
      paddingHorizontal: 24
    },
    locationTitleStyles: Object.assign({}, _text2.presets.h3, {
      color: _theme.color.palette.almostBlackGrey
    }),
    outlineStyle: {
      borderColor: _theme.color.palette.darkGrey
    },
    overlayStyle: {
      height: "100%",
      width: "100%"
    },
    purpleTextColor: {
      color: _theme.color.palette.lightPurple,
      lineHeight: 24,
      textAlign: "center",
      fontSize: 16
    },
    textBtnSelectStyle: {
      color: _theme.color.palette.almostWhiteGrey,
      fontFamily: _theme.typography.bold,
      fontSize: 16,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 24,
      textAlign: "center"
    },
    textCheckboxStyles: Object.assign({}, _text2.presets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey,
      fontWeight: "400",
      maxWidth: width - 84,
      width: "100%"
    })
  });
  var _default = exports.default = _react.default.memo(ExploreChangiModalFilter);
