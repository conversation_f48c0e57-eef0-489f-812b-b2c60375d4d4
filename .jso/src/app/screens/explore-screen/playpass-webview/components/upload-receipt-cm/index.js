  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _lodash = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[7]);
  var _icons = _$$_REQUIRE(_dependencyMap[8]);
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[9]);
  var _reactNativeFs = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _index = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _reactNativeImageResizer = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _camera = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[14]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var resizeImage = /*#__PURE__*/function () {
    var _ref2 = (0, _asyncToGenerator2.default)(function* (_ref) {
      var path = _ref.path,
        maxWidth = _ref.maxWidth,
        maxHeight = _ref.maxHeight,
        compressFormat = _ref.compressFormat,
        quality = _ref.quality;
      var response = yield _reactNativeImageResizer.default.createResizedImage(path, maxWidth, maxHeight, compressFormat, quality);
      return response;
    });
    return function resizeImage(_x) {
      return _ref2.apply(this, arguments);
    };
  }();
  var UploadReceiptCMScreen = function UploadReceiptCMScreen(props) {
    var onClosedSheet = props.onClosedSheet,
      handleTakePicture = props.handleTakePicture;
    var cameraRef = (0, _react.useRef)(null);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      onFlash = _useState2[0],
      setOnFlash = _useState2[1];
    var inset = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isCameraReady = _useState4[0],
      setIsCameraReady = _useState4[1];
    var onCameraReady = function onCameraReady() {
      setIsCameraReady(true);
    };
    var takePicture = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* () {
        var _cameraRef$current;
        var imageData = yield cameraRef == null || (_cameraRef$current = cameraRef.current) == null || _cameraRef$current.takePicture == null ? undefined : _cameraRef$current.takePicture();
        if (imageData != null && imageData.path) {
          var imagePath = _reactNative2.Platform.OS === "ios" ? imageData == null ? undefined : imageData.path : `file://${imageData == null ? undefined : imageData.path}`;
          _reactNative2.Image.getSize(imagePath, /*#__PURE__*/function () {
            var _ref4 = (0, _asyncToGenerator2.default)(function* (width, height) {
              if (width && height) {
                var resizedImage = yield resizeImage({
                  path: imageData == null ? undefined : imageData.path,
                  maxWidth: width < 1024 ? width : 1024,
                  maxHeight: height < 1024 ? height : 1024,
                  compressFormat: "JPEG",
                  quality: 100
                });
                var base64 = yield _reactNativeFs.default.readFile(resizedImage.uri, "base64");
                if (base64) {
                  handleTakePicture(base64);
                }
                setOnFlash(false);
              }
            });
            return function (_x2, _x3) {
              return _ref4.apply(this, arguments);
            };
          }());
        }
      });
      return function takePicture() {
        return _ref3.apply(this, arguments);
      };
    }();
    var toggleFlashMode = function toggleFlashMode() {
      setOnFlash(!onFlash);
    };
    var handleGallerySelected = /*#__PURE__*/function () {
      var _ref5 = (0, _asyncToGenerator2.default)(function* () {
        setTimeout(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
          try {
            var imageData = yield (0, _mediaHelper.choosePictureFromGallery)({
              multiple: false
            });
            var base64Img = (0, _lodash.get)(imageData, "base64");
            if (base64Img) {
              handleTakePicture(base64Img);
            }
          } catch (error) {}
        }), 200);
      });
      return function handleGallerySelected() {
        return _ref5.apply(this, arguments);
      };
    }();
    var onPressClose = function onPressClose() {
      onClosedSheet == null || onClosedSheet();
    };
    var renderHeader = function renderHeader() {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: [_index.default.containerHeader, {
          height: inset.top + 60
        }],
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _index.default.wrapHeader,
          children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _index.default.leftHeader
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: toggleFlashMode,
            children: onFlash ? (0, _jsxRuntime.jsx)(_icons.FlashOn, {}) : (0, _jsxRuntime.jsx)(_icons.FlashOff, {})
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: onPressClose,
            style: _index.default.rightHeader,
            children: (0, _jsxRuntime.jsx)(_icons.CrossWhite, {
              width: 13,
              height: 13
            })
          })]
        })
      });
    };
    var renderFooter = function renderFooter() {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _index.default.wrapFooter,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _index.default.leftFooter,
          children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: handleGallerySelected,
            children: (0, _jsxRuntime.jsx)(_icons.Gallery, {})
          })
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: takePicture,
          style: _index.default.takePictureButton,
          children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _index.default.circleBlackStyle,
            children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: _index.default.circleWhiteStyle
            })
          })
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _index.default.rightFooter
        })]
      });
    };
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _index.default.containerUploadReceipt,
        children: [(0, _jsxRuntime.jsx)(_camera.default, {
          ref: cameraRef,
          flashMode: onFlash,
          onCameraReady: onCameraReady
        }), isCameraReady ? (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _index.default.layerAction,
          children: [renderHeader(), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _index.default.wrapBellowContent,
            children: renderFooter()
          })]
        }) : null]
      })
    });
  };
  var _default = exports.default = UploadReceiptCMScreen;
