  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var styles = _reactNative.StyleSheet.create({
    circleBlackStyle: {
      backgroundColor: _theme.color.palette.almostBlackGrey,
      borderRadius: 100,
      flex: 1,
      margin: 2
    },
    circleWhiteStyle: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 100,
      flex: 1,
      margin: 2
    },
    closeCrossStyles: {
      color: _theme.color.palette.lightGrey,
      left: 15,
      position: "absolute",
      top: 12
    },
    containerHeader: {
      backgroundColor: `${_theme.color.palette.darkestGrey}80`,
      justifyContent: "flex-end"
    },
    containerUploadReceipt: {
      flex: 1
    },
    layerAction: {
      display: "flex",
      height: "100%",
      justifyContent: "space-between",
      position: "absolute",
      width: "100%"
    },
    leftFooter: {
      paddingTop: 5,
      width: "10%"
    },
    leftHeader: {
      width: "10%"
    },
    preview: {
      flex: 1
    },
    rightFooter: {
      width: "10%"
    },
    rightHeader: {
      alignItems: "flex-end",
      width: "10%"
    },
    takePictureButton: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 30,
      height: 60,
      width: 60
    },
    textUpload: {
      color: _theme.color.palette.whiteGrey,
      fontFamily: "Lato-Bold",
      fontSize: 12,
      fontStyle: "normal",
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: 'normal'
      }),
      lineHeight: 20,
      marginTop: 24,
      textAlign: "center",
      textDecorationLine: "underline"
    },
    wrapBellowContent: {
      height: 120,
      width: "100%"
    },
    wrapFooter: {
      backgroundColor: `${_theme.color.palette.darkestGrey}80`,
      flexDirection: "row",
      height: 148,
      justifyContent: "space-between",
      paddingHorizontal: 20,
      paddingTop: 25
    },
    wrapHeader: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "space-between",
      paddingBottom: 15,
      paddingHorizontal: 30,
      width: "100%"
    },
    wrapTakePictureButton: {
      alignItems: "center",
      height: 30,
      width: 60
    }
  });
  var _default = exports.default = styles;
