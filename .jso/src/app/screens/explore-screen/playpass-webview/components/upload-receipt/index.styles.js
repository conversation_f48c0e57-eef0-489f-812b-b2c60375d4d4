  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var styles = _reactNative.StyleSheet.create({
    closeCrossStyles: {
      color: _theme.color.palette.lightGrey,
      left: 15,
      position: "absolute",
      top: 12
    },
    containerHeader: {
      backgroundColor: `${_theme.color.palette.darkestGrey}80`,
      justifyContent: "flex-end"
    },
    containerUploadReceipt: {
      flex: 1
    },
    layerAction: {
      display: "flex",
      height: "100%",
      justifyContent: "space-between",
      position: "absolute",
      width: "100%"
    },
    leftHeader: {
      height: 24,
      width: 24
    },
    preview: {
      flex: 1
    },
    takePictureButton: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 30,
      bottom: 0,
      height: 60,
      position: "absolute",
      width: 60
    },
    textUpload: {
      color: _theme.color.palette.whiteGrey,
      fontSize: 12,
      fontStyle: "normal",
      fontFamily: _theme.typography.bold,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: 'normal'
      }),
      lineHeight: 20,
      marginTop: 24,
      textAlign: "center",
      textDecorationLine: "underline"
    },
    wrapBellowContent: {
      height: 120,
      width: "100%"
    },
    wrapFooter: {
      alignItems: "center",
      backgroundColor: `${_theme.color.palette.darkestGrey}80`,
      height: "100%",
      paddingHorizontal: 20
    },
    wrapHeader: {
      alignItems: "center",
      flexDirection: "row",
      height: 35,
      justifyContent: "space-between",
      paddingHorizontal: 30,
      width: "100%"
    },
    wrapTakePictureButton: {
      alignItems: "center",
      height: 30,
      width: 60
    }
  });
  var _default = exports.default = styles;
