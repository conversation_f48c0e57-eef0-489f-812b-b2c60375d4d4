  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _theme = _$$_REQUIRE(_dependencyMap[3]);
  var _icons = _$$_REQUIRE(_dependencyMap[4]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  var headerContainer = {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginHorizontal: 24,
    paddingVertical: 16.5,
    paddingTop: 10
  };
  var wrapLeftAction = {
    flexDirection: "row",
    alignItems: "center",
    width: "20%"
  };
  var wrapRightAction = {
    width: "20%"
  };
  var reloadIconStyle = {
    marginLeft: 10
  };
  var titleScreen = Object.assign({}, _text.presets.bodyTextBold, {
    textTransform: "uppercase",
    letterSpacing: 1.78571,
    color: _theme.color.palette.almostBlackGrey,
    lineHeight: 17,
    fontSize: 14.29
  });
  var iconStyle = {
    color: _theme.color.palette.almostBlackGrey
  };
  var ChangiMillionaireHeader = function ChangiMillionaireHeader(_ref) {
    var onBackButtonPressed = _ref.onBackButtonPressed,
      reloadScreen = _ref.reloadScreen;
    var inset = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    var handleMargin = function handleMargin() {
      if (_reactNative2.Platform.OS === "android") {
        if (inset != null && inset.top) {
          return (inset == null ? undefined : inset.top) + 10;
        }
        return 0;
      }
      return 0;
    };
    var marginHeader = {
      marginTop: handleMargin()
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: [headerContainer, Object.assign({}, marginHeader)],
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: wrapLeftAction,
        children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: onBackButtonPressed,
          testID: "BackButtonWebViewScreen",
          accessibilityLabel: "BackButtonWebViewScreen",
          children: (0, _jsxRuntime.jsx)(_icons.ArrowLeftFill, {
            width: 24,
            height: 24,
            style: iconStyle
          })
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: reloadScreen,
          testID: "BackButtonWebViewScreen",
          accessibilityLabel: "BackButtonWebViewScreen",
          style: reloadIconStyle,
          children: (0, _jsxRuntime.jsx)(_icons.ReloadFill, {
            width: 24,
            height: 24
          })
        })]
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        preset: "subTitleBold",
        tx: "changiMillionaire.header",
        style: titleScreen
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: wrapRightAction
      })]
    });
  };
  var _default = exports.default = ChangiMillionaireHeader;
