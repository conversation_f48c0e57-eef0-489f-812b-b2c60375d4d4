  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _theme = _$$_REQUIRE(_dependencyMap[2]);
  var _icons = _$$_REQUIRE(_dependencyMap[3]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var headerContainer = {
    flexDirection: "row",
    justifyContent: "space-between",
    margin: 16
  };
  var dummyViewStyle = {
    width: 32
  };
  var DefaultHeader = function DefaultHeader(_ref) {
    var canGoBack = _ref.canGoBack,
      onBack = _ref.onBack,
      onBackButtonPressed = _ref.onBackButtonPressed,
      webViewTitle = _ref.webViewTitle,
      title = _ref.title,
      needBackButton = _ref.needBackButton,
      needCloseButton = _ref.needCloseButton,
      onCloseButtonPressed = _ref.onCloseButtonPressed,
      _ref$headerStyle = _ref.headerStyle,
      headerStyle = _ref$headerStyle === undefined ? {} : _ref$headerStyle;
    var inset = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    var handleMargin = function handleMargin() {
      if (_reactNative2.Platform.OS === "android") {
        if (inset != null && inset.top) {
          return (inset == null ? undefined : inset.top) + 15;
        }
        return 0;
      }
      return 0;
    };
    var marginHeader = {
      marginTop: handleMargin()
    };
    var textTitle = Object.assign({}, _text.presets.subTitleBold, {
      fontSize: 16,
      color: _theme.color.palette.almostBlackGrey
    });
    var renderDummyView = (0, _react.useCallback)(function () {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: dummyViewStyle
      });
    }, []);
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: [headerContainer, Object.assign({}, marginHeader), headerStyle],
      children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        onPress: canGoBack ? onBack : onBackButtonPressed,
        testID: "BackButtonWebViewScreen",
        accessibilityLabel: "BackButtonWebViewScreen",
        hitSlop: {
          top: 10,
          left: 10,
          bottom: 10,
          right: 10
        },
        children: needBackButton ? (0, _jsxRuntime.jsx)(_icons.ArrowLeft, {
          width: 24,
          height: 24
        }) : renderDummyView()
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        preset: "subTitleBold",
        text: webViewTitle || title,
        style: textTitle
      }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        onPress: onCloseButtonPressed,
        testID: "CloseButtonWebViewScreen",
        accessibilityLabel: "CloseButtonWebViewScreen",
        hitSlop: {
          top: 10,
          left: 10,
          bottom: 10,
          right: 10
        },
        children: needCloseButton ? (0, _jsxRuntime.jsx)(_icons.CrossPurple, {
          width: 24,
          height: 24
        }) : renderDummyView()
      })]
    });
  };
  var _default = exports.default = DefaultHeader;
