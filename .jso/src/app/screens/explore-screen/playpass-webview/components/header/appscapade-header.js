  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _icons = _$$_REQUIRE(_dependencyMap[3]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  var headerContainer = {
    flexDirection: "row",
    justifyContent: "space-between",
    margin: 16
  };
  var AppscapadeHeader = function AppscapadeHeader(_ref) {
    var onReload = _ref.onReload,
      webViewTitle = _ref.webViewTitle,
      title = _ref.title,
      onCloseScreen = _ref.onCloseScreen,
      isDisable = _ref.isDisable;
    var inset = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    var handleMargin = function handleMargin() {
      if (_reactNative2.Platform.OS === "android") {
        if (inset != null && inset.top) {
          return (inset == null ? undefined : inset.top) + 15;
        }
        return 0;
      }
      return 0;
    };
    var marginHeader = {
      marginTop: handleMargin()
    };
    var textTitle = Object.assign({}, _text.presets.subTitleBold, {
      fontSize: 16
    });
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: [headerContainer, Object.assign({}, marginHeader)],
      children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        onPress: onReload,
        testID: "ReloadButtonWebViewScreen",
        accessibilityLabel: "ReloadButtonWebViewScreen",
        hitSlop: {
          top: 10,
          left: 10,
          bottom: 10,
          right: 10
        },
        disabled: isDisable,
        children: isDisable ? (0, _jsxRuntime.jsx)(_icons.RefreshInactive, {
          width: 24,
          height: 24
        }) : (0, _jsxRuntime.jsx)(_icons.Refresh, {
          width: 24,
          height: 24
        })
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        preset: "subTitleBold",
        text: webViewTitle || title,
        style: textTitle
      }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        onPress: onCloseScreen,
        testID: "CloseButtonWebViewScreen",
        accessibilityLabel: "CloseButtonWebViewScreen",
        hitSlop: {
          top: 10,
          left: 10,
          bottom: 10,
          right: 10
        },
        children: (0, _jsxRuntime.jsx)(_icons.CrossPurple, {
          width: 24,
          height: 24
        })
      })]
    });
  };
  var _default = exports.default = AppscapadeHeader;
