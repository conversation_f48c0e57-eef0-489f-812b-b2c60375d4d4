  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _lodash = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[7]);
  var _text = _$$_REQUIRE(_dependencyMap[8]);
  var _icons = _$$_REQUIRE(_dependencyMap[9]);
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[10]);
  var _index = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _reactNativeHoleView = _$$_REQUIRE(_dependencyMap[12]);
  var _native = _$$_REQUIRE(_dependencyMap[13]);
  var _camera = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[15]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _Dimensions$get = _reactNative2.Dimensions.get("window"),
    width = _Dimensions$get.width,
    height = _Dimensions$get.height;
  var WIDTH_SCAN_VIEW = width - 80;
  var HEIGHT_SCAN_VIEW = width - 80;
  var POSITION_X = 40;
  var POSITION_Y = height - (Math.round(height / 2) + Math.round(WIDTH_SCAN_VIEW / 2));
  var holeViewStyle = {
    position: "absolute",
    width: "100%",
    height: "100%",
    backgroundColor: _reactNative2.Platform.OS === 'android' ? 'clear' : "rgba(0,0,0,0.8)"
  };
  var uploadQRButtonStyle = {
    borderColor: "rgba(252,252,252, 0.3)",
    borderWidth: 2,
    borderRadius: 60,
    paddingHorizontal: 24,
    paddingVertical: 10,
    flexDirection: "row",
    alignItems: "center"
  };
  var ScanQRCMScreen = function ScanQRCMScreen(props) {
    var onClosedSheet = props.onClosedSheet,
      handleUploadCM = props.handleUploadCM,
      handleQRScan = props.handleQRScan;
    var cameraRef = (0, _react.useRef)(null);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      onFlash = _useState2[0],
      setOnFlash = _useState2[1];
    var inset = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    var _useState3 = (0, _react.useState)(true),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      needScan = _useState4[0],
      setNeedScan = _useState4[1];
    var codeTypeForQR = ['qr'];
    var codeTypeForBarCode = ['code-128', 'itf', 'code-39', 'code-93', 'pdf-417'];
    var onCodeScannedTypes = props != null && props.isBottomSheetBarCodeScanVisible ? codeTypeForBarCode : codeTypeForQR;
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      cameraStarted = _useState6[0],
      setCameraStarted = _useState6[1];
    var isFocused = (0, _native.useIsFocused)();
    var toggleFlashMode = function toggleFlashMode() {
      setOnFlash(!onFlash);
    };
    var handleGallerySelected = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* () {
        setTimeout(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
          try {
            var imageData = yield (0, _mediaHelper.choosePictureFromGallery)({
              multiple: false
            });
            var base64Img = (0, _lodash.get)(imageData, "base64");
            if (base64Img) {
              handleUploadCM(base64Img);
            }
          } catch (error) {}
        }), 200);
      });
      return function handleGallerySelected() {
        return _ref.apply(this, arguments);
      };
    }();
    var renderHeader = function renderHeader() {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: [_index.default.containerHeader, {
          height: inset.top + 60
        }],
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _index.default.wrapHeader,
          children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _index.default.leftHeader
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: toggleFlashMode,
            children: onFlash ? (0, _jsxRuntime.jsx)(_icons.FlashOn, {}) : (0, _jsxRuntime.jsx)(_icons.FlashOff, {})
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: onClosedSheet,
            style: _index.default.rightHeader,
            children: (0, _jsxRuntime.jsx)(_icons.CrossWhite, {
              width: 13,
              height: 13
            })
          })]
        })
      });
    };
    var renderFooter = function renderFooter() {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _index.default.wrapFooter,
        children: (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
          style: uploadQRButtonStyle,
          disabled: true,
          children: [(0, _jsxRuntime.jsx)(_icons.GalleryWithoutBorder, {}), (0, _jsxRuntime.jsx)(_text.Text, {
            text: "Upload QR",
            style: _index.default.textUpload,
            onPress: handleGallerySelected
          })]
        })
      });
    };
    var readFromCamera = function readFromCamera(codes, frame) {
      var _codes$, _codes$2;
      if ((codes == null ? undefined : codes.length) > 0 && codes != null && (_codes$ = codes[0]) != null && _codes$.type && codes != null && (_codes$2 = codes[0]) != null && _codes$2.value) {
        var _codes$3, _codes$4;
        if (codeTypeForBarCode.includes(codes == null || (_codes$3 = codes[0]) == null ? undefined : _codes$3.type) || codeTypeForQR.includes(codes == null || (_codes$4 = codes[0]) == null ? undefined : _codes$4.type)) {
          var _codes$5;
          setNeedScan(false);
          handleQRScan(codes == null || (_codes$5 = codes[0]) == null ? undefined : _codes$5.value);
        }
      }
    };
    var onCameraStarted = function onCameraStarted() {
      setTimeout(function () {
        setCameraStarted(true);
      }, 200);
    };
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _index.default.containerUploadReceipt,
        children: [(0, _jsxRuntime.jsx)(_camera.default, {
          ref: cameraRef,
          flashMode: onFlash,
          isCodeScanned: needScan,
          onCodeScanned: readFromCamera,
          onCodeScannedTypes: onCodeScannedTypes,
          isActive: isFocused,
          onStarted: onCameraStarted
        }), cameraStarted && (0, _jsxRuntime.jsxs)(_reactNativeHoleView.RNHoleView, {
          style: holeViewStyle,
          holes: [{
            x: POSITION_X,
            y: POSITION_Y,
            width: WIDTH_SCAN_VIEW,
            height: HEIGHT_SCAN_VIEW,
            borderRadius: 25
          }],
          children: [renderHeader(), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _index.default.wrapBellowContent,
            children: renderFooter()
          })]
        })]
      })
    });
  };
  var _default = exports.default = ScanQRCMScreen;
