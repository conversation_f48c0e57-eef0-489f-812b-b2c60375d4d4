  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _color = _$$_REQUIRE(_dependencyMap[1]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    bottomSheetContainer: {
      backgroundColor: _color.color.palette.transparent,
      flex: 1,
      justifyContent: "flex-end"
    },
    bottomSheetStyle: {
      backgroundColor: _color.color.palette.lightestGrey,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      height: _reactNative.Dimensions.get("window").height * 0.58 - 40
    },
    containerContent: {
      backgroundColor: _color.color.palette.transparent,
      flex: 1,
      justifyContent: "flex-end"
    },
    containerHeader: {
      alignContent: "center",
      alignItems: "center",
      backgroundColor: _color.color.palette.transparent,
      flex: 1,
      height: 40,
      justifyContent: "center"
    },
    containerStyle: {
      backgroundColor: _color.color.palette.lightestGrey,
      flex: 1
    },
    errorComponent: {
      marginTop: 0
    },
    exploreChangiTitle: {
      marginBottom: 32,
      paddingLeft: 24
    },
    headerContent: {
      marginTop: 0,
      position: "absolute",
      bottom: _reactNative.Dimensions.get("window").height * 0.58 - 40
    },
    iconChildrenHole: {
      height: 20,
      width: 25
    },
    iconCloseModalStyle: {
      height: 10,
      width: 35
    },
    itemShortcutLinkView: {
      alignItems: "center",
      justifyContent: "center",
      width: _reactNative.Dimensions.get("window").width / 3
    },
    mainContainerModal: {
      flex: 1
    },
    paddingStyle: {
      height: _reactNative.Platform.OS === "ios" ? 70 : 40
    },
    scrollBuddyButton: {
      bottom: 0,
      position: "absolute",
      right: 8
    },
    scrollBuddyImage: {
      height: 100,
      width: 100
    },
    separatorStyle: {
      height: 16
    },
    shortcutLinkContainer: {
      alignContent: "center",
      flex: 1,
      justifyContent: "center",
      zIndex: 100
    },
    swipeModalContentContainerStyle: {
      paddingBottom: 35
    },
    swipeModalHeaderContent: {
      alignItems: "center",
      justifyContent: "center"
    },
    titleBottomSheet: {
      marginBottom: 25,
      marginTop: 24,
      textAlign: "center"
    },
    viewContainerStyle: {
      backgroundColor: _color.color.palette.lightestGrey,
      marginBottom: 50,
      paddingTop: _reactNative.Platform.OS === "ios" ? 50 : 32
    },
    wrapChildrenHole: {
      alignItems: "center",
      backgroundColor: _color.color.palette.whiteGrey,
      borderRadius: 20,
      height: 40,
      justifyContent: "center",
      width: 40
    }
  });
