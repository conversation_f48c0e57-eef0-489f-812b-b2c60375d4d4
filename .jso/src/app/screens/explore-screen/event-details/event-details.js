  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.EventDetailsScreen = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _tags = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _tags2 = _$$_REQUIRE(_dependencyMap[5]);
  var _hero = _$$_REQUIRE(_dependencyMap[6]);
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _icons = _$$_REQUIRE(_dependencyMap[8]);
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[10]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[11]);
  var _reactNativeGestureHandler = _$$_REQUIRE(_dependencyMap[12]);
  var _native = _$$_REQUIRE(_dependencyMap[13]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[14]);
  var _exploreRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[15]));
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[16]));
  var _i18n = _$$_REQUIRE(_dependencyMap[17]);
  var _theme = _$$_REQUIRE(_dependencyMap[18]);
  var _dateTime = _$$_REQUIRE(_dependencyMap[19]);
  var _eventDetails = _$$_REQUIRE(_dependencyMap[20]);
  var _constants = _$$_REQUIRE(_dependencyMap[21]);
  var _calloutBanner = _$$_REQUIRE(_dependencyMap[22]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[23]));
  var _profileRedux = _$$_REQUIRE(_dependencyMap[24]);
  var _lodash = _$$_REQUIRE(_dependencyMap[25]);
  var _navigationType = _$$_REQUIRE(_dependencyMap[26]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[27]);
  var _errorCloud = _$$_REQUIRE(_dependencyMap[28]);
  var _exploreHelper = _$$_REQUIRE(_dependencyMap[29]);
  var _calloutBanner2 = _$$_REQUIRE(_dependencyMap[30]);
  var _infoBanner = _$$_REQUIRE(_dependencyMap[31]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[32]));
  var _button = _$$_REQUIRE(_dependencyMap[33]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[34]);
  var _utils = _$$_REQUIRE(_dependencyMap[35]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[36]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[37]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var HEADER_MAX_HEIGHT = 270;
  var HEADER_MIN_HEIGHT = _reactNative2.Platform.OS === "android" ? 70 : 84;
  var HEADER_SCROLL_DISTANCE = HEADER_MAX_HEIGHT - HEADER_MIN_HEIGHT;
  var SCREEN_NAME = "EventDetailsScreen";
  var renderHeaderEvent = function renderHeaderEvent(_ref) {
    var isHeaderIconsVisible = _ref.isHeaderIconsVisible,
      goBack = _ref.goBack;
    if (!isHeaderIconsVisible) {
      return null;
    }
    return (0, _jsxRuntime.jsxs)(_reactNative2.SafeAreaView, {
      style: styles.headerViewStyle,
      children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        onPress: goBack,
        style: styles.backButtonStyle,
        testID: `${SCREEN_NAME}__TouchableBackButton`,
        accessibilityLabel: `${SCREEN_NAME}__TouchableBackButton`,
        children: (0, _jsxRuntime.jsx)(_icons.BackButton, {})
      }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        onPress: function onPress() {
          return null;
        },
        style: styles.shareButtonStyle,
        testID: `${SCREEN_NAME}__TouchableShareButton`,
        accessibilityLabel: `${SCREEN_NAME}__TouchableShareButton`,
        children: (0, _jsxRuntime.jsx)(_icons.Share, {})
      })]
    });
  };
  var getLoadingFullScreen = function getLoadingFullScreen(eventDetailsType, eventStatusData) {
    return eventDetailsType === _eventDetails.EventDetailsType.loading || (eventStatusData == null ? undefined : eventStatusData.type) !== _calloutBanner2.CalloutBannerType.default;
  };
  var getSelectPasses = function getSelectPasses(isUserLoggedIn, eventStatusData) {
    return isUserLoggedIn && (eventStatusData == null ? undefined : eventStatusData.statusCodeSlotsAvailability) !== _eventDetails.EventStatusCodeSlotsAvailability.fullyBooked;
  };
  var renderEventStatusBanner = function renderEventStatusBanner(newEventStatusData) {
    if (!newEventStatusData) {
      return null;
    }
    var type = newEventStatusData.type,
      eventStatus = newEventStatusData.eventStatus;
    return (0, _jsxRuntime.jsx)(_calloutBanner.CalloutBanner, {
      type: type,
      isVisible: eventStatus == null ? undefined : eventStatus.isDisplay,
      iconUrl: eventStatus == null ? undefined : eventStatus.iconUrl,
      messageBold: (eventStatus == null ? undefined : eventStatus.mainMessage) || "",
      messageNormal: (eventStatus == null ? undefined : eventStatus.subMessage) || "",
      contentColorCode: (eventStatus == null ? undefined : eventStatus.colorContentCode) || "",
      layoutColorCode: (eventStatus == null ? undefined : eventStatus.colorLayoutCode) || ""
    });
  };
  var renderEventTokensBanner = function renderEventTokensBanner(newEventTokensData, tokenBannerCTACallback) {
    if (!newEventTokensData) {
      return null;
    }
    var type = newEventTokensData.type,
      bannerToken = newEventTokensData.bannerToken;
    return (type === _infoBanner.InfoBannerType.loading || !(0, _lodash.isEmpty)(bannerToken)) && (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: styles.eventTokensContainerStyle,
      children: (0, _jsxRuntime.jsx)(_infoBanner.InfoBanner, {
        type: type,
        state: _infoBanner.InfoBannerState.permanent,
        title: (bannerToken == null ? undefined : bannerToken.title) || "",
        text: (bannerToken == null ? undefined : bannerToken.description) || "",
        iconUrl: (bannerToken == null ? undefined : bannerToken.iconUrl) || "",
        linkText: (bannerToken == null ? undefined : bannerToken.linkText) || "",
        onPressed: function onPressed() {
          tokenBannerCTACallback(bannerToken);
        },
        testID: `${SCREEN_NAME}__InfoBanner`,
        accessibilityLabel: `${SCREEN_NAME}__InfoBanner`
      })
    });
  };
  var renderEventDetail = function renderEventDetail(eventTags, eventDetails, eventDetailsType) {
    if ((eventTags == null ? undefined : eventTags.length) > 0) {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.pageHeaderStyle,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          preset: "h1",
          style: styles.titleText,
          children: eventDetails.title
        }), (0, _jsxRuntime.jsx)(_tags.Tags, {
          type: eventDetailsType,
          tagsData: eventTags,
          orientation: _tags2.TagsOrientation.horizontal
        })]
      });
    }
    return null;
  };
  var renderLocationSubText = function renderLocationSubText(locationSubText) {
    if (locationSubText) {
      return (0, _jsxRuntime.jsx)(_text.Text, {
        preset: "bodyTextRegular",
        style: [styles.text, {
          color: _theme.color.palette.darkestGrey
        }],
        numberOfLines: 1,
        text: locationSubText
      });
    }
    return null;
  };
  var renderNeedLocationCTA = function renderNeedLocationCTA(needLocationCTA, hasMultipleLocations, showLocations) {
    if (needLocationCTA) {
      return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        onPress: function onPress() {
          if (hasMultipleLocations) {
            showLocations();
          }
        },
        testID: `${SCREEN_NAME}__TouchableShowLocation`,
        accessibilityLabel: `${SCREEN_NAME}__TouchableShowLocation`,
        style: visibleGetdirections,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "textLink",
          text: (0, _utils.handleCondition)(hasMultipleLocations, (0, _i18n.translate)("eventDetailsScreen.viewLocations"), (0, _i18n.translate)("eventDetailsScreen.getDirections")),
          style: styles.directionTextStyle
        })
      });
    }
    return null;
  };
  var renderLocationSection = function renderLocationSection(locationText, locationSubText, needLocationCTA, hasMultipleLocations, showLocations) {
    if (!locationText) {
      return null;
    }
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.sectionContainerStyle,
      children: [(0, _jsxRuntime.jsx)(_icons.Directions, {
        style: styles.directionIconstyle
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.locationInfoViewStyle,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          preset: "bodyTextBold",
          style: styles.text,
          text: locationText
        }), renderLocationSubText(locationSubText), renderNeedLocationCTA(needLocationCTA, hasMultipleLocations, showLocations)]
      })]
    });
  };
  var visibleGetdirections = {
    display: "none"
  };
  var EventDetailsScreen = exports.EventDetailsScreen = function EventDetailsScreen(_ref2) {
    var _eventDetails$imageHe, _eventDetails$locatio, _eventDetails$locatio2, _eventDetails$locatio3, _eventDetails$locatio4, _eventDetails$dateDet;
    var route = _ref2.route,
      navigation = _ref2.navigation;
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useState = (0, _react.useState)(true),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isHeaderIconsVisible = _useState2[0],
      setHeaderIconsVisible = _useState2[1];

    // Code to animate the header up on scroll
    var scrollY = (0, _react.useRef)(new _reactNative2.Animated.Value(0)).current;
    var headerTranslateY = scrollY.interpolate({
      inputRange: [0, HEADER_SCROLL_DISTANCE],
      outputRange: [0, -HEADER_SCROLL_DISTANCE],
      extrapolate: "clamp"
    });
    var imageOpacity = scrollY.interpolate({
      inputRange: [0, HEADER_SCROLL_DISTANCE / 2, HEADER_SCROLL_DISTANCE],
      outputRange: [1, 1, 0],
      extrapolate: "clamp"
    });
    var imageTranslateY = scrollY.interpolate({
      inputRange: [0, HEADER_SCROLL_DISTANCE],
      outputRange: [0, 100],
      extrapolate: "clamp"
    });
    var titleScale = scrollY.interpolate({
      inputRange: [0, HEADER_SCROLL_DISTANCE / 2, HEADER_SCROLL_DISTANCE],
      outputRange: [1, 1, 1],
      extrapolate: "clamp"
    });
    var titleTranslateY = scrollY.interpolate({
      inputRange: [0, HEADER_SCROLL_DISTANCE / 2, HEADER_SCROLL_DISTANCE],
      outputRange: [0, 0, -8],
      extrapolate: "clamp"
    });
    var heroContainerStyle = {
      opacity: imageOpacity,
      transform: [{
        translateY: imageTranslateY
      }]
    };
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isBottomSheetVisible = _useState4[0],
      setIsBottomSheetVisible = _useState4[1];
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var isUserLoggedIn = (0, _reactRedux.useSelector)(function (state) {
      return _nativeAuthRedux.NativeAuthSelectors.isLoggedIn(state);
    });
    var eventDetailsPayload = (0, _reactRedux.useSelector)(function (data) {
      return _exploreRedux.ExploreSelectors.eventDetailsData(data);
    });
    var processCRTransactionsTimeStamp = (0, _reactRedux.useSelector)(function (state) {
      return _exploreRedux.ExploreSelectors.getProcessCRTransactionsTimeStamp(state);
    });
    var eventDetails = (0, _lodash.get)(eventDetailsPayload, "eventDetails", {});
    var isEventDetailsError = (0, _lodash.get)(eventDetailsPayload, "hasError");
    var eventDetailsType = (0, _lodash.get)(eventDetailsPayload, "type");
    var eventStatusData = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.eventStatusData);
    var eventTokensData = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.eventTokensData);
    var isLoadingFullScreen = getLoadingFullScreen(eventDetailsType, eventStatusData);
    var isSelectPasses = getSelectPasses(isUserLoggedIn, eventStatusData);
    var isEventDetailsTypeDefault = eventDetailsType === _eventDetails.EventDetailsType.default;
    var isEventDetails = !!eventDetails;
    var isEmptyData = isEventDetailsTypeDefault && !eventDetails;
    var bookingPageUrlPayload = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.bookingPageUrlData);
    var editBookingPageUrl = (0, _lodash.get)(bookingPageUrlPayload, "data.editBookingPageUrl");
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("EVENT_DETAIL"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    // event details via api
    var _route$params = Object.assign({}, route == null ? undefined : route.params),
      _route$params$package = _route$params.packageCode,
      packageCode = _route$params$package === undefined ? "" : _route$params$package,
      _route$params$isFromP = _route$params.isFromPlayPassCart,
      isFromPlayPassCart = _route$params$isFromP === undefined ? false : _route$params$isFromP;
    var eventPackageCode = packageCode || (eventDetails == null ? undefined : eventDetails.packageCode);
    (0, _react.useEffect)(function () {
      isEmptyData && navigation.setParams({
        packageCode: ""
      });
    }, [isEmptyData]);
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      if (profilePayload != null && profilePayload.email && isFromPlayPassCart && !eventPackageCode) {
        dispatch(_exploreRedux.default.bookingPageUrlRequest({
          email: profilePayload == null ? undefined : profilePayload.email
        }));
      } else {
        dispatch(_exploreRedux.default.bookingPageUrlReset());
      }
    }, [isFromPlayPassCart, eventPackageCode]));

    /**
     * Function to check the time difference between the current time and the last CRTransactions processed timestamp
     * @param timeStamp CR Transactions processed timestamp
     * @returns difference in seconds between the current time and the timestamp passed
     */
    var isProcessCRTransactionsNeeded = _react.default.useCallback(function (timeStamp) {
      if (timeStamp === "") {
        return true;
      } else {
        var CRTransactionsPerformedTime = (0, _moment.default)(timeStamp);
        var currentTimeStamp = (0, _moment.default)();
        var duration = _moment.default.duration(currentTimeStamp.diff(CRTransactionsPerformedTime)).asSeconds();
        return duration >= _eventDetails.PROCESS_CRTRANSACTIONS_THRESHOLD;
      }
    }, []);
    var getEventTokens = _react.default.useCallback(function () {
      if (eventPackageCode && isEventDetailsTypeDefault && isEventDetails) {
        var needProcessCRTransactions = isProcessCRTransactionsNeeded(processCRTransactionsTimeStamp);
        if (profilePayload != null && profilePayload.email) {
          dispatch(_exploreRedux.default.selectedEventTokensBannerRequest(eventPackageCode, profilePayload == null ? undefined : profilePayload.email, needProcessCRTransactions));
        }
      }
    }, [eventPackageCode, isEventDetailsTypeDefault, isEventDetails]);
    (0, _react.useEffect)(function () {
      getEventTokens();
    }, [getEventTokens]);
    var heroCarouselImage = eventDetails == null || (_eventDetails$imageHe = eventDetails.imageHero) == null ? undefined : _eventDetails$imageHe.map(function (heroUrl, index) {
      return {
        backgroundImageUrl: heroUrl,
        orderId: index
      };
    });
    var eventTags = (0, _lodash.get)(eventDetails, "tags", []);
    var hasMultipleLocations = (eventDetails == null || (_eventDetails$locatio = eventDetails.location) == null ? undefined : _eventDetails$locatio.length) > 1;
    var locationText = hasMultipleLocations ? (0, _i18n.translate)("eventDetailsScreen.multipleLocations") : eventDetails == null || (_eventDetails$locatio2 = eventDetails.location) == null ? undefined : _eventDetails$locatio2[0];
    var needLocationCTA = hasMultipleLocations || (eventDetails == null || (_eventDetails$locatio3 = eventDetails.location) == null || (_eventDetails$locatio3 = _eventDetails$locatio3[0]) == null ? undefined : _eventDetails$locatio3.toLowerCase()) !== _eventDetails.LocationType.multipleLocations.toLowerCase();
    var locationSubText = hasMultipleLocations && needLocationCTA ? eventDetails == null || (_eventDetails$locatio4 = eventDetails.location) == null ? undefined : _eventDetails$locatio4.join(",") : null;
    if (_reactNative2.Platform.OS === "android") {
      _reactNative2.UIManager.setLayoutAnimationEnabledExperimental(true);
    }
    var onClosedSheet = _react.default.useCallback(function () {
      setIsBottomSheetVisible(false);
    }, []);
    var goBack = _react.default.useCallback(function () {
      navigation.goBack();
    }, []);
    var renderLocationItem = function renderLocationItem(_ref3) {
      var item = _ref3.item,
        index = _ref3.index;
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.locationItemContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_icons.Directions, {
          style: styles.directionIconstyle
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.locationInfoViewStyle,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            preset: "bodyTextBold",
            style: styles.text,
            text: item
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            testID: `${SCREEN_NAME}__TouchableLocationItem`,
            accessibilityLabel: `${SCREEN_NAME}__TouchableLocationItem`,
            style: visibleGetdirections,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              preset: "textLink",
              text: (0, _i18n.translate)("restaurantDetails.getDirection"),
              style: styles.directionTextStyle
            })
          })]
        })]
      }, index);
    };
    var renderSeparator = function renderSeparator() {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.dividerStyle
      });
    };
    var renderPriceInformation = function renderPriceInformation(ticketPrices) {
      var tickets = ticketPrices == null ? undefined : ticketPrices.filter(function (item) {
        return item == null ? undefined : item.trim();
      });
      if ((tickets == null ? undefined : tickets.length) === 0) {
        return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {});
      }
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.sectionContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_icons.Ticket, {}), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.ticketContentStyle,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            preset: "bodyTextBold",
            style: styles.text,
            text: (0, _i18n.translate)("eventDetailsScreen.ticketPrices")
          }), tickets.map(function (ticketPrice, index) {
            var priceDetails = ticketPrice.split(",").filter(function (item) {
              return item;
            });
            var isOptionalFieldPresent = priceDetails.length > 2;
            return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: styles.ticketDetailsContainerStyle,
              children: [(0, _jsxRuntime.jsxs)(_text.Text, {
                style: [styles.priceItemText, styles.priceItemContainer],
                children: [(0, _jsxRuntime.jsx)(_text.Text, {
                  preset: "bodyTextRegular",
                  style: styles.priceItemText,
                  text: isOptionalFieldPresent ? `${priceDetails[0]}\n` : priceDetails[0]
                }), isOptionalFieldPresent ? (0, _jsxRuntime.jsx)(_text.Text, {
                  preset: "bodyTextItalic",
                  style: styles.priceItemText,
                  text: priceDetails[1]
                }) : null]
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                preset: "bodyTextRegular",
                style: styles.priceItemText,
                text: priceDetails[priceDetails.length - 1]
              })]
            }, index);
          })]
        })]
      });
    };
    var tokenBannerCTACallback = _react.default.useCallback(function (token) {
      var _ref4 = token || "",
        navigationType = _ref4.navigationType,
        navigationValue = _ref4.navigationValue,
        redirect = _ref4.redirect;
      switch (navigationType) {
        case _navigationType.NavigationTypeEnum.external:
          navigation.navigate(_constants.NavigationConstants.webview, {
            uri: navigationValue
          });
          break;
        case _navigationType.NavigationTypeEnum.playpass:
          if (isUserLoggedIn) {
            navigation.navigate(_constants.NavigationConstants.playpassWebview, {
              uri: navigationValue,
              title: (0, _i18n.translate)("playPassWebView.uploadReceiptTitle"),
              needCloseButton: true
            });
          }
          break;
        default:
          handleNavigation(navigationType, navigationValue, redirect);
          break;
      }
    }, []);
    var showLocations = _react.default.useCallback(function () {
      setIsBottomSheetVisible(true);
    }, []);

    // event extra details
    var aboutDetails = (0, _lodash.get)(eventDetails, "aboutDetails");
    var aboutLink = (0, _lodash.get)(eventDetails, "aboutLink");
    var entryRequirements = (0, _lodash.get)(eventDetails, "entryRequirements");
    var termAndConditions = (0, _lodash.get)(eventDetails, "termAndConditions");
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isLinkLoading = _useState6[0],
      setIsLinkLoading = _useState6[1];
    var initOpenLink = _react.default.useCallback(/*#__PURE__*/function () {
      var _ref5 = (0, _asyncToGenerator2.default)(function* (uri) {
        setIsLinkLoading(true);
        yield (0, _exploreHelper.openLink)(navigation, uri);
        setIsLinkLoading(false);
      });
      return function (_x) {
        return _ref5.apply(this, arguments);
      };
    }(), []);
    var openTermsAndConditions = _react.default.useCallback(function () {
      initOpenLink(termAndConditions);
    }, [termAndConditions]);
    var openAboutLink = _react.default.useCallback(function () {
      initOpenLink(aboutLink);
    }, [aboutLink]);
    var loadEventDetails = _react.default.useCallback(function () {
      packageCode && dispatch(_exploreRedux.default.eventDetailsRequest({
        packageCode: packageCode,
        email: profilePayload == null ? undefined : profilePayload.email
      }));
    }, [packageCode]);
    var loadEventStatus = _react.default.useCallback(function () {
      if (eventPackageCode && isEventDetailsTypeDefault && isEventDetails) {
        dispatch(_exploreRedux.default.selectedEventStatusBannerRequest(eventPackageCode));
      }
    }, [eventPackageCode, isEventDetailsTypeDefault, isEventDetails]);
    var browseAllEvents = _react.default.useCallback(function () {
      navigation.navigate((0, _i18n.translate)("bottomNavigation.explore"), {
        isScrollToExploreChangiSection: true
      });
    }, []);
    var viewCart = _react.default.useCallback(function () {
      navigation.navigate(_constants.NavigationConstants.playpassWebview, {
        uri: editBookingPageUrl,
        title: (0, _i18n.translate)("playPassWebView.cartTitle"),
        needBackButton: true
      });
    }, [editBookingPageUrl]);
    var selectPassesCTAPressed = /*#__PURE__*/function () {
      var _ref6 = (0, _asyncToGenerator2.default)(function* () {
        if (isUserLoggedIn) {
          navigation.navigate(_constants.NavigationConstants.playpassWebview, {
            uri: eventDetails == null ? undefined : eventDetails.bookingUrl,
            title: (0, _i18n.translate)("playPassWebView.bookingTitle"),
            needBackButton: true
          });
        }
      });
      return function selectPassesCTAPressed() {
        return _ref6.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      loadEventDetails();
    }, []);
    (0, _react.useEffect)(function () {
      loadEventStatus();
    }, []);
    if (isEventDetailsError || !eventPackageCode || isEmptyData) {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.container,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.SafeAreaView, {
          style: styles.headerContainer,
          children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: goBack,
            testID: `${SCREEN_NAME}__TouchableArrowLeft`,
            accessibilityLabel: `${SCREEN_NAME}__TouchableArrowLeft`,
            children: (0, _jsxRuntime.jsx)(_icons.ArrowLeft, {
              width: 24,
              height: 24
            })
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "subTitleBold",
            style: styles.headerTextStyle,
            text: (0, _i18n.translate)("eventDetailsScreen.noEventFound"),
            numberOfLines: 1
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.dummyViewStyle
          })]
        }), isEventDetailsError && (0, _jsxRuntime.jsx)(_errorCloud.ErrorCloudComponent, {
          title: "eventDetailsScreen.errorTitle",
          content: "eventDetailsScreen.errorContent",
          onPress: loadEventDetails,
          style: styles.errorCloudComponentStyle,
          testID: `${SCREEN_NAME}__ErrorCloudComponentLoadEventDetail`,
          accessibilityLabel: `${SCREEN_NAME}__ErrorCloudComponentLoadEventDetail`
        }), (!eventPackageCode || isEmptyData) && (0, _jsxRuntime.jsx)(_errorCloud.ErrorCloudComponent, {
          title: "eventDetailsScreen.errorTitle",
          content: "eventDetailsScreen.noEventFoundErrorContent",
          buttonTx: "eventDetailsScreen.browseAllEvents",
          onPress: browseAllEvents,
          isLink: !!editBookingPageUrl && isFromPlayPassCart,
          linkTx: "eventDetailsScreen.viewYourCart",
          onLinkPress: viewCart,
          style: styles.errorCloudComponentStyle,
          testID: `${SCREEN_NAME}__ErrorCloudComponentBrowseAllEvents`,
          accessibilityLabel: `${SCREEN_NAME}__ErrorCloudComponentBrowseAllEvents`
        })]
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.container,
      testID: "EventDetailsScreen",
      children: [renderHeaderEvent({
        goBack: goBack,
        isHeaderIconsVisible: isHeaderIconsVisible
      }), (0, _utils.handleCondition)(isLoadingFullScreen, (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
        visible: true
      }), (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsx)(_reactNative2.Animated.ScrollView, {
          showsVerticalScrollIndicator: false,
          contentContainerStyle: {
            paddingTop: HEADER_MAX_HEIGHT
          },
          scrollEventThrottle: 16,
          onScroll: _reactNative2.Animated.event([{
            nativeEvent: {
              contentOffset: {
                y: scrollY
              }
            }
          }], {
            listener: function listener(event) {
              var nativeEvent = event.nativeEvent;
              if (nativeEvent.contentOffset.y > HEADER_SCROLL_DISTANCE) {
                isHeaderIconsVisible && setHeaderIconsVisible(false);
              } else {
                !isHeaderIconsVisible && setHeaderIconsVisible(true);
              }
            },
            useNativeDriver: false
          }),
          testID: `${SCREEN_NAME}__AnimatedScrollView`,
          accessibilityLabel: `${SCREEN_NAME}__AnimatedScrollView`,
          children: (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
            children: (0, _utils.handleCondition)(eventDetails, (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
              children: [renderEventStatusBanner(eventStatusData), renderEventDetail(eventTags, eventDetails, eventDetailsType), renderEventTokensBanner(eventTokensData, tokenBannerCTACallback), renderLocationSection(locationText, locationSubText, needLocationCTA, hasMultipleLocations, showLocations), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: styles.sectionContainerStyle,
                children: [(0, _jsxRuntime.jsx)(_icons.Calendar, {
                  fill: _theme.color.palette.darkGrey,
                  style: styles.calendarIconstyle
                }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                  style: styles.locationInfoViewStyle,
                  children: [(0, _jsxRuntime.jsx)(_text.Text, {
                    preset: "bodyTextBold",
                    style: styles.text,
                    text: (0, _dateTime.eventCardDateFormatting)(eventDetails.eventStart, eventDetails.eventEnd)
                  }), (0, _jsxRuntime.jsx)(_text.Text, {
                    preset: "bodyTextRegular",
                    style: [styles.text, {
                      color: _theme.color.palette.darkestGrey
                    }],
                    text: eventDetails == null || (_eventDetails$dateDet = eventDetails.dateDetails) == null || (_eventDetails$dateDet = _eventDetails$dateDet.toString()) == null || (_eventDetails$dateDet = _eventDetails$dateDet.split(";")) == null || (_eventDetails$dateDet = _eventDetails$dateDet.map(function (dateDetailText) {
                      return `${dateDetailText == null ? undefined : dateDetailText.trim()}`;
                    })) == null ? undefined : _eventDetails$dateDet.join("\n")
                  })]
                })]
              }), eventDetails.ticketPrices && renderPriceInformation(eventDetails.ticketPrices), renderSeparator(), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: styles.extraDetailsContainer,
                children: [!!aboutDetails && (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                  style: styles.sectionWithHeaderContainerStyle,
                  children: [(0, _jsxRuntime.jsx)(_text.Text, {
                    preset: "h4",
                    children: (0, _i18n.translate)("eventDetailsScreen.about")
                  }), (0, _jsxRuntime.jsx)(_text.Text, {
                    preset: "bodyTextRegular",
                    style: styles.sectionContentTextStyle,
                    children: aboutDetails
                  })]
                }), !!aboutLink && (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
                  children: [!aboutDetails && (0, _jsxRuntime.jsx)(_text.Text, {
                    preset: "h4",
                    style: styles.sectionHeaderTextStyle,
                    children: (0, _i18n.translate)("eventDetailsScreen.about")
                  }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
                    style: styles.sectionLinkContainerStyle,
                    onPress: openAboutLink,
                    testID: `${SCREEN_NAME}__TouchableFindOutMore`,
                    accessibilityLabel: `${SCREEN_NAME}__TouchableFindOutMore`,
                    children: [(0, _jsxRuntime.jsx)(_text.Text, {
                      preset: "textLink",
                      style: styles.sectionLinkTextStyle,
                      children: (0, _i18n.translate)("eventDetailsScreen.findOutMore")
                    }), (0, _jsxRuntime.jsx)(_icons.ExternalLink, {
                      width: 24,
                      height: 24
                    })]
                  })]
                }), !!entryRequirements && (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                  style: styles.sectionWithHeaderContainerStyle,
                  children: [(0, _jsxRuntime.jsx)(_text.Text, {
                    preset: "h4",
                    children: (0, _i18n.translate)("eventDetailsScreen.entryRequirements")
                  }), (0, _jsxRuntime.jsx)(_text.Text, {
                    preset: "bodyTextRegular",
                    style: styles.sectionContentTextStyle,
                    children: eventDetails.entryRequirements
                  })]
                }), !!termAndConditions && (0, _jsxRuntime.jsx)(_reactNative2.View, {
                  style: styles.sectionWithHeaderContainerStyle,
                  children: (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
                    style: styles.sectionHeaderStyle,
                    onPress: openTermsAndConditions,
                    testID: `${SCREEN_NAME}__TouchableTermsAndConditions`,
                    accessibilityLabel: `${SCREEN_NAME}__TouchableTermsAndConditions`,
                    children: [(0, _jsxRuntime.jsx)(_text.Text, {
                      preset: "h4",
                      children: (0, _i18n.translate)("eventDetailsScreen.termsAndConditions")
                    }), (0, _jsxRuntime.jsx)(_icons.ExternalLink, {
                      width: 24,
                      height: 24
                    })]
                  })
                })]
              }), (0, _jsxRuntime.jsxs)(_bottomSheet.default, {
                isModalVisible: isBottomSheetVisible,
                onClosedSheet: onClosedSheet,
                containerStyle: styles.bottomSheetStyle,
                stopDragCollapse: true,
                onBackPressHandle: onClosedSheet,
                animationInTiming: 500,
                animationOutTiming: 500,
                children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                  style: styles.downArrowStyle,
                  onPress: onClosedSheet,
                  testID: `${SCREEN_NAME}__TouchableClosedSheet`,
                  accessibilityLabel: `${SCREEN_NAME}__TouchableClosedSheet`,
                  children: (0, _jsxRuntime.jsx)(_icons.DownArrowWhite, {})
                }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
                  style: styles.locationListContainerStyle,
                  children: (0, _jsxRuntime.jsx)(_reactNativeGestureHandler.FlatList, {
                    style: styles.locationListStyle,
                    data: eventDetails == null ? undefined : eventDetails.location,
                    keyExtractor: function keyExtractor(_item, index) {
                      return index.toString();
                    },
                    renderItem: renderLocationItem,
                    ItemSeparatorComponent: function ItemSeparatorComponent() {
                      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
                        style: styles.listItemSeperatorStyle
                      });
                    },
                    testID: `${SCREEN_NAME}__FlatListLocationItem`,
                    accessibilityLabel: `${SCREEN_NAME}__FlatListLocationItem`
                  })
                })]
              })]
            }), null)
          })
        }), isSelectPasses && (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.selectPassesCTAContainer,
          children: (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
            style: styles.CTAButtonLinearStyle,
            start: {
              x: 0,
              y: 1
            },
            end: {
              x: 1,
              y: 0
            },
            colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
            children: (0, _jsxRuntime.jsx)(_button.Button, {
              sizePreset: "large",
              textPreset: "buttonLarge",
              typePreset: "secondary",
              tx: "eventDetailsScreen.selectPasses",
              statePreset: "default",
              backgroundPreset: "light",
              onPress: selectPassesCTAPressed,
              testID: `${SCREEN_NAME}__ButtonSelectPassesCTA`,
              accessibilityLabel: `${SCREEN_NAME}__ButtonSelectPassesCTA`
            })
          })
        }), (0, _jsxRuntime.jsx)(_reactNative2.Animated.View, {
          style: [styles.header, {
            transform: [{
              translateY: headerTranslateY
            }]
          }],
          children: (0, _jsxRuntime.jsx)(_reactNative2.Animated.View, {
            style: heroContainerStyle,
            children: (0, _jsxRuntime.jsx)(_hero.Hero, {
              heroImagesUrl: heroCarouselImage,
              logoImageUrl: null,
              type: eventDetailsType
            })
          })
        }), (0, _jsxRuntime.jsx)(_reactNative2.Animated.View, {
          style: [styles.topBar, {
            transform: [{
              scale: titleScale
            }, {
              translateY: titleTranslateY
            }]
          }],
          children: !isHeaderIconsVisible && (0, _jsxRuntime.jsxs)(_reactNative2.SafeAreaView, {
            style: styles.headerContainer,
            children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              onPress: goBack,
              testID: `${SCREEN_NAME}__TouchableArrowLeft`,
              accessibilityLabel: `${SCREEN_NAME}__TouchableArrowLeft`,
              children: (0, _jsxRuntime.jsx)(_icons.ArrowLeft, {
                width: 24,
                height: 24
              })
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              preset: "subTitleBold",
              style: styles.headerTextStyle,
              text: eventDetails == null ? undefined : eventDetails.title,
              numberOfLines: 1
            }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              onPress: function onPress() {
                return null;
              },
              testID: `${SCREEN_NAME}__TouchableShareBlue`,
              accessibilityLabel: `${SCREEN_NAME}__TouchableShareBlue`,
              children: (0, _jsxRuntime.jsx)(_icons.ShareBlue, {
                width: 24,
                height: 24
              })
            })]
          })
        })]
      })), isLinkLoading && (0, _jsxRuntime.jsx)(_reactNative2.ActivityIndicator, {
        style: styles.activityIndicatorViewStyle,
        size: "large",
        color: _theme.color.palette.lightPurple
      })]
    });
  };
