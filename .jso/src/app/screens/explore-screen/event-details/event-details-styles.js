  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.topBar = exports.titleText = exports.ticketDetailsContainerStyle = exports.ticketContentStyle = exports.text = exports.shareButtonStyle = exports.selectPassesCTAContainer = exports.sectionWithHeaderContainerStyle = exports.sectionLinkTextStyle = exports.sectionLinkContainerStyle = exports.sectionHeaderTextStyle = exports.sectionHeaderStyle = exports.sectionContentTextStyle = exports.sectionContainerStyle = exports.priceItemText = exports.priceItemContainer = exports.pageHeaderStyle = exports.locationListStyle = exports.locationListContainerStyle = exports.locationItemContainerStyle = exports.locationInfoViewStyle = exports.listItemSeperatorStyle = exports.headerViewStyle = exports.headerTextStyle = exports.headerContainer = exports.header = exports.extraDetailsContainer = exports.eventTokensContainerStyle = exports.errorCloudComponentStyle = exports.dummyViewStyle = exports.downArrowStyle = exports.dividerStyle = exports.directionTextStyle = exports.directionIconstyle = exports.container = exports.calendarIconstyle = exports.bottomSheetStyle = exports.backButtonStyle = exports.activityIndicatorViewStyle = exports.CTAButtonLinearStyle = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var container = exports.container = {
    flex: 1,
    backgroundColor: _theme.color.palette.whiteGrey
  };
  var headerViewStyle = exports.headerViewStyle = {
    position: "absolute",
    zIndex: 11,
    width: "100%",
    marginTop: 24,
    flexDirection: "row",
    justifyContent: "space-between"
  };
  var backButtonStyle = exports.backButtonStyle = {
    marginStart: 14
  };
  var shareButtonStyle = exports.shareButtonStyle = {
    alignItems: "flex-end",
    marginEnd: 14
  };
  var pageHeaderStyle = exports.pageHeaderStyle = {
    marginLeft: 25,
    marginTop: 16
  };
  var header = exports.header = {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: _theme.color.palette.almostWhiteGrey,
    overflow: "hidden",
    height: 270
  };
  var headerContainer = exports.headerContainer = {
    flexDirection: "row",
    justifyContent: "space-between",
    margin: 16
  };
  var headerTextStyle = exports.headerTextStyle = {
    flex: 1,
    textAlign: "center"
  };
  var topBar = exports.topBar = {
    flex: 1,
    marginTop: _reactNative.Platform.OS === "android" ? 16 : 40,
    position: "absolute",
    top: 0,
    left: 0,
    right: 0
  };
  var titleText = exports.titleText = {
    color: _theme.color.palette.almostBlackGrey,
    marginBottom: 12,
    fontWeight: _reactNative.Platform.OS === "android" ? "normal" : "700"
  };
  var sectionContainerStyle = exports.sectionContainerStyle = {
    flexDirection: "row",
    marginTop: 24,
    marginLeft: 25,
    marginRight: 22
  };
  var locationItemContainerStyle = exports.locationItemContainerStyle = {
    flexDirection: "row",
    marginLeft: 25,
    marginRight: 22
  };
  var directionIconstyle = exports.directionIconstyle = {
    marginRight: 5
  };
  var calendarIconstyle = exports.calendarIconstyle = {
    marginRight: 5
  };
  var ticketContentStyle = exports.ticketContentStyle = {
    flex: 1,
    marginLeft: 5
  };
  var ticketDetailsContainerStyle = exports.ticketDetailsContainerStyle = {
    flexDirection: "row",
    justifyContent: "space-between"
  };
  var locationInfoViewStyle = exports.locationInfoViewStyle = {
    marginRight: 24,
    flex: 1
  };
  var text = exports.text = {
    color: _theme.color.palette.almostBlackGrey,
    paddingLeft: 12,
    paddingBottom: 6
  };
  var priceItemText = exports.priceItemText = Object.assign({}, text, {
    color: _theme.color.palette.darkestGrey
  });
  var priceItemContainer = exports.priceItemContainer = {
    maxWidth: "70%"
  };
  var bottomSheetStyle = exports.bottomSheetStyle = {
    height: 449,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16
  };
  var directionTextStyle = exports.directionTextStyle = {
    paddingLeft: 12,
    fontWeight: _reactNative.Platform.OS === "android" ? "normal" : "700"
  };
  var dividerStyle = exports.dividerStyle = {
    height: 1,
    backgroundColor: _theme.color.palette.lighterGrey,
    marginVertical: 24,
    marginHorizontal: 24
  };
  var listItemSeperatorStyle = exports.listItemSeperatorStyle = {
    height: 1,
    backgroundColor: _theme.color.palette.lighterGrey,
    marginVertical: 16,
    marginHorizontal: 24
  };
  var locationListStyle = exports.locationListStyle = {
    backgroundColor: _theme.color.palette.whiteGrey
  };
  var downArrowStyle = exports.downArrowStyle = {
    alignItems: "center",
    marginBottom: 16
  };
  var locationListContainerStyle = exports.locationListContainerStyle = {
    backgroundColor: _theme.color.palette.whiteGrey,
    paddingVertical: 40,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    flex: 1
  };
  var selectPassesCTAContainer = exports.selectPassesCTAContainer = Object.assign({
    padding: 24,
    backgroundColor: _theme.color.palette.almostWhiteGrey
  }, _theme.shadow.primaryShadow);
  var CTAButtonLinearStyle = exports.CTAButtonLinearStyle = {
    borderRadius: 60
  };
  var sectionWithHeaderContainerStyle = exports.sectionWithHeaderContainerStyle = {
    flexDirection: "column",
    marginHorizontal: 25,
    marginTop: 12,
    marginBottom: 24
  };
  var sectionHeaderStyle = exports.sectionHeaderStyle = {
    flexDirection: "row",
    justifyContent: "space-between"
  };
  var sectionHeaderTextStyle = exports.sectionHeaderTextStyle = {
    marginHorizontal: 25,
    marginBottom: 24
  };
  var sectionLinkContainerStyle = exports.sectionLinkContainerStyle = {
    flexDirection: "row",
    marginHorizontal: 25,
    marginTop: -12,
    marginBottom: 32
  };
  var sectionLinkTextStyle = exports.sectionLinkTextStyle = {
    marginRight: 6
  };
  var sectionContentTextStyle = exports.sectionContentTextStyle = {
    marginTop: 12,
    marginBottom: 6,
    color: _theme.color.palette.darkestGrey
  };
  var extraDetailsContainer = exports.extraDetailsContainer = {
    paddingBottom: 24
  };
  var eventTokensContainerStyle = exports.eventTokensContainerStyle = {
    justifyContent: "center",
    paddingHorizontal: 24,
    paddingTop: 16
  };
  var activityIndicatorViewStyle = exports.activityIndicatorViewStyle = {
    position: "absolute",
    top: 0,
    left: 0,
    bottom: 0,
    right: 0
  };
  var dummyViewStyle = exports.dummyViewStyle = {
    width: 32
  };
  var errorCloudComponentStyle = exports.errorCloudComponentStyle = {
    flex: 1
  };
