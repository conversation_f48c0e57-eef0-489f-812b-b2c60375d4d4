  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[0]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[2]);
  var _theme = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var getDayStyle = function getDayStyle(_ref) {
    var isActive = _ref.isActive,
      isPast = _ref.isPast;
    if (isActive) {
      return styles.textActive;
    }
    if (isPast) {
      return styles.textPass;
    }
    return styles.textDay;
  };
  var DayItem = function DayItem(props) {
    var day = props.day,
      isActive = props.isActive,
      _onPress = props.onPress,
      dateString = props.dateString,
      isPast = props.isPast;
    var dayStyle = getDayStyle({
      isActive: isActive,
      isPast: isPast
    });
    return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
      style: isActive ? styles.touchActive : styles.touchDay,
      onPress: function onPress() {
        _onPress(dateString);
      },
      disabled: isPast,
      children: (0, _jsxRuntime.jsx)(_text.Text, {
        style: dayStyle,
        children: day
      })
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    textActive: {
      color: _theme.color.palette.whiteGrey,
      fontSize: 20,
      fontWeight: "400"
    },
    textDay: {
      color: _theme.color.palette.almostBlackGrey,
      fontFamily: _theme.typography.regular,
      fontSize: 20,
      fontWeight: "400"
    },
    textPass: {
      color: _theme.color.palette.lightGrey,
      fontFamily: _theme.typography.regular,
      fontSize: 20,
      fontWeight: "400"
    },
    touchActive: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.lightPurple,
      borderRadius: 20,
      height: 40,
      justifyContent: "center",
      width: 40
    },
    touchDay: {
      alignItems: "center",
      height: 40,
      justifyContent: "center",
      width: 40
    }
  });
  var _default = exports.default = (0, _react.memo)(DayItem);
