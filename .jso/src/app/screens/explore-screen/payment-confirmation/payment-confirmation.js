  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.PaymentConfirmationScreen = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _wave = _$$_REQUIRE(_dependencyMap[7]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _theme = _$$_REQUIRE(_dependencyMap[9]);
  var _illustration = _$$_REQUIRE(_dependencyMap[10]);
  var _text = _$$_REQUIRE(_dependencyMap[11]);
  var _viewCarousel = _$$_REQUIRE(_dependencyMap[12]);
  var _viewCarousel2 = _$$_REQUIRE(_dependencyMap[13]);
  var _reactNativeQrcodeSvg = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _dateTime = _$$_REQUIRE(_dependencyMap[16]);
  var _button = _$$_REQUIRE(_dependencyMap[17]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[18]);
  var _exploreRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[19]));
  var _loadingModal = _$$_REQUIRE(_dependencyMap[20]);
  var _errorCloud = _$$_REQUIRE(_dependencyMap[21]);
  var _translate = _$$_REQUIRE(_dependencyMap[22]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[23]);
  var _utils = _$$_REQUIRE(_dependencyMap[24]);
  var _adobe = _$$_REQUIRE(_dependencyMap[25]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[26]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[27]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[28]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _Dimensions$get = _reactNative2.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var SCREEN_NAME = "PaymentConfirmationScreen";
  var PaymentConfirmationScreen = exports.PaymentConfirmationScreen = function PaymentConfirmationScreen(_ref) {
    var _eventConfirmationPay, _eventConfirmationPay2, _eventConfirmationPay3, _eventConfirmationPay4, _eventConfirmationPay5;
    var route = _ref.route,
      navigation = _ref.navigation;
    var _route$params = Object.assign({}, route == null ? undefined : route.params),
      _route$params$cartId = _route$params.cartId,
      cartId = _route$params$cartId === undefined ? "" : _route$params$cartId;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isPriceBreakdownVisible = _useState2[0],
      setPriceBreakdownVisible = _useState2[1];
    var dispatch = (0, _reactRedux.useDispatch)();
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var loadEventConfirmation = _react.default.useCallback(function () {
      dispatch(_exploreRedux.default.eventConfirmationRequest({
        cartId: cartId,
        email: profilePayload == null ? undefined : profilePayload.email
      }));
    }, []);
    _react.default.useEffect(function () {
      loadEventConfirmation();
    }, [loadEventConfirmation]);
    var eventConfirmationPayload = (0, _reactRedux.useSelector)(function (data) {
      return _exploreRedux.ExploreSelectors.eventConfirmationData(data);
    });
    var isEventConfirmationError = eventConfirmationPayload == null ? undefined : eventConfirmationPayload.hasError;
    var isEventConfirmationLoading = eventConfirmationPayload == null ? undefined : eventConfirmationPayload.isLoading;
    var totalPaid = eventConfirmationPayload == null || (_eventConfirmationPay = eventConfirmationPayload.data) == null ? undefined : _eventConfirmationPay.totalPaid;
    var eventPassDetails = eventConfirmationPayload == null || (_eventConfirmationPay2 = eventConfirmationPayload.data) == null ? undefined : _eventConfirmationPay2.eventPassDetails;
    var paymentDetails = eventConfirmationPayload == null || (_eventConfirmationPay3 = eventConfirmationPayload.data) == null ? undefined : _eventConfirmationPay3.paymentDetails;
    var viewBreakDown = eventConfirmationPayload == null || (_eventConfirmationPay4 = eventConfirmationPayload.data) == null ? undefined : _eventConfirmationPay4.viewBreakDown;
    var bookingStatus = eventConfirmationPayload == null || (_eventConfirmationPay5 = eventConfirmationPayload.data) == null ? undefined : _eventConfirmationPay5.statusGetAllBookings;
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)("Explore_PaymentConfirmation");
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)("Explore_PaymentConfirmation", (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribeFocus;
    }, [navigation]);
    var renderEventPassItem = function renderEventPassItem(_ref2) {
      var item = _ref2.item;
      var title = item.title,
        qrContent = item.qrContent,
        countOfTickets = item.countOfTickets,
        location = item.location,
        startDate = item.startDate,
        startTime = item.startTime;
      var eventStartDate = (0, _moment.default)(startDate).locale(_dateTime.Locales.en).format(_dateTime.DateFormats.DayDateMonthYear);
      var eventStartTime = (0, _dateTime.convertTimeFrom24to12Hrs)(startTime);
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.carouselItemStyle,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          text: title,
          preset: "subTitleBold",
          style: styles.passItemTitleStyle
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.qrCodeContainerStyle,
          children: (0, _jsxRuntime.jsx)(_reactNativeQrcodeSvg.default, {
            value: qrContent,
            size: 140
          })
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.passCountContainerStyle,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            text: `${countOfTickets}`,
            preset: "bodyTextBold",
            style: styles.passesCountTextStyle
          })
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.locationContainerStyle,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            text: location,
            preset: "bodyTextRegular",
            style: styles.locationTextStyle
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            testID: `${SCREEN_NAME}__TouchableLocation`,
            accessibilityLabel: `${SCREEN_NAME}__TouchableLocation`,
            children: (0, _jsxRuntime.jsx)(_icons.Location, {
              height: 20,
              width: 20
            })
          })]
        }), startDate ? (0, _jsxRuntime.jsx)(_text.Text, {
          text: eventStartDate,
          preset: "h2",
          style: styles.dateTextStyle
        }) : null, startTime ? (0, _jsxRuntime.jsx)(_text.Text, {
          text: eventStartTime,
          preset: "h2",
          style: styles.timeTextStyle
        }) : null, startDate && startTime && (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.addToCalendarContainerStyle,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            text: (0, _translate.translate)("eventConfirmation.addToCalendar"),
            preset: "textLink",
            style: styles.addToCalendarTextStyle
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            testID: `${SCREEN_NAME}__TouchableCalendar`,
            accessibilityLabel: `${SCREEN_NAME}__TouchableCalendar`,
            children: (0, _jsxRuntime.jsx)(_icons.Calendar, {
              height: 20,
              width: 20
            })
          })]
        }), (0, _jsxRuntime.jsx)(_button.Button, {
          text: (0, _translate.translate)("eventConfirmation.viewInWallet"),
          typePreset: "secondary",
          onPress: function onPress() {
            return null;
          },
          sizePreset: "small",
          statePreset: "default",
          backgroundPreset: "light",
          textPreset: "buttonLarge",
          textStyle: styles.viewInWalletCTATextStyle,
          style: styles.viewInWalletCTAStyle,
          testID: `${SCREEN_NAME}__TouchableViewInWalletCTA`,
          accessibilityLabel: `${SCREEN_NAME}__TouchableViewInWalletCTA`
        })]
      });
    };
    var renderPaymentBreakDownList = function renderPaymentBreakDownList(_ref3) {
      var item = _ref3.item;
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.paymentBreakdownContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          text: item.slotName,
          preset: "subTitleBold"
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.paymentCartItemsContainerStyle,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            text: (item.countText || "") + " " + (item.passName || ""),
            preset: "caption1Regular"
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.paymentCartPriceContainerStyle,
            children: [item.beforePrice && (0, _jsxRuntime.jsx)(_text.Text, {
              text: item.beforePrice,
              preset: "caption1Regular",
              style: styles.beforePriceStyle
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              text: item.sgdPrice || "",
              preset: "caption1Bold",
              style: styles.sgdPriceStyle
            })]
          })]
        }), item.addOns && (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.paymentCartItemsContainerStyle,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            text: item.addOns,
            preset: "caption1Regular"
          })
        })]
      });
    };
    var togglePriceBreakdown = _react.default.useCallback(function () {
      setPriceBreakdownVisible(!isPriceBreakdownVisible);
    }, [isPriceBreakdownVisible]);
    var marginTopStyle = {
      marginTop: (eventPassDetails == null ? undefined : eventPassDetails.length) > 1 ? 58 : 28
    };
    if (bookingStatus != null && bookingStatus.statusCode && (bookingStatus == null ? undefined : bookingStatus.statusCode) !== "200" || isEventConfirmationError) {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.errorContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.errorContainerShadowStyle,
          children: (0, _jsxRuntime.jsxs)(_reactNative2.SafeAreaView, {
            style: styles.headerContainer,
            children: [(0, _jsxRuntime.jsx)(_reactNative2.StatusBar, {
              barStyle: "dark-content"
            }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: styles.errorContentContainerStyle,
              children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                onPress: function onPress() {
                  return navigation.goBack();
                },
                style: styles.headerLeftButtonStyle,
                testID: `${SCREEN_NAME}__TouchableArrowLeft`,
                accessibilityLabel: `${SCREEN_NAME}__TouchableArrowLeft`,
                children: (0, _jsxRuntime.jsx)(_icons.ArrowLeft, {
                  width: 24,
                  height: 24
                })
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                preset: "subTitleBold",
                text: isEventConfirmationError ? (0, _translate.translate)("eventConfirmation.eventConfirmation") : (0, _translate.translate)("eventConfirmation.noEventFound"),
                numberOfLines: 1
              })]
            })]
          })
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.errorMainContentStyle,
          children: isEventConfirmationError ? (0, _jsxRuntime.jsx)(_errorCloud.ErrorCloudComponent, {
            title: (0, _translate.translate)("eventConfirmation.errorTitle"),
            content: (0, _translate.translate)("eventConfirmation.errorContent"),
            onPress: loadEventConfirmation,
            testID: `${SCREEN_NAME}__ErrorCloudComponent`,
            accessibilityLabel: `${SCREEN_NAME}__ErrorCloudComponent`
          }) : (0, _jsxRuntime.jsx)(_errorCloud.ErrorCloudComponent, {
            title: (0, _translate.translate)("eventConfirmation.bookingNotFound"),
            content: (0, _translate.translate)("eventConfirmation.unableToLoadBooking"),
            buttonText: (0, _translate.translate)("eventConfirmation.viewBookings"),
            onPress: function onPress() {
              return navigation.goBack();
            },
            testID: `${SCREEN_NAME}__ErrorCloudComponentNotFound`,
            accessibilityLabel: `${SCREEN_NAME}__ErrorCloudComponentNotFound`
          })
        })]
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.container,
      testID: "PaymentConfirmationScreen",
      children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        onPress: function onPress() {
          return navigation.goBack();
        },
        style: styles.closeButtonStyle,
        testID: `${SCREEN_NAME}__TouchableCrossHighlighted`,
        accessibilityLabel: `${SCREEN_NAME}__TouchableCrossHighlighted`,
        children: (0, _jsxRuntime.jsx)(_icons.CrossHighlighted, {})
      }), isEventConfirmationLoading ? (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
        visible: true
      }) : (0, _jsxRuntime.jsxs)(_reactNative2.ScrollView, {
        showsVerticalScrollIndicator: false,
        testID: `${SCREEN_NAME}__ScrollView`,
        accessibilityLabel: `${SCREEN_NAME}__ScrollView`,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          children: [(0, _jsxRuntime.jsxs)(_reactNativeLinearGradient.default, {
            style: styles.flexStyle,
            start: {
              x: 0,
              y: 0.5
            },
            end: {
              x: 1,
              y: 0.5
            },
            colors: [_theme.color.palette.gradientColor3Start, _theme.color.palette.gradientColor3Mid, _theme.color.palette.gradientColor3End],
            locations: [0, 0, 1],
            children: [(0, _jsxRuntime.jsx)(_illustration.Illustration, {
              source: "confetti",
              loop: false,
              autoPlay: true,
              style: styles.confettiIllustrationStyle
            }), (0, _jsxRuntime.jsx)(_wave.Wave, {
              style: styles.waveShapeStyle,
              fill: "currentColor"
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.contentContainerStyle,
            children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: styles.illustrationContainerStyle,
              children: (0, _jsxRuntime.jsx)(_illustration.Illustration, {
                source: "paymentSuccess",
                loop: true,
                autoPlay: true,
                style: styles.successTickIllustrationStyle
              })
            }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: styles.textContainerStyle,
              children: [totalPaid && (totalPaid == null ? undefined : totalPaid.length) > 0 && (0, _jsxRuntime.jsx)(_text.Text, {
                text: totalPaid[0] + "" + totalPaid[1],
                preset: "h1",
                style: styles.headingStyle
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                text: (0, _translate.translate)("eventConfirmation.editBookingFromWallet"),
                preset: "caption1Regular",
                style: styles.subHeadingStyle
              })]
            }), (eventPassDetails == null ? undefined : eventPassDetails.length) > 0 && (0, _jsxRuntime.jsx)(_viewCarousel.ViewCarousel, {
              data: eventPassDetails,
              renderItem: renderEventPassItem,
              type: _viewCarousel2.ViewCarouselType.default,
              itemWidth: width,
              testID: `${SCREEN_NAME}__ViewCarousel`,
              accessibilityLabel: `${SCREEN_NAME}__ViewCarousel`
            })]
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: [styles.paymentSummaryContainerStyle, marginTopStyle],
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            text: (0, _translate.translate)("eventConfirmation.paymentSummary"),
            preset: "h4",
            style: styles.paymentSummaryTitleTextStyle
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.amountContainerStyle,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              text: paymentDetails == null ? undefined : paymentDetails.amountPaidTitle,
              preset: "h3",
              style: styles.amountTextStyle
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              text: paymentDetails == null ? undefined : paymentDetails.amountPaidValue,
              preset: "h3",
              style: styles.amountTextStyle
            })]
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            text: paymentDetails == null ? undefined : paymentDetails.paymentVia,
            preset: "caption2Regular",
            style: styles.paymentOptionTextStyle
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.dividerStyle
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.viewBreakdownTitleContainerStyle,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              text: (0, _translate.translate)("eventConfirmation.viewBreakDown"),
              preset: "caption1Bold",
              style: styles.viewBreakdownTextStyle
            }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              onPress: togglePriceBreakdown,
              testID: `${SCREEN_NAME}__TouchablePriceBreakdown`,
              accessibilityLabel: `${SCREEN_NAME}__TouchablePriceBreakdown`,
              children: (0, _utils.handleCondition)(isPriceBreakdownVisible, (0, _jsxRuntime.jsx)(_icons.TopArrow, {
                height: 24,
                width: 24,
                fill: _theme.color.palette.lightPurple
              }), (0, _jsxRuntime.jsx)(_icons.ArrowDown, {
                color: _theme.color.palette.lightPurple,
                height: 24,
                width: 24
              }))
            })]
          }), isPriceBreakdownVisible && viewBreakDown && (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
            data: viewBreakDown,
            renderItem: renderPaymentBreakDownList,
            testID: `${SCREEN_NAME}__FlatListPaymentBreakDownList`,
            accessibilityLabel: `${SCREEN_NAME}__FlatListPaymentBreakDownList`
          })]
        })]
      })]
    });
  };
