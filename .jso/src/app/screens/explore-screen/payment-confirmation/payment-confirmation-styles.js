  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.waveShapeStyle = exports.viewInWalletCTATextStyle = exports.viewInWalletCTAStyle = exports.viewBreakdownTitleContainerStyle = exports.viewBreakdownTextStyle = exports.timeTextStyle = exports.textContainerStyle = exports.successTickIllustrationStyle = exports.subHeadingStyle = exports.sgdPriceStyle = exports.qrCodeContainerStyle = exports.paymentSummaryTitleTextStyle = exports.paymentSummaryContainerStyle = exports.paymentOptionTextStyle = exports.paymentCartPriceContainerStyle = exports.paymentCartItemsContainerStyle = exports.paymentBreakdownContainerStyle = exports.passesCountTextStyle = exports.passItemTitleStyle = exports.passCountContainerStyle = exports.locationTextStyle = exports.locationContainerStyle = exports.illustrationContainerStyle = exports.headingStyle = exports.headerLeftButtonStyle = exports.headerContainer = exports.flexStyle = exports.errorMainContentStyle = exports.errorContentContainerStyle = exports.errorContainerStyle = exports.errorContainerShadowStyle = exports.errorCloudComponentStyle = exports.dummyViewStyle = exports.dividerStyle = exports.dateTextStyle = exports.contentContainerStyle = exports.containerStyle = exports.container = exports.confettiIllustrationStyle = exports.closeButtonStyle = exports.carouselItemStyle = exports.beforePriceStyle = exports.amountTextStyle = exports.amountContainerStyle = exports.addToCalendarTextStyle = exports.addToCalendarContainerStyle = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var closeButtonStyle = exports.closeButtonStyle = {
    marginEnd: 17,
    zIndex: 20,
    borderRadius: 24,
    backgroundColor: _theme.color.palette.darkestGrey,
    position: "absolute",
    right: 0,
    marginTop: _reactNative.Platform.OS === "android" ? 30 : 60
  };
  var flexStyle = exports.flexStyle = {
    height: 809
  };
  var containerStyle = exports.containerStyle = {
    flex: 1
  };
  var illustrationContainerStyle = exports.illustrationContainerStyle = {
    position: "absolute",
    zIndex: 1,
    width: "100%",
    marginTop: 50
  };
  var textContainerStyle = exports.textContainerStyle = {
    alignSelf: "center",
    marginTop: 180
  };
  var contentContainerStyle = exports.contentContainerStyle = {
    position: "absolute",
    alignSelf: "center"
  };
  var successTickIllustrationStyle = exports.successTickIllustrationStyle = {
    height: 188,
    width: 188,
    alignSelf: "center"
  };
  var confettiIllustrationStyle = exports.confettiIllustrationStyle = {
    height: 500,
    width: 500,
    alignSelf: "center",
    position: "absolute"
  };
  var headingStyle = exports.headingStyle = {
    color: _theme.color.palette.whiteGrey,
    textAlign: "center",
    marginVertical: 12
  };
  var subHeadingStyle = exports.subHeadingStyle = {
    color: _theme.color.palette.whiteGrey,
    textAlign: "center"
  };
  var container = exports.container = {
    flex: 1,
    backgroundColor: _theme.color.palette.lightestGrey
  };
  var waveShapeStyle = exports.waveShapeStyle = {
    position: "absolute",
    left: 0,
    bottom: 0,
    width: "100%",
    tintColor: _theme.color.palette.lightestGrey
  };
  var carouselItemStyle = exports.carouselItemStyle = {
    backgroundColor: _theme.color.palette.whiteGrey,
    padding: 24,
    marginHorizontal: 24,
    marginTop: 24,
    height: 490,
    borderRadius: 16,
    elevation: 5,
    shadowColor: _theme.color.palette.almostBlackGrey,
    shadowOpacity: 0.2,
    shadowRadius: 20,
    shadowOffset: {
      height: 6,
      width: 0
    }
  };
  var passItemTitleStyle = exports.passItemTitleStyle = {
    color: _theme.color.palette.almostBlackGrey,
    textAlign: "center",
    marginBottom: 16
  };
  var qrCodeContainerStyle = exports.qrCodeContainerStyle = {
    alignSelf: "center"
  };
  var passesCountTextStyle = exports.passesCountTextStyle = {
    color: _theme.color.palette.lightPurple
  };
  var passCountContainerStyle = exports.passCountContainerStyle = {
    backgroundColor: _theme.color.palette.lightestPurple,
    marginTop: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    alignItems: "center",
    alignSelf: "center"
  };
  var locationContainerStyle = exports.locationContainerStyle = {
    marginTop: 16,
    flexDirection: "row",
    alignSelf: "center",
    alignContent: "center"
  };
  var locationTextStyle = exports.locationTextStyle = {
    color: _theme.color.palette.almostBlackGrey,
    marginRight: 4
  };
  var dateTextStyle = exports.dateTextStyle = {
    marginTop: 16,
    textAlign: "center"
  };
  var timeTextStyle = exports.timeTextStyle = {
    textAlign: "center"
  };
  var addToCalendarTextStyle = exports.addToCalendarTextStyle = {
    color: _theme.color.palette.almostBlackGrey,
    marginRight: 7,
    textAlignVertical: "center"
  };
  var addToCalendarContainerStyle = exports.addToCalendarContainerStyle = {
    marginTop: 16,
    flexDirection: "row",
    alignSelf: "center",
    alignItems: "center"
  };
  var viewInWalletCTATextStyle = exports.viewInWalletCTATextStyle = {
    color: _theme.color.palette.lightPurple
  };
  var viewInWalletCTAStyle = exports.viewInWalletCTAStyle = {
    height: 44,
    marginTop: 25,
    width: 232,
    alignSelf: "center"
  };
  var paymentSummaryContainerStyle = exports.paymentSummaryContainerStyle = {
    marginTop: 28,
    marginBottom: 28,
    marginHorizontal: 24,
    backgroundColor: _theme.color.palette.whiteGrey,
    borderRadius: 16,
    elevation: 5,
    shadowColor: _theme.color.palette.almostBlackGrey,
    shadowOpacity: 0.2,
    shadowRadius: 20,
    shadowOffset: {
      height: 6,
      width: 0
    }
  };
  var paymentSummaryTitleTextStyle = exports.paymentSummaryTitleTextStyle = {
    marginTop: 24,
    marginLeft: 24,
    textAlign: "left",
    color: _theme.color.palette.almostBlackGrey
  };
  var amountContainerStyle = exports.amountContainerStyle = {
    marginHorizontal: 24,
    marginTop: 16,
    justifyContent: "space-between",
    flexDirection: "row"
  };
  var amountTextStyle = exports.amountTextStyle = {
    color: _theme.color.palette.almostBlackGrey
  };
  var paymentOptionTextStyle = exports.paymentOptionTextStyle = {
    color: _theme.color.palette.darkestGrey,
    marginHorizontal: 24,
    marginTop: 8
  };
  var dividerStyle = exports.dividerStyle = {
    height: 1,
    backgroundColor: _theme.color.palette.lighterGrey,
    marginVertical: 16,
    marginHorizontal: 24
  };
  var viewBreakdownTitleContainerStyle = exports.viewBreakdownTitleContainerStyle = {
    flexDirection: "row",
    justifyContent: "space-between",
    marginHorizontal: 24,
    marginBottom: 16
  };
  var viewBreakdownTextStyle = exports.viewBreakdownTextStyle = {
    color: _theme.color.palette.almostBlackGrey
  };
  var paymentBreakdownContainerStyle = exports.paymentBreakdownContainerStyle = {
    backgroundColor: _theme.color.palette.whiteGrey,
    paddingHorizontal: 24,
    paddingBottom: 24,
    paddingTop: 8,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16
  };
  var paymentCartItemsContainerStyle = exports.paymentCartItemsContainerStyle = {
    flexDirection: "row",
    marginTop: 12
  };
  var paymentCartPriceContainerStyle = exports.paymentCartPriceContainerStyle = {
    flexDirection: "row",
    flex: 1,
    justifyContent: "flex-end"
  };
  var beforePriceStyle = exports.beforePriceStyle = {
    color: _theme.color.palette.darkGrey,
    marginRight: 5
  };
  var sgdPriceStyle = exports.sgdPriceStyle = {
    color: _theme.color.palette.almostBlackGrey
  };
  var headerContainer = exports.headerContainer = {
    flexDirection: "row",
    justifyContent: "space-between"
  };
  var dummyViewStyle = exports.dummyViewStyle = {
    width: 32
  };
  var errorCloudComponentStyle = exports.errorCloudComponentStyle = {
    flex: 1
  };
  var errorContainerStyle = exports.errorContainerStyle = {
    flex: 1,
    backgroundColor: _theme.color.palette.whiteGrey
  };
  var errorContainerShadowStyle = exports.errorContainerShadowStyle = {
    backgroundColor: _theme.color.palette.whiteGrey,
    borderRadius: 0,
    elevation: 6,
    shadowColor: _theme.color.palette.almostBlackGrey,
    shadowOpacity: 0.1,
    shadowRadius: 6,
    shadowOffset: {
      height: 2,
      width: 0
    }
  };
  var errorContentContainerStyle = exports.errorContentContainerStyle = {
    flex: 1,
    height: 50,
    justifyContent: "center",
    flexDirection: "row",
    alignItems: "center"
  };
  var headerLeftButtonStyle = exports.headerLeftButtonStyle = {
    position: "absolute",
    left: 16
  };
  var errorMainContentStyle = exports.errorMainContentStyle = {
    position: "absolute",
    left: 0,
    top: 0,
    right: 0,
    bottom: 0,
    zIndex: -100
  };
