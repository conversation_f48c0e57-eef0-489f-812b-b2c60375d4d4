  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useCartTimer = exports.secondsTimer = exports.openLink = exports.navigateToWebViewWithAuth = exports.navigateToTab = exports.convertExploreChangiData = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _react = _$$_REQUIRE(_dependencyMap[5]);
  var _native = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativeFileViewer = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _file = _$$_REQUIRE(_dependencyMap[8]);
  var _momentTimezone = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _exploreRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _constants = _$$_REQUIRE(_dependencyMap[11]);
  var _envParams = _$$_REQUIRE(_dependencyMap[12]);
  var _feedbackToastProps = _$$_REQUIRE(_dependencyMap[13]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[14]);
  var _exploreChangiItemProps = _$$_REQUIRE(_dependencyMap[15]);
  var DOCUMENT_FILE_EXTENSION = /(\.doc|\.docx|\.odt|\.pdf|\.tex|\.txt|\.rtf|\.wps|\.wks|\.wpd)$/i;
  var timerStartShowBookings = 3600;
  var navigateToTab = exports.navigateToTab = function navigateToTab(selectedTab, scrollOffset, categoryItemText, dispatch) {
    dispatch(_exploreRedux.default.exploreSetSectionOffset({
      scrollOffset: scrollOffset,
      selectedTab: selectedTab
    }));
    dispatch(_exploreRedux.default.exploreCategoriesUpdateSelected({
      text: categoryItemText
    }));
  };
  var navigateToWebViewWithAuth = exports.navigateToWebViewWithAuth = function navigateToWebViewWithAuth(navigation, uri) {
    navigation == null || navigation.navigate(_constants.NavigationConstants.playpassWebview, {
      uri: uri,
      needBackButton: true,
      needCloseButton: true
    });
  };
  var openDocumentFileLink = /*#__PURE__*/function () {
    var _ref = (0, _asyncToGenerator2.default)(function* (uri) {
      var _env;
      var result = yield (0, _file.downloadFile)(uri, (_env = (0, _envParams.env)()) == null ? undefined : _env.PLAYPASS_AUTHORIZATION);
      if (result && result != null && result.localFilePath) {
        yield _reactNativeFileViewer.default.open(result == null ? undefined : result.localFilePath);
      }
    });
    return function openDocumentFileLink(_x) {
      return _ref.apply(this, arguments);
    };
  }();
  var openLink = exports.openLink = /*#__PURE__*/function () {
    var _ref2 = (0, _asyncToGenerator2.default)(function* (navigation, uri) {
      if (_reactNative.Platform.OS === "ios" || !DOCUMENT_FILE_EXTENSION.test(uri)) {
        navigateToWebViewWithAuth(navigation, uri);
        return true;
      }
      return yield openDocumentFileLink(uri);
    });
    return function openLink(_x2, _x3) {
      return _ref2.apply(this, arguments);
    };
  }();
  var secondsTimer = exports.secondsTimer = function secondsTimer(seconds) {
    var convertDigit = function convertDigit(digit) {
      return (digit < 10 ? "0" : "") + digit;
    };
    seconds = Math.floor(seconds);
    var minutes = Math.floor(seconds / 60);
    seconds = seconds % 60;
    minutes = minutes % 60;
    return `${convertDigit(minutes)}:${convertDigit(seconds)}`;
  };
  var useCartTimer = exports.useCartTimer = function useCartTimer(_ref3) {
    var isEmptyData = _ref3.isEmptyData,
      timerTs = _ref3.timerTs,
      toastRefCurrent = _ref3.toastRefCurrent,
      loading = _ref3.loading,
      fetchData = _ref3.fetchData;
    var _useState = (0, _react.useState)(0),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      secondsTimerValue = _useState2[0],
      setSecondsTimerValue = _useState2[1];
    var prevTimerValue = (0, _screenHook.usePrevious)(secondsTimerValue);
    var prevLoading = (0, _screenHook.usePrevious)(loading);
    var intervalRef = (0, _react.useRef)();
    (0, _react.useEffect)(function () {
      if (timerTs && !loading && prevLoading) {
        var timerValueInSeconds = _momentTimezone.default.duration(_momentTimezone.default.tz(timerTs, "Asia/Singapore").utc().diff((0, _momentTimezone.default)().utc())).asSeconds();
        if (timerValueInSeconds > 0) {
          setSecondsTimerValue(timerValueInSeconds);
          if (timerValueInSeconds < timerStartShowBookings) {
            toastRefCurrent == null || toastRefCurrent.show(_feedbackToastProps.DURATION.FOREVER);
          }
        }
      }
    }, [toastRefCurrent, timerTs, loading, prevLoading]);
    (0, _react.useEffect)(function () {
      if (secondsTimerValue <= 1) {
        toastRefCurrent == null || toastRefCurrent.closeNow();
        clearInterval(intervalRef.current);
      }
      if (secondsTimerValue <= timerStartShowBookings && prevTimerValue > timerStartShowBookings) {
        fetchData();
      }
    }, [secondsTimerValue, prevTimerValue, toastRefCurrent, fetchData]);
    (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
      if (!isEmptyData) {
        intervalRef.current = setInterval(function () {
          setSecondsTimerValue(function (previousValue) {
            return previousValue - 1;
          });
        }, 1000);
      }
      return function () {
        clearInterval(intervalRef.current);
      };
    }, [isEmptyData]));
    return {
      secondsTimerValue: secondsTimerValue,
      setSecondsTimerValue: setSecondsTimerValue
    };
  };
  var convertExploreChangiData = exports.convertExploreChangiData = function convertExploreChangiData(inputExploreData) {
    if (inputExploreData && inputExploreData.length > 0) {
      var dataFinal = [];
      var dataAEMPush = [];
      inputExploreData.forEach(function (item, index) {
        if ((item == null ? undefined : item.source) === _exploreChangiItemProps.ExploreChangiItemType.AEM) {
          dataAEMPush.push(item);
        }
        if ((item == null ? undefined : item.source) === _exploreChangiItemProps.ExploreChangiItemType.PlayPass) {
          if (dataAEMPush.length > 0) {
            var check = {
              aemItem: (0, _toConsumableArray2.default)(dataAEMPush),
              source: _exploreChangiItemProps.ExploreChangiItemType.AEM
            };
            dataFinal.push(check);
            dataAEMPush = [];
          }
          dataFinal.push(item);
        }
        if (index === inputExploreData.length - 1 && (item == null ? undefined : item.source) === _exploreChangiItemProps.ExploreChangiItemType.AEM) {
          var _check = {
            aemItem: (0, _toConsumableArray2.default)(dataAEMPush),
            source: _exploreChangiItemProps.ExploreChangiItemType.AEM
          };
          dataFinal.push(_check);
        }
      });
      return dataFinal;
    }
  };
