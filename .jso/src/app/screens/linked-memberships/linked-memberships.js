  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.LinkedMembershipsScreen = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[4]);
  var _core = _$$_REQUIRE(_dependencyMap[5]);
  var _linkCapitaStar = _$$_REQUIRE(_dependencyMap[6]);
  var _linkKrisFlyer = _$$_REQUIRE(_dependencyMap[7]);
  var _reactNativeKeyboardAwareScrollView = _$$_REQUIRE(_dependencyMap[8]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[9]);
  var _errorOverlay = _$$_REQUIRE(_dependencyMap[10]);
  var _profileRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[11]));
  var _aemRedux = _$$_REQUIRE(_dependencyMap[12]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[13]));
  var _adobe = _$$_REQUIRE(_dependencyMap[14]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[15]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[16]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[17]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SCREEN_NAME = "LinkedMembershipsScreen";
  var LinkedMembershipsScreen = exports.LinkedMembershipsScreen = function LinkedMembershipsScreen() {
    var dispatch = (0, _reactRedux.useDispatch)();
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var profileError = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profileError);
    var profileFetching = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profileFetching);
    var errorData = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var navigation = (0, _core.useNavigation)();
    var _useState = (0, _react.useState)(function () {
        return profileFetching;
      }),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isBooted = _useState2[0],
      setBooted = _useState2[1];
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)("Account_LinkedMembertity");
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)("Account_LinkedMembertity", (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribeFocus;
    }, [navigation]);
    (0, _react.useEffect)(function () {
      // Prevent call profile if it currently in progress
      if (!isBooted) {
        setBooted(true);
        dispatch(_profileRedux.default.profileRequest());
      }
    }, [isBooted]);
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.containerStyle,
        children: (0, _jsxRuntime.jsxs)(_reactNativeKeyboardAwareScrollView.KeyboardAwareScrollView, {
          enableResetScrollToCoords: false,
          style: styles.listStyle,
          showsVerticalScrollIndicator: false,
          extraScrollHeight: 64,
          keyboardShouldPersistTaps: "handled",
          children: [(0, _jsxRuntime.jsx)(_linkKrisFlyer.LinkKrisFlyer, {
            testID: `${SCREEN_NAME}__LinkKrisFlyer`,
            accessibilityLabel: `${SCREEN_NAME}__LinkKrisFlyer`
          }), (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: styles.dividerStyle
          }), (0, _jsxRuntime.jsx)(_linkCapitaStar.LinkCapitaStar, {
            testID: `${SCREEN_NAME}__LinkCapitaStar`,
            accessibilityLabel: `${SCREEN_NAME}__LinkCapitaStar`
          })]
        })
      }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingModal, {
        visible: profileFetching
      }), (0, _jsxRuntime.jsx)(_errorOverlay.ErrorOverlay, {
        reload: true,
        header: true,
        errorData: errorData,
        headerBackgroundColor: "transparent",
        visible: isBooted && profileError,
        onReload: function onReload() {
          dispatch(_profileRedux.default.profileReset());
          dispatch(_profileRedux.default.profileRequest());
        },
        onBack: function onBack() {
          navigation.goBack();
        },
        testID: `${SCREEN_NAME}__ErrorOverlay`,
        accessibilityLabel: `${SCREEN_NAME}__ErrorOverlay`
      })]
    });
  };
