  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ChangiRewardsDetailScreen = ChangiRewardsDetailScreen;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _sections = _$$_REQUIRE(_dependencyMap[6]);
  var _aboutYourPoints = _$$_REQUIRE(_dependencyMap[7]);
  var _yourBenefits = _$$_REQUIRE(_dependencyMap[8]);
  var _redemptionCatalogue = _$$_REQUIRE(_dependencyMap[9]);
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[10]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _native = _$$_REQUIRE(_dependencyMap[12]);
  var _adobe = _$$_REQUIRE(_dependencyMap[13]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[14]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[15]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[16]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[17]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SCREEN_NAME = "ChangiRewardsDetailScreen";
  function ChangiRewardsDetailScreen() {
    var navigation = (0, _native.useNavigation)();
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isNoConnection = _useState2[0],
      setNoConnection = _useState2[1];
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)("Account_ChangiRewardDetail");
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)("Account_ChangiRewardDetail", (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribeFocus;
    }, [navigation]);
    (0, _react.useEffect)(function () {
      var checkInternet = /*#__PURE__*/function () {
        var _ref = (0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch.isConnected;
          if (!isConnected) {
            setNoConnection(true);
          }
        });
        return function checkInternet() {
          return _ref.apply(this, arguments);
        };
      }();
      checkInternet();
    }, []);
    var onReload = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch2.isConnected;
        setNoConnection(isConnected);
      });
      return function onReload() {
        return _ref2.apply(this, arguments);
      };
    }();
    if (isNoConnection) {
      return (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        header: false,
        headerBackgroundColor: "transparent",
        hideScreenHeader: false,
        visible: true,
        testID: `${SCREEN_NAME}__ErrorOverlayNoConnection`,
        accessibilityLabel: `${SCREEN_NAME}__ErrorOverlayNoConnection`,
        onReload: onReload,
        noInternetOverlayStyle: styles.overlayStyle
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative.ScrollView, {
      style: styles.containerStyle,
      showsVerticalScrollIndicator: false,
      testID: `${SCREEN_NAME}__ScrollView`,
      accessibilityLabel: `${SCREEN_NAME}__ScrollView`,
      children: [(0, _jsxRuntime.jsx)(_reactNative.StatusBar, {
        barStyle: "dark-content"
      }), (0, _jsxRuntime.jsx)(_sections.ChangiRewardsDetail, {}), (0, _jsxRuntime.jsx)(_aboutYourPoints.AboutYourPoints, {}), (0, _jsxRuntime.jsx)(_yourBenefits.YourBenefitsSection, {}), (0, _jsxRuntime.jsx)(_redemptionCatalogue.RedemptionCatalogue, {
        title: "changiRewardsDetail.catalogue"
      })]
    });
  }
  var styles = _reactNative.StyleSheet.create({
    containerStyle: {
      backgroundColor: _theme.color.palette.lightestGrey
    },
    overlayStyle: {
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1,
      marginBottom: 80
    }
  });
