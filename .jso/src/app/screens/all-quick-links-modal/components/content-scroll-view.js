  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.ContentScrollView = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _text2 = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _quickLinkItem = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var ContentScrollView = exports.ContentScrollView = (0, _react.forwardRef)(function (_ref, ref) {
    var sections = _ref.sections,
      headerHeight = _ref.headerHeight,
      footerHeight = _ref.footerHeight,
      itemHeights = _ref.itemHeights,
      setHeaderHeight = _ref.setHeaderHeight,
      setFooterHeight = _ref.setFooterHeight,
      setItemHeights = _ref.setItemHeights,
      onSectionVisible = _ref.onSectionVisible,
      onItemPress = _ref.onItemPress,
      containerStyle = _ref.containerStyle,
      scrollViewStyle = _ref.scrollViewStyle,
      onScrollBeginDrag = _ref.onScrollBeginDrag,
      onMomentumScrollEnd = _ref.onMomentumScrollEnd;
    var flatListRef = (0, _react.useRef)(null);
    var flatData = (0, _react.useMemo)(function () {
      var data = [];
      sections.forEach(function (section, sectionIndex) {
        data.push({
          type: "header",
          sectionIndex: sectionIndex,
          title: section.title,
          key: `header-${sectionIndex}`
        });
        data.push({
          type: "item",
          sectionIndex: sectionIndex,
          items: section.items,
          key: `item-${sectionIndex}`
        });
        data.push({
          type: "footer",
          sectionIndex: sectionIndex,
          key: `footer-${sectionIndex}`
        });
      });
      return data;
    }, [sections]);
    var stickyHeaderIndices = (0, _react.useMemo)(function () {
      var indices = [];
      flatData.forEach(function (item, index) {
        if (item.type === "header") {
          indices.push(index);
        }
      });
      return indices;
    }, [flatData]);
    var getSectionOffset = (0, _react.useCallback)(function (sectionIndex, headerHeight, footerHeight, itemHeights) {
      var offset = 0;
      for (var i = 0; i < flatData.length; i++) {
        var item = flatData[i];
        if (item.type === "header" && item.sectionIndex === sectionIndex) {
          return offset;
        }
        if (item.type === "header") {
          offset += styles.header.marginBottom + headerHeight || 0;
        } else if (item.type === "item") {
          offset += itemHeights[item.sectionIndex] || 0;
        } else if (item.type === "footer") {
          offset += footerHeight || 0;
        }
      }
      return 0;
    }, [flatData]);
    var _scrollToSection = (0, _react.useCallback)(function (sectionIndex, headerHeight, footerHeight, itemHeights) {
      var _flatListRef$current;
      var offset = getSectionOffset(sectionIndex, headerHeight, footerHeight, itemHeights);
      (_flatListRef$current = flatListRef.current) == null || _flatListRef$current.scrollToOffset({
        offset: offset,
        animated: false
      });
    }, [getSectionOffset]);
    (0, _react.useImperativeHandle)(ref, function () {
      return {
        scrollToSection: function scrollToSection(sectionIndex) {
          _scrollToSection(sectionIndex, headerHeight, footerHeight, itemHeights);
        }
      };
    }, [_scrollToSection, headerHeight, footerHeight, itemHeights]);
    var currentStickyHeaderRef = (0, _react.useRef)(null);
    var onViewableItemsChanged = (0, _react.useCallback)(function (_ref2) {
      var viewableItems = _ref2.viewableItems;
      if (viewableItems.length > 0) {
        var first = viewableItems[0];
        var item = flatData[first.index];
        if ((item == null ? undefined : item.type) === "header" && typeof item.sectionIndex === "number") {
          onSectionVisible == null || onSectionVisible(item.sectionIndex);
        }
      }
    }, [flatData, onSectionVisible]);
    var onScroll = (0, _react.useCallback)(function (event) {
      var offsetY = event.nativeEvent.contentOffset.y;
      var newStickyHeader = null;
      var accumulatedHeight = 0;
      for (var i = 0; i < flatData.length; i++) {
        var item = flatData[i];
        if (item.type === "header") {
          if (offsetY >= accumulatedHeight) {
            newStickyHeader = item.sectionIndex;
          }
          accumulatedHeight += headerHeight || 0;
        } else if (item.type === "item") {
          accumulatedHeight += itemHeights[item.sectionIndex] || 0;
        } else if (item.type === "footer") {
          accumulatedHeight += footerHeight || 0;
        }
      }
      if (newStickyHeader !== null && newStickyHeader !== currentStickyHeaderRef.current) {
        currentStickyHeaderRef.current = newStickyHeader;
        onSectionVisible == null || onSectionVisible(newStickyHeader);
      }
    }, [flatData, headerHeight, footerHeight, itemHeights, onSectionVisible]);
    var viewabilityConfig = {
      itemVisiblePercentThreshold: 50
    };
    var renderItem = function renderItem(_ref3) {
      var item = _ref3.item;
      if (item.type === "header") {
        return (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.header,
          onLayout: function onLayout(event) {
            var height = event.nativeEvent.layout.height;
            if (height > 0) setHeaderHeight(height);
          },
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            text: item.title,
            style: [_text2.newPresets.smallTextBlackBold, styles.headerText]
          })
        });
      }
      if (item.type === "item") {
        return (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.itemsContainer,
          onLayout: function onLayout(event) {
            var height = event.nativeEvent.layout.height;
            if (height > 0) {
              setItemHeights(function (prev) {
                return Object.assign({}, prev, (0, _defineProperty2.default)({}, item.sectionIndex, height));
              });
            }
          },
          children: item.items.map(function (it, idx) {
            return (0, _jsxRuntime.jsx)(_quickLinkItem.QuickLinkItem, {
              item: it,
              onPress: onItemPress,
              sectionIndex: item == null ? undefined : item.sectionIndex
            }, `${item.sectionIndex}-${idx}`);
          })
        });
      }
      if (item.type === "footer") {
        return (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.sectionFooterSpacer,
          onLayout: function onLayout(event) {
            var height = event.nativeEvent.layout.height;
            if (height > 0) setFooterHeight(height);
          }
        });
      }
      return null;
    };
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: [styles.container, containerStyle],
      children: (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
        ref: flatListRef,
        data: flatData,
        keyExtractor: function keyExtractor(item) {
          return item.key;
        },
        onViewableItemsChanged: onViewableItemsChanged,
        onScroll: onScroll,
        onScrollBeginDrag: onScrollBeginDrag,
        onMomentumScrollEnd: onMomentumScrollEnd,
        viewabilityConfig: viewabilityConfig,
        contentContainerStyle: [styles.scrollView, scrollViewStyle],
        stickyHeaderIndices: stickyHeaderIndices,
        renderItem: renderItem
      })
    });
  });
  var styles = _reactNative.StyleSheet.create({
    container: {
      flex: 1
    },
    scrollView: {
      paddingBottom: 16
    },
    header: {
      paddingBottom: 8,
      marginHorizontal: 16,
      marginBottom: 12,
      backgroundColor: _theme.color.palette.whiteGrey,
      borderBottomWidth: 1,
      borderBottomColor: _theme.color.palette.lighterGrey
    },
    headerText: {
      color: _theme.color.palette.darkestGrey,
      textAlign: "right"
    },
    itemsContainer: {
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "flex-start",
      paddingHorizontal: 16,
      alignItems: "flex-start"
    },
    sectionFooterSpacer: {
      height: 24
    }
  });
  var _default = exports.default = ContentScrollView;
