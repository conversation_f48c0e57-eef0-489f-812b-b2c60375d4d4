  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.QuickLinkItem = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _textGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  var QuickLinkItem = exports.QuickLinkItem = function QuickLinkItem(_ref) {
    var item = _ref.item,
      _onPress = _ref.onPress,
      containerStyle = _ref.containerStyle,
      textStyle = _ref.textStyle,
      sectionIndex = _ref.sectionIndex;
    var textCombinedStyle = [styles.text, textStyle];
    return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      activeOpacity: 0.8,
      onPress: function onPress() {
        return _onPress(Object.assign({}, item, {
          sectionIndex: sectionIndex
        }));
      },
      style: [styles.container, containerStyle],
      children: [item.icon && (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.iconContainer,
        children: (0, _jsxRuntime.jsx)(_baseImage.default, {
          source: {
            uri: item.icon
          },
          style: styles.iconImage,
          resizeMode: "contain"
        })
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        text: item.name,
        preset: "XXSmallTextRegular",
        style: textCombinedStyle,
        numberOfLines: 3
      }), !!item.tag && (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.tagContainer,
        children: (0, _jsxRuntime.jsx)(_textGradient.default, {
          text: item.tag,
          colors: ["#7E1BCC", "#E74391"],
          start: {
            x: 0,
            y: 0
          },
          end: {
            x: 1,
            y: 0
          },
          style: styles.tagText,
          numberOfLines: 1
        })
      })]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    container: {
      alignItems: "center",
      width: "30%",
      marginBottom: 16,
      marginRight: "3.33%"
    },
    iconContainer: {
      justifyContent: "center",
      alignItems: "center"
    },
    iconImage: {
      width: 24,
      height: 24
    },
    text: {
      color: _theme.color.palette.almostBlackGrey,
      textAlign: "center",
      marginTop: 8
    },
    tagContainer: {
      marginTop: 8,
      paddingHorizontal: 6,
      paddingVertical: 2,
      borderRadius: 4,
      borderWidth: 1,
      borderColor: _theme.color.palette.lightestPurple,
      alignSelf: "center"
    },
    tagText: {
      fontFamily: "Lato",
      fontSize: 10,
      fontWeight: "700",
      lineHeight: 12,
      letterSpacing: 0
    }
  });
  var _default = exports.default = QuickLinkItem;
