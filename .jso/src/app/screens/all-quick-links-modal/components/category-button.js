  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.CategoryButton = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _utils = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  var CategoryButton = exports.CategoryButton = function CategoryButton(_ref) {
    var label = _ref.label,
      icon = _ref.icon,
      _ref$focused = _ref.focused,
      focused = _ref$focused === undefined ? false : _ref$focused,
      onPress = _ref.onPress,
      containerStyle = _ref.containerStyle,
      textStyle = _ref.textStyle;
    var containerCombinedStyle = [styles.baseContainer, focused ? styles.focusedContainer : styles.unfocusedContainer, containerStyle];
    var textCombinedStyle = [focused ? styles.focusedText : styles.unfocusedText, textStyle];
    return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      activeOpacity: 0.8,
      onPress: onPress,
      style: containerCombinedStyle,
      children: [icon && (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.iconContainer,
        children: (0, _jsxRuntime.jsx)(_reactNative2.Image, {
          source: {
            uri: (0, _utils.mappingUrlAem)(icon)
          },
          style: styles.iconImage,
          resizeMode: "contain"
        })
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        text: label,
        preset: focused ? "caption1Bold" : "XXSmallText",
        style: textCombinedStyle,
        numberOfLines: focused ? 1 : undefined
      })]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    baseContainer: {
      flexDirection: "row",
      alignItems: "center",
      alignSelf: "flex-start"
    },
    focusedContainer: {
      borderWidth: 1,
      borderRadius: 999,
      paddingVertical: 12,
      paddingHorizontal: 10,
      borderColor: _theme.color.palette.lighterGrey,
      backgroundColor: _theme.color.palette.whiteGrey,
      maxWidth: 100
    },
    unfocusedContainer: {
      paddingVertical: 6,
      paddingHorizontal: 8,
      maxWidth: 117
    },
    iconContainer: {
      marginRight: 8,
      width: 16,
      height: 16,
      justifyContent: "center",
      alignItems: "center"
    },
    iconImage: {
      width: 12,
      height: 12
    },
    focusedText: {
      color: _theme.color.palette.darkestGrey,
      textAlign: "center"
    },
    unfocusedText: {
      color: _theme.color.palette.darkGrey,
      textAlign: "left"
    }
  });
  var _default = exports.default = CategoryButton;
