  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.AppscapadeEgilibleStatus = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _native = _$$_REQUIRE(_dependencyMap[8]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _dateTime = _$$_REQUIRE(_dependencyMap[10]);
  var _i18n = _$$_REQUIRE(_dependencyMap[11]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[12]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[13]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[14]);
  var _adobe = _$$_REQUIRE(_dependencyMap[15]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[16]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[18]);
  var _lodash = _$$_REQUIRE(_dependencyMap[19]);
  var _rnQrGenerator = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[20]));
  var _backbutton = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[21]));
  var _reactNativeHoleView = _$$_REQUIRE(_dependencyMap[22]);
  var _icons = _$$_REQUIRE(_dependencyMap[23]);
  var _flySaga = _$$_REQUIRE(_dependencyMap[24]);
  var _navigationType = _$$_REQUIRE(_dependencyMap[25]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[26]);
  var _utils = _$$_REQUIRE(_dependencyMap[27]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[28]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[29]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var TIMEOUT_OFFSET = 30000;
  var _Dimensions$get = _reactNative2.Dimensions.get("window"),
    width = _Dimensions$get.width,
    height = _Dimensions$get.height;
  var MASK_WIDTH = width - 48;
  var MASK_HEIGHT = 142;
  var MASK_POSITION_X = Math.round(height / 2) - Math.round(71);
  var HOLE_VIEW_BACKGROUND = "rgba(0, 0, 0, 0.4)";
  var MASK_PADDING_LEFT = 24;
  var DURATION = 500;
  var timer;
  var mappingFlightData = function mappingFlightData(checkFlight) {
    var _checkFlight$airline_, _checkFlight$status_m, _checkFlight$status_m2;
    var newScheduledDate = (checkFlight == null ? undefined : checkFlight.direction) === "DEP" ? checkFlight == null ? undefined : checkFlight.scheduled_date : checkFlight == null ? undefined : checkFlight.origin_dep_date;
    return {
      logo: checkFlight == null || (_checkFlight$airline_ = checkFlight.airline_details) == null ? undefined : _checkFlight$airline_.logo_url,
      flightNumber: checkFlight == null ? undefined : checkFlight.flight_number,
      departingCode: "SIN",
      destinationCode: "PER",
      flightDate: newScheduledDate,
      scheduledDate: newScheduledDate,
      state: "default",
      codeShare: checkFlight == null ? undefined : checkFlight.slave_flights,
      destinationPlace: "Perth",
      departingPlace: "Singapore",
      timeOfFlight: checkFlight == null ? undefined : checkFlight.scheduled_time,
      flightStatus: checkFlight == null ? undefined : checkFlight.flight_status,
      flightStatusMapping: checkFlight == null || (_checkFlight$status_m = checkFlight.status_mapping) == null ? undefined : _checkFlight$status_m.listing_status_en,
      beltStatusMapping: null,
      statusColor: "Red",
      showGate: !!(checkFlight != null && (_checkFlight$status_m2 = checkFlight.status_mapping) != null && _checkFlight$status_m2.show_gate),
      isSaved: false,
      flightUniqueId: `${checkFlight == null ? undefined : checkFlight.flight_number}_${newScheduledDate}`,
      estimatedTimestamp: null,
      actualTimestamp: null,
      direction: checkFlight == null ? undefined : checkFlight.direction,
      terminal: checkFlight == null ? undefined : checkFlight.terminal,
      checkInRow: checkFlight == null ? undefined : checkFlight.check_in_row,
      displayBelt: null,
      displayTimestamp: "2023-09-22 00:05",
      viaAirportDetails: null
    };
  };
  var mappingWithCamelCaseFlightData = function mappingWithCamelCaseFlightData(checkFlight) {
    var _checkFlight$airlineD, _checkFlight$statusMa, _checkFlight$status_m3;
    return {
      logo: checkFlight == null || (_checkFlight$airlineD = checkFlight.airlineDetails) == null ? undefined : _checkFlight$airlineD.logo_url,
      flightNumber: checkFlight == null ? undefined : checkFlight.flightNumber,
      departingCode: "SIN",
      destinationCode: checkFlight == null ? undefined : checkFlight.destinationCode,
      flightDate: checkFlight == null ? undefined : checkFlight.scheduledDate,
      scheduledDate: checkFlight == null ? undefined : checkFlight.scheduledDate,
      state: "default",
      codeShares: checkFlight == null ? undefined : checkFlight.codeShares,
      destinationPlace: checkFlight == null ? undefined : checkFlight.destinationPlace,
      departingPlace: checkFlight == null ? undefined : checkFlight.departingPlace,
      timeOfFlight: checkFlight == null ? undefined : checkFlight.scheduledTime,
      flightStatus: checkFlight == null ? undefined : checkFlight.flightStatus,
      flightStatusMapping: checkFlight == null || (_checkFlight$statusMa = checkFlight.statusMapping) == null ? undefined : _checkFlight$statusMa.listing_status_en,
      beltStatusMapping: null,
      boardingGate: "B1",
      airportDetails: {
        code: "SUB",
        name: "Surabaya",
        name_zh: "泗水",
        name_zh_hant: "泗水",
        lat: "-7.379829884",
        lng: "112.7870026",
        country_code: "ID"
      },
      statusColor: "Red",
      showGate: !!(checkFlight != null && (_checkFlight$status_m3 = checkFlight.status_mapping) != null && _checkFlight$status_m3.show_gate),
      isSaved: false,
      flightUniqueId: `${checkFlight == null ? undefined : checkFlight.flightNumber}_${checkFlight == null ? undefined : checkFlight.scheduledDate}`,
      estimatedTimestamp: null,
      actualTimestamp: null,
      direction: checkFlight == null ? undefined : checkFlight.direction,
      terminal: checkFlight == null ? undefined : checkFlight.terminal,
      checkInRow: checkFlight == null ? undefined : checkFlight.checkInRow,
      displayBelt: null,
      displayTimestamp: checkFlight == null ? undefined : checkFlight.displayTimestamp,
      viaAirportDetails: null
    };
  };
  var AppscapadeEgilibleStatus = exports.AppscapadeEgilibleStatus = /*#__PURE__*/function (AppscapadeEgilibleStatus) {
    AppscapadeEgilibleStatus["success"] = "success";
    AppscapadeEgilibleStatus["passAlreadyScanned"] = "pass_already_scanned";
    AppscapadeEgilibleStatus["notSavedInAdvance"] = "not_saved_in_advance";
    AppscapadeEgilibleStatus["noSavedFlights"] = "no_saved_flights";
    AppscapadeEgilibleStatus["notDepartureFlight"] = "not_departure_flight";
    AppscapadeEgilibleStatus["notTravellingOnFlight"] = "not_travelling_on_flight";
    AppscapadeEgilibleStatus["maxEntriesSubmitted"] = "max_entries_submitted";
    AppscapadeEgilibleStatus["entryAlreadyExists"] = "entry_already_exists";
    return AppscapadeEgilibleStatus;
  }({});
  var AppscapadeScanBoadingPassScreenV2 = function AppscapadeScanBoadingPassScreenV2(_ref) {
    var _dataCommonAEM$data, _dataCommonAEM$data2, _dataCommonAEM$data3;
    var route = _ref.route;
    var navigation = (0, _native.useNavigation)();
    var _ref2 = (route == null ? undefined : route.params) || false,
      isFromFlightDetail = _ref2.isFromFlightDetail;
    var _useState = (0, _react.useState)(true),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      needScan = _useState2[0],
      setNeedScan = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      onFlash = _useState4[0],
      setOnFlash = _useState4[1];
    var _useState5 = (0, _react.useState)(0),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      time = _useState6[0],
      setTime = _useState6[1];
    var _useState7 = (0, _react.useState)(false),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      loadingDectectBoardingPass = _useState8[0],
      setLoadingDectectBoardingPass = _useState8[1];
    var isFocused = (0, _native.useIsFocused)();
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var _useState9 = (0, _react.useState)(false),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      isCheckingFlightAvailable = _useState0[0],
      setIsCheckingFlightAvailable = _useState0[1];
    var dataCommonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig("AEM_COMMON_DATA"));
    var msg52 = dataCommonAEM == null || (_dataCommonAEM$data = dataCommonAEM.data) == null || (_dataCommonAEM$data = _dataCommonAEM$data.messages) == null ? undefined : _dataCommonAEM$data.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG52";
    });
    var msg53 = dataCommonAEM == null || (_dataCommonAEM$data2 = dataCommonAEM.data) == null || (_dataCommonAEM$data2 = _dataCommonAEM$data2.messages) == null ? undefined : _dataCommonAEM$data2.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG53";
    });
    var msg54 = dataCommonAEM == null || (_dataCommonAEM$data3 = dataCommonAEM.data) == null || (_dataCommonAEM$data3 = _dataCommonAEM$data3.messages) == null ? undefined : _dataCommonAEM$data3.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG54";
    });
    var positionAnimatedLine = (0, _react.useRef)(new _reactNative2.Animated.Value(0)).current;
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("SCAN_BOADING_PASS"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var flightDetailDataRef = (0, _react.useRef)(null);
    (0, _react.useEffect)(function () {
      _reactNative2.Animated.loop(_reactNative2.Animated.sequence([_reactNative2.Animated.timing(positionAnimatedLine, {
        toValue: 82,
        duration: DURATION,
        useNativeDriver: true
      }), _reactNative2.Animated.timing(positionAnimatedLine, {
        toValue: 0,
        duration: DURATION,
        useNativeDriver: true
      })])).start();
    }, []);
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      resetState();
    }, []));
    var animatedLineStyle = {
      transform: [{
        translateY: positionAnimatedLine
      }]
    };
    var resetState = function resetState() {
      setNeedScan(true);
      setOnFlash(false);
      setTime(0);
      flightDetailDataRef.current = null;
    };
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)("Explore_ScanCode");
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)("Explore_ScanCode", (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribeFocus;
    }, [navigation]);
    (0, _react.useEffect)(function () {
      if (isFocused) {
        resetState();
        flightDetailDataRef.current = null;
        timer = setInterval(function () {
          setTime(function (prevTime) {
            return prevTime + 1000;
          });
        }, 1000);
      }
      return function () {
        clearInterval(timer);
      };
    }, [isFocused]);
    var alertCanNotDetect = function alertCanNotDetect() {
      _reactNative2.Alert.alert((0, _utils.handleCondition)(msg54 == null ? undefined : msg54.title, msg54 == null ? undefined : msg54.title, (0, _i18n.translate)("scanCode.noBoardingPassDetected.title")), (0, _utils.handleCondition)(msg54 == null ? undefined : msg54.message, msg54 == null ? undefined : msg54.message, (0, _i18n.translate)("scanCode.noBoardingPassDetected.description")), [{
        text: (0, _utils.handleCondition)(msg54 == null ? undefined : msg54.firstButton, msg54 == null ? undefined : msg54.firstButton, (0, _i18n.translate)("scanCode.noBoardingPassDetected.firstButton")),
        style: "cancel",
        onPress: function onPress() {
          setNeedScan(true);
          handleGoBack();
        }
      }, {
        text: (0, _utils.handleCondition)(msg54 == null ? undefined : msg54.secondButton, msg54 == null ? undefined : msg54.secondButton, (0, _i18n.translate)("scanCode.noBoardingPassDetected.secondButton")),
        onPress: function onPress() {
          setNeedScan(true);
        }
      }]);
    };
    (0, _react.useEffect)(function () {
      if ((0, _utils.handleCondition)(needScan && time && time % TIMEOUT_OFFSET === 0 && isFocused, true, false)) {
        setNeedScan(false);
        alertCanNotDetect();
      }
    }, [time]);
    (0, _react.useEffect)(function () {
      if (!isFocused) {
        clearInterval(timer);
      }
    }, [isFocused]);

    // Handle after get full permission and start scan
    var dayToDate = function dayToDate(day) {
      var year = new Date().getFullYear();
      var date = new Date(year, 0, day);
      return (0, _moment.default)(new Date(date)).format(_dateTime.DateFormats.YearMonthDay);
    };
    var handleGoToFlight = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* (flyItem) {
        var _flightDetailDataRef$;
        var flightDataMapped = mappingFlightData(flightDetailDataRef.current);
        clearInterval(timer);
        navigation.navigate("flightDetails", {
          payload: {
            item: Object.assign({}, flightDataMapped, {
              departingCode: flyItem.departingCode,
              destinationCode: flyItem.destinationCode
            }),
            itemIndex: 0,
            sectionIndex: 1,
            flightNavigationType: "departureLanding"
          },
          isFromScanBoardingPass: true,
          direction: (_flightDetailDataRef$ = flightDetailDataRef.current) == null ? undefined : _flightDetailDataRef$.direction
        });
      });
      return function handleGoToFlight(_x) {
        return _ref3.apply(this, arguments);
      };
    }();
    var handleResponseForEligiblePass = function handleResponseForEligiblePass(dataPass, flyItem) {
      var _ref4 = dataPass || "",
        status = _ref4.status,
        chanceId = _ref4.chance_id;
      switch (status) {
        case AppscapadeEgilibleStatus.success:
          if (!chanceId) break;
          handleNavigation(_navigationType.NavigationTypeEnum.deepLink, _navigationHelper.NavigationValueDeepLink.appscapadePlayAndWin, {
            chanceId: chanceId
          });
          break;
        case AppscapadeEgilibleStatus.passAlreadyScanned:
          _reactNative2.Alert.alert((0, _i18n.translate)("scanBoardingPass.passAlreadyScanned.title"), (0, _i18n.translate)("scanBoardingPass.passAlreadyScanned.description"), [{
            text: (0, _i18n.translate)("scanBoardingPass.passAlreadyScanned.buttonCancelLabel"),
            style: "cancel",
            onPress: function onPress() {
              setNeedScan(true);
              handleGoBack();
            }
          }]);
          break;
        case AppscapadeEgilibleStatus.notSavedInAdvance:
        case AppscapadeEgilibleStatus.noSavedFlights:
          _reactNative2.Alert.alert((0, _i18n.translate)("scanBoardingPass.notSavedInAdvance.title"), (0, _i18n.translate)("scanBoardingPass.notSavedInAdvance.description"), [{
            text: (0, _i18n.translate)("scanBoardingPass.notSavedInAdvance.buttonCancelLabel"),
            style: "cancel",
            onPress: function onPress() {
              setNeedScan(true);
              handleGoBack();
            }
          }, {
            text: (0, _i18n.translate)("scanBoardingPass.notSavedInAdvance.buttonAcceptLabel"),
            onPress: function onPress() {
              handleGoToFlight(flyItem);
            }
          }]);
          break;
        case AppscapadeEgilibleStatus.notDepartureFlight:
          _reactNative2.Alert.alert((0, _i18n.translate)("scanBoardingPass.notDepartureFlight.title"), (0, _i18n.translate)("scanBoardingPass.notDepartureFlight.description"), [{
            text: (0, _i18n.translate)("scanBoardingPass.notDepartureFlight.buttonCancelLabel"),
            style: "cancel",
            onPress: function onPress() {
              setNeedScan(true);
              handleGoBack();
            }
          }, {
            text: (0, _i18n.translate)("scanBoardingPass.notDepartureFlight.buttonAcceptLabel"),
            onPress: function onPress() {
              handleGoToFlight(flyItem);
            }
          }]);
          break;
        case AppscapadeEgilibleStatus.notTravellingOnFlight:
          _reactNative2.Alert.alert((0, _i18n.translate)("scanBoardingPass.notTravellingOnFlight.title"), (0, _i18n.translate)("scanBoardingPass.notTravellingOnFlight.description"), [{
            text: (0, _i18n.translate)("scanBoardingPass.notTravellingOnFlight.buttonCancelLabel"),
            style: "cancel",
            onPress: function onPress() {
              setNeedScan(true);
              handleGoBack();
            }
          }, {
            text: (0, _i18n.translate)("scanBoardingPass.notTravellingOnFlight.buttonAcceptLabel"),
            onPress: function onPress() {
              handleGoToFlight(flyItem);
            }
          }]);
          break;
        case AppscapadeEgilibleStatus.maxEntriesSubmitted:
          _reactNative2.Alert.alert((0, _i18n.translate)("scanBoardingPass.maxEntriesSubmitted.title"), (0, _i18n.translate)("scanBoardingPass.maxEntriesSubmitted.description"), [{
            text: (0, _i18n.translate)("scanBoardingPass.maxEntriesSubmitted.buttonCancelLabel"),
            style: "cancel",
            onPress: function onPress() {
              setNeedScan(true);
              handleGoBack();
            }
          }]);
          break;
        case AppscapadeEgilibleStatus.entryAlreadyExists:
          _reactNative2.Alert.alert((0, _i18n.translate)("scanBoardingPass.entryAlreadyExists.title"), (0, _i18n.translate)("scanBoardingPass.entryAlreadyExists.description"), [{
            text: (0, _i18n.translate)("scanBoardingPass.entryAlreadyExists.buttonCancelLabel"),
            style: "cancel",
            onPress: function onPress() {
              setNeedScan(true);
              handleGoBack();
            }
          }, {
            text: (0, _i18n.translate)("scanBoardingPass.entryAlreadyExists.buttonAcceptLabel"),
            onPress: function onPress() {
              handleGoToFlight(flyItem);
            }
          }]);
          break;
        default:
          setNeedScan(true);
          break;
      }
    };
    var readDataFromBoardingPass = /*#__PURE__*/function () {
      var _ref5 = (0, _asyncToGenerator2.default)(function* (data) {
        var _data$replace$trim;
        var dataSplit = (_data$replace$trim = data.replace(/\s+/g, " ").trim()) == null ? undefined : _data$replace$trim.split(" ");
        var indexOfFlightNumber = dataSplit.findIndex(function (newData) {
          return newData.length === 4 && !isNaN(newData);
        });
        var flyItem;
        if (indexOfFlightNumber > 0) {
          var placeInformation = dataSplit[indexOfFlightNumber - 1];
          var timeInformation = dataSplit[indexOfFlightNumber + 1];
          if (placeInformation && placeInformation.length === 8) {
            var departingCode = placeInformation.substring(0, 3);
            var destinationCode = placeInformation.substring(3, 6);
            var checkInSeqNumber = timeInformation.slice(-4);
            var airlineCode = placeInformation.substring(6, 8);
            flyItem = {
              departingCode: departingCode,
              destinationCode: destinationCode,
              airlineCode: airlineCode,
              direction: departingCode === "SIN" ? "DEP" : "ARR",
              flightNumber: `${airlineCode}${Number(dataSplit[indexOfFlightNumber])}`,
              checkInSeqNumber: Number(checkInSeqNumber)
            };
          } else {
            flyItem = false;
          }
          if (timeInformation && !isNaN(timeInformation == null ? undefined : timeInformation.substring(0, 3))) {
            var _flyItem;
            var scheduleDateTime = dayToDate(timeInformation == null ? undefined : timeInformation.substring(0, 3));
            flyItem = Object.assign({}, flyItem, {
              flightDate: scheduleDateTime,
              flightUniqueId: `${(_flyItem = flyItem) == null ? undefined : _flyItem.flightNumber}_${scheduleDateTime}`
            });
          } else {
            flyItem = false;
          }
        }
        if (!flyItem && isFocused) {
          _reactNative2.Alert.alert((0, _utils.handleCondition)(msg53 == null ? undefined : msg53.title, msg53 == null ? undefined : msg53.title, (0, _i18n.translate)("scanCode.invalidBoardingPass.title")), (0, _utils.handleCondition)(msg53 == null ? undefined : msg53.message, msg53 == null ? undefined : msg53.message, (0, _i18n.translate)("scanCode.invalidBoardingPass.description")), [{
            text: (0, _utils.handleCondition)(msg53 == null ? undefined : msg53.firstButton, msg53 == null ? undefined : msg53.firstButton, (0, _i18n.translate)("scanCode.invalidBoardingPass.firstButton")),
            style: "cancel",
            onPress: function onPress() {
              setNeedScan(true);
              handleGoBack();
            }
          }, {
            text: (0, _utils.handleCondition)(msg53 == null ? undefined : msg53.secondButton, msg53 == null ? undefined : msg53.secondButton, (0, _i18n.translate)("scanCode.invalidBoardingPass.secondButton")),
            onPress: function onPress() {
              setNeedScan(true);
            }
          }]);
        } else {
          var _flyItem2, _flyItem3, _flyItem4, _flyItem5, _checkFlight$flight_s, _flyItem6, _flyItem7, _flyItem8, _flyItem9, _flyItem0;
          setIsCheckingFlightAvailable(true);
          var checkFlight = yield (0, _flySaga.checkFlightAvailable)({
            direction: (_flyItem2 = flyItem) == null ? undefined : _flyItem2.direction,
            flightNumber: (_flyItem3 = flyItem) == null ? undefined : _flyItem3.flightNumber,
            scheduledDate: (_flyItem4 = flyItem) == null ? undefined : _flyItem4.flightDate,
            airlineCode: (_flyItem5 = flyItem) == null || (_flyItem5 = _flyItem5.flightNumber) == null ? undefined : _flyItem5.substring(0, 2),
            flightStatus: null
          });
          flightDetailDataRef.current = checkFlight;
          setIsCheckingFlightAvailable(false);
          var invalidStatus = ["departed", "landed"];
          if ((!(checkFlight != null && checkFlight.flight_number) || invalidStatus.includes((_checkFlight$flight_s = checkFlight.flight_status) == null ? undefined : _checkFlight$flight_s.toLowerCase())) && isFocused) {
            _reactNative2.Alert.alert((0, _utils.handleCondition)(msg52 == null ? undefined : msg52.title, msg52 == null ? undefined : msg52.title, (0, _i18n.translate)("scanCode.flightNotFound.title")), (0, _utils.handleCondition)(msg52 == null ? undefined : msg52.message, msg52 == null ? undefined : msg52.message, (0, _i18n.translate)("scanCode.flightNotFound.description")), [{
              text: (0, _utils.handleCondition)(msg52 == null ? undefined : msg52.firstButton, msg52 == null ? undefined : msg52.firstButton, (0, _i18n.translate)("scanCode.flightNotFound.firstButton")),
              style: "cancel",
              onPress: function onPress() {
                setNeedScan(true);
                handleGoBack();
              }
            }, {
              text: (0, _utils.handleCondition)(msg52 == null ? undefined : msg52.secondButton, msg52 == null ? undefined : msg52.secondButton, (0, _i18n.translate)("scanCode.flightNotFound.secondButton")),
              onPress: function onPress() {
                setNeedScan(true);
              }
            }]);
            return;
          }
          setIsCheckingFlightAvailable(true);
          var eligibleLuckyDraw = yield (0, _flySaga.checkLuckyDrawEligible)({
            direction: (_flyItem6 = flyItem) == null ? undefined : _flyItem6.direction,
            flightNumber: (_flyItem7 = flyItem) == null ? undefined : _flyItem7.flightNumber,
            scheduledDate: (_flyItem8 = flyItem) == null ? undefined : _flyItem8.flightDate,
            airportCode: (_flyItem9 = flyItem) == null ? undefined : _flyItem9.destinationCode,
            checkInSeqNumber: (_flyItem0 = flyItem) == null ? undefined : _flyItem0.checkInSeqNumber
          });
          setIsCheckingFlightAvailable(false);
          handleResponseForEligiblePass(eligibleLuckyDraw, flyItem);
        }
      });
      return function readDataFromBoardingPass(_x2) {
        return _ref5.apply(this, arguments);
      };
    }();
    var handleGoBack = function handleGoBack() {
      resetState();
      clearInterval(timer);
      if (isFromFlightDetail) {
        navigation.replace("flightDetails", {
          payload: {
            item: Object.assign({}, mappingWithCamelCaseFlightData(isFromFlightDetail))
          },
          isFromScanBoardingPass: true,
          direction: (0, _utils.handleCondition)((isFromFlightDetail == null ? undefined : isFromFlightDetail.departingCode) === "SIN", "DEP", "ARR")
        });
      } else {
        navigation.goBack();
      }
    };
    var toggleFlashMode = function toggleFlashMode() {
      setOnFlash(!onFlash);
    };
    var uploadBoardingPassFromGallery = function uploadBoardingPassFromGallery() {
      setNeedScan(false);
      setTimeout(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
        try {
          var imageData = yield (0, _mediaHelper.choosePictureFromGallery)({
            multiple: false
          });
          var uriImage = (0, _lodash.get)(imageData, "uri");
          if (uriImage) {
            setLoadingDectectBoardingPass(true);
            _rnQrGenerator.default.detect({
              uri: uriImage
            }).then(function (response) {
              var values = response.values;
              if (!(0, _lodash.isEmpty)(values)) {
                setLoadingDectectBoardingPass(false);
                readDataFromBoardingPass(values == null ? undefined : values[0]);
              } else {
                setLoadingDectectBoardingPass(false);
                alertCanNotDetect();
              }
            }).catch(function (error) {
              setNeedScan(true);
            });
          }
        } catch (error) {
          setNeedScan(true);
        }
      }), 200);
    };
    var headerScan = function headerScan() {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.wrapHeaderScan,
        children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: function onPress() {
            return handleGoBack();
          },
          children: (0, _jsxRuntime.jsx)(_backbutton.default, {})
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.wrapRightAction
        })]
      });
    };
    var uploadBoardingPassBtn = function uploadBoardingPassBtn() {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.wrapUploadBoardingPassBtn,
        children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: uploadBoardingPassFromGallery,
          children: (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
            style: styles.touchableUploadBoardingPassStyle,
            start: {
              x: 0,
              y: 1
            },
            end: {
              x: 1,
              y: 0
            },
            colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              text: "Upload E-Boarding Pass",
              style: styles.textBtnUploadBoardingPassStyle
            })
          })
        })
      });
    };
    var footerScan = function footerScan() {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.wrapFooterScan,
        children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: function onPress() {
            return toggleFlashMode();
          },
          children: (0, _utils.handleCondition)(onFlash, (0, _jsxRuntime.jsx)(_icons.FlashModeOn, {}), (0, _jsxRuntime.jsx)(_icons.FlashModeOff, {}))
        })
      });
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: {
        height: height,
        width: width
      },
      children: [(0, _jsxRuntime.jsxs)(_reactNativeHoleView.RNHoleView, {
        style: styles.holeViewStyle,
        holes: [{
          x: MASK_PADDING_LEFT,
          y: MASK_POSITION_X,
          width: MASK_WIDTH,
          height: MASK_HEIGHT,
          borderRadius: 12
        }],
        children: [headerScan(), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.bottomStyle,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            tx: "scanCode.description",
            style: styles.description,
            preset: "caption1Bold"
          }), uploadBoardingPassBtn(), footerScan()]
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative2.Animated.View, {
        style: [styles.animatedLine, animatedLineStyle]
      }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
        visible: (0, _utils.handleCondition)(isCheckingFlightAvailable || loadingDectectBoardingPass, true, false)
      })]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    animatedLine: {
      backgroundColor: _theme.color.palette.lightPurple,
      height: 4,
      left: 23,
      position: "absolute",
      top: MASK_POSITION_X + 20,
      width: MASK_WIDTH
    },
    bottomStyle: {
      alignItems: "center",
      paddingHorizontal: 52,
      position: "absolute",
      top: MASK_POSITION_X + MASK_HEIGHT + 16,
      width: "100%"
    },
    container: {
      flex: 1
    },
    description: Object.assign({}, _text.presets.caption1Bold, {
      color: _theme.color.palette.whiteGrey,
      letterSpacing: 0.06,
      lineHeight: 18,
      marginBottom: 16,
      textAlign: "center",
      textAlignVertical: "center"
    }),
    holeViewStyle: {
      backgroundColor: _reactNative2.Platform.OS === 'android' ? 'clear' : HOLE_VIEW_BACKGROUND,
      height: "100%",
      position: "absolute",
      width: "100%"
    },
    preview: {
      alignItems: "center",
      flex: 1,
      justifyContent: "flex-end"
    },
    textBtnUploadBoardingPassStyle: Object.assign({}, _text.presets.caption1Bold, {
      color: _theme.color.palette.whiteGrey,
      fontSize: 14,
      lineHeight: 20,
      marginBottom: 2,
      textAlignVertical: "center"
    }),
    touchableUploadBoardingPassStyle: {
      borderRadius: 60,
      paddingHorizontal: 16,
      paddingVertical: 4
    },
    wrapFooterScan: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "center",
      marginTop: 24,
      paddingHorizontal: 20
    },
    wrapHeaderScan: {
      alignItems: "center",
      backgroundColor: _theme.color.transparent,
      flexDirection: "row",
      justifyContent: "space-between",
      paddingHorizontal: 20,
      position: "absolute",
      top: 60,
      width: "100%"
    },
    wrapRightAction: {
      flexDirection: "row"
    },
    wrapUploadBoardingPassBtn: {
      alignItems: "center"
    }
  });
  var _default = exports.default = AppscapadeScanBoadingPassScreenV2;
