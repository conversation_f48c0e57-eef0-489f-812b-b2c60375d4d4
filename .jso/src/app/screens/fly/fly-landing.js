  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.topTabTouchableOpacityStyle = exports.savedFlightTooltipText = exports.savedFlightTooltipContent = exports.savedFlightTooltipContainer = exports.headerBarFlyWithTickerBand = exports.flightTooltipTouchStyles = exports.FlyLanding = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _materialTopTabs = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _color = _$$_REQUIRE(_dependencyMap[5]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _flightsLanding = _$$_REQUIRE(_dependencyMap[8]);
  var _translate = _$$_REQUIRE(_dependencyMap[9]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[10]);
  var _flightProps = _$$_REQUIRE(_dependencyMap[11]);
  var _storage = _$$_REQUIRE(_dependencyMap[12]);
  var _flyRedux = _$$_REQUIRE(_dependencyMap[13]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[14]);
  var _feedbackToast = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _feedbackToastProps = _$$_REQUIRE(_dependencyMap[16]);
  var _text = _$$_REQUIRE(_dependencyMap[17]);
  var _HeaderTabNavBar = _$$_REQUIRE(_dependencyMap[18]);
  var _airportLanding = _$$_REQUIRE(_dependencyMap[19]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[20]);
  var _lodash = _$$_REQUIRE(_dependencyMap[21]);
  var _tickerBand = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[22]));
  var _adobe = _$$_REQUIRE(_dependencyMap[23]);
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[24]);
  var _native = _$$_REQUIRE(_dependencyMap[25]);
  var _envParams = _$$_REQUIRE(_dependencyMap[26]);
  var _confirmPopupSaveFlight = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[27]));
  var _screenHelper = _$$_REQUIRE(_dependencyMap[28]);
  var _analytics = _$$_REQUIRE(_dependencyMap[29]);
  var _icons = _$$_REQUIRE(_dependencyMap[30]);
  var _responsive = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[31]));
  var _theme = _$$_REQUIRE(_dependencyMap[32]);
  var _utils = _$$_REQUIRE(_dependencyMap[33]);
  var _addReturnCalendar = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[34]));
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[35]));
  var _useTickerbandMaintanence = _$$_REQUIRE(_dependencyMap[36]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[37]);
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[38]);
  var _animatedFeedbackToast = _$$_REQUIRE(_dependencyMap[39]);
  var _bottomNavigator = _$$_REQUIRE(_dependencyMap[40]);
  var _mytravelRedux = _$$_REQUIRE(_dependencyMap[41]);
  var _forYouRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[42]));
  var _constants = _$$_REQUIRE(_dependencyMap[43]);
  var _useModal2 = _$$_REQUIRE(_dependencyMap[44]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[45]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SCREEN_NAME = "FlyLanding";
  var Tab = (0, _materialTopTabs.createMaterialTopTabNavigator)();
  var parentContainerStyle = {
    flex: 1,
    backgroundColor: _color.color.palette.whiteGrey
  };
  var feedBackToastStyle = {
    width: "100%",
    paddingHorizontal: 16,
    bottom: 8
  };
  var toastButtonStyle = Object.assign({}, _text.presets.textLink, {
    fontWeight: "normal",
    color: _color.color.palette.lightBlue,
    alignItems: "flex-end"
  });
  var toastTextStyle = Object.assign({}, _text.presets.bodyTextRegular, {
    color: _color.color.palette.whiteGrey,
    width: "80%"
  });
  var topTabTouchableOpacityStyle = exports.topTabTouchableOpacityStyle = {
    alignItems: "center",
    top: 20,
    marginEnd: 24
  };
  var headerBarFlyWithTickerBand = exports.headerBarFlyWithTickerBand = {
    height: 72,
    paddingTop: 0
  };
  var savedFlightTooltipContainer = exports.savedFlightTooltipContainer = {
    position: "absolute",
    right: 24
  };
  var savedFlightTooltipContent = exports.savedFlightTooltipContent = {
    flex: 1,
    justifyContent: "center",
    alignItems: "center"
  };
  var flightTooltipTouchStyles = exports.flightTooltipTouchStyles = {
    flex: 1,
    borderRadius: 8,
    padding: 12,
    backgroundColor: "black"
  };
  var savedFlightTooltipText = exports.savedFlightTooltipText = {
    textAlign: "center",
    color: _color.color.palette.whiteGrey,
    fontStyle: "normal",
    fontFamily: _theme.typography.regular
  };
  var FlyLanding = exports.FlyLanding = function FlyLanding(_ref) {
    var _currentState$routes, _dataCommonAEM$data, _dataCommonAEM$data2, _dataCommonAEM$data3, _saveFlightContent, _saveFlightContent2, _saveFlightContent3, _saveFlightContent4, _insertFlightPayload$6, _saveFlightContent5;
    var navigation = _ref.navigation;
    var dispatch = (0, _reactRedux.useDispatch)();
    var currentState = navigation == null ? undefined : navigation.getState();
    var currentTab = currentState == null || (_currentState$routes = currentState.routes) == null || (_currentState$routes = _currentState$routes[currentState == null ? undefined : currentState.index]) == null || (_currentState$routes = _currentState$routes.state) == null ? undefined : _currentState$routes.index;
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var toastForRemoveFlight = (0, _react.useRef)(null);
    var toastForSavedFlight = (0, _react.useRef)(null);
    var myTravelFlightsPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.myTravelFlightsPayload);
    var _useContext = (0, _react.useContext)(_bottomNavigator.BottomNavContext),
      bottomTabsPosition = _useContext.bottomTabsPosition;
    var insertFlightPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.insertFlightPayload);
    var removeFlightPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.removeFlightPayload);
    var isFocused = (0, _native.useIsFocused)();
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isGoToListingFlight = _useState2[0],
      setIsGoToListingFlight = _useState2[1];
    var _useModal = (0, _useModal2.useModal)("saveConnectingFlightLanding"),
      isModalVisible = _useModal.isModalVisible,
      openModal = _useModal.openModal,
      closeModal = _useModal.closeModal,
      closeLandingOptionModal = _useModal.closeLandingOptionModal;
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      showCalendarModal = _useState4[0],
      setShowCalendarModal = _useState4[1];
    var dataCommonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig("AEM_COMMON_DATA"));
    var msg47 = dataCommonAEM == null || (_dataCommonAEM$data = dataCommonAEM.data) == null || (_dataCommonAEM$data = _dataCommonAEM$data.messages) == null ? undefined : _dataCommonAEM$data.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG47";
    });
    var msg57 = dataCommonAEM == null || (_dataCommonAEM$data2 = dataCommonAEM.data) == null || (_dataCommonAEM$data2 = _dataCommonAEM$data2.messages) == null ? undefined : _dataCommonAEM$data2.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG57";
    });
    var msg59 = dataCommonAEM == null || (_dataCommonAEM$data3 = dataCommonAEM.data) == null || (_dataCommonAEM$data3 = _dataCommonAEM$data3.messages) == null ? undefined : _dataCommonAEM$data3.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG59";
    });
    var connectingFlightPayload = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.connectingFlightPayload);
    var _useTickerbandMaintan = (0, _useTickerbandMaintanence.useTickerbandMaintanance)(_useTickerbandMaintanence.MaintananceTickerBandType.FLY_FLANDING),
      isShowTickerband = _useTickerbandMaintan.isShowTickerband,
      onPressCTA = _useTickerbandMaintan.onPressCTA,
      onCloseTickerBand = _useTickerbandMaintan.onCloseTickerBand,
      tickerBand = _useTickerbandMaintan.tickerBand,
      tickerBandDescription = _useTickerbandMaintan.tickerBandDescription,
      tickerBandButtonText = _useTickerbandMaintan.tickerBandButtonText;
    var _useState5 = (0, _react.useState)(true),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      fadeIn = _useState6[0],
      setFadeIn = _useState6[1];
    var fadeInAnim = (0, _react.useRef)(new _reactNative2.Animated.Value(0)).current;
    var animatedValue = (0, _react.useRef)(new _reactNative2.Animated.Value(0)).current;
    var heightViewTickerband = (0, _react.useRef)(0);
    var tickerBandContainerPadding = isShowTickerband ? 24 : 0;
    var savedFlightTooltipContainerTop = _responsive.default.isDynamicIsLand() ? 101 + heightViewTickerband.current - tickerBandContainerPadding : 96 + heightViewTickerband.current - tickerBandContainerPadding;
    var startAnimation = function startAnimation(toValue) {
      _reactNative2.Animated.timing(animatedValue, {
        toValue: toValue,
        duration: 1000,
        easing: _reactNative2.Easing.linear,
        useNativeDriver: true
      }).start();
    };
    (0, _react.useEffect)(function () {
      startAnimation(fadeIn ? 1 : 0);
    }, [fadeIn]);
    var zIndexToastAnimated = fadeInAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [-1, 9],
      extrapolate: "clamp"
    });
    var translateY = animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [0, 10],
      extrapolate: "clamp"
    });
    var handleFadeShow = (0, _react.useCallback)(function () {
      fadeInAnim.stopAnimation();
      if (fadeIn) {
        _reactNative2.Animated.timing(fadeInAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true
        }).start(function (_ref2) {
          var finished = _ref2.finished;
          if (finished) {
            startAnimation(0);
            setTimeout(function () {
              _reactNative2.Animated.timing(fadeInAnim, {
                toValue: 0,
                duration: 1000,
                useNativeDriver: true
              }).start(function (_ref3) {
                var startFinished = _ref3.finished;
                setFadeIn(true);
                if (startFinished) {
                  fadeInAnim.stopAnimation();
                }
              });
            }, 4000);
          }
        });
        setFadeIn(false);
      } else {
        startAnimation(0);
        _reactNative2.Animated.timing(fadeInAnim, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true
        }).start(function (_ref4) {
          var finished = _ref4.finished;
          if (finished) {
            fadeInAnim.stopAnimation();
          }
        });
        setFadeIn(true);
      }
    }, [fadeIn]);
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      var dtFlyFocusAction = (0, _analytics.dtActionEvent)(_analytics.DT_ANALYTICS_LOG_EVENT_NAME.DT_FLY_SCREEN_FOCUS);
      return function () {
        (0, _analytics.dtLeaveActionEvent)(dtFlyFocusAction);
      };
    }, []));
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)(_constants.TrackingScreenName.FlyLanding);
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      _reactNative2.InteractionManager.runAfterInteractions(function () {
        (0, _mmkvStorage.setMMKVdata)(_mmkvStorage.ENUM_STORAGE_MMKV.IS_FIRST_APP, true);
        (0, _adobe.commonTrackingScreen)(_constants.TrackingScreenName.FlyLanding, (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
    }, []));
    (0, _react.useEffect)(function () {
      (0, _analytics.analyticsLogEvent)(_analytics.ANALYTICS_LOG_EVENT_NAME.FLIGHT_LANDING);
      (0, _analytics.dtACtionLogEvent)(_analytics.ANALYTICS_LOG_EVENT_NAME.FLIGHT_LANDING);
      (0, _analytics.dtBizEvent)(SCREEN_NAME, _analytics.ANALYTICS_LOG_EVENT_NAME.FLIGHT_LANDING, 'App-Event', {});
    }, []);
    (0, _react.useEffect)(function () {
      var _insertFlightPayload$, _env;
      var timeStamp = new Date().getTime();
      var insertFlightSuccessCondition = (0, _utils.ifOneTrue)([insertFlightPayload == null || (_insertFlightPayload$ = insertFlightPayload.insertFlightData) == null ? undefined : _insertFlightPayload$.success, insertFlightPayload == null ? undefined : insertFlightPayload.recordExist]);
      var addReturnPopupCondition = (0, _utils.ifOneTrue)([(0, _mmkvStorage.getLastSavedFlightTime)() + ((_env = (0, _envParams.env)()) == null ? undefined : _env.FLIGHT_SHOW_POPUP_ADD_RETURN) < timeStamp, (0, _lodash.size)(myTravelFlightsPayload == null ? undefined : myTravelFlightsPayload.getMyTravelFlightDetails) === 1]);
      if (insertFlightSuccessCondition) {
        if ((0, _utils.ifAllTrue)([insertFlightPayload == null ? undefined : insertFlightPayload.isInsertSuccessfully, isFocused])) {
          var _insertFlightPayload$2;
          (0, _screenHelper.putFlightInfoToAdobeAnalyticAfterSaveUnSave)({
            data: insertFlightPayload,
            isSuccess: true,
            tag: _adobe.AdobeTagName.CAppSaveFlight,
            flyProfile: "flying",
            pageName: _adobe.AdobeTagName.CAppSearchResult,
            isSaveFlight: true
          });
          if (addReturnPopupCondition && insertFlightPayload != null && (_insertFlightPayload$2 = insertFlightPayload.flightData) != null && _insertFlightPayload$2.isPassenger) {
            openModal();
            (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(true);
            (0, _mmkvStorage.setLastSavedFlightTime)(0);
            (0, _storage.save)(_storage.StorageKey.isSaveFlightTriggered, true);
          } else {
            var _toastForRemoveFlight;
            closeLandingOptionModal();
            handleFadeShow();
            dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
            toastForRemoveFlight == null || (_toastForRemoveFlight = toastForRemoveFlight.current) == null || _toastForRemoveFlight.closeNow();
            (0, _storage.save)(_storage.StorageKey.isSaveFlightTriggered, true);
          }
        }
      }
    }, [insertFlightPayload]);
    (0, _react.useEffect)(function () {
      if (removeFlightPayload != null && removeFlightPayload.isRemovedSuccessFully && isFocused) {
        var _toastForSavedFlight$, _toastForRemoveFlight2;
        (0, _screenHelper.putFlightInfoToAdobeAnalyticAfterSaveUnSave)({
          data: removeFlightPayload,
          isSuccess: true,
          tag: _adobe.AdobeTagName.CAppFlyFlightListRemoveFlight,
          flyProfile: "flying",
          pageName: _adobe.AdobeTagName.CAppFlightLanding,
          isSaveFlight: false
        });
        toastForSavedFlight == null || (_toastForSavedFlight$ = toastForSavedFlight.current) == null || _toastForSavedFlight$.closeNow();
        toastForRemoveFlight == null || (_toastForRemoveFlight2 = toastForRemoveFlight.current) == null || _toastForRemoveFlight2.show(_feedbackToastProps.DURATION.LENGTH_LONG);
        dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
      }
    }, [removeFlightPayload]);
    (0, _react.useEffect)(function () {
      if (insertFlightPayload != null && insertFlightPayload.errorFlag || removeFlightPayload != null && removeFlightPayload.errorFlag) {
        if (!isFocused) {
          return;
        }
        _reactNative2.Alert.alert((insertFlightPayload != null && insertFlightPayload.errorFlag ? msg57 == null ? undefined : msg57.title : msg59 == null ? undefined : msg59.title) || (0, _translate.translate)("flightLanding.currentlyUnavailable"), insertFlightPayload != null && insertFlightPayload.errorFlag ? (msg57 == null ? undefined : msg57.message) || (0, _translate.translate)("flightLanding.saveFlightError") : (msg59 == null ? undefined : msg59.message) || (0, _translate.translate)("flightLanding.removeFlightError"), [{
          text: (insertFlightPayload != null && insertFlightPayload.errorFlag ? msg57 == null ? undefined : msg57.firstButton : msg59 == null ? undefined : msg59.firstButton) || (0, _translate.translate)("flightLanding.okay"),
          style: "cancel",
          onPress: function onPress() {
            return dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
          }
        }]);
      }
    }, [insertFlightPayload == null ? undefined : insertFlightPayload.errorFlag, removeFlightPayload == null ? undefined : removeFlightPayload.errorFlag, isFocused]);
    (0, _react.useEffect)(function () {
      (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(isModalVisible || showCalendarModal);
    }, [isModalVisible, showCalendarModal]);
    var showToastForRemoveFlight = function showToastForRemoveFlight() {
      return (0, _jsxRuntime.jsx)(_animatedFeedbackToast.AnimatedFeedBackToast, {
        ref: toastForRemoveFlight,
        style: feedBackToastStyle,
        textButtonStyle: toastButtonStyle,
        position: "custom",
        positionValue: {
          bottom: 8
        },
        textStyle: toastTextStyle,
        type: _feedbackToastProps.FeedBackToastType.smallFeedBack,
        text: (0, _translate.translate)("flyLanding.removeFlightNew"),
        testID: `${SCREEN_NAME}__FeedBackToastRemoveFlight`,
        accessibilityLabel: `${SCREEN_NAME}__FeedBackToastRemoveFlight`,
        positionBottom: bottomTabsPosition
      });
    };
    var showFlightAddedFeedBackToastMessage = function showFlightAddedFeedBackToastMessage() {
      return (0, _jsxRuntime.jsx)(_feedbackToast.default, {
        ref: toastForSavedFlight,
        style: feedBackToastStyle,
        textButtonStyle: toastButtonStyle,
        position: "bottom",
        textStyle: toastTextStyle,
        type: _feedbackToastProps.FeedBackToastType.smallFeedBack,
        text: (msg47 == null ? undefined : msg47.title) || "Flight Added!"
      });
    };
    var saveFlightContent = function saveFlightContent() {
      var _insertFlightPayload$3;
      var result = {
        title: msg47 == null ? undefined : msg47.title,
        messageText: (msg47 == null ? undefined : msg47.message) || (0, _translate.translate)("flightDetails.popupConfirmSaveFlight.message"),
        textButtonConfirm: msg47 == null ? undefined : msg47.firstButton,
        textButtonCancel: msg47 == null ? undefined : msg47.secondButton,
        textButtonConnection: ""
      };
      result.title = (0, _translate.translate)("flightDetails.newPopupConfirmSaveFlight.title");
      result.textButtonConnection = (0, _translate.translate)("flightDetails.newPopupConfirmSaveFlight.addConnectingFlightButton");
      result.textButtonConfirm = (0, _translate.translate)("flightDetails.newPopupConfirmSaveFlight.addReturnFlightButton");
      result.textButtonCancel = (0, _translate.translate)("flightDetails.newPopupConfirmSaveFlight.cancelButton");
      if ((insertFlightPayload == null || (_insertFlightPayload$3 = insertFlightPayload.flightData) == null || (_insertFlightPayload$3 = _insertFlightPayload$3.item) == null ? undefined : _insertFlightPayload$3.direction) === _flightProps.FlightDirection.arrival) {
        result.messageText = (0, _translate.translate)("flightDetails.newPopupConfirmSaveFlight.arrivalMessage");
      } else {
        result.messageText = (0, _translate.translate)("flightDetails.newPopupConfirmSaveFlight.departureMessage");
      }
      return result;
    };
    var handleModalConfirmSaveFlightHide = function handleModalConfirmSaveFlightHide() {
      var timeStamp = new Date().getTime();
      (0, _mmkvStorage.setLastSavedFlightTime)(timeStamp);
      if (isGoToListingFlight) {
        handleGoToListingFlight();
      } else {
        handleNotGoToListingFlight();
      }
    };
    var handleGoToListingFlight = function handleGoToListingFlight() {
      setShowCalendarModal(true);
      setIsGoToListingFlight(false);
    };
    var handleNotGoToListingFlight = function handleNotGoToListingFlight() {
      handleFadeShow();
      dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
      (0, _storage.save)(_storage.StorageKey.isSaveFlightTriggered, true);
      // setIsShowModalCheckRatingPopup(false)
    };
    var handleConnectingFlightOnPress = function handleConnectingFlightOnPress() {
      var _insertFlightPayload$4, _insertFlightPayload$5;
      var connectingFlight = {
        isConnecting: true,
        flightConnecting: Object.assign({}, insertFlightPayload == null || (_insertFlightPayload$4 = insertFlightPayload.flightData) == null ? undefined : _insertFlightPayload$4.item, {
          isPassenger: insertFlightPayload == null || (_insertFlightPayload$5 = insertFlightPayload.flightData) == null ? undefined : _insertFlightPayload$5.isPassenger
        })
      };
      setIsGoToListingFlight(true);
      closeModal();
      dispatch(_flyRedux.FlyCreators.setConnectingFlightPayload(connectingFlight));
      // setIsShowModalCheckRatingPopup(false)
    };
    (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
      return function () {
        navigation.setParams({
          screen: undefined,
          params: undefined
        });
      };
    }, [navigation]));
    (0, _react.useEffect)(function () {
      _reactNative2.InteractionManager.runAfterInteractions(function () {
        // Load more menu data
        dispatch(_forYouRedux.default.moreOptionsRequest());
      });
      return function () {
        _reactNative2.InteractionManager.runAfterInteractions(function () {
          dispatch(_forYouRedux.default.moreOptionsReset());
        });
      };
    }, []);
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: parentContainerStyle,
      testID: `${SCREEN_NAME}__FlyLandingScreen`,
      accessibilityLabel: `${SCREEN_NAME}__FlyLandingScreen`,
      children: [(0, _jsxRuntime.jsx)(_reactNative2.StatusBar, {
        translucent: true,
        backgroundColor: "transparent"
      }), isShowTickerband && !currentTab && (0, _jsxRuntime.jsx)(_reactNative2.View, {
        onLayout: function onLayout(event) {
          var layout = event.nativeEvent.layout;
          heightViewTickerband.current = layout.height;
        },
        children: (0, _jsxRuntime.jsx)(_tickerBand.default, {
          urgent: false,
          title: tickerBand,
          description: tickerBandDescription,
          buttonText: tickerBandButtonText,
          onCTAPress: onPressCTA,
          onClose: onCloseTickerBand,
          isLanding: true
        })
      }), (0, _jsxRuntime.jsxs)(Tab.Navigator, {
        tabBar: function tabBar(props) {
          return (0, _jsxRuntime.jsx)(_HeaderTabNavBar.HeaderTabNavBar, {
            props: Object.assign({}, props),
            isFlyLanding: true,
            testID: `${SCREEN_NAME}__FlyLandingHeaderTabNavBar`,
            accessibilityLabel: `${SCREEN_NAME}__FlyLandingHeaderTabNavBar`,
            topTabParentStyle: isShowTickerband && !currentTab ? Object.assign({}, headerBarFlyWithTickerBand) : {}
          });
        },
        children: [(0, _jsxRuntime.jsx)(Tab.Screen, {
          name: "flights",
          component: _flightsLanding.FlightsLanding,
          initialParams: navigation,
          options: {
            lazy: false,
            swipeEnabled: false,
            tabBarTestID: `${SCREEN_NAME}__TabFlightsLanding`,
            tabBarAccessibilityLabel: `${SCREEN_NAME}__TabFlightsLanding`,
            title: (0, _translate.translate)("flyLanding.flightTabTitle")
          },
          listeners: {
            tabPress: function tabPress() {
              (0, _adobe.trackState)(_adobe.AdobeTagName.CAppFlyTopMenuToggle, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlyTopMenuToggle, (0, _translate.translate)("flyLanding.flightTabTitle")));
            }
          }
        }), (0, _jsxRuntime.jsx)(Tab.Screen, {
          initialParams: navigation,
          name: "airport",
          component: _airportLanding.AirportLanding,
          options: {
            lazy: false,
            swipeEnabled: false,
            tabBarTestID: `${SCREEN_NAME}__TabAirportLanding`,
            tabBarAccessibilityLabel: `${SCREEN_NAME}__TabAirportLanding`,
            title: (0, _translate.translate)("flyLanding.airportTabTitle")
          },
          listeners: {
            tabPress: function tabPress() {
              (0, _adobe.trackState)(_adobe.AdobeTagName.CAppFlyTopMenuToggle, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlyTopMenuToggle, (0, _translate.translate)("flyLanding.airportTabTitle")));
            }
          }
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative2.Animated.View, {
        style: [savedFlightTooltipContainer, {
          top: savedFlightTooltipContainerTop,
          opacity: fadeInAnim,
          zIndex: zIndexToastAnimated,
          transform: [{
            translateY: translateY
          }]
        }],
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: savedFlightTooltipContent,
          children: [(0, _jsxRuntime.jsx)(_icons.FlightTopTooltip, {}), (0, _jsxRuntime.jsx)(_reactNative.TouchableHighlight, {
            style: flightTooltipTouchStyles,
            onPress: function onPress() {
              return handleFadeShow();
            },
            disabled: fadeIn,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              tx: "flyLanding.yourFlightSaved",
              preset: "caption1Italic",
              style: savedFlightTooltipText
            })
          })]
        })
      }), (0, _jsxRuntime.jsx)(_confirmPopupSaveFlight.default, {
        imageUrl: (0, _mediaHelper.handleImageUrl)(msg47 == null ? undefined : msg47.icon),
        visible: isModalVisible && isFocused,
        title: (_saveFlightContent = saveFlightContent()) == null ? undefined : _saveFlightContent.title,
        messageText: (_saveFlightContent2 = saveFlightContent()) == null ? undefined : _saveFlightContent2.messageText,
        onClose: function onClose() {
          closeModal();
          (0, _storage.save)(_storage.StorageKey.isSaveFlightTriggered, true);
        },
        onButtonPressed: function onButtonPressed() {
          setIsGoToListingFlight(true);
          closeModal();
        },
        textButtonConfirm: (_saveFlightContent3 = saveFlightContent()) == null ? undefined : _saveFlightContent3.textButtonConfirm,
        textButtonCancel: (_saveFlightContent4 = saveFlightContent()) == null ? undefined : _saveFlightContent4.textButtonCancel,
        onModalHide: handleModalConfirmSaveFlightHide,
        isShowButtonConnection: (insertFlightPayload == null || (_insertFlightPayload$6 = insertFlightPayload.flightData) == null || (_insertFlightPayload$6 = _insertFlightPayload$6.item) == null ? undefined : _insertFlightPayload$6.direction) === _flightProps.FlightDirection.arrival,
        onButtonConnectionPressed: handleConnectingFlightOnPress,
        textButtonConnection: (_saveFlightContent5 = saveFlightContent()) == null ? undefined : _saveFlightContent5.textButtonConnection,
        disableCloseButton: true,
        openPendingModal: true
      }), (0, _jsxRuntime.jsx)(_addReturnCalendar.default, {
        isVisible: showCalendarModal,
        filterDate: (0, _moment.default)((0, _lodash.get)(insertFlightPayload, "flightData.item.displayTimestamp")).format("YYYY-MM-DD") || (0, _moment.default)().format("YYYY-MM-DD"),
        initialMinDate: (0, _moment.default)((0, _lodash.get)(insertFlightPayload, "flightData.item.displayTimestamp")).format("YYYY-MM-DD") || (0, _moment.default)().format("YYYY-MM-DD"),
        onClosedCalendarModal: function onClosedCalendarModal() {
          handleFadeShow();
          setShowCalendarModal(false);
          // setIsShowModalCheckRatingPopup(false)
          dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
          var connectingFlightPayloadReset = {
            isConnecting: false,
            flightConnecting: null
          };
          dispatch(_flyRedux.FlyCreators.setConnectingFlightPayload(connectingFlightPayloadReset));
          (0, _storage.save)(_storage.StorageKey.isSaveFlightTriggered, true);
        },
        onDateSelected: function onDateSelected(dateString) {
          var direction = (0, _lodash.get)(insertFlightPayload, "flightData.item.direction", "DEP");
          var country = (0, _lodash.get)(insertFlightPayload, "flightData.item.country", "");
          var date = (0, _moment.default)(dateString).format("YYYY-MM-DD");
          setShowCalendarModal(false);
          // setIsShowModalCheckRatingPopup(false)
          dispatch(_flyRedux.FlyCreators.setFlightSearchDate(date));
          navigation.navigate("flightResultLandingScreen", {
            screen: direction === _flightProps.FlightDirection.departure ? _flightProps.FlightDirection.arrival : _flightProps.FlightDirection.departure,
            sourcePage: _adobe.AdobeTagName.CAppFlyFlightDetail,
            selectedDate: date,
            country: connectingFlightPayload.isConnecting ? "Singapore" : country
          });
          dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
          (0, _storage.save)(_storage.StorageKey.isSaveFlightTriggered, true);
        },
        testID: `${SCREEN_NAME}__AddReturnCalendar`,
        accessibilityLabel: `${SCREEN_NAME}__AddReturnCalendar`
      }), showToastForRemoveFlight(), showFlightAddedFeedBackToastMessage()]
    });
  };
