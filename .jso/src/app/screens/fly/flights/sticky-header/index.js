  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _flyRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _search = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _stickyHeaderArrivalActive = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _stickyHeaderDepartureActive = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _stickyHeaderArrivalInactive = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _stickyHeaderDepartureInactive = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _flightProps = _$$_REQUIRE(_dependencyMap[14]);
  var _filterDateLanding = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _filterFlight = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _adobe = _$$_REQUIRE(_dependencyMap[17]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[18]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[19]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var styles = _reactNative2.StyleSheet.create({
    container: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.lightestGrey,
      flexDirection: "row",
      flexShrink: 0,
      height: 76,
      justifyContent: "space-between",
      paddingLeft: 16,
      paddingRight: 24
    },
    containerFilterStyle: {
      marginTop: 0
    },
    dateFilterButton: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      borderColor: _theme.color.palette.lightGrey,
      borderRadius: 12,
      borderWidth: 1,
      flexDirection: "row",
      height: 44,
      paddingHorizontal: 16,
      paddingVertical: 10
    },
    dateFilterContainer: {
      flex: 1,
      paddingLeft: 12
    },
    locationCalendarContainerViewStyle: {
      justifyContent: "flex-start",
      width: 24
    },
    searchButton: {
      marginRight: 22
    },
    searchFilterContainer: {
      alignItems: "center",
      flexDirection: "row",
      paddingLeft: 12
    },
    switchActiveButton: {
      backgroundColor: _theme.color.palette.lightPurple
    },
    switchButton: {
      alignItems: "center",
      borderRadius: 35,
      height: 40,
      marginTop: -1,
      overflow: "hidden",
      paddingVertical: 8,
      width: 40
    },
    switchButtonLeft: {
      marginLeft: -1
    },
    switchButtonRight: {
      marginLeft: 6
    },
    switchFlightContainer: {},
    switchFlightWrapper: {
      borderColor: _theme.color.palette.greyCCCCCC,
      borderRadius: 35,
      borderWidth: 1,
      flexDirection: "row",
      height: 40,
      width: 86
    }
  });
  var FILTER_ELEMENT = {
    width: 20,
    height: 20
  };
  var StickyHeader = function StickyHeader(props) {
    var onPress = props.onPress,
      navigation = props.navigation,
      onSearchPress = props.onSearchPress,
      dispatch = props.dispatch;
    var flyLandingSelectedTab = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flyLandingSelectedTab);
    var filterDateDeparture = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.filterDateDeparture);
    var _useState = (0, _react.useState)(1),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      opacity1 = _useState2[0],
      setOpacity1 = _useState2[1];
    var _useState3 = (0, _react.useState)(1),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      opacity2 = _useState4[0],
      setOpacity2 = _useState4[1];
    var onPressChangeTab = function onPressChangeTab(tab) {
      if (tab === flyLandingSelectedTab) return true;
      onPress({
        value: tab
      });
    };
    var departureButtonIcon = function departureButtonIcon() {
      if (flyLandingSelectedTab === _flightProps.FlightDirection.arrival) {
        return (0, _jsxRuntime.jsx)(_stickyHeaderDepartureInactive.default, {});
      } else {
        return (0, _jsxRuntime.jsx)(_stickyHeaderDepartureActive.default, {});
      }
    };
    var arrivalButtonIcon = function arrivalButtonIcon() {
      if (flyLandingSelectedTab === _flightProps.FlightDirection.arrival) {
        return (0, _jsxRuntime.jsx)(_stickyHeaderArrivalActive.default, {});
      } else {
        return (0, _jsxRuntime.jsx)(_stickyHeaderArrivalInactive.default, {});
      }
    };
    var onSearch = function onSearch() {
      return onSearchPress();
    };
    var onFilterFlight = function onFilterFlight(_ref) {
      var date = _ref.date,
        filterLocation = _ref.filterLocation,
        direction = _ref.direction,
        airline = _ref.airline,
        cityAirport = _ref.cityAirport;
      if (date) {
        dispatch(_flyRedux.FlyCreators.setFlightSearchDate(date));
        dispatch(_flyRedux.FlyCreators.setFilterDateDeparture(date));
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlyFlightListFilters, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlyFlightListFilters, (0, _moment.default)(date).format("YYYY-MM-DD")));
      }
      if (filterLocation && (filterLocation == null ? undefined : filterLocation.length) > 0) {
        dispatch(_flyRedux.FlyCreators.setFlightSearchTerminal(filterLocation));
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlyFlightListFilters, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlyFlightListFilters, `${filterLocation.join("|")}`));
      }
      var flightFilterOptionSelected = {
        terminal: filterLocation,
        airline: airline,
        cityAirport: cityAirport
      };
      dispatch(_flyRedux.FlyCreators.setFlightFilterOption(flightFilterOptionSelected));
      _reactNative2.InteractionManager.runAfterInteractions(function () {
        navigation.navigate("flightResultLandingScreen", {
          screen: direction,
          query: (0, _defineProperty2.default)({}, direction, {
            selectedDate: date,
            filterLocation: filterLocation,
            airline: airline,
            cityAirport: cityAirport
          }),
          sourcePage: _adobe.AdobeTagName.CAppFlightLanding
        });
      });
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.switchFlightContainer,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.switchFlightWrapper,
          children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
            onPress: function onPress() {
              return onPressChangeTab(_flightProps.FlightDirection.arrival);
            },
            onPressIn: function onPressIn() {
              return setOpacity2(0.2);
            },
            onPressOut: function onPressOut() {
              return setOpacity2(1);
            },
            children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: [styles.switchButton, {
                opacity: opacity2
              }, styles.switchButtonLeft, flyLandingSelectedTab === _flightProps.FlightDirection.arrival ? styles.switchActiveButton : {}],
              children: arrivalButtonIcon()
            })
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
            onPress: function onPress() {
              return onPressChangeTab(_flightProps.FlightDirection.departure);
            },
            onPressIn: function onPressIn() {
              return setOpacity1(0.2);
            },
            onPressOut: function onPressOut() {
              return setOpacity1(1);
            },
            children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: [styles.switchButton, {
                opacity: opacity1
              }, styles.switchButtonRight, flyLandingSelectedTab !== _flightProps.FlightDirection.arrival ? styles.switchActiveButton : {}],
              children: departureButtonIcon()
            })
          })]
        })
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.dateFilterContainer,
        children: (0, _jsxRuntime.jsx)(_filterDateLanding.default, {
          disableChangeDate: true,
          flightScreen: "Landing",
          direction: flyLandingSelectedTab,
          dispatch: dispatch,
          navigation: navigation,
          buttonStyles: styles.dateFilterButton
        })
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.searchFilterContainer,
        children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: styles.searchButton,
          onPress: onSearch,
          children: (0, _jsxRuntime.jsx)(_search.default, {})
        }), (0, _jsxRuntime.jsx)(_filterFlight.default, {
          onFilterFlight: onFilterFlight,
          initialDate: new Date(),
          disableChangeDate: true,
          disableChangeFilterLocation: true,
          displayDate: "",
          containerFilterStyle: styles.containerFilterStyle,
          locationCalendarContainerViewStyle: styles.locationCalendarContainerViewStyle,
          isDateSelected: !!filterDateDeparture,
          direction: flyLandingSelectedTab,
          isShowCalendar: false,
          isNewDesign: true
        })]
      })]
    });
  };
  var _default = exports.default = StickyHeader;
