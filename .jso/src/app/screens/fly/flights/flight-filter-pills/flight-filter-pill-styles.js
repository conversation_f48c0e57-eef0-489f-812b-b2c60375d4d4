  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    container: {
      marginTop: 6
    },
    contentContainerStyle: {
      paddingLeft: 24,
      paddingRight: 16
    },
    itemFilterPillStyles: {
      borderRadius: 24,
      borderWidth: 1,
      marginRight: 8
    },
    tagTitleStyles: {
      color: _theme.color.palette.darkestGrey,
      lineHeight: 20,
      marginHorizontal: 12,
      marginVertical: 4,
      textAlign: 'center'
    }
  });
