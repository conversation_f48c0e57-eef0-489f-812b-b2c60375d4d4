  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativeSizeMatters = _$$_REQUIRE(_dependencyMap[7]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _flyRedux = _$$_REQUIRE(_dependencyMap[9]);
  var _adobe = _$$_REQUIRE(_dependencyMap[10]);
  var _icons = _$$_REQUIRE(_dependencyMap[11]);
  var _text = _$$_REQUIRE(_dependencyMap[12]);
  var _calendarFliter = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _reactNativeModal = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[15]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[16]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var styles = exports.styles = _reactNative2.StyleSheet.create({
    calendarFilterStyles: {
      borderRadius: 13,
      paddingHorizontal: 8,
      paddingVertical: 12
    },
    calendarPickerContainer: {
      height: "100%",
      justifyContent: "center",
      paddingHorizontal: 24,
      width: "100%"
    },
    modalContainer: {
      margin: 0
    }
  });
  var FilterByDate = function FilterByDate(_ref) {
    var _ref$flightScreen = _ref.flightScreen,
      flightScreen = _ref$flightScreen === undefined ? "" : _ref$flightScreen,
      _ref$direction = _ref.direction,
      direction = _ref$direction === undefined ? "DEP" : _ref$direction,
      dispatch = _ref.dispatch,
      navigation = _ref.navigation,
      _ref$buttonStyles = _ref.buttonStyles,
      buttonStyles = _ref$buttonStyles === undefined ? null : _ref$buttonStyles;
    var initialDate = new Date();
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      showCalendar = _useState2[0],
      setShowCalendar = _useState2[1];
    var initialCalendarFilterMinDate = (0, _moment.default)(initialDate).add(-1, "days").format("YYYY-MM-DD");
    var FilterByDateContainer = {
      width: (0, _reactNativeSizeMatters.scale)(155),
      backgroundColor: _theme.color.palette.whiteGrey,
      borderTopWidth: 1,
      borderBottomWidth: 1,
      borderLeftWidth: 1,
      borderTopLeftRadius: 12,
      borderBottomLeftRadius: 12,
      borderColor: _theme.color.palette.lighterGrey,
      paddingVertical: 10,
      paddingHorizontal: 16
    };
    var FilterByDateWrapper = {
      flexDirection: "row",
      height: 24,
      alignItems: "center"
    };
    var FilterByDateIcon = {
      marginRight: 10,
      height: 24,
      width: 24
    };
    var FilterByDateText = {
      color: _theme.color.palette.almostBlackGrey
    };
    var onFilterFlight = function onFilterFlight(_ref2) {
      var date = _ref2.date,
        _ref2$filterLocation = _ref2.filterLocation,
        filterLocation = _ref2$filterLocation === undefined ? null : _ref2$filterLocation;
      if (date) {
        if (direction === "DEP") {
          dispatch(_flyRedux.FlyCreators.setFilterDateDeparture(date));
        } else {
          dispatch(_flyRedux.FlyCreators.setFilterDateArrival(date));
        }
        dispatch(_flyRedux.FlyCreators.setFlightSearchDate(date));
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlyFlightListFilters, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlyFlightListFilters, (0, _moment.default)(date).format("YYYY-MM-DD")));
      }
      if (filterLocation && (filterLocation == null ? undefined : filterLocation.length) > 0) {
        dispatch(_flyRedux.FlyCreators.setFlightSearchTerminal(filterLocation));
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlyFlightListFilters, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlyFlightListFilters, `${filterLocation.join("|")}`));
      }
      if (flightScreen === "Landing") {
        navigation.navigate("flightResultLandingScreen", {
          screen: direction,
          query: (0, _defineProperty2.default)({}, direction, {
            selectedDate: date,
            filterLocation: filterLocation
          }),
          sourcePage: _adobe.AdobeTagName.CAppFlightLanding
        });
      }
    };
    var onClosedCalendarModal = function onClosedCalendarModal() {
      setShowCalendar(false);
      (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
    };
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        onPress: function onPress() {
          _reactNative2.Keyboard.dismiss();
          (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(true);
          setShowCalendar(true);
        },
        children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: buttonStyles || FilterByDateContainer,
          children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: FilterByDateWrapper,
            children: [(0, _jsxRuntime.jsx)(_icons.CalendarSearchFlight, {
              style: FilterByDateIcon
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              preset: "bodyTextRegular",
              text: "Today",
              style: FilterByDateText
            })]
          })
        })
      }), (0, _jsxRuntime.jsx)(_reactNativeModal.default, {
        isVisible: showCalendar,
        swipeDirection: null,
        style: styles.modalContainer,
        animationInTiming: 500,
        animationOutTiming: 500,
        animationIn: "slideInUp",
        animationOut: "slideOutDown",
        onBackdropPress: onClosedCalendarModal,
        onSwipeComplete: onClosedCalendarModal,
        children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: styles.calendarPickerContainer,
          activeOpacity: 1.0,
          onPress: function onPress() {
            setShowCalendar(false);
            (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
          },
          children: (0, _jsxRuntime.jsx)(_calendarFliter.default, {
            initialMinDate: initialCalendarFilterMinDate,
            containerStyle: styles.calendarFilterStyles,
            setFilterDate: function setFilterDate(dateString) {
              setShowCalendar(false);
              onFilterFlight({
                date: dateString
              });
              (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
            },
            filterDate: (0, _moment.default)(initialDate).format("YYYY-MM-DD") || (0, _moment.default)().format("YYYY-MM-DD"),
            testID: `dateTimePicker`,
            accessibilityLabel: `dateTimePicker`
          })
        })
      })]
    });
  };
  var _default = exports.default = _react.default.memo(FilterByDate);
