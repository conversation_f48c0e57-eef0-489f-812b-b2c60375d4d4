  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _calendarFliter = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _text = _$$_REQUIRE(_dependencyMap[9]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  var _Dimensions$get = _reactNative2.Dimensions.get("window"),
    height = _Dimensions$get.height;
  var AddReturnCalendar = function AddReturnCalendar(props) {
    var isVisible = props.isVisible,
      onClosedCalendarModal = props.onClosedCalendarModal,
      filterDate = props.filterDate,
      _props$initialMinDate = props.initialMinDate,
      initialMinDate = _props$initialMinDate === undefined ? (0, _moment.default)().format("YYYY-MM-DD") : _props$initialMinDate,
      onDateSelected = props.onDateSelected,
      onModalHide = props.onModalHide,
      testID = props.testID,
      accessibilityLabel = props.accessibilityLabel;
    return (0, _jsxRuntime.jsx)(_bottomSheet.default, {
      isModalVisible: isVisible,
      onModalHide: onModalHide,
      onClosedSheet: onClosedCalendarModal,
      containerStyle: styles.bottomSheetStyle,
      stopDragCollapse: true,
      onBackPressHandle: onClosedCalendarModal,
      animationInTiming: 500,
      animationOutTiming: 500,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.modalContainer,
        children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: styles.topModalContainer,
          onPress: onClosedCalendarModal
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.parentContainer,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.headerContainer,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              tx: "flightDetails.newPopupConfirmSaveFlight.selectReturnDate",
              preset: "h2",
              style: styles.headerTextStyles
            }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              style: styles.closeButtonStyles,
              onPress: onClosedCalendarModal,
              testID: `${testID}__TouchableClose`,
              accessibilityLabel: `${accessibilityLabel}__TouchableCrossClose`,
              children: (0, _jsxRuntime.jsx)(_icons.CrossPurple, {})
            })]
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.calendarContainer,
            children: (0, _jsxRuntime.jsx)(_calendarFliter.default, {
              setFilterDate: function setFilterDate(dateString) {
                onDateSelected(dateString);
              },
              initialMinDate: initialMinDate,
              filterDate: (0, _moment.default)(filterDate).format("YYYY-MM-DD") || (0, _moment.default)().format("YYYY-MM-DD"),
              testID: `${testID}__CalendarComponent`,
              accessibilityLabel: `${accessibilityLabel}__CalendarComponent`
            })
          })]
        })]
      })
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    bottomSheetStyle: {
      backgroundColor: _theme.color.palette.transparent,
      height: height
    },
    calendarContainer: Object.assign({
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 13,
      padding: 16
    }, _reactNative2.Platform.select({
      ios: {
        shadowRadius: 2,
        shadowOpacity: 0.1,
        shadowOffset: {
          width: 0,
          height: 3
        },
        backgroundColor: _theme.color.palette.whiteGrey
      },
      android: {
        elevation: 3,
        backgroundColor: _theme.color.palette.whiteGrey
      }
    })),
    closeButtonStyles: {
      position: 'absolute',
      right: -8
    },
    headerContainer: {
      alignItems: "center",
      justifyContent: "center"
    },
    headerTextStyles: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey,
      paddingVertical: 20
    }),
    modalContainer: {
      flex: 1,
      justifyContent: "flex-end"
    },
    parentContainer: {
      backgroundColor: _theme.color.palette.lightestGrey,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      height: "auto",
      paddingBottom: 36,
      paddingHorizontal: 24
    },
    topModalContainer: {
      backgroundColor: _theme.color.palette.transparent,
      flex: 1
    }
  });
  var _default = exports.default = AddReturnCalendar;
