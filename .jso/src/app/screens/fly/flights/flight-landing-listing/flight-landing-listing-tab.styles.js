  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  // import { presets } from "app/elements/text/text.presets"

  var styles = _reactNative.StyleSheet.create({
    topTabParentStyle: {
      flexDirection: "row",
      paddingHorizontal: 24,
      height: 40
    },
    tabTouchableOpacityStyle: {
      flex: 1,
      alignItems: "center",
      marginEnd: 24
    },
    topTabContainerStyle: {},
    topTabContentStyle: {
      flexDirection: "row"
    },
    tabActiveLabelStyle: {
      color: _theme.color.palette.lightPurple
    },
    tabInActiveLabelStyle: {
      color: _theme.color.palette.darkestGrey
    },
    tabActiveIndicatorStyle: {
      height: 4,
      backgroundColor: _theme.color.palette.lightPurple,
      borderRadius: 56,
      marginTop: 4
    },
    leftIconStyles: {
      marginRight: 4
    }
  });
  var _default = exports.default = styles;
