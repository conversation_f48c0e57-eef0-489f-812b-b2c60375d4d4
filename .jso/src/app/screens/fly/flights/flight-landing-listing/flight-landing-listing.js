  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[0]));
  var _flightProps = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNativePlugin = _$$_REQUIRE(_dependencyMap[2]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var FlightLandingListing = function FlightLandingListing(_ref) {
    var renderFlightTab = _ref.renderFlightTab;
    var _useContext = (0, _react.useContext)(_flightProps.FlightLandingContext),
      screenDirection = _useContext.screenDirection;
    var screenIndex = screenDirection === 'DEP' ? 1 : 0;
    var TabComponent = renderFlightTab == null ? undefined : renderFlightTab.tabList[screenIndex].component;
    var TabProps = renderFlightTab.tabList[screenIndex].props;
    return (0, _reactNativePlugin.createElement)(TabComponent, Object.assign({}, TabProps, {
      key: `FlightLandingListing-${screenDirection}`
    }));
  };
  var _default = exports.default = _react.default.memo(FlightLandingListing);
