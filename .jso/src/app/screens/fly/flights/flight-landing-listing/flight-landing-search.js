  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _color = _$$_REQUIRE(_dependencyMap[6]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _reactNativePermissions = _$$_REQUIRE(_dependencyMap[8]);
  var _utils = _$$_REQUIRE(_dependencyMap[9]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[10]);
  var _translate = _$$_REQUIRE(_dependencyMap[11]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[12]);
  var _flyRedux = _$$_REQUIRE(_dependencyMap[13]);
  var _icons = _$$_REQUIRE(_dependencyMap[14]);
  var _search = _$$_REQUIRE(_dependencyMap[15]);
  var _search2 = _$$_REQUIRE(_dependencyMap[16]);
  var _filterDateLanding = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[18]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var styles = _reactNative2.StyleSheet.create({
    searchBarWrap: {
      backgroundColor: _color.color.palette.lightestGrey
    },
    searchBarContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginHorizontal: 24
    },
    searchBarTopContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginHorizontal: 24,
      marginBottom: 12
    },
    searchBarText: {
      flex: 1
    },
    scanIconContainer: {
      marginLeft: 10,
      flexDirection: "row",
      alignItems: "center"
    },
    scanIconText: {
      marginLeft: 2
    },
    searchStyle: {
      flex: 1,
      borderTopLeftRadius: 0,
      borderBottomLeftRadius: 0
    }
  });
  var FlightLandingSearch = function FlightLandingSearch(props) {
    var _dataCommonAEM$data, _dataCommonAEM$data2;
    var testID = props.testID,
      setNoConnection = props.setNoConnection,
      navigation = props.navigation,
      onSearchPress = props.onSearchPress;
    var dispatch = (0, _reactRedux.useDispatch)();
    var flyLandingSelectedTab = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flyLandingSelectedTab);
    var dataCommonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig("AEM_COMMON_DATA"));
    var msg61 = dataCommonAEM == null || (_dataCommonAEM$data = dataCommonAEM.data) == null || (_dataCommonAEM$data = _dataCommonAEM$data.messages) == null ? undefined : _dataCommonAEM$data.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG61";
    });
    var msg62 = dataCommonAEM == null || (_dataCommonAEM$data2 = dataCommonAEM.data) == null || (_dataCommonAEM$data2 = _dataCommonAEM$data2.messages) == null ? undefined : _dataCommonAEM$data2.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG62";
    });
    var Rationale = {
      title: (msg61 == null ? undefined : msg61.title) || (0, _translate.translate)("requestPermission.camera.title"),
      message: (msg61 == null ? undefined : msg61.message) || (0, _translate.translate)("requestPermission.camera.message"),
      buttonPositive: (msg61 == null ? undefined : msg61.secondButton) || (0, _translate.translate)("requestPermission.camera.buttonPositive"),
      buttonNegative: (msg61 == null ? undefined : msg61.firstButton) || (0, _translate.translate)("requestPermission.camera.buttonNegative")
    };
    var onScanPress = (0, _react.useCallback)(function () {
      var checkConnection = /*#__PURE__*/function () {
        var _ref = (0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch.isConnected;
          if (!isConnected) {
            setNoConnection(true);
          } else {
            (0, _reactNativePermissions.request)((0, _utils.handleCondition)(_reactNative2.Platform.OS === "ios", _reactNativePermissions.PERMISSIONS.IOS.CAMERA, _reactNativePermissions.PERMISSIONS.ANDROID.CAMERA), Rationale).then(function (result) {
              if (result === _reactNativePermissions.RESULTS.BLOCKED) {
                _reactNative2.Alert.alert((msg62 == null ? undefined : msg62.title) || (0, _translate.translate)("scanCode.needAccessPermission.title"), (msg62 == null ? undefined : msg62.message) || (0, _translate.translate)("scanCode.needAccessPermission.description"), [{
                  text: (msg62 == null ? undefined : msg62.firstButton) || (0, _translate.translate)("scanCode.needAccessPermission.firstButton"),
                  style: "cancel",
                  onPress: function onPress() {
                    (0, _reactNativePermissions.openSettings)();
                  }
                }, {
                  text: (msg62 == null ? undefined : msg62.secondButton) || (0, _translate.translate)("scanCode.needAccessPermission.secondButton"),
                  onPress: function onPress() {
                    return null;
                  }
                }]);
              } else if (result === _reactNativePermissions.RESULTS.GRANTED) {
                navigation.navigate("scanCode");
              }
            });
          }
        });
        return function checkConnection() {
          return _ref.apply(this, arguments);
        };
      }();
      checkConnection();
    }, []);
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.searchBarWrap,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.searchBarTopContainer,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          preset: "h4",
          text: "find your flight",
          style: styles.searchBarText
        }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
          style: styles.scanIconContainer,
          onPress: onScanPress,
          testID: `${testID}__TouchableScan`,
          accessibilityLabel: `${testID}__TouchableScan`,
          children: [(0, _jsxRuntime.jsx)(_icons.ScanFlight, {
            width: 25,
            height: 25
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "textLink",
            text: "Scan",
            style: styles.scanIconText
          })]
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.searchBarContainer,
        children: [(0, _jsxRuntime.jsx)(_filterDateLanding.default, {
          flightScreen: "Landing",
          direction: flyLandingSelectedTab,
          dispatch: dispatch,
          navigation: navigation,
          buttonStyles: null
        }), (0, _jsxRuntime.jsx)(_search2.Search, {
          type: _search.SearchBarVariations.flightSearchBar,
          style: styles.searchStyle,
          placeholderTx: "departureSection.searchBarText",
          onPressed: onSearchPress,
          testID: `${testID}__Search`,
          accessibilityLabel: `${testID}__Search`
        })]
      })]
    });
  };
  var _default = exports.default = _react.default.memo(FlightLandingSearch);
