  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _translate = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _icons = _$$_REQUIRE(_dependencyMap[4]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _flightLandingListingTab = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _text = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var TabFlightLanding = function TabFlightLanding(_ref) {
    var tabOnClickCallback = _ref.tabOnClickCallback,
      tabKey = _ref.tabKey;
    var _useState = (0, _react.useState)([{
        key: "ARR",
        name: (0, _translate.translate)("flightLanding.arrivalTabTitle"),
        icon: {
          active: {
            leftSVGIcon: _icons.FlightArrivalIconActive
          },
          inActive: {
            leftSVGIcon: _icons.FlightArrivalIconInActive
          }
        },
        isFocused: true
      }, {
        key: "DEP",
        name: (0, _translate.translate)("flightLanding.departureTabTitle"),
        icon: {
          active: {
            leftSVGIcon: _icons.FlightDepartureIconActive
          },
          inActive: {
            leftSVGIcon: _icons.FlightDepartureIconInActive
          }
        }
      }]),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      data = _useState2[0],
      setData = _useState2[1];
    (0, _react.useEffect)(function () {
      onUpdateItem(tabKey);
    }, [tabKey]);
    var onUpdateItem = (0, _react.useCallback)(function (key) {
      setData(function (prevData) {
        return prevData.map(function (el) {
          if (el.key === key) {
            return Object.assign({}, el, {
              isFocused: true
            });
          } else {
            return Object.assign({}, el, {
              isFocused: false
            });
          }
        });
      });
    }, []);
    var _onPress = (0, _react.useCallback)(function (_ref2) {
      var item = _ref2.item,
        index = _ref2.index;
      onUpdateItem(item.key);
      tabOnClickCallback({
        index: index
      });
    }, []);
    var renderItem = (0, _react.useCallback)(function (_ref3) {
      var _item$icon, _item$icon2;
      var item = _ref3.item,
        index = _ref3.index;
      var LeftIcon = item != null && item.isFocused ? item == null || (_item$icon = item.icon) == null || (_item$icon = _item$icon.active) == null ? undefined : _item$icon.leftSVGIcon : item == null || (_item$icon2 = item.icon) == null || (_item$icon2 = _item$icon2.inActive) == null ? undefined : _item$icon2.leftSVGIcon;
      return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        accessibilityRole: "button",
        accessibilityLabel: `TabFlightLanding__Button__${item.key}`,
        testID: `TabFlightLanding__Button__${item.key}`,
        onPress: function onPress() {
          return _onPress({
            item: item,
            index: index
          });
        },
        style: _flightLandingListingTab.default.tabTouchableOpacityStyle,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _flightLandingListingTab.default.topTabContainerStyle,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _flightLandingListingTab.default.topTabContentStyle,
            children: [LeftIcon && (0, _jsxRuntime.jsx)(LeftIcon, {
              height: 20,
              width: 20,
              style: _flightLandingListingTab.default.leftIconStyles
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              style: item != null && item.isFocused ? _flightLandingListingTab.default.tabActiveLabelStyle : _flightLandingListingTab.default.tabInActiveLabelStyle,
              preset: "tabsSmall",
              children: item.name
            })]
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: item != null && item.isFocused ? _flightLandingListingTab.default.tabActiveIndicatorStyle : {}
          })]
        })
      }, `TabFlightLanding__Button__${item.key}`);
    }, []);
    return (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
      data: data,
      scrollEnabled: false,
      renderItem: renderItem,
      horizontal: false,
      showsVerticalScrollIndicator: false,
      keyExtractor: function keyExtractor(item) {
        return "TabFlightLanding-FlatList-" + item.key;
      },
      contentContainerStyle: _flightLandingListingTab.default.topTabParentStyle
    });
  };
  var _default = exports.default = _react.default.memo(TabFlightLanding);
