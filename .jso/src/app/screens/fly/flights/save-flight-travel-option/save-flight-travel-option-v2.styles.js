  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    height = _Dimensions$get.height;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    bottomSheetStyle: {
      height: height,
      backgroundColor: "transparent"
    },
    animatedContainer: {
      position: "absolute",
      left: 0,
      right: 0,
      backgroundColor: _theme.color.palette.transparent
    },
    modalContainer: {
      flex: 1,
      justifyContent: "flex-end"
    },
    topModalContainer: {
      backgroundColor: _theme.color.palette.transparent,
      flex: 1
    },
    parentContainer: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      paddingHorizontal: 24,
      width: "100%"
    },
    wrapHeaderBottomSheet: {
      alignItems: "center"
    },
    selectYourProfile: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey
    }),
    descriptionHeader: {
      marginBottom: 16,
      marginTop: 8,
      paddingHorizontal: 20,
      textAlign: "center",
      color: _theme.color.palette.almostBlackGrey
    },
    travellingText: {
      marginBottom: 12
    },
    pickingText: {
      marginTop: 12
    },
    containerTitle: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: 18
    },
    containerTitleLeft: {
      marginRight: 12,
      flex: 1
    },
    containerTitleRight: {
      flex: 1
    },
    wrapTitle: {
      flexDirection: "row",
      marginBottom: 5
    },
    title: {
      marginLeft: 2,
      color: _theme.color.palette.almostBlackGrey,
      flexShrink: 1 // allow text to wrap when it exceeds the container width
    },
    containerSelectYourProfile: {
      backgroundColor: _theme.color.palette.whiteGrey,
      paddingHorizontal: 16,
      width: "100%"
    },
    wrapSelectYourProfile: {
      borderWidth: 1,
      borderRadius: 4,
      borderColor: _theme.color.palette.lighterGrey,
      padding: 16,
      marginBottom: 10
    },
    headerTitle: {
      color: _theme.color.palette.almostBlackGrey,
      textAlign: "center",
      marginVertical: 12
    },
    img: {
      width: 136,
      height: 56,
      alignSelf: "center",
      marginBottom: 11,
      marginTop: 16
    },
    containerCloseIcon: {
      position: "absolute",
      right: 6,
      top: 0,
      padding: 10
    },
    containerFlightJourney: {
      flex: 1
    },
    overlay: Object.assign({}, _reactNative.StyleSheet.absoluteFillObject, {
      backgroundColor: _theme.color.palette.almostBlackGrey,
      opacity: 0.7
    }),
    bottomContainer: {
      paddingHorizontal: 24,
      paddingTop: 12,
      paddingBottom: 36,
      backgroundColor: _theme.color.palette.whiteGrey,
      marginBottom: -1
    },
    buttonLinearStyle: {
      borderRadius: 60,
      width: "100%"
    },
    loadingButtonStyles: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.lightGrey,
      borderRadius: 60,
      height: 44,
      justifyContent: "center",
      width: "100%"
    },
    lottieView: {
      height: 24,
      width: "100%"
    },
    textButtonStyleas: {
      color: _theme.color.palette.almostWhiteGrey
    }
  });
