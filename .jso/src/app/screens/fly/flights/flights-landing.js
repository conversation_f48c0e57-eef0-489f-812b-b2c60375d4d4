  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.topTabTouchableOpacityStyle = exports.FlightsLanding = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _departureLanding = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _navigationUtilities = _$$_REQUIRE(_dependencyMap[9]);
  var _translate = _$$_REQUIRE(_dependencyMap[10]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[11]);
  var _arrivalLanding = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _flightProps = _$$_REQUIRE(_dependencyMap[13]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[14]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[15]);
  var _flyRedux = _$$_REQUIRE(_dependencyMap[16]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[17]);
  var _dateTime = _$$_REQUIRE(_dependencyMap[18]);
  var _feedbackToastProps = _$$_REQUIRE(_dependencyMap[19]);
  var _text = _$$_REQUIRE(_dependencyMap[20]);
  var _flightListingCard = _$$_REQUIRE(_dependencyMap[21]);
  var _shortLink = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[22]));
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[23]));
  var _aemRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[24]));
  var _airportLandingRedux = _$$_REQUIRE(_dependencyMap[25]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[26]);
  var _storageKey = _$$_REQUIRE(_dependencyMap[27]);
  var _storage = _$$_REQUIRE(_dependencyMap[28]);
  var _isEmpty = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[29]));
  var _lodash = _$$_REQUIRE(_dependencyMap[30]);
  var _adobe = _$$_REQUIRE(_dependencyMap[31]);
  var _utils = _$$_REQUIRE(_dependencyMap[32]);
  var _flightLanding = _$$_REQUIRE(_dependencyMap[33]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[34]);
  var _stickyHeader = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[35]));
  var _native = _$$_REQUIRE(_dependencyMap[36]);
  var _fly = _$$_REQUIRE(_dependencyMap[37]);
  var _constants = _$$_REQUIRE(_dependencyMap[38]);
  var _appscapadeFlight = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[39]));
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[40]);
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[41]);
  var _bottomTabs = _$$_REQUIRE(_dependencyMap[42]);
  var _fab = _$$_REQUIRE(_dependencyMap[43]);
  var _reactNativeReanimated = _$$_REQUIRE(_dependencyMap[44]);
  var _flightLandingListingTab = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[45]));
  var _flightLandingListing = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[46]));
  var _flightLandingSearch = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[47]));
  var _screenHook = _$$_REQUIRE(_dependencyMap[48]);
  var _suspend = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[49]));
  var _mytravelRedux = _$$_REQUIRE(_dependencyMap[50]);
  var _pageConfigRedux = _$$_REQUIRE(_dependencyMap[51]);
  var _animatedFeedbackToast = _$$_REQUIRE(_dependencyMap[52]);
  var _bottomNavigator = _$$_REQUIRE(_dependencyMap[53]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[54]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  /* eslint-disable @typescript-eslint/no-unused-vars */

  var parentContainerStyle = {
    flex: 1,
    backgroundColor: _theme.color.palette.lightestGrey
  };
  var topTabTouchableOpacityStyle = exports.topTabTouchableOpacityStyle = {
    alignItems: "center",
    marginEnd: 24
  };
  var toastButtonStyle = Object.assign({}, _text.presets.textLink, {
    fontWeight: "normal",
    color: _theme.color.palette.lightBlue,
    alignItems: "flex-end"
  });
  var toastTextStyle = Object.assign({}, _text.presets.bodyTextRegular, {
    color: _theme.color.palette.whiteGrey,
    width: "80%"
  });
  var searchBarMarginBottom = 20;
  var searchBarContainerWithSeparator = {
    marginBottom: searchBarMarginBottom
  };
  var feedBackToastStyle = {
    width: "100%",
    paddingHorizontal: 16,
    bottom: 8
  };
  var flightLandingContainer = {
    paddingBottom: 10
  };
  var stickyHeaderStyle = {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0
  };
  var FlightComponentTypes = /*#__PURE__*/function (FlightComponentTypes) {
    FlightComponentTypes["shortcutLink"] = "shortcutLink";
    FlightComponentTypes["search"] = "search";
    FlightComponentTypes["flightList"] = "flightList";
    FlightComponentTypes["listTravelling"] = "listTravelling";
    FlightComponentTypes["listTransiting"] = "listTransiting";
    FlightComponentTypes["facilitiesAndServices"] = "facilitiesAndServices";
    FlightComponentTypes["listSavedFlight"] = "listSavedFlight";
    FlightComponentTypes["appscapadeBanner"] = "appscapadeBanner";
    return FlightComponentTypes;
  }(FlightComponentTypes || {});
  var COMPONENT_NAME = "FlightsLanding";
  var FlightsLanding = exports.FlightsLanding = function FlightsLanding(_ref) {
    var navigation = _ref.navigation,
      route = _ref.route;
    var _ref2 = (route == null ? undefined : route.params) || {},
      initFlightListTabKey = _ref2.initFlightListTabKey;
    var isFocused = (0, _native.useIsFocused)();
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var dispatch = (0, _reactRedux.useDispatch)();
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isNoConnection = _useState2[0],
      _setNoConnection = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      loadingLoadMoreLocal = _useState4[0],
      setLoadingLoadMore = _useState4[1];
    var _useHandleScroll = (0, _navigationUtilities.useHandleScroll)(),
      handleScroll = _useHandleScroll.handleScroll,
      scrollDirection = _useHandleScroll.scrollDirection;
    var toast = (0, _react.useRef)(null);
    var scrollViewRef = (0, _react.useRef)(null);
    var isTriggerFlightRequestRef = (0, _react.useRef)(false);
    var flyContentShortLinkFetching = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flyContentShortLinkFetching);
    var dataFlyLandingPageConfiguration = (0, _reactRedux.useSelector)(_pageConfigRedux.PageConfigSelectors.flyPagePayload);
    var configPage = (0, _utils.handleCondition)(!(0, _isEmpty.default)(dataFlyLandingPageConfiguration), dataFlyLandingPageConfiguration, _flightLanding.defaultFlyLandingPageConfiguration);
    var positionScrollRef = (0, _react.useRef)({
      contentOffsetY: 0,
      contentOffsetX: 0
    });
    var searchBarRef = (0, _react.useRef)(null);
    var _useState5 = (0, _react.useState)(0),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      locationSearchBar = _useState6[0],
      setLocationSearchBar = _useState6[1];
    var _useState7 = (0, _react.useState)(new Date().getTime()),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      pullToRefreshTimeStamp = _useState8[0],
      setPullToRefreshTimeStamp = _useState8[1];
    var FLY_CONTEXT_HANDLERS = _react.default.useContext(_fly.FLY_CONTEXT).Handlers;
    var animatedScrollYValue = (0, _react.useRef)(new _reactNative2.Animated.Value(0)).current;
    var flyPayload = (0, _reactRedux.useSelector)(function (state) {
      return _flyRedux.FlySelectors.flyArrivalLanding(state);
    });
    var isScrollToTopAfterShowTooltip = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.isScrollToTopAfterShowTooltip);
    var callAEMData = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.FLY_APPSCAPADE));
    var getAppCapadeLoading = (0, _react.useMemo)(function () {
      return (0, _lodash.get)(callAEMData, "loading");
    }, [callAEMData]);
    var bottomTabHeight = (0, _bottomTabs.useBottomTabBarHeight)();
    var fabOpacity = (0, _reactNativeReanimated.useSharedValue)(0);
    var _useContext = (0, _react.useContext)(_bottomNavigator.BottomNavContext),
      bottomTabsPosition = _useContext.bottomTabsPosition;
    var getOpenFlyScreenBefore = function getOpenFlyScreenBefore() {
      var openFlyScreenBefore = false;
      (0, _storage.load)(_storageKey.StorageKey.needDisableToolTipFlyScreen).then(function (value) {
        if (value) {
          openFlyScreenBefore = value;
        }
      });
      return openFlyScreenBefore;
    };
    var getStateGetArrFlight = (0, _react.useMemo)(function () {
      return (flyPayload == null ? undefined : flyPayload.type) === _flightListingCard.FlightListingState.loading;
    }, [flyPayload]);
    var checkAllowScrollEnabled = (0, _react.useMemo)(function () {
      var isOpenFlyScreenBefore = getOpenFlyScreenBefore();
      if (isOpenFlyScreenBefore) {
        if ((flyPayload == null ? undefined : flyPayload.type) === _flightListingCard.FlightListingState.loading) return true;
        if ((0, _isEmpty.default)(flyPayload == null ? undefined : flyPayload.data) || flyPayload != null && flyPayload.errorFlag) return false;
        return true;
      } else {
        return !getStateGetArrFlight;
      }
    }, [getStateGetArrFlight, getOpenFlyScreenBefore, flyPayload]);
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)(_constants.TrackingScreenName.FlyLanding);
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      _reactNative2.InteractionManager.runAfterInteractions(function () {
        FLY_CONTEXT_HANDLERS.fly_listing_departure_focus_search_bar = null;
        FLY_CONTEXT_HANDLERS.fly_listing_arrival_focus_search_bar = null;
        (0, _adobe.commonTrackingScreen)(_constants.TrackingScreenName.FlyLanding, (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
    }, []));
    var flyLandingSelectedTab = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flyLandingSelectedTab);
    var getChangiGameUrlLoading = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.getChangiGameUrlLoading);
    var flyLastUpdatedTimeStamp = (0, _reactRedux.useSelector)(function (state) {
      return _flyRedux.FlySelectors.flyLastUpdatedTimeStamp(state);
    });
    var contentContainerStyle = {
      paddingTop: 16,
      paddingBottom: ((0, _reactNativeSafeAreaContext.useSafeAreaInsets)().bottom > 0 ? 50 : 18) + bottomTabHeight
    };
    var flyDepartureLandingState = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flyDepartureLandingState);
    var flyArrivalLandingState = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flyArrivalLandingState);
    var flyDepartureLanding = (0, _reactRedux.useSelector)(function (state) {
      return _flyRedux.FlySelectors.flyDepartureLanding(state);
    });
    var flyArrivalLanding = (0, _reactRedux.useSelector)(function (state) {
      return _flyRedux.FlySelectors.flyArrivalLanding(state);
    });
    var departureLandingLoading = (0, _reactRedux.useSelector)(function (state) {
      return _flyRedux.FlySelectors.departureLandingLoading(state);
    });
    var arrivalLandingLoading = (0, _reactRedux.useSelector)(function (state) {
      return _flyRedux.FlySelectors.arrivalLandingLoading(state);
    });
    var isEndEarlierFlightLanding = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.isEndEarlierFlightLanding);
    var filterPillList = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.filterPillList) || [];
    var isLoadingTickerBandRef = (0, _react.useRef)(false);
    var getTickerBand = function getTickerBand() {
      isLoadingTickerBandRef.current = true;
      dispatch(_aemRedux.default.getAemConfigData({
        name: _aemRedux.AEM_PAGE_NAME.TICKER_BAND_FLIGHT,
        pathName: "getTickerbandFly",
        forceRequest: true
      }));
      setTimeout(function () {
        isLoadingTickerBandRef.current = false;
      }, 2000);
    };
    (0, _react.useEffect)(function () {
      if (isScrollToTopAfterShowTooltip) {
        var _scrollViewRef$curren;
        (_scrollViewRef$curren = scrollViewRef.current) == null || _scrollViewRef$curren.scrollTo({
          y: 0,
          animated: true
        });
        dispatch(_flyRedux.FlyCreators.setScrollToTopAfterShowTooltip(false));
      }
    }, [isScrollToTopAfterShowTooltip]);
    var checkOpenedScreen = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* () {
        var openFlyScreenBefore = yield (0, _storage.load)(_storageKey.StorageKey.needDisableToolTipFlyScreen);
        var appscapadeCompomentIsConfig = configPage == null ? undefined : configPage.find(function (element) {
          return element.sectionComponent === FlightComponentTypes.appscapadeBanner;
        });
        var isFeatureFlagON = (0, _remoteConfig.isFlagOnCondition)(FLY_CONTEXT_HANDLERS.fly_appscapade_flight_landing);
        var checkAppCapadeCondition = !(0, _isEmpty.default)(appscapadeCompomentIsConfig) && isFeatureFlagON ? getAppCapadeLoading !== undefined && !getAppCapadeLoading : true;
        if (!openFlyScreenBefore && !flyContentShortLinkFetching && checkAppCapadeCondition) {
          var _scrollViewRef$curren2;
          scrollViewRef == null || (_scrollViewRef$curren2 = scrollViewRef.current) == null || _scrollViewRef$curren2.scrollTo({
            y: locationSearchBar
          });
          _reactNative2.Platform.OS === "android" && setTimeout(function () {
            dispatch(_flyRedux.FlyCreators.setShowTooltipAfterScroll(true));
          }, 500);
        }
      });
      return function checkOpenedScreen() {
        return _ref3.apply(this, arguments);
      };
    }();
    (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
      _reactNative2.InteractionManager.runAfterInteractions(function () {
        if (!isLoadingTickerBandRef.current) {
          getTickerBand();
        }
        checkOpenedScreen();
      });
    }, [locationSearchBar, flyContentShortLinkFetching, getAppCapadeLoading, configPage]));
    (0, _react.useEffect)(function () {
      if (flyDepartureLandingState === _flightListingCard.FlightListingState.loading || flyArrivalLandingState === _flightListingCard.FlightListingState.loading || flyDepartureLandingState === undefined || flyArrivalLandingState === undefined) {
        var _toast$current;
        toast == null || (_toast$current = toast.current) == null || _toast$current.closeNow();
      }
    }, [flyDepartureLandingState, flyArrivalLandingState]);
    var checkInternetConnection = /*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        return isConnected;
      });
      return function checkInternetConnection() {
        return _ref4.apply(this, arguments);
      };
    }();
    var handleRefresh = function handleRefresh() {
      var callAllDataPage = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;
      checkInternetConnection().then(function (isConnection) {
        if (isConnection) {
          var _toast$current2;
          toast == null || (_toast$current2 = toast.current) == null || _toast$current2.closeNow();
          getTickerBand();
          getLandingData();
          if (isLoggedIn) {
            dispatch(_mytravelRedux.MytravelCreators.flyMyTravelFlightsRequest(profilePayload == null ? undefined : profilePayload.email));
          }
          if (callAllDataPage) {
            // call shortLinks and F&S
            dispatch(_flyRedux.FlyCreators.flyContentShortLinkRequest());
          }
        } else {
          if (flyLandingSelectedTab === _flightProps.FlightDirection.departure) {
            if (flyDepartureLandingState === _flightListingCard.FlightListingState.default) {
              var _toast$current3;
              toast == null || (_toast$current3 = toast.current) == null || _toast$current3.show(_constants.TOAST_MESSAGE_DURATION);
            }
          } else {
            if (flyArrivalLandingState === _flightListingCard.FlightListingState.default) {
              var _toast$current4;
              toast == null || (_toast$current4 = toast.current) == null || _toast$current4.show(_constants.TOAST_MESSAGE_DURATION);
            }
          }
        }
      });
    };
    var getLandingData = function getLandingData() {
      var filterPillItemChecked = filterPillList == null ? undefined : filterPillList.find(function (item) {
        return item.isSelected;
      });
      var filters = filterPillItemChecked.tagCode === "all" ? [] : [filterPillItemChecked.tagName];
      if (flyLandingSelectedTab === _flightProps.FlightDirection.departure) {
        dispatch(_flyRedux.FlyCreators.flyLandingDepartureRequest(_flightProps.FlightDirection.departure, _flightProps.FlightRequestType.FlightRefresh, filters));
      } else {
        dispatch(_flyRedux.FlyCreators.flyLandingArrivalRequest(_flightProps.FlightDirection.arrival, _flightProps.FlightRequestType.FlightRefresh, filters));
      }
      isEndEarlierFlightLanding && dispatch(_flyRedux.FlyCreators.resetEndEarlierFlightLanding());
      dispatch(_flyRedux.FlyCreators.flyLastUpdatedTimeStamp((0, _dateTime.flyModuleUpdatedTime)()));
    };
    var isFlyDepartureLandingRequestType = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.isFlyDepartureLandingRequestType);
    var isFlyDepartureLandingError = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.isFlyDepartureLandingError);
    var isFlyArrivalLandingRequestType = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.isFlyArrivalLandingRequestType);
    var isFlyArrivalLandingError = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.isFlyArrivalLandingError);
    var showFeedBackToastMessage = function showFeedBackToastMessage() {
      return (0, _jsxRuntime.jsx)(_animatedFeedbackToast.AnimatedFeedBackToast, {
        ref: toast,
        style: feedBackToastStyle,
        textButtonStyle: toastButtonStyle,
        position: "custom",
        textStyle: toastTextStyle,
        type: _feedbackToastProps.FeedBackToastType.fullWidthFeedBack,
        text: (0, _translate.translate)("flightLanding.feedBackToastErrorMessage") + flyLastUpdatedTimeStamp,
        testID: `${COMPONENT_NAME}__FeedBackToastErrorMessage`,
        accessibilityLabel: `${COMPONENT_NAME}__FeedBackToastErrorMessage`,
        positionBottom: bottomTabsPosition
      });
    };
    (0, _react.useEffect)(function () {
      var _toast$current5;
      toast == null || (_toast$current5 = toast.current) == null || _toast$current5.closeNow();
    }, [flyLandingSelectedTab]);
    (0, _react.useEffect)(function () {
      if (isFlyDepartureLandingError && flyLandingSelectedTab === _flightProps.FlightDirection.departure && isFlyDepartureLandingRequestType === _flightProps.FlightRequestType.FlightRefresh) {
        var _toast$current6;
        toast == null || (_toast$current6 = toast.current) == null || _toast$current6.show(_constants.TOAST_MESSAGE_DURATION);
      } else {
        var _toast$current7;
        toast == null || (_toast$current7 = toast.current) == null || _toast$current7.closeNow();
      }
    }, [isFlyDepartureLandingError, isFlyDepartureLandingRequestType === _flightProps.FlightRequestType.FlightRefresh]);
    (0, _react.useEffect)(function () {
      if (isFlyArrivalLandingError && flyLandingSelectedTab === _flightProps.FlightDirection.arrival && isFlyArrivalLandingRequestType === _flightProps.FlightRequestType.FlightRefresh) {
        var _toast$current8;
        toast == null || (_toast$current8 = toast.current) == null || _toast$current8.show(_constants.TOAST_MESSAGE_DURATION);
      } else {
        var _toast$current9;
        toast == null || (_toast$current9 = toast.current) == null || _toast$current9.closeNow();
      }
    }, [isFlyArrivalLandingError, isFlyArrivalLandingRequestType === _flightProps.FlightRequestType.FlightRefresh]);
    (0, _react.useEffect)(function () {
      ;
      (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch2.isConnected;
        if (!isConnected) {
          _setNoConnection(true);
        }
      })();
    }, []);
    var onLayoutTab = function onLayoutTab(_params) {
      return null;
    };
    var showUnableMessage = function showUnableMessage() {
      var _toast$current0, _toast$current1;
      toast == null || (_toast$current0 = toast.current) == null || _toast$current0.closeNow();
      toast == null || (_toast$current1 = toast.current) == null || _toast$current1.show(_constants.TOAST_MESSAGE_DURATION);
    };
    var hideUnableMessage = function hideUnableMessage() {
      var _toast$current10;
      toast == null || (_toast$current10 = toast.current) == null || _toast$current10.closeNow();
    };
    var triggerFlightRequest = function triggerFlightRequest() {
      isTriggerFlightRequestRef.current = true;
      var filterPillItemChecked = filterPillList == null ? undefined : filterPillList.find(function (item) {
        return item.isSelected;
      });
      var filters = filterPillItemChecked.tagCode === "all" ? [] : [filterPillItemChecked.tagName];
      var tab = flyLandingSelectedTab || _flightProps.FlightDirection.departure;
      if (tab === _flightProps.FlightDirection.arrival && flyArrivalLanding != null && flyArrivalLanding.nextToken) {
        setLoadingLoadMore(true);
        dispatch(_flyRedux.FlyCreators.flyLandingPaginationArrivalRequest(tab, flyArrivalLanding == null ? undefined : flyArrivalLanding.nextToken, filters));
      } else if (tab === _flightProps.FlightDirection.departure && flyDepartureLanding != null && flyDepartureLanding.nextToken) {
        setLoadingLoadMore(true);
        dispatch(_flyRedux.FlyCreators.flyLandingPaginationDepartureRequest(tab, flyDepartureLanding == null ? undefined : flyDepartureLanding.nextToken, filters));
      }
      setTimeout(function () {
        isTriggerFlightRequestRef.current = false;
      }, 2000);
    };
    var onScroll = function onScroll(event) {
      if (event.nativeEvent.contentOffset.y > locationSearchBar + 105) {
        fabOpacity.value = 1;
      } else {
        fabOpacity.value = 0;
      }
      if (event.nativeEvent.contentOffset.y > 0 && FLY_CONTEXT_HANDLERS != null && FLY_CONTEXT_HANDLERS.flyRefreshRef) {
        clearInterval(FLY_CONTEXT_HANDLERS == null ? undefined : FLY_CONTEXT_HANDLERS.flyRefreshRef);
      }
      handleScroll(event);
      positionScrollRef.current.contentOffsetX = event.nativeEvent.contentOffset.x;
      positionScrollRef.current.contentOffsetY = event.nativeEvent.contentOffset.y;
      if ((scrollDirection == null ? undefined : scrollDirection.current) === "up") return;
      if (departureLandingLoading || arrivalLandingLoading) return;
      var threshHold = (0, _utils.simpleCondition)({
        condition: event.nativeEvent.contentSize.height,
        ifValue: {
          condition: event.nativeEvent.contentSize.height > 7000,
          ifValue: 700,
          elseValue: event.nativeEvent.contentSize.height * 0.1
        },
        elseValue: 400
      });
      if ((0, _screenHelper.isCloseToBottom)(event, threshHold) && !isTriggerFlightRequestRef.current) {
        triggerFlightRequest();
      }
    };
    var searchBarOnLayout = function searchBarOnLayout(event) {
      setLocationSearchBar(event.nativeEvent.layout.y);
    };
    var _useState9 = (0, _react.useState)(initFlightListTabKey || _flightProps.FlightDirection.arrival),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      tabKey = _useState0[0],
      setTabKey = _useState0[1];
    var stickyOnPress = function stickyOnPress(event) {
      setLoadingLoadMore(false);
      var value = event.value;
      setTimeout(function () {
        var _scrollViewRef$curren3;
        (_scrollViewRef$curren3 = scrollViewRef.current) == null || _scrollViewRef$curren3.scrollTo({
          y: locationSearchBar + 50 + searchBarMarginBottom,
          animated: true
        });
        setTabKey(value);
      }, 0);
    };
    var tabOnClickCallback = function tabOnClickCallback(_ref6) {
      var index = _ref6.index;
      setLoadingLoadMore(false);
      var value = _flightProps.FlightDirection.arrival;
      if (index === 1) value = _flightProps.FlightDirection.departure;
      setTimeout(function () {
        setTabKey(value);
      }, 0);
      dispatch(_flyRedux.FlyCreators.flyLandingSelectedTab(value));
    };
    var scrollToTop = (0, _react.useCallback)(function () {
      var _scrollViewRef$curren4;
      (_scrollViewRef$curren4 = scrollViewRef.current) == null || _scrollViewRef$curren4.scrollTo({
        y: 0,
        animated: true
      });
      handleRefresh();
    }, [flyLandingSelectedTab, filterPillList, profilePayload, isLoggedIn, isEndEarlierFlightLanding]);
    var onSearchPress = function onSearchPress() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlyFlightSearchSearchBar, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlyFlightSearchSearchBar, _adobe.AdobeTagName.CAppFlightLanding));
      if (flyLandingSelectedTab === _flightProps.FlightDirection.arrival) {
        FLY_CONTEXT_HANDLERS.fly_listing_arrival_focus_search_bar = true;
      } else {
        FLY_CONTEXT_HANDLERS.fly_listing_departure_focus_search_bar = true;
      }
      navigation.navigate("flightResultLandingScreen", {
        screen: flyLandingSelectedTab,
        sourcePage: _adobe.AdobeTagName.CAppFlightLanding
      });
    };
    var hideLoadingLoadMore = (0, _react.useCallback)(function () {
      setLoadingLoadMore(false);
    }, []);
    var renderFlightTab = (0, _react.useMemo)(function () {
      return {
        tabList: [{
          component: _arrivalLanding.default,
          props: {
            showUnableMessage: showUnableMessage,
            hideUnableMessage: hideUnableMessage,
            onLayoutTab: onLayoutTab,
            pullToRefreshTimeStamp: pullToRefreshTimeStamp,
            loadingLoadMoreLocal: loadingLoadMoreLocal,
            hideLoadingLoadMore: hideLoadingLoadMore
          }
        }, {
          component: _departureLanding.default,
          props: {
            showUnableMessage: showUnableMessage,
            hideUnableMessage: hideUnableMessage,
            onLayoutTab: onLayoutTab,
            pullToRefreshTimeStamp: pullToRefreshTimeStamp,
            loadingLoadMoreLocal: loadingLoadMoreLocal,
            hideLoadingLoadMore: hideLoadingLoadMore
          }
        }]
      };
    }, [pullToRefreshTimeStamp, loadingLoadMoreLocal, hideLoadingLoadMore]);
    var pullToRefresh = function pullToRefresh() {
      setPullToRefreshTimeStamp(new Date().getTime());
      handleRefresh();
    };
    var headerOpacity = animatedScrollYValue.interpolate({
      inputRange: [locationSearchBar + 10, locationSearchBar + 15],
      outputRange: [0, 1],
      extrapolate: "clamp"
    });
    var headerZIndex = animatedScrollYValue.interpolate({
      inputRange: [locationSearchBar + 9, locationSearchBar + 10],
      outputRange: [-1, 1],
      extrapolate: "clamp"
    });
    var animatedStickyHeader = (0, _react.useMemo)(function () {
      if (!locationSearchBar) {
        return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {});
      }
      return (0, _jsxRuntime.jsx)(_reactNative2.Animated.View, {
        style: [stickyHeaderStyle, {
          opacity: headerOpacity,
          zIndex: headerZIndex
        }],
        children: (0, _jsxRuntime.jsx)(_stickyHeader.default, {
          onPress: stickyOnPress,
          navigation: navigation,
          onSearchPress: onSearchPress,
          dispatch: dispatch
        })
      });
    }, [locationSearchBar, headerOpacity, headerZIndex, flyLandingSelectedTab]);
    var showContent = (0, _react.useMemo)(function () {
      return configPage == null ? undefined : configPage.map(function (element, index) {
        var needSeparator = index !== (configPage == null ? undefined : configPage.length) - 1;
        switch (element == null ? undefined : element.sectionComponent) {
          case FlightComponentTypes.shortcutLink:
            return (0, _jsxRuntime.jsx)(_shortLink.default, {
              setNoConnection: function setNoConnection(isCheckConnect) {
                return _setNoConnection(isCheckConnect);
              },
              testID: `${COMPONENT_NAME}__Search`,
              accessibilityLabel: `${COMPONENT_NAME}__Search`,
              index: index,
              needSeparator: needSeparator
            }, `${element == null ? undefined : element.sectionComponent}-${index}`);
          case FlightComponentTypes.listSavedFlight:
            return (0, _jsxRuntime.jsx)(_reactNative2.View, {}, `${element == null ? undefined : element.sectionComponent}-${index}`);
          case FlightComponentTypes.search:
            return (0, _jsxRuntime.jsx)(_reactNative2.View, {
              ref: searchBarRef,
              style: (0, _utils.handleCondition)(needSeparator, searchBarContainerWithSeparator, null),
              onLayout: searchBarOnLayout,
              children: (0, _jsxRuntime.jsx)(_flightLandingSearch.default, {
                testID: COMPONENT_NAME,
                setNoConnection: _setNoConnection,
                navigation: navigation,
                onSearchPress: onSearchPress
              })
            }, `${element == null ? undefined : element.sectionComponent}-${index}`);
          case FlightComponentTypes.flightList:
            return (0, _jsxRuntime.jsx)(_suspend.default, {
              freeze: !isFocused,
              children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: flightLandingContainer,
                children: [(0, _jsxRuntime.jsx)(_flightLandingListingTab.default, {
                  tabOnClickCallback: tabOnClickCallback,
                  tabKey: tabKey
                }), (0, _jsxRuntime.jsx)(_flightProps.FlightLandingContext.Provider, {
                  value: {
                    screenDirection: tabKey
                  },
                  children: (0, _jsxRuntime.jsx)(_flightLandingListing.default, {
                    renderFlightTab: renderFlightTab
                  })
                })]
              }, `${element == null ? undefined : element.sectionComponent}-${index}`)
            });
          case FlightComponentTypes.facilitiesAndServices:
            return (0, _jsxRuntime.jsx)(_reactNative2.View, {}, `${element == null ? undefined : element.sectionComponent}-${index}`);
          case FlightComponentTypes.listTravelling:
            return (0, _jsxRuntime.jsx)(_reactNative2.View, {}, `${element == null ? undefined : element.sectionComponent}-${index}`);
          case FlightComponentTypes.listTransiting:
            return (0, _jsxRuntime.jsx)(_reactNative2.View, {}, `${element == null ? undefined : element.sectionComponent}-${index}`);
          case FlightComponentTypes.appscapadeBanner:
            return (0, _jsxRuntime.jsx)(_appscapadeFlight.default, {}, `${element == null ? undefined : element.sectionComponent}-${index}`);
          default:
            return null;
        }
      });
    }, [tabKey, isFocused, renderFlightTab]);
    (0, _react.useEffect)(function () {
      var isArrivalTabActived = tabKey === _flightProps.FlightDirection.arrival;
      var isInitTabKeyIsDeparture = initFlightListTabKey === _flightProps.FlightDirection.departure;
      if (isInitTabKeyIsDeparture && isArrivalTabActived) {
        setTabKey(_flightProps.FlightDirection.departure);
      }
    }, [initFlightListTabKey]);
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [showFeedBackToastMessage(), animatedStickyHeader, (0, _jsxRuntime.jsx)(_reactNative2.Animated.ScrollView, {
        keyboardShouldPersistTaps: "always",
        refreshControl: (0, _jsxRuntime.jsx)(_reactNative.RefreshControl, {
          refreshing: false,
          onRefresh: pullToRefresh
        }),
        onScroll: _reactNative2.Animated.event([{
          nativeEvent: {
            contentOffset: {
              y: animatedScrollYValue
            }
          }
        }], {
          listener: onScroll,
          useNativeDriver: true
        }),
        scrollEnabled: checkAllowScrollEnabled,
        scrollEventThrottle: 16,
        showsVerticalScrollIndicator: false,
        contentContainerStyle: contentContainerStyle,
        testID: `${COMPONENT_NAME}__ScrollViewFlightLanding`,
        accessibilityLabel: `${COMPONENT_NAME}__ScrollViewFlightLanding`,
        style: parentContainerStyle,
        ref: scrollViewRef,
        onMomentumScrollEnd: /*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
          var openFlyScreenBefore = yield (0, _storage.load)(_storageKey.StorageKey.needDisableToolTipFlyScreen);
          if (!openFlyScreenBefore) {
            dispatch(_flyRedux.FlyCreators.setShowTooltipAfterScroll(true));
          }
        }),
        children: showContent
      }), (0, _jsxRuntime.jsx)(_fab.FAB, {
        opacity: fabOpacity,
        onPress: scrollToTop
      }), (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        header: false,
        headerBackgroundColor: "transparent",
        visible: isNoConnection,
        testID: `${COMPONENT_NAME}__ErrorOverlayNoConnection`,
        onReload: function onReload() {
          var checkConnection = /*#__PURE__*/function () {
            var _ref8 = (0, _asyncToGenerator2.default)(function* () {
              var _yield$NetInfo$fetch3 = yield _netinfo.default.fetch(),
                isConnected = _yield$NetInfo$fetch3.isConnected;
              if (isConnected) {
                handleRefresh(true);
                _setNoConnection(false);
              }
            });
            return function checkConnection() {
              return _ref8.apply(this, arguments);
            };
          }();
          checkConnection();
        }
      }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
        visible: getChangiGameUrlLoading
      })]
    });
  };
