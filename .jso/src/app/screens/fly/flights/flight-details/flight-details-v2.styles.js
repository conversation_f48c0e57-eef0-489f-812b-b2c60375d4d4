  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useFlightDetailV2Styles = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[3]);
  var _Dimensions$get = _reactNative.Dimensions.get("screen"),
    SCREEN_HEIGHT = _Dimensions$get.height,
    SCREEN_WIDTH = _Dimensions$get.width;
  var LAYOUT_1_HEIGHT = SCREEN_HEIGHT * 0.8; // 80% of screen height
  var LAYOUT_2_HEIGHT = SCREEN_HEIGHT;
  var LAYOUT_3_HEIGHT = SCREEN_HEIGHT * 0.6;
  var LAYOUT_2_TOP_OFFSET = 14;
  var LAYOUT_3_TOP_OFFSET = 60;
  var ANIMATED_TIME_DURATION = 700; // milliseconds

  var useFlightDetailV2Styles = exports.useFlightDetailV2Styles = function useFlightDetailV2Styles() {
    var _useSafeAreaInsets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)(),
      top = _useSafeAreaInsets.top;
    var styles = _reactNative.StyleSheet.create({
      containerScrollView: {
        flex: 1
      },
      container: {
        flex: 1
      },
      layout1: {
        alignItems: "center",
        paddingTop: top,
        width: SCREEN_WIDTH,
        zIndex: 1,
        paddingHorizontal: 16
      },
      layout2: {
        height: LAYOUT_2_HEIGHT,
        position: "absolute",
        width: SCREEN_WIDTH,
        borderTopRightRadius: 30,
        borderTopLeftRadius: 30,
        justifyContent: "center",
        alignItems: "center",
        zIndex: 2,
        overflow: "hidden"
      },
      layout3: {
        borderTopRightRadius: 30,
        borderTopLeftRadius: 30,
        position: "absolute",
        width: SCREEN_WIDTH,
        zIndex: 3,
        minHeight: LAYOUT_3_HEIGHT,
        backgroundColor: _theme.color.palette.lightestGrey
      },
      wrapHeader: {
        paddingHorizontal: 14,
        position: "absolute"
      },
      toastStyleAddedFlight: {
        width: "100%",
        height: 60,
        marginBottom: 24,
        alignItems: "flex-start"
      },
      toastTextStyleAdded: Object.assign({}, _text.presets.bodyTextRegular, {
        color: _theme.color.palette.whiteGrey,
        width: "80%"
      }),
      toastButtonStyleAdded: Object.assign({}, _text.presets.textLink, {
        fontWeight: "normal",
        color: _theme.color.palette.lightBlue,
        alignItems: "flex-end"
      }),
      feedBackToastStyle: {
        width: SCREEN_WIDTH - 32,
        marginHorizontal: 16,
        height: 72,
        marginBottom: 20
      },
      toastButtonStyle: Object.assign({}, _text.presets.textLink, {
        fontWeight: "normal",
        color: _theme.color.palette.lightBlue,
        alignItems: "flex-end"
      }),
      toastTextStyle: Object.assign({}, _text.presets.bodyTextRegular, {
        color: _theme.color.palette.whiteGrey,
        width: "80%"
      }),
      containerInformationHubCard: {
        flex: 1,
        width: "100%",
        height: "100%",
        backgroundColor: "#F7F7F7"
      },
      coverFooterCointainer: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: _theme.color.palette.lightestGrey
      },
      containerUnsaveFlight: {
        paddingTop: 12,
        borderTopWidth: 1,
        borderTopColor: _theme.color.palette.lighterGrey,
        marginHorizontal: 16
      },
      containerUnsaveFlightBtn: {
        paddingTop: 12
      },
      unsaveFlightLabel: {
        color: _theme.color.palette.lightPurple
      },
      maintenanceErrorContainer: {
        justifyContent: "center",
        alignItems: "center",
        flex: 1
      },
      errorOverLay: {
        width: _reactNative.Dimensions.get("screen").width,
        height: _reactNative.Dimensions.get("screen").height,
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 999,
        backgroundColor: _theme.color.palette.lightestGrey
      },
      flightSaverContainer: {
        width: SCREEN_WIDTH - 32,
        height: 88,
        borderRadius: 16,
        paddingHorizontal: 16,
        alignItems: "center",
        flexDirection: "row",
        marginVertical: 16
      },
      evoucherContainer: {
        width: 40,
        height: 40,
        justifyContent: "center",
        alignItems: "center",
        borderRadius: 40,
        backgroundColor: _theme.color.palette.whiteGrey,
        marginRight: 12
      },
      flightSaverInformationContainer: {
        flex: 1,
        rowGap: 4
      },
      rowCenter: {
        flexDirection: "row",
        alignItems: "center"
      },
      justifySpaceBetween: {
        justifyContent: "space-between"
      },
      addText: {
        fontFamily: _theme.typography.bold,
        fontSize: 10,
        lineHeight: 12,
        color: _theme.color.palette.whiteGrey,
        marginRight: 6
      },
      whiteText: {
        color: _theme.color.palette.whiteGrey
      },
      textGradient: {
        fontSize: 14,
        fontFamily: _theme.typography.bold,
        lineHeight: 18
      },
      descriptionSuffix: {
        color: _theme.color.palette.whiteGrey,
        alignSelf: "flex-start"
      },
      flightSaverTitle: {
        flex: 1,
        color: "#FCFCFC99",
        letterSpacing: 2.4
      },
      addButtonContainer: {
        width: 32,
        height: 32,
        justifyContent: "center",
        alignItems: "center",
        borderRadius: 40,
        backgroundColor: _theme.color.palette.whiteGrey
      }
    });
    return {
      styles: styles,
      SCREEN_HEIGHT: SCREEN_HEIGHT,
      LAYOUT_1_HEIGHT: LAYOUT_1_HEIGHT,
      LAYOUT_2_TOP_OFFSET: LAYOUT_2_TOP_OFFSET,
      LAYOUT_3_TOP_OFFSET: LAYOUT_3_TOP_OFFSET,
      LAYOUT_3_HEIGHT: LAYOUT_3_HEIGHT,
      ANIMATED_TIME_DURATION: ANIMATED_TIME_DURATION
    };
  };
