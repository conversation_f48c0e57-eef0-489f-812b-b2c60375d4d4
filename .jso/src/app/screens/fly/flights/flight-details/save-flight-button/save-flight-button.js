  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _button = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _lottieReactNative = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  var buttonLinearStyle = {
    borderRadius: 60,
    marginTop: 12
  };
  var saveFlightStyle = {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 24,
    paddingTop: 12,
    paddingBottom: 36,
    backgroundColor: _theme.color.palette.whiteGrey
  };
  var containerStyle = {
    height: 44,
    borderRadius: 60,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: _theme.color.palette.lightGrey
  };
  var lottieView = {
    height: 60,
    width: '100%'
  };
  var activeGradient = [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End];
  var inActiveGradient = [_theme.color.palette.lightGrey, _theme.color.palette.lightGrey];
  var buttonDisabledStyle = {
    borderColor: _theme.color.palette.lightGrey
  };
  var textButtonDisabledStyle = {
    color: _theme.color.palette.darkGrey
  };
  var SaveFlightButton = function SaveFlightButton(props) {
    var loading = props.loading,
      onPress = props.onPress,
      isFlightCanSave = props.isFlightCanSave,
      disabled = props.disabled,
      onLayout = props.onLayout;
    var renderLoadingButton = function renderLoadingButton() {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: containerStyle,
        children: (0, _jsxRuntime.jsx)(_lottieReactNative.default, {
          source: _$$_REQUIRE(_dependencyMap[8]),
          autoPlay: true,
          loop: true,
          style: lottieView
        })
      });
    };
    var renderSaveButton = function renderSaveButton() {
      return (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
        style: buttonLinearStyle,
        start: {
          x: 0,
          y: 1
        },
        end: {
          x: 1,
          y: 0
        },
        colors: isFlightCanSave ? activeGradient : inActiveGradient,
        children: (0, _jsxRuntime.jsx)(_button.Button, {
          tx: "flightDetails.saveFlight",
          sizePreset: "large",
          textPreset: "buttonLarge",
          typePreset: "secondary",
          statePreset: "default",
          backgroundPreset: "light",
          onPress: onPress,
          disabled: disabled,
          style: isFlightCanSave ? {} : buttonDisabledStyle,
          textStyle: isFlightCanSave ? {} : textButtonDisabledStyle
        })
      });
    };
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      onLayout: onLayout,
      style: saveFlightStyle,
      children: loading ? renderLoadingButton() : renderSaveButton()
    });
  };
  var _default = exports.default = SaveFlightButton;
