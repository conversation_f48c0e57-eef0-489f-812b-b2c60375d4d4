  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _icons = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  var FlightLocationPlace = function FlightLocationPlace(_ref) {
    var shortName = _ref.shortName,
      fullName = _ref.fullName,
      type = _ref.type,
      _ref$showDetails = _ref.showDetails,
      showDetails = _ref$showDetails === undefined ? false : _ref$showDetails,
      renderDetails = _ref.renderDetails;
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.header,
        children: [type === "departure" ? (0, _jsxRuntime.jsx)(_icons.FlightDepartureIconV2, {}) : (0, _jsxRuntime.jsx)(_icons.FlightArrivalIconV2, {}), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.locationTextContainer,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            text: shortName,
            style: _text.newPresets.bodyTextBlackBold,
            testID: "Text_FlightLocationPlace",
            accessibilityLabel: "Text_FlightLocationPlace"
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            text: ` ${fullName}`,
            style: styles.fullNameText,
            testID: "Text_fullNameText",
            accessibilityLabel: "Text_fullNameText"
          })]
        })]
      }), !!showDetails && (renderDetails == null ? undefined : renderDetails())]
    });
  };
  var _default = exports.default = FlightLocationPlace;
  var styles = _reactNative.StyleSheet.create({
    container: {
      rowGap: 4
    },
    header: {
      flexDirection: 'row',
      columnGap: 8,
      alignItems: 'center'
    },
    locationTextContainer: {
      flexDirection: 'row',
      columnGap: 2,
      alignItems: 'flex-end'
    },
    fullNameText: Object.assign({}, _text.newPresets.caption2Regular, {
      color: _theme.color.palette.midGrey
    })
  });
