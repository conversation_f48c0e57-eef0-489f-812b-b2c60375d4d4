  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[1]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _icons = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _flightProps = _$$_REQUIRE(_dependencyMap[6]);
  var _utils = _$$_REQUIRE(_dependencyMap[7]);
  var _flightDetails = _$$_REQUIRE(_dependencyMap[8]);
  var _constants = _$$_REQUIRE(_dependencyMap[9]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var FlightCheckinInfo = function FlightCheckinInfo(_ref) {
    var onPressGate = _ref.onPressGate,
      onPressBaggageBelt = _ref.onPressBaggageBelt,
      onPressCheckinRow = _ref.onPressCheckinRow,
      flightDetailsData = _ref.flightDetailsData;
    var _ref2 = flightDetailsData || {},
      gate = _ref2.gate,
      direction = _ref2.direction,
      baggageBelt = _ref2.baggageBelt,
      terminal = _ref2.terminal,
      statusMapping = _ref2.statusMapping,
      checkInRow = _ref2.checkInRow;
    var flightTerminalDisclaimerText = (0, _react.useMemo)(function () {
      return flightDetailsData == null ? undefined : flightDetailsData.displayTerminalDisclaimer;
    }, [flightDetailsData == null ? undefined : flightDetailsData.displayTerminalDisclaimer]);
    var isShowGate = (0, _react.useMemo)(function () {
      if (direction === _flightProps.FlightDirection.arrival) {
        return !!(statusMapping != null && statusMapping.show_gate) && !!gate;
      }
      return !!gate;
    }, [statusMapping == null ? undefined : statusMapping.show_gate, gate, direction]);
    if (direction === _flightProps.FlightDirection.departure) {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: stylesDEP.containerCheckinInfo,
        children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: stylesDEP.containerCheckin,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            tx: "flightDetailV2.flightInfo.checkin.label",
            style: stylesDEP.checkinLabel,
            testID: "Text_baggageBelt",
            accessibilityLabel: "Text_baggageBelt"
          }), (0, _jsxRuntime.jsxs)(_reactNative2.TouchableOpacity, {
            style: stylesDEP.containerLocationCheckin,
            disabled: !checkInRow,
            onPress: function onPress() {
              return onPressCheckinRow(_flightDetails.TypePressDetailFlightCard.CHECK_IN_ROW);
            },
            testID: "TouchableOpacity_baggageBelt",
            accessibilityLabel: "TouchableOpacity_baggageBelt",
            children: [!!terminal || !!checkInRow ? (0, _jsxRuntime.jsx)(_text.Text, {
              text: `${!!terminal ? `T${terminal}` : '-'}, ${!!checkInRow ? `Row ${checkInRow}` : 'Row -'}`,
              style: stylesDEP.checkinInfoLabel,
              testID: "Text_terminalRow",
              accessibilityLabel: "Text_terminalRow"
            }) : (0, _jsxRuntime.jsx)(_text.Text, {
              text: "-",
              style: stylesDEP.checkinInfoLabel,
              testID: "Text_terminalRow",
              accessibilityLabel: "Text_terminalRow"
            }), !!terminal && !!checkInRow && (0, _jsxRuntime.jsx)(_icons.LocationV2, {
              width: 12,
              height: 12
            })]
          }), !!flightTerminalDisclaimerText && (0, _jsxRuntime.jsx)(_text.Text, {
            text: flightTerminalDisclaimerText,
            style: stylesDEP.disclaimerText,
            testID: "Text_disclaimerText",
            accessibilityLabel: "Text_disclaimerText"
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: stylesDEP.containerCheckin,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            tx: "flightDetailV2.flightInfo.checkin.gateLabel",
            style: stylesDEP.checkinLabel,
            testID: "Text_gateLabel",
            accessibilityLabel: "Text_gateLabel"
          }), (0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
            style: stylesDEP.containerLocationCheckin,
            disabled: !isShowGate,
            onPress: function onPress() {
              return onPressGate(_flightDetails.TypePressDetailFlightCard.GATE);
            },
            testID: "TouchableOpacity_gateLabel",
            accessibilityLabel: "TouchableOpacity_gateLabel",
            children: (0, _utils.handleCondition)(isShowGate, (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                text: gate,
                style: stylesDEP.checkinInfoLabel,
                testID: "Text_checkinInfoLabel",
                accessibilityLabel: "Text_checkinInfoLabel"
              }), (0, _jsxRuntime.jsx)(_icons.LocationV2, {
                width: 12,
                height: 12
              })]
            }), (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                text: "-",
                style: stylesDEP.checkinInfoLabel,
                testID: "Text_checkinInfoLabel",
                accessibilityLabel: "Text_checkinInfoLabel"
              })
            }))
          }), (0, _utils.handleCondition)(!!(statusMapping != null && statusMapping.show_gate) && !!_constants.SKYTRAIN_GATES_OBJ[gate], (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: stylesDEP.containerSkyTrain,
            children: [(0, _jsxRuntime.jsx)(_icons.FlightSkyTrainV2, {}), (0, _jsxRuntime.jsx)(_text.Text, {
              tx: "flightDetailV2.flightInfo.checkin.transport",
              style: styles.transportLabel
            })]
          }), null)]
        })]
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.containerCheckinInfo,
      children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.containerCheckin,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          tx: "flightDetailV2.flightInfo.checkin.gateLabel",
          style: styles.checkinLabel,
          testID: "Text_gateLabel",
          accessibilityLabel: "Text_gateLabel"
        }), (0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
          style: styles.containerLocationCheckin,
          disabled: !isShowGate,
          onPress: function onPress() {
            return onPressGate(_flightDetails.TypePressDetailFlightCard.GATE);
          },
          testID: "TouchableOpacity_gateLabel",
          accessibilityLabel: "TouchableOpacity_gateLabel",
          children: (0, _utils.handleCondition)(isShowGate, (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              text: gate,
              style: styles.checkinInfoLabel,
              testID: "Text_checkinInfoLabel",
              accessibilityLabel: "Text_checkinInfoLabel"
            }), (0, _jsxRuntime.jsx)(_icons.LocationV2, {
              width: 12,
              height: 12
            })]
          }), (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              text: "-",
              style: styles.checkinInfoLabel,
              testID: "Text_checkinInfoLabel",
              accessibilityLabel: "Text_checkinInfoLabel"
            })
          }))
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: [styles.containerCheckin, {
          flex: 2
        }],
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          tx: "flightDetailV2.flightInfo.checkin.baggageLabel",
          style: styles.checkinLabel,
          testID: "Text_baggageBelt",
          accessibilityLabel: "Text_baggageBelt"
        }), (0, _jsxRuntime.jsxs)(_reactNative2.TouchableOpacity, {
          style: styles.containerLocationCheckin,
          disabled: !baggageBelt,
          onPress: function onPress() {
            return onPressBaggageBelt(_flightDetails.TypePressDetailFlightCard.BAGGAGE_BELT);
          },
          testID: "TouchableOpacity_baggageBelt",
          accessibilityLabel: "TouchableOpacity_baggageBelt",
          children: [!!terminal || !!baggageBelt ? (0, _jsxRuntime.jsx)(_text.Text, {
            text: `${!!terminal ? `T${terminal}` : '-'}, ${!!baggageBelt ? `Belt ${baggageBelt}` : '-'}`,
            style: styles.checkinInfoLabel,
            testID: "Text_terminalBaggage",
            accessibilityLabel: "Text_terminalBaggage"
          }) : (0, _jsxRuntime.jsx)(_text.Text, {
            text: "-",
            style: styles.checkinInfoLabel,
            testID: "Text_terminalBaggage",
            accessibilityLabel: "Text_terminalBaggage"
          }), !!terminal && !!baggageBelt && (0, _jsxRuntime.jsx)(_icons.LocationV2, {
            width: 12,
            height: 12
          })]
        }), !!flightTerminalDisclaimerText && (0, _jsxRuntime.jsx)(_text.Text, {
          text: flightTerminalDisclaimerText,
          style: styles.disclaimerText,
          testID: "Text_disclaimerText",
          accessibilityLabel: "Text_disclaimerText"
        })]
      })]
    });
  };
  var _default = exports.default = FlightCheckinInfo;
  var styles = _reactNative.StyleSheet.create({
    containerCheckinInfo: {
      flexDirection: "row",
      width: '100%',
      columnGap: 4,
      flexWrap: 'wrap',
      rowGap: 4
    },
    containerCheckin: {
      padding: 8,
      backgroundColor: _theme.color.palette.backgroundFlightCheckin,
      borderRadius: 8,
      flex: 1,
      rowGap: 4
    },
    checkinLabel: Object.assign({}, _text.newPresets.caption2Regular, {
      color: _theme.color.palette.midGrey
    }),
    containerLocationCheckin: {
      flexDirection: "row",
      alignItems: "center"
    },
    checkinInfoLabel: Object.assign({}, _text.newPresets.bodyTextBold, {
      color: _theme.color.palette.whiteGrey,
      marginRight: 4
    }),
    transportLabel: Object.assign({}, _text.newPresets.caption2Regular, {
      color: _theme.color.palette.midGrey,
      marginLeft: 4
    }),
    disclaimerText: Object.assign({}, _text.newPresets.caption2Regular, {
      color: _theme.color.palette.midGrey
    })
  });
  var stylesDEP = _reactNative.StyleSheet.create({
    containerCheckinInfo: {
      flexDirection: "row",
      width: '100%',
      columnGap: 4,
      flexWrap: 'wrap',
      rowGap: 4
    },
    containerCheckin: {
      padding: 8,
      backgroundColor: _theme.color.palette.backgroundFlightCheckin,
      borderRadius: 8,
      flex: 1,
      rowGap: 4
    },
    checkinLabel: Object.assign({}, _text.newPresets.caption2Regular, {
      color: _theme.color.palette.midGrey
    }),
    containerLocationCheckin: {
      flexDirection: "row",
      alignItems: "center"
    },
    checkinInfoLabel: Object.assign({}, _text.newPresets.bodyTextBold, {
      color: _theme.color.palette.whiteGrey,
      marginRight: 4
    }),
    transportLabel: Object.assign({}, _text.newPresets.caption2Regular, {
      color: _theme.color.palette.midGrey,
      marginLeft: 4
    }),
    disclaimerText: Object.assign({}, _text.newPresets.caption2Regular, {
      color: _theme.color.palette.midGrey
    }),
    containerSkyTrain: {
      flexDirection: 'row',
      alignItems: 'center',
      columnGap: 2
    }
  });
