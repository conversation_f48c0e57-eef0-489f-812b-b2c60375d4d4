  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  var testID = "FlightDetail";
  var FlightTransitError = _react.default.memo(function (props) {
    var onRetry = props.onRetry;
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        tx: "flightDetailV2.flightInfo.transitError",
        style: styles.txtContent,
        testID: `${testID}__title_transit_error`,
        accessibilityLabel: `${testID}__title_transit_error`
      }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: styles.buttonRetry,
        onPress: onRetry,
        testID: `${testID}__ButtonRetry`,
        accessibilityLabel: `${testID}__ButtonRetry`,
        children: [(0, _jsxRuntime.jsx)(_icons.RetryIcon, {
          width: 11,
          height: 11
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          tx: "flightDetailV2.flightInfo.retry",
          style: styles.txtButton,
          testID: `${testID}__Text_ButtonRetry`,
          accessibilityLabel: `${testID}__Text_ButtonRetry`
        })]
      })]
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center'
    },
    txtContent: Object.assign({}, _text.presets.caption2Regular, {
      fontSize: 12,
      lineHeight: 16,
      color: _theme.color.palette.midGrey
    }),
    buttonRetry: {
      flexDirection: 'row',
      alignItems: 'center',
      marginLeft: 4
    },
    txtButton: Object.assign({}, _text.presets.caption1Bold, {
      color: _theme.color.palette.lighterPurple,
      marginLeft: 2
    })
  });
  var _default = exports.default = FlightTransitError;
