  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.SIZE_FLIGHT_ICON = exports.SIZE_CIRCLE = exports.PADDING_TOP_FLIGHT_ICON = exports.HEIGHT_TRANSIT_FOREIGN_FLIGHT = exports.HEIGHT_NORMAL_FLIGHT_TRANSIT = exports.HEIGHT_NORMAL_FLIGHT_FASTCHECKIN = exports.HEIGHT_NORMAL_FLIGHT = exports.HEIGHT_LINE_CIRCLE_ONLY = exports.HEIGHT_FAST_CHECKIN = exports.GAP_8 = exports.GAP_10 = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _flightInfo = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNativeSvg = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _flightInfo2 = _$$_REQUIRE(_dependencyMap[6]);
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _utils = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var GAP_8 = exports.GAP_8 = 8;
  var GAP_10 = exports.GAP_10 = 10;
  var PADDING_TOP_FLIGHT_ICON = exports.PADDING_TOP_FLIGHT_ICON = 16;
  var SIZE_FLIGHT_ICON = exports.SIZE_FLIGHT_ICON = 24;
  var HEIGHT_FAST_CHECKIN = exports.HEIGHT_FAST_CHECKIN = 25;
  var HEIGHT_TRANSIT_FOREIGN_FLIGHT = exports.HEIGHT_TRANSIT_FOREIGN_FLIGHT = 100;
  var HEIGHT_NORMAL_FLIGHT_FASTCHECKIN = exports.HEIGHT_NORMAL_FLIGHT_FASTCHECKIN = 210;
  var HEIGHT_NORMAL_FLIGHT = exports.HEIGHT_NORMAL_FLIGHT = 210;
  var HEIGHT_NORMAL_FLIGHT_TRANSIT = exports.HEIGHT_NORMAL_FLIGHT_TRANSIT = 213;
  var SIZE_CIRCLE = exports.SIZE_CIRCLE = 9;
  var HEIGHT_LINE_CIRCLE_ONLY = exports.HEIGHT_LINE_CIRCLE_ONLY = SIZE_FLIGHT_ICON / 2 - SIZE_CIRCLE / 2 + PADDING_TOP_FLIGHT_ICON;
  var getValue = function getValue(value, defaultValue) {
    return value || defaultValue;
  };
  var renderCircle = function renderCircle() {
    return (0, _jsxRuntime.jsx)(_reactNativeSvg.default, {
      height: SIZE_CIRCLE,
      width: SIZE_CIRCLE,
      children: (0, _jsxRuntime.jsx)(_reactNativeSvg.Circle, {
        cx: SIZE_CIRCLE / 2,
        cy: SIZE_CIRCLE / 2,
        r: SIZE_CIRCLE / 2,
        fill: _theme.color.palette.greyD9D9D9
      })
    });
  };
  var renderLine = function renderLine(height) {
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: {
        height: height
      },
      children: (0, _jsxRuntime.jsx)(_reactNativeSvg.default, {
        height: "100%",
        width: 2,
        children: (0, _jsxRuntime.jsx)(_reactNativeSvg.Line, {
          x1: 1,
          y1: 0,
          x2: 1,
          y2: 700,
          stroke: _theme.color.palette.greyD9D9D9,
          strokeWidth: 1,
          strokeDasharray: "2 2"
        })
      })
    });
  };
  var TimelineHeaderItem = function TimelineHeaderItem(_ref) {
    var type = _ref.type,
      height = _ref.height,
      flightScheduledDate = _ref.flightScheduledDate,
      mainTime = _ref.mainTime,
      reTimeFlag = _ref.reTimeFlag,
      numberDaysDiff = _ref.numberDaysDiff,
      scheduledTime = _ref.scheduledTime,
      gapEnd = _ref.gapEnd,
      needCircleEnd = _ref.needCircleEnd,
      needCircleDepDateTimeEnd = _ref.needCircleDepDateTimeEnd,
      isHasTransitDataAndDestinationEmpty = _ref.isHasTransitDataAndDestinationEmpty;
    switch (type) {
      case _flightInfo.TypeTimelineHeader.DepCircleEnd:
        return (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: _flightInfo2.styles.centered,
          children: [renderLine(HEIGHT_LINE_CIRCLE_ONLY), renderCircle()]
        });
      case _flightInfo.TypeTimelineHeader.ArrCircleEnd:
        return (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: _flightInfo2.styles.centered,
          children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
            style: {
              height: HEIGHT_LINE_CIRCLE_ONLY
            }
          }), renderCircle(), renderLine(height - PADDING_TOP_FLIGHT_ICON - SIZE_FLIGHT_ICON / 2)]
        });
      case _flightInfo.TypeTimelineHeader.DepCircleMiddle:
        return (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: _flightInfo2.styles.centered,
          children: [renderLine(height), (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: [_flightInfo2.styles.centered, _flightInfo2.styles.absoluteUp],
            children: renderCircle()
          })]
        });
      case _flightInfo.TypeTimelineHeader.DepDateTimeEnd:
        return (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: _flightInfo2.styles.rowGap2,
          children: [needCircleDepDateTimeEnd && (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: [_flightInfo2.styles.centered],
            children: renderCircle()
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: [_text.newPresets.smallTextBlackBold, _flightInfo2.styles.date],
            text: flightScheduledDate
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
              style: _flightInfo2.styles.timeRow,
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                style: _text.newPresets.bodyTextBold,
                text: mainTime
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                style: _flightInfo2.styles.dayAdded,
                text: reTimeFlag && !!numberDaysDiff ? `${numberDaysDiff}` : ""
              })]
            }), reTimeFlag && (0, _jsxRuntime.jsx)(_text.Text, {
              style: _flightInfo2.styles.oldTime,
              text: getValue(scheduledTime, "N/A")
            })]
          })]
        });
      case _flightInfo.TypeTimelineHeader.DepDateTimeStart:
        return (0, _jsxRuntime.jsxs)(_reactNative.View, {
          children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: _flightInfo2.styles.rowGap2,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: [_text.newPresets.smallTextBlackBold, _flightInfo2.styles.date],
              text: flightScheduledDate
            }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
              children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
                style: _flightInfo2.styles.timeRow,
                children: [(0, _jsxRuntime.jsx)(_text.Text, {
                  style: _text.newPresets.bodyTextBold,
                  text: mainTime
                }), (0, _jsxRuntime.jsx)(_text.Text, {
                  style: _flightInfo2.styles.dayAdded,
                  text: reTimeFlag && !!numberDaysDiff ? `${numberDaysDiff}` : ""
                })]
              }), (0, _utils.handleCondition)(!!reTimeFlag, (0, _jsxRuntime.jsx)(_text.Text, {
                style: [_flightInfo2.styles.oldTime, {
                  marginBottom: GAP_8
                }],
                text: getValue(scheduledTime, "N/A")
              }), (0, _jsxRuntime.jsx)(_reactNative.View, {
                style: {
                  height: GAP_10
                }
              }))]
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: _flightInfo2.styles.centered,
            children: [(0, _utils.handleCondition)(!!needCircleEnd, renderLine(height - PADDING_TOP_FLIGHT_ICON - SIZE_FLIGHT_ICON - GAP_8 * 3 - SIZE_CIRCLE), renderLine(height - PADDING_TOP_FLIGHT_ICON - SIZE_FLIGHT_ICON - GAP_8 * 2)), !!needCircleEnd && renderCircle()]
          })]
        });
      case _flightInfo.TypeTimelineHeader.ArrCircleMiddle:
        return (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: _flightInfo2.styles.centered,
          children: [renderLine(height), (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: [_flightInfo2.styles.centered, _flightInfo2.styles.absoluteUp],
            children: renderCircle()
          })]
        });
      case _flightInfo.TypeTimelineHeader.ArrDateTimeEnd:
        return (0, _jsxRuntime.jsxs)(_reactNative.View, {
          children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: _flightInfo2.styles.rowGap2,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: [_text.newPresets.smallTextBlackBold, _flightInfo2.styles.date],
              text: flightScheduledDate
            }), (0, _utils.handleCondition)(!!mainTime, (0, _jsxRuntime.jsxs)(_reactNative.View, {
              children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
                style: _flightInfo2.styles.timeRow,
                children: [(0, _jsxRuntime.jsx)(_text.Text, {
                  style: _text.newPresets.bodyTextBold,
                  text: mainTime
                }), (0, _jsxRuntime.jsx)(_text.Text, {
                  style: _flightInfo2.styles.dayAdded,
                  text: reTimeFlag && !!numberDaysDiff ? `${numberDaysDiff}` : ""
                })]
              }), reTimeFlag && (0, _jsxRuntime.jsx)(_text.Text, {
                style: _flightInfo2.styles.oldTime,
                text: getValue(scheduledTime, "N/A")
              })]
            }), null)]
          }), (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: {
              height: GAP_8
            }
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: _flightInfo2.styles.centered,
            children: [renderCircle(), renderLine(height - PADDING_TOP_FLIGHT_ICON - SIZE_FLIGHT_ICON - (gapEnd || 0))]
          })]
        });
      case _flightInfo.TypeTimelineHeader.ArrDateTimeStart:
        return (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: _flightInfo2.styles.rowGap2,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: [_text.newPresets.smallTextBlackBold, _flightInfo2.styles.date],
            text: flightScheduledDate
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
              style: _flightInfo2.styles.timeRow,
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                style: _text.newPresets.bodyTextBold,
                text: mainTime
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                style: _flightInfo2.styles.dayAdded,
                text: reTimeFlag && !!numberDaysDiff ? `${numberDaysDiff}` : ""
              })]
            }), reTimeFlag && (0, _jsxRuntime.jsx)(_text.Text, {
              style: _flightInfo2.styles.oldTime,
              text: getValue(scheduledTime, "N/A")
            })]
          })]
        });
      case _flightInfo.TypeTimelineHeader.ArrDateTimeMiddle:
        return (0, _jsxRuntime.jsxs)(_reactNative.View, {
          children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: _flightInfo2.styles.rowGap2,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: [_text.newPresets.smallTextBlackBold, _flightInfo2.styles.date],
              text: flightScheduledDate
            }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
              children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
                style: _flightInfo2.styles.timeRow,
                children: [(0, _jsxRuntime.jsx)(_text.Text, {
                  style: _text.newPresets.bodyTextBold,
                  text: mainTime
                }), (0, _jsxRuntime.jsx)(_text.Text, {
                  style: _flightInfo2.styles.dayAdded,
                  text: reTimeFlag && !!numberDaysDiff ? `${numberDaysDiff}` : ""
                })]
              }), reTimeFlag && (0, _jsxRuntime.jsx)(_text.Text, {
                style: _flightInfo2.styles.oldTime,
                text: getValue(scheduledTime, "N/A")
              })]
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: _flightInfo2.styles.centered,
            children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
              style: {
                height: GAP_8
              }
            }), renderCircle(), renderLine(height - PADDING_TOP_FLIGHT_ICON - SIZE_FLIGHT_ICON - GAP_8 * 3 - gapEnd)]
          })]
        });
      case _flightInfo.TypeTimelineHeader.DepDateTimeMiddle:
        return (0, _jsxRuntime.jsxs)(_reactNative.View, {
          children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: _flightInfo2.styles.rowGap2,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              testID: `dateDepDateTimeMiddle`,
              accessibilityLabel: `dateDepDateTimeMiddle`,
              style: [_text.newPresets.smallTextBlackBold, _flightInfo2.styles.date],
              text: flightScheduledDate
            }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
              children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
                style: _flightInfo2.styles.timeRow,
                children: [(0, _jsxRuntime.jsx)(_text.Text, {
                  testID: `mainTimeDepDateTimeMiddle`,
                  accessibilityLabel: `mainTimeDepDateTimeMiddle`,
                  style: _text.newPresets.bodyTextBold,
                  text: mainTime
                }), (0, _jsxRuntime.jsx)(_text.Text, {
                  testID: `dayAddedDepDateTimeMiddle`,
                  accessibilityLabel: `dayAddedDepDateTimeMiddle`,
                  style: _flightInfo2.styles.dayAdded,
                  text: reTimeFlag && !!numberDaysDiff ? `${numberDaysDiff}` : ""
                })]
              }), reTimeFlag && (0, _jsxRuntime.jsx)(_text.Text, {
                testID: `scheduledTimeDepDateTimeMiddle`,
                accessibilityLabel: `scheduledTimeDepDateTimeMiddle`,
                style: _flightInfo2.styles.oldTime,
                text: getValue(scheduledTime, "N/A")
              })]
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: _flightInfo2.styles.centered,
            children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
              style: {
                height: GAP_8
              }
            }), renderLine(isHasTransitDataAndDestinationEmpty ? height - GAP_8 * 3 : HEIGHT_TRANSIT_FOREIGN_FLIGHT - PADDING_TOP_FLIGHT_ICON - SIZE_FLIGHT_ICON - GAP_8 * 2 - SIZE_CIRCLE - gapEnd), renderCircle()]
          })]
        });
      default:
        return null;
    }
  };
  var _default = exports.default = TimelineHeaderItem;
