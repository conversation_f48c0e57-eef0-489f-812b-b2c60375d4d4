  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _flightInfo = _$$_REQUIRE(_dependencyMap[1]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _blur = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  var BlurViewFlightInfo = function BlurViewFlightInfo() {
    return (0, _jsxRuntime.jsx)(_blur.BlurView, {
      style: _flightInfo.styles.containerBlur,
      blurAmount: 8,
      overlayColor: _theme.color.palette.backgroundFlightTimeline
    });
  };
  var _default = exports.default = BlurViewFlightInfo;
