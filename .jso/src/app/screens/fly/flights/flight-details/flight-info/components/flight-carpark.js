  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[2]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _icons = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _utils = _$$_REQUIRE(_dependencyMap[7]);
  var _flightDetails = _$$_REQUIRE(_dependencyMap[8]);
  var _flightProps = _$$_REQUIRE(_dependencyMap[9]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  var FlightCarpark = function FlightCarpark(_ref) {
    var onPressCarpark = _ref.onPressCarpark,
      intoCityOrAirportPayload = _ref.intoCityOrAirportPayload,
      flightDetailsData = _ref.flightDetailsData;
    var _ref2 = intoCityOrAirportPayload || {},
      nearestCarpark = _ref2.link2;
    var _ref3 = flightDetailsData || {},
      terminal = _ref3.terminal,
      direction = _ref3.direction;
    var isT1 = Number(terminal) === 1;
    var isARR = direction === _flightProps.FlightDirection.arrival;
    var isDEP = direction === _flightProps.FlightDirection.departure;
    if (isT1 && isARR) {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.containerCarparkT1,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          tx: "flightDetailV2.flightInfo.carPark.title",
          style: styles.carparkTitle
        }), (0, _jsxRuntime.jsxs)(_reactNative2.TouchableOpacity, {
          style: styles.rowCarparkT1,
          onPress: function onPress() {
            return onPressCarpark(_flightDetails.TypeGetIntoAirport.LINK2);
          },
          disabled: !(nearestCarpark && nearestCarpark !== "N/A"),
          children: [(0, _jsxRuntime.jsx)(_icons.FlightParkingV2, {}), (0, _utils.handleCondition)(!!nearestCarpark && nearestCarpark !== "N/A", (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.containerCarparkIcon,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              text: nearestCarpark,
              style: styles.carparkLabel,
              testID: "Text_carPark",
              accessibilityLabel: "Text_carPark"
            }), (0, _jsxRuntime.jsx)(_icons.LocationV2, {
              width: 12,
              height: 12
            })]
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            text: '-',
            style: styles.carparkLabel,
            testID: "Text_carPark",
            accessibilityLabel: "Text_carPark"
          }))]
        })]
      });
    }
    if (isT1 && isDEP) {
      return (0, _jsxRuntime.jsxs)(_reactNative2.TouchableOpacity, {
        style: styles.containerCarpark,
        onPress: function onPress() {
          return onPressCarpark(_flightDetails.TypeGetIntoAirport.LINK2);
        },
        disabled: !(nearestCarpark && nearestCarpark !== "N/A"),
        children: [(0, _jsxRuntime.jsx)(_icons.FlightParkingV2, {}), (0, _utils.handleCondition)(!!nearestCarpark && nearestCarpark !== "N/A", (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.containerCarparkIcon,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            tx: "flightDetailV2.flightInfo.carPark.labelT1DEP",
            txOptions: {
              carpark: nearestCarpark
            },
            style: styles.carparkLabel,
            testID: "Text_carPark",
            accessibilityLabel: "Text_carPark"
          }), (0, _jsxRuntime.jsx)(_icons.LocationV2, {
            width: 12,
            height: 12
          })]
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          tx: "flightDetailV2.flightInfo.carPark.label",
          txOptions: {
            carpark: '-'
          },
          style: styles.carparkLabel,
          testID: "Text_carPark",
          accessibilityLabel: "Text_carPark"
        }))]
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative2.TouchableOpacity, {
      style: styles.containerCarpark,
      onPress: function onPress() {
        return onPressCarpark(_flightDetails.TypeGetIntoAirport.LINK2);
      },
      disabled: !(nearestCarpark && nearestCarpark !== "N/A"),
      children: [(0, _jsxRuntime.jsx)(_icons.FlightParkingV2, {}), (0, _utils.handleCondition)(!!nearestCarpark && nearestCarpark !== "N/A", (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.containerCarparkIcon,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          tx: "flightDetailV2.flightInfo.carPark.label",
          txOptions: {
            carpark: nearestCarpark
          },
          style: styles.carparkLabel,
          testID: "Text_carPark",
          accessibilityLabel: "Text_carPark"
        }), (0, _jsxRuntime.jsx)(_icons.LocationV2, {
          width: 12,
          height: 12
        })]
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        tx: "flightDetailV2.flightInfo.carPark.label",
        txOptions: {
          carpark: '-'
        },
        style: styles.carparkLabel,
        testID: "Text_carPark",
        accessibilityLabel: "Text_carPark"
      }))]
    });
  };
  var _default = exports.default = FlightCarpark;
  var styles = _reactNative.StyleSheet.create({
    containerCarparkT1: {
      padding: 8,
      paddingRight: 0,
      backgroundColor: _theme.color.palette.backgroundFlightCheckin,
      borderRadius: 8,
      rowGap: 4
    },
    carparkTitle: Object.assign({}, _text.newPresets.caption2Regular, {
      color: _theme.color.palette.midGrey
    }),
    rowCarparkT1: {
      flexDirection: "row",
      alignItems: 'center'
    },
    containerCarpark: {
      flexDirection: "row",
      padding: 8,
      paddingRight: 0,
      backgroundColor: _theme.color.palette.backgroundFlightCheckin,
      borderRadius: 8,
      alignItems: 'center'
    },
    containerCarparkIcon: {
      flexDirection: 'row',
      alignItems: 'center',
      maxWidth: '90%'
    },
    carparkLabel: Object.assign({}, _text.newPresets.caption2Regular, {
      color: _theme.color.palette.whiteGrey,
      marginHorizontal: 4
    })
  });
