  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[2]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _icons = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _utils = _$$_REQUIRE(_dependencyMap[7]);
  var _flightDetails = _$$_REQUIRE(_dependencyMap[8]);
  var _flightProps = _$$_REQUIRE(_dependencyMap[9]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  var FlightPickUpDropOff = function FlightPickUpDropOff(_ref) {
    var onPressPickupOrDropOff = _ref.onPressPickupOrDropOff,
      onPressAccessiblePickupOrDropOff = _ref.onPressAccessiblePickupOrDropOff,
      intoCityOrAirportPayload = _ref.intoCityOrAirportPayload,
      flightDetailsData = _ref.flightDetailsData;
    var _ref2 = intoCityOrAirportPayload || {},
      pickupOrDropOff = _ref2.link1,
      accessiblePickupOrDropOff = _ref2.link3;
    var _ref3 = flightDetailsData || {},
      direction = _ref3.direction;
    var hasPickUpDropOff = !!pickupOrDropOff && pickupOrDropOff !== "N/A";
    var hasAccessiblePickUpDropOff = !!accessiblePickupOrDropOff && accessiblePickupOrDropOff !== "N/A";
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.containerDropOff,
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        tx: (0, _utils.handleCondition)(direction === _flightProps.FlightDirection.departure, "flightDetailV2.flightInfo.dropOff.label", "flightDetailV2.flightInfo.pickUp.label"),
        style: styles.checkinLabel
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.row,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
          style: styles.pickupDropoffButton,
          disabled: !hasPickUpDropOff,
          onPress: function onPress() {
            return onPressPickupOrDropOff(_flightDetails.TypeGetIntoAirport.LINK1, (flightDetailsData == null ? undefined : flightDetailsData.dropOffDoor) || (intoCityOrAirportPayload == null ? undefined : intoCityOrAirportPayload.link1));
          },
          testID: "TouchableOpacity_pickupOrDropOff",
          accessibilityLabel: "TouchableOpacity_pickupOrDropOff",
          children: (0, _utils.handleCondition)(!!hasPickUpDropOff, (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.containerDropOffIcon,
            children: [(0, _jsxRuntime.jsx)(_icons.FlightDropOffV2, {
              width: 14,
              height: 14
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              text: pickupOrDropOff,
              style: styles.dropOffLabel,
              testID: "Text_pickupOrDropOff",
              accessibilityLabel: "Text_pickupOrDropOff"
            }), (0, _jsxRuntime.jsx)(_icons.LocationV2, {
              width: 12,
              height: 12
            })]
          }), (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
            children: [(0, _jsxRuntime.jsx)(_icons.FlightDropOffV2, {
              width: 14,
              height: 14
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              text: "-",
              style: styles.dropOffLabel,
              testID: "Text_pickupOrDropOff",
              accessibilityLabel: "Text_pickupOrDropOff"
            })]
          }))
        }), (0, _utils.handleCondition)(!hasPickUpDropOff && !hasAccessiblePickUpDropOff, null, (0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
          style: styles.pickupDropoffButton,
          disabled: !hasAccessiblePickUpDropOff,
          onPress: function onPress() {
            return onPressAccessiblePickupOrDropOff(_flightDetails.TypeGetIntoAirport.LINK3, intoCityOrAirportPayload == null ? undefined : intoCityOrAirportPayload.link3);
          },
          testID: "TouchableOpacity_accessiblePickupOrDropOff",
          accessibilityLabel: "TouchableOpacity_accessiblePickupOrDropOff",
          children: (0, _utils.handleCondition)(!!hasAccessiblePickUpDropOff, (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.containerDropOffIcon,
            children: [(0, _jsxRuntime.jsx)(_icons.FlightMobilityV2, {
              width: 14,
              height: 14
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              text: accessiblePickupOrDropOff,
              style: styles.dropOffLabel,
              testID: "Text_accessiblePickupOrDropOff",
              accessibilityLabel: "Text_accessiblePickupOrDropOff"
            }), (0, _jsxRuntime.jsx)(_icons.LocationV2, {
              width: 12,
              height: 12
            })]
          }), (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
            children: [(0, _jsxRuntime.jsx)(_icons.FlightMobilityV2, {
              width: 14,
              height: 14
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              text: "-",
              style: styles.dropOffLabel,
              testID: "Text_accessiblePickupOrDropOff",
              accessibilityLabel: "Text_accessiblePickupOrDropOff"
            })]
          }))
        }))]
      })]
    });
  };
  var _default = exports.default = FlightPickUpDropOff;
  var styles = _reactNative.StyleSheet.create({
    containerDropOff: {
      padding: 8,
      paddingRight: 0,
      backgroundColor: _theme.color.palette.backgroundFlightCheckin,
      borderRadius: 8,
      rowGap: 4
    },
    checkinLabel: Object.assign({}, _text.newPresets.caption2Regular, {
      color: _theme.color.palette.midGrey
    }),
    containerDropOffIcon: {
      flexDirection: 'row',
      alignItems: 'center',
      maxWidth: '90%',
      columnGap: 4
    },
    dropOffLabel: Object.assign({}, _text.newPresets.caption2Regular, {
      color: _theme.color.palette.whiteGrey
    }),
    row: {
      flexDirection: 'row',
      alignItems: 'center',
      columnGap: 8,
      flexWrap: 'wrap',
      rowGap: 4
    },
    pickupDropoffButton: {
      flexDirection: 'row',
      columnGap: 4,
      alignItems: 'center'
    }
  });
