  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _native = _$$_REQUIRE(_dependencyMap[8]);
  var _constants = _$$_REQUIRE(_dependencyMap[9]);
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var FlightFastCheckIn = function FlightFastCheckIn() {
    var navigation = (0, _native.useNavigation)();
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      modalFastCk = _useState2[0],
      setShowModal = _useState2[1];
    var onClosedSheet = (0, _react.useCallback)(function () {
      setShowModal(false);
    }, []);
    var showModal = (0, _react.useCallback)(function () {
      setShowModal(true);
    }, []);
    var navigateToWebView = (0, _react.useCallback)(function () {
      onClosedSheet();
      setTimeout(function () {
        navigation == null || navigation.navigate(_constants.NavigationConstants.webview, {
          uri: 'https://www.changiairport.com/en/fly/departure-guide/fast-check-in.html'
        });
      }, 200);
    }, [navigation, onClosedSheet]);
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.TouchableOpacity, {
        style: styles.containerFastCheckin,
        onPress: showModal,
        testID: "TouchableOpacity_containerFastCheckin",
        accessibilityLabel: "TouchableOpacity_containerFastCheckin",
        children: [(0, _jsxRuntime.jsx)(_icons.FastCheckinIcon, {
          width: 12,
          height: 12,
          color: _theme.color.palette.midGrey
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          tx: "flightDetailV2.flightInfo.flightInfoMoreDetail.fastCheckin",
          style: styles.fastCheckinLabel,
          testID: "Text_fastCheckin",
          accessibilityLabel: "Text_fastCheckin"
        }), (0, _jsxRuntime.jsx)(_icons.InfoMidGray, {
          width: 12,
          height: 12,
          color: _theme.color.palette.midGrey
        })]
      }), (0, _jsxRuntime.jsxs)(_bottomSheet.default, {
        isModalVisible: modalFastCk,
        onClosedSheet: onClosedSheet,
        modalStyle: styles.modalStyle,
        containerStyle: styles.bottomSheetStyle,
        stopDragCollapse: true,
        onBackPressHandle: onClosedSheet,
        animationInTiming: 200,
        animationOutTiming: 200,
        children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.headerFilter,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.titleSheet,
            tx: "flightDetails.fastCheckin.titleModal",
            testID: "Text_titleSheet",
            accessibilityLabel: "Text_titleSheet"
          }), (0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
            onPress: onClosedSheet,
            testID: "TouchableOpacity_headerFilter",
            accessibilityLabel: "TouchableOpacity_headerFilter",
            children: (0, _jsxRuntime.jsx)(_icons.CrossBlue, {
              width: 24,
              height: 24
            })
          })]
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.content,
          tx: "flightDetails.fastCheckin.content",
          testID: "Text_fastCheckinContent",
          accessibilityLabel: "Text_fastCheckinContent"
        }), (0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
          onPress: navigateToWebView,
          testID: "TouchableOpacity_buttonSeeMore",
          accessibilityLabel: "TouchableOpacity_buttonSeeMore",
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.txtButton,
            tx: "flightDetails.fastCheckin.buttonSeeMore",
            testID: "Text_buttonSeeMore",
            accessibilityLabel: "Text_buttonSeeMore"
          })
        })]
      })]
    });
  };
  var _default = exports.default = FlightFastCheckIn;
  var styles = _reactNative.StyleSheet.create({
    containerFastCheckin: {
      flexDirection: "row",
      alignItems: "center",
      columnGap: 2
    },
    fastCheckinLabel: Object.assign({}, _text.newPresets.caption2Regular, {
      color: _theme.color.palette.midGrey
    }),
    title: {
      fontFamily: _theme.typography.black,
      fontSize: 11,
      lineHeight: 14,
      fontWeight: '900',
      color: _theme.color.palette.almostBlackGrey,
      marginHorizontal: 2
    },
    modalStyle: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 24
    },
    bottomSheetStyle: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      padding: 24,
      overflow: "hidden",
      width: '100%'
    },
    headerFilter: {
      width: '100%',
      flexDirection: "row",
      alignItems: 'center',
      justifyContent: 'space-between'
    },
    titleSheet: Object.assign({}, _text.presets.subTitleBold, {
      color: _theme.color.palette.almostBlackGrey
    }),
    content: Object.assign({}, _text.presets.bodyTextRegular, {
      marginVertical: 16,
      color: _theme.color.palette.almostBlackGrey
    }),
    txtButton: Object.assign({}, _text.presets.textLink)
  });
