  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useOnlineCheckIn = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _constants = _$$_REQUIRE(_dependencyMap[2]);
  var _react = _$$_REQUIRE(_dependencyMap[3]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[4]);
  var _flyRedux = _$$_REQUIRE(_dependencyMap[5]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _request = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _envParams = _$$_REQUIRE(_dependencyMap[8]);
  var _awsAmplify = _$$_REQUIRE(_dependencyMap[9]);
  var _queries = _$$_REQUIRE(_dependencyMap[10]);
  var _systemRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var useOnlineCheckIn = exports.useOnlineCheckIn = function useOnlineCheckIn(props) {
    var direction = props.direction,
      flyItem = props.flyItem,
      navigation = props.navigation,
      refreshFlightDetails = props.refreshFlightDetails,
      setShowSaveFlightWhenOnlineCheckIn = props.setShowSaveFlightWhenOnlineCheckIn,
      scrollToTop = props.scrollToTop;
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var dispatch = (0, _reactRedux.useDispatch)();
    var handleRefreshDetails = function handleRefreshDetails(refreshAllData) {
      if (scrollToTop && refreshAllData) {
        scrollToTop();
      }
      if (refreshFlightDetails) {
        refreshFlightDetails(refreshAllData);
      }
    };
    var handleCheckInOnline = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* () {
        if (isLoggedIn) {
          var onlineCheckinData = yield checkOnlineCheckin == null ? undefined : checkOnlineCheckin();
          if (onlineCheckinData != null && onlineCheckinData.link) {
            navigation == null || navigation.navigate(_constants.NavigationConstants.webview, {
              uri: onlineCheckinData == null ? undefined : onlineCheckinData.link,
              screen: "flightDetails",
              refreshFlightDetails: handleRefreshDetails
            });
          }
          return;
        }
        dispatch(_flyRedux.FlyCreators.flyPendingCheckInOnline(true));
        // @ts-ignore
        navigation.navigate(_constants.NavigationConstants.authScreen, {
          sourceSystem: _constants.SOURCE_SYSTEM.ANYTHING_IN_APP
        });
      });
      return function handleCheckInOnline() {
        return _ref.apply(this, arguments);
      };
    }();
    var checkOnlineCheckin = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      try {
        var _env, _env2, _response$data;
        var response = yield (0, _request.default)({
          url: (_env = (0, _envParams.env)()) == null ? undefined : _env.APPSYNC_GRAPHQL_URL,
          method: "post",
          data: (0, _awsAmplify.graphqlOperation)(_queries.getOnlineCheckInQuery, {
            direction: direction,
            flightNumber: flyItem == null ? undefined : flyItem.flightNumber,
            scheduledDate: flyItem == null ? undefined : flyItem.scheduledDate
          }),
          parameters: {},
          headers: {
            "x-api-key": (_env2 = (0, _envParams.env)()) == null ? undefined : _env2.APPSYNC_GRAPHQL_API_KEY
          }
        });
        var onlineCheckinData = response == null || (_response$data = response.data) == null || (_response$data = _response$data.data) == null ? undefined : _response$data.getOnlineCheckin;
        if (onlineCheckinData != null && onlineCheckinData.link) {
          dispatch(_flyRedux.FlyCreators.getOnlineCheckinSuccess(onlineCheckinData));
          return onlineCheckinData;
        }
        dispatch(_flyRedux.FlyCreators.getOnlineCheckinFailure());
        dispatch(_systemRedux.default.setBottomSheetErrorData({
          visible: true
        }));
        return null;
      } catch (err) {
        dispatch(_flyRedux.FlyCreators.getOnlineCheckinFailure());
        return null;
      }
    }), [direction, JSON.stringify(flyItem)]);
    var handleSaveFlightWhenCheckInOnline = (0, _react.useCallback)(/*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* (isFlightSaved) {
        if (isLoggedIn) {
          if (isFlightSaved) {
            var onlineCheckinData = yield checkOnlineCheckin == null ? undefined : checkOnlineCheckin();
            if (onlineCheckinData != null && onlineCheckinData.link) {
              navigation == null || navigation.navigate(_constants.NavigationConstants.webview, {
                uri: onlineCheckinData == null ? undefined : onlineCheckinData.link,
                screen: "flightDetails",
                refreshFlightDetails: refreshFlightDetails
              });
            }
            return;
          }
          setShowSaveFlightWhenOnlineCheckIn(true);
          return;
        }
        dispatch(_flyRedux.FlyCreators.flyPendingCheckInOnline(true));
        // @ts-ignore
        navigation.navigate(_constants.NavigationConstants.authScreen, {
          sourceSystem: _constants.SOURCE_SYSTEM.ANYTHING_IN_APP
        });
      });
      return function (_x) {
        return _ref3.apply(this, arguments);
      };
    }(), [checkOnlineCheckin, isLoggedIn, navigation, refreshFlightDetails]);
    return {
      handleCheckInOnline: handleCheckInOnline,
      handleSaveFlightWhenCheckInOnline: handleSaveFlightWhenCheckInOnline
    };
  };
