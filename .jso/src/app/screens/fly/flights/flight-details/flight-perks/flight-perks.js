  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _i18n = _$$_REQUIRE(_dependencyMap[5]);
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[6]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _flyRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _orderBy = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _forEach = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _forYouRedux = _$$_REQUIRE(_dependencyMap[11]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[12]);
  var _perks = _$$_REQUIRE(_dependencyMap[13]);
  var _adobe = _$$_REQUIRE(_dependencyMap[14]);
  var _utils = _$$_REQUIRE(_dependencyMap[15]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[16]);
  var handleConditionShowPerks = function handleConditionShowPerks(_ref) {
    var listPerks = _ref.listPerks,
      isLoggedIn = _ref.isLoggedIn,
      rewardsData = _ref.rewardsData,
      flightDirection = _ref.flightDirection;
    var validPerks = [];
    (0, _forEach.default)(listPerks, function (e) {
      var _e$displayLocation, _e$displayLocation2;
      if ((e == null || (_e$displayLocation = e.displayLocation) == null || (_e$displayLocation = _e$displayLocation[0]) == null || (_e$displayLocation = _e$displayLocation.tagName) == null ? undefined : _e$displayLocation.toLowerCase()) === "grpb" || (e == null || (_e$displayLocation2 = e.displayLocation) == null || (_e$displayLocation2 = _e$displayLocation2[0]) == null || (_e$displayLocation2 = _e$displayLocation2.tagName) == null ? undefined : _e$displayLocation2.toLowerCase()) === "fly") {
        var _e$customerEligibilit, _e$customerEligibilit2, _e$customerEligibilit3;
        if ((e == null || (_e$customerEligibilit = e.customerEligibility) == null || (_e$customerEligibilit = _e$customerEligibilit[0]) == null ? undefined : _e$customerEligibilit.tagName) === "all" || (e == null || (_e$customerEligibilit2 = e.customerEligibility) == null || (_e$customerEligibilit2 = _e$customerEligibilit2[0]) == null ? undefined : _e$customerEligibilit2.tagName) === "flying_dep" && flightDirection === "DEP" || (e == null || (_e$customerEligibilit3 = e.customerEligibility) == null || (_e$customerEligibilit3 = _e$customerEligibilit3[0]) == null ? undefined : _e$customerEligibilit3.tagName) === "flying_arr" && flightDirection === "ARR") {
          var _e$memberEligibility, _e$memberEligibility2, _e$memberEligibility3, _rewardsData$reward2;
          if ((e == null || (_e$memberEligibility = e.memberEligibility) == null || (_e$memberEligibility = _e$memberEligibility[0]) == null ? undefined : _e$memberEligibility.tagName) === "member") {
            var _rewardsData$reward;
            if (isLoggedIn && (rewardsData == null || (_rewardsData$reward = rewardsData.reward) == null || (_rewardsData$reward = _rewardsData$reward.currentTierInfo) == null ? undefined : _rewardsData$reward.toLowerCase()) === "member") {
              validPerks.push(e);
            }
          }
          if ((e == null || (_e$memberEligibility2 = e.memberEligibility) == null || (_e$memberEligibility2 = _e$memberEligibility2[0]) == null ? undefined : _e$memberEligibility2.tagName) === "all" || (e == null || (_e$memberEligibility3 = e.memberEligibility) == null || (_e$memberEligibility3 = _e$memberEligibility3[0]) == null ? undefined : _e$memberEligibility3.tagName) === (rewardsData == null || (_rewardsData$reward2 = rewardsData.reward) == null || (_rewardsData$reward2 = _rewardsData$reward2.currentTierInfo) == null ? undefined : _rewardsData$reward2.toLowerCase())) {
            validPerks.push(e);
          }
        }
      }
    });
    return validPerks.slice(0, 10);
  };
  var FlightPerks = function FlightPerks(props) {
    var _props$testID = props.testID,
      testID = _props$testID === undefined ? "FlightPerks" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "FlightPerks" : _props$accessibilityL,
      _props$flightDetail = props.flightDetail,
      flightDetail = _props$flightDetail === undefined ? {} : _props$flightDetail,
      flightDirection = props.flightDirection,
      _props$isSavedFlight = props.isSavedFlight,
      isSavedFlight = _props$isSavedFlight === undefined ? false : _props$isSavedFlight;
    var flightDetailPerkFetching = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flightDetailPerkFetching);
    var flightDetailPerk = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flightDetailPerk);
    var rewardsData = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.rewardsData);
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var dataPerk = handleConditionShowPerks({
      listPerks: flightDetailPerk,
      isLoggedIn: isLoggedIn,
      rewardsData: rewardsData,
      flightDirection: flightDirection
    });
    var loadView = function loadView() {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.container,
        children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.textTitle,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            text: (0, _i18n.translate)("flightDetails.saveYourFlightForPerks"),
            preset: "h4",
            style: styles.textTitle
          }), (0, _jsxRuntime.jsxs)(_reactNative.ScrollView, {
            contentContainerStyle: styles.wrapLoadingItem,
            horizontal: true,
            showsHorizontalScrollIndicator: false,
            children: [(0, _jsxRuntime.jsx)(_perks.Perks, {
              isLoading: true
            }), (0, _jsxRuntime.jsx)(_reactNative.View, {
              style: styles.spaceBetweenItem
            }), (0, _jsxRuntime.jsx)(_perks.Perks, {
              isLoading: true
            })]
          })]
        })
      });
    };
    var defaultView = function defaultView(data) {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.container,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          text: (0, _i18n.translate)("flightDetails.saveYourFlightForPerks"),
          preset: "h4",
          style: styles.textTitle
        }), (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
          horizontal: true,
          data: (0, _orderBy.default)(data, ["sequenceNumber"], ["asc"]),
          renderItem: function renderItem(_ref2) {
            var item = _ref2.item,
              index = _ref2.index;
            return (0, _jsxRuntime.jsx)(_perks.Perks, {
              width: 327,
              isLoading: false,
              offerTitle: item.offerTitle,
              imageUrl: (0, _mediaHelper.handleImageUrl)(item.image),
              onPressed: function onPressed() {
                (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlyFlightDetailPerks, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlyFlightDetailPerks, `${item == null ? undefined : item.offerTitle}`));
              },
              testID: `${testID}__Locked__${index}`,
              accessibilityLabel: `${accessibilityLabel}__Locked__${index}`,
              tenantName: item == null ? undefined : item.tenantName,
              isLocked: !isSavedFlight,
              validTillDate: item == null ? undefined : item.subCopy,
              isValidTillDate: true,
              addButton: (item == null ? undefined : item.isRedeemable) && (item == null ? undefined : item.isRedeemable) === "Yes",
              ribbonText: item == null ? undefined : item.ribbonText,
              isDisabled: !isSavedFlight,
              index: index
            });
          },
          keyExtractor: function keyExtractor(_, index) {
            return `key_travelling_item_${index}`;
          },
          showsHorizontalScrollIndicator: false,
          testID: `${testID}__FlightPerksFlatList`,
          accessibilityLabel: `${accessibilityLabel}__FlightPerksFlatList`,
          contentContainerStyle: styles.wrapContentContainerStyle,
          ItemSeparatorComponent: function ItemSeparatorComponent() {
            return (0, _jsxRuntime.jsx)(_reactNative.View, {
              style: styles.spaceBetweenItem
            });
          }
        })]
      });
    };
    return flightDetailPerkFetching && !flightDetailPerk || !(flightDetail != null && flightDetail.flightNumber) ? loadView() : (0, _utils.handleCondition)(dataPerk && (dataPerk == null ? undefined : dataPerk.length) > 0, defaultView(dataPerk), null);
  };
  var styles = _reactNative.StyleSheet.create({
    container: {
      marginTop: 40,
      paddingLeft: 24
    },
    spaceBetweenItem: {
      width: 20
    },
    textTitle: {
      marginBottom: 10
    },
    wrapContentContainerStyle: {
      paddingBottom: 10,
      paddingRight: 20
    },
    wrapLoadingItem: {
      paddingBottom: 10,
      paddingRight: 20,
      paddingTop: 5
    }
  });
  var _default = exports.default = FlightPerks;
