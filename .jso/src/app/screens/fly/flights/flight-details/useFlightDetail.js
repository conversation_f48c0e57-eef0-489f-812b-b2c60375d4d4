  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useFlightDetail = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _$$_REQUIRE(_dependencyMap[3]);
  var _awsAmplify = _$$_REQUIRE(_dependencyMap[4]);
  var _request = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _envParams = _$$_REQUIRE(_dependencyMap[6]);
  var _queries = _$$_REQUIRE(_dependencyMap[7]);
  var _reactNativeFastImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _lodash = _$$_REQUIRE(_dependencyMap[9]);
  var useFlightDetail = exports.useFlightDetail = function useFlightDetail() {
    var _useState = (0, _react.useState)(null),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      errorData = _useState2[0],
      setErrorData = _useState2[1];
    var getDeeplinkShare = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* (input) {
        try {
          var _env, _env2, _response$data;
          var response = yield (0, _request.default)({
            url: (_env = (0, _envParams.env)()) == null ? undefined : _env.APPSYNC_GRAPHQL_URL,
            method: "post",
            data: (0, _awsAmplify.graphqlOperation)(_queries.getFlightDetailsDeeplink, {
              input: input
            }),
            parameters: {},
            headers: {
              "x-api-key": (_env2 = (0, _envParams.env)()) == null ? undefined : _env2.APPSYNC_GRAPHQL_API_KEY
            }
          });
          if (response != null && (_response$data = response.data) != null && (_response$data = _response$data.data) != null && (_response$data = _response$data.getReferralLink) != null && _response$data.shortLink) {
            return {
              success: true,
              data: response.data.data.getReferralLink.shortLink,
              error: null
            };
          } else {
            var error = response.data.errors || "API failed";
            setErrorData(error);
            return {
              success: false,
              data: null,
              error: error
            };
          }
        } catch (error) {
          setErrorData(error);
          return {
            success: false,
            data: null,
            error: error
          };
        }
      });
      return function getDeeplinkShare(_x) {
        return _ref.apply(this, arguments);
      };
    }();
    var getTravelChecklistAEM = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* (_ref2) {
        var flight_no = _ref2.flight_no,
          user_profile = _ref2.user_profile,
          direction = _ref2.direction,
          airport = _ref2.airport,
          airline = _ref2.airline,
          terminal = _ref2.terminal;
        try {
          var _env3;
          var response = yield (0, _request.default)({
            url: ((_env3 = (0, _envParams.env)()) == null ? undefined : _env3.AEM_URL) + `/bin/ichangi/fly/flight-personalization/travel-checklist.${flight_no != null ? flight_no : 'all'}.${user_profile}.${direction}.${airport != null ? airport : 'all'}.${airline != null ? airline : 'all'}.${terminal ? `T${terminal}` : 'all'}.model.json`,
            method: "get"
          });
          if (response != null && response.data) {
            return {
              success: true,
              data: response.data,
              error: null
            };
          } else {
            var error = response.data.errors || "API failed";
            setErrorData(error);
            return {
              success: false,
              data: null,
              error: error
            };
          }
        } catch (error) {
          setErrorData(error);
          return {
            success: false,
            data: null,
            error: error
          };
        }
      });
      return function getTravelChecklistAEM(_x2) {
        return _ref3.apply(this, arguments);
      };
    }();
    var getBannerAEM = /*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* () {
        try {
          var _env4, _response$data2;
          var response = yield (0, _request.default)({
            url: ((_env4 = (0, _envParams.env)()) == null ? undefined : _env4.AEM_URL) + `/bin/ichangi/fly/flight-personalization/travel-checklist-banner/v2`,
            method: "get"
          });
          if (response != null && (_response$data2 = response.data) != null && _response$data2.list) {
            var _response$data3, _response$data4;
            var bannerImages = response == null || (_response$data3 = response.data) == null || (_response$data3 = _response$data3.list) == null ? undefined : _response$data3.filter(function (item) {
              return item == null ? undefined : item.image;
            });
            if (!(0, _lodash.isEmpty)(bannerImages)) {
              _reactNativeFastImage.default.preload(bannerImages.map(function (item) {
                var _env5;
                return {
                  uri: `${(_env5 = (0, _envParams.env)()) == null ? undefined : _env5.AEM_URL}${item == null ? undefined : item.image}`
                };
              }));
            }
            return {
              success: true,
              data: (_response$data4 = response.data) == null ? undefined : _response$data4.list,
              error: null
            };
          } else {
            var error = response.data.errors || "API failed";
            setErrorData(error);
            return {
              success: false,
              data: null,
              error: error
            };
          }
        } catch (error) {
          setErrorData(error);
          return {
            success: false,
            data: null,
            error: error
          };
        }
      });
      return function getBannerAEM() {
        return _ref4.apply(this, arguments);
      };
    }();
    return {
      errorData: errorData,
      setErrorData: setErrorData,
      getDeeplinkShare: getDeeplinkShare,
      getTravelChecklistAEM: getTravelChecklistAEM,
      getBannerAEM: getBannerAEM
    };
  };
