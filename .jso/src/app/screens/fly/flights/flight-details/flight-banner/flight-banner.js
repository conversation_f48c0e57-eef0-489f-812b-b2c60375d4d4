  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[6]);
  var _error = _$$_REQUIRE(_dependencyMap[7]);
  var _loadingBanner = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _theme = _$$_REQUIRE(_dependencyMap[9]);
  var _sectionContentFragment = _$$_REQUIRE(_dependencyMap[10]);
  var _fly = _$$_REQUIRE(_dependencyMap[11]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[12]);
  var _type = _$$_REQUIRE(_dependencyMap[13]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[14]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var FlightBanner = function FlightBanner(_ref) {
    var _useContext;
    var bannerAEMResponse = _ref.bannerAEMResponse,
      isBannerAEMLoading = _ref.isBannerAEMLoading,
      onPressReloadBanner = _ref.onPressReloadBanner,
      isSaved = _ref.isSaved,
      onPressBanner = _ref.onPressBanner,
      flyFlightDetailsPayload = _ref.flyFlightDetailsPayload,
      insertFlightPayload = _ref.insertFlightPayload;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isError = _useState2[0],
      setIsError = _useState2[1];
    var success = bannerAEMResponse == null ? undefined : bannerAEMResponse.success;
    var _useContext$Handlers = (_useContext = (0, _react.useContext)(_fly.FLY_CONTEXT)) == null ? undefined : _useContext.Handlers,
      flyDetailsFirstFlag = _useContext$Handlers.flyDetailsFirstFlag;
    var isFlightDetailsFirst = (0, _remoteConfig.isFlagOnCondition)(flyDetailsFirstFlag);
    var getBannerDataByType = function getBannerDataByType(bannerType) {
      var _bannerAEMResponse$da;
      return bannerAEMResponse == null || (_bannerAEMResponse$da = bannerAEMResponse.data) == null ? undefined : _bannerAEMResponse$da.find(function (item) {
        var _item$type;
        return (item == null || (_item$type = item.type) == null ? undefined : _item$type.tagName) === bannerType;
      });
    };
    var bannerData = (0, _react.useMemo)(function () {
      var _flyFlightDetailsPayl;
      var _ref2 = (flyFlightDetailsPayload == null || (_flyFlightDetailsPayl = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl.flightInsurance) || {},
        bannerType = _ref2.bannerType;
      if (bannerType) {
        if (!isSaved) {
          return getBannerDataByType(_type.EnumBannerTypeAEM.NOT_SAVED);
        }
        return getBannerDataByType(_type.EnumBannerTypeAEM == null ? undefined : _type.EnumBannerTypeAEM[bannerType]);
      }
      return undefined;
    }, [bannerAEMResponse, isSaved, flyFlightDetailsPayload, insertFlightPayload]);
    var onPressToBanner = function onPressToBanner() {
      if (onPressBanner && typeof onPressBanner === 'function') {
        onPressBanner();
      }
    };
    var onError = function onError() {
      setIsError(true);
    };
    if (isBannerAEMLoading) return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.cardLinksContainer,
      children: (0, _jsxRuntime.jsx)(_loadingBanner.default, {})
    });
    if (!success) return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: _sectionContentFragment.containerErrorStyle,
      children: [isFlightDetailsFirst && (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.marginTop
      }), (0, _jsxRuntime.jsx)(_error.ErrorComponent, {
        type: _error.ErrorComponentType.flightCF,
        onPressed: onPressReloadBanner,
        flightCFContainerStyle: {
          marginHorizontal: isFlightDetailsFirst ? -12 : 0
        }
      })]
    });
    if (!(bannerData != null && bannerData.image) || isError) {
      if (isFlightDetailsFirst) {
        return (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.containerEmplyView
        });
      }
      return null;
    }
    return (0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
      style: isFlightDetailsFirst ? styles.cardLinksContainerWithoutShadow : styles.cardLinksContainer,
      activeOpacity: 0.5,
      onPress: onPressToBanner,
      children: (0, _jsxRuntime.jsx)(_baseImage.default, {
        source: {
          uri: (0, _mediaHelper.handleImageUrl)(bannerData == null ? undefined : bannerData.image)
        },
        style: [styles.imageStyle, {
          width: isFlightDetailsFirst ? width - 32 : width - 48
        }],
        resizeMode: "stretch",
        onError: onError
      })
    });
  };
  var _default = exports.default = FlightBanner;
  var styles = _reactNative.StyleSheet.create({
    cardLinksContainerWithoutShadow: {
      alignSelf: "center",
      marginVertical: 16,
      borderRadius: 16
    },
    cardLinksContainer: Object.assign({
      alignSelf: "center",
      marginVertical: 16,
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16
    }, _reactNative.Platform.select({
      android: {
        elevation: 3
      },
      ios: {
        shadowColor: _theme.color.palette.almostBlackGrey,
        shadowOffset: {
          width: 0,
          height: 6
        },
        shadowOpacity: 0.08,
        shadowRadius: 5
      }
    })),
    imageStyle: {
      borderRadius: 16,
      height: 0.25688073394495414 * (width - 48),
      // This formula based on *imageStyle* of component *FlyAppscapadeBanner*
      overflow: "hidden",
      width: width - 48
    },
    containerEmplyView: {
      marginVertical: 12
    },
    marginTop: {
      marginTop: 12
    }
  });
