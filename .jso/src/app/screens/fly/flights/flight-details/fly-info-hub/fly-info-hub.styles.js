  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var commonShadow = Object.assign({}, _reactNative.Platform.select({
    android: {
      elevation: 2
    },
    ios: {
      shadowColor: _theme.color.palette.almostBlackGrey,
      shadowOffset: {
        width: 0,
        height: 6
      },
      shadowOpacity: 0.08,
      shadowRadius: 5
    }
  }));
  var styles = exports.styles = _reactNative.StyleSheet.create({
    scrollContent: {
      columnGap: 12,
      marginBottom: 24,
      marginTop: 12,
      paddingHorizontal: 24
    },
    menuButton: Object.assign({
      padding: 8,
      paddingRight: 16,
      borderRadius: 12,
      backgroundColor: _theme.color.palette.whiteGrey,
      columnGap: 8,
      flexDirection: "row",
      alignItems: "center"
    }, commonShadow),
    menuButtonFocus: Object.assign({
      padding: 8,
      paddingRight: 16,
      borderRadius: 12,
      backgroundColor: _theme.color.palette.whiteGrey,
      columnGap: 8,
      flexDirection: "row",
      alignItems: "center",
      borderWidth: 2,
      borderColor: _theme.color.palette.gradientColor1Start
    }, commonShadow),
    menuText: Object.assign({}, _text.newPresets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey
    })
  });
