  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.FlightInformationHub = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _flyInfoHub = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _native = _$$_REQUIRE(_dependencyMap[6]);
  var _icons = _$$_REQUIRE(_dependencyMap[7]);
  var _flightDetail = _$$_REQUIRE(_dependencyMap[8]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[9]);
  var _constants = _$$_REQUIRE(_dependencyMap[10]);
  var _travelOptions = _$$_REQUIRE(_dependencyMap[11]);
  var _navigationType = _$$_REQUIRE(_dependencyMap[12]);
  var _reactNativeGestureHandler = _$$_REQUIRE(_dependencyMap[13]);
  var _fly = _$$_REQUIRE(_dependencyMap[14]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[15]);
  var _adobe = _$$_REQUIRE(_dependencyMap[16]);
  var _i18n = _$$_REQUIRE(_dependencyMap[17]);
  var _firebase = _$$_REQUIRE(_dependencyMap[18]);
  var _useFlightDetailClickEvent = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[19]));
  var _flightProps = _$$_REQUIRE(_dependencyMap[20]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[21]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[22]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var getInitialFilterData = function getInitialFilterData(_ref) {
    var terminal = _ref.terminal,
      isIAmPicking = _ref.isIAmPicking,
      navigationValue = _ref.navigationValue;
    var filterData = [];
    if (terminal) {
      filterData.push({
        filterType: "locations",
        tagName: `terminal ${terminal}`,
        tagTitle: `T${terminal}`
      });
    }
    if (navigationValue === _constants.NavigationInAppValueSpecial.shop) {
      filterData.push({
        filterType: "type",
        tagName: "shop",
        tagTitle: "Shop"
      });
    }
    if (navigationValue === _constants.NavigationInAppValueSpecial.dine) {
      filterData.push({
        filterType: "type",
        tagName: "dine",
        tagTitle: "Dine"
      });
    }
    if (isIAmPicking) {
      filterData.push({
        filterType: "areas",
        tagName: "public",
        tagTitle: "Public Area"
      });
    }
    return filterData;
  };
  var FlightInformationHub = exports.FlightInformationHub = function FlightInformationHub(_ref2) {
    var _useContext;
    var typeFocusInformationHub = _ref2.typeFocusInformationHub,
      setTypeFocusInformationHub = _ref2.setTypeFocusInformationHub,
      terminal = _ref2.terminal,
      selectedTravelOption = _ref2.selectedTravelOption,
      direction = _ref2.direction,
      flightNumber = _ref2.flightNumber;
    var navigation = (0, _native.useNavigation)();
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)(),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var _useContext$Handlers = (_useContext = (0, _react.useContext)(_fly.FLY_CONTEXT)) == null ? undefined : _useContext.Handlers,
      flyDetailsFirstFlag = _useContext$Handlers.flyDetailsFirstFlag;
    var isFlightDetailsFirst = (0, _remoteConfig.isFlagOnCondition)(flyDetailsFirstFlag);
    var isTravellerLocal = selectedTravelOption === _travelOptions.TravelOption.iAmTravelling;
    var isDepartureLocal = direction === _flightProps.FlightDirection.departure;
    var _useFlightDetailClick = (0, _useFlightDetailClickEvent.default)({
        isDeparture: isDepartureLocal,
        isTraveller: isTravellerLocal
      }),
      logClickEvent = _useFlightDetailClick.logClickEvent;
    var isIAmPicking = selectedTravelOption === _travelOptions.TravelOption.iAmPicking;
    var trackingModeValue = isIAmPicking ? _adobe.AdobeValueByTagName.FlightDetailsPagePickingSomeone : _adobe.AdobeValueByTagName.FlightDetailsPageTravelling;
    var isShopDineV2 = (0, _remoteConfig.getFeatureFlagInit)(_remoteConfig.REMOTE_CONFIG_FLAGS.SHOPDINE_V2);
    var travelTabLabelTx = "flightDetailV2.flightInformationHub.menu.travel";
    var shopTabLabelTx = "flightDetailV2.flightInformationHub.menu.shop";
    var dineTabLabelTx = "flightDetailV2.flightInformationHub.menu.dine";
    var facilitiesTabLabelTx = "flightDetailV2.flightInformationHub.menu.facilities";
    var onPressTravel = function onPressTravel() {
      setTypeFocusInformationHub(_flightDetail.TypeFocusInformationHub.TRAVEL);
      (0, _adobe.trackAction)(_adobe.AdobeTagName.FlyFlightDetailsPageBottomDetails, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.FlyFlightDetailsPageBottomDetails, `${flightNumber} | ${trackingModeValue} | ${direction} | ${(0, _i18n.translate)(travelTabLabelTx)} | Tabs`));
    };
    var onPressShopAndDine = function onPressShopAndDine(_ref3) {
      var navigationValue = _ref3.navigationValue;
      if (isShopDineV2) {
        var filterData = getInitialFilterData({
          terminal: terminal,
          isIAmPicking: isIAmPicking,
          navigationValue: navigationValue
        });
        navigation.navigate(_constants.NavigationConstants.DineShopDirectory, {
          filterData: filterData
        });
        return;
      }
      var filterTitles = [];
      if (terminal) {
        filterTitles.push({
          filterType: 'locations',
          tagName: `terminal ${terminal}`,
          tagTitle: `Terminal ${terminal}`
        });
      }
      if (isIAmPicking) {
        filterTitles.push({
          filterType: 'areas',
          tagName: 'public',
          tagTitle: 'Public'
        });
      }
      handleNavigation(_navigationType.NavigationTypeEnum.inScreen, navigationValue, undefined, {
        filters: filterTitles,
        isFocusRedirect: true
      });
    };
    var onPressFacilities = function onPressFacilities() {
      setTypeFocusInformationHub(_flightDetail.TypeFocusInformationHub.FACILITIES);
      logClickEvent(_firebase.ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME.FACILITIES_TAB);
      (0, _adobe.trackAction)(_adobe.AdobeTagName.FlyFlightDetailsPageBottomDetails, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.FlyFlightDetailsPageBottomDetails, `${flightNumber} | ${trackingModeValue} | ${direction} | ${(0, _i18n.translate)(facilitiesTabLabelTx)} | Tabs`));
    };
    var onPressShopTab = function onPressShopTab() {
      onPressShopAndDine({
        navigationValue: _constants.NavigationInAppValueSpecial.shop
      });
      logClickEvent(_firebase.ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME.SHOP_TAB);
      (0, _adobe.trackAction)(_adobe.AdobeTagName.FlyFlightDetailsPageBottomDetails, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.FlyFlightDetailsPageBottomDetails, `${flightNumber} | ${trackingModeValue} | ${direction} | ${(0, _i18n.translate)(shopTabLabelTx)} | Tabs`));
    };
    var onPressDineTab = function onPressDineTab() {
      onPressShopAndDine({
        navigationValue: _constants.NavigationInAppValueSpecial.dine
      });
      logClickEvent(_firebase.ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME.DINE_TAB);
      (0, _adobe.trackAction)(_adobe.AdobeTagName.FlyFlightDetailsPageBottomDetails, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.FlyFlightDetailsPageBottomDetails, `${flightNumber} | ${trackingModeValue} | ${direction} | ${(0, _i18n.translate)(dineTabLabelTx)} | Tabs`));
    };
    return (0, _jsxRuntime.jsxs)(_reactNativeGestureHandler.ScrollView, {
      horizontal: true,
      nestedScrollEnabled: true,
      contentContainerStyle: [_flyInfoHub.styles.scrollContent, {
        paddingHorizontal: isFlightDetailsFirst ? 16 : 24
      }],
      showsHorizontalScrollIndicator: false,
      children: [(0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: typeFocusInformationHub === _flightDetail.TypeFocusInformationHub.TRAVEL ? _flyInfoHub.styles.menuButtonFocus : _flyInfoHub.styles.menuButton,
        onPress: onPressTravel,
        children: [(0, _jsxRuntime.jsx)(_icons.FlightTravelIcon, {}), (0, _jsxRuntime.jsx)(_text.Text, {
          tx: travelTabLabelTx,
          style: _flyInfoHub.styles.menuText
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: _flyInfoHub.styles.menuButton,
        onPress: onPressShopTab,
        children: [(0, _jsxRuntime.jsx)(_icons.FlightShoppingBag, {}), (0, _jsxRuntime.jsx)(_text.Text, {
          tx: shopTabLabelTx,
          style: _flyInfoHub.styles.menuText
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: _flyInfoHub.styles.menuButton,
        onPress: onPressDineTab,
        children: [(0, _jsxRuntime.jsx)(_icons.FlightDineIcon, {}), (0, _jsxRuntime.jsx)(_text.Text, {
          tx: dineTabLabelTx,
          style: _flyInfoHub.styles.menuText
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: typeFocusInformationHub === _flightDetail.TypeFocusInformationHub.FACILITIES ? _flyInfoHub.styles.menuButtonFocus : _flyInfoHub.styles.menuButton,
        onPress: onPressFacilities,
        children: [(0, _jsxRuntime.jsx)(_icons.FlightFacilitiesIcon, {}), (0, _jsxRuntime.jsx)(_text.Text, {
          tx: "flightDetailV2.flightInformationHub.menu.facilities",
          style: _flyInfoHub.styles.menuText
        })]
      })]
    });
  };
