  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.FacilitiesTab = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[0]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _facilitiesTab = _$$_REQUIRE(_dependencyMap[2]);
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _native = _$$_REQUIRE(_dependencyMap[4]);
  var _i18n = _$$_REQUIRE(_dependencyMap[5]);
  var _attractionsFacilitiesServices = _$$_REQUIRE(_dependencyMap[6]);
  var _exploreItemType = _$$_REQUIRE(_dependencyMap[7]);
  var _button = _$$_REQUIRE(_dependencyMap[8]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[9]);
  var _constants = _$$_REQUIRE(_dependencyMap[10]);
  var _errorCloudV = _$$_REQUIRE(_dependencyMap[11]);
  var _fly = _$$_REQUIRE(_dependencyMap[12]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[13]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[14]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var FacilitiesTab = exports.FacilitiesTab = function FacilitiesTab(_ref) {
    var _useContext;
    var facilitiesServicesData = _ref.facilitiesServicesData,
      loading = _ref.loading,
      testID = _ref.testID,
      accessibilityLabel = _ref.accessibilityLabel,
      terminal = _ref.terminal,
      onPressItem = _ref.onPressItem,
      onPressAll = _ref.onPressAll,
      selectedTravelOption = _ref.selectedTravelOption,
      onPressReloadFacilities = _ref.onPressReloadFacilities,
      facilitiesError = _ref.facilitiesError;
    var navigation = (0, _native.useNavigation)();
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("FLIGHT_LANDING_FACILITIES_SERVICES"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var _useContext$Handlers = (_useContext = (0, _react.useContext)(_fly.FLY_CONTEXT)) == null ? undefined : _useContext.Handlers,
      flyDetailsFirstFlag = _useContext$Handlers.flyDetailsFirstFlag,
      flyLandingFeatureFlag = _useContext$Handlers.flyLandingFeatureFlag;
    var isFlightDetailsFirst = (0, _remoteConfig.isFlagOnCondition)(flyDetailsFirstFlag);
    var bottomList = function bottomList() {
      return (0, _jsxRuntime.jsx)(_button.Button, {
        textStyle: _facilitiesTab.styles.textBottomListButton,
        style: [_facilitiesTab.styles.styleBottomListButton, {
          marginHorizontal: isFlightDetailsFirst ? 16 : 0
        }],
        tx: "flightDetailV2.facilitiesTab.seeAll",
        testID: `${testID}__ButtonViewAllFacilitiesAndServices`,
        accessibilityLabel: `${accessibilityLabel}__ButtonViewAllFacilitiesAndServices`,
        onPress: function onPress() {
          if (onPressAll) {
            onPressAll();
          }
          var isFlyLandingV2 = (0, _remoteConfig.getFeatureFlagInit)(_remoteConfig.REMOTE_CONFIG_FLAGS.FLY_LANDING, flyLandingFeatureFlag);
          if (isFlyLandingV2) {
            // @ts-ignore
            navigation.navigate(_constants.NavigationConstants.facilitiesServices);
          } else {
            // @ts-ignore
            navigation.popTo('bottomNavigation', {
              screen: 'fly',
              params: {
                screen: 'airport',
                params: {
                  moveToElement: "facilitiesServices",
                  rand: Math.random(),
                  terminal: terminal,
                  selectedTravelOption: selectedTravelOption
                }
              }
            });
          }
        }
      });
    };
    var onPress = function onPress(navigate) {
      if (!navigate) {
        return;
      }
      var _ref2 = (navigate == null ? undefined : navigate.navigation) || "",
        type = _ref2.type,
        value = _ref2.value;
      var _ref3 = navigate || {},
        redirect = _ref3.redirect;
      handleNavigation(type, value, redirect);
    };
    var renderTitle = function renderTitle() {
      if (!terminal) {
        return (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: _facilitiesTab.styles.containerTitle,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            text: (0, _i18n.translate)("flightDetailV2.facilitiesTab.allTerminal").toUpperCase(),
            preset: "caption2Bold",
            style: _facilitiesTab.styles.textAlmostBackColor
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            tx: "flightDetailV2.facilitiesTab.descAllTerminal",
            preset: "bodyTextRegular",
            style: _facilitiesTab.styles.descAllTerminalLabel
          })]
        });
      }
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _facilitiesTab.styles.containerTitle,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          text: (0, _i18n.translate)("flightDetailV2.facilitiesTab.titleWithTerminal", {
            terminal: terminal
          }).toUpperCase(),
          preset: "caption2Bold",
          style: _facilitiesTab.styles.textAlmostBackColor
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          tx: "flightDetailV2.facilitiesTab.descWithTerminal",
          preset: "bodyTextRegular",
          style: _facilitiesTab.styles.descAllTerminalLabel
        })]
      });
    };
    if (!!facilitiesError) {
      return (0, _jsxRuntime.jsx)(_errorCloudV.ErrorCloudComponentV2, {
        title: (0, _i18n.translate)("screenError.oop"),
        content: (0, _i18n.translate)("screenError.somethingWrong"),
        buttonText: (0, _i18n.translate)("screenError.reload"),
        onPress: onPressReloadFacilities
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: _facilitiesTab.styles.container,
      children: [renderTitle(), (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
        data: !loading ? facilitiesServicesData : _constants.SHIMMER_FLATLIST_DATA,
        renderItem: function renderItem(_ref4) {
          var item = _ref4.item,
            index = _ref4.index;
          return (0, _jsxRuntime.jsx)(_attractionsFacilitiesServices.AttractionsFacilitiesServices, {
            type: loading ? _exploreItemType.ExploreItemTypeEnum.loading : _exploreItemType.ExploreItemTypeEnum.default,
            title: item == null ? undefined : item.title,
            imageUrl: item == null ? undefined : item.image_url,
            attractionId: undefined,
            locationDisplayText: item == null ? undefined : item.locationText,
            onPressed: function onPressed() {
              if (onPressItem) {
                onPressItem(item);
              }
              onPress(item);
            },
            testID: `${testID}__AttractionsFacilitiesServices__${index}`,
            accessibilityLabel: `${accessibilityLabel}__AttractionsFacilitiesServices__${index}`,
            isFlex1Fixed: true
          });
        },
        keyExtractor: function keyExtractor(_, index) {
          return `key_travelling_item_${index}`;
        },
        numColumns: 2,
        style: _facilitiesTab.styles.flatListStyle,
        contentContainerStyle: [_facilitiesTab.styles.contentContainerStyle, {
          marginHorizontal: isFlightDetailsFirst ? 0 : 16,
          paddingHorizontal: isFlightDetailsFirst ? 8 : 0
        }],
        scrollEnabled: false,
        ListFooterComponent: bottomList,
        ListFooterComponentStyle: _facilitiesTab.styles.footerComponentStyle,
        testID: `${testID}__FlatListFacilitiesAndServices`,
        accessibilityLabel: `${accessibilityLabel}__FlatListFacilitiesAndServices`
      })]
    });
  };
