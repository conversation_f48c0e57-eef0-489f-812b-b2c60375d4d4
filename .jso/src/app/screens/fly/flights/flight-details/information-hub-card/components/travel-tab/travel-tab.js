  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.TravelTab = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _sectionContentFragment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _travelTab = _$$_REQUIRE(_dependencyMap[4]);
  var _constants = _$$_REQUIRE(_dependencyMap[5]);
  var _i18n = _$$_REQUIRE(_dependencyMap[6]);
  var _errorCloudV = _$$_REQUIRE(_dependencyMap[7]);
  var _loadingBanner = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _fly = _$$_REQUIRE(_dependencyMap[9]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[10]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var cardLinksContainer = {
    alignSelf: "center",
    marginBottom: 12
  };
  var TravelTab = exports.TravelTab = function TravelTab(_ref) {
    var _useContext;
    var direction = _ref.direction,
      handleMap = _ref.handleMap,
      travelChecklistAEM = _ref.travelChecklistAEM,
      flightDetailSectionData = _ref.flightDetailSectionData,
      flyFlightDetailsPayload = _ref.flyFlightDetailsPayload,
      flyFlightDetailsError = _ref.flyFlightDetailsError,
      selectedTopTravelOption = _ref.selectedTopTravelOption,
      isFlightSaved = _ref.isFlightSaved,
      enableEciDynamicDisplay = _ref.enableEciDynamicDisplay,
      onPressFlightCardLinks = _ref.onPressFlightCardLinks,
      onPressReloadTravelAEM = _ref.onPressReloadTravelAEM,
      isTravelChecklistAEMLoading = _ref.isTravelChecklistAEMLoading,
      onPressReloadFlightDetails = _ref.onPressReloadFlightDetails,
      disableSaveFlight = _ref.disableSaveFlight,
      onSaveFlight = _ref.onSaveFlight,
      saveFlightWhenCheckInOnline = _ref.saveFlightWhenCheckInOnline;
    var _useContext$Handlers = (_useContext = (0, _react.useContext)(_fly.FLY_CONTEXT)) == null ? undefined : _useContext.Handlers,
      flyDetailsFirstFlag = _useContext$Handlers.flyDetailsFirstFlag;
    var isFlightDetailsFirst = (0, _remoteConfig.isFlagOnCondition)(flyDetailsFirstFlag);
    var renderContent = function renderContent() {
      if (isTravelChecklistAEMLoading) {
        return (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
          data: _constants.SHIMMER_TRAVEL_LIST_DATA,
          renderItem: function renderItem(_ref2) {
            var item = _ref2.item,
              index = _ref2.index;
            return (0, _jsxRuntime.jsx)(_reactNative.View, {
              style: cardLinksContainer,
              children: (0, _jsxRuntime.jsx)(_loadingBanner.default, {})
            });
          },
          keyExtractor: function keyExtractor(_, index) {
            return `key_travelling_item_${index}`;
          },
          numColumns: 1,
          style: _travelTab.styles.flatListStyle,
          scrollEnabled: false
        });
      }
      if (travelChecklistAEM != null && travelChecklistAEM.error) {
        return (0, _jsxRuntime.jsx)(_errorCloudV.ErrorCloudComponentV2, {
          title: (0, _i18n.translate)("screenError.oop"),
          content: (0, _i18n.translate)("screenError.somethingWrong"),
          buttonText: (0, _i18n.translate)("screenError.reload"),
          onPress: onPressReloadTravelAEM
        });
      }

      // case call api get flight detail error in the first time
      if (!flightDetailSectionData && !!flyFlightDetailsError) {
        return (0, _jsxRuntime.jsx)(_errorCloudV.ErrorCloudComponentV2, {
          title: (0, _i18n.translate)("screenError.oop"),
          content: (0, _i18n.translate)("screenError.somethingWrong"),
          buttonText: (0, _i18n.translate)("screenError.reload"),
          onPress: onPressReloadTravelAEM
        });
      }
      return (0, _jsxRuntime.jsx)(_sectionContentFragment.default, {
        data: flightDetailSectionData,
        flyFlightDetailsPayload: flyFlightDetailsPayload,
        flyFlightDetailsError: flyFlightDetailsError,
        direction: direction,
        selectedTravelOption: selectedTopTravelOption,
        isFlightSaved: isFlightSaved,
        enableEciDynamicDisplay: enableEciDynamicDisplay,
        handleMap: handleMap,
        onPressFlightCardLinks: onPressFlightCardLinks,
        onPressReloadFlightDetails: onPressReloadFlightDetails,
        isFlightDetailsFirst: isFlightDetailsFirst,
        disableSaveFlight: disableSaveFlight,
        onSaveFlight: onSaveFlight,
        saveFlightWhenCheckInOnline: saveFlightWhenCheckInOnline
      });
    };
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: {
        minHeight: isFlightDetailsFirst ? _reactNative.Dimensions.get("screen").height * 0.6 : undefined
      },
      children: renderContent()
    });
  };
