  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.InformationHubCard = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _flyInfoHub = _$$_REQUIRE(_dependencyMap[5]);
  var _flightDetail = _$$_REQUIRE(_dependencyMap[6]);
  var _informationHubCard = _$$_REQUIRE(_dependencyMap[7]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[8]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[9]);
  var _aemRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[10]));
  var _utils = _$$_REQUIRE(_dependencyMap[11]);
  var _useInformationHubCard = _$$_REQUIRE(_dependencyMap[12]);
  var _adobe = _$$_REQUIRE(_dependencyMap[13]);
  var _flightBanner = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _travelTab = _$$_REQUIRE(_dependencyMap[15]);
  var _facilitiesTab = _$$_REQUIRE(_dependencyMap[16]);
  var _useFlightDetail2 = _$$_REQUIRE(_dependencyMap[17]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[18]);
  var _fly = _$$_REQUIRE(_dependencyMap[19]);
  var _i18n = _$$_REQUIRE(_dependencyMap[20]);
  var _travelOptions = _$$_REQUIRE(_dependencyMap[21]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[22]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var InformationHubCard = exports.InformationHubCard = function InformationHubCard(_ref) {
    var _useContext, _flyFlightDetailsPayl2, _flyFlightDetailsPayl3, _flyFlightDetailsPayl5, _flyFlightDetailsPayl6, _flyFlightDetailsPayl7;
    var direction = _ref.direction,
      handleMap = _ref.handleMap,
      travelChecklistAEM = _ref.travelChecklistAEM,
      flightDetailSectionData = _ref.flightDetailSectionData,
      flyFlightDetailsPayload = _ref.flyFlightDetailsPayload,
      flyFlightDetailsError = _ref.flyFlightDetailsError,
      selectedTopTravelOption = _ref.selectedTopTravelOption,
      isFlightSaved = _ref.isFlightSaved,
      enableEciDynamicDisplay = _ref.enableEciDynamicDisplay,
      onPressFlightCardLinks = _ref.onPressFlightCardLinks,
      customerEligibility = _ref.customerEligibility,
      onPressReloadTravelAEM = _ref.onPressReloadTravelAEM,
      isTravelChecklistAEMLoading = _ref.isTravelChecklistAEMLoading,
      onPressReloadFlightDetails = _ref.onPressReloadFlightDetails,
      disableSaveFlight = _ref.disableSaveFlight,
      onSaveFlight = _ref.onSaveFlight,
      saveFlightWhenCheckInOnline = _ref.saveFlightWhenCheckInOnline;
    var _useContext$Handlers = (_useContext = (0, _react.useContext)(_fly.FLY_CONTEXT)) == null ? undefined : _useContext.Handlers,
      flyDetailsFirstFlag = _useContext$Handlers.flyDetailsFirstFlag;
    var isFlightDetailsFirst = (0, _remoteConfig.isFlagOnCondition)(flyDetailsFirstFlag);
    var _useState = (0, _react.useState)(null),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      bannerAEMResponse = _useState2[0],
      setBannerAEMResponse = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isBannerAEMLoading = _useState4[0],
      setIsBannerAEMLoading = _useState4[1];
    var _useState5 = (0, _react.useState)(_flightDetail.TypeFocusInformationHub.TRAVEL),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      typeFocusInformationHub = _useState6[0],
      setTypeFocusInformationHub = _useState6[1];
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useInformationHubCar = (0, _useInformationHubCard.useInformationHubCard)(),
      filterDataFacilitiesServices = _useInformationHubCar.filterDataFacilitiesServices;
    var _useFlightDetail = (0, _useFlightDetail2.useFlightDetail)(),
      getBannerAEM = _useFlightDetail.getBannerAEM;
    var handleGetBannerAEM = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        setIsBannerAEMLoading(true);
        var res = yield getBannerAEM();
        setBannerAEMResponse(res);
        setIsBannerAEMLoading(false);
      });
      return function handleGetBannerAEM() {
        return _ref2.apply(this, arguments);
      };
    }();
    var facilitiesServicesState = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.FACILITIES_SERVICES));
    var facilitiesServicesData = (0, _react.useMemo)(function () {
      var _flyFlightDetailsPayl;
      return filterDataFacilitiesServices({
        listDataTranform: facilitiesServicesState == null ? undefined : facilitiesServicesState.data,
        customerEligibility: customerEligibility,
        terminal: flyFlightDetailsPayload == null || (_flyFlightDetailsPayl = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl.displayTerminal,
        selectedTravelOption: selectedTopTravelOption
      });
    }, [customerEligibility, facilitiesServicesState, flyFlightDetailsPayload == null || (_flyFlightDetailsPayl2 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl2.displayTerminal, selectedTopTravelOption]);
    var loading = facilitiesServicesState == null ? undefined : facilitiesServicesState.loading;
    var facilitiesError = facilitiesServicesState == null ? undefined : facilitiesServicesState.error;
    var flightNumberValue = flyFlightDetailsPayload == null || (_flyFlightDetailsPayl3 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl3.flightNumber;
    var facilitiesTabLabelTitle = (0, _i18n.translate)("flightDetailV2.flightInformationHub.menu.facilities");
    var isTraveller = selectedTopTravelOption === _travelOptions.TravelOption.iAmTravelling;
    var trackingModeValue = isTraveller ? _adobe.AdobeValueByTagName.FlightDetailsPageTravelling : _adobe.AdobeValueByTagName.FlightDetailsPagePickingSomeone;
    var callBackAfterSuccess = (0, _react.useCallback)(function (data) {
      var _data$list;
      // Tranform data
      var listDataTranform = data == null || (_data$list = data.list) == null ? undefined : _data$list.map(function (element) {
        return Object.assign({}, element, {
          locationText: element == null ? undefined : element.locationDisplayText,
          image_url: (0, _utils.mappingUrlAem)(element == null ? undefined : element.image)
        });
      });
      return listDataTranform;
    }, []);
    var handleGetAemConfigData = function handleGetAemConfigData() {
      var needReset = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;
      if (!!needReset) {
        dispatch(_aemRedux.default.clearAemConfigData(_aemRedux.AEM_PAGE_NAME.FACILITIES_SERVICES));
      }
      dispatch(_aemRedux.default.getAemConfigData({
        name: _aemRedux.AEM_PAGE_NAME.FACILITIES_SERVICES,
        pathName: "getFacilitiesServices",
        callBackAfterSuccess: callBackAfterSuccess
      }));
    };
    var onPressFacilityItem = function onPressFacilityItem(facilityItem) {
      var facilityItemTitle = (facilityItem == null ? undefined : facilityItem.title) || "";
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlyFlightDetailFacilitiesServices, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlyFlightDetailFacilitiesServices, facilityItemTitle));
      (0, _adobe.trackAction)(_adobe.AdobeTagName.FlyFlightDetailsPageBottomDetails, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.FlyFlightDetailsPageBottomDetails, `${flightNumberValue} | ${trackingModeValue} | ${direction} | ${facilitiesTabLabelTitle} | ${facilityItemTitle}`));
    };
    var onPressAllFacilities = function onPressAllFacilities() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlyFlightDetailFacilitiesServicesViewAll, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlyFlightDetailFacilitiesServicesViewAll, "1"));
      (0, _adobe.trackAction)(_adobe.AdobeTagName.FlyFlightDetailsPageBottomDetails, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.FlyFlightDetailsPageBottomDetails, `${flightNumberValue} | ${trackingModeValue} | ${direction} | ${facilitiesTabLabelTitle} | See all`));
    };
    (0, _react.useEffect)(function () {
      handleGetBannerAEM();
      handleGetAemConfigData();
      return function () {
        dispatch(_aemRedux.default.clearAemConfigData(_aemRedux.AEM_PAGE_NAME.FACILITIES_SERVICES));
      };
    }, []);
    var renderTabs = function renderTabs() {
      var _flyFlightDetailsPayl4;
      switch (typeFocusInformationHub) {
        case _flightDetail.TypeFocusInformationHub.TRAVEL:
          return (0, _jsxRuntime.jsx)(_travelTab.TravelTab, {
            direction: direction,
            handleMap: handleMap,
            travelChecklistAEM: travelChecklistAEM,
            flightDetailSectionData: flightDetailSectionData,
            flyFlightDetailsPayload: flyFlightDetailsPayload,
            flyFlightDetailsError: flyFlightDetailsError,
            selectedTopTravelOption: selectedTopTravelOption,
            isFlightSaved: isFlightSaved,
            enableEciDynamicDisplay: enableEciDynamicDisplay,
            onPressFlightCardLinks: onPressFlightCardLinks,
            onPressReloadTravelAEM: onPressReloadTravelAEM,
            onPressReloadFlightDetails: onPressReloadFlightDetails,
            isTravelChecklistAEMLoading: isTravelChecklistAEMLoading,
            disableSaveFlight: disableSaveFlight,
            onSaveFlight: onSaveFlight,
            saveFlightWhenCheckInOnline: saveFlightWhenCheckInOnline
          });
        case _flightDetail.TypeFocusInformationHub.FACILITIES:
          return (0, _jsxRuntime.jsx)(_facilitiesTab.FacilitiesTab, {
            facilitiesServicesData: facilitiesServicesData,
            loading: loading,
            facilitiesError: facilitiesError,
            testID: "FacilitiesAndServices",
            accessibilityLabel: "FacilitiesAndServices",
            terminal: flyFlightDetailsPayload == null || (_flyFlightDetailsPayl4 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl4.displayTerminal,
            selectedTravelOption: selectedTopTravelOption,
            onPressItem: onPressFacilityItem,
            onPressAll: onPressAllFacilities,
            onPressReloadFacilities: function onPressReloadFacilities() {
              return handleGetAemConfigData(true);
            }
          });
        default:
          return null;
      }
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: _informationHubCard.styles.container,
      children: [!isFlightDetailsFirst && (0, _jsxRuntime.jsx)(_flightBanner.default, {
        bannerAEMResponse: bannerAEMResponse,
        isBannerAEMLoading: isBannerAEMLoading,
        onPressReloadBanner: handleGetBannerAEM
      }), (0, _jsxRuntime.jsx)(_flyInfoHub.FlightInformationHub, {
        typeFocusInformationHub: typeFocusInformationHub,
        setTypeFocusInformationHub: setTypeFocusInformationHub,
        terminal: flyFlightDetailsPayload == null || (_flyFlightDetailsPayl5 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl5.displayTerminal,
        direction: flyFlightDetailsPayload == null || (_flyFlightDetailsPayl6 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl6.direction,
        flightNumber: flyFlightDetailsPayload == null || (_flyFlightDetailsPayl7 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl7.flightNumber,
        selectedTravelOption: selectedTopTravelOption
      }), renderTabs()]
    });
  };
