  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = exports.containerErrorStyle = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNativeSizeMatters = _$$_REQUIRE(_dependencyMap[2]);
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    container: Object.assign({
      borderRadius: 16,
      width: width - (0, _reactNativeSizeMatters.scale)(45),
      marginBottom: 12,
      alignSelf: "center",
      padding: 16,
      backgroundColor: _theme.color.palette.whiteGrey
    }, _reactNative.Platform.select({
      ios: {
        shadowRadius: 16,
        shadowOpacity: 0.08,
        shadowOffset: {
          width: 0,
          height: 3
        },
        backgroundColor: _theme.color.palette.whiteGrey
      },
      android: {
        elevation: 3,
        backgroundColor: _theme.color.palette.whiteGrey
      }
    }))
  });
  var containerErrorStyle = exports.containerErrorStyle = Object.assign({}, styles.container, {
    padding: 0,
    backgroundColor: _theme.color.palette.transparent
  });
