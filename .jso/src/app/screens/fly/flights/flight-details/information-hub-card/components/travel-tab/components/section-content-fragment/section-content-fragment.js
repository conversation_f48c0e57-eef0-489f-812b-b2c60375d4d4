  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _cmsFlightCard = _$$_REQUIRE(_dependencyMap[4]);
  var _sectionContentFragment = _$$_REQUIRE(_dependencyMap[5]);
  var _flightProps = _$$_REQUIRE(_dependencyMap[6]);
  var _flightDetail = _$$_REQUIRE(_dependencyMap[7]);
  var _flightCheckin = _$$_REQUIRE(_dependencyMap[8]);
  var _flightTransfer = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _travelOptions = _$$_REQUIRE(_dependencyMap[10]);
  var _flightMap = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _leavingTheAirport = _$$_REQUIRE(_dependencyMap[12]);
  var _exploreItemType = _$$_REQUIRE(_dependencyMap[13]);
  var _error = _$$_REQUIRE(_dependencyMap[14]);
  var _i18n = _$$_REQUIRE(_dependencyMap[15]);
  var _reactNativeSizeMatters = _$$_REQUIRE(_dependencyMap[16]);
  var _adobe = _$$_REQUIRE(_dependencyMap[17]);
  var _weatherFlightCard = _$$_REQUIRE(_dependencyMap[18]);
  var _useFlightDetailClickEvent = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[19]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[20]);
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var SectionContentFragment = function SectionContentFragment(_ref) {
    var _flyFlightDetailsPayl;
    var data = _ref.data,
      direction = _ref.direction,
      selectedTravelOption = _ref.selectedTravelOption,
      flyFlightDetailsPayload = _ref.flyFlightDetailsPayload,
      isFlightSaved = _ref.isFlightSaved,
      enableEciDynamicDisplay = _ref.enableEciDynamicDisplay,
      handleMap = _ref.handleMap,
      onPressFlightCardLinks = _ref.onPressFlightCardLinks,
      flyFlightDetailsError = _ref.flyFlightDetailsError,
      onPressReloadFlightDetails = _ref.onPressReloadFlightDetails,
      isFlightDetailsFirst = _ref.isFlightDetailsFirst,
      disableSaveFlight = _ref.disableSaveFlight,
      onSaveFlight = _ref.onSaveFlight,
      saveFlightWhenCheckInOnline = _ref.saveFlightWhenCheckInOnline;
    if (!data) {
      return null;
    }
    var sections = data.sections;
    var isDeparture = direction === _flightProps.FlightDirection.departure;
    var isTraveller = selectedTravelOption === _travelOptions.TravelOption.iAmTravelling;
    var _ref2 = (flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData) || {},
      displayTerminal = _ref2.displayTerminal,
      flightNumber = _ref2.flightNumber;
    var loadingPayload = (flyFlightDetailsPayload == null || (_flyFlightDetailsPayl = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl.type) === _exploreItemType.ExploreItemTypeEnum.loading;
    var travelTabLabelTitle = (0, _i18n.translate)("flightDetailV2.flightInformationHub.menu.travel");
    var _useFlightDetailClick = (0, _useFlightDetailClickEvent.default)({
        isDeparture: isDeparture,
        isTraveller: isTraveller
      }),
      logClickEvent = _useFlightDetailClick.logClickEvent;
    var _onSendTrackingData = function onSendTrackingData(titleValue, section) {
      var modeValue = isTraveller ? _adobe.AdobeValueByTagName.FlightDetailsPageTravelling : _adobe.AdobeValueByTagName.FlightDetailsPagePickingSomeone;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.FlyFlightDetailsPageBottomDetails, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.FlyFlightDetailsPageBottomDetails, `${flightNumber} | ${modeValue} | ${direction} | ${travelTabLabelTitle} | ${titleValue}`));
      logClickEvent(section == null ? undefined : section.sectionKey);
    };
    var renderErrorComponent = function renderErrorComponent(title) {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: [_sectionContentFragment.containerErrorStyle, {
          width: isFlightDetailsFirst ? width - (0, _reactNativeSizeMatters.scale)(32) : width - (0, _reactNativeSizeMatters.scale)(45)
        }],
        children: (0, _jsxRuntime.jsx)(_error.ErrorComponent, {
          type: _error.ErrorComponentType.flightCF,
          flightCFTitle: title,
          onPressed: onPressReloadFlightDetails,
          flightCFContainerStyle: {
            marginHorizontal: 0
          }
        })
      });
    };
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: sections.map(function (section, index) {
        if (section.type.tagName === _flightDetail.SectionTagNameEnum.GTTD && !isDeparture) {
          if (flyFlightDetailsError) return renderErrorComponent();
          return (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: [_sectionContentFragment.styles.container, {
              width: isFlightDetailsFirst ? width - (0, _reactNativeSizeMatters.scale)(32) : width - (0, _reactNativeSizeMatters.scale)(45)
            }],
            children: (0, _jsxRuntime.jsx)(_leavingTheAirport.LeavingTheAirport, {
              section: section,
              flightDetails: flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData,
              flyFlightDetailsPayload: flyFlightDetailsPayload,
              isTravelling: selectedTravelOption === _travelOptions.TravelOption.iAmTravelling,
              hubTabTitle: travelTabLabelTitle,
              onSendTrackingData: function onSendTrackingData(title) {
                return _onSendTrackingData(title, section);
              }
            })
          }, index);
        }
        if (section.type.tagName === _flightDetail.SectionTagNameEnum.ATOM) {
          if (flyFlightDetailsError) return renderErrorComponent();
          return (0, _jsxRuntime.jsx)(_reactNative.View, {
            children: (0, _jsxRuntime.jsx)(_flightMap.default, {
              title: section == null ? undefined : section.title,
              onPressed: handleMap,
              terminal: displayTerminal,
              onSendTrackingData: function onSendTrackingData(title) {
                return _onSendTrackingData(title, section);
              },
              isFlightDetailsFirst: isFlightDetailsFirst
            })
          }, index);
        }
        if (section.type.tagName === _flightDetail.SectionTagNameEnum.FLIGHT_CHECK_IN && !!isDeparture && !!isTraveller) {
          if (flyFlightDetailsError) return renderErrorComponent();
          return (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: [_sectionContentFragment.styles.container, {
              width: isFlightDetailsFirst ? width - (0, _reactNativeSizeMatters.scale)(32) : width - (0, _reactNativeSizeMatters.scale)(45)
            }],
            children: (0, _jsxRuntime.jsx)(_flightCheckin.FlightCheckIn, Object.assign({}, flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData, {
              loading: loadingPayload,
              isFlightSaved: isFlightSaved,
              onPressFlightCardLinks: onPressFlightCardLinks,
              direction: direction,
              enableEciDynamicDisplay: enableEciDynamicDisplay,
              testID: "FlightCheckIn",
              title: section == null ? undefined : section.title,
              onSendTrackingData: function onSendTrackingData(title) {
                return _onSendTrackingData(title, section);
              },
              saveFlightWhenCheckInOnline: saveFlightWhenCheckInOnline
            }))
          }, index);
        }
        if (section.type.tagName === _flightDetail.SectionTagNameEnum.CMS_FLIGHT_CARD) {
          if (flyFlightDetailsError) return renderErrorComponent();
          return (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: [_sectionContentFragment.styles.container, {
              width: isFlightDetailsFirst ? width - (0, _reactNativeSizeMatters.scale)(32) : width - (0, _reactNativeSizeMatters.scale)(45)
            }],
            children: (0, _jsxRuntime.jsx)(_cmsFlightCard.CMSFlightCard, {
              section: section,
              onSendTrackingData: function onSendTrackingData(title) {
                return _onSendTrackingData(title, section);
              },
              flightDetails: flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData
            })
          }, index);
        }
        if (section.type.tagName === _flightDetail.SectionTagNameEnum.TRANSFER_IN_TERMINAL && !!displayTerminal) {
          if (flyFlightDetailsError) return renderErrorComponent();
          return (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: [_sectionContentFragment.styles.container, {
              width: isFlightDetailsFirst ? width - (0, _reactNativeSizeMatters.scale)(32) : width - (0, _reactNativeSizeMatters.scale)(45)
            }],
            children: (0, _jsxRuntime.jsx)(_flightTransfer.default, {
              section: section,
              terminal: displayTerminal,
              onPressedMarker: handleMap,
              onSendTrackingData: function onSendTrackingData(title) {
                return _onSendTrackingData(title, section);
              },
              isTravelling: selectedTravelOption === _travelOptions.TravelOption.iAmTravelling
            })
          }, index);
        }
        if (isFlightDetailsFirst && section.type.tagName === _flightDetail.SectionTagNameEnum.WEATHER) {
          if (flyFlightDetailsError) return renderErrorComponent();
          if (disableSaveFlight && !isFlightSaved) {
            return null;
          }
          return (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: [_sectionContentFragment.styles.container, {
              width: width - (0, _reactNativeSizeMatters.scale)(32),
              paddingHorizontal: 0
            }],
            children: (0, _jsxRuntime.jsx)(_weatherFlightCard.WeatherFlightCard, {
              section: section,
              onSendTrackingData: function onSendTrackingData(title) {
                return _onSendTrackingData(title, section);
              },
              flightDetailsData: flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData,
              isFlightSaved: isFlightSaved,
              onSaveFlight: onSaveFlight
            })
          }, index);
        }
        // handle other section types
        return null;
      })
    });
  };
  var _default = exports.default = SectionContentFragment;
