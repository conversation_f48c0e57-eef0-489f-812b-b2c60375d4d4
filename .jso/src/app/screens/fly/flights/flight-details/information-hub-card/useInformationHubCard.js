  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useInformationHubCard = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _flightProps = _$$_REQUIRE(_dependencyMap[2]);
  var _travelOptions = _$$_REQUIRE(_dependencyMap[3]);
  var useInformationHubCard = exports.useInformationHubCard = function useInformationHubCard() {
    var locationHasTerminal = function locationHasTerminal() {
      var locations = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];
      var terminal = arguments.length > 1 ? arguments[1] : undefined;
      return terminal ? locations.some(function (loc) {
        return loc.tagName === `terminal-${terminal}`;
      }) : locations.some(function (loc) {
        return loc.tagName === _flightProps.CustomerEligibility.All;
      });
    };
    var areaHasPublic = function areaHasPublic() {
      var areas = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];
      return areas.some(function (area) {
        return area.tagName === "public";
      });
    };
    var customerEligibilityHasTagName = function customerEligibilityHasTagName() {
      var customerEligibility = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];
      var tagName = arguments.length > 1 ? arguments[1] : undefined;
      return customerEligibility.some(function (cusEligibility) {
        var _cusEligibility$tagNa;
        return (_cusEligibility$tagNa = cusEligibility.tagName) == null ? undefined : _cusEligibility$tagNa.includes(tagName);
      });
    };
    var filterDataFacilitiesServices = function filterDataFacilitiesServices(_ref) {
      var listDataTranform = _ref.listDataTranform,
        customerEligibility = _ref.customerEligibility,
        terminal = _ref.terminal,
        selectedTravelOption = _ref.selectedTravelOption;
      if ((listDataTranform == null ? undefined : listDataTranform.length) > 0) {
        var _ref2;
        var fSByCustomerEligibility = listDataTranform == null ? undefined : listDataTranform.filter(function (currentValue) {
          var conditional = customerEligibilityHasTagName(currentValue == null ? undefined : currentValue.customerEligibility, customerEligibility);
          if (terminal) {
            conditional = conditional && locationHasTerminal(currentValue == null ? undefined : currentValue.location, terminal);
          }
          if (selectedTravelOption === _travelOptions.TravelOption.iAmPicking) {
            conditional = conditional && areaHasPublic(currentValue == null ? undefined : currentValue.area);
          }
          return conditional;
        });
        var fSByCustomerEligibilityEqualAll = [];
        if ((fSByCustomerEligibility == null ? undefined : fSByCustomerEligibility.length) < 6) {
          // List Facilities Services of contentId
          var listFSOfContentID = fSByCustomerEligibility == null ? undefined : fSByCustomerEligibility.map(function (fS) {
            return fS == null ? undefined : fS.contentId;
          });
          // Filter Facilities Services by customerEligibility includes customerEligibility=all
          fSByCustomerEligibilityEqualAll = listDataTranform == null ? undefined : listDataTranform.filter(function (currentValue) {
            var conditional = !(listFSOfContentID != null && listFSOfContentID.includes(currentValue == null ? undefined : currentValue.contentId)) && customerEligibilityHasTagName(currentValue == null ? undefined : currentValue.customerEligibility, _flightProps.CustomerEligibility.All) && locationHasTerminal(currentValue == null ? undefined : currentValue.location);
            return conditional;
          });
        }
        return (_ref2 = [].concat((0, _toConsumableArray2.default)(fSByCustomerEligibility), (0, _toConsumableArray2.default)(fSByCustomerEligibilityEqualAll))) == null ? undefined : _ref2.slice(0, 6);
      }
      return listDataTranform;
    };
    return {
      filterDataFacilitiesServices: filterDataFacilitiesServices
    };
  };
