  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.LeavingTheAirport = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _utils = _$$_REQUIRE(_dependencyMap[5]);
  var _leavingTheAirport = _$$_REQUIRE(_dependencyMap[6]);
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _gttdUtils = _$$_REQUIRE(_dependencyMap[8]);
  var _adobe = _$$_REQUIRE(_dependencyMap[9]);
  var _native = _$$_REQUIRE(_dependencyMap[10]);
  var _lodash = _$$_REQUIRE(_dependencyMap[11]);
  var _constants = _$$_REQUIRE(_dependencyMap[12]);
  var _icons = _$$_REQUIRE(_dependencyMap[13]);
  var _dateTime = _$$_REQUIRE(_dependencyMap[14]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _flyTileCard = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _i18n = _$$_REQUIRE(_dependencyMap[17]);
  var _menuOption = _$$_REQUIRE(_dependencyMap[18]);
  var _flightDetail = _$$_REQUIRE(_dependencyMap[19]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[20]));
  var _screenHelper = _$$_REQUIRE(_dependencyMap[21]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[22]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var TWO_HOURS = 120; // 2 hours in minutes

  var handleScheduledDateForAA = function handleScheduledDateForAA(statusMapping, displayTimestamp, scheduledDate) {
    var date = scheduledDate;
    if ((0, _utils.handleCondition)((statusMapping == null ? undefined : statusMapping.details_status_en) && ((statusMapping == null ? undefined : statusMapping.details_status_en.includes("Re-timed")) || (statusMapping == null ? undefined : statusMapping.details_status_en.includes("Delayed"))), true, false)) {
      var _displayTimestamp$spl;
      date = (0, _utils.handleCondition)(displayTimestamp, displayTimestamp == null || (_displayTimestamp$spl = displayTimestamp.splitranslate(" ")) == null ? undefined : _displayTimestamp$spl[0], scheduledDate);
    }
    return date;
  };
  var LeavingTheAirport = exports.LeavingTheAirport = function LeavingTheAirport(_ref) {
    var _flyFlightDetailsPayl, _flyFlightDetailsPayl2;
    var section = _ref.section,
      flyFlightDetailsPayload = _ref.flyFlightDetailsPayload,
      isTravelling = _ref.isTravelling,
      hubTabTitle = _ref.hubTabTitle,
      onSendTrackingData = _ref.onSendTrackingData,
      flightDetails = _ref.flightDetails;
    var navigation = (0, _native.useNavigation)();
    var _ref2 = (flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData) || "",
      groundTransport = _ref2.groundTransport;
    var terminal = flyFlightDetailsPayload == null || (_flyFlightDetailsPayl = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl.displayTerminal;
    var displayTimestamp = flyFlightDetailsPayload == null || (_flyFlightDetailsPayl2 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl2.displayTimestamp;
    var isMoreThan2HoursBeforeDisplay = (0, _react.useMemo)(function () {
      var currentSGTTimestamp = (0, _dateTime.getCurrentTimeSingapore)();
      var diffTime = (0, _moment.default)(currentSGTTimestamp, _dateTime.DateFormats["YearMonthDayTime"]).diff((0, _moment.default)(displayTimestamp, _dateTime.DateFormats["YearMonthDayTime"]), "minutes");
      return diffTime > TWO_HOURS;
    }, [displayTimestamp]);
    var tiles = (0, _react.useMemo)(function () {
      if (section != null && section.tiles) {
        return section.tiles.sort(function (a, b) {
          return Number(a.sequenceNumber) - Number(b.sequenceNumber);
        });
      }
      return [];
    }, [section]);
    var onPressGTTDOptions = function onPressGTTDOptions() {
      var _flyFlightDetailsPayl3, _flyFlightDetailsPayl4, _flyFlightDetailsPayl5;
      var _ref3 = (flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData) || {},
        flightNumber = _ref3.flightNumber;
      var scheduledDate = handleScheduledDateForAA(flyFlightDetailsPayload == null || (_flyFlightDetailsPayl3 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl3.statusMapping, flyFlightDetailsPayload == null || (_flyFlightDetailsPayl4 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl4.displayTimestamp, flyFlightDetailsPayload == null || (_flyFlightDetailsPayl5 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl5.scheduledDate);
      var name = groundTransport == null ? undefined : groundTransport.name;
      var url = groundTransport == null ? undefined : groundTransport.url;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlyFlightDetailFlightCardLinks, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlyFlightDetailFlightCardLinks, `${name}|${flightNumber}|${scheduledDate}`));
      // @ts-ignore
      navigation.navigate(_constants.NavigationConstants.webview, {
        uri: url
      });
    };
    var renderGroundTransport = function renderGroundTransport(props) {
      var name = props.name,
        subText2 = props.subText2,
        price = props.price,
        time = props.time,
        transportType = props.transportType;
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _leavingTheAirport.styles.wrapGTTDCard,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          tx: "flightDetails.shortestWaitTimeNow",
          preset: "caption1Bold",
          style: _leavingTheAirport.styles.textTitleGTTDStyle
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _leavingTheAirport.styles.wrapContentGTTDCardStyle,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _leavingTheAirport.styles.nameAndSubTextSectionStyle,
            children: [(0, _gttdUtils.handleGTTDIcon)(transportType), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                text: name,
                preset: "bodyTextBold",
                style: _leavingTheAirport.styles.textNameGTTDStyle
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                text: subText2,
                preset: "XSmallBold",
                style: _leavingTheAirport.styles.textSubText2GTTDStyle
              })]
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _leavingTheAirport.styles.priceAndTimeSectionStyle,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              text: price,
              preset: "caption1Regular",
              style: _leavingTheAirport.styles.textPriceGTTDStyle
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              text: time,
              preset: "caption1Regular",
              style: _leavingTheAirport.styles.textTimeGTTDStyle
            })]
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
          style: _leavingTheAirport.styles.containerMoreOptions,
          onPress: onPressGTTDOptions,
          children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _leavingTheAirport.styles.containerLeftMoreOptions,
            children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                tx: "flightDetails.moreOptions",
                preset: "bodyTextBold",
                style: _leavingTheAirport.styles.textTouchableGTTDStyle
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                tx: "flightDetails.subTextMoreOptions",
                preset: "caption2Regular",
                style: _leavingTheAirport.styles.textTouchableGTTDStyle
              })]
            })
          }), (0, _jsxRuntime.jsx)(_icons.CaretRight, {})]
        })]
      });
    };
    var renderDefaultGTTDCard = function renderDefaultGTTDCard() {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _leavingTheAirport.styles.containerDefaultLeavingTheAirport,
        children: (0, _jsxRuntime.jsx)(_flyTileCard.default, {
          icon: (0, _jsxRuntime.jsx)(_icons.FlightCarConcierge, {}),
          title: (0, _i18n.translate)("flightDetails.transportForLargerGroup"),
          desc: (0, _i18n.translate)("flightDetails.bookLargerVehiclesToSuiltYourNeeds"),
          navigationTiles: {
            type: _menuOption.NavigationType.external,
            value: "https://forms.office.com/Pages/ResponsePage.aspx?id=NJmMJfoiSECEyMUrRzGHzuSetualMwZLpbKwNU4WIzNURFY3WTFWUkI4TFVXMU9DOUVIV1pKNlA4Vy4u"
          },
          tileTag: _flightDetail.SectionTileTagNameEnum.LONG_DESIGN,
          onSendTrackingData: onSendTrackingData
        })
      });
    };
    var renderIconCard = function renderIconCard(icon) {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _leavingTheAirport.styles.containerIcon,
        children: (0, _jsxRuntime.jsx)(_baseImage.default, {
          source: {
            uri: (0, _screenHelper.getUriImage)(icon)
          },
          style: _leavingTheAirport.styles.imageStyle,
          resizeMode: "contain"
        })
      });
    };
    var renderTiles = function renderTiles() {
      if ((0, _lodash.isEmpty)(tiles)) return null;
      var renderedRows = [];
      var rowQueue = []; // Temporary array to hold tiles that are not long-design

      /**
       * Flush the rowQueue:
       * - If there is only one tile in the queue (tileTag !== "long-design"), add a dummy tile
       *   to always render 2 tiles in one row.
       */
      var flushRowQueue = function flushRowQueue(keySuffix) {
        if (rowQueue.length === 0) return;
        // If there is only one non-long-design tile, add a dummy tile.
        if (rowQueue.length === 1) {
          var dummyTile = {
            icon: "",
            title: "",
            description: "",
            navigation: [],
            redirect: undefined,
            type: {
              tagName: "dummy"
            }
          };
          rowQueue.push(dummyTile);
        }
        // Render two tiles in one row
        renderedRows.push((0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _leavingTheAirport.styles.transportServicesRow,
          children: rowQueue.map(function (tile, idx) {
            return (0, _jsxRuntime.jsx)(_flyTileCard.default, {
              icon: tile.icon ? renderIconCard(tile.icon) : (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: _leavingTheAirport.styles.placeholderIcon
              }),
              title: tile.title,
              desc: tile.description,
              navigationTiles: tile.navigation,
              redirect: tile.redirect,
              tileTag: tile.type.tagName,
              onSendTrackingData: onSendTrackingData,
              flightDetails: flightDetails
            }, `tile-${keySuffix}-${idx}`);
          })
        }, `row-${keySuffix}`));
        rowQueue = [];
      };

      // Loop through each tile in the list
      tiles.forEach(function (tile, index) {
        var tileTag = tile.type.tagName;
        if (tileTag === "long-design") {
          // Flush any pending group before rendering a long-design tile
          flushRowQueue(index);
          renderedRows.push((0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _leavingTheAirport.styles.containerDefaultLeavingTheAirport,
            children: (0, _jsxRuntime.jsx)(_flyTileCard.default, {
              icon: renderIconCard(tile.icon),
              title: tile.title,
              desc: tile.description,
              navigationTiles: tile.navigation,
              redirect: tile.redirect,
              tileTag: tileTag,
              onSendTrackingData: onSendTrackingData,
              flightDetails: flightDetails
            })
          }, `longTile-${index}`));
        } else {
          // For any tile that is not long-design, group them into rows of 2.
          rowQueue.push(tile);
          if (rowQueue.length === 2) {
            flushRowQueue(index);
          }
        }
      });

      // Flush any remaining non-long-design tile(s) in the rowQueue
      flushRowQueue("final");

      // Optionally remove the bottom border from the last row
      if (renderedRows.length > 0) {
        var lastRow = renderedRows[renderedRows.length - 1];
        renderedRows[renderedRows.length - 1] = _react.default.cloneElement(lastRow, {
          style: [lastRow.props.style, {
            borderBottomWidth: 0,
            borderBottomColor: "transparent",
            paddingBottom: 0
          }]
        });
      }
      return renderedRows;
    };
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _leavingTheAirport.styles.titleContainer,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          text: (section == null ? undefined : section.title) || "",
          preset: "caption1Bold",
          numberOfLines: 1,
          style: _leavingTheAirport.styles.textAlmostBackColor
        })
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        children: [Number(terminal) !== 1 || (0, _lodash.isEmpty)(groundTransport) || isMoreThan2HoursBeforeDisplay ? renderDefaultGTTDCard() : renderGroundTransport(groundTransport), renderTiles()]
      })]
    });
  };
