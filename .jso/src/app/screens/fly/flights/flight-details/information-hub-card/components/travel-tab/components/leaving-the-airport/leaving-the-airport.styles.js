  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    titleContainer: {
      flex: 1
    },
    textAlmostBackColor: {
      color: _theme.color.palette.almostBlackGrey,
      textAlign: "left"
    },
    textDaskestGrey: {
      color: _theme.color.palette.darkestGrey,
      textAlign: "left"
    },
    wrapGTTDCard: {
      marginTop: 16,
      alignItems: "flex-start",
      borderBottomWidth: 1,
      borderBottomColor: _theme.color.palette.lighterGrey,
      paddingBottom: 16
    },
    textTitleGTTDStyle: {
      color: _theme.color.palette.darkestGrey
    },
    wrapContentGTTDCardStyle: {
      flexDirection: "row",
      width: "100%",
      alignItems: "center",
      marginTop: 16,
      justifyContent: "space-between"
    },
    nameAndSubTextSectionStyle: {
      alignItems: "flex-start",
      flexDirection: "row"
    },
    priceAndTimeSectionStyle: {
      alignItems: "flex-start",
      marginLeft: 8
    },
    textNameGTTDStyle: {
      marginLeft: 4,
      color: _theme.color.palette.almostBlackGrey
    },
    textSubText2GTTDStyle: {
      marginLeft: 4,
      color: _theme.color.palette.almostBlackGrey,
      textTransform: "none"
    },
    textPriceGTTDStyle: {
      color: _theme.color.palette.almostBlackGrey
    },
    textTimeGTTDStyle: {
      color: _theme.color.palette.almostBlackGrey
    },
    textTouchableGTTDStyle: {
      color: _theme.color.palette.purple671A9D
    },
    containerMoreOptions: {
      flexDirection: "row",
      marginTop: 16,
      alignItems: "center"
    },
    containerLeftMoreOptions: {
      flex: 1
    },
    containerTransportCard: {
      flexDirection: "row",
      alignItems: "center",
      flex: 1
    },
    transportServicesRow: {
      flexDirection: "row",
      marginTop: 16,
      gap: 12
    },
    containerDefaultLeavingTheAirport: {
      marginTop: 16,
      borderBottomWidth: 1,
      borderBottomColor: _theme.color.palette.lighterGrey,
      paddingBottom: 16
    },
    containerIcon: {
      width: 40,
      height: 40,
      borderRadius: 4,
      backgroundColor: _theme.color.palette.lightestGrey,
      justifyContent: "center",
      alignItems: "center"
    },
    imageStyle: {
      height: 24,
      width: 24
    },
    placeholderIcon: {
      width: 40,
      height: 40,
      borderRadius: 4,
      backgroundColor: _theme.color.palette.whiteGrey,
      justifyContent: "center",
      alignItems: "center"
    }
  });
