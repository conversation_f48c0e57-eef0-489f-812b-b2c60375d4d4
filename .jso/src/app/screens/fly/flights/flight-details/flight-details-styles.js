  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    safeAreaVieContainerStyle: {
      position: "absolute",
      width: "100%",
      zIndex: 1,
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginTop: 58
    },
    backButtonStyle: {
      marginStart: 14
    },
    shareButtonStyle: {
      marginEnd: 12
    },
    shareMoreContainerStyle: {
      flexDirection: "row",
      position: "absolute",
      right: 0,
      bottom: 0,
      marginEnd: 14
    },
    scrollViewContainerStyle: {
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1
    },
    dropDownSelectCardContainerStyle: {
      position: "absolute",
      bottom: -12,
      left: 24,
      right: 24,
      zIndex: 100
    },
    informativeTextStyle: Object.assign({
      paddingLeft: 16,
      paddingRight: 16
    }, _text.presets.bodyTextRegular, {
      fontSize: 12,
      lineHeight: 16,
      marginBottom: -8,
      color: _theme.color.palette.whiteGrey
    }),
    headerStyle: {
      color: _theme.color.palette.whiteGrey
    },
    dropDownSelectCardStyle: {
      width: "100%"
    },
    fabContainerViewStyle: {
      position: "absolute",
      alignSelf: "flex-end",
      alignItems: "center",
      justifyContent: "center",
      right: 12
    },
    fabArrowStyle: {
      position: "absolute",
      zIndex: 1
    },
    buttonLinearStyle: {
      borderRadius: 60,
      marginTop: 12
    },
    saveFlightStyle: {
      position: "absolute",
      bottom: 0,
      left: 0,
      right: 0,
      paddingHorizontal: 24,
      paddingTop: 12,
      paddingBottom: 36,
      backgroundColor: _theme.color.palette.whiteGrey
    },
    feedBackToastContainerStyle: {
      position: "absolute",
      bottom: 8,
      width: "100%"
    },
    feedBackToastStyle: {
      width: "100%",
      paddingHorizontal: 16,
      bottom: 40
    },
    toastButtonStyle: Object.assign({}, _text.presets.textLink, {
      fontWeight: "normal",
      color: _theme.color.palette.lightBlue,
      alignItems: "flex-end"
    }),
    toastTextStyle: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.whiteGrey,
      width: "80%"
    }),
    maintenanceErrorContainer: {
      zIndex: -1,
      marginBottom: 150
    },
    titleText: Object.assign({}, _text.presets.h2, {
      textAlign: 'center',
      marginHorizontal: 24,
      marginBottom: 16,
      color: _theme.color.palette.whiteGrey
    }),
    descriptionText: Object.assign({}, _text.presets.bodyTextBlackRegular, {
      textAlign: 'center',
      marginHorizontal: 24,
      marginBottom: 16,
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.whiteGrey
    }),
    popupButtonPrimary: {
      borderRadius: 60,
      backgroundColor: _theme.color.palette.whiteGrey,
      paddingVertical: 10,
      marginHorizontal: 24
    },
    popupButtonTextPrimary: {
      fontSize: 16,
      lineHeight: 24,
      fontFamily: _theme.typography.bold,
      textAlign: 'center',
      color: _theme.color.palette.lightPurple
    },
    popupButtonSecondary: {
      borderRadius: 60,
      marginTop: 12,
      borderWidth: 2,
      borderColor: _theme.color.palette.whiteGrey,
      paddingVertical: 10,
      marginHorizontal: 24
    },
    popupButtonTextSecondary: {
      fontSize: 16,
      lineHeight: 24,
      fontFamily: _theme.typography.bold,
      textAlign: 'center',
      color: _theme.color.palette.whiteGrey
    },
    headerImage: {
      width: '100%',
      height: (_reactNative.Dimensions.get('window').width - 48) * 148 / 327
    },
    popupContent: {
      width: '100%',
      borderBottomLeftRadius: 24,
      borderBottomRightRadius: 24,
      paddingBottom: 24
    },
    terminalDisclaimerText: Object.assign({}, _text.presets.bodyTextBlackRegular, {
      fontSize: 11,
      marginLeft: 4,
      marginRight: 4,
      lineHeight: 24,
      color: _theme.color.palette.darkGrey999
    })
  });
