  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _flyRedux = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _utils = _$$_REQUIRE(_dependencyMap[10]);
  var _lodash = _$$_REQUIRE(_dependencyMap[11]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[12]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[13]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[15]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[16]);
  var _navigationType = _$$_REQUIRE(_dependencyMap[17]);
  var _constants = _$$_REQUIRE(_dependencyMap[18]);
  var _native = _$$_REQUIRE(_dependencyMap[19]);
  var _adobe = _$$_REQUIRE(_dependencyMap[20]);
  var _mytravelRedux = _$$_REQUIRE(_dependencyMap[21]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[22]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _Dimensions$get = _reactNative2.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var checkInternetConnection = /*#__PURE__*/function () {
    var _ref = (0, _asyncToGenerator2.default)(function* () {
      var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
        isConnected = _yield$NetInfo$fetch.isConnected;
      return isConnected;
    });
    return function checkInternetConnection() {
      return _ref.apply(this, arguments);
    };
  }();
  var AppscapadeEnum = /*#__PURE__*/function (AppscapadeEnum) {
    AppscapadeEnum["notEligible"] = "not_eligible";
    AppscapadeEnum["noLuckyDrawEntry"] = "no_lucky_draw_entry";
    AppscapadeEnum["instantWinNotPlayed"] = "instant_win_not_played";
    AppscapadeEnum["instantWinPlayed"] = "instant_win_played";
    return AppscapadeEnum;
  }(AppscapadeEnum || {});
  var AppscapadeBannerFlightDetail = function AppscapadeBannerFlightDetail(_ref2) {
    var _flyFlightDetailsPayl, _flyFlightDetailsPayl2;
    var isTravelling = _ref2.isTravelling;
    var dispatch = (0, _reactRedux.useDispatch)();
    var navigation = (0, _native.useNavigation)();
    var flyFlightDetailsPayload = (0, _reactRedux.useSelector)(function (state) {
      return _flyRedux.FlySelectors.flyFlightDetailsPayload(state);
    });
    var isExistFlightData = !(0, _lodash.isEmpty)(flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData);
    var isSavedFlight = flyFlightDetailsPayload == null || (_flyFlightDetailsPayl = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl.isSaved;
    var isDepature = (flyFlightDetailsPayload == null || (_flyFlightDetailsPayl2 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl2.departingCode) === "SIN";
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var appscapadeBannerPayload = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.appscapadeBannerPayload);
    var removeFlightPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.removeFlightPayload);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      visibleBanner = _useState2[0],
      setVisibleBanner = _useState2[1];
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("FLIGHT_DETAIL_BANNER_APPSCAPADE"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    (0, _react.useEffect)(function () {
      return function () {
        dispatch(_flyRedux.FlyCreators.getAppscapadeBannerReset());
      };
    }, []);
    var fetchData = function fetchData() {
      var isFeatureFlagON = (0, _remoteConfig.isFlagON)(_remoteConfig.REMOTE_CONFIG_FLAGS.APPSCAPADE_FLIGHTDETAILS);
      if (!isFeatureFlagON) return;
      var _flyFlightDetailsPayl3 = flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData,
        scheduledDate = _flyFlightDetailsPayl3.scheduledDate,
        flightNumber = _flyFlightDetailsPayl3.flightNumber;
      checkInternetConnection().then(function (isConnection) {
        if (isConnection) {
          setVisibleBanner(true);
          dispatch(_flyRedux.FlyCreators.getAppscapadeBannerRequest({
            flightNumber: flightNumber,
            scheduledDate: scheduledDate,
            direction: "DEP"
          }));
        }
      });
    };
    (0, _react.useEffect)(function () {
      if ((0, _utils.ifAllTrue)([isExistFlightData, isSavedFlight, isTravelling, isDepature, isLoggedIn])) {
        fetchData();
      } else {
        setVisibleBanner(false);
      }
    }, [flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData, isLoggedIn, isSavedFlight]);
    (0, _react.useEffect)(function () {
      if (removeFlightPayload != null && removeFlightPayload.isRemovedSuccessFully) {
        setVisibleBanner(false);
      }
    }, [removeFlightPayload == null ? undefined : removeFlightPayload.isRemovedSuccessFully]);
    var onPressToBanner = function onPressToBanner() {
      if ((appscapadeBannerPayload == null ? undefined : appscapadeBannerPayload.navigation_value) === _navigationHelper.NavigationValueDeepLink.appscapade) {
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppAppscapadeBannerEntry, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppAppscapadeBannerEntry, `Flight Details | ${appscapadeBannerPayload == null ? undefined : appscapadeBannerPayload.fragment_title}`));
        if ((appscapadeBannerPayload == null ? undefined : appscapadeBannerPayload.status) === AppscapadeEnum.instantWinNotPlayed) {
          handleNavigation(_navigationType.NavigationTypeEnum.deepLink, _navigationHelper.NavigationValueDeepLink.appscapadePlayAndWin, {
            chanceId: appscapadeBannerPayload == null ? undefined : appscapadeBannerPayload.chance_id
          });
        } else if ((appscapadeBannerPayload == null ? undefined : appscapadeBannerPayload.status) === AppscapadeEnum.noLuckyDrawEntry) {
          navigation.replace(_constants.NavigationConstants.appscapadeScanBoadingPass, {
            isFromFlightDetail: flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData
          });
        } else if ((appscapadeBannerPayload == null ? undefined : appscapadeBannerPayload.status) === AppscapadeEnum.instantWinPlayed) {
          handleNavigation(_navigationType.NavigationTypeEnum.deepLink, _navigationHelper.NavigationValueDeepLink.appscapade);
        }
      }
    };
    if (!(0, _utils.ifAllTrue)([isExistFlightData, isSavedFlight, isTravelling, isDepature, isLoggedIn])) {
      return null;
    }
    if (!visibleBanner) return null;
    switch (appscapadeBannerPayload == null ? undefined : appscapadeBannerPayload.status) {
      case AppscapadeEnum.instantWinPlayed:
      case AppscapadeEnum.noLuckyDrawEntry:
      case AppscapadeEnum.instantWinNotPlayed:
        return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: styles.container,
          activeOpacity: 0.5,
          onPress: onPressToBanner,
          children: (0, _jsxRuntime.jsx)(_baseImage.default, {
            source: {
              uri: (0, _mediaHelper.handleImageUrl)(appscapadeBannerPayload == null ? undefined : appscapadeBannerPayload.image)
            },
            style: styles.imageStyle,
            resizeMode: "stretch"
          })
        });
      default:
        return null;
    }
  };
  var styles = _reactNative2.StyleSheet.create({
    container: {
      alignItems: "center",
      borderRadius: 16,
      marginTop: 16,
      overflow: "hidden"
    },
    imageStyle: {
      height: 88,
      width: width - 48
    }
  });
  var _default = exports.default = AppscapadeBannerFlightDetail;
