  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.FlightCheckIn = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _useFlightEarlyCheckinV = _$$_REQUIRE(_dependencyMap[5]);
  var _constants = _$$_REQUIRE(_dependencyMap[6]);
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _utils = _$$_REQUIRE(_dependencyMap[8]);
  var _flightDetailEarlyCheckinV = _$$_REQUIRE(_dependencyMap[9]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[10]);
  var _flyRedux = _$$_REQUIRE(_dependencyMap[11]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[12]);
  var _icons = _$$_REQUIRE(_dependencyMap[13]);
  var _loadingFlightCard = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _native = _$$_REQUIRE(_dependencyMap[15]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[16]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var notAvailable = "Not available";
  var onlineCheckInLabel = "Online check-in";
  var earlyCheckInLabel = "Early check-in";
  var cardLinksContainer = {
    alignSelf: "center",
    marginBottom: 12
  };
  var isShowOnlineAndEarlyCheckIn = function isShowOnlineAndEarlyCheckIn(statusTag) {
    var status = statusTag == null ? undefined : statusTag.toLowerCase();
    var isShowOnlineCheckIn = !/cancelled/gim.test(status) && !/departed/gim.test(status);
    var isShowEarlyCheckIn = !/delayed/gim.test(status) && !/cancelled/gim.test(status) && !/departed/gim.test(status);
    return {
      isShowOnlineCheckIn: isShowOnlineCheckIn,
      isShowEarlyCheckIn: isShowEarlyCheckIn
    };
  };
  var FlightCheckIn = exports.FlightCheckIn = function FlightCheckIn(props) {
    var _onlineCheckIn$link, _earlyCheckIn$link;
    var loading = props.loading,
      status = props.status,
      scheduledDate = props.scheduledDate,
      scheduledTime = props.scheduledTime,
      flightNumber = props.flightNumber,
      terminal = props.terminal,
      checkInRow = props.checkInRow,
      statusMapping = props.statusMapping,
      airlineDetails = props.airlineDetails,
      onlineCheckIn = props.onlineCheckIn,
      earlyCheckIn = props.earlyCheckIn,
      isFlightSaved = props.isFlightSaved,
      testID = props.testID,
      enableEciDynamicDisplay = props.enableEciDynamicDisplay,
      airportDetails = props.airportDetails,
      onPressFlightCardLinks = props.onPressFlightCardLinks,
      onSendTrackingData = props.onSendTrackingData,
      title = props.title,
      isSaved = props.isSaved,
      saveFlightWhenCheckInOnline = props.saveFlightWhenCheckInOnline;
    var dispatch = (0, _reactRedux.useDispatch)();
    var navigation = (0, _native.useNavigation)();
    var _isShowOnlineAndEarly = isShowOnlineAndEarlyCheckIn(status),
      isShowEarlyCheckIn = _isShowOnlineAndEarly.isShowEarlyCheckIn,
      isShowOnlineCheckIn = _isShowOnlineAndEarly.isShowOnlineCheckIn;
    var _useFlightEarlyChecki = (0, _useFlightEarlyCheckinV.useFlightEarlyCheckinV2)(airportDetails, {
        "airlineCode": airlineDetails == null ? undefined : airlineDetails.code,
        "airportCode": airportDetails == null ? undefined : airportDetails.code,
        "flightNumber": flightNumber,
        "flightStatus": statusMapping == null ? undefined : statusMapping.details_status_en,
        "scheduledDatetime": `${scheduledDate} ${scheduledTime}`,
        "checkinRow": checkInRow,
        "terminal": terminal
      }, enableEciDynamicDisplay),
      loadingData = _useFlightEarlyChecki.loadingData,
      isCalled = _useFlightEarlyChecki.isCalled,
      dataEarlyCheckinV2 = _useFlightEarlyChecki.dataEarlyCheckinV2;
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var isPendingCheckInOnline = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flyPendingCheckInOnline);
    var isPendingSaveFlight = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flyPendingSaveFlight);
    var isFocused = (0, _native.useIsFocused)();
    (0, _react.useEffect)(function () {
      if ((0, _utils.handleCondition)(isPendingCheckInOnline && isLoggedIn && isFocused, true, false)) {
        saveFlightWhenCheckInOnline == null || saveFlightWhenCheckInOnline(isSaved);
        dispatch(_flyRedux.FlyCreators.flyPendingCheckInOnline(false));
      }
    }, [isPendingCheckInOnline, isLoggedIn, isFocused]);
    (0, _react.useEffect)(function () {
      if ((0, _utils.handleCondition)(isPendingSaveFlight && isLoggedIn && isFlightSaved, true, false)) {
        saveFlightWhenCheckInOnline == null || saveFlightWhenCheckInOnline(isFlightSaved);
        dispatch(_flyRedux.FlyCreators.flyPendingSaveFlight(false));
      }
    }, [isPendingSaveFlight, isFlightSaved]);
    var handleEarlyCheckinV2 = function handleEarlyCheckinV2() {
      return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
        children: loadingData && !isCalled ? (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: {
            alignItems: 'flex-start'
          },
          children: (0, _jsxRuntime.jsx)(_reactNative2.ActivityIndicator, {
            size: "small",
            color: _theme.color.palette.lightPurple
          })
        }) : (0, _jsxRuntime.jsx)(_flightDetailEarlyCheckinV.FlightEarlyCheckInV2, {
          data: dataEarlyCheckinV2,
          sendEventTracking: function sendEventTracking(link) {
            return onPressFlightCardLinks(link);
          },
          trackingForFlightBottomDetails: onSendTrackingData
        })
      });
    };
    var handlePressOnlineCheckIn = function handlePressOnlineCheckIn() {
      onSendTrackingData(onlineCheckIn == null ? undefined : onlineCheckIn.linkText);
      onPressFlightCardLinks(onlineCheckIn == null ? undefined : onlineCheckIn.linkText);
      saveFlightWhenCheckInOnline == null || saveFlightWhenCheckInOnline(isFlightSaved);
    };
    var handlePressEarlyCheckIn = function handlePressEarlyCheckIn() {
      onSendTrackingData(earlyCheckIn == null ? undefined : earlyCheckIn.linkText);
      onPressFlightCardLinks(earlyCheckIn == null ? undefined : earlyCheckIn.linkText);
      // @ts-ignore
      navigation == null || navigation.navigate(_constants.NavigationConstants.webview, {
        uri: earlyCheckIn == null ? undefined : earlyCheckIn.link
      });
    };
    if (loading) return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: cardLinksContainer,
      children: (0, _jsxRuntime.jsx)(_loadingFlightCard.default, {})
    });
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        style: styles.cardTitle,
        text: title
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.cardRow,
        children: [(0, _jsxRuntime.jsx)(_icons.FlightCheckInIcon, {}), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            preset: "XSmallRegular",
            style: styles.cardLabel,
            text: onlineCheckInLabel
          }), (0, _utils.handleCondition)(isShowOnlineCheckIn && (onlineCheckIn == null || (_onlineCheckIn$link = onlineCheckIn.link) == null ? undefined : _onlineCheckIn$link.length) > 0, (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: handlePressOnlineCheckIn,
            testID: `${testID}__TouchableLinkText1`,
            accessibilityLabel: `${testID}__TouchableLinkText1`,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              preset: "textLink",
              style: styles.highlightText,
              text: onlineCheckIn == null ? undefined : onlineCheckIn.linkText
            })
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              style: [styles.highlightText, styles.disabledText],
              preset: "textLink",
              text: notAvailable
            })
          }))]
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.divider
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            preset: "XSmallRegular",
            style: styles.cardLabel,
            text: earlyCheckInLabel
          }), !enableEciDynamicDisplay ? (0, _utils.handleCondition)(isShowEarlyCheckIn && (earlyCheckIn == null || (_earlyCheckIn$link = earlyCheckIn.link) == null ? undefined : _earlyCheckIn$link.length) > 0, (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: handlePressEarlyCheckIn,
            testID: `${testID}__TouchableLinkText2`,
            accessibilityLabel: `${testID}__TouchableLinkText2`,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              preset: "textLink",
              style: styles.highlightText,
              text: earlyCheckIn == null ? undefined : earlyCheckIn.linkText
            })
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              style: [styles.highlightText, styles.disabledText],
              preset: "textLink",
              text: notAvailable
            })
          })) : handleEarlyCheckinV2()]
        })]
      })]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    card: {
      padding: 16,
      borderRadius: 16,
      backgroundColor: _theme.color.palette.whiteGrey,
      rowGap: 16
    },
    cardTitle: Object.assign({}, _text.newPresets.caption1Bold, {
      color: _theme.color.palette.almostBlackGrey
    }),
    cardRow: {
      flexDirection: "row",
      columnGap: 12,
      marginTop: 16
    },
    cardColumn: {
      rowGap: 8
    },
    cardLabel: Object.assign({}, _text.newPresets.caption2Regular, {
      color: _theme.color.palette.darkestGrey
    }),
    cardText: Object.assign({}, _text.newPresets.caption1Bold),
    highlightText: Object.assign({}, _text.newPresets.caption1Bold, {
      color: _theme.color.palette.lightPurple
    }),
    disabledText: {
      color: _theme.color.palette.midGrey
    },
    divider: {
      width: 1,
      height: 35,
      backgroundColor: _theme.color.palette.lighterGrey
    }
  });
