  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _flightApi = _$$_REQUIRE(_dependencyMap[8]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[9]);
  var _flightJourney = _$$_REQUIRE(_dependencyMap[10]);
  var _icons = _$$_REQUIRE(_dependencyMap[11]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _constants = _$$_REQUIRE(_dependencyMap[13]);
  var _theme = _$$_REQUIRE(_dependencyMap[14]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[15]);
  var _reactNativeMaps = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[16]));
  var _popupJourneyError = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _i18n = _$$_REQUIRE(_dependencyMap[18]);
  var _flightDetailsCard = _$$_REQUIRE(_dependencyMap[19]);
  var _flightInfo = _$$_REQUIRE(_dependencyMap[20]);
  var _useFlightJourney2 = _$$_REQUIRE(_dependencyMap[21]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[22]));
  var _utils = _$$_REQUIRE(_dependencyMap[23]);
  var _lodash = _$$_REQUIRE(_dependencyMap[24]);
  var _momentTimezone = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[25]));
  var _flyHelper = _$$_REQUIRE(_dependencyMap[26]);
  var _dateTime = _$$_REQUIRE(_dependencyMap[27]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[28]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var lighterGreyLoadingColors = _theme.color.palette.flightJourneyLinearGradient;
  var SCREEN_NAME = "FlightJourney";
  var ANIMATED_TIME_DURATION = 700;
  var STATUS_TIME_PLACEHOLDER = 15;
  var _worklet_13797666687337_init_data = {
    code: "function flightJourneyTsx1(){const{opacity}=this.__closure;return{opacity:opacity.value};}"
  };
  var _worklet_15402308412334_init_data = {
    code: "function flightJourneyTsx2(){const{headerHeightValue}=this.__closure;return{position:\"absolute\",backgroundColor:\"rgba(18, 18, 18, 0.9)\",left:0,right:0,paddingVertical:16,paddingHorizontal:24,top:headerHeightValue.value,opacity:headerHeightValue.value===0?0:1};}"
  };
  var FlightJourney = function FlightJourney(_ref) {
    var _flightDetails$airpor1, _flightDetails$airpor10, _flightDetails$airpor11, _flightDetails$airpor12;
    var isFlightJourneyFullScreen = _ref.isFlightJourneyFullScreen,
      isStartShowFlightJourney = _ref.isStartShowFlightJourney,
      handleHide = _ref.handleHide,
      flightDetails = _ref.flightDetails,
      isLoadingData = _ref.isLoadingData,
      getMyTripData = _ref.getMyTripData;
    var _useSafeAreaInsets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)(),
      top = _useSafeAreaInsets.top;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isLoadingFlightJourney = _useState2[0],
      setIsLoadingFlightJourney = _useState2[1];
    var _useState3 = (0, _react.useState)(),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      flightJourneyData = _useState4[0],
      setFlightJourneyData = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      showErrPopup = _useState6[0],
      setShowErrPopup = _useState6[1];
    var _useState7 = (0, _react.useState)(null),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      errorType = _useState8[0],
      setErrorType = _useState8[1];
    var _useState9 = (0, _react.useState)(false),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      isMapLoaded = _useState0[0],
      setIsMapLoaded = _useState0[1];
    var _useState1 = (0, _react.useState)(false),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      isShowTrackingNotAvailable = _useState10[0],
      setShowTrackingNotAvailable = _useState10[1];
    var _useState11 = (0, _react.useState)(0),
      _useState12 = (0, _slicedToArray2.default)(_useState11, 2),
      headerHeight = _useState12[0],
      setHeaderHeight = _useState12[1];
    var headerHeightValue = (0, _reactNativeReanimated.useSharedValue)(0);
    var mapRef = (0, _react.useRef)(null);
    var _useState13 = (0, _react.useState)(0),
      _useState14 = (0, _slicedToArray2.default)(_useState13, 2),
      mapRotation = _useState14[0],
      setMapRotation = _useState14[1];

    // flightJourneyStatus has 3 statuses: "not-started", "departed", "landed"
    var _useFlightJourney = (0, _useFlightJourney2.useFlightJourney)({
        flightDetails: flightDetails,
        getMyTripData: getMyTripData
      }),
      flightJourneyStatus = _useFlightJourney.flightJourneyStatus;
    var journeyStatusFlightDetails = (0, _react.useMemo)(function () {
      if ((0, _lodash.isEmpty)(flightDetails) || (0, _lodash.isEmpty)(getMyTripData)) {
        return null;
      }
      if ((flightDetails == null ? undefined : flightDetails.direction) === _constants.FlightDirection.Departure) {
        var _flightDetails$flight, _flightDetails$status;
        if (getMyTripData.flightStatus === _flightInfo.FlightJourneyEnumStatus.LANDED) {
          var _flightDetails$airpor;
          return {
            title: `Journey Completed`,
            subtitle: `This flight is expected to have landed in ${flightDetails == null || (_flightDetails$airpor = flightDetails.airportDetails) == null ? undefined : _flightDetails$airpor.code} as scheduled.`
          };
        }
        if (getMyTripData.flightStatus === _flightInfo.FlightJourneyEnumStatus.DEPARTED) {
          var finalEstimatedLocalDate = (getMyTripData == null ? undefined : getMyTripData.actualArrivalLocalDate) || (getMyTripData == null ? undefined : getMyTripData.estimatedArrivalLocalDate) || (getMyTripData == null ? undefined : getMyTripData.scheduledArrivalLocalDate);
          var estimatedLocalTimeStamp = _momentTimezone.default.tz(finalEstimatedLocalDate, _dateTime.DateFormats.DateTimeSeconds, getMyTripData.arrivalTimezone);
          var currentLocalTimeStamp = (0, _momentTimezone.default)().tz(getMyTripData.arrivalTimezone);
          if (!estimatedLocalTimeStamp.isValid() || !currentLocalTimeStamp.isValid()) {
            return null;
          }
          var diff = (0, _momentTimezone.default)(estimatedLocalTimeStamp).diff(currentLocalTimeStamp, "minutes");
          if (diff <= STATUS_TIME_PLACEHOLDER) {
            var _flightDetails$airpor2;
            return {
              title: "Landing Soon",
              subtitle: `This flight is approaching ${flightDetails == null || (_flightDetails$airpor2 = flightDetails.airportDetails) == null ? undefined : _flightDetails$airpor2.code}`
            };
          }
        }
        if (/departed/gim.test(flightDetails == null || (_flightDetails$flight = flightDetails.flightStatus) == null ? undefined : _flightDetails$flight.toLowerCase())) {
          var scheduledDateTime = _momentTimezone.default.tz(`${flightDetails == null ? undefined : flightDetails.scheduledDate} ${flightDetails == null ? undefined : flightDetails.scheduledTime}`, "Asia/Singapore");
          var displayTime = _momentTimezone.default.tz(flightDetails == null ? undefined : flightDetails.displayTimestamp, _dateTime.DateFormats.DateTimeSeconds, "Asia/Singapore");
          if (!scheduledDateTime.isValid() || !displayTime.isValid()) {
            return null;
          }
          var _diff = (0, _momentTimezone.default)(scheduledDateTime).diff(displayTime, "minutes");
          if (_diff < -15) {
            var _flightDetails$airpor3;
            var diffText = (0, _flyHelper.formatMinutes)(Math.abs(_diff));
            return {
              title: `Departed ${diffText} late`,
              subtitle: `This flight is currently en route to ${flightDetails == null || (_flightDetails$airpor3 = flightDetails.airportDetails) == null ? undefined : _flightDetails$airpor3.code}.`
            };
          } else if (_diff > STATUS_TIME_PLACEHOLDER) {
            var _flightDetails$airpor4;
            var _diffText = (0, _flyHelper.formatMinutes)(Math.abs(_diff));
            return {
              title: `Departed ${_diffText} early`,
              subtitle: `This flight is currently en route to ${flightDetails == null || (_flightDetails$airpor4 = flightDetails.airportDetails) == null ? undefined : _flightDetails$airpor4.code}.`
            };
          } else {
            var _flightDetails$airpor5;
            return {
              title: `Departed`,
              subtitle: `This flight is en route to ${flightDetails == null || (_flightDetails$airpor5 = flightDetails.airportDetails) == null ? undefined : _flightDetails$airpor5.code}.`
            };
          }
        }
        var flightStatusMapping = flightDetails == null || (_flightDetails$status = flightDetails.statusMapping) == null || (_flightDetails$status = _flightDetails$status.details_status_en) == null ? undefined : _flightDetails$status.toLowerCase();
        if (!["cancelled", "go to info counter"].includes(flightStatusMapping)) {
          return {
            title: "Awaiting Departure",
            subtitle: "Tracking will begin after takeoff from SIN."
          };
        }
        return null;
      } else {
        var _flightDetails$flight2;
        if (/landed/gim.test(flightDetails == null || (_flightDetails$flight2 = flightDetails.flightStatus) == null ? undefined : _flightDetails$flight2.toLowerCase())) {
          return {
            title: "Arrived",
            subtitle: "This flight has landed in SIN."
          };
        } else {
          var _displayTime = _momentTimezone.default.tz(flightDetails == null ? undefined : flightDetails.displayTimestamp, _dateTime.DateFormats.DateTimeSeconds, "Asia/Singapore");
          if (!_displayTime.isValid()) {
            return null;
          }
          var currentSgTime = (0, _momentTimezone.default)().tz("Asia/Singapore");
          var _diff2 = (0, _momentTimezone.default)(_displayTime).diff(currentSgTime, "minutes");
          if (_diff2 >= 0 && _diff2 <= STATUS_TIME_PLACEHOLDER) {
            return {
              title: "Arriving Soon",
              subtitle: "This flight is approaching SIN."
            };
          }
          if (getMyTripData.flightStatus === _flightInfo.FlightJourneyEnumStatus.NOT_STARTED) {
            var _flightDetails$airpor6;
            return {
              title: "Awaiting Departure",
              subtitle: `Tracking will begin after takeoff from ${flightDetails == null || (_flightDetails$airpor6 = flightDetails.airportDetails) == null ? undefined : _flightDetails$airpor6.code}.`
            };
          }
          if (getMyTripData.flightStatus === _flightInfo.FlightJourneyEnumStatus.DEPARTED) {
            var actualDepartureLocalDate = (getMyTripData == null ? undefined : getMyTripData.actualDepartureLocalDate) || (getMyTripData == null ? undefined : getMyTripData.estimatedDepartureLocalDate) || (getMyTripData == null ? undefined : getMyTripData.scheduledDepartureLocalDate);
            var actualDepartureLocalTimeStamp = _momentTimezone.default.tz(actualDepartureLocalDate, _dateTime.DateFormats.DateTimeSeconds, getMyTripData == null ? undefined : getMyTripData.departureTimezone);
            var scheduledDepartureLocalTimeStamp = _momentTimezone.default.tz(getMyTripData == null ? undefined : getMyTripData.scheduledDepartureLocalDate, _dateTime.DateFormats.DateTimeSeconds, getMyTripData == null ? undefined : getMyTripData.departureTimezone);
            if (!actualDepartureLocalTimeStamp.isValid() || !scheduledDepartureLocalTimeStamp.isValid()) {
              return null;
            }
            var _diff3 = (0, _momentTimezone.default)(scheduledDepartureLocalTimeStamp).diff(actualDepartureLocalTimeStamp, "minutes");
            if (_diff3 > STATUS_TIME_PLACEHOLDER) {
              return {
                title: `Departed ${(0, _flyHelper.formatMinutes)(Math.abs(_diff3))} early`,
                subtitle: `This flight is currently en route to SIN.`
              };
            } else if (_diff3 < -15) {
              return {
                title: `Departed ${(0, _flyHelper.formatMinutes)(Math.abs(_diff3))} late`,
                subtitle: `This flight is currently en route to SIN.`
              };
            } else {
              return {
                title: "Departed",
                subtitle: `This flight is en route to SIN.`
              };
            }
          }
          return null;
        }
      }
    }, [flightDetails, getMyTripData]);
    var getFlightJourneyData = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* (isShowloading) {
        if ((flightDetails == null ? undefined : flightDetails.direction) === "ARR" ? !(flightDetails != null && flightDetails.originDepDate) : !(flightDetails != null && flightDetails.scheduledDate)) {
          setShowErrPopup(true);
          setErrorType("unableToLoad");
          return;
        }
        if (isShowloading) {
          setIsLoadingFlightJourney(true);
        }
        try {
          var response = yield (0, _flightApi.fetchFlightJourney)({
            flight_number: flightDetails == null ? undefined : flightDetails.flightNumber,
            scheduled_date: (flightDetails == null ? undefined : flightDetails.direction) === "ARR" ? flightDetails == null ? undefined : flightDetails.originDepDate : flightDetails == null ? undefined : flightDetails.scheduledDate,
            direction: (flightDetails == null ? undefined : flightDetails.direction) === "ARR" ? _flightDetailsCard.FlightDetailsCardState.arrival : _flightDetailsCard.FlightDetailsCardState.departure
          });
          if (response.success) {
            setFlightJourneyData(response.data);
          } else {
            setShowErrPopup(true);
            setErrorType("unableToLoad");
          }
        } catch (error) {
          setShowErrPopup(true);
          setErrorType("unableToLoad");
        } finally {
          setIsLoadingFlightJourney(false);
        }
      });
      return function getFlightJourneyData(_x) {
        return _ref2.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      setIsLoadingFlightJourney(isStartShowFlightJourney);
    }, [isStartShowFlightJourney]);
    (0, _react.useEffect)(function () {
      if (isFlightJourneyFullScreen && flightDetails != null && flightDetails.upcomingStatusMapping) {
        var _flightDetails$upcomi;
        var statusNeedIgnore = ["cancelled", "go to info counter"];
        var _isShowTrackingNotAvailable = (statusNeedIgnore == null ? undefined : statusNeedIgnore.includes(flightDetails == null || (_flightDetails$upcomi = flightDetails.upcomingStatusMapping) == null ? undefined : _flightDetails$upcomi.toLowerCase())) || !!(flightDetails != null && flightDetails.via);
        setShowTrackingNotAvailable(_isShowTrackingNotAvailable);
        if (!_isShowTrackingNotAvailable && flightJourneyStatus !== _flightInfo.FlightJourneyEnumStatus.NOT_STARTED) {
          getFlightJourneyData(true);
        } else {
          setIsLoadingFlightJourney(false);
        }
      } else {
        setFlightJourneyData(undefined);
        setIsLoadingFlightJourney(false);
        setShowErrPopup(false);
        setErrorType(null);
        setShowTrackingNotAvailable(false);
        setIsMapLoaded(false);
      }
    }, [isFlightJourneyFullScreen, flightDetails == null ? undefined : flightDetails.upcomingStatusMapping, flightJourneyStatus]);
    (0, _react.useEffect)(function () {
      if (isShowTrackingNotAvailable && isMapLoaded) {
        setShowErrPopup(true);
        setErrorType("trackingNotAvailable");
      }
    }, [isShowTrackingNotAvailable, isMapLoaded]);
    (0, _react.useEffect)(function () {
      if (!showErrPopup) {
        // Set interval to refresh flight journey data every 5 minutes
        var intervalId = setInterval(function () {
          if (isFlightJourneyFullScreen && !isShowTrackingNotAvailable && flightJourneyStatus !== _flightInfo.FlightJourneyEnumStatus.NOT_STARTED) {
            getFlightJourneyData(false);
          }
        }, 300000); // 5 minutes in milliseconds

        // Clean up the interval when component unmounts or showErrPopup changes
        return function () {
          clearInterval(intervalId);
        };
      }
    }, [showErrPopup, isFlightJourneyFullScreen, isShowTrackingNotAvailable, flightJourneyStatus]);
    var changiAirportInfo = (0, _react.useMemo)(function () {
      return {
        lat: 1.3644202,
        lng: 103.9915308,
        name: "SIN | Singapore"
      };
    }, []);
    var foreignAirportInfo = (0, _react.useMemo)(function () {
      var _flightDetails$airpor7, _flightDetails$airpor8, _flightDetails$airpor9, _flightDetails$airpor0;
      return {
        lat: parseFloat((flightDetails == null || (_flightDetails$airpor7 = flightDetails.airportDetails) == null ? undefined : _flightDetails$airpor7.lat) || "0"),
        lng: parseFloat((flightDetails == null || (_flightDetails$airpor8 = flightDetails.airportDetails) == null ? undefined : _flightDetails$airpor8.lng) || "0"),
        name: `${flightDetails == null || (_flightDetails$airpor9 = flightDetails.airportDetails) == null ? undefined : _flightDetails$airpor9.code} | ${flightDetails == null || (_flightDetails$airpor0 = flightDetails.airportDetails) == null ? undefined : _flightDetails$airpor0.name}`
      };
    }, [flightDetails == null || (_flightDetails$airpor1 = flightDetails.airportDetails) == null ? undefined : _flightDetails$airpor1.lat, flightDetails == null || (_flightDetails$airpor10 = flightDetails.airportDetails) == null ? undefined : _flightDetails$airpor10.lng, flightDetails == null || (_flightDetails$airpor11 = flightDetails.airportDetails) == null ? undefined : _flightDetails$airpor11.code, flightDetails == null || (_flightDetails$airpor12 = flightDetails.airportDetails) == null ? undefined : _flightDetails$airpor12.name]);
    (0, _react.useEffect)(function () {
      var isForeignValid = foreignAirportInfo.lat !== 0 && foreignAirportInfo.lng !== 0;
      if (isMapLoaded && isForeignValid && mapRef.current && !showErrPopup) {
        mapRef.current.fitToCoordinates([{
          latitude: changiAirportInfo.lat,
          longitude: changiAirportInfo.lng
        }, {
          latitude: foreignAirportInfo.lat,
          longitude: foreignAirportInfo.lng
        }], {
          edgePadding: {
            top: 60,
            right: 80,
            bottom: 90 + (0, _utils.getNavBarHeight)(),
            left: 80
          },
          animated: true
        });
      }
    }, [isMapLoaded, foreignAirportInfo.lat, showErrPopup, mapRef.current]);
    var errorDescription = (0, _react.useMemo)(function () {
      if (errorType === "unableToLoad") {
        return (0, _i18n.translate)("flightDetailV2.flightJourney.error.unableToLoad");
      } else if (errorType === "trackingNotAvailable") {
        return (0, _i18n.translate)("flightDetailV2.flightJourney.error.trackingNotAvailable");
      }
      return "";
    }, [errorType]);
    var errorTitleBtn = (0, _react.useMemo)(function () {
      if (errorType === "unableToLoad") {
        return (0, _i18n.translate)("flightDetailV2.flightJourney.buttons.tryAgain");
      } else if (errorType === "trackingNotAvailable") {
        return (0, _i18n.translate)("flightDetailV2.flightJourney.buttons.close");
      }
      return "";
    }, [errorType]);
    var flightPositions = (0, _react.useMemo)(function () {
      if (!(flightJourneyData != null && flightJourneyData.positions)) {
        return [];
      }
      if (flightJourneyStatus === _flightInfo.FlightJourneyEnumStatus.NOT_STARTED) {
        return [];
      }
      var flightPositions = flightJourneyData.positions;
      if ((flightDetails == null ? undefined : flightDetails.direction) === "DEP") {
        // add changiAirportInfo in the first list position
        flightPositions = [{
          lat: changiAirportInfo.lat,
          long: changiAirportInfo.lng,
          date: ""
        }].concat((0, _toConsumableArray2.default)(flightJourneyData.positions));
      } else {
        // add foreignAirportInfo in the first list position
        flightPositions = [{
          lat: foreignAirportInfo.lat,
          long: foreignAirportInfo.lng,
          date: ""
        }].concat((0, _toConsumableArray2.default)(flightJourneyData.positions));
      }
      if (flightJourneyStatus === _flightInfo.FlightJourneyEnumStatus.LANDED) {
        if ((flightDetails == null ? undefined : flightDetails.direction) === "DEP") {
          flightPositions = [].concat((0, _toConsumableArray2.default)(flightPositions), [{
            lat: foreignAirportInfo.lat,
            long: foreignAirportInfo.lng,
            date: ""
          }]);
        } else {
          flightPositions = [].concat((0, _toConsumableArray2.default)(flightPositions), [{
            lat: changiAirportInfo.lat,
            long: changiAirportInfo.lng,
            date: ""
          }]);
        }
      }
      return flightPositions;
    }, [flightJourneyData == null ? undefined : flightJourneyData.positions, flightJourneyStatus, flightDetails == null ? undefined : flightDetails.direction, foreignAirportInfo.lat, changiAirportInfo.lat]);
    var onPressClosePopup = function onPressClosePopup() {
      setShowErrPopup(false);
      if (errorType === "unableToLoad") {
        getFlightJourneyData(true);
      } else {
        handleHide();
      }
    };
    var renderAirlineHeaderLoading = function renderAirlineHeaderLoading() {
      if (isLoadingFlightJourney) {
        return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
          children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: lighterGreyLoadingColors,
            shimmerStyle: _flightJourney.styles.airlineTitleLoading
          })
        });
      }
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: [_flightJourney.styles.airlineTitleLoading, {
          height: undefined
        }],
        testID: `${SCREEN_NAME}__AirlineTitleLoading`,
        accessibilityLabel: `${SCREEN_NAME}__AirlineTitleLoading`,
        children: !errorType && (flightJourneyData == null ? undefined : flightJourneyData.equipment_name) && (0, _jsxRuntime.jsx)(_text.Text, {
          text: `for ${flightJourneyData == null ? undefined : flightJourneyData.equipment_name}`,
          preset: "caption1Regular",
          style: _flightJourney.styles.airlineTitle,
          testID: `${SCREEN_NAME}__AirlineName`,
          accessibilityLabel: `${SCREEN_NAME}__AirlineName`
        })
      });
    };

    // Animation value for the close button
    var opacity = (0, _reactNativeReanimated.useSharedValue)(0);

    // Update opacity based on isStartShowFlightJourney changes
    (0, _react.useEffect)(function () {
      if (isStartShowFlightJourney) {
        opacity.value = (0, _reactNativeReanimated.withTiming)(1, {
          duration: ANIMATED_TIME_DURATION
        });
      } else {
        opacity.value = (0, _reactNativeReanimated.withTiming)(0, {
          duration: ANIMATED_TIME_DURATION
        });
      }
    }, [isStartShowFlightJourney]);

    // Create animated style once
    var animatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var flightJourneyTsx1 = function flightJourneyTsx1() {
        return {
          opacity: opacity.value
        };
      };
      flightJourneyTsx1.__closure = {
        opacity: opacity
      };
      flightJourneyTsx1.__workletHash = 13797666687337;
      flightJourneyTsx1.__initData = _worklet_13797666687337_init_data;
      return flightJourneyTsx1;
    }());

    // Render the close button separately
    var renderCloseButton = function renderCloseButton() {
      return (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
        style: [_flightJourney.styles.containerCloseBtn, {
          top: top
        }, animatedStyle],
        children: (0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
          testID: `${SCREEN_NAME}__ContainerCloseBtn`,
          accessibilityLabel: `${SCREEN_NAME}__ContainerCloseBtn`,
          onPress: handleHide,
          style: _flightJourney.styles.closeBtnIcon,
          children: (0, _jsxRuntime.jsx)(_reactNative.View, {
            testID: `${SCREEN_NAME}__CloseButton`,
            accessibilityLabel: `${SCREEN_NAME}__CloseButton`,
            children: (0, _jsxRuntime.jsx)(_icons.CrossWhite, {})
          })
        })
      });
    };
    var renderHeader = function renderHeader() {
      if (!isStartShowFlightJourney) {
        return (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: _flightJourney.styles.headerContainer,
          testID: `${SCREEN_NAME}__HeaderContainer`,
          accessibilityLabel: `${SCREEN_NAME}__HeaderContainer`,
          onLayout: function onLayout(e) {
            return setHeaderHeight(e.nativeEvent.layout.height);
          },
          children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
            style: _flightJourney.styles.viewTopHeader,
            testID: `${SCREEN_NAME}__ViewTopHeader`,
            accessibilityLabel: `${SCREEN_NAME}__ViewTopHeader`
          }), !isLoadingData ? (0, _jsxRuntime.jsx)(_text.Text, {
            tx: "flightDetailV2.flightJourney.header.title",
            preset: "caption1Bold",
            style: _flightJourney.styles.headerTitle,
            testID: `${SCREEN_NAME}__HeaderTitle`,
            accessibilityLabel: `${SCREEN_NAME}__HeaderTitle`
          }) : (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: lighterGreyLoadingColors,
            shimmerStyle: _flightJourney.styles.headerTitleLoading
          })]
        });
      }
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: [_flightJourney.styles.headerContainerFullScreen, {
          paddingTop: top + 16
        }],
        testID: `${SCREEN_NAME}__HeaderContainerFullScreen`,
        accessibilityLabel: `${SCREEN_NAME}__HeaderContainerFullScreen`,
        onLayout: function onLayout(e) {
          setHeaderHeight(e.nativeEvent.layout.height);
          if (!isLoadingFlightJourney) {
            headerHeightValue.value = (0, _reactNativeReanimated.withDelay)(200, (0, _reactNativeReanimated.withTiming)(e.nativeEvent.layout.height));
          }
        },
        children: (0, _jsxRuntime.jsxs)(_reactNative2.TouchableOpacity, {
          disabled: isLoadingData,
          onPress: handleHide,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            tx: "flightDetailV2.flightJourney.header.title",
            preset: "caption1Bold",
            style: _flightJourney.styles.headerTitleFullScreen,
            testID: `${SCREEN_NAME}__HeaderTitleFullScreen`,
            accessibilityLabel: `${SCREEN_NAME}__HeaderTitleFullScreen`
          }), renderAirlineHeaderLoading()]
        })
      });
    };
    var renderCircle = function renderCircle() {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: _flightJourney.styles.containerCircle,
        children: (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _flightJourney.styles.circle
        })
      });
    };
    var renderMarkers = function renderMarkers() {
      var _flightDetails$airpor13, _flightDetails$airpor14, _flightDetails$airpor15, _flightDetails$airpor16;
      if (!foreignAirportInfo.lng || !foreignAirportInfo.lng || showErrPopup || isShowTrackingNotAvailable) {
        return null;
      }

      // Determine if Changi Airport is above or below the foreign airport
      var isChangiAboveForeign = changiAirportInfo.lat > foreignAirportInfo.lat;

      // Based on the deviation of lng and lat to decide whether the markerLabelContainer for each point
      // is above or below the renderCircle
      // Goal: ensure labels do not cover the flight path
      return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsx)(_reactNativeMaps.Marker, {
          coordinate: {
            latitude: changiAirportInfo.lat,
            longitude: changiAirportInfo.lng
          },
          anchor: {
            x: 0.5,
            y: 0.5
          },
          tappable: false,
          testID: `${SCREEN_NAME}__ChangiMarker`,
          accessibilityLabel: `${SCREEN_NAME}__ChangiMarker`,
          tracksViewChanges: _reactNative.Platform.OS === 'ios',
          children: (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: _flightJourney.styles.markerContainer,
            children: isChangiAboveForeign ? (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
              children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
                style: [_flightJourney.styles.markerLabelContainer, {
                  marginBottom: 60
                }],
                children: [(0, _jsxRuntime.jsx)(_icons.FlightMarker, {}), (0, _jsxRuntime.jsx)(_text.Text, {
                  text: "SIN | Singapore",
                  preset: "caption1Bold",
                  style: _flightJourney.styles.markerLabel
                })]
              }), renderCircle()]
            }) : (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
              children: [renderCircle(), (0, _jsxRuntime.jsxs)(_reactNative.View, {
                style: [_flightJourney.styles.markerLabelContainer, {
                  marginTop: 60
                }],
                children: [(0, _jsxRuntime.jsx)(_icons.FlightMarker, {}), (0, _jsxRuntime.jsx)(_text.Text, {
                  text: "SIN | Singapore",
                  preset: "caption1Bold",
                  style: _flightJourney.styles.markerLabel
                })]
              })]
            })
          })
        }), (0, _jsxRuntime.jsx)(_reactNativeMaps.Marker, {
          coordinate: {
            latitude: foreignAirportInfo.lat,
            longitude: foreignAirportInfo.lng
          },
          anchor: {
            x: 0.5,
            y: 0.5
          },
          tappable: false,
          testID: `${SCREEN_NAME}__ForeignMarker`,
          accessibilityLabel: `${SCREEN_NAME}__ForeignMarker`,
          tracksViewChanges: _reactNative.Platform.OS === 'ios',
          children: (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: _flightJourney.styles.markerContainer,
            children: isChangiAboveForeign ? (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
              children: [renderCircle(), (0, _jsxRuntime.jsxs)(_reactNative.View, {
                style: [_flightJourney.styles.markerLabelContainer, {
                  marginTop: 60
                }],
                children: [(0, _jsxRuntime.jsx)(_icons.FlightMarker, {}), (0, _jsxRuntime.jsx)(_text.Text, {
                  text: `${flightDetails == null || (_flightDetails$airpor13 = flightDetails.airportDetails) == null ? undefined : _flightDetails$airpor13.code} | ${flightDetails == null || (_flightDetails$airpor14 = flightDetails.airportDetails) == null ? undefined : _flightDetails$airpor14.name}`,
                  preset: "caption1Bold",
                  style: _flightJourney.styles.markerLabel
                })]
              })]
            }) : (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
              children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
                style: [_flightJourney.styles.markerLabelContainer, {
                  marginBottom: 60
                }],
                children: [(0, _jsxRuntime.jsx)(_icons.FlightMarker, {}), (0, _jsxRuntime.jsx)(_text.Text, {
                  text: `${flightDetails == null || (_flightDetails$airpor15 = flightDetails.airportDetails) == null ? undefined : _flightDetails$airpor15.code} | ${flightDetails == null || (_flightDetails$airpor16 = flightDetails.airportDetails) == null ? undefined : _flightDetails$airpor16.name}`,
                  preset: "caption1Bold",
                  style: _flightJourney.styles.markerLabel
                })]
              }), renderCircle()]
            })
          })
        })]
      });
    };
    var renderFlightMarker = function renderFlightMarker() {
      if (flightJourneyStatus !== _flightInfo.FlightJourneyEnumStatus.DEPARTED || showErrPopup || isShowTrackingNotAvailable || !(flightJourneyData != null && flightJourneyData.current_lat) || !(flightJourneyData != null && flightJourneyData.current_long)) {
        return null;
      }

      // Calculate bearing based on current position to destination
      var startLat = flightJourneyData.current_lat;
      var startLng = flightJourneyData.current_long;
      var endLat = (flightDetails == null ? undefined : flightDetails.direction) === "ARR" ? changiAirportInfo.lat : foreignAirportInfo.lat;
      var endLng = (flightDetails == null ? undefined : flightDetails.direction) === "ARR" ? changiAirportInfo.lng : foreignAirportInfo.lng;
      var bearing = (0, _flyHelper.calculateBearing)(startLat, startLng, endLat, endLng);
      return (0, _jsxRuntime.jsx)(_reactNativeMaps.Marker, {
        coordinate: {
          latitude: flightJourneyData.current_lat,
          longitude: flightJourneyData.current_long
        },
        anchor: {
          x: 0.5,
          y: 0.5
        },
        tappable: false,
        zIndex: 999 // Add high zIndex to ensure it's always on top
        ,
        testID: `${SCREEN_NAME}__FlightMarker`,
        accessibilityLabel: `${SCREEN_NAME}__FlightMarker`,
        children: (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: [_flightJourney.styles.containerFlightMark, {
            transform: [{
              rotate: `${-mapRotation}deg`
            }]
          }],
          children: (0, _jsxRuntime.jsx)(_icons.FlightMark, {
            style: {
              transform: [{
                rotate: `${bearing}deg`
              }]
            }
          })
        })
      });
    };
    var handleMapRegionChange = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* () {
        var _mapRef$current;
        var camera = yield (_mapRef$current = mapRef.current) == null ? undefined : _mapRef$current.getCamera();
        setMapRotation(camera.heading % 360);
      });
      return function handleMapRegionChange() {
        return _ref3.apply(this, arguments);
      };
    }();
    var renderContent = function renderContent() {
      if (!isStartShowFlightJourney || !flightDetails) {
        return null;
      }
      if (isLoadingFlightJourney) {
        return (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _flightJourney.styles.containerContentLoading,
          testID: `${SCREEN_NAME}__ContainerContentLoading`,
          accessibilityLabel: `${SCREEN_NAME}__ContainerContentLoading`,
          children: (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
            visible: true,
            customStyle: {
              backgroundColor: _theme.color.palette.almostBlackGrey
            }
          })
        });
      }
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _flightJourney.styles.containerContent,
        testID: `${SCREEN_NAME}__ContainerContent`,
        accessibilityLabel: `${SCREEN_NAME}__ContainerContent`,
        children: [(0, _jsxRuntime.jsxs)(_reactNativeMaps.default, {
          testID: `${SCREEN_NAME}__MapView`,
          accessibilityLabel: `${SCREEN_NAME}__MapView`,
          ref: mapRef,
          style: _flightJourney.styles.map,
          initialRegion: {
            latitude: 1.3644202,
            longitude: 103.9915308,
            latitudeDelta: 1,
            longitudeDelta: 1
          },
          onMapReady: function onMapReady() {
            return setIsMapLoaded(true);
          },
          onMapLoaded: function onMapLoaded() {
            return setIsMapLoaded(true);
          },
          showsUserLocation: false,
          showsMyLocationButton: false,
          onRegionChange: handleMapRegionChange,
          children: [renderMarkers(), flightPositions.length > 1 && (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
            children: [(0, _jsxRuntime.jsx)(_reactNativeMaps.Polyline, {
              coordinates: flightPositions.map(function (item) {
                return {
                  latitude: item.lat,
                  longitude: item.long
                };
              }),
              strokeColor: "#7A35B0" // Shadow color
              ,
              strokeWidth: 6 // Wider than the main line
              ,
              lineCap: "round",
              lineJoin: "round",
              geodesic: true,
              zIndex: 0 // Below the main line
              ,
              style: {
                opacity: 0.2
              } // Semi-transparent for shadow effect
            }), (0, _jsxRuntime.jsx)(_reactNativeMaps.Polyline, {
              coordinates: flightPositions.map(function (item) {
                return {
                  latitude: item.lat,
                  longitude: item.long
                };
              }),
              strokeColor: "#ECE0F5" // Purple Primary/200
              ,
              strokeWidth: 4 // 4px as requested
              ,
              lineCap: "round",
              lineJoin: "round",
              geodesic: true,
              zIndex: 1 // Above the shadow
            })]
          }), renderFlightMarker()]
        }), (0, _jsxRuntime.jsx)(_popupJourneyError.default, {
          visible: showErrPopup,
          onPressClosePopup: onPressClosePopup,
          description: errorDescription,
          titleBtn: errorTitleBtn,
          errorType: errorType,
          headerHeight: headerHeight
        })]
      });
    };
    var titleBanner = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var flightJourneyTsx2 = function flightJourneyTsx2() {
        return {
          position: "absolute",
          backgroundColor: "rgba(18, 18, 18, 0.9)",
          left: 0,
          right: 0,
          paddingVertical: 16,
          paddingHorizontal: 24,
          top: headerHeightValue.value,
          opacity: headerHeightValue.value === 0 ? 0 : 1
        };
      };
      flightJourneyTsx2.__closure = {
        headerHeightValue: headerHeightValue
      };
      flightJourneyTsx2.__workletHash = 15402308412334;
      flightJourneyTsx2.__initData = _worklet_15402308412334_init_data;
      return flightJourneyTsx2;
    }());
    var renderStatusContent = function renderStatusContent() {
      var _flightDetails$status2;
      var flightStatusMapping = flightDetails == null || (_flightDetails$status2 = flightDetails.statusMapping) == null || (_flightDetails$status2 = _flightDetails$status2.details_status_en) == null ? undefined : _flightDetails$status2.toLowerCase();
      var hideBannerStatus = ["cancelled", "go to info counter"].includes(flightStatusMapping);
      if (hideBannerStatus) {
        return null;
      }
      if (!isStartShowFlightJourney || !flightDetails || isLoadingFlightJourney || headerHeight === 0 || (0, _lodash.isEmpty)(getMyTripData == null ? undefined : getMyTripData.title)) {
        return null;
      }
      return (0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
        style: titleBanner,
        children: [(0, _jsxRuntime.jsxs)(_text.Text, {
          style: _flightJourney.styles.statusHeader,
          children: ["\u2708 ", journeyStatusFlightDetails ? journeyStatusFlightDetails.title : getMyTripData == null ? undefined : getMyTripData.title]
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: _flightJourney.styles.statusDescription,
          children: journeyStatusFlightDetails ? journeyStatusFlightDetails.subtitle : getMyTripData == null ? undefined : getMyTripData.subtitle
        })]
      });
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: _flightJourney.styles.container,
      testID: `${SCREEN_NAME}__Container`,
      accessibilityLabel: `${SCREEN_NAME}__Container`,
      children: [renderCloseButton(), renderHeader(), renderContent(), renderStatusContent()]
    });
  };
  var _default = exports.default = FlightJourney;
