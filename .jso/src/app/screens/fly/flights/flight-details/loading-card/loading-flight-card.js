  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _constants = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeSizeMatters = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  var width = _reactNative.Dimensions.get('screen').width;
  var container = Object.assign({
    backgroundColor: _theme.color.palette.whiteGrey,
    borderRadius: 16,
    elevation: 5,
    width: width - (0, _reactNativeSizeMatters.scale)(45)
  }, _reactNative.Platform.select({
    ios: {
      shadowRadius: 16,
      shadowOpacity: 0.08,
      shadowOffset: {
        width: 0,
        height: 3
      },
      backgroundColor: _theme.color.palette.whiteGrey
    },
    android: {
      elevation: 3,
      backgroundColor: _theme.color.palette.whiteGrey
    }
  }));
  var titleContainer = {
    flex: 1,
    marginLeft: 16,
    marginTop: 16,
    marginRight: 16
  };
  var bottomContainer = {
    marginLeft: 14,
    marginBottom: 16,
    marginTop: 12,
    marginRight: 12
  };
  var firstRowStyle = {
    flexDirection: "row"
  };
  var columnStyle = {
    flexDirection: "column",
    flex: 1.6,
    paddingEnd: 8,
    marginStart: 2
  };
  var leftColumnStyle = {
    flexDirection: "column",
    flex: 1.5,
    alignSelf: "flex-start"
  };
  var lighterGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var loadingStyle = [{
    width: 232,
    borderRadius: 4,
    height: 18
  }, {
    width: 80,
    height: 7,
    borderRadius: 2,
    backgroundColor: _theme.color.palette.lighterGrey,
    marginBottom: 12
  }, {
    width: 60,
    height: 10,
    borderRadius: 2,
    backgroundColor: _theme.color.palette.lightGrey
  }, {
    width: 80,
    height: 7,
    borderRadius: 2,
    backgroundColor: _theme.color.palette.lighterGrey,
    marginBottom: 12
  }, {
    width: 60,
    height: 10,
    borderRadius: 2,
    backgroundColor: _theme.color.palette.lighterGrey
  }];
  var LoadingFlightCardView = function LoadingFlightCardView() {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: container,
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: titleContainer,
        children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lighterGreyLoadingColors,
          shimmerStyle: loadingStyle[0]
        })
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: bottomContainer,
        children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: firstRowStyle,
          children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: leftColumnStyle,
            children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: lighterGreyLoadingColors,
              shimmerStyle: loadingStyle[1]
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: lighterGreyLoadingColors,
              shimmerStyle: loadingStyle[2]
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: columnStyle,
            children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: lighterGreyLoadingColors,
              shimmerStyle: loadingStyle[3]
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: lighterGreyLoadingColors,
              shimmerStyle: loadingStyle[4]
            })]
          })]
        })
      })]
    });
  };
  var _default = exports.default = LoadingFlightCardView;
