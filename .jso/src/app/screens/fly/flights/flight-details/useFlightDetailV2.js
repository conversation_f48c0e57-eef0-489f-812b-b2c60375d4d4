  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useFlightDetailV2 = exports.FLIGHT_TYPE = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _react = _$$_REQUIRE(_dependencyMap[5]);
  var _useFlightDetail = _$$_REQUIRE(_dependencyMap[6]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _fly = _$$_REQUIRE(_dependencyMap[8]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[9]);
  var _flyRedux = _$$_REQUIRE(_dependencyMap[10]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[11]);
  var _useTickerbandMaintanence = _$$_REQUIRE(_dependencyMap[12]);
  var _adobe = _$$_REQUIRE(_dependencyMap[13]);
  var _flightProps = _$$_REQUIRE(_dependencyMap[14]);
  var _flightDetail = _$$_REQUIRE(_dependencyMap[15]);
  var _aemRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[16]));
  var _mytravelRedux = _$$_REQUIRE(_dependencyMap[17]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[18]));
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[19]));
  var _native = _$$_REQUIRE(_dependencyMap[20]);
  var _i18n = _$$_REQUIRE(_dependencyMap[21]);
  var _reactNativeShare = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[22]));
  var _constants = _$$_REQUIRE(_dependencyMap[23]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[24]);
  var _storage = _$$_REQUIRE(_dependencyMap[25]);
  var _saveFlightTravelOption = _$$_REQUIRE(_dependencyMap[26]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[27]);
  var _envParams = _$$_REQUIRE(_dependencyMap[28]);
  var _lodash = _$$_REQUIRE(_dependencyMap[29]);
  var _feedbackToast = _$$_REQUIRE(_dependencyMap[30]);
  var _flightDetails = _$$_REQUIRE(_dependencyMap[31]);
  var _pageConfigSaga = _$$_REQUIRE(_dependencyMap[32]);
  var _flightApi = _$$_REQUIRE(_dependencyMap[33]);
  var _request = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[34]));
  var _dateTime = _$$_REQUIRE(_dependencyMap[35]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[36]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[37]);
  var _firebase = _$$_REQUIRE(_dependencyMap[38]);
  var _alertApp = _$$_REQUIRE(_dependencyMap[39]);
  var _awsAmplify = _$$_REQUIRE(_dependencyMap[40]);
  var _queries = _$$_REQUIRE(_dependencyMap[41]);
  var _useFlightSaveErrorHandling = _$$_REQUIRE(_dependencyMap[42]);
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[43]);
  var _flightDetailsCard = _$$_REQUIRE(_dependencyMap[44]);
  var _flightDetailV2Helper = _$$_REQUIRE(_dependencyMap[45]);
  var _useModal5 = _$$_REQUIRE(_dependencyMap[46]);
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[47]);
  var _modalManagerRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[48]));
  var _searchRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[49]));
  var _type = _$$_REQUIRE(_dependencyMap[50]);
  var _enum = _$$_REQUIRE(_dependencyMap[51]);
  var _systemRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[52]));
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var FLIGHT_TYPE = exports.FLIGHT_TYPE = {
    CONNECTING: "connecting",
    RETURN: "return"
  };
  var intervalRefreshFlight; // for clear interval
  var FLY_APPSCAPADE_TYPE = "general_entry_point";
  var sourceSystem = _constants.SOURCE_SYSTEM.FLIGHTS;
  var useFlightDetailV2 = exports.useFlightDetailV2 = function useFlightDetailV2(_ref) {
    var _dataCommonAEM$data, _dataCommonAEM$data2, _dataCommonAEM$data3, _dataCommonAEM$data4, _dataCommonAEM$data5, _useContext, _flyFlightDetailsPayl5, _flyFlightDetailsPayl6, _flyFlightDetailsPayl1;
    var flyItem = _ref.flyItem,
      direction = _ref.direction,
      isFromScanBoardingPass = _ref.isFromScanBoardingPass,
      referrer = _ref.referrer,
      toastForSavedFlight = _ref.toastForSavedFlight,
      toastForRemoveFlight = _ref.toastForRemoveFlight,
      isFromUpcomingEvent = _ref.isFromUpcomingEvent,
      priorActionRef = _ref.priorActionRef,
      scrollViewRef = _ref.scrollViewRef;
    var flightDetailV1Hook = (0, _useFlightDetail.useFlightDetail)();
    var dispatch = (0, _reactRedux.useDispatch)();
    var navigation = (0, _native.useNavigation)();
    var isFocused = (0, _native.useIsFocused)();
    var tickerbandMaintananceHook = (0, _useTickerbandMaintanence.useTickerbandMaintanance)(_useTickerbandMaintanence.MaintananceTickerBandType.FLY_DETAILS);
    var dataCommonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig("AEM_COMMON_DATA"));
    var inf22 = dataCommonAEM == null || (_dataCommonAEM$data = dataCommonAEM.data) == null || (_dataCommonAEM$data = _dataCommonAEM$data.informatives) == null ? undefined : _dataCommonAEM$data.find(function (e) {
      return (e == null ? undefined : e.code) === "INF22";
    });
    var msg47 = dataCommonAEM == null || (_dataCommonAEM$data2 = dataCommonAEM.data) == null || (_dataCommonAEM$data2 = _dataCommonAEM$data2.messages) == null ? undefined : _dataCommonAEM$data2.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG47";
    });
    var msg48 = dataCommonAEM == null || (_dataCommonAEM$data3 = dataCommonAEM.data) == null || (_dataCommonAEM$data3 = _dataCommonAEM$data3.messages) == null ? undefined : _dataCommonAEM$data3.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG48";
    });
    var msg58 = dataCommonAEM == null || (_dataCommonAEM$data4 = dataCommonAEM.data) == null || (_dataCommonAEM$data4 = _dataCommonAEM$data4.messages) == null ? undefined : _dataCommonAEM$data4.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG58";
    });
    var msg65 = dataCommonAEM == null || (_dataCommonAEM$data5 = dataCommonAEM.data) == null || (_dataCommonAEM$data5 = _dataCommonAEM$data5.messages) == null ? undefined : _dataCommonAEM$data5.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG65";
    });
    var iconUrl = (0, _mediaHelper.handleImageUrl)(msg47 == null ? undefined : msg47.icon);
    var _useContext$Handlers = (_useContext = (0, _react.useContext)(_fly.FLY_CONTEXT)) == null ? undefined : _useContext.Handlers,
      fly_eci_dynamic_display = _useContext$Handlers.fly_eci_dynamic_display,
      projectFirstFlightJourney = _useContext$Handlers.projectFirstFlightJourney,
      flyLandingFeatureFlag = _useContext$Handlers.flyLandingFeatureFlag,
      flyDelayBenefitFeatureFlag = _useContext$Handlers.flyDelayBenefitFeatureFlag;
    var enableEciDynamicDisplay = (0, _remoteConfig.isFlagOnCondition)(fly_eci_dynamic_display);
    var enableFlightJourney = (0, _remoteConfig.isFlagOnCondition)(projectFirstFlightJourney);
    var enableFlySavePrompt = (0, _remoteConfig.getFeatureFlagInit)(_remoteConfig.REMOTE_CONFIG_FLAGS.FLIGHT_SAVEPROMPT);
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var flyShowTickerBand = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flyShowTickerBand);
    var flyLastUpdatedTimeStamp = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flyLastUpdatedTimeStamp);
    var myTravelFlightsPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.myTravelFlightsPayload);
    var insertFlightPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.insertFlightPayload); //list of saved flights
    var removeFlightPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.removeFlightPayload); //info of flight is unsaved
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var flyCodesPayload = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flyCodesPayload);
    var connectingFlightPayload = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.connectingFlightPayload);
    var callAEMData = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.FLY_APPSCAPADE));
    var flyAppscapadeData = callAEMData == null ? undefined : callAEMData.data;
    var loadingShareRef = (0, _react.useRef)(false);
    var isSharing = (0, _react.useRef)(false);
    var refRetryAction = (0, _react.useRef)(null);
    var toastForRefresh = (0, _react.useRef)(null);
    var mapUnavailable = (0, _react.useRef)(null);
    var unableToLoadLocationRef = (0, _react.useRef)(null);
    var initSelectedTravelOption = direction === _flightProps.FlightDirection.departure ? _saveFlightTravelOption.TravelOption.iAmTravelling : _saveFlightTravelOption.TravelOption.iAmPicking;
    var directionText = (flyItem == null ? undefined : flyItem.direction) === _flightProps.FlightDirection.departure ? "Departure" : "Arrival";
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      showNoInternetError = _useState2[0],
      setShowNoInternetError = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      loadingFlightMap = _useState4[0],
      setLoadingFlightMap = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isNoInternetConnection = _useState6[0],
      setNoInterConnection = _useState6[1];
    var _useState7 = (0, _react.useState)(false),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      loadingSaveFlight = _useState8[0],
      setLoadingSaveFlight = _useState8[1];
    var _useState9 = (0, _react.useState)(false),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      loadingSaveFlightOverlay = _useState0[0],
      setLoadingSaveFlightOverlay = _useState0[1];
    var _useState1 = (0, _react.useState)(initSelectedTravelOption),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      selectedTravelOption = _useState10[0],
      setSelectedTravelOption = _useState10[1];
    var _useModal = (0, _useModal5.useModal)("saveConnectingFlightDetail"),
      isModalVisibleConnectingFlight = _useModal.isModalVisible,
      openModalConnectingFlight = _useModal.openModal,
      closeModalConnectingFlight = _useModal.closeModal;
    var _useModal2 = (0, _useModal5.useModal)("flightDetailSaveTravelOption"),
      isModalVisibleTravelOption = _useModal2.isModalVisible,
      openModalTravelOption = _useModal2.openModal,
      closeModalTravelOption = _useModal2.closeModal;
    var _useState11 = (0, _react.useState)(false),
      _useState12 = (0, _slicedToArray2.default)(_useState11, 2),
      isRefereeModalEverShown = _useState12[0],
      setRefereeModalEverShown = _useState12[1];
    var _useState13 = (0, _react.useState)(null),
      _useState14 = (0, _slicedToArray2.default)(_useState13, 2),
      flyFlightDetailsPayload = _useState14[0],
      setFlyFlightDetailsPayload = _useState14[1];
    var _useState15 = (0, _react.useState)(null),
      _useState16 = (0, _slicedToArray2.default)(_useState15, 2),
      flyFlightDetailsError = _useState16[0],
      setFlyFlightDetailsError = _useState16[1];
    var _useState17 = (0, _react.useState)(null),
      _useState18 = (0, _slicedToArray2.default)(_useState17, 2),
      intoCityOrAirportPayload = _useState18[0],
      setIntoCityOrAirportPayload = _useState18[1];
    var _useState19 = (0, _react.useState)(true),
      _useState20 = (0, _slicedToArray2.default)(_useState19, 2),
      isLoadingDetailFlight = _useState20[0],
      setIsLoadingDetailFlight = _useState20[1];
    var _useState21 = (0, _react.useState)(true),
      _useState22 = (0, _slicedToArray2.default)(_useState21, 2),
      isLoadingIntoCityOrAirport = _useState22[0],
      setIsIntoCityOrAirport = _useState22[1];
    var _useState23 = (0, _react.useState)(null),
      _useState24 = (0, _slicedToArray2.default)(_useState23, 2),
      travelChecklistAEM = _useState24[0],
      setTravelChecklistAEM = _useState24[1];
    var _useState25 = (0, _react.useState)(true),
      _useState26 = (0, _slicedToArray2.default)(_useState25, 2),
      isTravelChecklistAEMLoading = _useState26[0],
      setIsTravelChecklistAEMLoading = _useState26[1];
    var _useState27 = (0, _react.useState)(false),
      _useState28 = (0, _slicedToArray2.default)(_useState27, 2),
      showRemoveFlightAlert = _useState28[0],
      setShowRemoveFlightAlert = _useState28[1];
    var _useState29 = (0, _react.useState)(false),
      _useState30 = (0, _slicedToArray2.default)(_useState29, 2),
      mapRMFlag = _useState30[0],
      setMapRMFlag = _useState30[1];
    var _useState31 = (0, _react.useState)(null),
      _useState32 = (0, _slicedToArray2.default)(_useState31, 2),
      selectedFlightType = _useState32[0],
      setSelectedFlightType = _useState32[1];
    var _useState33 = (0, _react.useState)(false),
      _useState34 = (0, _slicedToArray2.default)(_useState33, 2),
      showCalendarModal = _useState34[0],
      setShowCalendarModal = _useState34[1];
    var _useState35 = (0, _react.useState)(null),
      _useState36 = (0, _slicedToArray2.default)(_useState35, 2),
      getMyTripData = _useState36[0],
      setGetMyTripData = _useState36[1];
    var _useState37 = (0, _react.useState)(false),
      _useState38 = (0, _slicedToArray2.default)(_useState37, 2),
      isLoadingGetMyTripData = _useState38[0],
      setIsLoadingGetMyTripData = _useState38[1];
    var _useState39 = (0, _react.useState)(false),
      _useState40 = (0, _slicedToArray2.default)(_useState39, 2),
      isErrorGetMyTrip = _useState40[0],
      setIsErrorGetMyTrip = _useState40[1];
    var _useState41 = (0, _react.useState)(false),
      _useState42 = (0, _slicedToArray2.default)(_useState41, 2),
      firstTimeLoadFD = _useState42[0],
      setFirstTimeLoadDF = _useState42[1];
    var _useState43 = (0, _react.useState)(false),
      _useState44 = (0, _slicedToArray2.default)(_useState43, 2),
      firstTimeLoadGMT = _useState44[0],
      setFirstTimeLoadGMT = _useState44[1];
    // Track if we need to trigger layout3 after save
    var _useState45 = (0, _react.useState)(null),
      _useState46 = (0, _slicedToArray2.default)(_useState45, 2),
      pendingShowFlightJourney = _useState46[0],
      setPendingShowFlightJourney = _useState46[1];
    var _useState47 = (0, _react.useState)(null),
      _useState48 = (0, _slicedToArray2.default)(_useState47, 2),
      statusSaveAndShare = _useState48[0],
      setStatusSaveAndShare = _useState48[1];
    var _useState49 = (0, _react.useState)(false),
      _useState50 = (0, _slicedToArray2.default)(_useState49, 2),
      loadingFreeFlightDelay = _useState50[0],
      setLoadingFreeFlightDelay = _useState50[1];
    var _useState51 = (0, _react.useState)(false),
      _useState52 = (0, _slicedToArray2.default)(_useState51, 2),
      isBannerAEMLoading = _useState52[0],
      setIsBannerAEMLoading = _useState52[1];
    var _useModal3 = (0, _useModal5.useModal)("modalSaveAndShare"),
      isModalSaveAndShare = _useModal3.isModalVisible,
      openModalSaveAndShare = _useModal3.openModal,
      closeModalSaveAndShare = _useModal3.closeModal;
    var _useModal4 = (0, _useModal5.useModal)("approvalForInformationSharing"),
      closeModalFlightSaver = _useModal4.closeModal;
    var isFlyLandingEnabled = (0, _remoteConfig.getFeatureFlagInit)(_remoteConfig.REMOTE_CONFIG_FLAGS.FLY_LANDING, flyLandingFeatureFlag);
    var _useState53 = (0, _react.useState)(0),
      _useState54 = (0, _slicedToArray2.default)(_useState53, 2),
      floatSaveButtonHeight = _useState54[0],
      setFloatSaveButtonHeight = _useState54[1];
    var attendanceType = (0, _i18n.translate)(selectedTravelOption);
    var isShowBanner = (0, _react.useMemo)(function () {
      var _flyFlightDetailsPayl;
      var isFlightDelayBenefit = (0, _remoteConfig.getFeatureFlagInit)(_remoteConfig.REMOTE_CONFIG_FLAGS.FLIGHT_DELAY_BENEFIT, flyDelayBenefitFeatureFlag);
      return (flyItem == null ? undefined : flyItem.direction) === _flightProps.FlightDirection.departure && isFlightDelayBenefit && (flyFlightDetailsPayload == null || (_flyFlightDetailsPayl = flyFlightDetailsPayload.flightDetailsData) == null || (_flyFlightDetailsPayl = _flyFlightDetailsPayl.flightInsurance) == null ? undefined : _flyFlightDetailsPayl.bannerType) && selectedTravelOption === _saveFlightTravelOption.TravelOption.iAmTravelling;
    }, [flyItem == null ? undefined : flyItem.direction, flyDelayBenefitFeatureFlag, flyFlightDetailsPayload, selectedTravelOption]);
    var clearFlyDetailData = function clearFlyDetailData() {
      dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
      dispatch(_flyRedux.FlyCreators.flyShowModalConfirmSaveFly(false));
      dispatch(_flyRedux.FlyCreators.flyCheckInOnlineLoadFailed(false));
      dispatch(_flyRedux.FlyCreators.getAppscapadeBannerReset());
    };
    var getBackgroundAirportAEM = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* (_ref2) {
        var airport = _ref2.airport;
        try {
          var _env;
          var response = yield (0, _request.default)({
            url: ((_env = (0, _envParams.env)()) == null ? undefined : _env.AEM_URL) + `/bin/ichangi/fly/flight-background-images.${airport}.json`,
            method: "get"
          });
          if (response != null && response.data && response.data.image) {
            return response.data.image;
          }
          return "";
        } catch (error) {
          return "";
        }
      });
      return function getBackgroundAirportAEM(_x) {
        return _ref3.apply(this, arguments);
      };
    }();
    var setErrorhandleGetMyTrip = function setErrorhandleGetMyTrip() {
      if (firstTimeLoadGMT) {
        var _toastForRefresh$curr;
        toastForRefresh == null || (_toastForRefresh$curr = toastForRefresh.current) == null || _toastForRefresh$curr.show(_feedbackToast.DURATION.LENGTH_MAX);
      } else {
        setIsErrorGetMyTrip(true);
      }
    };
    var handleGetMyTrip = /*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* (input) {
        if (!input.departure_date) {
          setErrorhandleGetMyTrip();
          setIsLoadingGetMyTripData(false);
          return;
        }
        try {
          var _env2, _env3, _response$data;
          var response = yield (0, _request.default)({
            url: (_env2 = (0, _envParams.env)()) == null ? undefined : _env2.APPSYNC_GRAPHQL_URL,
            method: "post",
            data: (0, _awsAmplify.graphqlOperation)(_queries.getMyTripQuery, {
              input: input
            }),
            parameters: {},
            headers: {
              "x-api-key": (_env3 = (0, _envParams.env)()) == null ? undefined : _env3.APPSYNC_GRAPHQL_API_KEY
            }
          });
          var result = response == null || (_response$data = response.data) == null || (_response$data = _response$data.data) == null ? undefined : _response$data.getMyTrip;
          if (result && (response == null ? undefined : response.statusCode) === 200 && (result == null ? undefined : result.status) !== "ERROR") {
            setIsErrorGetMyTrip(false);
            setFirstTimeLoadGMT(true);
            setGetMyTripData(result == null ? undefined : result.data);
          } else {
            setErrorhandleGetMyTrip();
          }
        } catch (error) {
          setErrorhandleGetMyTrip();
        } finally {
          setIsLoadingGetMyTripData(false);
        }
      });
      return function handleGetMyTrip(_x2) {
        return _ref4.apply(this, arguments);
      };
    }();

    // isHideLoadingDetail for hide loading skeleton when app auto reload interval
    var onRetryGetMyTripData = function onRetryGetMyTripData(_ref5) {
      var isHideLoadingDetail = _ref5.isHideLoadingDetail;
      if (isSaved) {
        setIsLoadingGetMyTripData(!isHideLoadingDetail);
        var _ref6 = (flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData) || {},
          flightNumber = _ref6.flightNumber,
          scheduledDate = _ref6.scheduledDate,
          airport = _ref6.airport,
          _direction = _ref6.direction,
          originDepDate = _ref6.originDepDate;
        handleGetMyTrip({
          flight_number: flightNumber,
          departure_date: _direction === "ARR" ? originDepDate : scheduledDate,
          airport: _direction === "ARR" ? airport : "SIN"
        });
      }
    };
    (0, _react.useEffect)(function () {
      if (removeFlightPayload != null && removeFlightPayload.isRemovedSuccessFully) {
        var _toastForSavedFlight$, _toastForRemoveFlight;
        (0, _screenHelper.putFlightInfoToAdobeAnalyticAfterSaveUnSave)({
          data: removeFlightPayload,
          isSuccess: true,
          tag: _adobe.AdobeTagName.CAppFlyFlightDetailRemoveFlight,
          flyProfile: "flying",
          pageName: _adobe.AdobeTagName.CAppFlyFlightDetail,
          isSaveFlight: false
        });
        toastForSavedFlight == null || (_toastForSavedFlight$ = toastForSavedFlight.current) == null || _toastForSavedFlight$.closeNow();
        toastForRemoveFlight == null || (_toastForRemoveFlight = toastForRemoveFlight.current) == null || _toastForRemoveFlight.show(_feedbackToast.DURATION.LENGTH_SHORT);
        setSelectedTravelOption(direction === _flightProps.FlightDirection.departure ? _saveFlightTravelOption.TravelOption.iAmTravelling : _saveFlightTravelOption.TravelOption.iAmPicking);
        // setSelectedTopTravelOption(direction === FlightDirection.departure ? TravelOption.iAmTravelling : TravelOption.iAmPicking) //do later
        dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
      }
    }, [removeFlightPayload]);
    (0, _react.useEffect)(function () {
      if (removeFlightPayload != null && removeFlightPayload.isRemovedSuccessFully) {
        try {
          var savedFlightPriorActions = (0, _mmkvStorage.getMMKVdata)(_mmkvStorage.ENUM_STORAGE_MMKV.SAVED_FLIGHT_PRIOR_ACTIONS, _mmkvStorage.ENUM_STORAGE_TYPE.string);
          if (savedFlightPriorActions) {
            var updatedSavedFlightPriorActions = JSON.parse(savedFlightPriorActions).filter(function (action) {
              return (action == null ? undefined : action.uid) === (profilePayload == null ? undefined : profilePayload.id) && (action == null ? undefined : action.direction) === direction && (action == null ? undefined : action.flightNumber) === (flyItem == null ? undefined : flyItem.flightNumber) && (action == null ? undefined : action.scheduledDate) === (flyItem == null ? undefined : flyItem.scheduledDate);
            });
            (0, _mmkvStorage.setMMKVdata)(_mmkvStorage.ENUM_STORAGE_MMKV.SAVED_FLIGHT_PRIOR_ACTIONS, JSON.stringify(updatedSavedFlightPriorActions));
          }
        } catch (error) {
          console.error(`Error to update saved flight prior actions:`, error);
        }
      }
    }, [removeFlightPayload == null ? undefined : removeFlightPayload.isRemovedSuccessFully, profilePayload == null ? undefined : profilePayload.id]);
    (0, _react.useEffect)(function () {
      try {
        var savedFlightPriorActions = (0, _mmkvStorage.getMMKVdata)(_mmkvStorage.ENUM_STORAGE_MMKV.SAVED_FLIGHT_PRIOR_ACTIONS, _mmkvStorage.ENUM_STORAGE_TYPE.string);
        if (savedFlightPriorActions) {
          var getMyTravelFlightDetails = (myTravelFlightsPayload == null ? undefined : myTravelFlightsPayload.getMyTravelFlightDetails) || [];
          var savedFlightPriorActionsArray = JSON.parse(savedFlightPriorActions);
          var updatedSavedFlightPriorActions = savedFlightPriorActionsArray == null || savedFlightPriorActionsArray.filter == null ? undefined : savedFlightPriorActionsArray.filter(function (item) {
            if ((item == null ? undefined : item.uid) === (profilePayload == null ? undefined : profilePayload.id)) {
              var flightDetails = getMyTravelFlightDetails == null || getMyTravelFlightDetails.find == null ? undefined : getMyTravelFlightDetails.find(function (flight) {
                return (flight == null ? undefined : flight.flightNumber) === (item == null ? undefined : item.flightNumber) && (flight == null ? undefined : flight.direction) === (item == null ? undefined : item.direction) && (flight == null ? undefined : flight.scheduledDate) === (item == null ? undefined : item.scheduledDate);
              });
              return !!flightDetails;
            } else {
              return true;
            }
          });
          (0, _mmkvStorage.setMMKVdata)(_mmkvStorage.ENUM_STORAGE_MMKV.SAVED_FLIGHT_PRIOR_ACTIONS, JSON.stringify(updatedSavedFlightPriorActions));
        }
      } catch (error) {
        console.error("Error to update saved flight prior actions:", error);
      }
    }, [myTravelFlightsPayload == null ? undefined : myTravelFlightsPayload.getMyTravelFlightDetails, profilePayload == null ? undefined : profilePayload.id]);
    (0, _react.useEffect)(function () {
      var fetchAtomRMConfig = function fetchAtomRMConfig() {
        var mapFlagEnable = (0, _remoteConfig.isFlagON)(_remoteConfig.REMOTE_CONFIG_FLAGS.ATOMS_MAP);
        setMapRMFlag(mapFlagEnable);
      };
      fetchAtomRMConfig();
    }, []);
    var handleMap = /*#__PURE__*/function () {
      var _ref7 = (0, _asyncToGenerator2.default)(function* (type) {
        var item = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
        var skytrainItem = arguments.length > 2 ? arguments[2] : undefined;
        var isConnection = yield checkInternetConnection();
        if (!isConnection) {
          refRetryAction.current = {
            type: type,
            item: item
          };
          setShowNoInternetError(true);
          return;
        }
        setShowNoInternetError(false);
        refRetryAction.current = null;
        if (!mapRMFlag) {
          return mapUnavailable == null ? undefined : mapUnavailable.current.show();
        }
        var flightDetailsData = flyFlightDetailsPayload.flightDetailsData;
        setLoadingFlightMap(true);
        var input = {
          category: "",
          terminal: flightDetailsData.displayTerminal,
          direction: (flyItem == null ? undefined : flyItem.direction) === "DEP" ? "Departure" : "Arrival",
          name: "",
          gate: undefined
        };
        var isFocusToArea = false; // For navigating to Terminal in ATOM Map
        switch (type) {
          case _flightDetails.TypePressDetailFlightCard.GATE:
            input.category = _flightDetails.TypePressDetailFlightCard.GATE;
            input.name = flightDetailsData.displayGate;
            break;
          case _flightDetails.TypePressDetailFlightCard.CHECK_IN_ROW:
            input.category = _flightDetails.TypePressDetailFlightCard.CHECK_IN_ROW;
            input.name = flightDetailsData.checkInRow;
            break;
          case _flightDetails.TypePressDetailFlightCard.BAGGAGE_BELT:
            input.category = _flightDetails.TypePressDetailFlightCard.BAGGAGE_BELT;
            input.name = flightDetailsData.baggageBelt;
            break;
          case _flightDetails.TypePressDetailFlightCard.TERMINAL:
            input.category = _flightDetails.TypePressDetailFlightCard.TERMINAL;
            input.name = "";
            isFocusToArea = true;
            break;
          case _flightDetails.TypePressDetailFlightCard.SKYTRAIN:
            input.category = _flightDetails.TypePressDetailFlightCard.SKYTRAIN;
            input.name = `T${skytrainItem.terminal} ${skytrainItem == null ? undefined : skytrainItem.type}`;
            input.gate = flightDetailsData.displayGate;
            break;
          case _flightDetails.TypeGetIntoAirport.LINK1:
            input.category = (flyItem == null ? undefined : flyItem.direction) === "DEP" ? "Drop-off" : "Pick-up";
            input.name = item;
            break;
          case _flightDetails.TypeGetIntoAirport.LINK3:
            input.category = (flyItem == null ? undefined : flyItem.direction) === "DEP" ? "Accessible Drop-off" : "Accessibility Pick-up";
            input.name = item;
            break;
          case _flightDetails.TypeGetIntoAirport.LINK2:
            input.category = "Nearest Car Park";
            input.name = flightDetailsData.nearestCarpark;
            break;
        }
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppNavigationMapsEnter, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppNavigationMapsEnter, `${_adobe.AdobeTagName.CAppFlyFlightDetail}|Fly`));
        var fetchData = yield (0, _pageConfigSaga.getLocationMapping)({
          input: input
        });
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppATOMSEntryClick, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppATOMSEntryClick, `${input.direction} Detail Page|${input.name}|${fetchData.local_ref}`));
        navigation.navigate(_constants.NavigationConstants.changiMap, {
          localRef: fetchData.local_ref,
          isFocusToArea: isFocusToArea
        });
        setLoadingFlightMap(false);
      });
      return function handleMap(_x3) {
        return _ref7.apply(this, arguments);
      };
    }();
    var handleGetTravelChecklistAEM = /*#__PURE__*/function () {
      var _ref8 = (0, _asyncToGenerator2.default)(function* () {
        setIsTravelChecklistAEMLoading(true);
        var isDeparture = direction === _flightProps.FlightDirection.departure;
        var isTraveller = selectedTravelOption === _saveFlightTravelOption.TravelOption.iAmTravelling;
        var _flyFlightDetailsPayl2 = flyFlightDetailsPayload.flightDetailsData,
          flightNumber = _flyFlightDetailsPayl2.flightNumber,
          airline = _flyFlightDetailsPayl2.airline,
          displayTerminal = _flyFlightDetailsPayl2.displayTerminal,
          destinationCode = _flyFlightDetailsPayl2.destinationCode,
          departingCode = _flyFlightDetailsPayl2.departingCode;
        var res = yield flightDetailV1Hook.getTravelChecklistAEM({
          flight_no: flightNumber,
          user_profile: isTraveller ? _flightDetail.UserProfileTagNameEnum.TRAVELLER : _flightDetail.UserProfileTagNameEnum.MEETERS_AND_GREETERS,
          direction: isDeparture ? _flightDetail.DirectionTagNameEnum.DEPARTURE : _flightDetail.DirectionTagNameEnum.ARRIVAL,
          airport: isDeparture ? destinationCode : departingCode,
          airline: airline,
          terminal: displayTerminal
        });
        setTravelChecklistAEM(res);
        setIsTravelChecklistAEMLoading(false);
      });
      return function handleGetTravelChecklistAEM() {
        return _ref8.apply(this, arguments);
      };
    }();
    var flightDetailSectionData = (0, _react.useMemo)(function () {
      var _travelChecklistAEM$d;
      if (!(travelChecklistAEM != null && travelChecklistAEM.success) || !(travelChecklistAEM != null && (_travelChecklistAEM$d = travelChecklistAEM.data) != null && (_travelChecklistAEM$d = _travelChecklistAEM$d.sections) != null && _travelChecklistAEM$d.length)) return null;
      return Object.assign({}, travelChecklistAEM.data, {
        sections: travelChecklistAEM.data.sections.sort(function (a, b) {
          return Number(a.sequenceNumber) - Number(b.sequenceNumber);
        })
      });
    }, [travelChecklistAEM]);
    var checkReloadTravelChecklistAEM = (0, _react.useMemo)(function () {
      var _flyFlightDetailsPayl3, _flyFlightDetailsPayl4;
      if (!(flyFlightDetailsPayload != null && flyFlightDetailsPayload.flightDetailsData)) {
        return "";
      }
      return `${flyFlightDetailsPayload == null || (_flyFlightDetailsPayl3 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl3.flightNumber}-${flyFlightDetailsPayload == null || (_flyFlightDetailsPayl4 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl4.scheduledDate}-${direction}`;
    }, [flyFlightDetailsPayload == null || (_flyFlightDetailsPayl5 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl5.flightNumber, flyFlightDetailsPayload == null || (_flyFlightDetailsPayl6 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl6.scheduledDate, direction]);
    (0, _react.useEffect)(function () {
      if (checkReloadTravelChecklistAEM) {
        handleGetTravelChecklistAEM();
      }
    }, [checkReloadTravelChecklistAEM, selectedTravelOption]);
    (0, _useFlightSaveErrorHandling.useFlightSaveErrorHandling)(isSharing.current, statusSaveAndShare ? true : false);
    (0, _react.useEffect)(function () {
      (0, _screenHook.setCurrentScreenActive)(`Flight_Detail_${flyItem == null ? undefined : flyItem.flightNumber}`);
      (0, _adobe.commonTrackingScreen)(`Flight_Detail_${flyItem == null ? undefined : flyItem.flightNumber}`, (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      setIntervalRefeshFlight();
      return function () {
        clearInterval(intervalRefreshFlight);
      };
    }, [isLoggedIn, flyItem == null ? undefined : flyItem.flightNumber, firstTimeLoadFD, firstTimeLoadGMT]);
    (0, _react.useEffect)(function () {
      dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
    }, [isLoggedIn, flyItem == null ? undefined : flyItem.flightNumber]);
    var filterDataForFly = function filterDataForFly(data) {
      var _data$list;
      if ((0, _lodash.isEmpty)(data == null ? undefined : data.list)) return null;
      return data == null || (_data$list = data.list) == null ? undefined : _data$list.find(function (e) {
        return (e == null ? undefined : e.type) === FLY_APPSCAPADE_TYPE;
      });
    };
    (0, _react.useEffect)(function () {
      if (!(callAEMData != null && callAEMData.data)) {
        dispatch(_aemRedux.default.getAemConfigData({
          name: _aemRedux.AEM_PAGE_NAME.FLY_APPSCAPADE,
          pathName: "getFlyAppscapade",
          callBackAfterSuccess: filterDataForFly
        }));
      }
    }, [callAEMData == null ? undefined : callAEMData.data]);
    var setIntervalRefeshFlight = function setIntervalRefeshFlight() {
      var _env4;
      var refreshInterval = (_env4 = (0, _envParams.env)()) == null ? undefined : _env4.FLIGHT_REFRESH_INTERVAL;
      clearInterval(intervalRefreshFlight);
      intervalRefreshFlight = setInterval(function () {
        if (!isFocused) {
          return;
        }
        if (!showNoInternetError) {
          _reactNative.InteractionManager.runAfterInteractions(function () {
            // hide skeleton loading when auto refresh
            refreshFlightDetails();
          });
        }
      }, refreshInterval);
    };
    var getTickerBand = function getTickerBand() {
      dispatch(_aemRedux.default.getAemConfigData({
        name: _aemRedux.AEM_PAGE_NAME.TICKER_BAND_FLIGHT,
        pathName: "getTickerbandFly",
        forceRequest: true
      }));
    };

    // refreshAllData true when user refresh this screen
    var refreshFlightDetails = /*#__PURE__*/function () {
      var _ref9 = (0, _asyncToGenerator2.default)(function* (refreshAllData) {
        var _toastForRefresh$curr3;
        var isConnection = yield checkInternetConnection();
        if (!isConnection) {
          var _toastForRefresh$curr2;
          return toastForRefresh == null || (_toastForRefresh$curr2 = toastForRefresh.current) == null ? undefined : _toastForRefresh$curr2.show(_feedbackToast.DURATION.LENGTH_MAX);
        }
        if (!(0, _lodash.isEmpty)(refRetryAction.current)) {
          var _refRetryAction$curre, _refRetryAction$curre2;
          handleMap((_refRetryAction$curre = refRetryAction.current) == null ? undefined : _refRetryAction$curre.type, (_refRetryAction$curre2 = refRetryAction.current) == null ? undefined : _refRetryAction$curre2.item);
          return;
        }
        if (isModalVisibleTravelOption ||
        //when use is selecting travel option
        isModalVisibleConnectingFlight || showRemoveFlightAlert || insertFlightPayload != null && insertFlightPayload.loading || removeFlightPayload != null && removeFlightPayload.loading) {
          return;
        }
        setIsBannerAEMLoading(refreshAllData);
        toastForRefresh == null || (_toastForRefresh$curr3 = toastForRefresh.current) == null || _toastForRefresh$curr3.closeNow();
        dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
        getTickerBand();
        yield getFlightDetails(!refreshAllData);
        if (refreshAllData) {
          yield Promise.all([handleGetTravelChecklistAEM()]);
        }
        onRetryGetMyTripData({
          isHideLoadingDetail: !refreshAllData
        });
        getIntoCityOrAirport();
        setShowNoInternetError(false);
        setIsBannerAEMLoading(false);
      });
      return function refreshFlightDetails(_x4) {
        return _ref9.apply(this, arguments);
      };
    }();
    var onPressFlightCardLinks = function onPressFlightCardLinks(titleCardLink) {
      var flightNumber = flyItem == null ? undefined : flyItem.flightNumber;
      var flightDate = flyItem == null ? undefined : flyItem.flightDate;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlyFlightDetailFlightCardLinks, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlyFlightDetailFlightCardLinks, `${titleCardLink}|${flightNumber}|${flightDate}`));
    };

    // isHideLoadingDetail for hide loading skeleton when app auto reload interval
    var getFlightDetails = /*#__PURE__*/function () {
      var _ref0 = (0, _asyncToGenerator2.default)(function* (isHideLoadingDetail) {
        var _flyItem$flightNumber;
        setIsLoadingDetailFlight(!isHideLoadingDetail);
        var result = yield (0, _flightApi.fetchFlightDetails)({
          direction: direction,
          flightNumber: flyItem == null ? undefined : flyItem.flightNumber,
          scheduledDate: flyItem == null ? undefined : flyItem.flightDate,
          flightStatus: flyItem == null ? undefined : flyItem.flightStatus,
          airlineCode: flyItem == null || (_flyItem$flightNumber = flyItem.flightNumber) == null ? undefined : _flyItem$flightNumber.substring(0, 2),
          isFromScanBoardingPass: isFromScanBoardingPass,
          flyCodes: flyCodesPayload
        });
        if (result.success) {
          setFlyFlightDetailsError(null);
          setFirstTimeLoadDF(true);
          dispatch(_flyRedux.FlyCreators.flyLastUpdatedTimeStamp((0, _dateTime.flyModuleUpdatedTime)()));
          setIsLoadingDetailFlight(false);
          result.data && setFlyFlightDetailsPayload(result.data);
          dispatch(_flyRedux.FlyCreators.flightDetailPerkRequest());
          if (isFromUpcomingEvent) {
            var _result$data;
            var flightDetailsResult = result == null || (_result$data = result.data) == null ? undefined : _result$data.flightDetailsData;
            (0, _flightDetailV2Helper.handleUpdateUpcomingAndSavedFlight)(flightDetailsResult);
          }
        } else {
          var _toastForRefresh$curr4;
          if (firstTimeLoadFD) {
            setIsLoadingDetailFlight(false);
          }
          setFlyFlightDetailsError(result.error);
          toastForRefresh == null || (_toastForRefresh$curr4 = toastForRefresh.current) == null || _toastForRefresh$curr4.show(_feedbackToast.DURATION.LENGTH_MAX);
          setIsTravelChecklistAEMLoading(false);
        }
      });
      return function getFlightDetails(_x5) {
        return _ref0.apply(this, arguments);
      };
    }();
    var getIntoCityOrAirport = /*#__PURE__*/function () {
      var _ref1 = (0, _asyncToGenerator2.default)(function* () {
        setIsIntoCityOrAirport(true);
        var fetchData = yield (0, _flightApi.fetchIntoCityOrAirportV2)({
          direction: direction,
          flightUniqueId: `${flyItem == null ? undefined : flyItem.flightNumber}_${flyItem == null ? undefined : flyItem.flightDate}`
        });
        setIsIntoCityOrAirport(false);
        if (fetchData.success) {
          setIntoCityOrAirportPayload(fetchData.data);
        }
      });
      return function getIntoCityOrAirport() {
        return _ref1.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      if (flyItem != null && flyItem.flightNumber && flyItem != null && flyItem.flightDate) {
        getFlightDetails();
        getIntoCityOrAirport();
      }
    }, [flyItem == null ? undefined : flyItem.flightNumber, flyItem == null ? undefined : flyItem.flightDate]);
    (0, _react.useEffect)(function () {
      return function () {
        clearFlyDetailData();
      };
    }, []);
    var checkInternetConnection = /*#__PURE__*/function () {
      var _ref10 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        return isConnected;
      });
      return function checkInternetConnection() {
        return _ref10.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      dispatch(_flyRedux.FlyCreators.flyLastUpdatedTimeStamp((0, _dateTime.flyModuleUpdatedTime)()));
      checkInternetConnection().then(function (isConnection) {
        if (!isConnection) {
          setShowNoInternetError(true);
        }
      });
    }, []);

    /**
     * Save flight success response
     */
    (0, _react.useEffect)(function () {
      var _insertFlightPayload$;
      if (insertFlightPayload != null && (_insertFlightPayload$ = insertFlightPayload.insertFlightData) != null && _insertFlightPayload$.success || insertFlightPayload != null && insertFlightPayload.recordExist) {
        (0, _screenHelper.putFlightInfoToAdobeAnalyticAfterSaveUnSave)({
          data: insertFlightPayload,
          isSuccess: true,
          tag: _adobe.AdobeTagName.CAppFlyFlightDetailSaveFlight,
          flyProfile: "flying",
          pageName: _adobe.AdobeTagName.CAppFlyFlightDetail,
          isSaveFlight: true
        });
        if (insertFlightPayload != null && insertFlightPayload.isInsertSuccessfully && isFocused) {
          getFlightDetails();
          if (insertFlightPayload != null && insertFlightPayload.isFlightSaver) {
            setLoadingSaveFlightOverlay(false);
            onProceedFreeFlightDelay();
          } else {
            var _env5, _insertFlightPayload$2;
            setLoadingSaveFlight(false);
            var timeStamp = new Date().getTime();
            // if user hasnt save any flight within 24hours or user has only 1 saved flight
            // show save connecting flight modal
            // else show native share popup and finish save
            var showConnectingFlight = ((0, _mmkvStorage.getLastSavedFlightTime)() + ((_env5 = (0, _envParams.env)()) == null ? undefined : _env5.FLIGHT_SHOW_POPUP_ADD_RETURN) < timeStamp || (0, _lodash.size)(myTravelFlightsPayload == null ? undefined : myTravelFlightsPayload.getMyTravelFlightDetails) === 1) && (insertFlightPayload == null || (_insertFlightPayload$2 = insertFlightPayload.flightData) == null ? undefined : _insertFlightPayload$2.isPassenger);
            if (!showConnectingFlight) {
              if (statusSaveAndShare) {
                setStatusSaveAndShare("SAVE SUCCESS");
                closeModalSaveAndShare();
              } else {
                var _toastForSavedFlight$2;
                closeModalTravelOption();
                toastForSavedFlight == null || (_toastForSavedFlight$2 = toastForSavedFlight.current) == null || _toastForSavedFlight$2.show(_feedbackToast.DURATION.LENGTH_LONG);
                if (pendingShowFlightJourney === "pending") {
                  setPendingShowFlightJourney("finished");
                }
              }
            } else {
              if (statusSaveAndShare) {
                setStatusSaveAndShare("SAVE SUCCESS");
                closeModalSaveAndShare();
              } else {
                dispatch(_searchRedux.default.resetAutoCompleteFlight());
                openModalConnectingFlight();
                (0, _mmkvStorage.setLastSavedFlightTime)(0);
              }
              (0, _storage.save)(_storage.StorageKey.isSaveFlightTriggered, true);
            }
          }
        }
      }
      if (insertFlightPayload != null && insertFlightPayload.errorFlag) {
        setLoadingSaveFlight(false);
        setLoadingSaveFlightOverlay(false);
        closeModalTravelOption();
      }
    }, [insertFlightPayload, statusSaveAndShare]);
    var trackAAWhenBack = function trackAAWhenBack() {
      var flightDirection = flyItem == null ? undefined : flyItem.direction;
      var flightNumber = flyItem == null ? undefined : flyItem.flightNumber;
      var flightDate = flyItem == null ? undefined : flyItem.flightNumber;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlyFlightDetailTopNavigationMenu, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlyFlightDetailTopNavigationMenu, `${flightDirection}|${flightNumber}|${flightDate}`));
    };
    var shouldShowShareButton = (0, _react.useMemo)(function () {
      return !tickerbandMaintananceHook.isShowMaintenance;
    }, [tickerbandMaintananceHook.isShowMaintenance]);
    var flightTerminalDisclaimerText = (0, _react.useMemo)(function () {
      var _flyFlightDetailsPayl7;
      return flyFlightDetailsPayload == null || (_flyFlightDetailsPayl7 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl7.displayTerminalDisclaimer;
    }, [flyFlightDetailsPayload]);
    var shouldShowSQArrivalTerminalInfo = (0, _react.useMemo)(function () {
      return !!flightTerminalDisclaimerText && !!(inf22 != null && inf22.informativeText);
    }, [inf22 == null ? undefined : inf22.informativeText, flightTerminalDisclaimerText]);
    var _useMemo = (0, _react.useMemo)(function () {
        var isPassenger = false;
        var isSaved = false;
        var data = flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData;
        if (!!(myTravelFlightsPayload != null && myTravelFlightsPayload.getMyTravelFlightDetails) && !!data) {
          var idx = myTravelFlightsPayload.getMyTravelFlightDetails.findIndex(function (savedFlight) {
            return savedFlight.flightNumber === data.flightNumber && savedFlight.scheduledDate === data.scheduledDate && (savedFlight.flightDirection || savedFlight.direction) === (data.flightDirection || data.direction);
          });
          if (idx >= 0) {
            isSaved = true;
            isPassenger = myTravelFlightsPayload.getMyTravelFlightDetails[idx].isPassenger;
          }
        }
        return {
          isSaved: isSaved,
          isPassenger: isPassenger
        };
      }, [flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData, myTravelFlightsPayload == null ? undefined : myTravelFlightsPayload.getMyTravelFlightDetails]),
      isSaved = _useMemo.isSaved,
      isPassenger = _useMemo.isPassenger;
    (0, _react.useEffect)(function () {
      if (isSaved) {
        setIsLoadingGetMyTripData(true);
        var _ref11 = (flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData) || {},
          flightNumber = _ref11.flightNumber,
          scheduledDate = _ref11.scheduledDate,
          airport = _ref11.airport,
          _direction2 = _ref11.direction,
          originDepDate = _ref11.originDepDate;
        handleGetMyTrip({
          flight_number: flightNumber,
          departure_date: _direction2 === "ARR" ? originDepDate : scheduledDate,
          airport: _direction2 === "ARR" ? airport : "SIN"
        });
      }
    }, [isSaved, flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData]);
    (0, _react.useEffect)(function () {
      if (!isSaved) return;
      if (!isPassenger) {
        // setSelectedTopTravelOption(TravelOption.iAmPicking) // do later
        setSelectedTravelOption(_saveFlightTravelOption.TravelOption.iAmPicking);
      } else {
        // setSelectedTopTravelOption(TravelOption.iAmTravelling) // do later
        setSelectedTravelOption(_saveFlightTravelOption.TravelOption.iAmTravelling);
      }
    }, [isSaved, isPassenger]);
    var isFlightAfter24h = (0, _react.useMemo)(function () {
      if (flyFlightDetailsPayload != null && flyFlightDetailsPayload.flightDetailsData) {
        var _flyFlightDetailsPayl8 = flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData,
          scheduledDate = _flyFlightDetailsPayl8.scheduledDate,
          scheduledTime = _flyFlightDetailsPayl8.scheduledTime,
          actualTimestamp = _flyFlightDetailsPayl8.actualTimestamp,
          displayTimestamp = _flyFlightDetailsPayl8.displayTimestamp;
        var priorityTime = actualTimestamp || displayTimestamp || `${scheduledDate} ${scheduledTime}`;
        if (priorityTime) {
          var formatedScheduledTime = _moment.default.tz(priorityTime, "YYYY-MM-DD HH:mm", "Asia/Singapore");
          var currentTimeToUTC = (0, _moment.default)().tz("Asia/Singapore");
          var isFlighterAfter24h = (0, _moment.default)(currentTimeToUTC).add(1, "day") <= formatedScheduledTime;
          return isFlighterAfter24h;
        }
        return false;
      }
      return false;
    }, [flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData]);

    /**
     * Display referree popup
     * if flight details is loaded and flight is not saved
     * flight number from referrer link must be the same as in screen
     * flow is from referrer link and referrer is different from referree
     * has appscapade data ready
     * flight is eligible (DEP + after 24h)
     * shown for the first time
     */
    (0, _react.useEffect)(function () {
      var _flyFlightDetailsPayl9, _flyFlightDetailsPayl0;
      if (flyFlightDetailsPayload != null && (_flyFlightDetailsPayl9 = flyFlightDetailsPayload.flightDetailsData) != null && _flyFlightDetailsPayl9.flightNumber && (flyItem == null ? undefined : flyItem.flightNumber) === (flyFlightDetailsPayload == null || (_flyFlightDetailsPayl0 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl0.flightNumber) && referrer && referrer !== (profilePayload == null ? undefined : profilePayload.id) && flyAppscapadeData && flyFlightDetailsPayload != null && flyFlightDetailsPayload.flightDetailsData && !isSaved && isFlightAfter24h && !isRefereeModalEverShown) {
        setRefereeModalEverShown(true);
      }
    }, [flyFlightDetailsPayload == null || (_flyFlightDetailsPayl1 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl1.flightNumber, isSaved, flyItem == null ? undefined : flyItem.flightNumber, referrer, flyAppscapadeData, isFlightAfter24h]);
    var handleMessage48 = function handleMessage48(message, number, place) {
      if (message) {
        return message.replace("<Flight No.>", number).replace("<country>", place);
      }
      return message;
    };
    var handleMessage58 = function handleMessage58(message) {
      if (message) {
        var _flyFlightDetailsPayl10, _status;
        var status = flyFlightDetailsPayload == null || (_flyFlightDetailsPayl10 = flyFlightDetailsPayload.flightDetailsData) == null || (_flyFlightDetailsPayl10 = _flyFlightDetailsPayl10.flightStatus) == null ? undefined : _flyFlightDetailsPayl10.toLowerCase();
        if ((_status = status) != null && _status.includes("cancelled")) {
          status = `been ${status}`;
        }
        return message.replace("<Flight No.>", flyItem == null ? undefined : flyItem.flightNumber).replace("<departed/landed/been cancelled>", status);
      }
      return message;
    };
    var checkFlightCanSave = function checkFlightCanSave(statusTag, newDirection) {
      var _flyFlightDetailsPayl11, _flyFlightDetailsPayl12, _flyFlightDetailsPayl13, _flyFlightDetailsPayl14;
      var status = statusTag == null ? undefined : statusTag.toLowerCase();
      var priorityTime = (flyFlightDetailsPayload == null || (_flyFlightDetailsPayl11 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl11.actualTimestamp) || (flyFlightDetailsPayload == null || (_flyFlightDetailsPayl12 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl12.estimatedTimestamp) || `${flyFlightDetailsPayload == null || (_flyFlightDetailsPayl13 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl13.scheduledDate} ${flyFlightDetailsPayload == null || (_flyFlightDetailsPayl14 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl14.scheduledTime}`;
      var currentTimeToUTC = (0, _moment.default)().tz("Asia/Singapore");
      var flightTime = _moment.default.tz(priorityTime, "YYYY-MM-DD HH:mm", "Asia/Singapore");
      if (newDirection === _flightProps.FlightDirection.departure) {
        switch (true) {
          case /departed/gim.test(status):
          case /cancelled/gim.test(status):
            return false;
          default:
            return true;
        }
      } else {
        switch (true) {
          case /cancelled/gim.test(status):
          case /landed/gim.test(status) && (0, _moment.default)(flightTime).add(1, "hours").format("YYYY-MM-DD HH:mm") < currentTimeToUTC.format("YYYY-MM-DD HH:mm"):
            return false;
          default:
            return true;
        }
      }
    };
    var notAbleToSaveAlert = function notAbleToSaveAlert(alertApp) {
      var _flyFlightDetailsPayl15, _flyItem$flightStatus, _alertApp$current;
      var temp = (flyFlightDetailsPayload == null || (_flyFlightDetailsPayl15 = flyFlightDetailsPayload.flightDetailsData) == null || (_flyFlightDetailsPayl15 = _flyFlightDetailsPayl15.flightStatus) == null ? undefined : _flyFlightDetailsPayl15.split(" ")) || (flyItem == null || (_flyItem$flightStatus = flyItem.flightStatus) == null ? undefined : _flyItem$flightStatus.split(" "));
      var status = (temp == null ? undefined : temp.length) > 0 ? temp[0] : "";
      var message = handleMessage58(msg58 == null ? undefined : msg58.message) || `${(0, _i18n.translate)("flightLanding.flight")} ${flyItem == null ? undefined : flyItem.flightNumber} ${(0, _i18n.translate)("flightLanding.has")} ${status} ${(0, _i18n.translate)("flightLanding.notSaveMessage")}`;
      alertApp == null || (_alertApp$current = alertApp.current) == null || _alertApp$current.show({
        title: (msg58 == null ? undefined : msg58.title) || (0, _i18n.translate)("flightLanding.alert"),
        description: message,
        labelAccept: (msg58 == null ? undefined : msg58.firstButton) || (0, _i18n.translate)("flightLanding.okay"),
        onAccept: function onAccept() {
          return null;
        },
        type: _alertApp.AlertTypes.ALERT
      });
    };
    var onSaveFlight = /*#__PURE__*/function () {
      var _ref12 = (0, _asyncToGenerator2.default)(function* (isRemove, statusPassenger, alertApp, payload) {
        var _flyFlightDetailsPayl16, _flyFlightDetailsPayl17;
        if (!checkFlightCanSave((flyFlightDetailsPayload == null || (_flyFlightDetailsPayl16 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl16.flightStatus) || (flyItem == null ? undefined : flyItem.flightStatus), direction) && !isRemove) {
          notAbleToSaveAlert(alertApp);
          return null;
        }
        var data = {
          enterpriseUserId: profilePayload == null ? undefined : profilePayload.email,
          countryOfResidence: profilePayload == null ? undefined : profilePayload.residentialCountry,
          flightNumber: flyItem == null ? undefined : flyItem.flightNumber,
          flightScheduledDate: (flyFlightDetailsPayload == null || (_flyFlightDetailsPayl17 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl17.scheduledDate) || (flyItem == null ? undefined : flyItem.scheduledDate),
          flightDirection: direction,
          flightPax: statusPassenger
        };
        if (isRemove && isLoggedIn) {
          var _flyFlightDetailsPayl18;
          setShowRemoveFlightAlert(true);
          _reactNative.Alert.alert(msg48 == null ? undefined : msg48.title, handleMessage48(msg48 == null ? undefined : msg48.message, flyItem == null ? undefined : flyItem.flightNumber, flyFlightDetailsPayload == null || (_flyFlightDetailsPayl18 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl18.destinationPlace), [{
            text: msg48 == null ? undefined : msg48.firstButton,
            onPress: function onPress() {
              return setShowRemoveFlightAlert(false);
            }
          }, {
            text: msg48 == null ? undefined : msg48.secondButton,
            style: "cancel",
            onPress: function onPress() {
              var _flyFlightDetailsPayl19, _flyFlightDetailsPayl20;
              var dtAction = (0, _firebase.dtManualActionEvent)(`${_firebase.FE_LOG_PREFIX}App__flight-detail-unsave`);
              dtAction.reportStringValue("flight-detail-unsave-press", `${flyItem == null ? undefined : flyItem.flightNumber}-${(flyFlightDetailsPayload == null || (_flyFlightDetailsPayl19 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl19.scheduledDate) || (flyItem == null ? undefined : flyItem.scheduledDate)}-${direction}-${statusPassenger}`);
              setShowRemoveFlightAlert(false);
              var payloadRemove = Object.assign({}, payload);
              payloadRemove.item.scheduledDate = flyFlightDetailsPayload == null || (_flyFlightDetailsPayl20 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl20.scheduledDate;
              dispatch(_mytravelRedux.MytravelCreators.flyMyTravelRemoveFlightRequest(data, payloadRemove));
              dtAction.leaveAction();
            }
          }]);
        } else {
          if (isLoggedIn) {
            openModalTravelOption();
          } else {
            navigation.navigate(_constants.NavigationConstants.authScreen, {
              sourceSystem: sourceSystem,
              callBackAfterLoginSuccess: function callBackAfterLoginSuccess() {
                getFlightDetails();
                openModalTravelOption();
              },
              callBackAfterLoginCancel: function callBackAfterLoginCancel() {
                return null;
              }
            });
          }
        }
      });
      return function onSaveFlight(_x6, _x7, _x8, _x9) {
        return _ref12.apply(this, arguments);
      };
    }();
    var savedFlightTravelOptionsOnModalHide = function savedFlightTravelOptionsOnModalHide() {
      if (!isSaved) {
        setSelectedTravelOption(initSelectedTravelOption);
      }
    };
    var onClosedTravelOptionSheet = function onClosedTravelOptionSheet() {
      if (!loadingSaveFlight) {
        closeModalTravelOption();
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlightPersonalizationSaveFlight, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlightPersonalizationSaveFlight, `${flyItem == null ? undefined : flyItem.flightNumber} | ${directionText} | ${attendanceType} | ${priorActionRef.current} | Close`));
        priorActionRef.current = "null";
      }
    };
    var onCloseConfirmPopUpSavedFlight = function onCloseConfirmPopUpSavedFlight() {
      closeModalConnectingFlight();
      if (pendingShowFlightJourney === "pending") {
        setPendingShowFlightJourney("finished");
      }
    };
    var updateSavedFlightPriorActions = function updateSavedFlightPriorActions() {
      try {
        var savedFlightPriorActions = (0, _mmkvStorage.getMMKVdata)(_mmkvStorage.ENUM_STORAGE_MMKV.SAVED_FLIGHT_PRIOR_ACTIONS, _mmkvStorage.ENUM_STORAGE_TYPE.string);
        var savedFlightPriorActionsArray = [];
        if (savedFlightPriorActions) {
          var _savedFlightPriorActi;
          try {
            savedFlightPriorActionsArray = JSON.parse(savedFlightPriorActions);
          } catch (e) {
            savedFlightPriorActionsArray = [];
          }
          var priorActionIndex = (_savedFlightPriorActi = savedFlightPriorActionsArray) == null || _savedFlightPriorActi.findIndex == null ? undefined : _savedFlightPriorActi.findIndex(function (action) {
            return (action == null ? undefined : action.uid) === (profilePayload == null ? undefined : profilePayload.id) && (action == null ? undefined : action.flightNumber) === (flyItem == null ? undefined : flyItem.flightNumber) && (action == null ? undefined : action.direction) === direction && (action == null ? undefined : action.scheduledDate) === (flyItem == null ? undefined : flyItem.scheduledDate);
          });
          if (priorActionIndex >= 0) {
            var updatedSavedFlightPriorActions = savedFlightPriorActionsArray.map(function (action, index) {
              if (index === priorActionIndex) {
                return Object.assign({}, action, {
                  priorAction: (priorActionRef == null ? undefined : priorActionRef.current) || (action == null ? undefined : action.priorAction)
                });
              } else {
                return action;
              }
            });
            (0, _mmkvStorage.setMMKVdata)(_mmkvStorage.ENUM_STORAGE_MMKV.SAVED_FLIGHT_PRIOR_ACTIONS, JSON.stringify(updatedSavedFlightPriorActions));
          } else {
            (0, _mmkvStorage.setMMKVdata)(_mmkvStorage.ENUM_STORAGE_MMKV.SAVED_FLIGHT_PRIOR_ACTIONS, JSON.stringify([].concat((0, _toConsumableArray2.default)(savedFlightPriorActionsArray), [{
              direction: direction,
              uid: profilePayload == null ? undefined : profilePayload.id,
              flightNumber: flyItem == null ? undefined : flyItem.flightNumber,
              scheduledDate: flyItem == null ? undefined : flyItem.scheduledDate,
              priorAction: (priorActionRef == null ? undefined : priorActionRef.current) || "null"
            }])));
          }
        } else {
          var storeFlightPriorActions = [{
            direction: direction,
            uid: profilePayload == null ? undefined : profilePayload.id,
            flightNumber: flyItem == null ? undefined : flyItem.flightNumber,
            scheduledDate: flyItem == null ? undefined : flyItem.scheduledDate,
            priorAction: (priorActionRef == null ? undefined : priorActionRef.current) || "null"
          }];
          (0, _mmkvStorage.setMMKVdata)(_mmkvStorage.ENUM_STORAGE_MMKV.SAVED_FLIGHT_PRIOR_ACTIONS, JSON.stringify(storeFlightPriorActions));
        }
        priorActionRef.current = "null";
      } catch (error) {
        console.error("Error updating saved flight prior actions:", error);
      }
    };
    var onPressSavedFlightOnPress = function onPressSavedFlightOnPress(e) {
      isSharing.current = false;
      savedFlightOnPress({}, e);
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlightPersonalizationSaveFlight, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlightPersonalizationSaveFlight, `${flyItem == null ? undefined : flyItem.flightNumber} | ${directionText} | ${attendanceType} | ${priorActionRef.current} | Save`));
      updateSavedFlightPriorActions();
    };
    (0, _react.useEffect)(function () {
      return function () {
        return setSelectedFlightType(null);
      };
    }, []);
    var onButtonPressedConfirmConnectingFlight = function onButtonPressedConfirmConnectingFlight() {
      setSelectedFlightType(FLIGHT_TYPE.RETURN);
      onCloseConfirmPopUpSavedFlight();
    };
    var openSearchDepartureFlightModal = function openSearchDepartureFlightModal() {
      dispatch(_modalManagerRedux.default.openModal("searchDepartureFlight"));
    };
    var showToastForSaveFlightSuccess = function showToastForSaveFlightSuccess() {
      var _toastForSavedFlight$3;
      toastForSavedFlight == null || (_toastForSavedFlight$3 = toastForSavedFlight.current) == null || _toastForSavedFlight$3.show(_feedbackToast.DURATION.LENGTH_LONG);
    };
    var onModalHideConfirmConnectingFlight = function onModalHideConfirmConnectingFlight() {
      dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
      var timeStamp = new Date().getTime();
      (0, _mmkvStorage.setLastSavedFlightTime)(timeStamp);
      if (selectedFlightType) {
        if (flyFlightDetailsPayload.flightDetailsData.direction === _flightProps.FlightDirection.arrival && selectedFlightType === FLIGHT_TYPE.CONNECTING && isFlyLandingEnabled) {
          openSearchDepartureFlightModal();
        } else {
          setShowCalendarModal(true);
        }
        setSelectedFlightType(null);
      } else {
        showToastForSaveFlightSuccess();
      }
    };
    var onButtonPressedConfirmSaveFlight = function onButtonPressedConfirmSaveFlight(payload) {
      var _flyFlightDetailsPayl21, _flyFlightDetailsPayl22, _flyFlightDetailsPayl23;
      var data = {
        enterpriseUserId: profilePayload == null ? undefined : profilePayload.email,
        countryOfResidence: profilePayload == null ? undefined : profilePayload.residentialCountry,
        flightNumber: flyItem == null ? undefined : flyItem.flightNumber,
        flightScheduledDate: flyItem == null ? undefined : flyItem.scheduledDate,
        flightDirection: direction,
        flightPax: selectedTravelOption === _saveFlightTravelOption.TravelOption.iAmTravelling
      };
      payload.item.flightDate = flyFlightDetailsPayload == null || (_flyFlightDetailsPayl21 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl21.scheduledDate;
      payload.item.scheduledDate = flyFlightDetailsPayload == null || (_flyFlightDetailsPayl22 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl22.scheduledDate;
      var dtAction = (0, _firebase.dtManualActionEvent)(`${_firebase.FE_LOG_PREFIX}App__flight-detail-save-confirm`);
      dtAction.reportStringValue("flight-detail-save-confirm-press-flightNumber", `${flyItem == null ? undefined : flyItem.flightNumber}`);
      dtAction.reportStringValue("flight-detail-save-confirm-press-scheduledDate", `${(flyFlightDetailsPayload == null || (_flyFlightDetailsPayl23 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl23.scheduledDate) || (flyItem == null ? undefined : flyItem.scheduledDate)}`);
      dtAction.reportStringValue("flight-detail-save-confirm-press-direction", `${direction}`);
      dtAction.reportStringValue("flight-detail-save-confirm-press-selectedTravelOption", `${selectedTravelOption}`);
      dispatch(_mytravelRedux.MytravelCreators.flyMyTravelInsertFlightRequest(data, payload));
      dispatch(_flyRedux.FlyCreators.flyPendingSaveFlight(true));
      dispatch(_flyRedux.FlyCreators.flyShowModalConfirmSaveFly(false));
      dtAction.leaveAction();
    };
    var onCloseConfirmSaveFlight = function onCloseConfirmSaveFlight() {
      dispatch(_flyRedux.FlyCreators.flyShowModalConfirmSaveFly(false));
    };
    var handleConnectingFlightOnPress = function handleConnectingFlightOnPress() {
      var _flyFlightDetailsPayl24, _flyFlightDetailsPayl25;
      var connectingFlightPayloadData = {
        isConnecting: true,
        flightConnecting: Object.assign({}, flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData, {
          flightDate: flyFlightDetailsPayload == null || (_flyFlightDetailsPayl24 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl24.scheduledDate,
          scheduledDate: (_flyFlightDetailsPayl25 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl25.scheduledDate
        })
      };
      dispatch(_flyRedux.FlyCreators.setConnectingFlightPayload(connectingFlightPayloadData));
      setSelectedFlightType(FLIGHT_TYPE.CONNECTING);
      onCloseConfirmPopUpSavedFlight();
    };
    var onClosedCalendarModal = function onClosedCalendarModal() {
      showToastForSaveFlightSuccess();
      setShowCalendarModal(false);
      var connectingFlightPayloadToClear = {
        isConnecting: false,
        flightConnecting: null
      };
      dispatch(_flyRedux.FlyCreators.setConnectingFlightPayload(connectingFlightPayloadToClear));
    };
    var onDateSelectedAddReturnCalendar = function onDateSelectedAddReturnCalendar(dateString) {
      var date = (0, _moment.default)(dateString);
      setShowCalendarModal(false);
      dispatch(_flyRedux.FlyCreators.setFlightSearchDate(date.format("YYYY-MM-DD")));
      if (isFlyLandingEnabled) {
        var _flyFlightDetailsPayl26;
        var country = (flyFlightDetailsPayload == null || (_flyFlightDetailsPayl26 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl26.country) || "";
        navigation.navigate(_constants.NavigationConstants.searchFlightsV2Result, {
          keyword: country,
          date: date,
          direction: direction === _flightProps.FlightDirection.departure ? _flightProps.FlightDirection.arrival : _flightProps.FlightDirection.departure
        });
      } else {
        var _flyFlightDetailsPayl27;
        var _country = connectingFlightPayload.isConnecting ? "Singapore" : (flyFlightDetailsPayload == null || (_flyFlightDetailsPayl27 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl27.country) || "";
        navigation.navigate("flightResultLandingScreen", {
          screen: "Tabs",
          selectedDate: date,
          country: _country,
          params: {
            screen: direction === _flightProps.FlightDirection.departure ? "arrivalResultScreen" : "departureResultScreen",
            selectedDate: date,
            country: _country
          }
        });
      }
    };
    var savedFlightOnPress = /*#__PURE__*/function () {
      var _ref13 = (0, _asyncToGenerator2.default)(function* (_, travelOption, isFlightSaver, flightSaverPayload) {
        var _flyFlightDetailsPayl28, _flyFlightDetailsPayl29;
        var dtAction = (0, _firebase.dtManualActionEvent)(`${_firebase.FE_LOG_PREFIX}App__flight-detailv2-save`);
        dtAction.reportStringValue("flight-detailv2-save-press-flightNumber", `${flyItem == null ? undefined : flyItem.flightNumber}`);
        dtAction.reportStringValue("flight-detailv2-save-press-scheduledDate", `${(flyFlightDetailsPayload == null || (_flyFlightDetailsPayl28 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl28.scheduledDate) || (flyItem == null ? undefined : flyItem.scheduledDate)}`);
        dtAction.reportStringValue("flight-detailv2-save-press-direction", `${direction}`);
        dtAction.reportStringValue("flight-detailv2-save-press-travelOption", `${travelOption}`);
        // call save
        if (!isFlightSaver) {
          setLoadingSaveFlight(true);
        } else {
          setLoadingSaveFlightOverlay(true);
        }
        var data = {
          enterpriseUserId: profilePayload == null ? undefined : profilePayload.email,
          countryOfResidence: profilePayload == null ? undefined : profilePayload.residentialCountry,
          flightNumber: flyItem == null ? undefined : flyItem.flightNumber,
          flightScheduledDate: (flyFlightDetailsPayload == null || (_flyFlightDetailsPayl29 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl29.scheduledDate) || (flyItem == null ? undefined : flyItem.scheduledDate),
          flightDirection: direction,
          flightPax: travelOption ? travelOption === _saveFlightTravelOption.TravelOption.iAmTravelling : selectedTravelOption === _saveFlightTravelOption.TravelOption.iAmTravelling
        };
        if (isLoggedIn) {
          var _payloadForSave, _payloadForSave2, _payloadForSave3, _payloadForSave4, _payloadForSave5, _payloadForSave6;
          var payloadForSave = {
            item: flyItem,
            referrer: referrer,
            flightNavigationType: _flightProps.FlightNavigationType.FLightSearchDetail,
            isFlightSaver: isFlightSaver
          };
          if (!((_payloadForSave = payloadForSave) != null && (_payloadForSave = _payloadForSave.item) != null && _payloadForSave.scheduledTime)) {
            var _flyFlightDetailsPayl30, _flyFlightDetailsPayl31, _flyFlightDetailsPayl32;
            payloadForSave = Object.assign({}, payloadForSave, {
              item: Object.assign({}, payloadForSave.item, {
                scheduledTime: flyFlightDetailsPayload == null || (_flyFlightDetailsPayl30 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl30.scheduledTime,
                flightDate: flyFlightDetailsPayload == null || (_flyFlightDetailsPayl31 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl31.scheduledDate,
                scheduledDate: flyFlightDetailsPayload == null || (_flyFlightDetailsPayl32 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl32.scheduledDate
              })
            });
          }
          if (referrer) {
            payloadForSave.referrer = referrer;
          }
          // enter flight details via FDL
          if (!((_payloadForSave2 = payloadForSave) != null && (_payloadForSave2 = _payloadForSave2.item) != null && _payloadForSave2.departingCode)) {
            var _flyFlightDetailsPayl33;
            payloadForSave.item.departingCode = flyFlightDetailsPayload == null || (_flyFlightDetailsPayl33 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl33.departingCode;
          }
          if (!((_payloadForSave3 = payloadForSave) != null && (_payloadForSave3 = _payloadForSave3.item) != null && _payloadForSave3.destinationCode)) {
            var _flyFlightDetailsPayl34;
            payloadForSave.item.destinationCode = flyFlightDetailsPayload == null || (_flyFlightDetailsPayl34 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl34.destinationCode;
          }
          if (!((_payloadForSave4 = payloadForSave) != null && (_payloadForSave4 = _payloadForSave4.item) != null && _payloadForSave4.direction)) {
            payloadForSave.item.direction = direction;
          }
          if (!((_payloadForSave5 = payloadForSave) != null && (_payloadForSave5 = _payloadForSave5.item) != null && _payloadForSave5.terminal)) {
            var _flyFlightDetailsPayl35;
            payloadForSave.item.terminal = flyFlightDetailsPayload == null || (_flyFlightDetailsPayl35 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl35.displayTerminal;
          }
          if (!((_payloadForSave6 = payloadForSave) != null && (_payloadForSave6 = _payloadForSave6.item) != null && _payloadForSave6.flightNavigationType)) {
            var _flyFlightDetailsPayl36;
            payloadForSave.flightNavigationType = (flyFlightDetailsPayload == null || (_flyFlightDetailsPayl36 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl36.flightNavigationType) || _flightProps.FlightNavigationType.FLightSearchDetail;
          }
          if (flightSaverPayload) {
            payloadForSave = Object.assign({}, payloadForSave, flightSaverPayload);
          } else {
            payloadForSave = Object.assign({}, payloadForSave, {
              optIn: null,
              alreadySaved: false
            });
          }
          dispatch(_mytravelRedux.MytravelCreators.flyMyTravelInsertFlightRequest(data, payloadForSave));
        } else {
          navigation.navigate(_constants.NavigationConstants.authScreen, {
            sourceSystem: sourceSystem
          });
        }
        dtAction.leaveAction();
      });
      return function savedFlightOnPress(_x0, _x1, _x10, _x11) {
        return _ref13.apply(this, arguments);
      };
    }();
    var onOpenFlightShareSheet = /*#__PURE__*/function () {
      var _ref14 = (0, _asyncToGenerator2.default)(function* (isSavedSuccess) {
        if (loadingFlightMap || loadingShareRef.current) {
          return;
        }
        loadingShareRef.current = true;
        setLoadingFlightMap(true);
        if (!isSavedSuccess) {
          var isConnected = yield checkInternetConnection();
          setNoInterConnection(!isConnected);
          if (!isConnected) {
            setLoadingFlightMap(false);
            loadingShareRef.current = false;
            return;
          }
        }
        var _flyFlightDetailsPayl37 = flyFlightDetailsPayload.flightDetailsData,
          flightNumber = _flyFlightDetailsPayl37.flightNumber,
          departingCode = _flyFlightDetailsPayl37.departingCode,
          destinationCode = _flyFlightDetailsPayl37.destinationCode,
          scheduledDate = _flyFlightDetailsPayl37.scheduledDate,
          scheduledTime = _flyFlightDetailsPayl37.scheduledTime;
        var isDeparture = direction === _flightProps.FlightDirection.departure;
        var options = {
          title: "I would like to share a flight's information with you...",
          message: "I would like to share a flight's information with you...",
          failOnCancel: true
        };
        var shareLinkResponse = yield flightDetailV1Hook.getDeeplinkShare({
          mode: "flight_details",
          flightNo: flightNumber,
          direction: direction,
          scheduledDate: scheduledDate,
          scheduledTime: scheduledTime,
          promoCode: "",
          isPassenger: isPassenger
        });
        setLoadingFlightMap(false);
        loadingShareRef.current = false;
        if (!shareLinkResponse.success) {
          return;
        }
        if (isDeparture) {
          options = {
            title: "I would like to share a flight's information with you...",
            message: (0, _i18n.translate)("flightDetails.share.shareMessageNonEligible", {
              flightNumber: flightNumber,
              departingCode: "SIN",
              destinationCode: destinationCode,
              scheduledDate: `${(0, _moment.default)(`${scheduledDate}`, "YYYY-MM-DD", "Asia/Singapore").format("MMM DD, YYYY")}, at ${(0, _moment.default)(scheduledTime, "HH:mm").format("HH:mm")}`,
              actualTimestamp: ` (Updated ${(0, _moment.default)().tz("Asia/Singapore").format("HH:mm, DD MMM YYYY")})`,
              flightDetailFDL: shareLinkResponse.data
            })
          };
        } else {
          options = {
            title: "I would like to share a flight's information with you...",
            message: (0, _i18n.translate)("flightDetails.share.shareMessageNonEligible", {
              flightNumber: flightNumber,
              departingCode: departingCode,
              destinationCode: destinationCode,
              scheduledDate: `${(0, _moment.default)(`${scheduledDate}`, "YYYY-MM-DD", "Asia/Singapore").format("MMM DD, YYYY")}, at ${(0, _moment.default)(scheduledTime, "HH:mm").format("HH:mm")}`,
              actualTimestamp: ` (Updated ${(0, _moment.default)().tz("Asia/Singapore").format("HH:mm, DD MMM YYYY")})`,
              flightDetailFDL: shareLinkResponse.data
            })
          };
        }
        _reactNative.InteractionManager.runAfterInteractions(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
          try {
            var shareResponse = yield _reactNativeShare.default.open(options);
            isSharing.current = false;
            loadingShareRef.current = false;
            if (shareResponse.success) {
              statusSaveAndShare && finishThreadSaveAndShareModal();
            }
          } catch (error) {
            statusSaveAndShare && finishThreadSaveAndShareModal();
          }
        }));
      });
      return function onOpenFlightShareSheet(_x12) {
        return _ref14.apply(this, arguments);
      };
    }();
    var onSharePress = function onSharePress() {
      onOpenFlightShareSheet();
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlyFlightDetailTopNavigationMenu, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlyFlightDetailTopNavigationMenu, `${direction} | ${flyItem == null ? undefined : flyItem.flightNumber} | Share Button`));
    };
    var onOpenSaveAndShareModal = function onOpenSaveAndShareModal() {
      openModalSaveAndShare();
      setStatusSaveAndShare("START FOLLOW");
    };
    var checkSaveAndShare = function checkSaveAndShare() {
      var _flyFlightDetailsPayl38;
      if (enableFlySavePrompt && checkFlightCanSave((flyFlightDetailsPayload == null || (_flyFlightDetailsPayl38 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl38.flightStatus) || (flyItem == null ? undefined : flyItem.flightStatus), direction) && !tickerbandMaintananceHook.isShowMaintenance) {
        onOpenSaveAndShareModal();
      } else {
        onSharePress();
      }
    };
    var handleSharePress = function handleSharePress() {
      if (isLoggedIn) {
        !isSaved ? checkSaveAndShare() : onSharePress();
      } else {
        clearInterval(intervalRefreshFlight);
        navigation.navigate(_constants.NavigationConstants.authScreen, {
          callBackAfterLoginSuccess: checkSaveAndShare,
          callBackAfterLoginCancel: function callBackAfterLoginCancel() {
            return null;
          }
        });
      }
    };
    var isButtonSaveHidden = (0, _react.useMemo)(function () {
      if (!isLoggedIn) {
        return false;
      }
      if (!(flyFlightDetailsPayload != null && flyFlightDetailsPayload.flightDetailsData)) {
        return true;
      }
      if (tickerbandMaintananceHook.isShowMaintenance) {
        return false;
      }
      return (flyItem == null ? undefined : flyItem.isMSError) || isSaved;
    }, [isLoggedIn, flyItem == null ? undefined : flyItem.isMSError, isSaved, myTravelFlightsPayload == null ? undefined : myTravelFlightsPayload.getMyTravelFlightDetails, flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData]);
    var closeBottomSheetError = function closeBottomSheetError() {
      dispatch(_flyRedux.FlyCreators.flyCheckInOnlineLoadFailed(false));
    };
    var onButtonPressBottomSheetError = function onButtonPressBottomSheetError() {
      if (isLoggedIn) {
        var _flyFlightDetailsPayl39;
        (0, _flightDetailsCard.handleNavigationFlightDetail)(flyFlightDetailsPayload == null || (_flyFlightDetailsPayl39 = flyFlightDetailsPayload.flightDetailsData) == null || (_flyFlightDetailsPayl39 = _flyFlightDetailsPayl39.onlineCheckIn) == null ? undefined : _flyFlightDetailsPayl39.link, isButtonSaveHidden, navigation, dispatch);
        return null;
      }
      dispatch(_flyRedux.FlyCreators.flyPendingCheckInOnline(true));
      navigation.navigate(_constants.NavigationConstants.authScreen, {
        sourceSystem: sourceSystem
      });
    };
    var onCloseModalSaveAndShare = function onCloseModalSaveAndShare() {
      closeModalSaveAndShare();
      setStatusSaveAndShare(null);
    };
    var onShareOnlyPress = function onShareOnlyPress() {
      closeModalSaveAndShare();
      setStatusSaveAndShare("STARTING SHARE");
      onSharePress();
    };
    var finishThreadSaveAndShareModal = function finishThreadSaveAndShareModal() {
      if (statusSaveAndShare === "SAVE SUCCESS") {
        showToastForSaveFlightSuccess();
      }
      setStatusSaveAndShare(null);
    };
    var onProceedFreeFlightDelay = /*#__PURE__*/function () {
      var _ref16 = (0, _asyncToGenerator2.default)(function* () {
        var _flyFlightDetailsPayl41, _flyFlightDetailsPayl42;
        var _flyFlightDetailsPayl40 = flyFlightDetailsPayload == null || (_flyFlightDetailsPayl41 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl41.flightInsurance,
          bannerType = _flyFlightDetailsPayl40.bannerType;
        setLoadingFreeFlightDelay(true);
        var ecid = yield (0, _adobe.getExperienceCloudId)();
        var inputData = {
          ecid: ecid,
          stateCode: bannerType === _type.EnumBannerType.OPT_IN || bannerType === _type.EnumBannerType.NOT_SAVED || !isSaved ? _type.EnumInputStateCode.PROMOLISTING_OPTIN : _type.EnumInputStateCode.PROMOLISTING_OPTOUT,
          staff: profilePayload != null && profilePayload.airportStaff ? 1 : 0
        };
        if (flyItem != null && flyItem.flightDate) {
          inputData.flightDt = flyItem == null ? undefined : flyItem.flightDate;
        }
        if (flyItem != null && flyItem.flightNumber) {
          inputData.flightNo = flyItem == null ? undefined : flyItem.flightNumber;
        }
        if (flyFlightDetailsPayload != null && (_flyFlightDetailsPayl42 = flyFlightDetailsPayload.flightDetailsData) != null && (_flyFlightDetailsPayl42 = _flyFlightDetailsPayl42.airportDetails) != null && _flyFlightDetailsPayl42.country_code) {
          var _flyFlightDetailsPayl43;
          inputData.route = flyFlightDetailsPayload == null || (_flyFlightDetailsPayl43 = flyFlightDetailsPayload.flightDetailsData) == null || (_flyFlightDetailsPayl43 = _flyFlightDetailsPayl43.airportDetails) == null ? undefined : _flyFlightDetailsPayl43.country_code;
        }
        try {
          var response = yield (0, _pageConfigSaga.getDeepLinkV2)({
            stateCode: _constants.StateCode.CSM_TSP,
            params: "",
            input: inputData
          }, true);
          if (response != null && response.redirectUri) {
            setLoadingFreeFlightDelay(false);
            navigation.navigate(_constants.NavigationConstants.playpassWebview, {
              uri: response == null ? undefined : response.redirectUri,
              needBackButton: true,
              needCloseButton: true,
              headerType: _enum.WebViewHeaderTypes.default,
              basicAuthCredential: response == null ? undefined : response.basicAuth
            });
          } else {
            throw response;
          }
        } catch (error) {
          setLoadingFreeFlightDelay(false);
          setTimeout(function () {
            dispatch(_systemRedux.default.setBottomSheetErrorData({
              visible: true
            }));
          }, 300);
        }
      });
      return function onProceedFreeFlightDelay() {
        return _ref16.apply(this, arguments);
      };
    }();
    var onProceedSharing = function onProceedSharing(alertApp) {
      var _flyFlightDetailsPayl44, _flyFlightDetailsPayl46;
      if (loadingFlightMap || loadingFreeFlightDelay || loadingSaveFlightOverlay) {
        return;
      }
      closeModalFlightSaver();
      if (!checkFlightCanSave((flyFlightDetailsPayload == null || (_flyFlightDetailsPayl44 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl44.flightStatus) || (flyItem == null ? undefined : flyItem.flightStatus), direction) && !isSaved) {
        notAbleToSaveAlert(alertApp);
        return;
      }
      var _flyFlightDetailsPayl45 = flyFlightDetailsPayload == null || (_flyFlightDetailsPayl46 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl46.flightInsurance,
        bannerType = _flyFlightDetailsPayl45.bannerType;
      savedFlightOnPress({}, _saveFlightTravelOption.TravelOption.iAmTravelling, true, {
        optIn: bannerType === _type.EnumBannerType.OPT_IN || bannerType === _type.EnumBannerType.NOT_SAVED || !isSaved,
        alreadySaved: isSaved
      });
    };
    var scrollToTop = function scrollToTop() {
      scrollViewRef.current.scrollTo({
        y: 0,
        animated: true
      });
    };
    (0, _react.useEffect)(function () {
      if (statusSaveAndShare && insertFlightPayload != null && insertFlightPayload.errorFlag) {
        onShareOnlyPress();
      }
    }, [insertFlightPayload == null ? undefined : insertFlightPayload.errorFlag]);
    (0, _react.useEffect)(function () {
      if (statusSaveAndShare === "SAVE SUCCESS") {
        onSharePress();
      }
    }, [statusSaveAndShare]);
    return {
      flightDetailV1Hook: flightDetailV1Hook,
      tickerbandMaintananceHook: tickerbandMaintananceHook,
      trackAAWhenBack: trackAAWhenBack,
      shouldShowShareButton: shouldShowShareButton,
      isLoggedIn: isLoggedIn,
      flyShowTickerBand: flyShowTickerBand,
      flyFlightDetailsPayload: flyFlightDetailsPayload,
      flyLastUpdatedTimeStamp: flyLastUpdatedTimeStamp,
      shouldShowSQArrivalTerminalInfo: shouldShowSQArrivalTerminalInfo,
      inf22: inf22,
      showNoInternetError: showNoInternetError,
      handleSharePress: handleSharePress,
      loadingSaveFlight: loadingSaveFlight,
      insertFlightPayload: insertFlightPayload,
      isNoInternetConnection: isNoInternetConnection,
      onOpenFlightShareSheet: onOpenFlightShareSheet,
      loadingFlightMap: loadingFlightMap,
      handleMap: handleMap,
      isLoadingDetailFlight: isLoadingDetailFlight,
      isLoadingIntoCityOrAirport: isLoadingIntoCityOrAirport,
      intoCityOrAirportPayload: intoCityOrAirportPayload,
      getBackgroundAirportAEM: getBackgroundAirportAEM,
      isSaved: isSaved,
      isButtonSaveHidden: isButtonSaveHidden,
      checkFlightCanSave: checkFlightCanSave,
      travelChecklistAEM: travelChecklistAEM,
      isTravelChecklistAEMLoading: isTravelChecklistAEMLoading,
      handleGetTravelChecklistAEM: handleGetTravelChecklistAEM,
      unableToLoadLocationRef: unableToLoadLocationRef,
      mapUnavailable: mapUnavailable,
      refreshFlightDetails: refreshFlightDetails,
      toastForRefresh: toastForRefresh,
      flyFlightDetailsError: flyFlightDetailsError,
      enableEciDynamicDisplay: enableEciDynamicDisplay,
      flightDetailSectionData: flightDetailSectionData,
      onPressFlightCardLinks: onPressFlightCardLinks,
      setSelectedTravelOption: setSelectedTravelOption,
      selectedTravelOption: selectedTravelOption,
      onSaveFlight: onSaveFlight,
      savedFlightTravelOptionsOnModalHide: savedFlightTravelOptionsOnModalHide,
      modalTravelOptionVisible: isModalVisibleTravelOption,
      onClosedTravelOptionSheet: onClosedTravelOptionSheet,
      savedFlightOnPress: savedFlightOnPress,
      isModalVisible: isModalVisibleConnectingFlight,
      isFocused: isFocused,
      onCloseConfirmPopUpSavedFlight: onCloseConfirmPopUpSavedFlight,
      msg47: msg47,
      onPressSavedFlightOnPress: onPressSavedFlightOnPress,
      onButtonPressedConfirmConnectingFlight: onButtonPressedConfirmConnectingFlight,
      onModalHideConfirmConnectingFlight: onModalHideConfirmConnectingFlight,
      handleConnectingFlightOnPress: handleConnectingFlightOnPress,
      showCalendarModal: showCalendarModal,
      onClosedCalendarModal: onClosedCalendarModal,
      onDateSelectedAddReturnCalendar: onDateSelectedAddReturnCalendar,
      removeFlightPayload: removeFlightPayload,
      isMSError: flyItem == null ? undefined : flyItem.isMSError,
      handleGetMyTrip: handleGetMyTrip,
      getMyTripData: getMyTripData,
      onRetryGetMyTripData: onRetryGetMyTripData,
      isLoadingGetMyTripData: isLoadingGetMyTripData,
      isErrorGetMyTrip: isErrorGetMyTrip,
      iconUrl: iconUrl,
      onButtonPressedConfirmSaveFlight: onButtonPressedConfirmSaveFlight,
      onCloseConfirmSaveFlight: onCloseConfirmSaveFlight,
      msg65: msg65,
      closeBottomSheetError: closeBottomSheetError,
      onButtonPressBottomSheetError: onButtonPressBottomSheetError,
      enableFlightJourney: enableFlightJourney,
      pendingShowFlightJourney: pendingShowFlightJourney,
      setPendingShowFlightJourney: setPendingShowFlightJourney,
      isFlyLandingEnabled: isFlyLandingEnabled,
      isModalSaveAndShare: isModalSaveAndShare,
      onCloseModalSaveAndShare: onCloseModalSaveAndShare,
      onShareOnlyPress: onShareOnlyPress,
      showToastForSaveFlightSuccess: showToastForSaveFlightSuccess,
      onProceedFreeFlightDelay: onProceedFreeFlightDelay,
      loadingFreeFlightDelay: loadingFreeFlightDelay,
      isShowBanner: isShowBanner,
      onProceedSharing: onProceedSharing,
      loadingSaveFlightOverlay: loadingSaveFlightOverlay,
      setIsBannerAEMLoading: setIsBannerAEMLoading,
      isBannerAEMLoading: isBannerAEMLoading,
      floatSaveButtonHeight: floatSaveButtonHeight,
      setFloatSaveButtonHeight: setFloatSaveButtonHeight,
      scrollToTop: scrollToTop
    };
  };
