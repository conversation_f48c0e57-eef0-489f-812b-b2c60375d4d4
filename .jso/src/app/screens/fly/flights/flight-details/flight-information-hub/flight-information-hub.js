  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.commonShadow = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[2]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _text2 = _$$_REQUIRE(_dependencyMap[6]);
  var _icons = _$$_REQUIRE(_dependencyMap[7]);
  var _reactNativeSizeMatters = _$$_REQUIRE(_dependencyMap[8]);
  var _utils = _$$_REQUIRE(_dependencyMap[9]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  var FlightInformationHub = function FlightInformationHub(_ref) {
    var children = _ref.children,
      setShowModalTravelOption = _ref.setShowModalTravelOption,
      selectedTravelOption = _ref.selectedTravelOption,
      loadingFlightDetail = _ref.loadingFlightDetail,
      isFlightSaved = _ref.isFlightSaved,
      isFlightJourneyFullScreen = _ref.isFlightJourneyFullScreen,
      handleHideFlightJourney = _ref.handleHideFlightJourney;
    return (0, _jsxRuntime.jsxs)(_reactNative2.TouchableOpacity, {
      style: [styles.container, {
        paddingBottom: (isFlightSaved ? (0, _reactNativeSizeMatters.scale)(56) : (0, _reactNativeSizeMatters.scale)(90)) + (0, _utils.getNavBarHeight)()
      }],
      disabled: !isFlightJourneyFullScreen,
      onPress: handleHideFlightJourney,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.TouchableOpacity, {
        style: styles.header,
        onPress: function onPress() {
          return setShowModalTravelOption(true);
        },
        children: [(0, _jsxRuntime.jsx)(_text2.Text, {
          tx: selectedTravelOption,
          style: styles.headerText
        }), (0, _jsxRuntime.jsx)(_icons.DownArrowBlack, {})]
      }), children]
    });
  };
  var _default = exports.default = FlightInformationHub;
  var commonShadow = exports.commonShadow = {
    shadowColor: "rgba(18, 18, 18, 0.08)",
    shadowOffset: {
      width: 0,
      height: 6
    },
    shadowOpacity: 0.08,
    shadowRadius: 20,
    elevation: 6
  };
  var styles = _reactNative.StyleSheet.create({
    container: Object.assign({
      paddingBottom: (0, _reactNativeSizeMatters.scale)(90) + (0, _utils.getNavBarHeight)(),
      width: "100%",
      backgroundColor: _theme.color.palette.lightestGrey,
      borderTopRightRadius: 30,
      borderTopLeftRadius: 30
    }, _reactNative.Platform.select({
      android: {
        elevation: 6
      },
      ios: {
        shadowColor: _theme.color.palette.almostBlackGrey,
        shadowOffset: {
          width: 0,
          height: -10
        },
        shadowOpacity: 0.3,
        shadowRadius: 24
      }
    })),
    header: {
      flexDirection: "row",
      columnGap: 8,
      alignItems: "center",
      width: "100%",
      justifyContent: "center",
      marginTop: 34,
      marginBottom: 14
    },
    headerText: Object.assign({}, _text.newPresets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey
    })
  });
