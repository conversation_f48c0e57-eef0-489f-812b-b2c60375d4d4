  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _icons = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[8]);
  var _constants = _$$_REQUIRE(_dependencyMap[9]);
  var _i18n = _$$_REQUIRE(_dependencyMap[10]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  var TransportItem = function TransportItem(_ref) {
    var _mapping$keyString2;
    var keyString = _ref.keyString,
      onSendTrackingData = _ref.onSendTrackingData;
    var navigation = (0, _navigationHelper.useNavigation)();
    var mapping = (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({}, 'betweenT123', {
      icon: (0, _jsxRuntime.jsx)(_icons.FlightT1, {}),
      uri: 'https://www.changiairport.com/en/at-changi/transport-and-directions/transferring-between-terminals-jewel.html?linkedListSection=t1?src=app'
    }), 'toFromJewel', {
      icon: (0, _jsxRuntime.jsx)(_icons.FlightJewel, {}),
      uri: 'https://www.changiairport.com/en/at-changi/transport-and-directions/transferring-between-terminals-jewel.html?linkedListSection=Jewel?src=app'
    }), 'toFromT4', {
      icon: (0, _jsxRuntime.jsx)(_icons.FlightT4, {}),
      uri: 'https://airportbus.plotigo.app/'
    });
    var navigateToMappingObject = function navigateToMappingObject() {
      var _mapping$keyString;
      onSendTrackingData == null || onSendTrackingData((0, _i18n.translate)(`flightDetailV2.flightInformationHub.sections.transferInfo.${keyString}`));
      navigation.navigate(_constants.NavigationConstants.webview, {
        uri: (_mapping$keyString = mapping[keyString]) == null ? undefined : _mapping$keyString.uri
      });
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.TouchableOpacity, {
      style: styles.transportItem,
      onPress: navigateToMappingObject,
      children: [(_mapping$keyString2 = mapping[keyString]) == null ? undefined : _mapping$keyString2.icon, (0, _jsxRuntime.jsx)(_text.Text, {
        tx: `flightDetailV2.flightInformationHub.sections.transferInfo.${keyString}`,
        style: styles.transportText
      })]
    });
  };
  var _default = exports.default = TransportItem;
  var styles = _reactNative.StyleSheet.create({
    transportItem: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      columnGap: 12
    },
    transportText: Object.assign({
      flex: 1,
      flexWrap: 'wrap'
    }, _text.newPresets.caption1Bold, {
      color: _theme.color.palette.almostBlackGrey
    })
  });
