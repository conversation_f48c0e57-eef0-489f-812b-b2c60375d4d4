  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[2]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _icons = _$$_REQUIRE(_dependencyMap[4]);
  var _flightDetails = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _i18n = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  var MarkerItem = function MarkerItem(_ref) {
    var type = _ref.type,
      train = _ref.train,
      onPressedMarker = _ref.onPressedMarker,
      onSendTrackingData = _ref.onSendTrackingData;
    var onPressFlightMarker = function onPressFlightMarker() {
      onPressedMarker(_flightDetails.TypePressDetailFlightCard.SKYTRAIN, null, {
        type: type,
        terminal: train
      });
      onSendTrackingData == null || onSendTrackingData((0, _i18n.translate)(`flightDetailV2.flightInformationHub.sections.transferInfo.${type}`));
    };
    return (0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
      disabled: true,
      testID: "button_MarkerItem",
      accessibilityLabel: "button_MarkerItem",
      style: styles.markerItem,
      onPress: onPressFlightMarker,
      children: (0, _jsxRuntime.jsx)(_icons.FlightMarkerNoPadding, {})
    });
  };
  var _default = exports.default = MarkerItem;
  var styles = _reactNative.StyleSheet.create({
    markerItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: _reactNative.Platform.OS === 'android' ? 1 : -1
    },
    infoText: Object.assign({}, _text.newPresets.caption1Regular, {
      color: _theme.color.palette.almostBlackGrey
    })
  });
