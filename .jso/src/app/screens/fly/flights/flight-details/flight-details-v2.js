  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.FlightDetailsV2 = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[7]));
  var _flightInfo = _$$_REQUIRE(_dependencyMap[8]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[9]);
  var _onboardingOverlay = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _backgroundImage = _$$_REQUIRE(_dependencyMap[11]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[12]);
  var _icons = _$$_REQUIRE(_dependencyMap[13]);
  var _singleTapButton = _$$_REQUIRE(_dependencyMap[14]);
  var _flightDetailsV = _$$_REQUIRE(_dependencyMap[15]);
  var _tickerBand = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _useFlightDetailV2 = _$$_REQUIRE(_dependencyMap[17]);
  var _i18n = _$$_REQUIRE(_dependencyMap[18]);
  var _feedbackToast = _$$_REQUIRE(_dependencyMap[19]);
  var _errorOverlayApp = _$$_REQUIRE(_dependencyMap[20]);
  var _flightProps = _$$_REQUIRE(_dependencyMap[21]);
  var _utils = _$$_REQUIRE(_dependencyMap[22]);
  var _saveFlightButton = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[23]));
  var _informationHubCard = _$$_REQUIRE(_dependencyMap[24]);
  var _bottomSheetUnableLoadLocation = _$$_REQUIRE(_dependencyMap[25]);
  var _bottomSheetMapUnavailable = _$$_REQUIRE(_dependencyMap[26]);
  var _flightInformationHub = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[27]));
  var _travelOptions = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[28]));
  var _adobe = _$$_REQUIRE(_dependencyMap[29]);
  var _flightBanner = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[30]));
  var _flightDetail = _$$_REQUIRE(_dependencyMap[31]);
  var _alertApp = _$$_REQUIRE(_dependencyMap[32]);
  var _confirmPopupSaveFlight = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[33]));
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[34]);
  var _addReturnCalendar = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[35]));
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[36]));
  var _text = _$$_REQUIRE(_dependencyMap[37]);
  var _saveFlightTravelOptionV = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[38]));
  var _modalSaveAndShare = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[39]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[40]);
  var _flyRedux = _$$_REQUIRE(_dependencyMap[41]);
  var _bottomSheetError = _$$_REQUIRE(_dependencyMap[42]);
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[43]);
  var _errorCloud = _$$_REQUIRE(_dependencyMap[44]);
  var _flightDetails = _$$_REQUIRE(_dependencyMap[45]);
  var _flightJourney = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[46]));
  var _profileRedux = _$$_REQUIRE(_dependencyMap[47]);
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[48]);
  var _searchDepartureFlight = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[49]));
  var _useModal2 = _$$_REQUIRE(_dependencyMap[50]);
  var _informationSharingModal = _$$_REQUIRE(_dependencyMap[51]);
  var _constants = _$$_REQUIRE(_dependencyMap[52]);
  var _type = _$$_REQUIRE(_dependencyMap[53]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[54]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SCREEN_NAME = "FlightDetails";
  var getValue = function getValue(value, defaultValue) {
    return value || defaultValue;
  };
  var PADDING_BOTTOM_SAVE_FLIGHT = 36;
  var _worklet_14271487422870_init_data = {
    code: "function flightDetailsV2Tsx1(){const{animatedTopLayout2Position,borderTopRightRadius,borderTopLeftRadius}=this.__closure;return{top:animatedTopLayout2Position.value,borderTopRightRadius:borderTopRightRadius.value,borderTopLeftRadius:borderTopLeftRadius.value};}"
  };
  var _worklet_819873017358_init_data = {
    code: "function flightDetailsV2Tsx2(){const{animatedTopLayout3Position}=this.__closure;return{top:animatedTopLayout3Position.value};}"
  };
  var _worklet_17058796411757_init_data = {
    code: "function flightDetailsV2Tsx3(){const{runOnJS,setIsFlightJourneyFullScreen}=this.__closure;runOnJS(setIsFlightJourneyFullScreen)(true);}"
  };
  var _worklet_4720210456641_init_data = {
    code: "function flightDetailsV2Tsx4(){const{runOnJS,setIsFlightJourneyFullScreen}=this.__closure;runOnJS(setIsFlightJourneyFullScreen)(false);}"
  };
  var _worklet_7719991814081_init_data = {
    code: "function flightDetailsV2Tsx5(){const{isShowTickerband,heightTickerBand}=this.__closure;return{top:isShowTickerband?heightTickerBand.value+25:58};}"
  };
  var _worklet_5562658735703_init_data = {
    code: "function flightDetailsV2Tsx6(event){const{scrollY}=this.__closure;scrollY.value=event.contentOffset.y;}"
  };
  var _worklet_5370272695388_init_data = {
    code: "function flightDetailsV2Tsx7(){const{interpolate,scrollY,threshold,heightTickerBand,coverHeight,Extrapolate}=this.__closure;const translateY=interpolate(scrollY.value,[threshold+heightTickerBand.value,threshold+heightTickerBand.value+coverHeight],[coverHeight,0],Extrapolate.CLAMP);return{transform:[{translateY:translateY}]};}"
  };
  var FlightDetailsV2 = exports.FlightDetailsV2 = function FlightDetailsV2(_ref) {
    var _flyFlightDetailsPayl2, _flyFlightDetailsPayl5, _flyFlightDetailsPayl6, _flyFlightDetailsPayl7, _flyFlightDetailsPayl8, _flyFlightDetailsPayl9;
    var navigation = _ref.navigation,
      route = _ref.route;
    var _useSafeAreaInsets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)(),
      top = _useSafeAreaInsets.top,
      bottom = _useSafeAreaInsets.bottom;
    var _useFlightDetailV2Sty = (0, _flightDetailsV.useFlightDetailV2Styles)(),
      styles = _useFlightDetailV2Sty.styles,
      SCREEN_HEIGHT = _useFlightDetailV2Sty.SCREEN_HEIGHT,
      LAYOUT_1_HEIGHT = _useFlightDetailV2Sty.LAYOUT_1_HEIGHT,
      LAYOUT_3_TOP_OFFSET = _useFlightDetailV2Sty.LAYOUT_3_TOP_OFFSET,
      ANIMATED_TIME_DURATION = _useFlightDetailV2Sty.ANIMATED_TIME_DURATION;
    var INITIAL_TOP_LAYOUT_2_VALUE = LAYOUT_1_HEIGHT;
    var INITIAL_TOP_LAYOUT_3_VALUE = INITIAL_TOP_LAYOUT_2_VALUE + LAYOUT_3_TOP_OFFSET;
    var coverHeight = SCREEN_HEIGHT * 0.4;
    var toastForSavedFlight = (0, _react.useRef)(undefined);
    var alertApp = (0, _react.useRef)(null);
    var toastForRemoveFlight = (0, _react.useRef)(null);
    var scrollViewRef = (0, _react.useRef)(null);
    var isShowModalConfirmSaveFly = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flyShowModalConfirmSaveFly);
    var isCheckInOnlineLoadFailed = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flyCheckInOnlineLoadFailed);
    var priorActionRef = (0, _react.useRef)("null");
    var scrollY = (0, _reactNativeReanimated.useSharedValue)(0);
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isFlightJourneyFullScreen = _useState2[0],
      setIsFlightJourneyFullScreen = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isStartShowFlightJourney = _useState4[0],
      setIsStartShowFlightJourney = _useState4[1];
    var animatedTopLayout2Position = (0, _reactNativeReanimated.useSharedValue)(INITIAL_TOP_LAYOUT_2_VALUE);
    var animatedTopLayout3Position = (0, _reactNativeReanimated.useSharedValue)(INITIAL_TOP_LAYOUT_3_VALUE);
    var borderTopRightRadius = (0, _reactNativeReanimated.useSharedValue)(30);
    var borderTopLeftRadius = (0, _reactNativeReanimated.useSharedValue)(30);
    var heightTickerBand = (0, _reactNativeReanimated.useSharedValue)(0);
    var _useState5 = (0, _react.useState)(0),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      heightLayout1 = _useState6[0],
      setHeightLayout1 = _useState6[1];
    var _useState7 = (0, _react.useState)(0),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      heightLayout3 = _useState8[0],
      setHeightLayout3 = _useState8[1];
    var _useState9 = (0, _react.useState)(SCREEN_HEIGHT),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      heightScrollView = _useState0[0],
      setHeightScrollView = _useState0[1];
    var _useState1 = (0, _react.useState)(false),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      showModalTravelOption = _useState10[0],
      setShowModalTravelOption = _useState10[1];
    var _useState11 = (0, _react.useState)(null),
      _useState12 = (0, _slicedToArray2.default)(_useState11, 2),
      bannerAEMResponse = _useState12[0],
      setBannerAEMResponse = _useState12[1];
    var _useState13 = (0, _react.useState)(false),
      _useState14 = (0, _slicedToArray2.default)(_useState13, 2),
      isFinishedJourneyOnboarding = _useState14[0],
      setIsFinishedJourneyOnboarding = _useState14[1];
    var _useState15 = (0, _react.useState)(false),
      _useState16 = (0, _slicedToArray2.default)(_useState15, 2),
      showSaveFlightWhenOnlineCheckIn = _useState16[0],
      setShowSaveFlightWhenOnlineCheckIn = _useState16[1];
    var _route$params = route.params,
      payload = _route$params.payload,
      direction = _route$params.direction,
      isFromScanBoardingPass = _route$params.isFromScanBoardingPass,
      referrer = _route$params.referrer,
      isFromUpcomingEvent = _route$params.isFromUpcomingEvent;
    var flyItem = payload.item;
    var _useFlightDetailV = (0, _useFlightDetailV2.useFlightDetailV2)({
        flyItem: flyItem,
        direction: direction,
        isFromScanBoardingPass: isFromScanBoardingPass,
        referrer: referrer,
        toastForSavedFlight: toastForSavedFlight,
        toastForRemoveFlight: toastForRemoveFlight,
        isFromUpcomingEvent: isFromUpcomingEvent,
        priorActionRef: priorActionRef,
        scrollViewRef: scrollViewRef
      }),
      trackAAWhenBack = _useFlightDetailV.trackAAWhenBack,
      shouldShowShareButton = _useFlightDetailV.shouldShowShareButton,
      flyShowTickerBand = _useFlightDetailV.flyShowTickerBand,
      tickerbandMaintananceHook = _useFlightDetailV.tickerbandMaintananceHook,
      flyFlightDetailsPayload = _useFlightDetailV.flyFlightDetailsPayload,
      flyLastUpdatedTimeStamp = _useFlightDetailV.flyLastUpdatedTimeStamp,
      shouldShowSQArrivalTerminalInfo = _useFlightDetailV.shouldShowSQArrivalTerminalInfo,
      inf22 = _useFlightDetailV.inf22,
      handleSharePress = _useFlightDetailV.handleSharePress,
      loadingSaveFlight = _useFlightDetailV.loadingSaveFlight,
      insertFlightPayload = _useFlightDetailV.insertFlightPayload,
      flightDetailV1Hook = _useFlightDetailV.flightDetailV1Hook,
      isNoInternetConnection = _useFlightDetailV.isNoInternetConnection,
      onOpenFlightShareSheet = _useFlightDetailV.onOpenFlightShareSheet,
      loadingFlightMap = _useFlightDetailV.loadingFlightMap,
      handleMap = _useFlightDetailV.handleMap,
      isLoadingDetailFlight = _useFlightDetailV.isLoadingDetailFlight,
      intoCityOrAirportPayload = _useFlightDetailV.intoCityOrAirportPayload,
      getBackgroundAirportAEM = _useFlightDetailV.getBackgroundAirportAEM,
      isSaved = _useFlightDetailV.isSaved,
      isButtonSaveHidden = _useFlightDetailV.isButtonSaveHidden,
      checkFlightCanSave = _useFlightDetailV.checkFlightCanSave,
      isLoggedIn = _useFlightDetailV.isLoggedIn,
      travelChecklistAEM = _useFlightDetailV.travelChecklistAEM,
      isTravelChecklistAEMLoading = _useFlightDetailV.isTravelChecklistAEMLoading,
      handleGetTravelChecklistAEM = _useFlightDetailV.handleGetTravelChecklistAEM,
      unableToLoadLocationRef = _useFlightDetailV.unableToLoadLocationRef,
      mapUnavailable = _useFlightDetailV.mapUnavailable,
      refreshFlightDetails = _useFlightDetailV.refreshFlightDetails,
      toastForRefresh = _useFlightDetailV.toastForRefresh,
      flyFlightDetailsError = _useFlightDetailV.flyFlightDetailsError,
      enableEciDynamicDisplay = _useFlightDetailV.enableEciDynamicDisplay,
      flightDetailSectionData = _useFlightDetailV.flightDetailSectionData,
      onPressFlightCardLinks = _useFlightDetailV.onPressFlightCardLinks,
      setSelectedTravelOption = _useFlightDetailV.setSelectedTravelOption,
      selectedTravelOption = _useFlightDetailV.selectedTravelOption,
      onSaveFlight = _useFlightDetailV.onSaveFlight,
      savedFlightTravelOptionsOnModalHide = _useFlightDetailV.savedFlightTravelOptionsOnModalHide,
      modalTravelOptionVisible = _useFlightDetailV.modalTravelOptionVisible,
      onClosedTravelOptionSheet = _useFlightDetailV.onClosedTravelOptionSheet,
      isModalVisible = _useFlightDetailV.isModalVisible,
      isFocused = _useFlightDetailV.isFocused,
      onCloseConfirmPopUpSavedFlight = _useFlightDetailV.onCloseConfirmPopUpSavedFlight,
      msg47 = _useFlightDetailV.msg47,
      onPressSavedFlightOnPress = _useFlightDetailV.onPressSavedFlightOnPress,
      onButtonPressedConfirmConnectingFlight = _useFlightDetailV.onButtonPressedConfirmConnectingFlight,
      onModalHideConfirmConnectingFlight = _useFlightDetailV.onModalHideConfirmConnectingFlight,
      handleConnectingFlightOnPress = _useFlightDetailV.handleConnectingFlightOnPress,
      showCalendarModal = _useFlightDetailV.showCalendarModal,
      onClosedCalendarModal = _useFlightDetailV.onClosedCalendarModal,
      onDateSelectedAddReturnCalendar = _useFlightDetailV.onDateSelectedAddReturnCalendar,
      removeFlightPayload = _useFlightDetailV.removeFlightPayload,
      isMSError = _useFlightDetailV.isMSError,
      getMyTripData = _useFlightDetailV.getMyTripData,
      _onRetryGetMyTripData = _useFlightDetailV.onRetryGetMyTripData,
      isLoadingGetMyTripData = _useFlightDetailV.isLoadingGetMyTripData,
      isErrorGetMyTrip = _useFlightDetailV.isErrorGetMyTrip,
      iconUrl = _useFlightDetailV.iconUrl,
      onButtonPressedConfirmSaveFlight = _useFlightDetailV.onButtonPressedConfirmSaveFlight,
      onCloseConfirmSaveFlight = _useFlightDetailV.onCloseConfirmSaveFlight,
      closeBottomSheetError = _useFlightDetailV.closeBottomSheetError,
      msg65 = _useFlightDetailV.msg65,
      onButtonPressBottomSheetError = _useFlightDetailV.onButtonPressBottomSheetError,
      showNoInternetError = _useFlightDetailV.showNoInternetError,
      enableFlightJourney = _useFlightDetailV.enableFlightJourney,
      pendingShowFlightJourney = _useFlightDetailV.pendingShowFlightJourney,
      setPendingShowFlightJourney = _useFlightDetailV.setPendingShowFlightJourney,
      showToastForSaveFlightSuccess = _useFlightDetailV.showToastForSaveFlightSuccess,
      isModalSaveAndShare = _useFlightDetailV.isModalSaveAndShare,
      onCloseModalSaveAndShare = _useFlightDetailV.onCloseModalSaveAndShare,
      onShareOnlyPress = _useFlightDetailV.onShareOnlyPress,
      isFlyLandingEnabled = _useFlightDetailV.isFlyLandingEnabled,
      loadingFreeFlightDelay = _useFlightDetailV.loadingFreeFlightDelay,
      isShowBanner = _useFlightDetailV.isShowBanner,
      onProceedSharing = _useFlightDetailV.onProceedSharing,
      loadingSaveFlightOverlay = _useFlightDetailV.loadingSaveFlightOverlay,
      setIsBannerAEMLoading = _useFlightDetailV.setIsBannerAEMLoading,
      isBannerAEMLoading = _useFlightDetailV.isBannerAEMLoading,
      floatSaveButtonHeight = _useFlightDetailV.floatSaveButtonHeight,
      setFloatSaveButtonHeight = _useFlightDetailV.setFloatSaveButtonHeight,
      scrollToTop = _useFlightDetailV.scrollToTop;
    var onPressCTA = tickerbandMaintananceHook.onPressCTA,
      onCloseTickerBand = tickerbandMaintananceHook.onCloseTickerBand,
      tickerBand = tickerbandMaintananceHook.tickerBand,
      tickerBandDescription = tickerbandMaintananceHook.tickerBandDescription,
      tickerBandButtonText = tickerbandMaintananceHook.tickerBandButtonText,
      isShowTickerband = tickerbandMaintananceHook.isShowTickerband,
      fetchTickerbandMaintanance = tickerbandMaintananceHook.fetchTickerbandMaintanance,
      errorData = tickerbandMaintananceHook.errorData,
      isShowMaintenance = tickerbandMaintananceHook.isShowMaintenance;
    var _useOnlineCheckIn = (0, _flightDetails.useOnlineCheckIn)({
        direction: direction,
        flyItem: flyItem,
        navigation: navigation,
        refreshFlightDetails: refreshFlightDetails,
        setShowSaveFlightWhenOnlineCheckIn: setShowSaveFlightWhenOnlineCheckIn,
        scrollToTop: scrollToTop
      }),
      handleCheckInOnline = _useOnlineCheckIn.handleCheckInOnline,
      handleSaveFlightWhenCheckInOnline = _useOnlineCheckIn.handleSaveFlightWhenCheckInOnline;
    var _useModal = (0, _useModal2.useModal)("approvalForInformationSharing"),
      openModalFlightSaver = _useModal.openModal;

    // Layout 2 Style
    var layout2Style = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var flightDetailsV2Tsx1 = function flightDetailsV2Tsx1() {
        return {
          top: animatedTopLayout2Position.value,
          borderTopRightRadius: borderTopRightRadius.value,
          borderTopLeftRadius: borderTopLeftRadius.value
        };
      };
      flightDetailsV2Tsx1.__closure = {
        animatedTopLayout2Position: animatedTopLayout2Position,
        borderTopRightRadius: borderTopRightRadius,
        borderTopLeftRadius: borderTopLeftRadius
      };
      flightDetailsV2Tsx1.__workletHash = 14271487422870;
      flightDetailsV2Tsx1.__initData = _worklet_14271487422870_init_data;
      return flightDetailsV2Tsx1;
    }());

    // Layout 3 Style
    var layout3Style = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var flightDetailsV2Tsx2 = function flightDetailsV2Tsx2() {
        return {
          top: animatedTopLayout3Position.value
        };
      };
      flightDetailsV2Tsx2.__closure = {
        animatedTopLayout3Position: animatedTopLayout3Position
      };
      flightDetailsV2Tsx2.__workletHash = 819873017358;
      flightDetailsV2Tsx2.__initData = _worklet_819873017358_init_data;
      return flightDetailsV2Tsx2;
    }());
    var onPressLayout2 = function onPressLayout2() {
      // Check if flight is saved before allowing fullscreen view
      // If flight is not saved, prompt user to save it first
      if (!isSaved) {
        priorActionRef.current = (0, _i18n.translate)("flightDetailV2.flightJourney.header.title");
        setPendingShowFlightJourney("pending");
        onSaveFlight(false, selectedTravelOption === _flightDetail.TravelOption.iAmTravelling, alertApp, payload);
        return;
      }
      handleShowFlightJourney();
    };
    var initTopLayout3Position = (0, _react.useMemo)(function () {
      return heightLayout1 + LAYOUT_3_TOP_OFFSET;
    }, [heightLayout1]);
    var initTopLayout2Position = (0, _react.useMemo)(function () {
      return heightLayout1;
    }, [heightLayout1]);
    var handleShowFlightJourney = (0, _react.useCallback)(function () {
      _reactNative.InteractionManager.runAfterInteractions(function () {
        // set the top position of layout 2 is top, and the top position of layout 3 is bottom
        var newTopLayout3Position = _reactNative.Dimensions.get("window").height - bottom + scrollY.value; // Layout2 cover Layout3 (fullscreen)
        var newTopLayout2Position = 0 + scrollY.value;
        animatedTopLayout3Position.value = (0, _reactNativeReanimated.withTiming)(newTopLayout3Position, {
          duration: ANIMATED_TIME_DURATION
        }, function () {
          var flightDetailsV2Tsx3 = function flightDetailsV2Tsx3() {
            (0, _reactNativeReanimated.runOnJS)(setIsFlightJourneyFullScreen)(true);
          };
          flightDetailsV2Tsx3.__closure = {
            runOnJS: _reactNativeReanimated.runOnJS,
            setIsFlightJourneyFullScreen: setIsFlightJourneyFullScreen
          };
          flightDetailsV2Tsx3.__workletHash = 17058796411757;
          flightDetailsV2Tsx3.__initData = _worklet_17058796411757_init_data;
          return flightDetailsV2Tsx3;
        }());
        animatedTopLayout2Position.value = (0, _reactNativeReanimated.withTiming)(newTopLayout2Position, {
          duration: ANIMATED_TIME_DURATION
        });
        borderTopRightRadius.value = (0, _reactNativeReanimated.withTiming)(0, {
          duration: ANIMATED_TIME_DURATION
        });
        borderTopLeftRadius.value = (0, _reactNativeReanimated.withTiming)(0, {
          duration: ANIMATED_TIME_DURATION
        });
        setIsStartShowFlightJourney(true);
      });
    }, [scrollY.value]);
    var checkReloadFlyItem = (0, _react.useMemo)(function () {
      if (!(flyItem != null && flyItem.flightNumber)) {
        return "";
      }
      return `${flyItem == null ? undefined : flyItem.flightNumber}-${flyItem == null ? undefined : flyItem.scheduledDate}-${direction}`;
    }, [flyItem == null ? undefined : flyItem.flightNumber, flyItem == null ? undefined : flyItem.scheduledDate, direction]);
    var handleHideFlightJourney = function handleHideFlightJourney() {
      _reactNative.InteractionManager.runAfterInteractions(function () {
        if (initTopLayout3Position && initTopLayout2Position) {
          // set the top position of layout 2 and layout 3 to the initial values
          var newTopLayout3Position = initTopLayout3Position;
          var newTopLayout2Position = initTopLayout2Position;
          animatedTopLayout3Position.value = (0, _reactNativeReanimated.withTiming)(newTopLayout3Position, {
            duration: ANIMATED_TIME_DURATION
          }, function () {
            var flightDetailsV2Tsx4 = function flightDetailsV2Tsx4() {
              (0, _reactNativeReanimated.runOnJS)(setIsFlightJourneyFullScreen)(false);
            };
            flightDetailsV2Tsx4.__closure = {
              runOnJS: _reactNativeReanimated.runOnJS,
              setIsFlightJourneyFullScreen: setIsFlightJourneyFullScreen
            };
            flightDetailsV2Tsx4.__workletHash = 4720210456641;
            flightDetailsV2Tsx4.__initData = _worklet_4720210456641_init_data;
            return flightDetailsV2Tsx4;
          }());
          animatedTopLayout2Position.value = (0, _reactNativeReanimated.withTiming)(newTopLayout2Position, {
            duration: ANIMATED_TIME_DURATION
          });
          borderTopRightRadius.value = (0, _reactNativeReanimated.withTiming)(30, {
            duration: ANIMATED_TIME_DURATION
          });
          borderTopLeftRadius.value = (0, _reactNativeReanimated.withTiming)(30, {
            duration: ANIMATED_TIME_DURATION
          });
          setIsStartShowFlightJourney(false);
        }
      });
    };
    (0, _react.useEffect)(function () {
      if (pendingShowFlightJourney === "finished" && isSaved && !isLoadingDetailFlight && !isLoadingGetMyTripData) {
        // First clear the pending state
        setPendingShowFlightJourney(null);
        handleShowFlightJourney();
      }
    }, [pendingShowFlightJourney, isSaved, isLoadingDetailFlight, isLoadingGetMyTripData, handleShowFlightJourney]);
    (0, _react.useEffect)(function () {
      // handle hide Flight Journey in case open flight detail from notification
      if (checkReloadFlyItem) {
        // set the top position of layout 2 and layout 3 to the initial values
        handleHideFlightJourney();
      }
    }, [checkReloadFlyItem]);
    var backOnPressed = function backOnPressed() {
      trackAAWhenBack();
      navigation.goBack();
    };
    var onClosedSheet = function onClosedSheet() {
      setShowModalTravelOption(false);
    };
    var travelOptionTapped = function travelOptionTapped(option) {
      var flyProfile = option === _flightDetail.TravelOption.iAmTravelling ? _flightDetail.FlyProfileEnum.flying : _flightDetail.FlyProfileEnum.nonFlying;
      var flightNumber = flyItem == null ? undefined : flyItem.flightNumber;
      var flightDate = flyItem == null ? undefined : flyItem.flightDate;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlyFlightDetailFlyProfile, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlyFlightDetailFlyProfile, `${flyProfile}|${flightNumber}|${flightDate}`));
      setShowModalTravelOption(false);
      setSelectedTravelOption(option);
      // setSelectedTopTravelOption(option) //do late
    };
    var handleGetBannerAEM = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        setIsBannerAEMLoading(true);
        var res = yield flightDetailV1Hook.getBannerAEM();
        setBannerAEMResponse(res);
        setIsBannerAEMLoading(false);
      });
      return function handleGetBannerAEM() {
        return _ref2.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      handleGetBannerAEM();
    }, []);
    var handleOnLayout = function handleOnLayout(event) {
      var height = event.nativeEvent.layout.height;
      heightTickerBand.value = height;
    };
    var handleLayout1 = function handleLayout1(event) {
      setHeightLayout1(event.nativeEvent.layout.height);
      // Only update position when not in fullscreen mode
      if (!isStartShowFlightJourney) {
        if (enableFlightJourney) {
          animatedTopLayout2Position.value = event.nativeEvent.layout.height;
          animatedTopLayout3Position.value = event.nativeEvent.layout.height + LAYOUT_3_TOP_OFFSET;
        } else {
          animatedTopLayout3Position.value = event.nativeEvent.layout.height;
        }
      }
    };
    var handleLayout3 = function handleLayout3(event) {
      setHeightLayout3(event.nativeEvent.layout.height);
    };
    var headerPositionStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var flightDetailsV2Tsx5 = function flightDetailsV2Tsx5() {
        return {
          top: isShowTickerband ? heightTickerBand.value + 25 : 58
        };
      };
      flightDetailsV2Tsx5.__closure = {
        isShowTickerband: isShowTickerband,
        heightTickerBand: heightTickerBand
      };
      flightDetailsV2Tsx5.__workletHash = 7719991814081;
      flightDetailsV2Tsx5.__initData = _worklet_7719991814081_init_data;
      return flightDetailsV2Tsx5;
    }());
    var scrollHandler = (0, _reactNativeReanimated.useAnimatedScrollHandler)({
      onScroll: function () {
        var flightDetailsV2Tsx6 = function flightDetailsV2Tsx6(event) {
          scrollY.value = event.contentOffset.y;
        };
        flightDetailsV2Tsx6.__closure = {
          scrollY: scrollY
        };
        flightDetailsV2Tsx6.__workletHash = 5562658735703;
        flightDetailsV2Tsx6.__initData = _worklet_5562658735703_init_data;
        return flightDetailsV2Tsx6;
      }()
    });
    var threshold = heightScrollView - SCREEN_HEIGHT;
    var footerAnimatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var flightDetailsV2Tsx7 = function flightDetailsV2Tsx7() {
        var translateY = (0, _reactNativeReanimated.interpolate)(scrollY.value, [threshold + heightTickerBand.value, threshold + heightTickerBand.value + coverHeight], [coverHeight, 0], _reactNativeReanimated.Extrapolate.CLAMP);
        return {
          transform: [{
            translateY: translateY
          }]
        };
      };
      flightDetailsV2Tsx7.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        scrollY: scrollY,
        threshold: threshold,
        heightTickerBand: heightTickerBand,
        coverHeight: coverHeight,
        Extrapolate: _reactNativeReanimated.Extrapolate
      };
      flightDetailsV2Tsx7.__workletHash = 5370272695388;
      flightDetailsV2Tsx7.__initData = _worklet_5370272695388_init_data;
      return flightDetailsV2Tsx7;
    }());
    var onOpenSaveFlightModal = function onOpenSaveFlightModal(priorActionLabel) {
      priorActionRef.current = priorActionLabel || "";
      onSaveFlight(false, selectedTravelOption === _flightDetail.TravelOption.iAmTravelling, alertApp, payload);
    };
    var onUnsaveFlight = function onUnsaveFlight() {
      onSaveFlight(true, selectedTravelOption === _flightDetail.TravelOption.iAmTravelling, alertApp, payload);
      var savedFlightPriorActions = (0, _mmkvStorage.getMMKVdata)(_mmkvStorage.ENUM_STORAGE_MMKV.SAVED_FLIGHT_PRIOR_ACTIONS, _mmkvStorage.ENUM_STORAGE_TYPE.string);
      try {
        var priorActionText = "null";
        if (savedFlightPriorActions) {
          var priorAction = JSON.parse(savedFlightPriorActions).find(function (action) {
            return (action == null ? undefined : action.uid) === (profilePayload == null ? undefined : profilePayload.id) && (action == null ? undefined : action.flightNumber) === (flyItem == null ? undefined : flyItem.flightNumber) && (action == null ? undefined : action.direction) === (flyItem == null ? undefined : flyItem.direction);
          });
          if (priorAction) {
            priorActionText = (priorAction == null ? undefined : priorAction.priorAction) || "null";
          }
        }
        var directionText = (flyItem == null ? undefined : flyItem.direction) === _flightProps.FlightDirection.departure ? "Departure" : "Arrival";
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlightPersonalizationSaveFlight, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlightPersonalizationSaveFlight, `${flyItem == null ? undefined : flyItem.flightNumber} | ${directionText} | ${(0, _i18n.translate)(selectedTravelOption)} | ${priorActionText} | Unsave`));
      } catch (error) {
        console.error("Error parsing saved flight prior actions:", error);
      }
    };
    var onFloatingSaveButtonLayout = function onFloatingSaveButtonLayout(e) {
      setFloatSaveButtonHeight(e.nativeEvent.layout.height);
    };
    var isHideSaveFlight = (0, _react.useMemo)(function () {
      return !isFinishedJourneyOnboarding || isButtonSaveHidden || isLoadingDetailFlight && !(insertFlightPayload != null && insertFlightPayload.loading) || isStartShowFlightJourney;
    }, [isButtonSaveHidden, isLoadingDetailFlight, insertFlightPayload == null ? undefined : insertFlightPayload.loading, isStartShowFlightJourney, isFinishedJourneyOnboarding]);
    (0, _react.useEffect)(function () {
      var height = heightLayout1 + heightLayout3 + PADDING_BOTTOM_SAVE_FLIGHT + 24;
      if (_reactNative.Platform.OS === 'android' && !isHideSaveFlight) {
        height = height + (floatSaveButtonHeight - 8);
      }
      setHeightScrollView(height);
    }, [heightLayout3, heightLayout1, floatSaveButtonHeight, isHideSaveFlight]);
    var filterDateAddReturnCalendar = (0, _react.useMemo)(function () {
      if (isLoadingDetailFlight) {
        return (0, _moment.default)().format("YYYY-MM-DD");
      } else {
        var _flyFlightDetailsPayl;
        return (0, _moment.default)(flyFlightDetailsPayload == null || (_flyFlightDetailsPayl = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl.displayTimestamp).format("YYYY-MM-DD") || (0, _moment.default)().format("YYYY-MM-DD");
      }
    }, [flyFlightDetailsPayload == null || (_flyFlightDetailsPayl2 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl2.displayTimestamp, isLoadingDetailFlight]);
    var isFlightSaved = (0, _react.useMemo)(function () {
      return isButtonSaveHidden && isSaved && isLoggedIn;
    }, [isButtonSaveHidden, isSaved, isLoggedIn]);
    var isShowUnSaveFlight = (0, _react.useMemo)(function () {
      if (isLoadingDetailFlight) {
        return false;
      }
      return isFlightSaved && !(removeFlightPayload != null && removeFlightPayload.loading) && !isMSError;
    }, [isFlightSaved, removeFlightPayload == null ? undefined : removeFlightPayload.loading, isMSError, isLoadingDetailFlight]);
    var onCloseTickerBandView = function onCloseTickerBandView() {
      onCloseTickerBand();
      heightTickerBand.value = 0;
    };
    var renderTickerBand = function renderTickerBand() {
      return (0, _jsxRuntime.jsx)(_tickerBand.default, {
        urgent: false,
        title: flyShowTickerBand && tickerBand,
        description: flyShowTickerBand && tickerBandDescription,
        buttonText: flyShowTickerBand && tickerBandButtonText,
        onCTAPress: onPressCTA,
        onClose: onCloseTickerBandView,
        isLanding: true,
        onLayout: handleOnLayout
      });
    };
    var renderHeader = function renderHeader() {
      var _flyFlightDetailsPayl3;
      if (isStartShowFlightJourney) {
        return null;
      }
      return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
          style: [styles.wrapHeader, headerPositionStyle, {
            left: 0
          }],
          children: (0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
            style: {
              zIndex: 10
            },
            onPress: backOnPressed,
            testID: `${SCREEN_NAME}__BackButton`,
            accessibilityLabel: `${SCREEN_NAME}__BackButton`,
            children: (0, _jsxRuntime.jsx)(_icons.BackButton, {})
          })
        }), shouldShowShareButton && (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
          style: [styles.wrapHeader, headerPositionStyle, {
            right: 0
          }],
          children: (0, _jsxRuntime.jsx)(_singleTapButton.SingleTapButton, {
            disabled: loadingFlightMap || !(flyFlightDetailsPayload != null && (_flyFlightDetailsPayl3 = flyFlightDetailsPayload.flightDetailsData) != null && _flyFlightDetailsPayl3.flightNumber),
            onPress: handleSharePress,
            testID: `${SCREEN_NAME}__ShareButton`,
            accessibilityLabel: `${SCREEN_NAME}__ShareButton`,
            children: (0, _jsxRuntime.jsx)(_icons.Share, {})
          })
        })]
      });
    };
    var showErrorFeedBackToastMessage = function showErrorFeedBackToastMessage() {
      return (0, _jsxRuntime.jsx)(_feedbackToast.FeedBackToast, {
        ref: toastForRefresh,
        style: styles.feedBackToastStyle,
        textButtonStyle: styles.toastButtonStyle,
        position: "bottom",
        textStyle: styles.toastTextStyle,
        type: _feedbackToast.FeedBackToastType.fullWidthFeedBack,
        text: (0, _i18n.translate)("flightLanding.feedBackToastErrorMessage") + flyLastUpdatedTimeStamp
      });
    };
    var showFlightAddedFeedBackToastMessage = function showFlightAddedFeedBackToastMessage() {
      return (0, _jsxRuntime.jsx)(_feedbackToast.FeedBackToast, {
        ref: toastForSavedFlight,
        style: styles.toastStyleAddedFlight,
        textButtonStyle: styles.toastButtonStyleAdded,
        position: "bottom",
        textStyle: styles.toastTextStyleAdded,
        type: _feedbackToast.FeedBackToastType.smallFeedBack,
        text: (0, _i18n.translate)("flightDetails.newPopupConfirmSaveFlight.toastFlightSaved")
      });
    };
    var showToastForRemoveFlight = function showToastForRemoveFlight() {
      return (0, _jsxRuntime.jsx)(_feedbackToast.FeedBackToast, {
        ref: toastForRemoveFlight,
        style: styles.feedBackToastStyle,
        textButtonStyle: styles.toastButtonStyle,
        position: "custom",
        positionValue: {
          bottom: 100
        },
        textStyle: styles.toastTextStyle,
        type: _feedbackToast.FeedBackToastType.smallFeedBack,
        text: (0, _i18n.translate)("flyLanding.removeFlightNew")
      });
    };
    var renderBottomSheetError = function renderBottomSheetError() {
      return (0, _jsxRuntime.jsx)(_bottomSheetError.BottomSheetError, {
        iconUrl: msg65 == null ? undefined : msg65.icon,
        icon: (0, _jsxRuntime.jsx)(_icons.ErrorOutlined, {
          width: "70",
          height: "70"
        }),
        visible: isCheckInOnlineLoadFailed,
        title: getValue(msg65 == null ? undefined : msg65.title, (0, _i18n.translate)("popupError.somethingWrong")),
        errorMessage: getValue(msg65 == null ? undefined : msg65.message, (0, _i18n.translate)("popupError.networkErrorMessage")),
        onClose: closeBottomSheetError,
        buttonText: getValue(msg65 == null ? undefined : msg65.firstButton, (0, _i18n.translate)("popupError.retry")),
        onButtonPressed: onButtonPressBottomSheetError,
        testID: `${SCREEN_NAME}__BottomSheetError`,
        accessibilityLabel: `${SCREEN_NAME}__BottomSheetError`
      });
    };
    if (isNoInternetConnection) {
      return (0, _jsxRuntime.jsx)(_errorOverlayApp.ErrorOverlayApp, {
        reload: true,
        onReload: onOpenFlightShareSheet
      });
    }
    if (isShowMaintenance) {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.maintenanceErrorContainer,
        children: (0, _jsxRuntime.jsx)(_errorCloud.ErrorCloudComponent, {
          skipStatusbar: true,
          style: {
            backgroundColor: "transparent"
          },
          titleStyle: {
            marginTop: 16
          },
          buttonStyle: {
            width: "auto"
          },
          errorData: errorData,
          onPress: fetchTickerbandMaintanance
        })
      });
    }
    var onPressBanner = function onPressBanner() {
      var _flyFlightDetailsPayl4;
      if (!isLoggedIn) {
        navigation.navigate(_constants.NavigationConstants.authScreen, {
          sourceSystem: _constants.SOURCE_SYSTEM.FLIGHTS,
          callBackAfterLoginSuccess: function callBackAfterLoginSuccess() {
            openModalFlightSaver();
          },
          callBackAfterLoginCancel: function callBackAfterLoginCancel() {
            return null;
          }
        });
        return;
      }
      var _ref3 = (flyFlightDetailsPayload == null || (_flyFlightDetailsPayl4 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl4.flightInsurance) || {},
        bannerType = _ref3.bannerType;
      switch (bannerType) {
        case _type.EnumBannerType.NOT_SAVED:
        case _type.EnumBannerType.OPT_IN:
          openModalFlightSaver();
          break;
        case _type.EnumBannerType.OPT_OUT:
          onPressProceedSharing();
          break;
        case _type.EnumBannerType.DELAY:
          navigation.navigate(_constants.NavigationConstants.vouchersPrizesRedemptionsScreen);
          break;
        default:
          openModalFlightSaver();
          break;
      }
    };
    var onPressProceedSharing = function onPressProceedSharing() {
      onProceedSharing(alertApp);
    };
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
        visible: loadingFlightMap || (removeFlightPayload == null ? undefined : removeFlightPayload.loading) || loadingFreeFlightDelay || loadingSaveFlightOverlay
      }), isShowTickerband && renderTickerBand(), showNoInternetError && (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        hideScreenHeader: false,
        headerBackgroundColor: "transparent",
        visible: showNoInternetError,
        onBack: function onBack() {
          navigation.goBack();
        },
        testID: `${SCREEN_NAME}__ErrorOverlayNoConnection`,
        onReload: refreshFlightDetails,
        noInternetOverlayStyle: styles.errorOverLay
      }), (0, _jsxRuntime.jsx)(_backgroundImage.BackgroundImage, {
        direction: direction,
        getBackgroundAirportAEM: getBackgroundAirportAEM,
        isSaved: isSaved,
        airport: direction === _flightProps.FlightDirection.departure ? flyFlightDetailsPayload == null || (_flyFlightDetailsPayl5 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl5.destinationCode : flyFlightDetailsPayload == null || (_flyFlightDetailsPayl6 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl6.departingCode
      }), (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.ScrollView, {
        ref: scrollViewRef,
        style: styles.containerScrollView,
        showsVerticalScrollIndicator: false,
        onScroll: scrollHandler,
        refreshControl: (0, _jsxRuntime.jsx)(_reactNative2.RefreshControl, {
          refreshing: false,
          onRefresh: function onRefresh() {
            return !isStartShowFlightJourney ? refreshFlightDetails(true) : undefined;
          }
        }),
        scrollEnabled: !isStartShowFlightJourney,
        bounces: !isStartShowFlightJourney,
        children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: [styles.container, {
            minHeight: heightScrollView
          }],
          children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: [styles.layout1, {
              paddingBottom: isShowBanner ? 0 : 20
            }],
            onLayout: function onLayout(e) {
              return handleLayout1(e);
            },
            children: [(0, _jsxRuntime.jsx)(_flightInfo.FlightInfo, {
              directionFromParams: direction,
              flyFlightDetailsPayload: flyFlightDetailsPayload,
              flyLastUpdatedTimeStamp: flyLastUpdatedTimeStamp,
              shouldShowSQArrivalTerminalInfo: shouldShowSQArrivalTerminalInfo,
              inf22: inf22,
              handleMap: handleMap,
              isLoadingDetailFlight: isLoadingDetailFlight,
              intoCityOrAirportPayload: intoCityOrAirportPayload,
              marginTop: isShowTickerband ? -top + 25 : 58 - top // TODO: handler when show TickerBand later
              ,
              isSaved: isSaved,
              onSaveFlight: onOpenSaveFlightModal,
              getMyTripData: getMyTripData,
              isLoadingGetMyTripData: isLoadingGetMyTripData,
              isErrorGetMyTrip: isErrorGetMyTrip,
              onRetryGetMyTripData: function onRetryGetMyTripData() {
                return _onRetryGetMyTripData({
                  isHideLoadingDetail: false
                });
              }
            }), isShowBanner && (0, _jsxRuntime.jsx)(_flightBanner.default, {
              bannerAEMResponse: bannerAEMResponse,
              isBannerAEMLoading: isBannerAEMLoading,
              onPressReloadBanner: handleGetBannerAEM,
              onPressBanner: onPressBanner,
              isSaved: isSaved,
              flyFlightDetailsPayload: flyFlightDetailsPayload,
              insertFlightPayload: insertFlightPayload
            })]
          }), enableFlightJourney && (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
            style: [styles.layout2, layout2Style],
            children: (0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
              activeOpacity: 1,
              onPress: onPressLayout2,
              disabled: isStartShowFlightJourney || isLoadingGetMyTripData || isLoadingDetailFlight,
              children: (0, _jsxRuntime.jsx)(_flightJourney.default, {
                isFlightJourneyFullScreen: isFlightJourneyFullScreen,
                isStartShowFlightJourney: isStartShowFlightJourney,
                handleHide: handleHideFlightJourney,
                flightDetails: flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData,
                isLoadingData: isLoadingDetailFlight || isLoadingGetMyTripData,
                getMyTripData: getMyTripData
              })
            })
          }), (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
            style: [styles.layout3, layout3Style],
            onLayout: function onLayout(e) {
              return handleLayout3(e);
            },
            children: (0, _jsxRuntime.jsxs)(_flightInformationHub.default, {
              setShowModalTravelOption: setShowModalTravelOption,
              selectedTravelOption: selectedTravelOption,
              loadingFlightDetail: isLoadingDetailFlight,
              isFlightSaved: isSaved,
              isFlightJourneyFullScreen: isStartShowFlightJourney,
              handleHideFlightJourney: handleHideFlightJourney,
              children: [(0, _jsxRuntime.jsx)(_informationHubCard.InformationHubCard, {
                direction: direction,
                handleMap: handleMap,
                travelChecklistAEM: travelChecklistAEM,
                flightDetailSectionData: flightDetailSectionData,
                flyFlightDetailsPayload: flyFlightDetailsPayload,
                flyFlightDetailsError: flyFlightDetailsError,
                selectedTopTravelOption: selectedTravelOption,
                isFlightSaved: isButtonSaveHidden && isLoggedIn,
                enableEciDynamicDisplay: enableEciDynamicDisplay,
                onPressFlightCardLinks: onPressFlightCardLinks,
                customerEligibility: (0, _utils.handleCondition)(direction === _flightProps.FlightDirection.departure, _flightProps.CustomerEligibility.FlyingDep, _flightProps.CustomerEligibility.FlyingArr),
                onPressReloadTravelAEM: handleGetTravelChecklistAEM,
                isTravelChecklistAEMLoading: isTravelChecklistAEMLoading,
                onPressReloadFlightDetails: refreshFlightDetails,
                disableSaveFlight: tickerbandMaintananceHook.isShowMaintenance ? true : !checkFlightCanSave((flyFlightDetailsPayload == null || (_flyFlightDetailsPayl7 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl7.flightStatus) || (flyItem == null ? undefined : flyItem.flightStatus), direction),
                onSaveFlight: onOpenSaveFlightModal,
                saveFlightWhenCheckInOnline: handleSaveFlightWhenCheckInOnline
              }), (0, _utils.handleCondition)(isShowUnSaveFlight, (0, _jsxRuntime.jsx)(_reactNative.View, {
                style: styles.containerUnsaveFlight,
                children: (0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
                  onPress: onUnsaveFlight,
                  style: styles.containerUnsaveFlightBtn,
                  children: (0, _jsxRuntime.jsx)(_text.Text, {
                    tx: "flightDetailV2.unsaveFlight",
                    preset: "caption1Bold",
                    style: styles.unsaveFlightLabel
                  })
                })
              }), null)]
            })
          })]
        })
      }), _reactNative.Platform.OS === "ios" && !!(flyFlightDetailsPayload != null && flyFlightDetailsPayload.flightDetailsData) && (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
        style: [styles.coverFooterCointainer, {
          height: coverHeight
        }, footerAnimatedStyle],
        pointerEvents: "none"
      }), showErrorFeedBackToastMessage(), (0, _utils.handleCondition)(!modalTravelOptionVisible && isHideSaveFlight, null, (0, _jsxRuntime.jsx)(_saveFlightButton.default, {
        loading: insertFlightPayload == null ? undefined : insertFlightPayload.loading,
        onPress: function onPress() {
          return onOpenSaveFlightModal("null");
        },
        disabled: tickerbandMaintananceHook.isShowMaintenance,
        onLayout: onFloatingSaveButtonLayout,
        isFlightCanSave: tickerbandMaintananceHook.isShowMaintenance ? false : checkFlightCanSave((flyFlightDetailsPayload == null || (_flyFlightDetailsPayl8 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl8.flightStatus) || (flyItem == null ? undefined : flyItem.flightStatus), direction)
      })), (0, _jsxRuntime.jsx)(_addReturnCalendar.default, {
        isVisible: showCalendarModal,
        filterDate: filterDateAddReturnCalendar,
        initialMinDate: filterDateAddReturnCalendar,
        onClosedCalendarModal: onClosedCalendarModal,
        onDateSelected: function onDateSelected(dateString) {
          return onDateSelectedAddReturnCalendar(dateString);
        },
        testID: `${SCREEN_NAME}__AddReturnCalendar`,
        accessibilityLabel: `${SCREEN_NAME}__AddReturnCalendar`
      }), showFlightAddedFeedBackToastMessage(), (0, _jsxRuntime.jsx)(_alertApp.AlertApp, {
        ref: alertApp
      }), renderHeader(), (0, _jsxRuntime.jsx)(_travelOptions.default, {
        visible: showModalTravelOption,
        onClosed: onClosedSheet,
        onBackPressed: onClosedSheet,
        selectedOption: selectedTravelOption,
        onPress: function onPress(option) {
          return travelOptionTapped(option);
        },
        flightDirection: direction
      }), showToastForRemoveFlight(), (0, _jsxRuntime.jsx)(_saveFlightTravelOptionV.default, {
        onModalHide: savedFlightTravelOptionsOnModalHide,
        visible: modalTravelOptionVisible,
        onClosed: onClosedTravelOptionSheet,
        loadingSaveFlight: loadingSaveFlight,
        onBackPressed: onClosedTravelOptionSheet,
        selectedOption: selectedTravelOption,
        savedFlightOnPress: function savedFlightOnPress(e) {
          return onPressSavedFlightOnPress(e);
        },
        onPress: function onPress(option) {
          return travelOptionTapped(option);
        },
        flightDirection: direction
      }), isFlyLandingEnabled && (0, _jsxRuntime.jsx)(_searchDepartureFlight.default, {
        handleOnClose: showToastForSaveFlightSuccess,
        displayTimestamp: flyFlightDetailsPayload == null || (_flyFlightDetailsPayl9 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl9.displayTimestamp
      }), (0, _jsxRuntime.jsx)(_modalSaveAndShare.default, {
        onModalHide: savedFlightTravelOptionsOnModalHide,
        visible: isModalSaveAndShare,
        onClosed: onCloseModalSaveAndShare,
        loadingSaveFlight: loadingSaveFlight,
        onShareOnlyPress: onShareOnlyPress,
        selectedOption: selectedTravelOption,
        savedFlightOnPress: function savedFlightOnPress(e) {
          return onPressSavedFlightOnPress(e);
        },
        onPress: function onPress(option) {
          return travelOptionTapped(option);
        },
        flightDirection: direction
      }), (0, _jsxRuntime.jsx)(_confirmPopupSaveFlight.default, {
        imageUrl: (0, _mediaHelper.handleImageUrl)(msg47 == null ? undefined : msg47.icon),
        visible: isModalVisible && isFocused,
        title: (0, _i18n.translate)("flightDetails.newPopupConfirmSaveFlight.title"),
        messageText: (0, _utils.simpleCondition)({
          condition: direction === _flightProps.FlightDirection.arrival,
          ifValue: (0, _i18n.translate)("flightDetails.newPopupConfirmSaveFlight.arrivalMessage"),
          elseValue: (0, _i18n.translate)("flightDetails.newPopupConfirmSaveFlight.departureMessage")
        }),
        onClose: function onClose() {
          onCloseConfirmPopUpSavedFlight();
        },
        onButtonPressed: onButtonPressedConfirmConnectingFlight,
        onModalHide: onModalHideConfirmConnectingFlight,
        textButtonConfirm: (0, _i18n.translate)("flightDetails.newPopupConfirmSaveFlight.addReturnFlightButton"),
        textButtonCancel: (0, _i18n.translate)("flightDetails.newPopupConfirmSaveFlight.cancelButton"),
        isShowButtonConnection: direction === _flightProps.FlightDirection.arrival,
        onButtonConnectionPressed: handleConnectingFlightOnPress,
        textButtonConnection: (0, _i18n.translate)("flightDetails.newPopupConfirmSaveFlight.addConnectingFlightButton"),
        disableCloseButton: true
      }), (0, _jsxRuntime.jsx)(_confirmPopupSaveFlight.default, {
        imageUrl: iconUrl,
        visible: isShowModalConfirmSaveFly,
        messageText: (0, _i18n.translate)("flightDetails.popupConfirmSaveFlight.message"),
        onClose: onCloseConfirmSaveFlight,
        onButtonPressed: function onButtonPressed() {
          return onButtonPressedConfirmSaveFlight(payload);
        }
      }), (0, _jsxRuntime.jsx)(_confirmPopupSaveFlight.default, {
        imageUrl: iconUrl,
        visible: showSaveFlightWhenOnlineCheckIn,
        messageText: (0, _i18n.translate)("flightDetails.popupConfirmSaveFlight.messageOnlineCheckIn"),
        onClose: function onClose() {
          return setShowSaveFlightWhenOnlineCheckIn(false);
        },
        onButtonPressed: function onButtonPressed() {
          return onButtonPressedConfirmSaveFlight(payload);
        },
        textButtonCancel: (0, _i18n.translate)("flightDetails.popupConfirmSaveFlight.onlineCheckInOnly"),
        onSecondaryBtnPressed: handleCheckInOnline
      }), renderBottomSheetError(), (0, _jsxRuntime.jsx)(_bottomSheetUnableLoadLocation.BottomSheetUnableLoadLocation, {
        ref: unableToLoadLocationRef
      }), (0, _jsxRuntime.jsx)(_bottomSheetMapUnavailable.BottomSheetMapUnavailable, {
        ref: mapUnavailable
      }), (0, _jsxRuntime.jsx)(_onboardingOverlay.default, {
        direction: direction,
        isLoadingDetailFlight: isLoadingDetailFlight,
        setIsFinishedJourneyOnboarding: setIsFinishedJourneyOnboarding
      }), (0, _jsxRuntime.jsx)(_informationSharingModal.InformationSharingModal, {
        onPressProceed: onPressProceedSharing
      })]
    });
  };
