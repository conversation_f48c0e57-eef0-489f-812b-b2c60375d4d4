  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.UserProfileTagNameEnum = exports.TypeFocusInformationHub = exports.TravelOption = exports.SectionTileTagNameEnum = exports.SectionTagNameEnum = exports.FlyProfileEnum = exports.ExpandCollapseEnum = exports.DirectionTagNameEnum = undefined;
  var SectionTagNameEnum = exports.SectionTagNameEnum = /*#__PURE__*/function (SectionTagNameEnum) {
    SectionTagNameEnum["GTTD"] = "gttd";
    SectionTagNameEnum["TRANSFER_IN_TERMINAL"] = "transfer-in-terminal";
    SectionTagNameEnum["FLIGHT_CHECK_IN"] = "flight-check-in";
    SectionTagNameEnum["ATOM"] = "atoms";
    SectionTagNameEnum["CMS_FLIGHT_CARD"] = "cms-flight-card";
    SectionTagNameEnum["WEATHER"] = "weather";
    return SectionTagNameEnum;
  }({});
  var SectionTileTagNameEnum = exports.SectionTileTagNameEnum = /*#__PURE__*/function (SectionTileTagNameEnum) {
    SectionTileTagNameEnum["SHORT_DESIGN"] = "short-design";
    SectionTileTagNameEnum["LONG_DESIGN"] = "long-design";
    SectionTileTagNameEnum["WITH_SUB_CATEGORY"] = "with-sub-category";
    return SectionTileTagNameEnum;
  }({});
  var UserProfileTagNameEnum = exports.UserProfileTagNameEnum = /*#__PURE__*/function (UserProfileTagNameEnum) {
    UserProfileTagNameEnum["TRAVELLER"] = "traveller";
    UserProfileTagNameEnum["MEETERS_AND_GREETERS"] = "meeters-and-greeters";
    return UserProfileTagNameEnum;
  }({});
  var DirectionTagNameEnum = exports.DirectionTagNameEnum = /*#__PURE__*/function (DirectionTagNameEnum) {
    DirectionTagNameEnum["DEPARTURE"] = "departure";
    DirectionTagNameEnum["ARRIVAL"] = "arrival";
    return DirectionTagNameEnum;
  }({});
  var ExpandCollapseEnum = exports.ExpandCollapseEnum = /*#__PURE__*/function (ExpandCollapseEnum) {
    ExpandCollapseEnum["EXPAND"] = "expand";
    ExpandCollapseEnum["COLLAPSE"] = "collapse";
    return ExpandCollapseEnum;
  }({});
  var TypeFocusInformationHub = exports.TypeFocusInformationHub = /*#__PURE__*/function (TypeFocusInformationHub) {
    TypeFocusInformationHub["TRAVEL"] = "travel";
    TypeFocusInformationHub["FACILITIES"] = "facilities";
    return TypeFocusInformationHub;
  }({});
  var FlyProfileEnum = exports.FlyProfileEnum = /*#__PURE__*/function (FlyProfileEnum) {
    FlyProfileEnum["flying"] = "flying";
    FlyProfileEnum["nonFlying"] = "non-flying";
    return FlyProfileEnum;
  }({});
  var TravelOption = exports.TravelOption = /*#__PURE__*/function (TravelOption) {
    TravelOption["iAmTravelling"] = "dropdownSelectCard.imTravellingOnThisFlight";
    TravelOption["iAmPicking"] = "dropdownSelectCard.imDroppingOffSomeone";
    return TravelOption;
  }({});
