  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.toastTextStyle = exports.toastButtonStyle = exports.feedBackToastStyle = exports.TypeTransport = exports.TypePressDetailFlightCard = exports.TypeGetIntoAirport = exports.FlightDetails = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _flightHeroImage = _$$_REQUIRE(_dependencyMap[7]);
  var _icons = _$$_REQUIRE(_dependencyMap[8]);
  var _dropdownSelectCard = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _flightDetailsCard = _$$_REQUIRE(_dependencyMap[10]);
  var _flightDetailsCard2 = _$$_REQUIRE(_dependencyMap[11]);
  var _flightDetailsStyles = _$$_REQUIRE(_dependencyMap[12]);
  var _backgrounds = _$$_REQUIRE(_dependencyMap[13]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[14]);
  var _flyRedux = _$$_REQUIRE(_dependencyMap[15]);
  var _getIntoAirport = _$$_REQUIRE(_dependencyMap[16]);
  var _timelineSection = _$$_REQUIRE(_dependencyMap[17]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[18]);
  var _saveFlightButton = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[19]));
  var _constants = _$$_REQUIRE(_dependencyMap[20]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[21]);
  var _flightDetailsCrt = _$$_REQUIRE(_dependencyMap[22]);
  var _flightProps = _$$_REQUIRE(_dependencyMap[23]);
  var _i18n = _$$_REQUIRE(_dependencyMap[24]);
  var _dateTime = _$$_REQUIRE(_dependencyMap[25]);
  var _travelOptions = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[26]));
  var _feedbackToast = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[27]));
  var _feedbackToastProps = _$$_REQUIRE(_dependencyMap[28]);
  var _envParams = _$$_REQUIRE(_dependencyMap[29]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[30]));
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[31]);
  var _flightPerks = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[32]));
  var _facilitiesServices = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[33]));
  var _confirmPopupSaveFlight = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[34]));
  var _aemRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[35]));
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[36]);
  var _native = _$$_REQUIRE(_dependencyMap[37]);
  var _momentTimezone = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[38]));
  var _tickerBand = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[39]));
  var _lodash = _$$_REQUIRE(_dependencyMap[40]);
  var _adobe = _$$_REQUIRE(_dependencyMap[41]);
  var _text = _$$_REQUIRE(_dependencyMap[42]);
  var _theme = _$$_REQUIRE(_dependencyMap[43]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[44]);
  var _pageConfigSaga = _$$_REQUIRE(_dependencyMap[45]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[46]);
  var _utils = _$$_REQUIRE(_dependencyMap[47]);
  var _storage = _$$_REQUIRE(_dependencyMap[48]);
  var _storageKey = _$$_REQUIRE(_dependencyMap[49]);
  var _alertApp = _$$_REQUIRE(_dependencyMap[50]);
  var _alertApp2 = _$$_REQUIRE(_dependencyMap[51]);
  var _bottomSheetError = _$$_REQUIRE(_dependencyMap[52]);
  var _bottomSheetUnableLoadLocation = _$$_REQUIRE(_dependencyMap[53]);
  var _appscapadeFlightDetail = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[54]));
  var _flightDetailsCardV = _$$_REQUIRE(_dependencyMap[55]);
  var _bottomSheetMapUnavailable = _$$_REQUIRE(_dependencyMap[56]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[57]);
  var _saveFlightTravelOption = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[58]));
  var _addReturnCalendar = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[59]));
  var _useTickerbandMaintanence = _$$_REQUIRE(_dependencyMap[60]);
  var _reactNativeShare = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[61]));
  var _fly = _$$_REQUIRE(_dependencyMap[62]);
  var _useFlightSaveErrorHandling = _$$_REQUIRE(_dependencyMap[63]);
  var _singleTapButton = _$$_REQUIRE(_dependencyMap[64]);
  var _useFlightDetail2 = _$$_REQUIRE(_dependencyMap[65]);
  var _errorOverlayApp = _$$_REQUIRE(_dependencyMap[66]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[67]);
  var _errorCloud = _$$_REQUIRE(_dependencyMap[68]);
  var _mytravelRedux = _$$_REQUIRE(_dependencyMap[69]);
  var _flightDetail = _$$_REQUIRE(_dependencyMap[70]);
  var _informationHubCard = _$$_REQUIRE(_dependencyMap[71]);
  var _firebase = _$$_REQUIRE(_dependencyMap[72]);
  var _flightDetailV2Helper = _$$_REQUIRE(_dependencyMap[73]);
  var _flightDetails = _$$_REQUIRE(_dependencyMap[74]);
  var _useFlightDetailClickEvent = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[75]));
  var _useModal4 = _$$_REQUIRE(_dependencyMap[76]);
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[77]);
  var _modalSaveAndShare = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[78]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[79]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SCREEN_NAME = "FlightDetails";
  var toastButtonStyleAdded = Object.assign({}, _text.presets.textLink, {
    fontWeight: "normal",
    color: _theme.color.palette.lightBlue,
    alignItems: "flex-end"
  });
  var toastTextStyleAdded = Object.assign({}, _text.presets.bodyTextRegular, {
    color: _theme.color.palette.whiteGrey,
    width: "80%"
  });
  var feedBackToastStyle = exports.feedBackToastStyle = {
    alignItems: "flex-start",
    backgroundColor: _theme.color.palette.black,
    borderRadius: 8,
    height: 60,
    marginBottom: 20,
    marginHorizontal: 16,
    width: "95%"
  };
  var toastButtonStyle = exports.toastButtonStyle = Object.assign({}, _text.presets.textLink, {
    fontWeight: "normal",
    color: _theme.color.palette.lightBlue,
    alignItems: "flex-end"
  });
  var toastTextStyle = exports.toastTextStyle = Object.assign({}, _text.presets.bodyTextRegular, {
    color: _theme.color.palette.whiteGrey,
    width: "80%"
  });
  var toastStyleAddedFlight = {
    width: "100%",
    height: 60,
    marginBottom: 24,
    alignItems: "flex-start"
  };
  var TypePressDetailFlightCard = exports.TypePressDetailFlightCard = /*#__PURE__*/function (TypePressDetailFlightCard) {
    TypePressDetailFlightCard["CHECK_IN_ROW"] = "CHECK_IN_ROW";
    TypePressDetailFlightCard["GATE"] = "GATE";
    TypePressDetailFlightCard["BAGGAGE_BELT"] = "BAGGAGE_BELT";
    TypePressDetailFlightCard["TERMINAL"] = "TERMINAL";
    TypePressDetailFlightCard["SKYTRAIN"] = "SKYTRAIN";
    return TypePressDetailFlightCard;
  }({});
  var TypeTransport = exports.TypeTransport = /*#__PURE__*/function (TypeTransport) {
    TypeTransport["PUBLIC"] = "public";
    TypeTransport["TRANSIT"] = "transit";
    return TypeTransport;
  }({});
  var TypeGetIntoAirport = exports.TypeGetIntoAirport = /*#__PURE__*/function (TypeGetIntoAirport) {
    TypeGetIntoAirport["LINK1"] = "LINK1";
    TypeGetIntoAirport["LINK2"] = "LINK2";
    TypeGetIntoAirport["LINK3"] = "LINK3";
    return TypeGetIntoAirport;
  }({});
  var wrapHeader = {
    paddingHorizontal: 14,
    position: "absolute",
    zIndex: 10
  };
  var getValue = function getValue(value, defaultValue) {
    return value || defaultValue;
  };
  var intervalRefreshFlight;
  var FLY_APPSCAPADE_TYPE = "general_entry_point";
  var FlightDetails = exports.FlightDetails = function FlightDetails(_ref) {
    var _dataCommonAEM$data, _dataCommonAEM$data2, _dataCommonAEM$data3, _dataCommonAEM$data4, _dataCommonAEM$data5, _useContext, _flyFlightDetailsPayl3, _flyFlightDetailsPayl7, _flyFlightDetailsPayl8, _flyFlightDetailsPayl1, _flyFlightDetailsPayl41, _flyFlightDetailsPayl42, _flyFlightDetailsPayl43, _flyFlightDetailsPayl44, _flyFlightDetailsPayl45, _flyFlightDetailsPayl46;
    var navigation = _ref.navigation,
      route = _ref.route;
    var _route$params = route.params,
      payload = _route$params.payload,
      direction = _route$params.direction,
      isFromScanBoardingPass = _route$params.isFromScanBoardingPass,
      referrer = _route$params.referrer,
      isFromUpcomingEvent = _route$params.isFromUpcomingEvent;
    var flyItem = payload.item;
    var dispatch = (0, _reactRedux.useDispatch)();
    var isFocused = (0, _native.useIsFocused)();
    var enableFlySavePrompt = (0, _remoteConfig.getFeatureFlagInit)(_remoteConfig.REMOTE_CONFIG_FLAGS.FLIGHT_SAVEPROMPT);
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var toastForRefresh = (0, _react.useRef)(null);
    var toastForRemoveFlight = (0, _react.useRef)(null);
    var toastForSavedFlight = (0, _react.useRef)(null);
    var unableToLoadLocationRef = (0, _react.useRef)(null);
    var mapUnavailable = (0, _react.useRef)(null);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      loadingFlightMap = _useState2[0],
      setLoadingFlightMap = _useState2[1];
    var _useState3 = (0, _react.useState)(0),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      heightTickerBand = _useState4[0],
      setHeightTickerBand = _useState4[1];
    var _useState5 = (0, _react.useState)(null),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      travelChecklistAEM = _useState6[0],
      setTravelChecklistAEM = _useState6[1];
    var _useState7 = (0, _react.useState)(true),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      isTravelChecklistAEMLoading = _useState8[0],
      setIsTravelChecklistAEMLoading = _useState8[1];
    var loadingShareRef = (0, _react.useRef)(false);
    var flyFlightDetailsPayload = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flyFlightDetailsPayload);
    var flyFlightDetailsFetching = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flyFlightDetailsFetching);
    var flyFlightDetailsFetchingFirst = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flyFlightDetailsFetchingFirst);
    var getIntoCityOrAirportPayload = (0, _reactRedux.useSelector)(function (state) {
      return _flyRedux.FlySelectors.getIntoCityOrAirportPayload(state);
    });
    var flyFlightDetailsError = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flyFlightDetailsError);
    var insertFlightPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.insertFlightPayload); //list of saved flights
    var removeFlightPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.removeFlightPayload); //info of flight is unsaved
    var flyLastUpdatedTimeStamp = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flyLastUpdatedTimeStamp);
    var callAEMData = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.FLY_APPSCAPADE));
    var flyAppscapadeData = callAEMData == null ? undefined : callAEMData.data;
    var dataCommonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig("AEM_COMMON_DATA"));
    var msg47 = dataCommonAEM == null || (_dataCommonAEM$data = dataCommonAEM.data) == null || (_dataCommonAEM$data = _dataCommonAEM$data.messages) == null ? undefined : _dataCommonAEM$data.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG47";
    });
    var msg48 = dataCommonAEM == null || (_dataCommonAEM$data2 = dataCommonAEM.data) == null || (_dataCommonAEM$data2 = _dataCommonAEM$data2.messages) == null ? undefined : _dataCommonAEM$data2.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG48";
    });
    var msg58 = dataCommonAEM == null || (_dataCommonAEM$data3 = dataCommonAEM.data) == null || (_dataCommonAEM$data3 = _dataCommonAEM$data3.messages) == null ? undefined : _dataCommonAEM$data3.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG58";
    });
    var msg65 = dataCommonAEM == null || (_dataCommonAEM$data4 = dataCommonAEM.data) == null || (_dataCommonAEM$data4 = _dataCommonAEM$data4.messages) == null ? undefined : _dataCommonAEM$data4.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG65";
    });
    var iconUrl = (0, _mediaHelper.handleImageUrl)(msg47 == null ? undefined : msg47.icon);
    var inf22 = dataCommonAEM == null || (_dataCommonAEM$data5 = dataCommonAEM.data) == null || (_dataCommonAEM$data5 = _dataCommonAEM$data5.informatives) == null ? undefined : _dataCommonAEM$data5.find(function (e) {
      return (e == null ? undefined : e.code) === "INF22";
    });
    var initSelectedTravelOption = direction === _flightProps.FlightDirection.departure ? _saveFlightTravelOption.TravelOption.iAmTravelling : _saveFlightTravelOption.TravelOption.iAmPicking;
    var tickerbandMaintananceHook = (0, _useTickerbandMaintanence.useTickerbandMaintanance)(_useTickerbandMaintanence.MaintananceTickerBandType.FLY_DETAILS);
    var scrollRef = (0, _react.useRef)(null);
    var alertApp = (0, _react.useRef)(null);
    var _useState9 = (0, _react.useState)(false),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      fabVisibility = _useState0[0],
      setFabVisibility = _useState0[1];
    var _useModal = (0, _useModal4.useModal)("saveConnectingFlightDetail"),
      isModalVisible = _useModal.isModalVisible,
      openModal = _useModal.openModal,
      closeModal = _useModal.closeModal;
    var _useModal2 = (0, _useModal4.useModal)("flightDetailSaveTravelOption"),
      isModalVisibleTravelOption = _useModal2.isModalVisible,
      openModalTravelOption = _useModal2.openModal,
      closeModalTravelOption = _useModal2.closeModal;
    var _useState1 = (0, _react.useState)(false),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      showModalTravelOption = _useState10[0],
      setShowModalTravelOption = _useState10[1];
    var _useState11 = (0, _react.useState)(initSelectedTravelOption),
      _useState12 = (0, _slicedToArray2.default)(_useState11, 2),
      selectedTravelOption = _useState12[0],
      setSelectedTravelOption = _useState12[1];
    var _useState13 = (0, _react.useState)(false),
      _useState14 = (0, _slicedToArray2.default)(_useState13, 2),
      showNoInternetError = _useState14[0],
      setShowNoInternetError = _useState14[1];
    var _useState15 = (0, _react.useState)(false),
      _useState16 = (0, _slicedToArray2.default)(_useState15, 2),
      isGoToListingFlight = _useState16[0],
      setIsGoToListingFlight = _useState16[1];
    var _useState17 = (0, _react.useState)(false),
      _useState18 = (0, _slicedToArray2.default)(_useState17, 2),
      isRefereePopupVisible = _useState18[0],
      setRefereePopupVisible = _useState18[1];
    var flyShowTickerBand = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flyShowTickerBand);
    var _useTickerbandMaintan = (0, _useTickerbandMaintanence.useTickerbandMaintanance)(_useTickerbandMaintanence.MaintananceTickerBandType.FLY_DETAILS),
      onPressCTA = _useTickerbandMaintan.onPressCTA,
      onCloseTickerBand = _useTickerbandMaintan.onCloseTickerBand,
      tickerBand = _useTickerbandMaintan.tickerBand,
      tickerBandDescription = _useTickerbandMaintan.tickerBandDescription,
      tickerBandButtonText = _useTickerbandMaintan.tickerBandButtonText,
      isShowMaintenance = _useTickerbandMaintan.isShowMaintenance,
      isShowTickerband = _useTickerbandMaintan.isShowTickerband,
      fetchTickerbandMaintanance = _useTickerbandMaintan.fetchTickerbandMaintanance,
      errorData = _useTickerbandMaintan.errorData;
    var _useState19 = (0, _react.useState)(null),
      _useState20 = (0, _slicedToArray2.default)(_useState19, 2),
      statusSaveAndShare = _useState20[0],
      setStatusSaveAndShare = _useState20[1];
    var _useModal3 = (0, _useModal4.useModal)("modalSaveAndShare"),
      isModalSaveAndShare = _useModal3.isModalVisible,
      openModalSaveAndShare = _useModal3.openModal,
      closeModalSaveAndShare = _useModal3.closeModal;
    var isShowModalConfirmSaveFly = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flyShowModalConfirmSaveFly);
    var isCheckInOnlineLoadFailed = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flyCheckInOnlineLoadFailed);
    var myTravelFlightsPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.myTravelFlightsPayload);
    var _useState21 = (0, _react.useState)(false),
      _useState22 = (0, _slicedToArray2.default)(_useState21, 2),
      mapRMFlag = _useState22[0],
      setMapRMFlag = _useState22[1];
    var _useState23 = (0, _react.useState)(direction === _flightProps.FlightDirection.departure ? _saveFlightTravelOption.TravelOption.iAmTravelling : _saveFlightTravelOption.TravelOption.iAmPicking),
      _useState24 = (0, _slicedToArray2.default)(_useState23, 2),
      selectedTopTravelOption = _useState24[0],
      setSelectedTopTravelOption = _useState24[1];
    var isTravellerLocal = selectedTopTravelOption === _saveFlightTravelOption.TravelOption.iAmTravelling;
    var isDepartureLocal = direction === _flightProps.FlightDirection.departure;
    var _useFlightDetailClick = (0, _useFlightDetailClickEvent.default)({
        isTraveller: isTravellerLocal,
        isDeparture: isDepartureLocal
      }),
      logClickEvent = _useFlightDetailClick.logClickEvent;
    var _useState25 = (0, _react.useState)(false),
      _useState26 = (0, _slicedToArray2.default)(_useState25, 2),
      loadingSaveFlight = _useState26[0],
      setLoadingSaveFlight = _useState26[1];
    var _useState27 = (0, _react.useState)(false),
      _useState28 = (0, _slicedToArray2.default)(_useState27, 2),
      showCalendarModal = _useState28[0],
      setShowCalendarModal = _useState28[1];
    var connectingFlightPayload = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.connectingFlightPayload);
    var isSharing = (0, _react.useRef)(false);
    var _useContext$Handlers = (_useContext = (0, _react.useContext)(_fly.FLY_CONTEXT)) == null ? undefined : _useContext.Handlers,
      fly_eci_dynamic_display = _useContext$Handlers.fly_eci_dynamic_display,
      flyDetailP1Flag = _useContext$Handlers.flyDetailP1Flag;
    var enableEciDynamicDisplay = (0, _remoteConfig.isFlagOnCondition)(fly_eci_dynamic_display);
    var isFlightDetailP1 = (0, _remoteConfig.isFlagOnCondition)(flyDetailP1Flag);
    var _useState29 = (0, _react.useState)(false),
      _useState30 = (0, _slicedToArray2.default)(_useState29, 2),
      showSaveFlightWhenOnlineCheckIn = _useState30[0],
      setShowSaveFlightWhenOnlineCheckIn = _useState30[1];
    var _useOnlineCheckIn = (0, _flightDetails.useOnlineCheckIn)({
        direction: direction,
        flyItem: flyItem,
        navigation: navigation,
        setShowSaveFlightWhenOnlineCheckIn: setShowSaveFlightWhenOnlineCheckIn
      }),
      handleCheckInOnline = _useOnlineCheckIn.handleCheckInOnline,
      handleSaveFlightWhenCheckInOnline = _useOnlineCheckIn.handleSaveFlightWhenCheckInOnline;
    var flightTerminalDisclaimerText = (0, _react.useMemo)(function () {
      var _flyFlightDetailsPayl;
      return flyFlightDetailsPayload == null || (_flyFlightDetailsPayl = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl.displayTerminalDisclaimer;
    }, [flyFlightDetailsPayload]);
    var shouldShowSQArrivalTerminalInfo = (0, _react.useMemo)(function () {
      return !!flightTerminalDisclaimerText && !!(inf22 != null && inf22.informativeText);
    }, [inf22 == null ? undefined : inf22.informativeText, flightTerminalDisclaimerText]);
    var flightHeroImageStyle = shouldShowSQArrivalTerminalInfo ? {
      marginBottom: 32
    } : {};
    var dropDownSelectCardContainerStyle = shouldShowSQArrivalTerminalInfo ? {
      bottom: -40
    } : {};
    var flightDetailsCardDisclaimerText = flightTerminalDisclaimerText ? (0, _jsxRuntime.jsx)(_text.Text, {
      text: `(${flightTerminalDisclaimerText})`,
      style: _flightDetailsStyles.styles.terminalDisclaimerText
    }) : null;
    var _useFlightDetail = (0, _useFlightDetail2.useFlightDetail)(),
      getDeeplinkShare = _useFlightDetail.getDeeplinkShare,
      getTravelChecklistAEM = _useFlightDetail.getTravelChecklistAEM;
    var _useState31 = (0, _react.useState)(false),
      _useState32 = (0, _slicedToArray2.default)(_useState31, 2),
      isRefereeModalEverShown = _useState32[0],
      setRefereeModalEverShown = _useState32[1];
    var _useState33 = (0, _react.useState)(false),
      _useState34 = (0, _slicedToArray2.default)(_useState33, 2),
      isNoInternetConnection = _useState34[0],
      setNoInterConnection = _useState34[1];
    var _useState35 = (0, _react.useState)(false),
      _useState36 = (0, _slicedToArray2.default)(_useState35, 2),
      showRemoveFlightAlert = _useState36[0],
      setShowRemoveFlightAlert = _useState36[1];
    var isFlightAfter24h = (0, _react.useMemo)(function () {
      if (flyFlightDetailsPayload != null && flyFlightDetailsPayload.flightDetailsData) {
        var _flyFlightDetailsPayl2 = flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData,
          scheduledDate = _flyFlightDetailsPayl2.scheduledDate,
          scheduledTime = _flyFlightDetailsPayl2.scheduledTime,
          actualTimestamp = _flyFlightDetailsPayl2.actualTimestamp,
          displayTimestamp = _flyFlightDetailsPayl2.displayTimestamp;
        var priorityTime = actualTimestamp || displayTimestamp || `${scheduledDate} ${scheduledTime}`;
        if (priorityTime) {
          var formatedScheduledTime = _momentTimezone.default.tz(priorityTime, "YYYY-MM-DD HH:mm", "Asia/Singapore");
          var currentTimeToUTC = (0, _momentTimezone.default)().tz("Asia/Singapore");
          var isFlighterAfter24h = (0, _momentTimezone.default)(currentTimeToUTC).add(1, "day") <= formatedScheduledTime;
          return isFlighterAfter24h;
        }
        return false;
      }
      return false;
    }, [flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData]);
    var shouldShowShareButton = (flyFlightDetailsPayload == null || (_flyFlightDetailsPayl3 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl3.flightNumber) && !isShowMaintenance;
    var _useMemo = (0, _react.useMemo)(function () {
        var isPassenger = false;
        var isSaved = false;
        var data = (flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData) || {};
        if (myTravelFlightsPayload != null && myTravelFlightsPayload.getMyTravelFlightDetails) {
          var idx = myTravelFlightsPayload.getMyTravelFlightDetails.findIndex(function (savedFlight) {
            return savedFlight.flightNumber === data.flightNumber && savedFlight.scheduledDate === data.scheduledDate && (savedFlight.flightDirection || savedFlight.direction) === (data.flightDirection || data.direction);
          });
          if (idx >= 0) {
            isSaved = true;
            isPassenger = myTravelFlightsPayload.getMyTravelFlightDetails[idx].isPassenger;
          }
        }
        return {
          isSaved: isSaved,
          isPassenger: isPassenger
        };
      }, [flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData, myTravelFlightsPayload == null ? undefined : myTravelFlightsPayload.getMyTravelFlightDetails]),
      isSaved = _useMemo.isSaved,
      isPassenger = _useMemo.isPassenger;
    var isButtonSaveHidden = (0, _react.useMemo)(function () {
      if (!isLoggedIn) {
        return false;
      }
      if (!(flyFlightDetailsPayload != null && flyFlightDetailsPayload.flightDetailsData) || !(myTravelFlightsPayload != null && myTravelFlightsPayload.getMyTravelFlightDetails)) {
        return true;
      }
      if (isShowMaintenance) {
        return false;
      }
      return (flyItem == null ? undefined : flyItem.isMSError) || isSaved;
    }, [isLoggedIn, flyItem == null ? undefined : flyItem.isMSError, isSaved, myTravelFlightsPayload == null ? undefined : myTravelFlightsPayload.getMyTravelFlightDetails, flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData]);
    var refRetryAction = (0, _react.useRef)(null);
    (0, _react.useEffect)(function () {
      if (flyFlightDetailsError) {
        var _toastForRefresh$curr;
        toastForRefresh == null || (_toastForRefresh$curr = toastForRefresh.current) == null || _toastForRefresh$curr.show(_feedbackToastProps.DURATION.FOREVER);
        setIsTravelChecklistAEMLoading(false);
      }
    }, [flyFlightDetailsError]);
    (0, _useFlightSaveErrorHandling.useFlightSaveErrorHandling)(isSharing.current, statusSaveAndShare ? true : false);
    var wrapHeaderStyles = (0, _react.useMemo)(function () {
      return {
        flexDirection: "row",
        width: "100%",
        top: (0, _utils.simpleCondition)({
          condition: isShowTickerband,
          ifValue: 25,
          elseValue: 58
        }),
        justifyContent: "center",
        paddingHorizontal: 14,
        position: "absolute",
        zIndex: 99999,
        alignItems: "center"
      };
    }, [isShowTickerband]);
    var getPositionBackButton = (0, _react.useMemo)(function () {
      return {
        top: isShowTickerband ? heightTickerBand + 25 : 58
      };
    }, [isShowTickerband, heightTickerBand]);
    (0, _react.useEffect)(function () {
      (0, _screenHook.setCurrentScreenActive)(`Flight_Detail_${flyItem == null ? undefined : flyItem.flightNumber}`);
      (0, _adobe.commonTrackingScreen)(`Flight_Detail_${flyItem == null ? undefined : flyItem.flightNumber}`, (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
      getFlightDetailsApiFirst();
      setIntervalRefeshFlight();
      return function () {
        clearInterval(intervalRefreshFlight);
      };
    }, [route.params, isLoggedIn]);
    var filterDataForFly = function filterDataForFly(data) {
      var _data$list;
      if ((0, _lodash.isEmpty)(data == null ? undefined : data.list)) return null;
      return data == null || (_data$list = data.list) == null ? undefined : _data$list.find(function (e) {
        return (e == null ? undefined : e.type) === FLY_APPSCAPADE_TYPE;
      });
    };
    (0, _react.useEffect)(function () {
      if (!(callAEMData != null && callAEMData.data)) {
        dispatch(_aemRedux.default.getAemConfigData({
          name: _aemRedux.AEM_PAGE_NAME.FLY_APPSCAPADE,
          pathName: "getFlyAppscapade",
          callBackAfterSuccess: filterDataForFly
        }));
      }
    }, [callAEMData == null ? undefined : callAEMData.data]);
    var handleGetTravelChecklistAEM = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        setIsTravelChecklistAEMLoading(true);
        var isDeparture = direction === _flightProps.FlightDirection.departure;
        var isTraveller = selectedTopTravelOption === _saveFlightTravelOption.TravelOption.iAmTravelling;
        var _flyFlightDetailsPayl4 = flyFlightDetailsPayload.flightDetailsData,
          flightNumber = _flyFlightDetailsPayl4.flightNumber,
          airline = _flyFlightDetailsPayl4.airline,
          displayTerminal = _flyFlightDetailsPayl4.displayTerminal,
          destinationCode = _flyFlightDetailsPayl4.destinationCode,
          departingCode = _flyFlightDetailsPayl4.departingCode;
        var res = yield getTravelChecklistAEM({
          flight_no: flightNumber,
          user_profile: isTraveller ? _flightDetail.UserProfileTagNameEnum.TRAVELLER : _flightDetail.UserProfileTagNameEnum.MEETERS_AND_GREETERS,
          direction: isDeparture ? _flightDetail.DirectionTagNameEnum.DEPARTURE : _flightDetail.DirectionTagNameEnum.ARRIVAL,
          airport: isDeparture ? destinationCode : departingCode,
          airline: airline,
          terminal: displayTerminal
        });
        setTravelChecklistAEM(res);
        setIsTravelChecklistAEMLoading(false);
      });
      return function handleGetTravelChecklistAEM() {
        return _ref2.apply(this, arguments);
      };
    }();
    var checkReloadTravelChecklistAEM = (0, _react.useMemo)(function () {
      var _flyFlightDetailsPayl5, _flyFlightDetailsPayl6;
      if (!(flyFlightDetailsPayload != null && flyFlightDetailsPayload.flightDetailsData)) {
        return "";
      }
      return `${flyFlightDetailsPayload == null || (_flyFlightDetailsPayl5 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl5.flightNumber}-${flyFlightDetailsPayload == null || (_flyFlightDetailsPayl6 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl6.scheduledDate}-${direction}`;
    }, [flyFlightDetailsPayload == null || (_flyFlightDetailsPayl7 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl7.flightNumber, flyFlightDetailsPayload == null || (_flyFlightDetailsPayl8 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl8.scheduledDate, direction]);
    (0, _react.useEffect)(function () {
      if (isFlightDetailP1 && checkReloadTravelChecklistAEM) {
        handleGetTravelChecklistAEM();
      }
    }, [checkReloadTravelChecklistAEM, selectedTopTravelOption, isFlightDetailP1]);
    var flightDetailSectionData = (0, _react.useMemo)(function () {
      var _travelChecklistAEM$d;
      if (!isFlightDetailP1 || !(travelChecklistAEM != null && travelChecklistAEM.success) || !(travelChecklistAEM != null && (_travelChecklistAEM$d = travelChecklistAEM.data) != null && (_travelChecklistAEM$d = _travelChecklistAEM$d.sections) != null && _travelChecklistAEM$d.length)) return null;
      return Object.assign({}, travelChecklistAEM.data, {
        sections: travelChecklistAEM.data.sections.sort(function (a, b) {
          return Number(a.sequenceNumber) - Number(b.sequenceNumber);
        })
      });
    }, [travelChecklistAEM, isFlightDetailP1]);

    /**
     * Display referree popup
     * if flight details is loaded and flight is not saved
     * flight number from referrer link must be the same as in screen
     * flow is from referrer link and referrer is different from referree
     * has appscapade data ready
     * flight is eligible (DEP + after 24h)
     * shown for the first time
     */
    (0, _react.useEffect)(function () {
      var _flyFlightDetailsPayl9, _flyFlightDetailsPayl0;
      if (flyFlightDetailsPayload != null && (_flyFlightDetailsPayl9 = flyFlightDetailsPayload.flightDetailsData) != null && _flyFlightDetailsPayl9.flightNumber && (flyItem == null ? undefined : flyItem.flightNumber) === (flyFlightDetailsPayload == null || (_flyFlightDetailsPayl0 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl0.flightNumber) && referrer && referrer !== (profilePayload == null ? undefined : profilePayload.id) && flyAppscapadeData && flyFlightDetailsPayload != null && flyFlightDetailsPayload.flightDetailsData && !isSaved && isFlightAfter24h && !isRefereePopupVisible && !isRefereeModalEverShown) {
        setRefereePopupVisible(true);
        setRefereeModalEverShown(true);
      }
    }, [flyFlightDetailsPayload == null || (_flyFlightDetailsPayl1 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl1.flightNumber, isSaved, flyItem == null ? undefined : flyItem.flightNumber, referrer, flyAppscapadeData, isFlightAfter24h]);
    var setIntervalRefeshFlight = function setIntervalRefeshFlight() {
      var _env;
      var refreshInterval = (_env = (0, _envParams.env)()) == null ? undefined : _env.FLIGHT_REFRESH_INTERVAL;
      clearInterval(intervalRefreshFlight);
      intervalRefreshFlight = setInterval(function () {
        if (!isFocused) {
          return;
        }
        if (!showNoInternetError) {
          _reactNative2.InteractionManager.runAfterInteractions(function () {
            refreshFlightDetails();
          });
        }
      }, refreshInterval);
    };
    var getTickerBand = function getTickerBand() {
      dispatch(_aemRedux.default.getAemConfigData({
        name: _aemRedux.AEM_PAGE_NAME.TICKER_BAND_FLIGHT,
        pathName: "getTickerbandFly",
        forceRequest: true
      }));
    };
    var clearFlyDetailData = function clearFlyDetailData() {
      dispatch(_flyRedux.FlyCreators.flyClearFlightDetailsPayload());
      dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
      dispatch(_flyRedux.FlyCreators.flyShowModalConfirmSaveFly(false));
      dispatch(_flyRedux.FlyCreators.flyCheckInOnlineLoadFailed(false));
      dispatch(_flyRedux.FlyCreators.getAppscapadeBannerReset());
    };
    (0, _react.useEffect)(function () {
      return function () {
        clearFlyDetailData();
      };
    }, []);
    var checkInternetConnection = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        return isConnected;
      });
      return function checkInternetConnection() {
        return _ref3.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      checkInternetConnection().then(function (isConnection) {
        if (!isConnection) {
          setShowNoInternetError(true);
        }
      });
    }, []);
    (0, _react.useEffect)(function () {
      if (!isSaved) return;
      if (!isPassenger) {
        setSelectedTopTravelOption(_saveFlightTravelOption.TravelOption.iAmPicking);
        setSelectedTravelOption(_saveFlightTravelOption.TravelOption.iAmPicking);
      } else {
        setSelectedTopTravelOption(_saveFlightTravelOption.TravelOption.iAmTravelling);
        setSelectedTravelOption(_saveFlightTravelOption.TravelOption.iAmTravelling);
      }
    }, [flyFlightDetailsPayload, isSaved, isPassenger]);
    (0, _react.useEffect)(function () {
      if (isFromUpcomingEvent) {
        var flightDetailsResult = flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData;
        (0, _flightDetailV2Helper.handleUpdateUpcomingAndSavedFlight)(flightDetailsResult);
      }
    }, [flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData, isFromUpcomingEvent]);

    /**
     * Save flight success response
     */
    (0, _react.useEffect)(function () {
      var _insertFlightPayload$;
      if (insertFlightPayload != null && (_insertFlightPayload$ = insertFlightPayload.insertFlightData) != null && _insertFlightPayload$.success || insertFlightPayload != null && insertFlightPayload.recordExist) {
        (0, _screenHelper.putFlightInfoToAdobeAnalyticAfterSaveUnSave)({
          data: insertFlightPayload,
          isSuccess: true,
          tag: _adobe.AdobeTagName.CAppFlyFlightDetailSaveFlight,
          flyProfile: "flying",
          pageName: _adobe.AdobeTagName.CAppFlyFlightDetail,
          isSaveFlight: true
        });
        if (insertFlightPayload != null && insertFlightPayload.isInsertSuccessfully && isFocused) {
          var _env2, _insertFlightPayload$2;
          setLoadingSaveFlight(false);
          var timeStamp = new Date().getTime();
          // if user hasnt save any flight within 24hours or user has only 1 saved flight
          // show save connecting flight modal
          // else show native share popup and finish save
          var showConnectingFlight = ((0, _mmkvStorage.getLastSavedFlightTime)() + ((_env2 = (0, _envParams.env)()) == null ? undefined : _env2.FLIGHT_SHOW_POPUP_ADD_RETURN) < timeStamp || (0, _lodash.size)(myTravelFlightsPayload == null ? undefined : myTravelFlightsPayload.getMyTravelFlightDetails) === 1) && (insertFlightPayload == null || (_insertFlightPayload$2 = insertFlightPayload.flightData) == null ? undefined : _insertFlightPayload$2.isPassenger);
          if (!showConnectingFlight) {
            if (statusSaveAndShare) {
              setStatusSaveAndShare("SAVE SUCCESS");
              closeModalSaveAndShare();
            } else {
              var _toastForSavedFlight$;
              closeModalTravelOption();
              toastForSavedFlight == null || (_toastForSavedFlight$ = toastForSavedFlight.current) == null || _toastForSavedFlight$.show(_feedbackToastProps.DURATION.LENGTH_LONG);
            }
          } else {
            if (statusSaveAndShare) {
              setStatusSaveAndShare("SAVE SUCCESS");
              closeModalSaveAndShare();
            } else {
              openModal();
              (0, _mmkvStorage.setLastSavedFlightTime)(0);
            }
          }
          (0, _storage.save)(_storageKey.StorageKey.isSaveFlightTriggered, true);
        }
      }
      if (insertFlightPayload != null && insertFlightPayload.errorFlag) {
        setLoadingSaveFlight(false);
        closeModalTravelOption();
      }
    }, [insertFlightPayload, statusSaveAndShare]);
    (0, _react.useEffect)(function () {
      if (removeFlightPayload != null && removeFlightPayload.isRemovedSuccessFully) {
        var _toastForSavedFlight$2, _toastForRemoveFlight;
        (0, _screenHelper.putFlightInfoToAdobeAnalyticAfterSaveUnSave)({
          data: removeFlightPayload,
          isSuccess: true,
          tag: _adobe.AdobeTagName.CAppFlyFlightDetailRemoveFlight,
          flyProfile: "flying",
          pageName: _adobe.AdobeTagName.CAppFlyFlightDetail,
          isSaveFlight: false
        });
        toastForSavedFlight == null || (_toastForSavedFlight$2 = toastForSavedFlight.current) == null || _toastForSavedFlight$2.closeNow();
        toastForRemoveFlight == null || (_toastForRemoveFlight = toastForRemoveFlight.current) == null || _toastForRemoveFlight.show(_feedbackToastProps.DURATION.LENGTH_SHORT);
        setSelectedTravelOption(direction === _flightProps.FlightDirection.departure ? _saveFlightTravelOption.TravelOption.iAmTravelling : _saveFlightTravelOption.TravelOption.iAmPicking);
        setSelectedTopTravelOption(direction === _flightProps.FlightDirection.departure ? _saveFlightTravelOption.TravelOption.iAmTravelling : _saveFlightTravelOption.TravelOption.iAmPicking);
        dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
      }
    }, [removeFlightPayload]);
    (0, _react.useEffect)(function () {
      return function () {
        return setIsGoToListingFlight(false);
      };
    }, []);
    (0, _react.useEffect)(function () {
      var fetchAtomRMConfig = function fetchAtomRMConfig() {
        var mapFlagEnable = (0, _remoteConfig.isFlagON)(_remoteConfig.REMOTE_CONFIG_FLAGS.ATOMS_MAP);
        setMapRMFlag(mapFlagEnable);
      };
      fetchAtomRMConfig();
    }, []);
    var getTitleDropdownTravelOption = (0, _react.useMemo)(function () {
      if (direction === _flightProps.FlightDirection.departure || selectedTopTravelOption === _saveFlightTravelOption.TravelOption.iAmTravelling) {
        return selectedTopTravelOption;
      }
      if (selectedTopTravelOption === _saveFlightTravelOption.TravelOption.iAmPicking) {
        return "dropdownSelectCard.imPickingSomeone";
      }
    }, [selectedTopTravelOption, direction]);
    var filterDateAddReturnCalendar = (0, _react.useMemo)(function () {
      var _flyFlightDetailsPayl10;
      if ((flyFlightDetailsPayload == null || (_flyFlightDetailsPayl10 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl10.type) === "loading") {
        return (0, _momentTimezone.default)().format("YYYY-MM-DD");
      } else {
        var _flyFlightDetailsPayl11;
        return (0, _momentTimezone.default)(flyFlightDetailsPayload == null || (_flyFlightDetailsPayl11 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl11.displayTimestamp).format("YYYY-MM-DD") || (0, _momentTimezone.default)().format("YYYY-MM-DD");
      }
    }, [flyFlightDetailsPayload]);
    var getFlightDetailsApiFirst = function getFlightDetailsApiFirst() {
      var _flyItem$flightNumber;
      dispatch(_flyRedux.FlyCreators.flyFlightDetailsRequestFirst(direction, flyItem == null ? undefined : flyItem.flightNumber, flyItem == null ? undefined : flyItem.flightDate, _flightProps.FlightRequestType.FlightDefault, flyItem == null || (_flyItem$flightNumber = flyItem.flightNumber) == null ? undefined : _flyItem$flightNumber.substring(0, 2), flyItem == null ? undefined : flyItem.flightStatus, isFromScanBoardingPass));
      dispatch(_flyRedux.FlyCreators.flightDetailPerkRequest());
    };
    var getFlightDetailsApi = function getFlightDetailsApi() {
      var _flyItem$flightNumber2;
      var flightRequest = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : _flightProps.FlightRequestType.FlightDefault;
      dispatch(_flyRedux.FlyCreators.flyFlightDetailsRequest(direction, flyItem == null ? undefined : flyItem.flightNumber, flyItem == null ? undefined : flyItem.flightDate, flightRequest, flyItem == null || (_flyItem$flightNumber2 = flyItem.flightNumber) == null ? undefined : _flyItem$flightNumber2.substring(0, 2), flyItem == null ? undefined : flyItem.flightStatus, isFromScanBoardingPass));
      dispatch(_flyRedux.FlyCreators.flightDetailPerkRequest());
    };
    (0, _react.useEffect)(function () {
      if (flyItem != null && flyItem.flightNumber && flyItem != null && flyItem.flightDate) {
        dispatch(_flyRedux.FlyCreators.flyGetIntoCityOrAirportRequest(direction, `${flyItem == null ? undefined : flyItem.flightNumber}_${flyItem == null ? undefined : flyItem.flightDate}`));
      }
    }, [flyItem == null ? undefined : flyItem.flightNumber, flyItem == null ? undefined : flyItem.flightDate]);
    (0, _react.useEffect)(function () {
      if (statusSaveAndShare === "SAVE SUCCESS") {
        onSharePress();
      }
    }, [statusSaveAndShare]);
    (0, _react.useEffect)(function () {
      if (statusSaveAndShare && insertFlightPayload != null && insertFlightPayload.errorFlag) {
        onShareOnlyPress();
      }
    }, [insertFlightPayload == null ? undefined : insertFlightPayload.errorFlag]);
    var refreshFlightDetails = function refreshFlightDetails() {
      checkInternetConnection().then(function (isConnection) {
        if (isConnection) {
          var _toastForRefresh$curr2;
          if (!(0, _lodash.isEmpty)(refRetryAction.current)) {
            var _refRetryAction$curre, _refRetryAction$curre2;
            handleMap((_refRetryAction$curre = refRetryAction.current) == null ? undefined : _refRetryAction$curre.type, (_refRetryAction$curre2 = refRetryAction.current) == null ? undefined : _refRetryAction$curre2.item);
            return;
          }
          //prevent data collision when use save/unsave flight at the same time as interval flight details api
          if (isModalVisibleTravelOption ||
          //when use is selecting travel option
          isModalVisible || showRemoveFlightAlert || insertFlightPayload != null && insertFlightPayload.loading || removeFlightPayload != null && removeFlightPayload.loading) {
            return;
          }
          toastForRefresh == null || (_toastForRefresh$curr2 = toastForRefresh.current) == null || _toastForRefresh$curr2.closeNow();
          dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
          getTickerBand();
          getFlightDetailsApi(_flightProps.FlightRequestType.FlightRefresh);
          dispatch(_flyRedux.FlyCreators.flyGetIntoCityOrAirportRequest(direction, `${flyItem == null ? undefined : flyItem.flightNumber}_${flyItem == null ? undefined : flyItem.flightDate}`));
          setShowNoInternetError(false);
        } else {
          var _toastForRefresh$curr3;
          toastForRefresh == null || (_toastForRefresh$curr3 = toastForRefresh.current) == null || _toastForRefresh$curr3.show(_feedbackToastProps.DURATION.FOREVER);
        }
      });
      dispatch(_flyRedux.FlyCreators.flyLastUpdatedTimeStamp((0, _dateTime.flyModuleUpdatedTime)()));
    };
    var onPressTouch = function onPressTouch() {
      var _scrollRef$current;
      (_scrollRef$current = scrollRef.current) == null || _scrollRef$current.scrollTo({
        y: 0,
        animated: true
      });
    };
    var handleScroll = function handleScroll(event) {
      if (event.nativeEvent.contentOffset.y > 200) {
        setFabVisibility(true);
      } else if (event.nativeEvent.contentOffset.y === 0) {
        setFabVisibility(false);
      }
    };
    if (!flyFlightDetailsPayload) {
      return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {});
    }
    var showErrorFeedBackToastMessage = function showErrorFeedBackToastMessage() {
      return (0, _jsxRuntime.jsx)(_feedbackToast.default, {
        ref: toastForRefresh,
        style: _flightDetailsStyles.styles.feedBackToastStyle,
        textButtonStyle: _flightDetailsStyles.styles.toastButtonStyle,
        position: "bottom",
        textStyle: _flightDetailsStyles.styles.toastTextStyle,
        type: _feedbackToastProps.FeedBackToastType.fullWidthFeedBack,
        text: (0, _i18n.translate)("flightLanding.feedBackToastErrorMessage") + flyLastUpdatedTimeStamp
      });
    };
    var showToastForRemoveFlight = function showToastForRemoveFlight() {
      return (0, _jsxRuntime.jsx)(_feedbackToast.default, {
        ref: toastForRemoveFlight,
        style: [_flightDetailsStyles.styles.feedBackToastStyle, isFlightDetailP1 && {
          zIndex: 1000
        }] // zIndex = 1000 to overlap ShowNoInternetConnection (zIndex = 999) if isFlightDetailP1 = true
        ,
        textButtonStyle: _flightDetailsStyles.styles.toastButtonStyle,
        position: "custom",
        positionValue: {
          bottom: 100
        },
        textStyle: _flightDetailsStyles.styles.toastTextStyle,
        type: _feedbackToastProps.FeedBackToastType.smallFeedBack,
        text: (0, _i18n.translate)("flyLanding.removeFlightNew")
      });
    };
    var onClosedSheet = function onClosedSheet() {
      setShowModalTravelOption(false);
    };
    var onCloseConfirmPopUpSavedFlight = function onCloseConfirmPopUpSavedFlight() {
      closeModal();
    };
    var travelOptionTapped = function travelOptionTapped(option) {
      var flyProfile = option === _saveFlightTravelOption.TravelOption.iAmTravelling ? "flying" : "non-flying";
      var flightNumber = flyItem == null ? undefined : flyItem.flightNumber;
      var flightDate = flyItem == null ? undefined : flyItem.flightDate;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlyFlightDetailFlyProfile, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlyFlightDetailFlyProfile, `${flyProfile}|${flightNumber}|${flightDate}`));
      setShowModalTravelOption(false);
      setSelectedTravelOption(option);
      setSelectedTopTravelOption(option);
    };
    var handleMessage58 = function handleMessage58(message) {
      if (message) {
        var _flyFlightDetailsPayl12, _status;
        var status = flyFlightDetailsPayload == null || (_flyFlightDetailsPayl12 = flyFlightDetailsPayload.flightDetailsData) == null || (_flyFlightDetailsPayl12 = _flyFlightDetailsPayl12.flightStatus) == null ? undefined : _flyFlightDetailsPayl12.toLowerCase();
        if ((_status = status) != null && _status.includes("cancelled")) {
          status = `been ${status}`;
        }
        return message.replace("<Flight No.>", flyItem == null ? undefined : flyItem.flightNumber).replace("<departed/landed/been cancelled>", status);
      }
      return message;
    };
    var handleMessage48 = function handleMessage48(message, number, place) {
      if (message) {
        return message.replace("<Flight No.>", number).replace("<country>", place);
      }
      return message;
    };
    var notAbleToSaveAlert = function notAbleToSaveAlert() {
      var _flyFlightDetailsPayl13, _flyItem$flightStatus, _alertApp$current;
      var temp = (flyFlightDetailsPayload == null || (_flyFlightDetailsPayl13 = flyFlightDetailsPayload.flightDetailsData) == null || (_flyFlightDetailsPayl13 = _flyFlightDetailsPayl13.flightStatus) == null ? undefined : _flyFlightDetailsPayl13.split(" ")) || (flyItem == null || (_flyItem$flightStatus = flyItem.flightStatus) == null ? undefined : _flyItem$flightStatus.split(" "));
      var status = (temp == null ? undefined : temp.length) > 0 ? temp[0] : "";
      var message = handleMessage58(msg58 == null ? undefined : msg58.message) || `${(0, _i18n.translate)("flightLanding.flight")} ${flyItem == null ? undefined : flyItem.flightNumber} ${(0, _i18n.translate)("flightLanding.has")} ${status} ${(0, _i18n.translate)("flightLanding.notSaveMessage")}`;
      alertApp == null || (_alertApp$current = alertApp.current) == null || _alertApp$current.show({
        title: (msg58 == null ? undefined : msg58.title) || (0, _i18n.translate)("flightLanding.alert"),
        description: message,
        labelAccept: (msg58 == null ? undefined : msg58.firstButton) || (0, _i18n.translate)("flightLanding.okay"),
        onAccept: function onAccept() {
          return null;
        },
        type: _alertApp2.AlertTypes.ALERT
      });
    };
    var checkFlightCanSave = function checkFlightCanSave(statusTag, newDirection) {
      var _flyFlightDetailsPayl14, _flyFlightDetailsPayl15, _flyFlightDetailsPayl16, _flyFlightDetailsPayl17;
      var status = statusTag == null ? undefined : statusTag.toLowerCase();
      var priorityTime = (flyFlightDetailsPayload == null || (_flyFlightDetailsPayl14 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl14.actualTimestamp) || (flyFlightDetailsPayload == null || (_flyFlightDetailsPayl15 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl15.estimatedTimestamp) || `${flyFlightDetailsPayload == null || (_flyFlightDetailsPayl16 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl16.scheduledDate} ${flyFlightDetailsPayload == null || (_flyFlightDetailsPayl17 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl17.scheduledTime}`;
      var currentTimeToUTC = (0, _momentTimezone.default)().tz("Asia/Singapore");
      var flightTime = _momentTimezone.default.tz(priorityTime, "YYYY-MM-DD HH:mm", "Asia/Singapore");
      if (newDirection === _flightProps.FlightDirection.departure) {
        switch (true) {
          case /departed/gim.test(status):
          case /cancelled/gim.test(status):
            return false;
          default:
            return true;
        }
      } else {
        switch (true) {
          case /cancelled/gim.test(status):
          case /landed/gim.test(status) && (0, _momentTimezone.default)(flightTime).add(1, "hours").format("YYYY-MM-DD HH:mm") < currentTimeToUTC.format("YYYY-MM-DD HH:mm"):
            return false;
          default:
            return true;
        }
      }
    };
    var onSaveFlight = /*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* (isRemove, statusPassenger) {
        var _flyFlightDetailsPayl18, _flyFlightDetailsPayl19;
        logClickEvent(isRemove ? _firebase.ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME.UNSAVE_FLIGHT_BUTTON : _firebase.ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME.SAVE_FLIGHT_BUTTON);
        if (!checkFlightCanSave((flyFlightDetailsPayload == null || (_flyFlightDetailsPayl18 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl18.flightStatus) || (flyItem == null ? undefined : flyItem.flightStatus), direction) && !isRemove) {
          notAbleToSaveAlert();
          return null;
        }
        var data = {
          enterpriseUserId: profilePayload == null ? undefined : profilePayload.email,
          countryOfResidence: profilePayload == null ? undefined : profilePayload.residentialCountry,
          flightNumber: flyItem == null ? undefined : flyItem.flightNumber,
          flightScheduledDate: (flyFlightDetailsPayload == null || (_flyFlightDetailsPayl19 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl19.scheduledDate) || (flyItem == null ? undefined : flyItem.scheduledDate),
          flightDirection: direction,
          flightPax: statusPassenger
        };
        if (isRemove && isLoggedIn) {
          var _flyFlightDetailsPayl20;
          setShowRemoveFlightAlert(true);
          _reactNative2.Alert.alert(msg48 == null ? undefined : msg48.title, handleMessage48(msg48 == null ? undefined : msg48.message, flyItem == null ? undefined : flyItem.flightNumber, flyFlightDetailsPayload == null || (_flyFlightDetailsPayl20 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl20.destinationPlace), [{
            text: msg48 == null ? undefined : msg48.firstButton,
            onPress: function onPress() {
              return setShowRemoveFlightAlert(false);
            }
          }, {
            text: msg48 == null ? undefined : msg48.secondButton,
            style: "cancel",
            onPress: function onPress() {
              var _flyFlightDetailsPayl21, _flyFlightDetailsPayl22;
              var dtAction = (0, _firebase.dtManualActionEvent)(`${_firebase.FE_LOG_PREFIX}App__flight-detail-unsave`);
              dtAction.reportStringValue("flight-detail-unsave-press-flightNumber", `${flyItem == null ? undefined : flyItem.flightNumber}`);
              dtAction.reportStringValue("flight-detail-unsave-press-scheduledDate", `${(flyFlightDetailsPayload == null || (_flyFlightDetailsPayl21 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl21.scheduledDate) || (flyItem == null ? undefined : flyItem.scheduledDate)}`);
              dtAction.reportStringValue("flight-detail-unsave-press-direction", `${direction}`);
              dtAction.reportStringValue("flight-detail-unsave-press-statusPassenger", `${statusPassenger}`);
              setShowRemoveFlightAlert(false);
              var payloadRemove = Object.assign({}, payload);
              payloadRemove.item.scheduledDate = flyFlightDetailsPayload == null || (_flyFlightDetailsPayl22 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl22.scheduledDate;
              dispatch(_mytravelRedux.MytravelCreators.flyMyTravelRemoveFlightRequest(data, payloadRemove));
              dtAction.leaveAction();
            }
          }]);
        } else {
          if (isLoggedIn) {
            openModalTravelOption();
          } else {
            navigation.navigate(_constants.NavigationConstants.authScreen, {
              sourceSystem: sourceSystem,
              callBackAfterLoginSuccess: function callBackAfterLoginSuccess() {
                getFlightDetailsApi();
                openModalTravelOption();
              },
              callBackAfterLoginCancel: function callBackAfterLoginCancel() {
                return null;
              }
            });
          }
        }
      });
      return function onSaveFlight(_x, _x2) {
        return _ref4.apply(this, arguments);
      };
    }();
    var _savedFlightOnPress = /*#__PURE__*/function () {
      var _ref5 = (0, _asyncToGenerator2.default)(function* (_, travelOption) {
        var _flyFlightDetailsPayl23, _flyFlightDetailsPayl24;
        var dtAction = (0, _firebase.dtManualActionEvent)(`${_firebase.FE_LOG_PREFIX}App__flight-detail-save`);
        dtAction.reportStringValue("flight-detail-save-press-flightNumber", `${flyItem == null ? undefined : flyItem.flightNumber}`);
        dtAction.reportStringValue("flight-detail-save-press-scheduledDate", `${(flyFlightDetailsPayload == null || (_flyFlightDetailsPayl23 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl23.scheduledDate) || (flyItem == null ? undefined : flyItem.scheduledDate)}`);
        dtAction.reportStringValue("flight-detail-save-press-direction", `${direction}`);
        dtAction.reportStringValue("flight-detail-save-press-travelOption", `${travelOption}`);
        // call save
        setLoadingSaveFlight(true);
        var data = {
          enterpriseUserId: profilePayload == null ? undefined : profilePayload.email,
          countryOfResidence: profilePayload == null ? undefined : profilePayload.residentialCountry,
          flightNumber: flyItem == null ? undefined : flyItem.flightNumber,
          flightScheduledDate: (flyFlightDetailsPayload == null || (_flyFlightDetailsPayl24 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl24.scheduledDate) || (flyItem == null ? undefined : flyItem.scheduledDate),
          flightDirection: direction,
          flightPax: travelOption ? travelOption === _saveFlightTravelOption.TravelOption.iAmTravelling : selectedTravelOption === _saveFlightTravelOption.TravelOption.iAmTravelling
        };
        if (isLoggedIn) {
          var _payloadForSave, _payloadForSave2, _payloadForSave3, _payloadForSave4, _payloadForSave5, _payloadForSave6;
          var payloadForSave = payload;
          if (!((_payloadForSave = payloadForSave) != null && (_payloadForSave = _payloadForSave.item) != null && _payloadForSave.scheduledTime)) {
            var _flyFlightDetailsPayl25, _flyFlightDetailsPayl26, _flyFlightDetailsPayl27;
            payloadForSave = Object.assign({}, payloadForSave, {
              item: Object.assign({}, payloadForSave.item, {
                scheduledTime: flyFlightDetailsPayload == null || (_flyFlightDetailsPayl25 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl25.scheduledTime,
                flightDate: flyFlightDetailsPayload == null || (_flyFlightDetailsPayl26 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl26.scheduledDate,
                scheduledDate: flyFlightDetailsPayload == null || (_flyFlightDetailsPayl27 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl27.scheduledDate
              })
            });
          }
          if (referrer) {
            payloadForSave.referrer = referrer;
          }
          // enter flight details via FDL
          if (!((_payloadForSave2 = payloadForSave) != null && (_payloadForSave2 = _payloadForSave2.item) != null && _payloadForSave2.departingCode)) {
            var _flyFlightDetailsPayl28;
            payloadForSave.item.departingCode = flyFlightDetailsPayload == null || (_flyFlightDetailsPayl28 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl28.departingCode;
          }
          if (!((_payloadForSave3 = payloadForSave) != null && (_payloadForSave3 = _payloadForSave3.item) != null && _payloadForSave3.destinationCode)) {
            var _flyFlightDetailsPayl29;
            payloadForSave.item.destinationCode = flyFlightDetailsPayload == null || (_flyFlightDetailsPayl29 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl29.destinationCode;
          }
          if (!((_payloadForSave4 = payloadForSave) != null && (_payloadForSave4 = _payloadForSave4.item) != null && _payloadForSave4.direction)) {
            payloadForSave.item.direction = direction;
          }
          if (!((_payloadForSave5 = payloadForSave) != null && (_payloadForSave5 = _payloadForSave5.item) != null && _payloadForSave5.terminal)) {
            var _flyFlightDetailsPayl30;
            payloadForSave.item.terminal = flyFlightDetailsPayload == null || (_flyFlightDetailsPayl30 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl30.displayTerminal;
          }
          if (!((_payloadForSave6 = payloadForSave) != null && (_payloadForSave6 = _payloadForSave6.item) != null && _payloadForSave6.flightNavigationType)) {
            var _flyFlightDetailsPayl31;
            payloadForSave.flightNavigationType = (flyFlightDetailsPayload == null || (_flyFlightDetailsPayl31 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl31.flightNavigationType) || _flightProps.FlightNavigationType.FLightSearchDetail;
          }
          dispatch(_mytravelRedux.MytravelCreators.flyMyTravelInsertFlightRequest(data, payloadForSave));
        } else {
          navigation.navigate(_constants.NavigationConstants.authScreen, {
            sourceSystem: sourceSystem
          });
        }
        dtAction.leaveAction();
      });
      return function savedFlightOnPress(_x3, _x4) {
        return _ref5.apply(this, arguments);
      };
    }();
    var fabContainerBottom = {
      bottom: 120
    };
    var sectionSeparator = {
      height: 90
    };
    if (isButtonSaveHidden) {
      fabContainerBottom = {
        bottom: 40
      };
      sectionSeparator = {
        height: 40
      };
    }
    var showFlightAddedFeedBackToastMessage = function showFlightAddedFeedBackToastMessage() {
      return (0, _jsxRuntime.jsx)(_feedbackToast.default, {
        ref: toastForSavedFlight,
        style: toastStyleAddedFlight,
        textButtonStyle: toastButtonStyleAdded,
        position: "bottom",
        textStyle: toastTextStyleAdded,
        type: _feedbackToastProps.FeedBackToastType.smallFeedBack,
        text: (0, _i18n.translate)("flightDetails.newPopupConfirmSaveFlight.toastFlightSaved"),
        onCallback: function onCallback() {
          return dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
        }
      });
    };
    var onPressFlightCardLinks = function onPressFlightCardLinks(titleCardLink) {
      var flightNumber = flyItem == null ? undefined : flyItem.flightNumber;
      var flightDate = flyItem == null ? undefined : flyItem.flightDate;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlyFlightDetailFlightCardLinks, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlyFlightDetailFlightCardLinks, `${titleCardLink}|${flightNumber}|${flightDate}`));
    };
    var onTrackingTimeline = function onTrackingTimeline(value, sectionKey) {
      var flightNumber = flyItem == null ? undefined : flyItem.flightNumber;
      var flightDate = flyItem == null ? undefined : flyItem.flightDate;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlyFlightDetailTimelineTiles, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlyFlightDetailTimelineTiles, `${value}|${flightNumber}|${flightDate}`));
      logClickEvent(sectionKey);
    };
    var handleMap = /*#__PURE__*/function () {
      var _ref6 = (0, _asyncToGenerator2.default)(function* (type) {
        var item = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
        var skytrainItem = arguments.length > 2 ? arguments[2] : undefined;
        var isConnection = yield checkInternetConnection();
        if (!isConnection) {
          refRetryAction.current = {
            type: type,
            item: item
          };
          setShowNoInternetError(true);
          return;
        }
        setShowNoInternetError(false);
        refRetryAction.current = null;
        if (!mapRMFlag) {
          return mapUnavailable == null ? undefined : mapUnavailable.current.show();
        }
        var flightDetailsData = flyFlightDetailsPayload.flightDetailsData;
        setLoadingFlightMap(true);
        var input = {
          category: "",
          terminal: flightDetailsData.displayTerminal,
          direction: (flyItem == null ? undefined : flyItem.direction) === "DEP" ? "Departure" : "Arrival",
          name: "",
          gate: undefined
        };
        var isFocusToArea = false; // For navigating to Terminal in ATOM Map
        switch (type) {
          case TypePressDetailFlightCard.GATE:
            input.category = TypePressDetailFlightCard.GATE;
            input.name = flightDetailsData.displayGate;
            break;
          case TypePressDetailFlightCard.CHECK_IN_ROW:
            input.category = TypePressDetailFlightCard.CHECK_IN_ROW;
            input.name = flightDetailsData.checkInRow;
            break;
          case TypePressDetailFlightCard.BAGGAGE_BELT:
            input.category = TypePressDetailFlightCard.BAGGAGE_BELT;
            input.name = flightDetailsData.baggageBelt;
            break;
          case TypePressDetailFlightCard.TERMINAL:
            input.category = TypePressDetailFlightCard.TERMINAL;
            input.name = "";
            isFocusToArea = true;
            break;
          case TypePressDetailFlightCard.SKYTRAIN:
            input.category = TypePressDetailFlightCard.SKYTRAIN;
            input.name = `T${skytrainItem.terminal} ${skytrainItem == null ? undefined : skytrainItem.type}`;
            input.gate = flightDetailsData.displayGate;
            break;
          case TypeGetIntoAirport.LINK1:
            input.category = (flyItem == null ? undefined : flyItem.direction) === "DEP" ? "Drop-off" : "Pick-up";
            input.name = item;
            break;
          case TypeGetIntoAirport.LINK3:
            input.category = (flyItem == null ? undefined : flyItem.direction) === "DEP" ? "Accessible Drop-off" : "Accessibility Pick-up";
            input.name = item;
            break;
          case TypeGetIntoAirport.LINK2:
            input.category = "Nearest Car Park";
            input.name = flightDetailsData.nearestCarpark;
            break;
        }
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppNavigationMapsEnter, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppNavigationMapsEnter, `${_adobe.AdobeTagName.CAppFlyFlightDetail}|Fly`));
        if (type !== TypePressDetailFlightCard.TERMINAL) {
          logClickEvent(skytrainItem ? _firebase.ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME.GETTING_AROUND_CHANGI_AIRPORT : _firebase.ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME.FLIGHT_INFO_DETAILS);
        }
        var fetchData = yield (0, _pageConfigSaga.getLocationMapping)({
          input: input
        });
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppATOMSEntryClick, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppATOMSEntryClick, `${input.direction} Detail Page|${input.name}|${fetchData.local_ref}`));
        navigation.navigate(_constants.NavigationConstants.changiMap, {
          localRef: fetchData.local_ref,
          isFocusToArea: isFocusToArea
        });
        setLoadingFlightMap(false);
      });
      return function handleMap(_x5) {
        return _ref6.apply(this, arguments);
      };
    }();
    var handleOnLayout = function handleOnLayout(event) {
      var height = event.nativeEvent.layout.height;
      setHeightTickerBand(height);
    };
    var renderTickerBand = function renderTickerBand() {
      return (0, _jsxRuntime.jsx)(_tickerBand.default, {
        urgent: false,
        title: flyShowTickerBand && tickerBand,
        description: flyShowTickerBand && tickerBandDescription,
        buttonText: flyShowTickerBand && tickerBandButtonText,
        onCTAPress: onPressCTA,
        onClose: function onClose() {
          return onCloseTickerBand();
        },
        isLanding: true,
        onLayout: handleOnLayout
      });
    };
    var trackAAWhenBack = function trackAAWhenBack() {
      var flightDirection = flyItem == null ? undefined : flyItem.direction;
      var flightNumber = flyItem == null ? undefined : flyItem.flightNumber;
      var flightDate = flyItem == null ? undefined : flyItem.flightNumber;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlyFlightDetailTopNavigationMenu, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlyFlightDetailTopNavigationMenu, `${flightDirection}|${flightNumber}|${flightDate}`));
    };
    var onClosedTravelOptionSheet = function onClosedTravelOptionSheet() {
      if (!loadingSaveFlight) {
        closeModalTravelOption();
      }
    };
    var savedFlightTravelOptionsOnModalHide = function savedFlightTravelOptionsOnModalHide() {
      setSelectedTravelOption(initSelectedTravelOption);
    };
    var handleConnectingFlightOnPress = function handleConnectingFlightOnPress() {
      var _insertFlightPayload$3, _insertFlightPayload$4;
      var connectingFlightPayloadData = {
        isConnecting: true,
        flightConnecting: Object.assign({}, insertFlightPayload == null || (_insertFlightPayload$3 = insertFlightPayload.flightData) == null ? undefined : _insertFlightPayload$3.item, {
          isPassenger: insertFlightPayload == null || (_insertFlightPayload$4 = insertFlightPayload.flightData) == null ? undefined : _insertFlightPayload$4.isPassenger
        })
      };
      dispatch(_flyRedux.FlyCreators.setConnectingFlightPayload(connectingFlightPayloadData));
      setIsGoToListingFlight(true);
      onCloseConfirmPopUpSavedFlight();
    };
    var backOnPressed = function backOnPressed() {
      trackAAWhenBack();
      navigation.goBack();
    };
    var sourceSystem = _constants.SOURCE_SYSTEM.FLIGHTS;
    var onOpenSaveAndShareModal = function onOpenSaveAndShareModal() {
      openModalSaveAndShare();
      setStatusSaveAndShare("START FOLLOW");
    };
    var checkSaveAndShare = function checkSaveAndShare() {
      var _flyFlightDetailsPayl32;
      if (enableFlySavePrompt && checkFlightCanSave((flyFlightDetailsPayload == null || (_flyFlightDetailsPayl32 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl32.flightStatus) || (flyItem == null ? undefined : flyItem.flightStatus), direction) && !tickerbandMaintananceHook.isShowMaintenance) {
        onOpenSaveAndShareModal();
      } else {
        onSharePress();
      }
    };
    var handleSharePress = function handleSharePress() {
      logClickEvent(_firebase.ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME.SHARE_FLIGHT_BUTTON);
      if (isLoggedIn) {
        !isSaved ? checkSaveAndShare() : onSharePress();
      } else {
        clearInterval(intervalRefreshFlight);
        navigation.navigate(_constants.NavigationConstants.authScreen, {
          callBackAfterLoginSuccess: checkSaveAndShare,
          callBackAfterLoginCancel: function callBackAfterLoginCancel() {
            return null;
          }
        });
      }
    };
    var onSharePress = function onSharePress() {
      onOpenFlightShareSheet();
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlyFlightDetailTopNavigationMenu, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlyFlightDetailTopNavigationMenu, `${direction} | ${flyItem == null ? undefined : flyItem.flightNumber} | Share Button`));
    };
    var onOpenFlightShareSheet = /*#__PURE__*/function () {
      var _ref7 = (0, _asyncToGenerator2.default)(function* (isSavedSuccess) {
        if (loadingFlightMap || loadingShareRef.current) {
          return;
        }
        loadingShareRef.current = true;
        setLoadingFlightMap(true);
        if (!isSavedSuccess) {
          var isConnected = yield checkInternetConnection();
          setNoInterConnection(!isConnected);
          if (!isConnected) {
            setLoadingFlightMap(false);
            loadingShareRef.current = false;
            return;
          }
        }
        var _flyFlightDetailsPayl33 = flyFlightDetailsPayload.flightDetailsData,
          flightNumber = _flyFlightDetailsPayl33.flightNumber,
          departingCode = _flyFlightDetailsPayl33.departingCode,
          destinationCode = _flyFlightDetailsPayl33.destinationCode,
          scheduledDate = _flyFlightDetailsPayl33.scheduledDate,
          scheduledTime = _flyFlightDetailsPayl33.scheduledTime;
        var isDeparture = direction === _flightProps.FlightDirection.departure;
        var options = {
          title: "I would like to share a flight's information with you...",
          message: "I would like to share a flight's information with you...",
          failOnCancel: true
        };
        var shareLinkResponse = yield getDeeplinkShare({
          mode: "flight_details",
          flightNo: flightNumber,
          direction: direction,
          scheduledDate: scheduledDate,
          scheduledTime: scheduledTime,
          promoCode: "",
          isPassenger: isPassenger
        });
        setLoadingFlightMap(false);
        loadingShareRef.current = false;
        if (!shareLinkResponse.success) {
          return;
        }
        if (isDeparture) {
          options = {
            title: "I would like to share a flight's information with you...",
            message: (0, _i18n.translate)("flightDetails.share.shareMessageNonEligible", {
              flightNumber: flightNumber,
              departingCode: "SIN",
              destinationCode: destinationCode,
              scheduledDate: `${(0, _momentTimezone.default)(`${scheduledDate}`, "YYYY-MM-DD", "Asia/Singapore").format("MMM DD, YYYY")}, at ${(0, _momentTimezone.default)(scheduledTime, 'HH:mm').format("HH:mm")}`,
              actualTimestamp: ` (Updated ${(0, _momentTimezone.default)().tz("Asia/Singapore").format("HH:mm, DD MMM YYYY")})`,
              flightDetailFDL: shareLinkResponse.data
            })
          };
        } else {
          options = {
            title: "I would like to share a flight's information with you...",
            message: (0, _i18n.translate)("flightDetails.share.shareMessageNonEligible", {
              flightNumber: flightNumber,
              departingCode: departingCode,
              destinationCode: destinationCode,
              scheduledDate: `${(0, _momentTimezone.default)(`${scheduledDate}`, "YYYY-MM-DD", "Asia/Singapore").format("MMM DD, YYYY")}, at ${(0, _momentTimezone.default)(scheduledTime, 'HH:mm').format("HH:mm")}`,
              actualTimestamp: ` (Updated ${(0, _momentTimezone.default)().tz("Asia/Singapore").format("HH:mm, DD MMM YYYY")})`,
              flightDetailFDL: shareLinkResponse.data
            })
          };
        }
        try {
          var shareResponse = yield _reactNativeShare.default.open(options);
          isSharing.current = false;
          loadingShareRef.current = false;
          if (shareResponse.success) {
            statusSaveAndShare && finishThreadSaveAndShareModal();
          }
        } catch (error) {
          statusSaveAndShare && finishThreadSaveAndShareModal();
        }
      });
      return function onOpenFlightShareSheet(_x6) {
        return _ref7.apply(this, arguments);
      };
    }();
    var onCloseModalSaveAndShare = function onCloseModalSaveAndShare() {
      closeModalSaveAndShare();
      setStatusSaveAndShare(null);
    };
    var onShareOnlyPress = function onShareOnlyPress() {
      closeModalSaveAndShare();
      setStatusSaveAndShare("STARTING SHARE");
      onSharePress();
    };
    var finishThreadSaveAndShareModal = function finishThreadSaveAndShareModal() {
      if (statusSaveAndShare === "SAVE SUCCESS") {
        var _toastForSavedFlight$3;
        toastForSavedFlight == null || (_toastForSavedFlight$3 = toastForSavedFlight.current) == null || _toastForSavedFlight$3.show(_feedbackToastProps.DURATION.LENGTH_LONG);
      }
      setStatusSaveAndShare(null);
    };
    var closeBottomSheetError = function closeBottomSheetError() {
      dispatch(_flyRedux.FlyCreators.flyCheckInOnlineLoadFailed(false));
    };
    var onButtonPressBottomSheetError = function onButtonPressBottomSheetError() {
      if (isLoggedIn) {
        var _flyFlightDetailsPayl34;
        (0, _flightDetailsCard.handleNavigationFlightDetail)(flyFlightDetailsPayload == null || (_flyFlightDetailsPayl34 = flyFlightDetailsPayload.flightDetailsData) == null || (_flyFlightDetailsPayl34 = _flyFlightDetailsPayl34.onlineCheckIn) == null ? undefined : _flyFlightDetailsPayl34.link, isButtonSaveHidden, navigation, dispatch);
        return null;
      }
      dispatch(_flyRedux.FlyCreators.flyPendingCheckInOnline(true));
      navigation.navigate(_constants.NavigationConstants.authScreen, {
        sourceSystem: sourceSystem
      });
    };
    var onButtonPressedConfirmSaveFlight = function onButtonPressedConfirmSaveFlight() {
      var _flyFlightDetailsPayl35, _flyFlightDetailsPayl36, _flyFlightDetailsPayl37;
      var data = {
        enterpriseUserId: profilePayload == null ? undefined : profilePayload.email,
        countryOfResidence: profilePayload == null ? undefined : profilePayload.residentialCountry,
        flightNumber: flyItem == null ? undefined : flyItem.flightNumber,
        flightScheduledDate: flyItem == null ? undefined : flyItem.scheduledDate,
        flightDirection: direction,
        flightPax: selectedTravelOption === _saveFlightTravelOption.TravelOption.iAmTravelling
      };
      var savePayload = Object.assign({}, payload);
      savePayload.item.flightDate = flyFlightDetailsPayload == null || (_flyFlightDetailsPayl35 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl35.scheduledDate;
      savePayload.item.scheduledDate = flyFlightDetailsPayload == null || (_flyFlightDetailsPayl36 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl36.scheduledDate;
      var dtAction = (0, _firebase.dtManualActionEvent)(`${_firebase.FE_LOG_PREFIX}App__flight-detail-save-confirm`);
      dtAction.reportStringValue("flight-detail-save-confirm-press-flightNumber", `${flyItem == null ? undefined : flyItem.flightNumber}`);
      dtAction.reportStringValue("flight-detail-save-confirm-press-scheduledDate", `${(flyFlightDetailsPayload == null || (_flyFlightDetailsPayl37 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl37.scheduledDate) || (flyItem == null ? undefined : flyItem.scheduledDate)}`);
      dtAction.reportStringValue("flight-detail-save-confirm-press-direction", `${direction}`);
      dtAction.reportStringValue("flight-detail-save-confirm-press-selectedTravelOption", `${selectedTravelOption}`);
      dispatch(_mytravelRedux.MytravelCreators.flyMyTravelInsertFlightRequest(data, savePayload));
      dispatch(_flyRedux.FlyCreators.flyPendingSaveFlight(true));
      dispatch(_flyRedux.FlyCreators.flyShowModalConfirmSaveFly(false));
      dtAction.leaveAction();
    };
    var onSelectCard = function onSelectCard() {
      logClickEvent(_firebase.ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME.USER_PROFILE_SELECTOR);
      setShowModalTravelOption(true);
    };
    var renderBottomSheetError = function renderBottomSheetError() {
      return (0, _jsxRuntime.jsx)(_bottomSheetError.BottomSheetError, {
        iconUrl: msg65 == null ? undefined : msg65.icon,
        icon: (0, _jsxRuntime.jsx)(_icons.ErrorOutlined, {
          width: "70",
          height: "70"
        }),
        visible: isCheckInOnlineLoadFailed,
        title: getValue(msg65 == null ? undefined : msg65.title, (0, _i18n.translate)("popupError.somethingWrong")),
        errorMessage: getValue(msg65 == null ? undefined : msg65.message, (0, _i18n.translate)("popupError.networkErrorMessage")),
        onClose: closeBottomSheetError,
        buttonText: getValue(msg65 == null ? undefined : msg65.firstButton, (0, _i18n.translate)("popupError.retry")),
        onButtonPressed: onButtonPressBottomSheetError,
        testID: `${SCREEN_NAME}__BottomSheetError`,
        accessibilityLabel: `${SCREEN_NAME}__BottomSheetError`
      });
    };
    var renderFlightDetails = function renderFlightDetails() {
      var _flyFlightDetailsPayl38, _flyFlightDetailsPayl39;
      return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsx)(_flightDetailsCardV.FlightDetailsCardV2, Object.assign({
          timeStamp: (0, _i18n.translate)("flightLanding.updatedSystemTime") + " " + flyLastUpdatedTimeStamp
        }, flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData, {
          state: (0, _utils.handleCondition)(direction === _flightProps.FlightDirection.departure, _flightDetailsCard2.FlightDetailsCardState.departure, _flightDetailsCard2.FlightDetailsCardState.arrival),
          type: flyFlightDetailsPayload == null || (_flyFlightDetailsPayl38 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl38.type,
          navigation: navigation,
          isFlightSaved: isButtonSaveHidden && isLoggedIn,
          removeFlightLoading: removeFlightPayload == null ? undefined : removeFlightPayload.loading,
          onButtonPressed: function onButtonPressed() {
            return onSaveFlight(true, selectedTravelOption === _saveFlightTravelOption.TravelOption.iAmTravelling);
          },
          showGate: flyFlightDetailsPayload == null || (_flyFlightDetailsPayl39 = flyFlightDetailsPayload.flightDetailsData) == null || (_flyFlightDetailsPayl39 = _flyFlightDetailsPayl39.statusMapping) == null ? undefined : _flyFlightDetailsPayl39.show_gate,
          isMSError: flyItem == null ? undefined : flyItem.isMSError,
          selectedTravelOption: selectedTravelOption,
          selectedTopTravelOption: selectedTopTravelOption,
          route: route,
          flyShowTickerBand: isShowTickerband,
          onPressFlightCardLinks: onPressFlightCardLinks,
          onPressCheckInRow: function onPressCheckInRow() {
            return handleMap(TypePressDetailFlightCard.CHECK_IN_ROW);
          },
          onPressGate: function onPressGate() {
            return handleMap(TypePressDetailFlightCard.GATE);
          },
          onPressBaggageBelt: function onPressBaggageBelt() {
            return handleMap(TypePressDetailFlightCard.BAGGAGE_BELT);
          },
          onPressNearestCarPark: function onPressNearestCarPark() {
            return handleMap(TypeGetIntoAirport.LINK2);
          },
          onPressPickupOrDropOff: function onPressPickupOrDropOff() {
            var _flyFlightDetailsPayl40, _getIntoCityOrAirport;
            return handleMap(TypeGetIntoAirport.LINK1, (flyFlightDetailsPayload == null || (_flyFlightDetailsPayl40 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl40.dropOffDoor) || (getIntoCityOrAirportPayload == null || (_getIntoCityOrAirport = getIntoCityOrAirportPayload.getIntoCityOrAirport) == null ? undefined : _getIntoCityOrAirport.text1));
          },
          onPressAccessiblePickupOrDropOff: function onPressAccessiblePickupOrDropOff() {
            var _getIntoCityOrAirport2;
            return handleMap(TypeGetIntoAirport.LINK3, getIntoCityOrAirportPayload == null || (_getIntoCityOrAirport2 = getIntoCityOrAirportPayload.getIntoCityOrAirport) == null ? undefined : _getIntoCityOrAirport2.text3);
          },
          testID: `${SCREEN_NAME}FlightDetailsCard`,
          accessibilityLabel: `${SCREEN_NAME}FlightDetailsCard`,
          profilePayload: profilePayload,
          direction: direction,
          disclaimerText: flightDetailsCardDisclaimerText,
          enableEciDynamicDisplay: enableEciDynamicDisplay,
          saveFlightWhenCheckInOnline: handleSaveFlightWhenCheckInOnline,
          isFlightDetailP1: isFlightDetailP1
        })), isFlightDetailP1 && (0, _jsxRuntime.jsx)(_informationHubCard.InformationHubCard, {
          direction: direction,
          handleMap: handleMap,
          travelChecklistAEM: travelChecklistAEM,
          flightDetailSectionData: flightDetailSectionData,
          flyFlightDetailsPayload: flyFlightDetailsPayload,
          flyFlightDetailsError: flyFlightDetailsError,
          selectedTopTravelOption: selectedTopTravelOption,
          isFlightSaved: isButtonSaveHidden && isLoggedIn,
          enableEciDynamicDisplay: enableEciDynamicDisplay,
          onPressFlightCardLinks: onPressFlightCardLinks,
          customerEligibility: (0, _utils.handleCondition)(direction === _flightProps.FlightDirection.departure, _flightProps.CustomerEligibility.FlyingDep, _flightProps.CustomerEligibility.FlyingArr),
          onPressReloadTravelAEM: handleGetTravelChecklistAEM,
          isTravelChecklistAEMLoading: isTravelChecklistAEMLoading,
          onPressReloadFlightDetails: refreshFlightDetails,
          saveFlightWhenCheckInOnline: handleSaveFlightWhenCheckInOnline
        }), !isFlightDetailP1 && (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [(0, _jsxRuntime.jsx)(_getIntoAirport.GetIntoAirport, {
            direction: direction,
            flyFlightDetailsPayload: flyFlightDetailsPayload,
            flightUniqueId: (flyItem == null ? undefined : flyItem.flightUniqueId) || `${flyItem == null ? undefined : flyItem.flightNumber}_${flyItem == null ? undefined : flyItem.flightDate}`,
            onPressed: function onPressed(type, item) {
              return handleMap(type, item);
            }
          }), (0, _jsxRuntime.jsx)(_appscapadeFlightDetail.default, {
            isTravelling: selectedTravelOption === _saveFlightTravelOption.TravelOption.iAmTravelling
          }), (0, _jsxRuntime.jsx)(_flightDetailsCrt.FlightDetailsCrt, {
            flightDirection: direction,
            navigation: navigation
          }), (0, _jsxRuntime.jsx)(_flightPerks.default, {
            testID: `${SCREEN_NAME}__FlightPerks`,
            accessibilityLabel: `${SCREEN_NAME}__FlightPerks`,
            flightDetail: flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData,
            flightDirection: direction,
            isSavedFlight: isButtonSaveHidden && isLoggedIn
          }), (0, _jsxRuntime.jsx)(_timelineSection.TimelineSection, {
            flyItem: flyItem,
            direction: direction,
            flyFlightDetailsPayload: flyFlightDetailsPayload,
            onTrackingTimeline: onTrackingTimeline,
            testID: `${SCREEN_NAME}__TimeLineSection`,
            accessibilityLabel: `${SCREEN_NAME}__TimeLineSection`
          }), (0, _jsxRuntime.jsx)(_facilitiesServices.default, {
            testID: `${SCREEN_NAME}__FacilitiesAndServices`,
            accessibilityLabel: `${SCREEN_NAME}__FacilitiesAndServices`,
            customerEligibility: (0, _utils.handleCondition)(direction === _flightProps.FlightDirection.departure, _flightProps.CustomerEligibility.FlyingDep, _flightProps.CustomerEligibility.FlyingArr),
            onPressItem: function onPressItem(item) {
              logClickEvent(_firebase.ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME.FACILITIES_SERVICES);
              (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlyFlightDetailFacilitiesServices, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlyFlightDetailFacilitiesServices, (item == null ? undefined : item.title) || ""));
            },
            onPressAll: function onPressAll() {
              logClickEvent(_firebase.ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME.FACILITIES_SERVICES_VIEW_ALL);
              (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlyFlightDetailFacilitiesServicesViewAll, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlyFlightDetailFacilitiesServicesViewAll, "1"));
            }
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: sectionSeparator
        })]
      });
    };
    if (isNoInternetConnection) {
      return (0, _jsxRuntime.jsx)(_errorOverlayApp.ErrorOverlayApp, {
        reload: true,
        onReload: onOpenFlightShareSheet
      });
    }
    return (0, _jsxRuntime.jsxs)(_react.Fragment, {
      children: [isShowTickerband && renderTickerBand(), showNoInternetError && (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        hideScreenHeader: false,
        headerBackgroundColor: "transparent",
        visible: showNoInternetError,
        onBack: function onBack() {
          navigation.goBack();
        },
        testID: `${SCREEN_NAME}__ErrorOverlayNoConnection`,
        onReload: refreshFlightDetails
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: [wrapHeader, getPositionBackButton, {
          left: 0
        }],
        children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: {
            zIndex: 10
          },
          onPress: backOnPressed,
          testID: `${SCREEN_NAME}__BackButton`,
          accessibilityLabel: `${SCREEN_NAME}__BackButton`,
          children: (0, _jsxRuntime.jsx)(_icons.BackButton, {})
        })
      }), shouldShowShareButton && (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: [wrapHeader, getPositionBackButton, {
          right: 0
        }],
        children: (0, _jsxRuntime.jsx)(_singleTapButton.SingleTapButton, {
          disabled: loadingFlightMap,
          onPress: handleSharePress,
          testID: `${SCREEN_NAME}__ShareButton`,
          accessibilityLabel: `${SCREEN_NAME}__ShareButton`,
          children: (0, _jsxRuntime.jsx)(_icons.Share, {})
        })
      }), (0, _jsxRuntime.jsxs)(_reactNative2.ScrollView, {
        style: _flightDetailsStyles.styles.scrollViewContainerStyle,
        showsVerticalScrollIndicator: false,
        ref: scrollRef,
        onScroll: handleScroll,
        refreshControl: (0, _jsxRuntime.jsx)(_reactNative.RefreshControl, {
          refreshing: false,
          onRefresh: refreshFlightDetails
        }),
        scrollEnabled: true,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: flightHeroImageStyle,
          children: [(0, _jsxRuntime.jsx)(_flightHeroImage.FlightHeroImage, {
            imageUrl: flyFlightDetailsPayload == null || (_flyFlightDetailsPayl41 = flyFlightDetailsPayload.heroImageData) == null ? undefined : _flyFlightDetailsPayl41.imageUrl,
            state: direction,
            travelInfo: flyFlightDetailsPayload == null || (_flyFlightDetailsPayl42 = flyFlightDetailsPayload.heroImageData) == null ? undefined : _flyFlightDetailsPayl42.travelInfo,
            type: flyFlightDetailsPayload == null || (_flyFlightDetailsPayl43 = flyFlightDetailsPayload.heroImageData) == null ? undefined : _flyFlightDetailsPayl43.type,
            flightNumber: flyItem == null ? undefined : flyItem.flightNumber,
            airportCode: (0, _utils.handleCondition)((flyItem == null ? undefined : flyItem.direction) === "DEP", flyFlightDetailsPayload == null || (_flyFlightDetailsPayl44 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl44.destinationCode, "SIN"),
            wrapHeaderStyles: wrapHeaderStyles
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: [_flightDetailsStyles.styles.dropDownSelectCardContainerStyle, dropDownSelectCardContainerStyle, {
              left: isFlightDetailP1 ? 16 : 24,
              right: isFlightDetailP1 ? 16 : 24
            }],
            children: [shouldShowSQArrivalTerminalInfo && (0, _jsxRuntime.jsx)(_text.Text, {
              text: inf22 == null ? undefined : inf22.informativeText,
              style: _flightDetailsStyles.styles.informativeTextStyle
            }), (0, _jsxRuntime.jsx)(_dropdownSelectCard.default, {
              style: [_flightDetailsStyles.styles.dropDownSelectCardStyle, {
                marginTop: 20
              }],
              onPressed: onSelectCard,
              title: getTitleDropdownTravelOption,
              type: flyFlightDetailsPayload == null || (_flyFlightDetailsPayl45 = flyFlightDetailsPayload.heroImageData) == null ? undefined : _flyFlightDetailsPayl45.type
            })]
          })]
        }), isShowMaintenance ? (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _flightDetailsStyles.styles.maintenanceErrorContainer,
          children: (0, _jsxRuntime.jsx)(_errorCloud.ErrorCloudComponent, {
            skipStatusbar: true,
            style: {
              backgroundColor: "transparent"
            },
            titleStyle: {
              marginTop: 16
            },
            buttonStyle: {
              width: "auto"
            },
            errorData: errorData,
            onPress: fetchTickerbandMaintanance
          })
        }) : renderFlightDetails()]
      }), showErrorFeedBackToastMessage(), (0, _utils.handleCondition)(isButtonSaveHidden || flyFlightDetailsFetching && !(insertFlightPayload != null && insertFlightPayload.loading), null, (0, _jsxRuntime.jsx)(_saveFlightButton.default, {
        loading: insertFlightPayload == null ? undefined : insertFlightPayload.loading,
        onPress: function onPress() {
          return onSaveFlight(false, selectedTravelOption === _saveFlightTravelOption.TravelOption.iAmTravelling);
        },
        disabled: isShowMaintenance,
        isFlightCanSave: isShowMaintenance ? false : checkFlightCanSave((flyFlightDetailsPayload == null || (_flyFlightDetailsPayl46 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl46.flightStatus) || (flyItem == null ? undefined : flyItem.flightStatus), direction)
      })), fabVisibility && (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: [_flightDetailsStyles.styles.fabContainerViewStyle, fabContainerBottom],
        onPress: function onPress() {
          return onPressTouch();
        },
        testID: `${SCREEN_NAME}__Button_GotoTop`,
        accessibilityLabel: `${SCREEN_NAME}__Button_GotoTop`,
        children: [(0, _jsxRuntime.jsx)(_icons.ArrowUp, {
          style: _flightDetailsStyles.styles.fabArrowStyle,
          height: 24,
          width: 24
        }), (0, _jsxRuntime.jsx)(_backgrounds.FabBackground, {})]
      }), (0, _jsxRuntime.jsx)(_travelOptions.default, {
        visible: showModalTravelOption,
        onClosed: onClosedSheet,
        onBackPressed: onClosedSheet,
        selectedOption: selectedTopTravelOption,
        onPress: function onPress(option) {
          return travelOptionTapped(option);
        },
        flightDirection: direction
      }), showToastForRemoveFlight(), (0, _jsxRuntime.jsx)(_saveFlightTravelOption.default, {
        onModalHide: savedFlightTravelOptionsOnModalHide,
        visible: isModalVisibleTravelOption,
        onClosed: onClosedTravelOptionSheet,
        loadingSaveFlight: loadingSaveFlight,
        onBackPressed: onClosedTravelOptionSheet,
        selectedOption: selectedTravelOption,
        savedFlightOnPress: function savedFlightOnPress(e) {
          isSharing.current = false;
          _savedFlightOnPress(e);
        },
        onPress: function onPress(option) {
          return travelOptionTapped(option);
        },
        flightDirection: direction
      }), (0, _jsxRuntime.jsx)(_modalSaveAndShare.default, {
        onModalHide: savedFlightTravelOptionsOnModalHide,
        visible: isModalSaveAndShare,
        onClosed: onCloseModalSaveAndShare,
        loadingSaveFlight: loadingSaveFlight,
        onShareOnlyPress: onShareOnlyPress,
        selectedOption: selectedTravelOption,
        savedFlightOnPress: function savedFlightOnPress(e) {
          isSharing.current = false;
          _savedFlightOnPress(e);
        },
        onPress: function onPress(option) {
          return travelOptionTapped(option);
        },
        flightDirection: direction
      }), (0, _jsxRuntime.jsx)(_confirmPopupSaveFlight.default, {
        imageUrl: (0, _mediaHelper.handleImageUrl)(msg47 == null ? undefined : msg47.icon),
        visible: isModalVisible && isFocused,
        title: (0, _i18n.translate)("flightDetails.newPopupConfirmSaveFlight.title"),
        messageText: (0, _utils.simpleCondition)({
          condition: direction === _flightProps.FlightDirection.arrival,
          ifValue: (0, _i18n.translate)("flightDetails.newPopupConfirmSaveFlight.arrivalMessage"),
          elseValue: (0, _i18n.translate)("flightDetails.newPopupConfirmSaveFlight.departureMessage")
        }),
        onClose: function onClose() {
          onCloseConfirmPopUpSavedFlight();
        },
        onButtonPressed: function onButtonPressed() {
          setIsGoToListingFlight(true);
          onCloseConfirmPopUpSavedFlight();
        },
        onModalHide: function onModalHide() {
          dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
          var timeStamp = new Date().getTime();
          (0, _mmkvStorage.setLastSavedFlightTime)(timeStamp);
          if (isGoToListingFlight) {
            setShowCalendarModal(true);
            setIsGoToListingFlight(false);
          } else {
            var _toastForSavedFlight$4;
            toastForSavedFlight == null || (_toastForSavedFlight$4 = toastForSavedFlight.current) == null || _toastForSavedFlight$4.show(_feedbackToastProps.DURATION.LENGTH_LONG);
          }
        },
        textButtonConfirm: (0, _i18n.translate)("flightDetails.newPopupConfirmSaveFlight.addReturnFlightButton"),
        textButtonCancel: (0, _i18n.translate)("flightDetails.newPopupConfirmSaveFlight.cancelButton"),
        isShowButtonConnection: direction === _flightProps.FlightDirection.arrival,
        onButtonConnectionPressed: handleConnectingFlightOnPress,
        textButtonConnection: (0, _i18n.translate)("flightDetails.newPopupConfirmSaveFlight.addConnectingFlightButton"),
        disableCloseButton: true,
        openPendingModal: true
      }), (0, _jsxRuntime.jsx)(_addReturnCalendar.default, {
        isVisible: showCalendarModal,
        filterDate: filterDateAddReturnCalendar,
        initialMinDate: filterDateAddReturnCalendar,
        onClosedCalendarModal: function onClosedCalendarModal() {
          var _toastForSavedFlight$5;
          toastForSavedFlight == null || (_toastForSavedFlight$5 = toastForSavedFlight.current) == null || _toastForSavedFlight$5.show(_feedbackToastProps.DURATION.LENGTH_LONG);
          setShowCalendarModal(false);
          var connectingFlightPayloadToClear = {
            isConnecting: false,
            flightConnecting: null
          };
          dispatch(_flyRedux.FlyCreators.setConnectingFlightPayload(connectingFlightPayloadToClear));
        },
        onDateSelected: function onDateSelected(dateString) {
          var _flyFlightDetailsPayl47;
          var country = connectingFlightPayload.isConnecting ? "Singapore" : (flyFlightDetailsPayload == null || (_flyFlightDetailsPayl47 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl47.country) || "";
          var date = (0, _momentTimezone.default)(dateString).format("YYYY-MM-DD");
          setShowCalendarModal(false);
          dispatch(_flyRedux.FlyCreators.setFlightSearchDate(date));
          navigation.navigate("flightResultLandingScreen", {
            screen: "Tabs",
            selectedDate: date,
            country: country,
            params: {
              screen: direction === _flightProps.FlightDirection.departure ? "arrivalResultScreen" : "departureResultScreen",
              selectedDate: date,
              country: country
            }
          });
        },
        testID: `${SCREEN_NAME}__AddReturnCalendar`,
        accessibilityLabel: `${SCREEN_NAME}__AddReturnCalendar`
      }), showFlightAddedFeedBackToastMessage(), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
        visible: loadingFlightMap || flyFlightDetailsFetchingFirst
      }), (0, _jsxRuntime.jsx)(_alertApp.AlertApp, {
        ref: alertApp
      }), (0, _jsxRuntime.jsx)(_confirmPopupSaveFlight.default, {
        imageUrl: iconUrl,
        visible: isShowModalConfirmSaveFly,
        messageText: (0, _i18n.translate)("flightDetails.popupConfirmSaveFlight.message"),
        onClose: function onClose() {
          dispatch(_flyRedux.FlyCreators.flyShowModalConfirmSaveFly(false));
        },
        onButtonPressed: onButtonPressedConfirmSaveFlight
      }), (0, _jsxRuntime.jsx)(_confirmPopupSaveFlight.default, {
        imageUrl: iconUrl,
        visible: showSaveFlightWhenOnlineCheckIn,
        messageText: (0, _i18n.translate)("flightDetails.popupConfirmSaveFlight.messageOnlineCheckIn"),
        onClose: function onClose() {
          return setShowSaveFlightWhenOnlineCheckIn(false);
        },
        onButtonPressed: onButtonPressedConfirmSaveFlight,
        textButtonCancel: (0, _i18n.translate)("flightDetails.popupConfirmSaveFlight.onlineCheckInOnly"),
        onSecondaryBtnPressed: handleCheckInOnline
      }), renderBottomSheetError(), (0, _jsxRuntime.jsx)(_bottomSheetUnableLoadLocation.BottomSheetUnableLoadLocation, {
        ref: unableToLoadLocationRef
      }), (0, _jsxRuntime.jsx)(_bottomSheetMapUnavailable.BottomSheetMapUnavailable, {
        ref: mapUnavailable
      })]
    });
  };
