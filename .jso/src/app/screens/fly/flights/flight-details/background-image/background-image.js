  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.BackgroundImage = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _backgroundImage = _$$_REQUIRE(_dependencyMap[4]);
  var _flightProps = _$$_REQUIRE(_dependencyMap[5]);
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _theme = _$$_REQUIRE(_dependencyMap[8]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _screenHelper = _$$_REQUIRE(_dependencyMap[10]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var GradientOverlay = function GradientOverlay() {
    return (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
      colors: _theme.color.palette.overlayFlightDetailV2,
      start: {
        x: 0.5,
        y: 1
      },
      end: {
        x: 0.5,
        y: 0
      },
      style: _reactNative.StyleSheet.absoluteFill
    });
  };
  var BackgroundImage = exports.BackgroundImage = function BackgroundImage(_ref) {
    var direction = _ref.direction,
      getBackgroundAirportAEM = _ref.getBackgroundAirportAEM,
      isSaved = _ref.isSaved,
      airport = _ref.airport;
    var _React$useState = _react.default.useState(""),
      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),
      imageUrl = _React$useState2[0],
      setImageUrl = _React$useState2[1];
    (0, _react.useEffect)(function () {
      if (isSaved && direction === _flightProps.FlightDirection.departure) {
        getBackgroundAirportAEM({
          airport: airport
        }).then(function (url) {
          setImageUrl(url);
        });
      }
    }, [isSaved, airport, direction]);
    var renderImage = function renderImage() {
      if (isSaved) {
        if (direction === _flightProps.FlightDirection.arrival) {
          return (0, _jsxRuntime.jsx)(_baseImage.default, {
            source: _icons.BgSavedArr,
            style: _backgroundImage.styles.bgImage
          });
        }
        // Departure
        if (imageUrl) {
          return (0, _jsxRuntime.jsx)(_baseImage.default, {
            source: {
              uri: (0, _screenHelper.getUriImage)(imageUrl)
            },
            style: _backgroundImage.styles.bgImage,
            cache: true
          });
        }
        return (0, _jsxRuntime.jsx)(_baseImage.default, {
          source: _icons.BgNotSavedDep,
          style: _backgroundImage.styles.bgImage
        });
      } else {
        if (direction === _flightProps.FlightDirection.arrival) {
          return (0, _jsxRuntime.jsx)(_baseImage.default, {
            source: _icons.BgNotSavedArr,
            style: _backgroundImage.styles.bgImage
          });
        }
        return (0, _jsxRuntime.jsx)(_baseImage.default, {
          source: _icons.BgNotSavedDep,
          style: _backgroundImage.styles.bgImage
        });
      }
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: _backgroundImage.styles.bgContainer,
      children: [renderImage(), (0, _jsxRuntime.jsx)(GradientOverlay, {})]
    });
  };
