  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _Dimensions$get = _reactNative.Dimensions.get("screen"),
    width = _Dimensions$get.width,
    height = _Dimensions$get.height;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    bgContainer: {
      width: width,
      height: height,
      flex: 1,
      position: 'absolute',
      backgroundColor: _theme.color.palette.whiteGrey
    },
    bgImage: {
      width: width,
      height: height
    }
  });
