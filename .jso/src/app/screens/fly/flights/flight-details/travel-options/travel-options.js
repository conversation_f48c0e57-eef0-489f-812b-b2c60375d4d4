  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.outerRadioRingStyle = exports.innerRadioCircleStyle = exports.default = exports.TravelOption = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _icons = _$$_REQUIRE(_dependencyMap[7]);
  var _radio = _$$_REQUIRE(_dependencyMap[8]);
  var _divider = _$$_REQUIRE(_dependencyMap[9]);
  var _flightProps = _$$_REQUIRE(_dependencyMap[10]);
  var _fly = _$$_REQUIRE(_dependencyMap[11]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[12]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _Dimensions$get = _reactNative2.Dimensions.get("screen"),
    height = _Dimensions$get.height;
  var TEMPLATE_HEIGHT = 812; // BASED ON IPHONE 11 PRO
  var HEIGHT_BOTTOM_SHEET = height * 292 / TEMPLATE_HEIGHT;
  var styles = _reactNative2.StyleSheet.create({
    bottomSheetStyle: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      height: HEIGHT_BOTTOM_SHEET
    },
    descriptionHeader: {
      marginBottom: 34,
      marginTop: 30,
      textAlign: "center"
    },
    headerButton: {},
    leftPart: {
      width: "10%"
    },
    parentContainer: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      flex: 1,
      paddingHorizontal: 20,
      paddingTop: 21
    },
    upper: {
      marginBottom: 18
    },
    rightPart: {
      alignItems: "flex-end",
      width: "10%"
    },
    selectYourProfile: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey
    }),
    under: {
      marginTop: 18
    },
    wrapHeaderBottomSheet: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "space-between"
    }
  });
  var TravelOption = exports.TravelOption = /*#__PURE__*/function (TravelOption) {
    TravelOption["iAmTravelling"] = "dropdownSelectCard.imTravellingOnThisFlight";
    TravelOption["iAmPicking"] = "dropdownSelectCard.imDroppingOffSomeone";
    return TravelOption;
  }({});
  var TravelOptions = function TravelOptions(props) {
    var _useContext;
    var visible = props.visible,
      onClosed = props.onClosed,
      onBackPressed = props.onBackPressed,
      selectedOption = props.selectedOption,
      onPress = props.onPress,
      flightDirection = props.flightDirection;
    var _useContext$Handlers = (_useContext = (0, _react.useContext)(_fly.FLY_CONTEXT)) == null ? undefined : _useContext.Handlers,
      flyDetailsFirstFlag = _useContext$Handlers.flyDetailsFirstFlag;
    var isFlightDetailsFirst = (0, _remoteConfig.isFlagOnCondition)(flyDetailsFirstFlag);
    var buttons = (0, _react.useMemo)(function () {
      if (flightDirection === _flightProps.FlightDirection.departure) {
        return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [(0, _jsxRuntime.jsx)(_radio.Radio, {
            tx: TravelOption.iAmTravelling,
            isChecked: selectedOption === TravelOption.iAmTravelling,
            onChecked: function onChecked() {
              return onPress(TravelOption.iAmTravelling);
            },
            style: styles.upper
          }), (0, _jsxRuntime.jsx)(_divider.Divider, {}), (0, _jsxRuntime.jsx)(_radio.Radio, {
            tx: flightDirection === _flightProps.FlightDirection.departure ? TravelOption.iAmPicking : "dropdownSelectCard.imPickingSomeone",
            isChecked: selectedOption === TravelOption.iAmPicking,
            onChecked: function onChecked() {
              return onPress(TravelOption.iAmPicking);
            },
            style: styles.under
          })]
        });
      } else {
        return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [(0, _jsxRuntime.jsx)(_radio.Radio, {
            tx: flightDirection === _flightProps.FlightDirection.departure ? TravelOption.iAmPicking : "dropdownSelectCard.imPickingSomeone",
            isChecked: selectedOption === TravelOption.iAmPicking,
            onChecked: function onChecked() {
              return onPress(TravelOption.iAmPicking);
            },
            style: styles.upper
          }), (0, _jsxRuntime.jsx)(_divider.Divider, {}), (0, _jsxRuntime.jsx)(_radio.Radio, {
            tx: TravelOption.iAmTravelling,
            isChecked: selectedOption === TravelOption.iAmTravelling,
            onChecked: function onChecked() {
              return onPress(TravelOption.iAmTravelling);
            },
            style: styles.under
          })]
        });
      }
    }, [flightDirection, selectedOption]);
    return (0, _jsxRuntime.jsx)(_bottomSheet.default, {
      isModalVisible: visible,
      onClosedSheet: onClosed,
      containerStyle: styles.bottomSheetStyle,
      stopDragCollapse: true,
      onBackPressHandle: onBackPressed,
      animationInTiming: 500,
      animationOutTiming: 500,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.parentContainer,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.wrapHeaderBottomSheet,
          children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.leftPart
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            tx: "dropdownSelectCard.selectYourProfile",
            preset: "h2",
            style: styles.selectYourProfile
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.rightPart,
            children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              onPress: function onPress() {
                return onClosed();
              },
              style: styles.headerButton,
              children: (0, _jsxRuntime.jsx)(_icons.CrossPurple, {
                height: "24",
                width: "24"
              })
            })
          })]
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          tx: isFlightDetailsFirst ? "dropdownSelectCard.descriptionSelectProfileV2" : "dropdownSelectCard.descriptionSelectProfile",
          preset: "bodyTextBlackRegular",
          style: styles.descriptionHeader
        }), buttons]
      })
    });
  };
  var outerRadioRingStyle = exports.outerRadioRingStyle = {
    height: 24,
    width: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: _theme.color.palette.lightPurple,
    alignItems: "center",
    justifyContent: "center"
  };
  var innerRadioCircleStyle = exports.innerRadioCircleStyle = {
    height: 12,
    width: 12,
    borderRadius: 6,
    backgroundColor: _theme.color.palette.lightPurple
  };
  var _default = exports.default = TravelOptions;
