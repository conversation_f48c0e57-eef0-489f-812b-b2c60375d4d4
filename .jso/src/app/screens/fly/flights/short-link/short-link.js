  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _shortcutLink = _$$_REQUIRE(_dependencyMap[3]);
  var _shortcutLink2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _flyRedux = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[7]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[8]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[9]);
  var _sectionsData = _$$_REQUIRE(_dependencyMap[10]);
  var _isEmpty = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _error = _$$_REQUIRE(_dependencyMap[12]);
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[13]);
  var _native = _$$_REQUIRE(_dependencyMap[14]);
  var _constants = _$$_REQUIRE(_dependencyMap[15]);
  var _airportLandingRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[16]));
  var _adobe = _$$_REQUIRE(_dependencyMap[17]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[18]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[19]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[20]);
  var _mytravelRedux = _$$_REQUIRE(_dependencyMap[21]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[22]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    width = _Dimensions$get.width,
    height = _Dimensions$get.height;
  var ShortCutLinkFlySection = function ShortCutLinkFlySection(props) {
    var dispatch = (0, _reactRedux.useDispatch)();
    var navigation = (0, _native.useNavigation)();
    var _props$testID = props.testID,
      testID = _props$testID === undefined ? "ShortCutLinkFlySection" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "ShortCutLinkFlySection" : _props$accessibilityL,
      needSeparator = props.needSeparator;
    var contentShortLink = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flyContentShortLink);
    var flyContentShortLinkFetching = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flyContentShortLinkFetching);
    var myTravelFlightsPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.myTravelFlightsPayload);
    var getChangiGameUrlPathData = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.getChangiGameUrlPathData);
    var profileData = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var _useState = (0, _react.useState)(null),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      changiGame = _useState2[0],
      getChangiGame = _useState2[1];
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("FLY_SHORTLINK"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var _useState3 = (0, _react.useState)(null),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      contentShortLinkProcessed = _useState4[0],
      setContentShortLinkProcessed = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      mapRMFlag = _useState6[0],
      setMapRMFlag = _useState6[1];
    var shimmerColorsPrimary = [_theme.color.palette.lightestGrey, _theme.color.palette.almostWhiteGrey, _theme.color.palette.lightestGrey];
    (0, _react.useEffect)(function () {
      _reactNative.InteractionManager.runAfterInteractions(function () {
        dispatch(_flyRedux.FlyCreators.flyContentShortLinkRequest());
      });
    }, []);
    (0, _react.useEffect)(function () {
      if (changiGame && profileData) {
        var myTravelFlightDetails = myTravelFlightsPayload == null ? undefined : myTravelFlightsPayload.getMyTravelFlightDetails;
        var flightNo = "";
        if (myTravelFlightDetails && myTravelFlightDetails.length > 0) {
          var _myTravelFlightDetail;
          flightNo = (_myTravelFlightDetail = myTravelFlightDetails[myTravelFlightDetails.length - 1]) == null ? undefined : _myTravelFlightDetail.flightNumber;
        }
        getChangiGame(null);
        dispatch(_airportLandingRedux.default.getChangiGameUrlRequest("", flightNo, changiGame, profileData == null ? undefined : profileData.cardNo));
      }
    }, [changiGame, myTravelFlightsPayload, profileData]);
    (0, _react.useEffect)(function () {
      var _getChangiGameUrlPath;
      if (getChangiGameUrlPathData != null && (_getChangiGameUrlPath = getChangiGameUrlPathData.data) != null && (_getChangiGameUrlPath = _getChangiGameUrlPath.getChangiGames) != null && _getChangiGameUrlPath.url) {
        var _getChangiGameUrlPath2;
        var url = getChangiGameUrlPathData == null || (_getChangiGameUrlPath2 = getChangiGameUrlPathData.data) == null || (_getChangiGameUrlPath2 = _getChangiGameUrlPath2.getChangiGames) == null ? undefined : _getChangiGameUrlPath2.url;
        navigation.navigate(_constants.NavigationConstants.webview, {
          uri: url
        });
      }
    }, [getChangiGameUrlPathData]);
    var shortcutLinksOnPressed = function shortcutLinksOnPressed(shortcutLinkDetail, item) {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlyQuickLinks, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlyQuickLinks, item == null ? undefined : item.title));
      var _ref = shortcutLinkDetail || {},
        type = _ref.type,
        value = _ref.value;
      var _ref2 = item || {},
        redirect = _ref2.redirect;
      if (!type || !value) return;
      handleNavigation(type, value, redirect, {
        redirectFrom: _constants.CM24RedirectSource.QuickLinks
      });
    };
    var shortcutLinkContainerStyleWithSeparator = {
      flexDirection: "row",
      marginBottom: 32,
      paddingStart: 12
    };
    (0, _react.useEffect)(function () {
      var fetchAtomRMConfig = function fetchAtomRMConfig() {
        var mapFlagEnable = (0, _remoteConfig.isFlagON)(_remoteConfig.REMOTE_CONFIG_FLAGS.ATOMS_MAP);
        setMapRMFlag(mapFlagEnable);
      };
      fetchAtomRMConfig();
    }, []);
    (0, _react.useEffect)(function () {
      var links = contentShortLink;
      if (!(0, _isEmpty.default)(links)) {
        if (!mapRMFlag) {
          links = links.filter(function (link) {
            return link.navigation.value !== "atomMap";
          });
        }
        setContentShortLinkProcessed(links);
      }
    }, [contentShortLink, mapRMFlag]);
    if (flyContentShortLinkFetching) {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: needSeparator ? shortcutLinkContainerStyleWithSeparator : styles.shortcutLinkContainerStyle,
        testID: "FlightsLanding__ShortcutLink",
        accessibilityLabel: "FlightsLanding__ShortcutLink",
        children: _sectionsData.sectionsPayloadFlightListing.shortcutLinks.default.map(function (item, ind) {
          return (0, _jsxRuntime.jsx)(_shortcutLink2.default, Object.assign({
            style: styles.shortcutLinkStyle,
            shimmerColorsPrimary: shimmerColorsPrimary,
            type: _shortcutLink.ShortcutLinkType.loading
          }, item, {
            testID: `${testID}__ShortcutLinkDefault`,
            accessibilityLabel: `${accessibilityLabel}__ShortcutLinkDefault`
          }), ind);
        })
      });
    } else if ((0, _isEmpty.default)(contentShortLinkProcessed) && !flyContentShortLinkFetching) {
      return (0, _jsxRuntime.jsx)(_error.ErrorComponent, {
        type: _error.ErrorComponentType.standard,
        onPressed: function onPressed() {
          return dispatch(_flyRedux.FlyCreators.flyContentShortLinkRequest());
        },
        testID: `${testID}__ErrorComponentShortCutLinkFlySection`,
        accessibilityLabel: `${accessibilityLabel}__ErrorComponentShortCutLinkFlySection`
      });
    } else {
      return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
        children: (0, _jsxRuntime.jsx)(_reactNative.ScrollView, {
          style: needSeparator ? shortcutLinkContainerStyleWithSeparator : styles.shortcutLinkContainerStyle,
          testID: "FlightsLanding__ShortcutLink",
          accessibilityLabel: "FlightsLanding__ShortcutLink",
          horizontal: true,
          showsHorizontalScrollIndicator: false,
          contentContainerStyle: styles.contentScrollView,
          children: contentShortLinkProcessed.map(function (item, indx) {
            return (0, _jsxRuntime.jsx)(_shortcutLink2.default, Object.assign({
              style: styles.shortcutLinkStyle,
              shimmerColorsPrimary: shimmerColorsPrimary,
              type: _shortcutLink.ShortcutLinkType.default
            }, item, {
              testID: `${testID}__ShortcutLinkDefault`,
              accessibilityLabel: `${accessibilityLabel}__ShortcutLinkDefault`,
              isCustomIcon: false,
              icon: (0, _mediaHelper.handleImageUrl)(item == null ? undefined : item.icon),
              onPressed: function onPressed(newProps) {
                return shortcutLinksOnPressed(newProps == null ? undefined : newProps.navigation, item);
              }
            }), indx);
          })
        })
      });
    }
  };
  var styles = _reactNative.StyleSheet.create({
    contentScrollView: {
      paddingRight: 20
    },
    overLayLoading: {
      height: height,
      position: "absolute",
      width: width
    },
    shortcutLinkContainerStyle: {
      flexDirection: "row",
      paddingStart: 12
    },
    shortcutLinkContainerStyleWithSeparator: {
      flexDirection: "row",
      marginBottom: 50,
      paddingStart: 12
    },
    shortcutLinkStyle: {
      backgroundColor: _theme.color.palette.whiteGrey
    }
  });
  var _default = exports.default = _react.default.memo(ShortCutLinkFlySection);
