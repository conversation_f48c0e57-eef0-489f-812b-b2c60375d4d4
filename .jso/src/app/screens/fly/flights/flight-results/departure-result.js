  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.DepartureScreen = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _native = _$$_REQUIRE(_dependencyMap[7]);
  var _alertApp = _$$_REQUIRE(_dependencyMap[8]);
  var _alertApp2 = _$$_REQUIRE(_dependencyMap[9]);
  var _errorOverlay = _$$_REQUIRE(_dependencyMap[10]);
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[11]);
  var _feedbackToast = _$$_REQUIRE(_dependencyMap[12]);
  var _confirmPopupSaveFlight = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _flightListingCard = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _loadingAnimation = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _text = _$$_REQUIRE(_dependencyMap[16]);
  var _i18n = _$$_REQUIRE(_dependencyMap[17]);
  var _aemRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[18]));
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[19]);
  var _flyRedux = _$$_REQUIRE(_dependencyMap[20]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[21]);
  var _calendarFliter = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[22]));
  var _adobe = _$$_REQUIRE(_dependencyMap[23]);
  var _fly = _$$_REQUIRE(_dependencyMap[24]);
  var _color = _$$_REQUIRE(_dependencyMap[25]);
  var _constants = _$$_REQUIRE(_dependencyMap[26]);
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[27]);
  var _storageKey = _$$_REQUIRE(_dependencyMap[28]);
  var _backgrounds = _$$_REQUIRE(_dependencyMap[29]);
  var _icons = _$$_REQUIRE(_dependencyMap[30]);
  var _lodash = _$$_REQUIRE(_dependencyMap[31]);
  var _lottieReactNative = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[32]));
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[33]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[34]);
  var _modal = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[35]));
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[36]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[37]);
  var _customToast = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[38]));
  var _flightProps = _$$_REQUIRE(_dependencyMap[39]);
  var _emptyStateListing = _$$_REQUIRE(_dependencyMap[40]);
  var _flightResultStyles = _$$_REQUIRE(_dependencyMap[41]);
  var _flightResults = _$$_REQUIRE(_dependencyMap[42]);
  var _flightPageHeaderV = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[43]));
  var _useDepartureFlight = _$$_REQUIRE(_dependencyMap[44]);
  var _envParams = _$$_REQUIRE(_dependencyMap[45]);
  var _addReturnCalendar = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[46]));
  var _analytics = _$$_REQUIRE(_dependencyMap[47]);
  var _utils = _$$_REQUIRE(_dependencyMap[48]);
  var _flightFilterRevamp = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[49]));
  var _screenHelper = _$$_REQUIRE(_dependencyMap[50]);
  var _filterLocation = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[51]));
  var _screenHelper2 = _$$_REQUIRE(_dependencyMap[52]);
  var _useTickerbandMaintanence = _$$_REQUIRE(_dependencyMap[53]);
  var _flightInformationDisclaimer = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[54]));
  var _subscriptions = _$$_REQUIRE(_dependencyMap[55]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[56]);
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[57]);
  var _panResponder = _$$_REQUIRE(_dependencyMap[58]);
  var _mytravelRedux = _$$_REQUIRE(_dependencyMap[59]);
  var _flightDetail = _$$_REQUIRE(_dependencyMap[60]);
  var _saveFlightTraveOptionWrap = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[61]));
  var _useModal3 = _$$_REQUIRE(_dependencyMap[62]);
  var _storage = _$$_REQUIRE(_dependencyMap[63]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[64]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  /* eslint-disable @typescript-eslint/ban-ts-comment */

  var SCREEN_NAME = "DepartureResultScreen__";
  var AnimatedSectionList = _reactNativeReanimated.default.createAnimatedComponent(_reactNative2.SectionList);
  var _worklet_7513304489177_init_data = {
    code: "function departureResultTsx1(){const{heightHeaderUseShared}=this.__closure;return heightHeaderUseShared.value;}"
  };
  var _worklet_1369011269754_init_data = {
    code: "function departureResultTsx2(){const{isCheckHeaderStickyUseShared}=this.__closure;return isCheckHeaderStickyUseShared.value;}"
  };
  var _worklet_16277485591355_init_data = {
    code: "function departureResultTsx3(){const{isRefreshingUseShared}=this.__closure;return isRefreshingUseShared.value;}"
  };
  var _worklet_14594518406396_init_data = {
    code: "function departureResultTsx4(){const{isAndroidUseShared}=this.__closure;return isAndroidUseShared.value;}"
  };
  var _worklet_8083767714385_init_data = {
    code: "function departureResultTsx5(){const{withSpring,isCheckHeaderSticky}=this.__closure;return{opacity:withSpring(isCheckHeaderSticky.value?1:0)};}"
  };
  var _worklet_2816411205895_init_data = {
    code: "function departureResultTsx6(){const{withSpring,isCheckHeaderSticky}=this.__closure;return{paddingRight:withSpring(isCheckHeaderSticky.value?48:0)};}"
  };
  var _worklet_4236547513146_init_data = {
    code: "function departureResultTsx7(){const{withSpring,isRefreshing,isAndroid}=this.__closure;return{opacity:withSpring(isRefreshing.value&&isAndroid.value?1:0)};}"
  };
  var _worklet_14205965697752_init_data = {
    code: "function departureResultTsx8(){const{withSpring,fabVisibility}=this.__closure;return{opacity:withSpring(fabVisibility.value)};}"
  };
  var _worklet_1593895739991_init_data = {
    code: "function departureResultTsx9(event){const{scrollY,isRefreshingUseShared,isCheckHeaderStickyUseShared,heightHeader,fabVisibility,isOnPressTouch,willBeRefreshed}=this.__closure;scrollY.value=event.contentOffset.y;const yOffset=event.contentOffset.y;isRefreshingUseShared.value=yOffset<0;isCheckHeaderStickyUseShared.value=yOffset>=heightHeader.value-76;if(event.contentOffset.y>heightHeader.value){fabVisibility.value=1;if(!isOnPressTouch.value){willBeRefreshed.value=false;}}else if(event.contentOffset.y===0){willBeRefreshed.value=true;fabVisibility.value=0;isOnPressTouch.value=false;}else{willBeRefreshed.value=false;}}"
  };
  var DepartureScreen = exports.DepartureScreen = function DepartureScreen() {
    // HOOKS
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var _useState = (0, _react.useState)((0, _mmkvStorage.getIsShowModalCheckRatingPopup)()),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isModalCheckShowRatingPopup = _useState2[0],
      setStateIsModalCheckShowRatingPopup = _useState2[1];
    var connectingFlightPayload = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.connectingFlightPayload);
    var _React$useContext = _react.default.useContext(_flightResults.FlyContext),
      searchDate = _React$useContext.searchDate,
      setSearchDate = _React$useContext.setSearchDate,
      searchFilterLocation = _React$useContext.searchFilterLocation,
      setSearchFilterLocation = _React$useContext.setSearchFilterLocation,
      keySearchInput = _React$useContext.keySearchInput,
      setKeySearchInput = _React$useContext.setKeySearchInput,
      isLoadFlightAfter24h = _React$useContext.isLoadFlightAfter24h,
      searchFilterAirline = _React$useContext.searchFilterAirline,
      setSearchFilterAirline = _React$useContext.setSearchFilterAirline,
      searchFilterCityAirport = _React$useContext.searchFilterCityAirport,
      setSearchFilterCityAirport = _React$useContext.setSearchFilterCityAirport;
    var flyLandingSelectedTab = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flyLandingSelectedTab);
    var flightFilterOptions = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flightFilterOptions);
    var flightFilterOptionSelected = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flightFilterOptionSelected);
    var myTravelFlightsPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.myTravelFlightsPayload);
    var terminalList = (flightFilterOptions == null ? undefined : flightFilterOptions.terminal) || [];
    var dataCommonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getMessagesCommon);
    var msg58 = dataCommonAEM == null ? undefined : dataCommonAEM.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG58";
    });
    var msg47 = dataCommonAEM == null ? undefined : dataCommonAEM.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG47";
    });
    var msg48 = dataCommonAEM == null ? undefined : dataCommonAEM.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG48";
    });
    var msg50 = dataCommonAEM == null ? undefined : dataCommonAEM.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG50";
    });
    var dispatch = (0, _reactRedux.useDispatch)();
    var isFocused = (0, _native.useIsFocused)();
    var navigation = (0, _native.useNavigation)();
    var route = (0, _native.useRoute)();
    var _useDepatureFlight = (0, _useDepartureFlight.useDepatureFlight)(),
      sectionList = _useDepatureFlight.sectionList,
      isLoading = _useDepatureFlight.isLoading,
      lastUpdatedTime = _useDepatureFlight.lastUpdatedTime,
      isFocusedRef = _useDepatureFlight.isFocusedRef,
      isEndLoadMore = _useDepatureFlight.isEndLoadMore,
      searchMoreEnabled = _useDepatureFlight.searchMoreEnabled,
      isNetworkError = _useDepatureFlight.isNetworkError,
      setSectionList = _useDepatureFlight.setSectionList,
      startLoopApiCall = _useDepatureFlight.startLoopApiCall,
      cancelLoopApiJob = _useDepatureFlight.cancelLoopApiJob,
      getFlyDepartureList = _useDepatureFlight.getFlyDepartureList,
      searchDepartureFlight = _useDepatureFlight.searchDepartureFlight,
      myTravelInsertFlightQuery = _useDepatureFlight.myTravelInsertFlightQuery,
      removeSavedFlight = _useDepatureFlight.removeSavedFlight;
    var _useTickerbandMaintan = (0, _useTickerbandMaintanence.useTickerbandMaintanance)(_useTickerbandMaintanence.MaintananceTickerBandType.FLY_RESULTS),
      isShowMaintenance = _useTickerbandMaintan.isShowMaintenance;

    // STATE
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isFilterLocationOpen = _useState4[0],
      setFilterLocationOpen = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isCalendarVisible = _useState6[0],
      setCalendarVisible = _useState6[1];
    var _useState7 = (0, _react.useState)(null),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      isAutoFocus = _useState8[0],
      setAutoFocus = _useState8[1];
    var _useState9 = (0, _react.useState)(false),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      isNoConnection = _useState0[0],
      setNoConnection = _useState0[1];
    var _useState1 = (0, _react.useState)(null),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      isCanAutoFocus = _useState10[0],
      setCanAutoFocus = _useState10[1];
    var _useModal = (0, _useModal3.useModal)("saveConnectingFlightResultDep"),
      isModalVisible = _useModal.isModalVisible,
      openModal = _useModal.openModal,
      closeModal = _useModal.closeModal;
    var _useState11 = (0, _react.useState)(_flightDetail.TravelOption.iAmTravelling),
      _useState12 = (0, _slicedToArray2.default)(_useState11, 2),
      selectedTravelOption = _useState12[0],
      setSelectedTravelOption = _useState12[1];
    var _useModal2 = (0, _useModal3.useModal)("saveFlightTravelOptionResultDep"),
      isModalVisibleOption = _useModal2.isModalVisible,
      openModalOption = _useModal2.openModal,
      closeModalOption = _useModal2.closeModal;
    var _useState13 = (0, _react.useState)(false),
      _useState14 = (0, _slicedToArray2.default)(_useState13, 2),
      loadingSaveFlight = _useState14[0],
      setLoadingSaveFlight = _useState14[1];
    var _useState15 = (0, _react.useState)(null),
      _useState16 = (0, _slicedToArray2.default)(_useState15, 2),
      saveFlightPayload = _useState16[0],
      setSaveFlightPayload = _useState16[1];
    var _useState17 = (0, _react.useState)(false),
      _useState18 = (0, _slicedToArray2.default)(_useState17, 2),
      isGoToListingFlight = _useState18[0],
      setIsGoToListingFlight = _useState18[1];
    var _useState19 = (0, _react.useState)(false),
      _useState20 = (0, _slicedToArray2.default)(_useState19, 2),
      showCalendarModal = _useState20[0],
      setShowCalendarModal = _useState20[1];

    // REF
    var alertApp = (0, _react.useRef)(null);
    var userTyping = (0, _react.useRef)(false);
    var filterDate = (0, _react.useRef)(searchDate || "");
    var filtersLocation = (0, _react.useRef)(searchFilterLocation || []);
    var filterKeyword = (0, _react.useRef)(keySearchInput || "");
    var filterAirline = (0, _react.useRef)(searchFilterAirline || "");
    var filterCityAirport = (0, _react.useRef)(searchFilterCityAirport || "");
    var toastForRefresh = (0, _react.useRef)(null);
    var scrollRef = (0, _react.useRef)(null);
    var toast = (0, _react.useRef)(null);
    var toastForRemoveFlight = (0, _react.useRef)(null);
    var toastForSavedFlight = (0, _react.useRef)(null);
    var isAfterLogin = (0, _react.useRef)(false);

    // ANIMATION
    var scrollY = (0, _reactNativeReanimated.useSharedValue)(0);
    var heightHeaderUseShared = (0, _reactNativeReanimated.useSharedValue)(156);
    var fabVisibility = (0, _reactNativeReanimated.useSharedValue)(0);
    var isOnPressTouch = (0, _reactNativeReanimated.useSharedValue)(false);
    var willBeRefreshed = (0, _reactNativeReanimated.useSharedValue)(true);
    var isCheckHeaderStickyUseShared = (0, _reactNativeReanimated.useSharedValue)(false);
    var isRefreshingUseShared = (0, _reactNativeReanimated.useSharedValue)(false);
    var isAndroidUseShared = (0, _reactNativeReanimated.useSharedValue)(_reactNative2.Platform.OS === "android");
    var FLY_CONTEXT_HANDLERS = _react.default.useContext(_fly.FLY_CONTEXT).Handlers;
    var newFilterFlagOn = true;
    var _useContext = (0, _react.useContext)(_panResponder.PanResponderContext),
      conditionTimeRef = _useContext.conditionTimeRef,
      idleTimeRef = _useContext.idleTimeRef;
    var initialCalendarFilterMinDate = (0, _moment.default)().add(-1, "days").format("YYYY-MM-DD");
    var isLoadingResult = (0, _react.useMemo)(function () {
      return isLoading && !isModalCheckShowRatingPopup;
    }, [isLoading, isModalCheckShowRatingPopup]);
    var heightHeader = (0, _reactNativeReanimated.useDerivedValue)(function () {
      var departureResultTsx1 = function departureResultTsx1() {
        return heightHeaderUseShared.value;
      };
      departureResultTsx1.__closure = {
        heightHeaderUseShared: heightHeaderUseShared
      };
      departureResultTsx1.__workletHash = 7513304489177;
      departureResultTsx1.__initData = _worklet_7513304489177_init_data;
      return departureResultTsx1;
    }());
    var isCheckHeaderSticky = (0, _reactNativeReanimated.useDerivedValue)(function () {
      var departureResultTsx2 = function departureResultTsx2() {
        return isCheckHeaderStickyUseShared.value;
      };
      departureResultTsx2.__closure = {
        isCheckHeaderStickyUseShared: isCheckHeaderStickyUseShared
      };
      departureResultTsx2.__workletHash = 1369011269754;
      departureResultTsx2.__initData = _worklet_1369011269754_init_data;
      return departureResultTsx2;
    }());
    var isRefreshing = (0, _reactNativeReanimated.useDerivedValue)(function () {
      var departureResultTsx3 = function departureResultTsx3() {
        return isRefreshingUseShared.value;
      };
      departureResultTsx3.__closure = {
        isRefreshingUseShared: isRefreshingUseShared
      };
      departureResultTsx3.__workletHash = 16277485591355;
      departureResultTsx3.__initData = _worklet_16277485591355_init_data;
      return departureResultTsx3;
    }());
    var isAndroid = (0, _reactNativeReanimated.useDerivedValue)(function () {
      var departureResultTsx4 = function departureResultTsx4() {
        return isAndroidUseShared.value;
      };
      departureResultTsx4.__closure = {
        isAndroidUseShared: isAndroidUseShared
      };
      departureResultTsx4.__workletHash = 14594518406396;
      departureResultTsx4.__initData = _worklet_14594518406396_init_data;
      return departureResultTsx4;
    }());
    var onLayoutHeader = function onLayoutHeader(event) {
      var height = event.nativeEvent.layout.height;
      heightHeaderUseShared.value = height;
    };
    var animatedFilterLocationStyles = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var departureResultTsx5 = function departureResultTsx5() {
        return {
          opacity: (0, _reactNativeReanimated.withSpring)(isCheckHeaderSticky.value ? 1 : 0)
        };
      };
      departureResultTsx5.__closure = {
        withSpring: _reactNativeReanimated.withSpring,
        isCheckHeaderSticky: isCheckHeaderSticky
      };
      departureResultTsx5.__workletHash = 8083767714385;
      departureResultTsx5.__initData = _worklet_8083767714385_init_data;
      return departureResultTsx5;
    }());
    var animatedOnlyInputStyles = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var departureResultTsx6 = function departureResultTsx6() {
        return {
          paddingRight: (0, _reactNativeReanimated.withSpring)(isCheckHeaderSticky.value ? 48 : 0)
        };
      };
      departureResultTsx6.__closure = {
        withSpring: _reactNativeReanimated.withSpring,
        isCheckHeaderSticky: isCheckHeaderSticky
      };
      departureResultTsx6.__workletHash = 2816411205895;
      departureResultTsx6.__initData = _worklet_2816411205895_init_data;
      return departureResultTsx6;
    }());
    var refreshingAnimated = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var departureResultTsx7 = function departureResultTsx7() {
        return {
          opacity: (0, _reactNativeReanimated.withSpring)(isRefreshing.value && isAndroid.value ? 1 : 0)
        };
      };
      departureResultTsx7.__closure = {
        withSpring: _reactNativeReanimated.withSpring,
        isRefreshing: isRefreshing,
        isAndroid: isAndroid
      };
      departureResultTsx7.__workletHash = 4236547513146;
      departureResultTsx7.__initData = _worklet_4236547513146_init_data;
      return departureResultTsx7;
    }());
    var animatedFabVisibilityStyles = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var departureResultTsx8 = function departureResultTsx8() {
        return {
          opacity: (0, _reactNativeReanimated.withSpring)(fabVisibility.value)
        };
      };
      departureResultTsx8.__closure = {
        withSpring: _reactNativeReanimated.withSpring,
        fabVisibility: fabVisibility
      };
      departureResultTsx8.__workletHash = 14205965697752;
      departureResultTsx8.__initData = _worklet_14205965697752_init_data;
      return departureResultTsx8;
    }());
    var isShowBadgeRed = (0, _react.useMemo)(function () {
      var terminal = flightFilterOptionSelected.terminal,
        airline = flightFilterOptionSelected.airline,
        cityAirport = flightFilterOptionSelected.cityAirport;
      {
        return !(0, _lodash.isEmpty)(terminal) && terminal.length < terminalList.length - 1 || airline !== "all" || cityAirport !== "all";
      }
    }, [flightFilterOptionSelected, terminalList, searchFilterLocation, newFilterFlagOn]);
    var clearConnectingFlightPayload = function clearConnectingFlightPayload() {
      var connectingFlightPayloadToClear = {
        isConnecting: false,
        flightConnecting: null
      };
      dispatch(_flyRedux.FlyCreators.setConnectingFlightPayload(connectingFlightPayloadToClear));
    };
    (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
      if (!isAfterLogin.current) {
        onFilterFlight({});
      } else {
        isAfterLogin.current = false;
      }
      isFocusedRef.current = true;
      return function () {
        isFocusedRef.current = false;
        if (connectingFlightPayload.isConnecting) {
          clearConnectingFlightPayload();
        }
      };
    }, []));
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)(_constants.TrackingScreenName.FlightResultDeparture);
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)(_constants.TrackingScreenName.FlightResultDeparture, (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribeFocus;
    }, [navigation]);
    (0, _react.useEffect)(function () {
      (0, _analytics.analyticsLogEvent)(_analytics.ANALYTICS_LOG_EVENT_NAME.FLIGHT_DEP_LIST);
      (0, _analytics.dtACtionLogEvent)(_analytics.ANALYTICS_LOG_EVENT_NAME.FLIGHT_DEP_LIST);
      (0, _analytics.dtBizEvent)(SCREEN_NAME, _analytics.ANALYTICS_LOG_EVENT_NAME.FLIGHT_ARR_LIST, 'TAP-ON-DEP-LIST', {});
      return function () {
        return null;
      };
    }, []);
    var handleRefresh = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        if (isConnected) {
          var _toastForRefresh$curr;
          setNoConnection(false);
          toastForRefresh == null || (_toastForRefresh$curr = toastForRefresh.current) == null || _toastForRefresh$curr.closeNow();
          if (willBeRefreshed.value) {
            refreshDepartureFlights();
            dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
          }
        } else {
          if (!(0, _lodash.isEmpty)(sectionList)) {
            var _toastForRefresh$curr2, _toastForRefresh$curr3;
            toastForRefresh == null || (_toastForRefresh$curr2 = toastForRefresh.current) == null || _toastForRefresh$curr2.closeNow();
            toastForRefresh == null || (_toastForRefresh$curr3 = toastForRefresh.current) == null || _toastForRefresh$curr3.show(_constants.TOAST_MESSAGE_DURATION);
          } else {
            setNoConnection(true);
            setSectionList([]);
          }
        }
      });
      return function handleRefresh() {
        return _ref.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      if (isFilterLocationOpen || isCalendarVisible || isModalVisible || isModalVisibleOption || loadingSaveFlight || showCalendarModal) {
        cancelLoopApiJob();
      } else {
        startLoopApiCall(handleRefresh);
      }
    }, [isFilterLocationOpen, isCalendarVisible, isModalVisible, isModalVisibleOption, loadingSaveFlight, showCalendarModal]);
    (0, _react.useEffect)(function () {
      if (FLY_CONTEXT_HANDLERS.fly_listing_departure_focus_search_bar && isCanAutoFocus && !isLoading) {
        setAutoFocus(true);
      }
    }, [isCanAutoFocus, isLoading]);
    (0, _react.useEffect)(function () {
      filterDate.current = searchDate;
    }, [searchDate]);
    (0, _react.useEffect)(function () {
      filtersLocation.current = searchFilterLocation;
    }, [searchFilterLocation]);
    (0, _react.useEffect)(function () {
      filterAirline.current = searchFilterAirline;
    }, [searchFilterAirline]);
    (0, _react.useEffect)(function () {
      filterCityAirport.current = searchFilterCityAirport;
    }, [searchFilterCityAirport]);
    (0, _react.useEffect)(function () {
      filterKeyword.current = keySearchInput;
    }, [keySearchInput]);
    (0, _react.useEffect)(function () {
      (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(isLoadingResult);
      if (isLoadingResult) {
        (0, _screenHelper.clearAppRatingTimers)({
          conditionTimeRef: conditionTimeRef,
          idleTimeRef: idleTimeRef
        });
      } else {
        (0, _screenHelper.resetInactivityTimeout)({
          conditionTimeRef: conditionTimeRef,
          idleTimeRef: idleTimeRef,
          callback: function callback() {
            return (0, _screenHelper.trackingShowRatingPopup)({
              route: route
            });
          }
        });
      }
    }, [isLoadingResult]);
    var onFilterFlight = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* (_ref2) {
        var _ref2$date = _ref2.date,
          date = _ref2$date === undefined ? "" : _ref2$date,
          _ref2$filterLocation = _ref2.filterLocation,
          filters = _ref2$filterLocation === undefined ? undefined : _ref2$filterLocation,
          _ref2$keySearch = _ref2.keySearch,
          keySearch = _ref2$keySearch === undefined ? "" : _ref2$keySearch,
          _ref2$airline = _ref2.airline,
          airline = _ref2$airline === undefined ? "" : _ref2$airline,
          _ref2$cityAirport = _ref2.cityAirport,
          cityAirport = _ref2$cityAirport === undefined ? "" : _ref2$cityAirport;
        var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch2.isConnected;
        if (isConnected) {
          setNoConnection(false);
          if (date) {
            filterDate.current = date;
            (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlightListingFlightListFilter, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlightListingFlightListFilter, (0, _moment.default)(date).format("YYYY-MM-DD")));
          }
          if (filters) {
            filtersLocation.current = filters;
            (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlightListingFlightListFilter, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlightListingFlightListFilter, `${filters.join("|")}`));
          }
          if (keySearch) {
            filterKeyword.current = keySearch;
            (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlightListingFlightListFilter, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlightListingFlightListFilter, `${keySearch}`));
          }
          if (airline) {
            filterAirline.current = airline;
          }
          if (cityAirport) {
            filterCityAirport.current = cityAirport;
          }
          scrollListToTop();
          refreshDepartureFlights();
        } else {
          if (!(0, _lodash.isEmpty)(sectionList)) {
            var _toastForRefresh$curr4, _toastForRefresh$curr5;
            toastForRefresh == null || (_toastForRefresh$curr4 = toastForRefresh.current) == null || _toastForRefresh$curr4.closeNow();
            toastForRefresh == null || (_toastForRefresh$curr5 = toastForRefresh.current) == null || _toastForRefresh$curr5.show(_constants.TOAST_MESSAGE_DURATION);
          } else {
            setNoConnection(true);
            setSectionList([]);
          }
        }
      });
      return function onFilterFlight(_x) {
        return _ref3.apply(this, arguments);
      };
    }();
    var refreshDepartureFlights = function refreshDepartureFlights() {
      // prevent refresh data while user typing search keyword
      if (userTyping.current) {
        return;
      }
      loadDepartureResults();
      dispatch(_aemRedux.default.getAemConfigData({
        name: _aemRedux.AEM_PAGE_NAME.TICKER_BAND_FLIGHT,
        pathName: "getTickerbandFly",
        forceRequest: true
      }));
    };
    var isSearchMode = function isSearchMode() {
      return filterKeyword.current && filterKeyword.current.length >= 2;
    };
    var loadDepartureResults = function loadDepartureResults() {
      if (isSearchMode()) {
        // call search flight API
        var contextData = {
          direction: "Departure",
          keySearch: filterKeyword.current
        };
        (0, _screenHelper2.trackActionNewFormat)(_adobe.AdobeTagName.CAppFlightListingFlightsearch, contextData, true);
        searchDepartureFlight({
          direction: _flightProps.FlightDirection.departure,
          filterDate: filterDate.current ? (0, _moment.default)(filterDate.current) : (0, _moment.default)(),
          filterTerminal: (0, _utils.simpleCondition)({
            condition: filtersLocation.current.length === terminalList.length - 1,
            ifValue: [],
            elseValue: filtersLocation.current
          }),
          keyword: filterKeyword.current,
          filterAirline: (0, _utils.simpleCondition)({
            condition: filterAirline.current === "all",
            ifValue: "",
            elseValue: filterAirline.current
          }),
          filterCityAirport: (0, _utils.simpleCondition)({
            condition: filterCityAirport.current === "all",
            ifValue: "",
            elseValue: filterCityAirport.current
          })
        });
      } else {
        getFlyDepartureList({
          direction: _flightProps.FlightDirection.departure,
          filterDate: filterDate.current ? (0, _moment.default)(filterDate.current) : (0, _moment.default)(),
          filters: (0, _utils.simpleCondition)({
            condition: filtersLocation.current.length === terminalList.length - 1,
            ifValue: [],
            elseValue: filtersLocation.current
          }),
          isFilter: false,
          isLoadFlightAfter24h: isLoadFlightAfter24h,
          filterAirline: (0, _utils.simpleCondition)({
            condition: filterAirline.current === "all",
            ifValue: "",
            elseValue: filterAirline.current
          }),
          filterCityAirport: (0, _utils.simpleCondition)({
            condition: filterCityAirport.current === "all",
            ifValue: "",
            elseValue: filterCityAirport.current
          })
        });
        setCanAutoFocus(true);
      }
      startLoopApiCall(handleRefresh);
    };
    var scrollListToTop = function scrollListToTop() {
      if (sectionList.length > 0) {
        var _scrollRef$current;
        (_scrollRef$current = scrollRef.current) == null || _scrollRef$current.scrollToLocation({
          animated: true,
          sectionIndex: 0,
          itemIndex: 0,
          viewPosition: 0
        });
      }
    };
    var _onRefresh = /*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch3 = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch3.isConnected;
        if (isConnected) {
          var _toastForRefresh$curr6;
          toastForRefresh == null || (_toastForRefresh$curr6 = toastForRefresh.current) == null || _toastForRefresh$curr6.closeNow();
          willBeRefreshed.value = true;
          handleRefresh();
        } else {
          if (!(0, _lodash.isEmpty)(sectionList)) {
            var _toastForRefresh$curr7, _toastForRefresh$curr8;
            toastForRefresh == null || (_toastForRefresh$curr7 = toastForRefresh.current) == null || _toastForRefresh$curr7.closeNow();
            toastForRefresh == null || (_toastForRefresh$curr8 = toastForRefresh.current) == null || _toastForRefresh$curr8.show(_constants.TOAST_MESSAGE_DURATION);
          } else {
            setNoConnection(true);
            setSectionList([]);
          }
        }
        if (isAndroid.value) {
          isRefreshingUseShared.value = true;
          setTimeout(function () {
            isRefreshingUseShared.value = false;
          }, 2000);
        }
      });
      return function onRefresh() {
        return _ref4.apply(this, arguments);
      };
    }();
    var onOpenFilterLocation = function onOpenFilterLocation() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlightLandingFilter, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlightLandingFilter, "Open Filter"));
      (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(true);
      setStateIsModalCheckShowRatingPopup(true);
      setFilterLocationOpen(true);
    };
    var onOpenCalendarModal = function onOpenCalendarModal() {
      setCalendarVisible(true);
      (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(true);
      setStateIsModalCheckShowRatingPopup(true);
    };
    var onClosedCalendarModal = function onClosedCalendarModal() {
      setCalendarVisible(false);
      (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
      setStateIsModalCheckShowRatingPopup(false);
    };
    var TimeUpdated = function TimeUpdated() {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _flightResultStyles.departureStyles.flatListHeaderStyles,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          text: (0, _i18n.translate)("flightLanding.updatedSystemTime") + " " + lastUpdatedTime,
          preset: "caption2Regular",
          style: _flightResultStyles.departureStyles.lastUpdatedTextStyle
        }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
          style: _flightResultStyles.departureStyles.iconViewStyles,
          onPress: onOpenFilterLocation,
          children: [isShowBadgeRed && (0, _jsxRuntime.jsx)(_icons.Badge, {
            style: _flightResultStyles.departureStyles.badgeViewStyle,
            height: 8,
            width: 8
          }), (0, _jsxRuntime.jsx)(_icons.Filter, {
            color: _color.color.palette.lightPurple,
            height: 20,
            width: 20
          })]
        })]
      });
    };
    var getSectionHeaderStyles = function getSectionHeaderStyles(index) {
      return {
        marginBottom: 12,
        marginTop: index === 0 ? heightHeaderUseShared.value : 12
      };
    };
    var getTextySectionHeaderStyles = function getTextySectionHeaderStyles(index) {
      return {
        marginBottom: 12,
        marginTop: index === 0 ? 175 : 12
      };
    };
    var renderSectionHeader = function renderSectionHeader(_ref5) {
      var title = _ref5.section.title;
      var index = sectionList.findIndex(function (item) {
        return item.title === title;
      });
      if (index === 0) {
        return (0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
          style: getSectionHeaderStyles(index),
          children: [(0, _jsxRuntime.jsx)(TimeUpdated, {}), (0, _jsxRuntime.jsx)(_flightInformationDisclaimer.default, {
            marginTop: 12
          })]
        });
      }
      if (searchDate.length > 0) {
        return (0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
          style: getSectionHeaderStyles(index),
          children: [(0, _jsxRuntime.jsx)(TimeUpdated, {}), (0, _jsxRuntime.jsx)(_flightInformationDisclaimer.default, {
            marginTop: 12
          })]
        });
      }
      return (0, _jsxRuntime.jsx)(_text.Text, {
        preset: "h4",
        text: title,
        style: [_flightResultStyles.departureStyles.containerFilter, getTextySectionHeaderStyles(index)]
      });
    };
    var isSearchableRef = (0, _react.useRef)(true);
    var onSearch = function onSearch(keySearch) {
      if (isSearchableRef.current) {
        isSearchableRef.current = false;
        userTyping.current = false;
        onFilterFlight({
          keySearch: keySearch
        });
      }
    };
    var onUserTypingKeySearch = function onUserTypingKeySearch() {
      isSearchableRef.current = true;
      userTyping.current = true;
      cancelLoopApiJob();
    };
    var handleKeySearch = function handleKeySearch(e) {
      setKeySearchInput(e);
      onUserTypingKeySearch();
      onDebounceKeySearch(e);
    };
    var onDebounceKeySearch = (0, _react.useCallback)((0, _lodash.debounce)(function (e) {
      onSearch(e);
    }, 2000), []);
    var onFlightPress = function onFlightPress(item) {
      var flightDate = item.flightDate;
      var flightNumber = item.flightNumber;
      var direction = item.direction;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlightListingFlightCardClicked, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlightListingFlightCardClicked, "1"));
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlightListingViewFlightDetails, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlightListingViewFlightDetails, `${direction}|${flightDate}|${flightNumber}`));
      //@ts-ignore
      navigation.navigate("flightDetails", {
        payload: {
          item: item
        },
        direction: _flightProps.FlightDirection.departure
      });
    };
    var checkFlightCanSave = function checkFlightCanSave(statusTag) {
      var status = statusTag == null ? undefined : statusTag.toLowerCase();
      switch (true) {
        case /departed/gim.test(status):
        case /cancelled/gim.test(status):
        case /landed/gim.test(status):
          return false;
        default:
          return true;
      }
    };
    var handleMessage58 = function handleMessage58(message, flyItem) {
      if (message) {
        var _flyItem$flightStatus, _status;
        var status = flyItem == null || (_flyItem$flightStatus = flyItem.flightStatus) == null ? undefined : _flyItem$flightStatus.toLowerCase();
        if ((_status = status) != null && _status.includes("cancelled")) {
          status = `been ${status}`;
        }
        return message.replace("<Flight No.>", flyItem == null ? undefined : flyItem.flightNumber).replace("<departed/landed/been cancelled>", status);
      }
      return message;
    };
    var handleMessage48 = function handleMessage48(message, number, place) {
      if (message) {
        return message.replace("<Flight No.>", number).replace("<country>", place);
      }
      return message;
    };
    var notAbleToSaveAlert = function notAbleToSaveAlert(flyItem) {
      var _flyItem$flightStatus2, _alertApp$current;
      var temp = flyItem == null || (_flyItem$flightStatus2 = flyItem.flightStatus) == null ? undefined : _flyItem$flightStatus2.split(" ");
      var status = (temp == null ? undefined : temp.length) > 0 ? temp[0] : "";
      var message = handleMessage58(msg58 == null ? undefined : msg58.message, flyItem) || `${(0, _i18n.translate)("flightLanding.flight")} ${flyItem == null ? undefined : flyItem.flightNumber} ${(0, _i18n.translate)("flightLanding.has")} ${status} ${(0, _i18n.translate)("flightLanding.notSaveMessage")}`;
      alertApp == null || (_alertApp$current = alertApp.current) == null || _alertApp$current.show({
        title: (msg58 == null ? undefined : msg58.title) || (0, _i18n.translate)("flightLanding.alert"),
        description: message,
        labelAccept: (msg58 == null ? undefined : msg58.firstButton) || (0, _i18n.translate)("flightLanding.okay"),
        onAccept: function onAccept() {
          return null;
        },
        type: _alertApp2.AlertTypes.ALERT
      });
    };
    var onFABScrollPress = /*#__PURE__*/function () {
      var _ref6 = (0, _asyncToGenerator2.default)(function* () {
        isOnPressTouch.value = true;
        scrollListToTop();
        var _yield$NetInfo$fetch4 = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch4.isConnected;
        if (isConnected) {
          var _toastForRefresh$curr9;
          setNoConnection(false);
          toastForRefresh == null || (_toastForRefresh$curr9 = toastForRefresh.current) == null || _toastForRefresh$curr9.closeNow();
          refreshDepartureFlights();
          dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
        } else {
          var _toastForRefresh$curr0, _toastForRefresh$curr1;
          toastForRefresh == null || (_toastForRefresh$curr0 = toastForRefresh.current) == null || _toastForRefresh$curr0.closeNow();
          toastForRefresh == null || (_toastForRefresh$curr1 = toastForRefresh.current) == null || _toastForRefresh$curr1.show(_constants.TOAST_MESSAGE_DURATION);
        }
      });
      return function onFABScrollPress() {
        return _ref6.apply(this, arguments);
      };
    }();
    var onFlightSave = /*#__PURE__*/function () {
      var _ref7 = (0, _asyncToGenerator2.default)(function* (flightItem, isSaved) {
        if (isLoggedIn && isSaved) {
          onRemoveFlight({
            item: flightItem
          });
        } else {
          if (!checkFlightCanSave(flightItem.flightStatus)) {
            notAbleToSaveAlert(flightItem);
          } else {
            var payload = {
              item: flightItem
            };
            if (isLoggedIn) {
              openModalOption();
              setSaveFlightPayload(payload);
            } else {
              isAfterLogin.current = true;
              //@ts-ignore
              navigation.navigate(_constants.NavigationConstants.authScreen, {
                sourceSystem: _constants.SOURCE_SYSTEM.FLIGHTS,
                callBackAfterLoginSuccess: function callBackAfterLoginSuccess() {
                  openModalOption();
                  setSaveFlightPayload(payload);
                },
                callBackAfterLoginCancel: function callBackAfterLoginCancel() {
                  return null;
                }
              });
            }
          }
        }
      });
      return function onFlightSave(_x2, _x3) {
        return _ref7.apply(this, arguments);
      };
    }();
    var onRemoveFlight = function onRemoveFlight(payload) {
      var item = payload == null ? undefined : payload.item;
      _reactNative2.Alert.alert((msg48 == null ? undefined : msg48.title) || (0, _i18n.translate)("flightLanding.areYouSure"), msg48 != null && msg48.message ? handleMessage48(msg48 == null ? undefined : msg48.message, item == null ? undefined : item.flightNumber, item == null ? undefined : item.destinationPlace) : `${(0, _i18n.translate)("flightLanding.removeMessage1")} ${item == null ? undefined : item.flightNumber} ${(0, _i18n.translate)("flightLanding.to")} ${item == null ? undefined : item.destinationPlace} ${(0, _i18n.translate)("flightLanding.removeMessage2")}`, [{
        text: (msg48 == null ? undefined : msg48.firstButton) || (0, _i18n.translate)("flightLanding.cancel")
      }, {
        text: (msg48 == null ? undefined : msg48.secondButton) || (0, _i18n.translate)("flightLanding.remove"),
        style: "cancel",
        onPress: function onPress() {
          removeSavedFlight(payload, function () {
            var _saveFlightPayload$it, _saveFlightPayload$it2, _toastForSavedFlight$, _toastForRemoveFlight;
            var action = "Unsave";
            var flyProfile = "flying";
            var flightStatus = "Successful";
            (0, _screenHelper.trackActionOldFormat)(_adobe.AdobeTagName.CAppFlightListingSaveFlight, {
              pageName: _adobe.AdobeTagName.CAppFlightListing,
              flightNumber: saveFlightPayload == null || (_saveFlightPayload$it = saveFlightPayload.item) == null ? undefined : _saveFlightPayload$it.flightNumber,
              flightDirection: _flightProps.FlightDirection.departure,
              flightDate: saveFlightPayload == null || (_saveFlightPayload$it2 = saveFlightPayload.item) == null ? undefined : _saveFlightPayload$it2.flightDate,
              flyProfile: flyProfile,
              action: action,
              flightStatus: flightStatus
            });
            toastForSavedFlight == null || (_toastForSavedFlight$ = toastForSavedFlight.current) == null || _toastForSavedFlight$.closeNow();
            toastForRemoveFlight == null || (_toastForRemoveFlight = toastForRemoveFlight.current) == null || _toastForRemoveFlight.show(_constants.TOAST_MESSAGE_DURATION);
            dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
          }, function () {
            var _saveFlightPayload$it3, _saveFlightPayload$it4;
            var action = "Unsave";
            var flyProfile = "flying";
            var flightStatus = "Failed";
            (0, _screenHelper.trackActionOldFormat)(_adobe.AdobeTagName.CAppFlightListingSaveFlight, {
              pageName: _adobe.AdobeTagName.CAppFlightListing,
              flightNumber: saveFlightPayload == null || (_saveFlightPayload$it3 = saveFlightPayload.item) == null ? undefined : _saveFlightPayload$it3.flightNumber,
              flightDirection: _flightProps.FlightDirection.departure,
              flightDate: saveFlightPayload == null || (_saveFlightPayload$it4 = saveFlightPayload.item) == null ? undefined : _saveFlightPayload$it4.flightDate,
              flyProfile: flyProfile,
              action: action,
              flightStatus: flightStatus
            });
          });
        }
      }]);
    };
    var handleScroll = (0, _reactNativeReanimated.useAnimatedScrollHandler)(function () {
      var departureResultTsx9 = function departureResultTsx9(event) {
        scrollY.value = event.contentOffset.y;
        var yOffset = event.contentOffset.y;
        isRefreshingUseShared.value = yOffset < 0;
        isCheckHeaderStickyUseShared.value = yOffset >= heightHeader.value - 76;
        if (event.contentOffset.y > heightHeader.value) {
          fabVisibility.value = 1;
          if (!isOnPressTouch.value) {
            willBeRefreshed.value = false;
          }
        } else if (event.contentOffset.y === 0) {
          willBeRefreshed.value = true;
          fabVisibility.value = 0;
          isOnPressTouch.value = false;
        } else {
          willBeRefreshed.value = false;
        }
      };
      departureResultTsx9.__closure = {
        scrollY: scrollY,
        isRefreshingUseShared: isRefreshingUseShared,
        isCheckHeaderStickyUseShared: isCheckHeaderStickyUseShared,
        heightHeader: heightHeader,
        fabVisibility: fabVisibility,
        isOnPressTouch: isOnPressTouch,
        willBeRefreshed: willBeRefreshed
      };
      departureResultTsx9.__workletHash = 1593895739991;
      departureResultTsx9.__initData = _worklet_1593895739991_init_data;
      return departureResultTsx9;
    }());
    var handleLoadMore = function handleLoadMore() {
      if (isSearchMode()) {
        handleSearchMore();
      } else {
        handleLoadMoreList();
      }
    };
    var handleLoadMoreList = function handleLoadMoreList() {
      if (isEndLoadMore) {
        return;
      }
      getFlyDepartureList({
        direction: _flightProps.FlightDirection.departure,
        filterDate: filterDate.current ? (0, _moment.default)(filterDate.current) : (0, _moment.default)(),
        filters: (0, _utils.simpleCondition)({
          condition: filtersLocation.current.length === terminalList.length - 1,
          ifValue: [],
          elseValue: filtersLocation.current
        }),
        isFilter: searchDate !== "",
        isLoadFlightAfter24h: isLoadFlightAfter24h,
        isLoadMore: true,
        filterAirline: (0, _utils.simpleCondition)({
          condition: filterAirline.current === "all",
          ifValue: "",
          elseValue: filterAirline.current
        }),
        filterCityAirport: (0, _utils.simpleCondition)({
          condition: filterCityAirport.current === "all",
          ifValue: "",
          elseValue: filterCityAirport.current
        })
      });
    };
    var handleSearchMore = function handleSearchMore() {
      if (!searchMoreEnabled) {
        return;
      }
      var contextData = {
        direction: "Departure",
        keySearch: filterKeyword.current
      };
      (0, _screenHelper2.trackActionNewFormat)(_adobe.AdobeTagName.CAppFlightListingFlightsearch, contextData, true);
      searchDepartureFlight({
        direction: _flightProps.FlightDirection.departure,
        filterDate: filterDate.current ? (0, _moment.default)(filterDate.current) : (0, _moment.default)(),
        filterTerminal: (0, _utils.simpleCondition)({
          condition: filtersLocation.current.length === terminalList.length - 1,
          ifValue: [],
          elseValue: filtersLocation.current
        }),
        keyword: filterKeyword.current,
        isLoadMore: true,
        filterAirline: (0, _utils.simpleCondition)({
          condition: filterAirline.current === "all",
          ifValue: "",
          elseValue: filterAirline.current
        }),
        filterCityAirport: (0, _utils.simpleCondition)({
          condition: filterCityAirport.current === "all",
          ifValue: "",
          elseValue: filterCityAirport.current
        })
      });
    };
    var onClosedSheet = function onClosedSheet() {
      if (!loadingSaveFlight) {
        closeModalOption();
      }
    };
    var travelOptionTapped = function travelOptionTapped(option) {
      setSelectedTravelOption(option);
    };
    var savedFlightTravelOptionsOnModalHide = function savedFlightTravelOptionsOnModalHide() {
      setSelectedTravelOption(_flightDetail.TravelOption.iAmTravelling);
    };
    var savedFlightOnPress = /*#__PURE__*/function () {
      var _ref8 = (0, _asyncToGenerator2.default)(function* (_option) {
        var _saveFlightPayload$it5, _saveFlightPayload$it6;
        setLoadingSaveFlight(true);
        var data = {
          enterpriseUserId: profilePayload == null ? undefined : profilePayload.email,
          countryOfResidence: profilePayload == null ? undefined : profilePayload.residentialCountry,
          flightNumber: saveFlightPayload == null || (_saveFlightPayload$it5 = saveFlightPayload.item) == null ? undefined : _saveFlightPayload$it5.flightNumber,
          flightScheduledDate: saveFlightPayload == null || (_saveFlightPayload$it6 = saveFlightPayload.item) == null ? undefined : _saveFlightPayload$it6.scheduledDate,
          flightDirection: _flightProps.FlightDirection.departure,
          // check param
          flightPax: selectedTravelOption === _flightDetail.TravelOption.iAmTravelling
        };
        if (isLoggedIn) {
          var action = "Save";
          var flyProfile = "flying";
          myTravelInsertFlightQuery(data, saveFlightPayload, connectingFlightPayload,
          // success
          function (isFirstFlight) {
            var _env, _saveFlightPayload$it7, _saveFlightPayload$it8;
            setLoadingSaveFlight(false);
            var timeStamp = new Date().getTime();
            if (((0, _mmkvStorage.getLastSavedFlightTime)() + ((_env = (0, _envParams.env)()) == null ? undefined : _env.FLIGHT_SHOW_POPUP_ADD_RETURN) < timeStamp || isFirstFlight) && selectedTravelOption === _flightDetail.TravelOption.iAmTravelling) {
              _reactNative2.InteractionManager.runAfterInteractions(function () {
                openModal();
                (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(true);
                setStateIsModalCheckShowRatingPopup(true);
                (0, _mmkvStorage.setLastSavedFlightTime)(0);
              });
            } else {
              var _toastForRemoveFlight2, _toastForSavedFlight$2;
              closeModalOption();
              toastForRemoveFlight == null || (_toastForRemoveFlight2 = toastForRemoveFlight.current) == null || _toastForRemoveFlight2.closeNow();
              toastForSavedFlight == null || (_toastForSavedFlight$2 = toastForSavedFlight.current) == null || _toastForSavedFlight$2.show(_constants.TOAST_MESSAGE_DURATION);
              (0, _storage.save)(_storageKey.StorageKey.isSaveFlightTriggered, true);
            }
            var flightStatus = "Successful";
            if (connectingFlightPayload.isConnecting) {
              clearConnectingFlightPayload();
            }
            (0, _screenHelper.trackActionOldFormat)(_adobe.AdobeTagName.CAppFlightListingSaveFlight, {
              pageName: _adobe.AdobeTagName.CAppFlightListing,
              flightNumber: saveFlightPayload == null || (_saveFlightPayload$it7 = saveFlightPayload.item) == null ? undefined : _saveFlightPayload$it7.flightNumber,
              flightDirection: _flightProps.FlightDirection.departure,
              flightDate: saveFlightPayload == null || (_saveFlightPayload$it8 = saveFlightPayload.item) == null ? undefined : _saveFlightPayload$it8.flightDate,
              flyProfile: flyProfile,
              action: action,
              flightStatus: flightStatus
            });
          },
          // failure
          function () {
            var _saveFlightPayload$it9, _saveFlightPayload$it0;
            closeModalOption();
            setLoadingSaveFlight(false);
            var flightStatus = "failed";
            if (connectingFlightPayload.isConnecting) {
              clearConnectingFlightPayload();
            }
            (0, _screenHelper.trackActionOldFormat)(_adobe.AdobeTagName.CAppFlightListingSaveFlight, {
              pageName: _adobe.AdobeTagName.CAppFlightListing,
              flightNumber: saveFlightPayload == null || (_saveFlightPayload$it9 = saveFlightPayload.item) == null ? undefined : _saveFlightPayload$it9.flightNumber,
              flightDirection: _flightProps.FlightDirection.departure,
              flightDate: saveFlightPayload == null || (_saveFlightPayload$it0 = saveFlightPayload.item) == null ? undefined : _saveFlightPayload$it0.flightDate,
              flyProfile: flyProfile,
              action: action,
              flightStatus: flightStatus
            });
          });
        } else {
          //@ts-ignore
          navigation.navigate(_constants.NavigationConstants.authScreen, {
            sourceSystem: _constants.SOURCE_SYSTEM.FLIGHTS
          });
        }
      });
      return function savedFlightOnPress(_x4) {
        return _ref8.apply(this, arguments);
      };
    }();
    var footerListFlight = function footerListFlight() {
      var isEndPage = false;
      if (isSearchMode()) {
        isEndPage = !searchMoreEnabled;
      } else {
        isEndPage = isEndLoadMore;
      }
      if (isEndPage && sectionList.length > 0) {
        return (0, _jsxRuntime.jsx)(_text.Text, {
          tx: "flightLanding.noMoreFlights",
          style: _flightResultStyles.commonComponentStyles.noFlightListingStyle
        });
      }
      return null;
    };
    var onRenderItem = function onRenderItem(_ref9) {
      var item = _ref9.item,
        index = _ref9.index;
      var isSaved = false;
      if (myTravelFlightsPayload != null && myTravelFlightsPayload.getMyTravelFlightDetails) {
        isSaved = myTravelFlightsPayload.getMyTravelFlightDetails.findIndex(function (savedFlight) {
          return savedFlight.flightNumber === item.flightNumber && savedFlight.scheduledDate === item.scheduledDate && (savedFlight.flightDirection || savedFlight.direction) === (item.flightDirection || item.direction);
        }) >= 0;
      }
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _flightResultStyles.commonComponentStyles.flatListItemStyle,
        children: (0, _jsxRuntime.jsx)(_flightListingCard.default, Object.assign({}, item, {
          isLoggedIn: isLoggedIn,
          onPressed: function onPressed() {
            onFlightPress(item);
          },
          onSaved: function onSaved(isSaved) {
            onFlightSave(item, isSaved);
          },
          isSaved: isSaved,
          itemIndex: index,
          getSavedFlightsLoading: false
        }))
      });
    };
    var renderCalendarModalView = function renderCalendarModalView() {
      return (0, _jsxRuntime.jsx)(_modal.default, {
        isVisible: isCalendarVisible,
        swipeDirection: null,
        style: _flightResultStyles.commonComponentStyles.modalCalendarStyles,
        animationInTiming: 500,
        animationOutTiming: 500,
        animationIn: "slideInUp",
        animationOut: "slideOutDown",
        onBackdropPress: onClosedCalendarModal,
        onSwipeComplete: onClosedCalendarModal,
        children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: localStyles.calendarPickerContainer,
          activeOpacity: 1.0,
          onPress: function onPress() {
            setCalendarVisible(false);
            (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
            setStateIsModalCheckShowRatingPopup(false);
          },
          children: (0, _jsxRuntime.jsx)(_calendarFliter.default, {
            initialMinDate: initialCalendarFilterMinDate,
            containerStyle: localStyles.calendarFilterStyles,
            setFilterDate: function setFilterDate(dateString) {
              onFilterFlight({
                date: dateString
              });
              setSearchDate(dateString);
              setCalendarVisible(false);
              (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
              setStateIsModalCheckShowRatingPopup(false);
            },
            filterDate: searchDate ? (0, _moment.default)(new Date(searchDate)).format("YYYY-MM-DD") : (0, _moment.default)().format("YYYY-MM-DD"),
            testID: `dateTimePicker`,
            accessibilityLabel: `dateTimePicker`
          })
        })
      });
    };
    var renderFilterLocation = function renderFilterLocation() {
      {
        return (0, _jsxRuntime.jsx)(_flightFilterRevamp.default, {
          visible: isFilterLocationOpen,
          direction: _flightProps.FlightDirection.departure,
          navigationType: _flightProps.NavigationType.FlightsResult,
          onClosedSheet: function onClosedSheet() {
            setFilterLocationOpen(false);
            (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
          },
          handleApplyFilter: function handleApplyFilter(filterOption) {
            var direction = filterOption.direction;
            var flightFilterOptionSelected = {
              terminal: filterOption.terminal,
              airline: filterOption.airline,
              cityAirport: filterOption.cityAirport
            };
            setSearchFilterLocation(filterOption == null ? undefined : filterOption.terminal);
            setSearchFilterAirline(filterOption == null ? undefined : filterOption.airline);
            setSearchFilterCityAirport(filterOption == null ? undefined : filterOption.cityAirport);
            (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
            setStateIsModalCheckShowRatingPopup(false);
            dispatch(_flyRedux.FlyCreators.setFlightFilterOption(flightFilterOptionSelected));
            setFilterLocationOpen(false);
            if (direction === _flightProps.FlightDirection.departure) {
              onFilterFlight({
                filterLocation: filterOption.terminal,
                airline: filterOption.airline,
                cityAirport: filterOption.cityAirport
              });
            } else {
              //@ts-ignore
              navigation.navigate("arrivalResultScreen");
            }
          },
          testID: `${SCREEN_NAME}FlightFilterRevamp`,
          accessibilityLabel: `${SCREEN_NAME}FlightFilterRevamp`
        });
      }
    };
    var renderConfirmSaveFlight = function renderConfirmSaveFlight() {
      return (0, _jsxRuntime.jsx)(_confirmPopupSaveFlight.default, {
        imageUrl: (0, _mediaHelper.handleImageUrl)(msg47 == null ? undefined : msg47.icon),
        visible: isModalVisible && isFocused,
        title: (0, _i18n.translate)("flightDetails.newPopupConfirmSaveFlight.title"),
        messageText: (0, _i18n.translate)("flightDetails.newPopupConfirmSaveFlight.departureMessage"),
        onClose: function onClose() {
          closeModal();
          (0, _storage.save)(_storageKey.StorageKey.isSaveFlightTriggered, true);
        },
        onButtonPressed: function onButtonPressed() {
          setIsGoToListingFlight(true);
          closeModal();
        },
        textButtonConfirm: (0, _i18n.translate)("flightDetails.newPopupConfirmSaveFlight.addReturnFlightButton"),
        textButtonCancel: (0, _i18n.translate)("flightDetails.newPopupConfirmSaveFlight.cancelButton"),
        onModalHide: function onModalHide() {
          (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
          setStateIsModalCheckShowRatingPopup(false);
          var timeStamp = new Date().getTime();
          (0, _mmkvStorage.setLastSavedFlightTime)(timeStamp);
          dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
          if (isGoToListingFlight) {
            setShowCalendarModal(true);
            setIsGoToListingFlight(false);
          } else {
            var _toastForSavedFlight$3;
            toastForSavedFlight == null || (_toastForSavedFlight$3 = toastForSavedFlight.current) == null || _toastForSavedFlight$3.show(_feedbackToast.DURATION.LENGTH_LONG);
            dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
          }
        },
        disableCloseButton: true,
        openPendingModal: true
      });
    };
    var renderReturnCalendarView = function renderReturnCalendarView() {
      return (0, _jsxRuntime.jsx)(_addReturnCalendar.default, {
        isVisible: showCalendarModal,
        filterDate: (0, _moment.default)(saveFlightPayload ? saveFlightPayload.item.displayTimestamp : "").format("YYYY-MM-DD") || (0, _moment.default)().format("YYYY-MM-DD"),
        initialMinDate: (0, _moment.default)(saveFlightPayload ? saveFlightPayload.item.displayTimestamp : "").format("YYYY-MM-DD") || (0, _moment.default)().format("YYYY-MM-DD"),
        onClosedCalendarModal: function onClosedCalendarModal() {
          var _toastForSavedFlight$4;
          setShowCalendarModal(false);
          toastForSavedFlight == null || (_toastForSavedFlight$4 = toastForSavedFlight.current) == null || _toastForSavedFlight$4.show(_feedbackToast.DURATION.LENGTH_LONG);
          dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
          (0, _storage.save)(_storageKey.StorageKey.isSaveFlightTriggered, true);
        },
        onDateSelected: function onDateSelected(dateString) {
          var country = saveFlightPayload.item.country || "";
          setKeySearchInput(country);
          setShowCalendarModal(false);
          setSearchDate((0, _moment.default)(dateString).format("YYYY-MM-DD"));
          //@ts-ignore
          navigation.navigate("arrivalResultScreen");
          dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
          (0, _storage.save)(_storageKey.StorageKey.isSaveFlightTriggered, true);
        },
        testID: `${SCREEN_NAME}__AddReturnCalendar`,
        accessibilityLabel: `${SCREEN_NAME}__AddReturnCalendar`
      });
    };
    var showErrorToastMessage = function showErrorToastMessage() {
      return (0, _jsxRuntime.jsx)(_customToast.default, {
        ref: toast,
        style: _flightResultStyles.departureStyles.toastStyle,
        textButtonStyle: _flightResultStyles.departureStyles.toastButtonStyle,
        positionValue: _flightResultStyles.departureStyles.positionStyle,
        fadeInDuration: 750,
        fadeOutDuration: 1000,
        opacity: 1,
        position: "bottom",
        textStyle: _flightResultStyles.departureStyles.toastTextStyle
      });
    };
    var showErrorFeedBackToastMessage = function showErrorFeedBackToastMessage() {
      return (0, _jsxRuntime.jsx)(_feedbackToast.FeedBackToast, {
        ref: toastForRefresh,
        style: _flightResultStyles.departureStyles.feedBackToastStyle,
        textButtonStyle: _flightResultStyles.departureStyles.toastButtonStyle,
        position: "custom",
        textStyle: _flightResultStyles.departureStyles.toastTextStyle,
        type: _feedbackToast.FeedBackToastType.fullWidthFeedBack,
        text: (0, _i18n.translate)("flightLanding.feedBackToastErrorMessage") + lastUpdatedTime
      });
    };
    var renderNoInternetConnection = function renderNoInternetConnection() {
      return (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
        style: {
          transform: [{
            translateY: heightHeaderUseShared.value
          }]
        },
        children: (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
          reload: true,
          header: false,
          hideScreenHeader: true,
          headerBackgroundColor: "transparent",
          visible: true,
          testID: `${SCREEN_NAME}ErrorOverlayNoConnection`,
          onReload: handleRefresh,
          storyMode: true,
          noInternetOverlayStyle: _flightResultStyles.departureStyles.errorOverlayStyle
        })
      });
    };
    var renderNetworkError = function renderNetworkError() {
      return (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
        style: {
          transform: [{
            translateY: heightHeaderUseShared.value
          }]
        },
        children: (0, _jsxRuntime.jsx)(_errorOverlay.ErrorOverlay, {
          reload: true,
          header: false,
          visible: true,
          onReload: handleRefresh,
          testID: `${SCREEN_NAME}__ErrorComponent`,
          accessibilityLabel: `${SCREEN_NAME}__ErrorComponent`,
          variant: _errorOverlay.ErrorOverlayVariant.VARIANTSECTION,
          overlayStyle: _flightResultStyles.departureStyles.errorOverlayStyle,
          ignoreShowNoInternet: true
        })
      });
    };
    var EmptyComponent = (0, _react.useMemo)(function () {
      var contentText = _emptyStateListing.textForEmptyList.noFlightsForFilter;
      if (isSearchMode()) {
        contentText = _emptyStateListing.textForEmptyList.noFlightsForSearchResult;
      }
      return (0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
        style: [_flightResultStyles.commonComponentStyles.emptyComponentStyles, {
          marginTop: heightHeaderUseShared.value
        }],
        children: [(0, _jsxRuntime.jsx)(TimeUpdated, {}), (0, _jsxRuntime.jsx)(_text.Text, {
          style: _flightResultStyles.commonComponentStyles.textEmptyComponentStyles,
          preset: "caption1Regular",
          tx: contentText
        })]
      });
    }, [sectionList]);
    var renderContent = function renderContent() {
      return (0, _jsxRuntime.jsx)(AnimatedSectionList, {
        sections: sectionList,
        keyExtractor: function keyExtractor(_item, index) {
          return `${_item == null ? undefined : _item.flightNumber} ${_item == null ? undefined : _item.scheduledDate} ${index.toString()}`;
        }
        // @ts-ignore
        ,
        renderSectionHeader: renderSectionHeader,
        stickySectionHeadersEnabled: false,
        renderItem: onRenderItem,
        ListEmptyComponent: EmptyComponent
        // ListHeaderComponent={renderHeaderList}
        ,
        ListFooterComponent: footerListFlight,
        showsVerticalScrollIndicator: false,
        scrollEventThrottle: 16,
        onScroll: handleScroll,
        onEndReachedThreshold: 0.4,
        onEndReached: handleLoadMore,
        maxToRenderPerBatch: 15,
        style: _flightResultStyles.commonComponentStyles.sectionListWrapper,
        windowSize: 15,
        ref: scrollRef,
        refreshControl: (0, _jsxRuntime.jsx)(_reactNative.RefreshControl, {
          refreshing: false,
          onRefresh: function onRefresh() {
            _onRefresh();
            if (isAndroid.value) {
              isRefreshingUseShared.value = true;
              setTimeout(function () {
                isRefreshingUseShared.value = false;
              }, 2000);
            }
          }
        })
      });
    };
    var showToastForRemoveFlight = function showToastForRemoveFlight() {
      return (0, _jsxRuntime.jsx)(_feedbackToast.FeedBackToast, {
        ref: toastForRemoveFlight,
        style: _flightResultStyles.departureStyles.feedBackToastStyle,
        textButtonStyle: _flightResultStyles.departureStyles.toastButtonStyle,
        position: "custom",
        positionValue: {
          bottom: 8
        },
        textStyle: _flightResultStyles.departureStyles.toastTextStyle,
        type: _feedbackToast.FeedBackToastType.smallFeedBack,
        text: (msg50 == null ? undefined : msg50.message) || (0, _i18n.translate)("flyLanding.removeFlight")
      });
    };
    var showFlightAddedFeedBackToastMessage = function showFlightAddedFeedBackToastMessage() {
      return (0, _jsxRuntime.jsx)(_feedbackToast.FeedBackToast, {
        ref: toastForSavedFlight,
        style: _flightResultStyles.departureStyles.feedBackToastStyle,
        textButtonStyle: _flightResultStyles.departureStyles.toastButtonStyle,
        position: "bottom",
        textStyle: _flightResultStyles.departureStyles.toastTextStyle,
        type: _feedbackToast.FeedBackToastType.smallFeedBack,
        text: (0, _i18n.translate)("flightDetails.newPopupConfirmSaveFlight.toastFlightSaved"),
        onCallback: function onCallback() {
          return dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
        }
      });
    };
    if (isShowMaintenance) {
      return (0, _jsxRuntime.jsx)(_errorOverlay.ErrorOverlay, {
        reload: true,
        header: false,
        visible: true,
        onReload: handleRefresh,
        testID: `${SCREEN_NAME}__ErrorComponent`,
        accessibilityLabel: `${SCREEN_NAME}__ErrorComponent`,
        variant: _errorOverlay.ErrorOverlayVariant.VARIANTSECTION,
        overlayStyle: _flightResultStyles.departureStyles.errorOverlayStyle
      });
    }
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_flightPageHeaderV.default, {
        scrollY: scrollY,
        onLayoutHeader: onLayoutHeader,
        animatedOnlyInputStyles: animatedOnlyInputStyles,
        animatedFilterLocationStyles: animatedFilterLocationStyles,
        refreshingAnimated: refreshingAnimated,
        initialFilterLocation: searchFilterLocation,
        filterDate: searchDate || (0, _moment.default)().toString(),
        isAutoFocus: isAutoFocus,
        handleKeySearch: handleKeySearch,
        onSearch: onSearch,
        onLocationFilterPress: onOpenFilterLocation,
        onOpenCalendar: onOpenCalendarModal
      }), (0, _utils.simpleCondition)({
        condition: isNoConnection,
        ifValue: renderNoInternetConnection(),
        elseValue: {
          condition: isNetworkError,
          ifValue: renderNetworkError(),
          elseValue: renderContent()
        }
      }), (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
        style: [_flightResultStyles.departureStyles.fabContainerViewStyle, animatedFabVisibilityStyles],
        children: (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
          style: _flightResultStyles.departureStyles.fabTouchViewStyle,
          onPress: onFABScrollPress,
          testID: "scrollToTopButton",
          accessibilityLabel: "scrollToTopButton",
          children: [(0, _jsxRuntime.jsx)(_icons.ArrowUp, {
            style: _flightResultStyles.departureStyles.fabArrowStyle,
            height: 24,
            width: 24
          }), (0, _jsxRuntime.jsx)(_backgrounds.FabBackground, {})]
        })
      }), showErrorToastMessage(), showErrorFeedBackToastMessage(), showToastForRemoveFlight(), showFlightAddedFeedBackToastMessage(), (0, _jsxRuntime.jsx)(_alertApp.AlertApp, {
        ref: alertApp
      }), renderCalendarModalView(), renderFilterLocation(), renderConfirmSaveFlight(), renderReturnCalendarView(), (0, _jsxRuntime.jsx)(_saveFlightTraveOptionWrap.default, {
        onModalHide: savedFlightTravelOptionsOnModalHide,
        visible: isModalVisibleOption,
        onClosed: onClosedSheet,
        loadingSaveFlight: loadingSaveFlight,
        onBackPressed: onClosedSheet,
        selectedOption: selectedTravelOption,
        savedFlightOnPress: savedFlightOnPress,
        onPress: function onPress(option) {
          return travelOptionTapped(option);
        },
        flightDirection: _flightProps.FlightDirection.departure
      }), isLoadingResult && (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: localStyles.lottieLoadingBackground,
        children: (0, _jsxRuntime.jsx)(_lottieReactNative.default, {
          style: localStyles.lottieLoading,
          source: _loadingAnimation.default,
          autoPlay: true,
          loop: true
        })
      }), (0, _jsxRuntime.jsx)(_subscriptions.FlyListSubscription, {
        direction: _subscriptions.FlySubscriptionDirectionEnum.DEP,
        screen: _subscriptions.FlySubscriptionScreenEnum.result,
        duplicateDirection: flyLandingSelectedTab
      })]
    });
  };
  var localStyles = _reactNative2.StyleSheet.create({
    calendarFilterStyles: {
      borderRadius: 13,
      paddingHorizontal: 8,
      paddingVertical: 12
    },
    calendarPickerContainer: {
      height: "100%",
      justifyContent: "center",
      paddingHorizontal: 24,
      width: "100%"
    },
    lottieLoading: {
      alignItems: "center",
      alignSelf: "center",
      backgroundColor: _color.color.palette.transparent,
      marginLeft: 10,
      width: "70%",
      height: _reactNative2.Dimensions.get("window").height
    },
    lottieLoadingBackground: {
      backgroundColor: _color.color.palette.whiteColorOpacity,
      bottom: 0,
      left: 0,
      position: "absolute",
      right: 0,
      top: 0
    }
  });
