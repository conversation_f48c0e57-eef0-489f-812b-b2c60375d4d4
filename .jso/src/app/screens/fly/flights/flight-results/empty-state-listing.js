  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.textForEmptyList = exports.default = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var _theme = _$$_REQUIRE(_dependencyMap[2]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  var textForEmptyList = exports.textForEmptyList = /*#__PURE__*/function (textForEmptyList) {
    textForEmptyList["noFlightsForFilter"] = "flightLanding.noFlightsForFilter";
    textForEmptyList["noFlightsForSearchResult"] = "flightLanding.noFlightsForSearchResult";
    return textForEmptyList;
  }({});
  var EmptyStateListing = function EmptyStateListing(_ref) {
    var text = _ref.text;
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.emptyView,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "caption1Regular",
          tx: `${text || textForEmptyList.noFlightsForFilter}`
        })
      })
    });
  };
  var styles = _reactNative.StyleSheet.create({
    containerFilter: {
      paddingHorizontal: 24
    },
    dateIconsParentContainerViewStyle: {
      backgroundColor: _theme.color.palette.lightestGrey
    },
    emptyView: {
      justifyContent: "center",
      paddingHorizontal: 24,
      padding: 24
    },
    lastUpdatedTextStyle: {
      color: _theme.color.palette.darkGrey,
      flex: 1,
      marginTop: 7,
      paddingEnd: 2,
      textAlign: "right"
    },
    tapToLoadEarlierContainer: {
      flexDirection: "row",
      marginTop: 16,
      paddingHorizontal: 24
    }
  });
  var _default = exports.default = EmptyStateListing;
