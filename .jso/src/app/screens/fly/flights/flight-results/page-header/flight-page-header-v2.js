  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _native = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _textField = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _i18n = _$$_REQUIRE(_dependencyMap[6]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _adobe = _$$_REQUIRE(_dependencyMap[8]);
  var _theme = _$$_REQUIRE(_dependencyMap[9]);
  var _dateTime = _$$_REQUIRE(_dependencyMap[10]);
  var _icons = _$$_REQUIRE(_dependencyMap[11]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[13]));
  var _lodash = _$$_REQUIRE(_dependencyMap[14]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[15]);
  var _reactNativePermissions = _$$_REQUIRE(_dependencyMap[16]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[17]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[18]);
  var _flightResults = _$$_REQUIRE(_dependencyMap[19]);
  var _flightPageHeaderStyles = _$$_REQUIRE(_dependencyMap[20]);
  var _flyRedux = _$$_REQUIRE(_dependencyMap[21]);
  var _fly = _$$_REQUIRE(_dependencyMap[22]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[23]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _worklet_17421970137611_init_data = {
    code: "function flightPageHeaderV2Tsx1(){const{interpolate,scrollY,Extrapolation}=this.__closure;const scale1=interpolate(scrollY.value,[0,80],[0,-80],{extrapolateRight:Extrapolation.CLAMP});const scale2=interpolate(scrollY.value,[0,40],[0,-40],{extrapolateRight:Extrapolation.CLAMP});if(scrollY.value<0){return{transform:[{translateY:scale2}]};}else{return{transform:[{translateY:scale1}]};}}"
  };
  var _worklet_4629828327216_init_data = {
    code: "function flightPageHeaderV2Tsx2(){const{interpolate,scrollY,Extrapolation}=this.__closure;const scale=interpolate(scrollY.value,[0,50],[1,0],{extrapolateRight:Extrapolation.CLAMP});return{opacity:scale};}"
  };
  var _worklet_14362629198279_init_data = {
    code: "function flightPageHeaderV2Tsx3(){const{interpolate,scrollY,Extrapolation}=this.__closure;const scale1=interpolate(scrollY.value,[0,80],[0,40],{extrapolateRight:Extrapolation.CLAMP});const scale2=interpolate(scrollY.value,[0,0],[0,0],{extrapolateRight:Extrapolation.CLAMP});if(scrollY.value>0){return{transform:[{translateY:scale1}]};}else{return{transform:[{translateY:scale2}]};}}"
  };
  var FlightPageHeader = function FlightPageHeader(props) {
    var navigation = (0, _native.useNavigation)();
    var scrollY = props.scrollY,
      onLayoutHeader = props.onLayoutHeader,
      onSearch = props.onSearch,
      handleKeySearch = props.handleKeySearch,
      initialFilterLocation = props.initialFilterLocation,
      animatedOnlyInputStyles = props.animatedOnlyInputStyles,
      animatedFilterLocationStyles = props.animatedFilterLocationStyles,
      filterDate = props.filterDate,
      onLocationFilterPress = props.onLocationFilterPress,
      refreshingAnimated = props.refreshingAnimated,
      isAutoFocus = props.isAutoFocus,
      onOpenCalendar = props.onOpenCalendar;
    var searchRef = _react.default.useRef(null);
    var context = _react.default.useContext(_flightResults.FlyContext);
    var FLY_CONTEXT_HANDLERS = _react.default.useContext(_fly.FLY_CONTEXT).Handlers;
    var newFilterFlagOn = true;
    (0, _react.useEffect)(function () {
      var _searchRef$current;
      if (isAutoFocus && searchRef && searchRef != null && searchRef.current && !(searchRef != null && (_searchRef$current = searchRef.current) != null && _searchRef$current.isFocused())) {
        _reactNative2.InteractionManager.runAfterInteractions(function () {
          setTimeout(function () {
            var _searchRef$current2;
            return searchRef == null || (_searchRef$current2 = searchRef.current) == null ? undefined : _searchRef$current2.focus();
          }, 100);
        });
      }
    }, [isAutoFocus]);
    var translateHeader = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var flightPageHeaderV2Tsx1 = function flightPageHeaderV2Tsx1() {
        var scale1 = (0, _reactNativeReanimated.interpolate)(scrollY.value, [0, 80], [0, -80], {
          extrapolateRight: _reactNativeReanimated.Extrapolation.CLAMP
        });
        var scale2 = (0, _reactNativeReanimated.interpolate)(scrollY.value, [0, 40], [0, -40], {
          extrapolateRight: _reactNativeReanimated.Extrapolation.CLAMP
        });
        if (scrollY.value < 0) {
          return {
            transform: [{
              translateY: scale2
            }]
          };
        } else {
          return {
            transform: [{
              translateY: scale1
            }]
          };
        }
      };
      flightPageHeaderV2Tsx1.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        scrollY: scrollY,
        Extrapolation: _reactNativeReanimated.Extrapolation
      };
      flightPageHeaderV2Tsx1.__workletHash = 17421970137611;
      flightPageHeaderV2Tsx1.__initData = _worklet_17421970137611_init_data;
      return flightPageHeaderV2Tsx1;
    }());
    var opacityFilter = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var flightPageHeaderV2Tsx2 = function flightPageHeaderV2Tsx2() {
        var scale = (0, _reactNativeReanimated.interpolate)(scrollY.value, [0, 50], [1, 0], {
          extrapolateRight: _reactNativeReanimated.Extrapolation.CLAMP
        });
        return {
          opacity: scale
        };
      };
      flightPageHeaderV2Tsx2.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        scrollY: scrollY,
        Extrapolation: _reactNativeReanimated.Extrapolation
      };
      flightPageHeaderV2Tsx2.__workletHash = 4629828327216;
      flightPageHeaderV2Tsx2.__initData = _worklet_4629828327216_init_data;
      return flightPageHeaderV2Tsx2;
    }());
    var translateScanButton = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var flightPageHeaderV2Tsx3 = function flightPageHeaderV2Tsx3() {
        var scale1 = (0, _reactNativeReanimated.interpolate)(scrollY.value, [0, 80], [0, 40], {
          extrapolateRight: _reactNativeReanimated.Extrapolation.CLAMP
        });
        var scale2 = (0, _reactNativeReanimated.interpolate)(scrollY.value, [0, 0], [0, 0], {
          extrapolateRight: _reactNativeReanimated.Extrapolation.CLAMP
        });
        if (scrollY.value > 0) {
          return {
            transform: [{
              translateY: scale1
            }]
          };
        } else {
          return {
            transform: [{
              translateY: scale2
            }]
          };
        }
      };
      flightPageHeaderV2Tsx3.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        scrollY: scrollY,
        Extrapolation: _reactNativeReanimated.Extrapolation
      };
      flightPageHeaderV2Tsx3.__workletHash = 14362629198279;
      flightPageHeaderV2Tsx3.__initData = _worklet_14362629198279_init_data;
      return flightPageHeaderV2Tsx3;
    }());
    var flightFilterOptions = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flightFilterOptions);
    var flightFilterOptionSelected = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flightFilterOptionSelected);
    var terminalList = (flightFilterOptions == null ? undefined : flightFilterOptions.terminal) || [];
    var dataCommonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getMessagesCommon);
    var msg61 = dataCommonAEM == null ? undefined : dataCommonAEM.find(function (e) {
      return (e == null ? undefined : e.code) === "msg61";
    });
    var msg62 = dataCommonAEM == null ? undefined : dataCommonAEM.find(function (e) {
      return (e == null ? undefined : e.code) === "msg62";
    });
    var isShowBadgeRed = (0, _react.useMemo)(function () {
      var terminal = flightFilterOptionSelected.terminal,
        airline = flightFilterOptionSelected.airline,
        cityAirport = flightFilterOptionSelected.cityAirport;
      {
        return !(0, _lodash.isEmpty)(terminal) && terminal.length < terminalList.length - 1 || airline !== "all" || cityAirport !== "all";
      }
    }, [flightFilterOptionSelected, terminalList, initialFilterLocation, newFilterFlagOn]);
    var Rationale = {
      title: (msg61 == null ? undefined : msg61.title) || (0, _i18n.translate)("requestPermission.camera.title"),
      message: (msg61 == null ? undefined : msg61.message) || (0, _i18n.translate)("requestPermission.camera.message"),
      buttonPositive: (msg61 == null ? undefined : msg61.secondButton) || (0, _i18n.translate)("requestPermission.camera.buttonPositive"),
      buttonNegative: (msg61 == null ? undefined : msg61.firstButton) || (0, _i18n.translate)("requestPermission.camera.buttonNegative")
    };
    var onPressScan = function onPressScan() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlightListingScanBoardingPass, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlightListingScanBoardingPass, "1"));
      (0, _reactNativePermissions.request)(_reactNative2.Platform.OS === "ios" ? _reactNativePermissions.PERMISSIONS.IOS.CAMERA : _reactNativePermissions.PERMISSIONS.ANDROID.CAMERA, Rationale).then(function (result) {
        if (result === _reactNativePermissions.RESULTS.BLOCKED) {
          _reactNative2.Alert.alert((msg62 == null ? undefined : msg62.title) || (0, _i18n.translate)("scanCode.needAccessPermission.title"), (msg62 == null ? undefined : msg62.message) || (0, _i18n.translate)("scanCode.needAccessPermission.description"), [{
            text: (msg62 == null ? undefined : msg62.firstButton) || (0, _i18n.translate)("scanCode.needAccessPermission.firstButton"),
            style: "cancel",
            onPress: function onPress() {
              (0, _reactNativePermissions.openSettings)();
            }
          }, {
            text: (msg62 == null ? undefined : msg62.secondButton) || (0, _i18n.translate)("scanCode.needAccessPermission.secondButton"),
            onPress: function onPress() {
              return null;
            }
          }]);
        } else if (result === _reactNativePermissions.RESULTS.GRANTED) {
          navigation.navigate("scanCode");
        }
      });
    };
    var getDateFilter = function getDateFilter(date) {
      var todayString = (0, _moment.default)().format(_dateTime.DateFormats.YearMonthDay);
      var dateString = (0, _moment.default)(date).format(_dateTime.DateFormats.YearMonthDay);
      var isStartDateToday = (0, _moment.default)(todayString).isSame(dateString);
      if (isStartDateToday) {
        return (0, _i18n.translate)("upcomingEvent.today");
      }
      return (0, _moment.default)(date).format(_dateTime.DateFormats.DayMonthYearWithSlash);
    };
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
        onPress: function onPress() {
          return _reactNative2.Keyboard.dismiss();
        },
        children: (0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
          style: [_flightPageHeaderStyles.styles.container, translateHeader],
          onLayout: onLayoutHeader,
          children: [(0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
            style: [_flightPageHeaderStyles.styles.refreshViewAndroid, refreshingAnimated],
            children: (0, _jsxRuntime.jsx)(_reactNative2.ActivityIndicator, {
              size: "large",
              color: _theme.color.palette.almostBlackGrey
            })
          }), (0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
            style: [opacityFilter, translateScanButton],
            children: [(0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
              style: _flightPageHeaderStyles.styles.scanBoardingPassStyles,
              onPress: onPressScan,
              children: [(0, _jsxRuntime.jsx)(_icons.ScanFlight, {
                style: _flightPageHeaderStyles.styles.scanIconStyles
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                tx: "departureSection.scanButtonText",
                style: _flightPageHeaderStyles.styles.btnScanTextStyles
              })]
            }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: _flightPageHeaderStyles.styles.lineViewStyles
            })]
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _flightPageHeaderStyles.styles.bottomContainer,
            children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _flightPageHeaderStyles.styles.filterContainerStyles,
              children: [(0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
                style: _flightPageHeaderStyles.styles.calendarContainer,
                onPress: onOpenCalendar,
                children: [(0, _jsxRuntime.jsx)(_icons.CalendarSearchFlight, {
                  style: _flightPageHeaderStyles.styles.calendarIconstyle,
                  width: 24,
                  height: 24
                }), (0, _jsxRuntime.jsx)(_text.Text, {
                  text: getDateFilter(filterDate),
                  style: _flightPageHeaderStyles.styles.dateFilterStyles
                })]
              }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: _flightPageHeaderStyles.styles.searchBoxContainer,
                children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                  style: _flightPageHeaderStyles.styles.searchViewAnimationStyle,
                  children: [(0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
                    style: [_flightPageHeaderStyles.styles.contentContainerStyle, animatedOnlyInputStyles],
                    children: (0, _jsxRuntime.jsx)(_textField.TextField, {
                      numberOfLines: 1,
                      inputStyle: _flightPageHeaderStyles.styles.inputFieldSearchStyle,
                      placeholderTextColor: _theme.color.palette.midGrey,
                      secureTextEntry: false,
                      preset: "noMargin",
                      allowFontScaling: false,
                      onBlur: function onBlur() {
                        onSearch(context.keySearchInput);
                      },
                      onChangeText: function onChangeText(e) {
                        handleKeySearch(e);
                      },
                      forwardedRef: searchRef,
                      value: context.keySearchInput,
                      placeholderTx: "departureSection.searchBarText"
                    })
                  }), (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
                    style: [_flightPageHeaderStyles.styles.locationIconViewStyles, animatedFilterLocationStyles],
                    children: (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
                      style: _flightPageHeaderStyles.styles.iconViewStyles,
                      onPress: onLocationFilterPress,
                      children: [isShowBadgeRed && (0, _jsxRuntime.jsx)(_icons.Badge, {
                        style: _flightPageHeaderStyles.styles.badgeViewStyle,
                        height: 8,
                        width: 8
                      }), (0, _jsxRuntime.jsx)(_icons.Filter, {
                        color: _theme.color.palette.lightPurple,
                        height: 20,
                        width: 20
                      })]
                    })
                  })]
                })
              })]
            })
          })]
        })
      })
    });
  };
  var _default = exports.default = FlightPageHeader;
