  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _responsive = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var styles = exports.styles = _reactNative.StyleSheet.create({
    badgeViewStyle: {
      alignSelf: "flex-end",
      position: "absolute",
      right: -3,
      top: -3,
      zIndex: 1
    },
    bottomContainer: {
      backgroundColor: _theme.color.palette.lightestGrey,
      paddingHorizontal: 26,
      paddingVertical: 16
    },
    btnScanTextStyles: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.lightPurple,
      lineHeight: _responsive.default.getFontSize(22)
    }),
    calendarContainer: {
      borderRightColor: _theme.color.palette.lightGrey,
      borderRightWidth: 0.5,
      flex: 0.89,
      flexDirection: "row",
      paddingHorizontal: 16,
      alignItems: "center"
    },
    calendarFilterStyles: {
      borderRadius: 13,
      paddingHorizontal: 8,
      paddingVertical: 12
    },
    calendarIconstyle: {
      marginRight: 10
    },
    calendarPickerContainer: {
      height: "100%",
      justifyContent: "center",
      paddingHorizontal: 24,
      width: "100%"
    },
    container: {
      backgroundColor: _theme.color.palette.lightestGrey,
      position: "absolute",
      width: "100%",
      zIndex: 1
    },
    contentContainerStyle: {
      flex: 1
    },
    dateFilterStyles: Object.assign({}, _text.presets.bodyTextBlackRegular, {
      lineHeight: 20
    }),
    filterContainerStyles: {
      alignContent: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      borderColor: _theme.color.palette.lightGrey,
      borderRadius: 12,
      borderWidth: 1,
      flexDirection: "row",
      height: 44,
      justifyContent: "center"
    },
    iconViewStyles: {
      padding: 2
    },
    inputFieldSearchStyle: {
      backgroundColor: _theme.color.palette.transparent,
      flex: 1,
      paddingHorizontal: 10
    },
    lineViewStyles: {
      backgroundColor: _theme.color.palette.lighterGrey,
      height: 1,
      width: "100%"
    },
    locationIconViewStyles: {
      alignItems: "center",
      borderLeftColor: _theme.color.palette.lightGrey,
      borderLeftWidth: 1,
      height: 44,
      justifyContent: "center",
      position: 'absolute',
      right: 0,
      width: 48
    },
    modalContainer: {
      margin: 0
    },
    refreshViewAndroid: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.lighterGrey,
      borderRadius: 25,
      height: 50,
      justifyContent: "center",
      left: _reactNative.Dimensions.get("screen").width / 2 - 25,
      position: "absolute",
      right: _reactNative.Dimensions.get("screen").width / 2 - 25,
      top: 4,
      width: 50,
      zIndex: 999
    },
    scanBoardingPassStyles: {
      alignContent: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      borderColor: _theme.color.palette.lightGrey,
      borderRadius: 12,
      borderWidth: 1,
      flexDirection: "row",
      justifyContent: "center",
      marginBottom: 16,
      marginHorizontal: 26,
      marginTop: 20,
      paddingVertical: 11
    },
    scanIconStyles: {
      marginRight: 8,
      tintColor: _theme.color.palette.lightPurple
    },
    searchBoxContainer: {
      alignItems: "center",
      borderLeftColor: _theme.color.palette.lightGrey,
      borderLeftWidth: 0.5,
      flex: 1.11,
      justifyContent: "center",
      paddingLeft: 16
    },
    searchViewAnimationStyle: {
      bottom: 0,
      flexDirection: "row",
      left: 0,
      position: "absolute",
      right: 0,
      top: 0
    }
  });
