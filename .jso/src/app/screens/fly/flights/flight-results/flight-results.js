  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.tabs = exports.FlyContext = exports.FlightResultLandingScreen = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _materialTopTabs = _$$_REQUIRE(_dependencyMap[6]);
  var _departureResult = _$$_REQUIRE(_dependencyMap[7]);
  var _arrivalResult = _$$_REQUIRE(_dependencyMap[8]);
  var _translate = _$$_REQUIRE(_dependencyMap[9]);
  var _stack = _$$_REQUIRE(_dependencyMap[10]);
  var _navigationUtilities = _$$_REQUIRE(_dependencyMap[11]);
  var _flightProps = _$$_REQUIRE(_dependencyMap[12]);
  var _theme = _$$_REQUIRE(_dependencyMap[13]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[14]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[15]);
  var _flyRedux = _$$_REQUIRE(_dependencyMap[16]);
  var _lodash = _$$_REQUIRE(_dependencyMap[17]);
  var _constants = _$$_REQUIRE(_dependencyMap[18]);
  var _tickerBand = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[19]));
  var _tickerBand2 = _$$_REQUIRE(_dependencyMap[20]);
  var _adobe = _$$_REQUIRE(_dependencyMap[21]);
  var _utils = _$$_REQUIRE(_dependencyMap[22]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[23]);
  var _errorUnplannedMaintenance = _$$_REQUIRE(_dependencyMap[24]);
  var _icons = _$$_REQUIRE(_dependencyMap[25]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[26]));
  var _useTickerbandMaintanence = _$$_REQUIRE(_dependencyMap[27]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[28]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var screenOptions = {
    headerShown: false
  };
  var SCREEN_NAME = "FlightListingScreen";
  var topTabParentWithTickerBandStyle = {
    height: 50,
    paddingTop: 15,
    backgroundColor: _theme.color.palette.whiteGrey
  };
  var topTabParentStyle = Object.assign({
    height: 30
  }, _theme.shadow.primaryShadow, {
    backgroundColor: _theme.color.palette.whiteGrey
  });
  var topTabTouchableOpacityStyle = {
    alignItems: "center",
    marginEnd: 24
  };
  var topTabLabelStyle = _reactNative.StyleSheet.create({
    active: {
      color: _theme.color.palette.lightPurple,
      flex: 1
    },
    inActive: {
      color: _theme.color.palette.darkestGrey,
      flex: 1
    }
  });
  var Stack = (0, _stack.createStackNavigator)();
  var Tab = (0, _materialTopTabs.createMaterialTopTabNavigator)();
  var FlyContext = exports.FlyContext = _react.default.createContext({
    screen: "",
    isTapToLoadEarlierClicked: false,
    pageInfo: {},
    selectedDate: {},
    isLoadFlightAfter24h: false,
    refForIsLoadFlightAfter24hValueV2: {
      current: false
    },
    keySearchInput: "",
    setKeySearchInput: function setKeySearchInput(_e) {
      // do nothing.
    },
    searchDate: "",
    setSearchDate: function setSearchDate(_date) {
      // do nothing.
    },
    searchFilterLocation: [],
    setSearchFilterLocation: function setSearchFilterLocation(_locations) {
      // do nothing.
    },
    searchFilterAirline: "",
    setSearchFilterAirline: function setSearchFilterAirline(_airline) {
      // do nothing.
    },
    searchFilterCityAirport: "",
    setSearchFilterCityAirport: function setSearchFilterCityAirport(_cityAirport) {
      // do nothing.
    }
  });
  var FlightResultLandingScreen = exports.FlightResultLandingScreen = function FlightResultLandingScreen(_ref) {
    var _route$params4, _route$params5, _route$params6, _route$params7, _route$params8, _route$params9, _route$params0, _route$params14, _route$params15;
    var route = _ref.route;
    var intialDate = function intialDate() {
      var _route$params, _route$params2, _route$params3;
      var selectedDate = (route == null || (_route$params = route.params) == null || (_route$params = _route$params.query) == null || (_route$params = _route$params[_flightProps.FlightDirection.departure]) == null ? undefined : _route$params.selectedDate) || (route == null || (_route$params2 = route.params) == null || (_route$params2 = _route$params2.query) == null || (_route$params2 = _route$params2[_flightProps.FlightDirection.arrival]) == null ? undefined : _route$params2.selectedDate) || (route == null || (_route$params3 = route.params) == null ? undefined : _route$params3.selectedDate) ||
      // selected since search date is shared between departure and arrival in new design
      "";
      if (selectedDate === "") {
        return "";
      } else {
        return selectedDate || (0, _moment.default)().format("YYYY-MM-DD");
      }
    };
    var _React$useState = _react.default.useState((route == null || (_route$params4 = route.params) == null ? undefined : _route$params4.country) || ""),
      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),
      keySearchInput = _React$useState2[0],
      setKeySearchInput = _React$useState2[1];
    var _React$useState3 = _react.default.useState(intialDate()),
      _React$useState4 = (0, _slicedToArray2.default)(_React$useState3, 2),
      searchDate = _React$useState4[0],
      setSearchDate = _React$useState4[1];
    var _React$useState5 = _react.default.useState((route == null || (_route$params5 = route.params) == null || (_route$params5 = _route$params5.query) == null || (_route$params5 = _route$params5[_flightProps.FlightDirection.departure]) == null ? undefined : _route$params5.filterLocation) || (route == null || (_route$params6 = route.params) == null || (_route$params6 = _route$params6.query) == null || (_route$params6 = _route$params6[_flightProps.FlightDirection.arrival]) == null ? undefined : _route$params6.filterLocation) || []),
      _React$useState6 = (0, _slicedToArray2.default)(_React$useState5, 2),
      searchFilterLocation = _React$useState6[0],
      setSearchFilterLocation = _React$useState6[1];
    var _React$useState7 = _react.default.useState((route == null || (_route$params7 = route.params) == null || (_route$params7 = _route$params7.query) == null || (_route$params7 = _route$params7[_flightProps.FlightDirection.departure]) == null ? undefined : _route$params7.airline) || (route == null || (_route$params8 = route.params) == null || (_route$params8 = _route$params8.query) == null || (_route$params8 = _route$params8[_flightProps.FlightDirection.arrival]) == null ? undefined : _route$params8.airline) || ""),
      _React$useState8 = (0, _slicedToArray2.default)(_React$useState7, 2),
      searchFilterAirline = _React$useState8[0],
      setSearchFilterAirline = _React$useState8[1];
    var _React$useState9 = _react.default.useState((route == null || (_route$params9 = route.params) == null || (_route$params9 = _route$params9.query) == null || (_route$params9 = _route$params9[_flightProps.FlightDirection.departure]) == null ? undefined : _route$params9.cityAirport) || (route == null || (_route$params0 = route.params) == null || (_route$params0 = _route$params0.query) == null || (_route$params0 = _route$params0[_flightProps.FlightDirection.arrival]) == null ? undefined : _route$params0.cityAirport) || ""),
      _React$useState0 = (0, _slicedToArray2.default)(_React$useState9, 2),
      searchFilterCityAirport = _React$useState0[0],
      setSearchFilterCityAirport = _React$useState0[1];
    var _route$params1 = route == null ? undefined : route.params,
      isLoadFlightAfter24h = _route$params1.isLoadFlightAfter24h;
    var refForIsLoadFlightAfter24hValueV2 = (0, _react.useRef)(isLoadFlightAfter24h);
    (0, _react.useEffect)(function () {
      var _route$params10, _route$params12;
      if (route != null && (_route$params10 = route.params) != null && (_route$params10 = _route$params10.params) != null && _route$params10.country) {
        var _route$params11;
        setKeySearchInput(route == null || (_route$params11 = route.params) == null ? undefined : _route$params11.country);
      }
      if (route != null && (_route$params12 = route.params) != null && (_route$params12 = _route$params12.params) != null && _route$params12.selectedDate) {
        var _route$params13;
        setSearchDate(route == null || (_route$params13 = route.params) == null ? undefined : _route$params13.selectedDate);
      }
    }, [route == null || (_route$params14 = route.params) == null ? undefined : _route$params14.country, route == null || (_route$params15 = route.params) == null ? undefined : _route$params15.selectedDate]);
    return (0, _jsxRuntime.jsx)(FlyContext.Provider, {
      value: Object.assign({}, route.params, {
        keySearchInput: keySearchInput,
        refForIsLoadFlightAfter24hValueV2: refForIsLoadFlightAfter24hValueV2,
        setKeySearchInput: setKeySearchInput,
        searchDate: searchDate,
        setSearchDate: setSearchDate,
        searchFilterLocation: searchFilterLocation,
        setSearchFilterLocation: setSearchFilterLocation,
        searchFilterAirline: searchFilterAirline,
        setSearchFilterAirline: setSearchFilterAirline,
        searchFilterCityAirport: searchFilterCityAirport,
        setSearchFilterCityAirport: setSearchFilterCityAirport
      }),
      children: (0, _jsxRuntime.jsx)(Stack.Navigator, {
        initialRouteName: "Tabs",
        screenOptions: screenOptions,
        children: (0, _jsxRuntime.jsx)(Stack.Screen, {
          name: "Tabs",
          component: tabs
        })
      })
    });
  };
  var tabs = exports.tabs = function tabs() {
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("FLIGHT_RESULT"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var flyContext = _react.default.useContext(FlyContext);
    var flyShowTickerBand = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flyShowTickerBand);
    var isEndEarlierFlight = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.isEndEarlierFlight);
    var isEndLoadMoreFlight = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.isEndLoadMoreFlight);
    var _useTickerbandMaintan = (0, _useTickerbandMaintanence.useTickerbandMaintanance)(_useTickerbandMaintanence.MaintananceTickerBandType.FLY_RESULTS),
      isShowMaintenance = _useTickerbandMaintan.isShowMaintenance,
      isShowTickerband = _useTickerbandMaintan.isShowTickerband,
      onPressCTA = _useTickerbandMaintan.onPressCTA,
      onCloseTickerBand = _useTickerbandMaintan.onCloseTickerBand,
      errorData = _useTickerbandMaintan.errorData,
      tickerBand = _useTickerbandMaintan.tickerBand,
      tickerBandDescription = _useTickerbandMaintan.tickerBandDescription,
      tickerBandButtonText = _useTickerbandMaintan.tickerBandButtonText,
      fetchTickerbandMaintanance = _useTickerbandMaintan.fetchTickerbandMaintanance;
    var dataErrorMaintenanceConfig = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.GET_ERROR_MAINTENANCE));
    var listErrorMaintenance = (0, _lodash.get)(dataErrorMaintenanceConfig, "data.list", []);
    var screenMaintenanceObj = (0, _react.useMemo)(function () {
      var data = listErrorMaintenance == null ? undefined : listErrorMaintenance.find(function (obj) {
        return obj.screenName === _constants.screenTagName.flightsListing;
      });
      return data || {};
    }, [listErrorMaintenance]);
    var DepartureResultScreenRoot = _departureResult.DepartureScreen;
    var ArrivalResultScreenRoot = _arrivalResult.ArrivalScreen;
    var isShowNewDesign = true;
    (0, _react.useEffect)(function () {
      isEndEarlierFlight && dispatch(_flyRedux.FlyCreators.resetCheckEndEarlierFlight());
      isEndLoadMoreFlight && dispatch(_flyRedux.FlyCreators.resetCheckLoadMoreEarlierFlight());
      return function () {
        dispatch(_flyRedux.FlyCreators.setFilterDateArrival(null));
        dispatch(_flyRedux.FlyCreators.setFilterDateDeparture(null));
      };
    }, []);
    (0, _react.useEffect)(function () {
      return function () {
        var flightFilterOptionSelected = {
          terminal: [],
          airline: "all",
          cityAirport: "all"
        };
        dispatch(_flyRedux.FlyCreators.setFlightFilterOption(flightFilterOptionSelected));
        dispatch(_flyRedux.FlyCreators.setFlightSearchDate());
        dispatch(_flyRedux.FlyCreators.setFlightSearchTerminal());
        dispatch(_flyRedux.FlyCreators.setFlightSearchKeyWord());
      };
    }, []);
    var handleButtonOnPress = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* (navigationObj, redirect) {
        var type = navigationObj.type,
          value = navigationObj.value;
        if (!type || !value) return;
        handleNavigation(type, value, redirect);
      });
      return function handleButtonOnPress(_x, _x2) {
        return _ref2.apply(this, arguments);
      };
    }();
    if ((0, _utils.ifAllTrue)([!(0, _lodash.isEmpty)(screenMaintenanceObj), screenMaintenanceObj == null ? undefined : screenMaintenanceObj.enableMode])) {
      return (0, _jsxRuntime.jsx)(_errorUnplannedMaintenance.ErrorUnplannedMaintenance, {
        header: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.header,
        subHeader: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.subHeader,
        icon: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.icon,
        buttonLabel: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.buttonLabel,
        buttonLabel2: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.buttonLabel2,
        onFirstButtonPress: function onFirstButtonPress() {
          return handleButtonOnPress(screenMaintenanceObj == null ? undefined : screenMaintenanceObj.navigationFirst, screenMaintenanceObj == null ? undefined : screenMaintenanceObj.redirectFirst);
        },
        onSecondButtonPress: function onSecondButtonPress() {
          return handleButtonOnPress(screenMaintenanceObj == null ? undefined : screenMaintenanceObj.navigationSecond, screenMaintenanceObj == null ? undefined : screenMaintenanceObj.redirectSecond);
        },
        testID: `${SCREEN_NAME}__ErrorUnplannedMaintenance`,
        accessibilityLabel: `${SCREEN_NAME}__ErrorUnplannedMaintenance`
      });
    }
    var renderTopTabByFlag = function renderTopTabByFlag(props) {
      {
        return (0, _jsxRuntime.jsx)(_navigationUtilities.TopTabNavBarFlightListing, {
          props: Object.assign({}, props),
          topTabLabelsPresets: "tabsSmall",
          topTabParentStyle: (0, _utils.simpleCondition)({
            condition: flyShowTickerBand === _tickerBand2.TickerBandStatus.NEED_SHOW,
            ifValue: topTabParentWithTickerBandStyle,
            elseValue: topTabParentStyle
          }),
          topTabTouchableOpacityStyle: topTabTouchableOpacityStyle,
          isCenter: true
        });
      }
    };
    var renderTabNavigation = function renderTabNavigation() {
      var initialRouteName = (0, _utils.handleCondition)(flyContext.screen === _flightProps.FlightDirection.departure, "departureResultScreen", "arrivalResultScreen");
      var commonTabProps = {
        tabBar: function tabBar(props) {
          return renderTopTabByFlag(props);
        },
        initialRouteName: initialRouteName
      };
      var arrivalTab = (0, _jsxRuntime.jsx)(Tab.Screen, {
        options: {
          lazy: true,
          swipeEnabled: false,
          title: (0, _translate.translate)("flightLanding.arrivalTabTitle"),
          tabBarIcon: _icons.FlightArrivalIcon
        },
        name: "arrivalResultScreen",
        component: ArrivalResultScreenRoot,
        listeners: {
          tabPress: function tabPress() {
            (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlightListingDepArrToggle, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlightListingDepArrToggle, "Arr"));
          }
        }
      });
      var departureTab = (0, _jsxRuntime.jsx)(Tab.Screen, {
        options: {
          lazy: true,
          swipeEnabled: false,
          title: (0, _translate.translate)("flightLanding.departureTabTitle"),
          tabBarIcon: _icons.FlightDepartureIcon
        },
        name: "departureResultScreen",
        component: DepartureResultScreenRoot,
        listeners: {
          tabPress: function tabPress() {
            (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlightListingDepArrToggle, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlightListingDepArrToggle, "Dep"));
          }
        }
      });
      return (0, _utils.simpleCondition)({
        condition: isShowNewDesign,
        ifValue: (0, _jsxRuntime.jsxs)(Tab.Navigator, Object.assign({}, commonTabProps, {
          children: [arrivalTab, departureTab]
        })),
        elseValue: (0, _jsxRuntime.jsxs)(Tab.Navigator, Object.assign({}, commonTabProps, {
          children: [departureTab, arrivalTab]
        }))
      });
    };
    return (0, _jsxRuntime.jsxs)(_react.Fragment, {
      children: [isShowTickerband && (0, _jsxRuntime.jsx)(_tickerBand.default, {
        urgent: false,
        title: flyShowTickerBand && tickerBand,
        description: flyShowTickerBand && tickerBandDescription,
        buttonText: flyShowTickerBand && tickerBandButtonText,
        onCTAPress: onPressCTA,
        onClose: onCloseTickerBand,
        isLanding: false
      }), renderTabNavigation()]
    });
  };
