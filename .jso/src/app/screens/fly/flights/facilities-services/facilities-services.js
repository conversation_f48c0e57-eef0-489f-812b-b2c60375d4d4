  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _attractionsFacilitiesServices = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _exploreItemType = _$$_REQUIRE(_dependencyMap[6]);
  var _button = _$$_REQUIRE(_dependencyMap[7]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _aemRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[9]));
  var _utils = _$$_REQUIRE(_dependencyMap[10]);
  var _native = _$$_REQUIRE(_dependencyMap[11]);
  var _flightProps = _$$_REQUIRE(_dependencyMap[12]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[13]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[14]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var filterDataFacilitiesServices = function filterDataFacilitiesServices(listDataTranform, customerEligibility) {
    if ((listDataTranform == null ? undefined : listDataTranform.length) > 0) {
      var _ref;
      // Filter Facilities Services by customerEligibility
      var fSByCustomerEligibility = listDataTranform == null ? undefined : listDataTranform.reduce(function (preValue, currentValue) {
        var _currentValue$custome;
        var customerEligibilityOfTagName = currentValue == null || (_currentValue$custome = currentValue.customerEligibility) == null ? undefined : _currentValue$custome.map(function (cusEligibility) {
          return cusEligibility == null ? undefined : cusEligibility.tagName;
        });
        if (customerEligibilityOfTagName != null && customerEligibilityOfTagName.includes(customerEligibility)) {
          return [].concat((0, _toConsumableArray2.default)(preValue), [currentValue]);
        }
        return preValue;
      }, []);
      var fSByCustomerEligibilityEqualAll = [];
      if ((fSByCustomerEligibility == null ? undefined : fSByCustomerEligibility.length) < 6) {
        // List Facilities Services of contentId
        var listFSOfContentID = fSByCustomerEligibility == null ? undefined : fSByCustomerEligibility.map(function (fS) {
          return fS == null ? undefined : fS.contentId;
        });
        // Filter Facilities Services by customerEligibility includes customerEligibility=all
        fSByCustomerEligibilityEqualAll = listDataTranform == null ? undefined : listDataTranform.reduce(function (preValue, currentValue) {
          var _currentValue$custome2;
          var customerEligibilityOfTagName = currentValue == null || (_currentValue$custome2 = currentValue.customerEligibility) == null ? undefined : _currentValue$custome2.map(function (cusEligibility) {
            return cusEligibility == null ? undefined : cusEligibility.tagName;
          });
          if (!(listFSOfContentID != null && listFSOfContentID.includes(currentValue == null ? undefined : currentValue.contentId)) && customerEligibilityOfTagName != null && customerEligibilityOfTagName.includes(_flightProps.CustomerEligibility.All)) {
            return [].concat((0, _toConsumableArray2.default)(preValue), [currentValue]);
          }
          return preValue;
        }, []);
      }
      return (_ref = [].concat((0, _toConsumableArray2.default)(fSByCustomerEligibility), (0, _toConsumableArray2.default)(fSByCustomerEligibilityEqualAll))) == null ? undefined : _ref.slice(0, 6);
    }
    return listDataTranform;
  };
  var textBottomListButton = {
    fontSize: 16,
    lineHeight: 22
  };
  var styleBottomListButton = {
    height: 22
  };
  var FacilitiesAndServices = function FacilitiesAndServices(props) {
    var dispatch = (0, _reactRedux.useDispatch)();
    var _props$testID = props.testID,
      testID = _props$testID === undefined ? "ListTravelling" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "ListTravelling" : _props$accessibilityL,
      _props$customerEligib = props.customerEligibility,
      customerEligibility = _props$customerEligib === undefined ? _flightProps.CustomerEligibility.Flying : _props$customerEligib,
      onPressItem = props.onPressItem,
      onPressAll = props.onPressAll,
      needSeparator = props.needSeparator;
    var facilitiesServicesState = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.FACILITIES_SERVICES));
    var isFocused = (0, _native.useIsFocused)();
    var navigation = (0, _native.useNavigation)();
    var facilitiesServicesData = (0, _react.useMemo)(function () {
      return filterDataFacilitiesServices(facilitiesServicesState == null ? undefined : facilitiesServicesState.data, customerEligibility);
    }, [customerEligibility, facilitiesServicesState]);
    var loading = facilitiesServicesState == null ? undefined : facilitiesServicesState.loading;
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("FLIGHT_LANDING_FACILITIES_SERVICES"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var bottomList = function bottomList() {
      return (0, _jsxRuntime.jsx)(_button.Button, {
        textStyle: textBottomListButton,
        style: styleBottomListButton,
        tx: "flightLanding.viewAll",
        testID: `${testID}__ButtonViewAllFacilitiesAndServices`,
        accessibilityLabel: `${accessibilityLabel}__ButtonViewAllFacilitiesAndServices`,
        onPress: function onPress() {
          if (onPressAll) {
            onPressAll();
          }
          navigation.navigate("fly", {
            screen: "airport",
            params: {
              moveToElement: "facilitiesServices",
              rand: Math.random()
            }
          });
        }
      });
    };
    var callBackAfterSuccess = function callBackAfterSuccess(data) {
      var _data$list;
      // Tranform data
      var listDataTranform = data == null || (_data$list = data.list) == null ? undefined : _data$list.map(function (element) {
        return Object.assign({}, element, {
          locationText: element == null ? undefined : element.locationDisplayText,
          image_url: (0, _utils.mappingUrlAem)(element == null ? undefined : element.image)
        });
      });
      return listDataTranform;
    };
    var onPress = function onPress(navigate) {
      if (!navigate) {
        return;
      }
      var _ref2 = (navigate == null ? undefined : navigate.navigation) || "",
        type = _ref2.type,
        value = _ref2.value;
      var _ref3 = navigate || {},
        redirect = _ref3.redirect;
      handleNavigation(type, value, redirect);
    };
    (0, _react.useEffect)(function () {
      if (isFocused) {
        dispatch(_aemRedux.default.getAemConfigData({
          name: _aemRedux.AEM_PAGE_NAME.FACILITIES_SERVICES,
          pathName: "getFacilitiesServices",
          callBackAfterSuccess: callBackAfterSuccess
        }));
      }
    }, [isFocused]);
    (0, _react.useEffect)(function () {
      var unsubscribe = navigation.addListener("blur", function () {
        dispatch(_aemRedux.default.clearAemConfigData(_aemRedux.AEM_PAGE_NAME.FACILITIES_SERVICES));
      });
      return unsubscribe;
    }, [navigation]);
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: needSeparator ? styles.containerWithSeparator : styles.container,
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        tx: "flightLanding.facilitiesAndServices",
        preset: "h4",
        style: styles.textTitle
      }), (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
        data: !loading ? facilitiesServicesData : [1, 2, 3, 4, 5, 6],
        renderItem: function renderItem(_ref4) {
          var item = _ref4.item,
            index = _ref4.index;
          return (0, _jsxRuntime.jsx)(_attractionsFacilitiesServices.AttractionsFacilitiesServices, {
            type: loading ? _exploreItemType.ExploreItemTypeEnum.loading : _exploreItemType.ExploreItemTypeEnum.default,
            title: item == null ? undefined : item.title,
            imageUrl: item == null ? undefined : item.image_url,
            attractionId: undefined,
            locationDisplayText: item == null ? undefined : item.locationText,
            onPressed: function onPressed() {
              if (onPressItem) {
                onPressItem(item);
              }
              onPress(item);
            },
            testID: `${testID}__AttractionsFacilitiesServices__${index}`,
            accessibilityLabel: `${accessibilityLabel}__AttractionsFacilitiesServices__${index}`
          });
        },
        keyExtractor: function keyExtractor(_, index) {
          return `key_travelling_item_${index}`;
        },
        numColumns: 2,
        style: styles.flatListStyle,
        contentContainerStyle: styles.contentContainerStyle,
        scrollEnabled: false,
        ListFooterComponent: function ListFooterComponent() {
          return bottomList();
        },
        ListFooterComponentStyle: styles.footerComponentStyle,
        testID: `${testID}__FlatListFacilitiesAndServices`,
        accessibilityLabel: `${accessibilityLabel}__FlatListFacilitiesAndServices`
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    container: {},
    containerWithSeparator: {
      marginBottom: 50
    },
    contentContainerStyle: {
      marginHorizontal: 16,
      paddingBottom: 20
    },
    flatListStyle: {
      paddingBottom: 20,
      width: "100%"
    },
    footerComponentStyle: {
      marginTop: 24
    },
    textTitle: {
      marginBottom: 4,
      paddingLeft: 24
    }
  });
  var _default = exports.default = FacilitiesAndServices;
