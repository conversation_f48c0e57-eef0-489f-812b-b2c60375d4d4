  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _flyRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _native = _$$_REQUIRE(_dependencyMap[9]);
  var _button = _$$_REQUIRE(_dependencyMap[10]);
  var _dateTime = _$$_REQUIRE(_dependencyMap[11]);
  var _flightProps = _$$_REQUIRE(_dependencyMap[12]);
  var _errorOverlay = _$$_REQUIRE(_dependencyMap[13]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[14]);
  var _constants = _$$_REQUIRE(_dependencyMap[15]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[16]);
  var _i18n = _$$_REQUIRE(_dependencyMap[17]);
  var _envParams = _$$_REQUIRE(_dependencyMap[18]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[19]);
  var _filterFlight = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[20]));
  var _lodash = _$$_REQUIRE(_dependencyMap[21]);
  var _adobe = _$$_REQUIRE(_dependencyMap[22]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[23]));
  var _storage = _$$_REQUIRE(_dependencyMap[24]);
  var _storageKey = _$$_REQUIRE(_dependencyMap[25]);
  var _alertApp = _$$_REQUIRE(_dependencyMap[26]);
  var _alertApp2 = _$$_REQUIRE(_dependencyMap[27]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[28]));
  var _errorUnplannedMaintenance = _$$_REQUIRE(_dependencyMap[29]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[30]);
  var _text = _$$_REQUIRE(_dependencyMap[31]);
  var _footerLoading = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[32]));
  var _flightFilterPill = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[33]));
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[34]);
  var _fly = _$$_REQUIRE(_dependencyMap[35]);
  var _flightInformationDisclaimer = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[36]));
  var _screenHelper = _$$_REQUIRE(_dependencyMap[37]);
  var _subscriptions = _$$_REQUIRE(_dependencyMap[38]);
  var _useTickerbandMaintanence = _$$_REQUIRE(_dependencyMap[39]);
  var _flightListingCard = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[40]));
  var _flightsEmptyState = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[41]));
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[42]);
  var _panResponder = _$$_REQUIRE(_dependencyMap[43]);
  var _errorCloud = _$$_REQUIRE(_dependencyMap[44]);
  var _mytravelRedux = _$$_REQUIRE(_dependencyMap[45]);
  var _firebase = _$$_REQUIRE(_dependencyMap[46]);
  var _flightDetail = _$$_REQUIRE(_dependencyMap[47]);
  var _saveFlightTraveOptionWrap = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[48]));
  var _useModal2 = _$$_REQUIRE(_dependencyMap[49]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[50]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var COMPONENT_NAME = "Flight__DepartureLanding";
  var styles = _reactNative.StyleSheet.create({
    containerFilterStyle: {
      backgroundColor: _theme.color.palette.lightestGrey,
      marginTop: 0,
      paddingTop: 14
    },
    dateTextStyle: {
      paddingHorizontal: 24,
      marginBottom: 10,
      paddingTop: 12
    },
    dateTextStyleWhenNoLoading: {
      paddingTop: 28
    },
    dotsContainer: {
      height: 90,
      marginTop: -14
    },
    errorComponentContainerStyle: {
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1
    },
    errorOverlayStyle: {
      marginVertical: 40
    },
    errorUnplannedMaintenance: {
      marginTop: 30
    },
    flightListingCardSectionStyle: {},
    landingFilterContainer: {
      alignItems: "flex-end",
      flex: 1
    },
    lastUpdatedTextStyle: {
      color: _theme.color.palette.darkGrey999,
      flex: 1,
      marginTop: 7,
      paddingEnd: 2
    },
    loadingHeaderContainerStyle: {
      paddingVertical: 10
    },
    noEarlierFlights: {
      paddingLeft: 24,
      paddingTop: 40
    },
    noInternetOverlayStyle: {
      marginBottom: 50,
      marginTop: 50
    },
    noMoreFlights: {
      alignItems: "center",
      flex: 1,
      paddingBottom: 40,
      paddingTop: 20
    },
    parentContainerStyle: {
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1
    },
    tapToLoadEarlierButtonStyle: {
      backgroundColor: _theme.color.transparent
    },
    tapToLoadEarlierContainer: {
      flexDirection: "row",
      marginTop: 16,
      paddingHorizontal: 24
    },
    maintenanceErrorContainer: {
      flex: 1,
      alignItems: "center",
      marginTop: 16
    },
    renderItemViewStyles: {
      paddingBottom: 12,
      alignItems: "center"
    },
    renderEmptyStateView: {
      paddingHorizontal: 24,
      paddingTop: 6
    },
    emptyTitleFirstDate: {
      height: 10
    }
  });
  var DepartureLandingScreen = function DepartureLandingScreen(_ref) {
    var _dataCommonAEM$data, _dataCommonAEM$data2, _flyPayload$data4, _flyPayload$data5;
    var onLayoutTab = _ref.onLayoutTab,
      showUnableMessage = _ref.showUnableMessage,
      hideUnableMessage = _ref.hideUnableMessage,
      pullToRefreshTimeStamp = _ref.pullToRefreshTimeStamp,
      loadingLoadMoreLocal = _ref.loadingLoadMoreLocal,
      hideLoadingLoadMore = _ref.hideLoadingLoadMore;
    var isFocused = (0, _native.useIsFocused)();
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("DEPARTURE_LANDING"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var navigation = (0, _native.useNavigation)();
    var route = (0, _native.useRoute)();
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var flyPayload = (0, _reactRedux.useSelector)(function (state) {
      return _flyRedux.FlySelectors.flyDepartureLanding(state);
    });
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var flyLastUpdatedTimeStamp = (0, _reactRedux.useSelector)(function (state) {
      return _flyRedux.FlySelectors.flyLastUpdatedTimeStamp(state);
    });
    var myTravelFlightsPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.myTravelFlightsPayload);
    var departureLandingLoading = (0, _reactRedux.useSelector)(function (state) {
      return _flyRedux.FlySelectors.departureLandingLoading(state);
    });
    var departureLandingEarlierLoading = (0, _reactRedux.useSelector)(function (state) {
      return _flyRedux.FlySelectors.departureLandingEarlierLoading(state);
    });
    var isEndEarlierFlightLanding = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.isEndEarlierFlightLanding);
    var dataErrorMaintenanceConfig = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.GET_ERROR_MAINTENANCE));
    var listErrorMaintenance = (0, _lodash.get)(dataErrorMaintenanceConfig, "data.list", []);
    var sectionMaintenanceObj = (0, _react.useMemo)(function () {
      var data = listErrorMaintenance == null ? undefined : listErrorMaintenance.find(function (obj) {
        return obj.sectionName === _constants.sectionTagName.departure;
      });
      return data || {};
    }, [listErrorMaintenance]);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isNoConnection = _useState2[0],
      setNoConnection = _useState2[1];
    var alertApp = (0, _react.useRef)(null);
    var viewRef = (0, _react.useRef)(null);
    var dataCommonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig("AEM_COMMON_DATA"));
    var msg58 = dataCommonAEM == null || (_dataCommonAEM$data = dataCommonAEM.data) == null || (_dataCommonAEM$data = _dataCommonAEM$data.messages) == null ? undefined : _dataCommonAEM$data.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG58";
    });
    var msg48 = dataCommonAEM == null || (_dataCommonAEM$data2 = dataCommonAEM.data) == null || (_dataCommonAEM$data2 = _dataCommonAEM$data2.messages) == null ? undefined : _dataCommonAEM$data2.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG48";
    });
    var filterDateDeparture = (0, _reactRedux.useSelector)(function (state) {
      return _flyRedux.FlySelectors.filterDateDeparture(state);
    });
    var filterPillList = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.filterPillList) || [];
    var _useState3 = (0, _react.useState)(_flightDetail.TravelOption.iAmTravelling),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      selectedTravelOption = _useState4[0],
      setSelectedTravelOption = _useState4[1];
    var _useModal = (0, _useModal2.useModal)("saveFlightTravelOptionLandingdep"),
      isModalVisible = _useModal.isModalVisible,
      openModal = _useModal.openModal,
      closeModal = _useModal.closeModal;
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      loadingSaveFlight = _useState6[0],
      setLoadingSaveFlight = _useState6[1];
    var _useState7 = (0, _react.useState)(null),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      saveFlightPayload = _useState8[0],
      setSaveFlightPayload = _useState8[1];
    var insertFlightPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.insertFlightPayload);
    var FLY_CONTEXT_HANDLERS = _react.default.useContext(_fly.FLY_CONTEXT).Handlers;
    var _useContext = (0, _react.useContext)(_panResponder.PanResponderContext),
      conditionTimeRef = _useContext.conditionTimeRef,
      idleTimeRef = _useContext.idleTimeRef;
    var isAutoReload = (0, _react.useRef)(false);
    var isLandingDefaultEmpty = false;
    var _useTickerbandMaintan = (0, _useTickerbandMaintanence.useTickerbandMaintanance)(_useTickerbandMaintanence.MaintananceTickerBandType.FLY_FLANDING),
      isShowMaintenance = _useTickerbandMaintan.isShowMaintenance,
      errorData = _useTickerbandMaintan.errorData,
      fetchTickerbandMaintanance = _useTickerbandMaintan.fetchTickerbandMaintanance;
    var filterPillItemSelected = (0, _react.useMemo)(function () {
      var pillItem = filterPillList == null ? undefined : filterPillList.find(function (item) {
        return item.isSelected;
      });
      return pillItem;
    }, [filterPillList]);
    var checkInternetConnection = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        return isConnected;
      });
      return function checkInternetConnection() {
        return _ref2.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      if (isFocused) {
        _reactNative.InteractionManager.runAfterInteractions(function () {
          if (FLY_CONTEXT_HANDLERS != null && FLY_CONTEXT_HANDLERS.flyRefreshRef) {
            clearInterval(FLY_CONTEXT_HANDLERS == null ? undefined : FLY_CONTEXT_HANDLERS.flyRefreshRef);
          }
          if (!isAutoReload.current) {
            flightAutoReload();
          }
        });
      }
    }, [pullToRefreshTimeStamp, isFocused]);
    (0, _react.useEffect)(function () {
      if (isFocused && (departureLandingLoading || departureLandingEarlierLoading)) {
        _reactNative.InteractionManager.runAfterInteractions(function () {
          dispatch(_flyRedux.FlyCreators.resetDepartureLandingLoading());
        });
      }
    }, [isFocused, departureLandingLoading, departureLandingEarlierLoading]);
    var flightAutoReload = function flightAutoReload() {
      var _env;
      isAutoReload.current = true;
      var refreshInterval = (_env = (0, _envParams.env)()) == null ? undefined : _env.FLIGHT_REFRESH_INTERVAL;
      setTimeout(function () {
        _reactNative.InteractionManager.runAfterInteractions(function () {
          dispatch(_flyRedux.FlyCreators.flyLandingSelectedTab(_flightProps.FlightDirection.departure));
        });
      }, 0);
      FLY_CONTEXT_HANDLERS.flyRefreshRef = setInterval(function () {
        hideUnableMessage();
        dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
        if (!isLoggedIn) {
          var filterPillItemChecked = filterPillList == null ? undefined : filterPillList.find(function (item) {
            return item.isSelected;
          });
          callLandingDepartureApi(_flightProps.FlightRequestType.FlightRefresh, filterPillItemChecked);
        }
      }, refreshInterval);
      setTimeout(function () {
        isAutoReload.current = false;
      }, 1000);
    };
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      _reactNative.InteractionManager.runAfterInteractions(function () {
        if (FLY_CONTEXT_HANDLERS.flyRefreshRef) {
          clearInterval(FLY_CONTEXT_HANDLERS.flyRefreshRef);
        }
        if (!isAutoReload.current) {
          flightAutoReload();
        }
      });
      return function () {
        _reactNative.InteractionManager.runAfterInteractions(function () {
          clearInterval(FLY_CONTEXT_HANDLERS.flyRefreshRef);
          isEndEarlierFlightLanding && dispatch(_flyRedux.FlyCreators.resetEndEarlierFlightLanding());
        });
      };
    }, []));
    (0, _react.useEffect)(function () {
      _reactNative.InteractionManager.runAfterInteractions(function () {
        callLandingDepartureApi(_flightProps.FlightRequestType.FlightDefault, filterPillItemSelected);
      });
    }, [filterPillItemSelected]);
    var callLandingDepartureApi = function callLandingDepartureApi(flightRequest, filterPillItem) {
      checkInternetConnection().then(function (isConnection) {
        if (isConnection) {
          var filters = filterPillItem.tagCode === "all" ? [] : [filterPillItem.tagName];
          hideUnableMessage();
          dispatch(_flyRedux.FlyCreators.flyLandingDepartureRequest(_flightProps.FlightDirection.departure, flightRequest, filters));
          isEndEarlierFlightLanding && dispatch(_flyRedux.FlyCreators.resetEndEarlierFlightLanding());
          setNoConnection(false);
          dispatch(_flyRedux.FlyCreators.flyLastUpdatedTimeStamp((0, _dateTime.flyModuleUpdatedTime)()));
        } else {
          var _flyPayload$data;
          setNoConnection(true);
          if (flyPayload != null && (_flyPayload$data = flyPayload.data) != null && (_flyPayload$data = _flyPayload$data[0]) != null && (_flyPayload$data = _flyPayload$data.flightLanding) != null && (_flyPayload$data = _flyPayload$data[0]) != null && _flyPayload$data.state) {
            showUnableMessage();
          }
        }
      });
    };
    var showErrorMessage = function showErrorMessage() {
      if (flyPayload != null && flyPayload.errorFlag) {
        return (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.errorComponentContainerStyle,
          children: (0, _jsxRuntime.jsx)(_errorOverlay.ErrorOverlay, {
            reload: true,
            header: false,
            visible: true,
            onReload: function onReload() {
              return callLandingDepartureApi(_flightProps.FlightRequestType.FlightDefault, filterPillItemSelected);
            },
            ignoreShowNoInternet: true,
            testID: `${COMPONENT_NAME}__ErrorComponent`,
            accessibilityLabel: `${COMPONENT_NAME}__ErrorComponent`,
            variant: _errorOverlay.ErrorOverlayVariant.VARIANTSECTION,
            overlayStyle: styles.errorOverlayStyle
          })
        });
      }
    };
    var handleMessage48 = function handleMessage48(message, number, place) {
      if (message) {
        return message.replace("<Flight No.>", number).replace("<country>", place);
      }
      return message;
    };
    var onRemoveFlight = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* (payload) {
        var item = payload == null ? undefined : payload.item;
        var data = {
          enterpriseUserId: profilePayload == null ? undefined : profilePayload.email,
          countryOfResidence: profilePayload == null ? undefined : profilePayload.residentialCountry,
          flightNumber: item == null ? undefined : item.flightNumber,
          flightScheduledDate: item == null ? undefined : item.scheduledDate,
          flightDirection: item == null ? undefined : item.direction
        };
        _reactNative.Alert.alert((msg48 == null ? undefined : msg48.title) || (0, _i18n.translate)("flightLanding.areYouSure"), msg48 != null && msg48.message ? handleMessage48(msg48 == null ? undefined : msg48.message, item == null ? undefined : item.flightNumber, item == null ? undefined : item.destinationPlace) : `${(0, _i18n.translate)("flightLanding.removeMessage1")} ${item == null ? undefined : item.flightNumber} ${(0, _i18n.translate)("flightLanding.to")} ${item == null ? undefined : item.destinationPlace} ${(0, _i18n.translate)("flightLanding.removeMessage2")}`, [{
          text: (msg48 == null ? undefined : msg48.firstButton) || (0, _i18n.translate)("flightLanding.cancel")
        }, {
          text: (msg48 == null ? undefined : msg48.secondButton) || (0, _i18n.translate)("flightLanding.remove"),
          style: "cancel",
          onPress: function onPress() {
            var dtAction = (0, _firebase.dtManualActionEvent)(`${_firebase.FE_LOG_PREFIX}App__flight-departure-landing-unsave`);
            dtAction.reportStringValue("flight-departure-landing-unsave-press-flightNumber", `${item == null ? undefined : item.flightNumber}`);
            dtAction.reportStringValue("flight-departure-landing-unsave-press-scheduledDate", `${item == null ? undefined : item.scheduledDate}`);
            dtAction.reportStringValue("flight-departure-landing-unsave-press-direction", `${item == null ? undefined : item.direction}`);
            dispatch(_mytravelRedux.MytravelCreators.flyMyTravelRemoveFlightRequest(data, payload));
            dtAction.leaveAction();
          }
        }]);
      });
      return function onRemoveFlight(_x) {
        return _ref3.apply(this, arguments);
      };
    }();
    var handleMessage58 = function handleMessage58(message, flyItem) {
      if (message) {
        var _flyItem$flightStatus, _status;
        var status = flyItem == null || (_flyItem$flightStatus = flyItem.flightStatus) == null ? undefined : _flyItem$flightStatus.toLowerCase();
        if ((_status = status) != null && _status.includes("cancelled")) {
          status = `been ${status}`;
        }
        return message.replace("<Flight No.>", flyItem == null ? undefined : flyItem.flightNumber).replace("<departed/landed/been cancelled>", status);
      }
      return message;
    };
    var notAbleToSaveAlert = function notAbleToSaveAlert(flyItem) {
      var _flyItem$flightStatus2, _alertApp$current;
      var temp = flyItem == null || (_flyItem$flightStatus2 = flyItem.flightStatus) == null ? undefined : _flyItem$flightStatus2.split(" ");
      var status = (temp == null ? undefined : temp.length) > 0 ? temp[0] : "";
      var message = handleMessage58(msg58 == null ? undefined : msg58.message, flyItem) || `${(0, _i18n.translate)("flightLanding.flight")} ${flyItem == null ? undefined : flyItem.flightNumber} ${(0, _i18n.translate)("flightLanding.has")} ${status} ${(0, _i18n.translate)("flightLanding.notSaveMessage")}`;
      alertApp == null || (_alertApp$current = alertApp.current) == null || _alertApp$current.show({
        title: (msg58 == null ? undefined : msg58.title) || (0, _i18n.translate)("flightLanding.alert"),
        description: message,
        labelAccept: (msg58 == null ? undefined : msg58.firstButton) || (0, _i18n.translate)("flightLanding.okay"),
        onAccept: function onAccept() {
          return null;
        },
        type: _alertApp2.AlertTypes.ALERT
      });
    };
    var checkFlightCanSave = function checkFlightCanSave(statusTag) {
      var status = statusTag == null ? undefined : statusTag.toLowerCase();
      switch (true) {
        case /departed/gim.test(status):
        case /cancelled/gim.test(status):
        case /landed/gim.test(status):
          return false;
        default:
          return true;
      }
    };
    var handleTriggerAppRatingIdle = function handleTriggerAppRatingIdle() {
      (0, _screenHelper.resetInactivityTimeout)({
        conditionTimeRef: conditionTimeRef,
        idleTimeRef: idleTimeRef,
        callback: function callback() {
          return (0, _screenHelper.trackingShowRatingPopup)({
            route: route
          });
        }
      });
    };
    (0, _react.useEffect)(function () {
      var _insertFlightPayload$;
      var trackingAction = "Save";
      var trackingFlyProfile = "flying";
      if (insertFlightPayload != null && (_insertFlightPayload$ = insertFlightPayload.insertFlightData) != null && _insertFlightPayload$.success || insertFlightPayload != null && insertFlightPayload.recordExist) {
        if (insertFlightPayload != null && insertFlightPayload.isInsertSuccessfully && isFocused) {
          var _saveFlightPayload$it, _saveFlightPayload$it2;
          setLoadingSaveFlight(false);
          var saveFlightStatus = "Successful";
          (0, _screenHelper.trackActionOldFormat)(_adobe.AdobeTagName.CAppFlyFlightListSaveFlight, {
            pageName: _adobe.AdobeTagName.CAppFlightLanding,
            flightNumber: saveFlightPayload == null || (_saveFlightPayload$it = saveFlightPayload.item) == null ? undefined : _saveFlightPayload$it.flightNumber,
            flightDirection: _flightProps.FlightDirection.departure,
            flightDate: saveFlightPayload == null || (_saveFlightPayload$it2 = saveFlightPayload.item) == null ? undefined : _saveFlightPayload$it2.flightDate,
            flyProfile: trackingFlyProfile,
            action: trackingAction,
            flightStatus: saveFlightStatus
          });
          (0, _storage.save)(_storageKey.StorageKey.isSaveFlightTriggered, true);
          handleTriggerAppRatingIdle();
        }
      }
      if (insertFlightPayload != null && insertFlightPayload.errorFlag) {
        var _saveFlightPayload$it3, _saveFlightPayload$it4;
        setLoadingSaveFlight(false);
        closeModal();
        var _saveFlightStatus = "Failed";
        (0, _screenHelper.trackActionOldFormat)(_adobe.AdobeTagName.CAppFlyFlightListSaveFlight, {
          pageName: _adobe.AdobeTagName.CAppFlightLanding,
          flightNumber: saveFlightPayload == null || (_saveFlightPayload$it3 = saveFlightPayload.item) == null ? undefined : _saveFlightPayload$it3.flightNumber,
          flightDirection: _flightProps.FlightDirection.departure,
          flightDate: saveFlightPayload == null || (_saveFlightPayload$it4 = saveFlightPayload.item) == null ? undefined : _saveFlightPayload$it4.flightDate,
          flyProfile: trackingFlyProfile,
          action: trackingAction,
          flightStatus: _saveFlightStatus
        });
        handleTriggerAppRatingIdle();
      }
    }, [insertFlightPayload]);
    var savedFlightOnPress = /*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* () {
        var _saveFlightPayload$it5, _saveFlightPayload$it6;
        setLoadingSaveFlight(true);
        var data = {
          enterpriseUserId: profilePayload == null ? undefined : profilePayload.email,
          countryOfResidence: profilePayload == null ? undefined : profilePayload.residentialCountry,
          flightNumber: saveFlightPayload == null || (_saveFlightPayload$it5 = saveFlightPayload.item) == null ? undefined : _saveFlightPayload$it5.flightNumber,
          flightScheduledDate: saveFlightPayload == null || (_saveFlightPayload$it6 = saveFlightPayload.item) == null ? undefined : _saveFlightPayload$it6.scheduledDate,
          flightDirection: _flightProps.FlightDirection.departure,
          // check param
          flightPax: selectedTravelOption === _flightDetail.TravelOption.iAmTravelling
        };
        if (isLoggedIn) {
          var _saveFlightPayload$it7, _saveFlightPayload$it8;
          var dtAction = (0, _firebase.dtManualActionEvent)(`${_firebase.FE_LOG_PREFIX}App__flight-departure-landing-save`);
          dtAction.reportStringValue("flight-departure-landing-save-press-flightNumber", `${saveFlightPayload == null || (_saveFlightPayload$it7 = saveFlightPayload.item) == null ? undefined : _saveFlightPayload$it7.flightNumber}`);
          dtAction.reportStringValue("flight-departure-landing-save-press-scheduledDate", `${saveFlightPayload == null || (_saveFlightPayload$it8 = saveFlightPayload.item) == null ? undefined : _saveFlightPayload$it8.scheduledDate}`);
          dtAction.reportStringValue("flight-departure-landing-save-press-travelOption", `${selectedTravelOption}`);
          dispatch(_mytravelRedux.MytravelCreators.flyMyTravelInsertFlightRequest(data, saveFlightPayload));
          dtAction.leaveAction();
        } else {
          //@ts-ignore
          navigation.navigate(_constants.NavigationConstants.authScreen, {
            sourceSystem: _constants.SOURCE_SYSTEM.FLIGHTS
          });
        }
      });
      return function savedFlightOnPress() {
        return _ref4.apply(this, arguments);
      };
    }();
    var onFilterFlight = function onFilterFlight(_ref5) {
      var date = _ref5.date,
        filterLocation = _ref5.filterLocation,
        direction = _ref5.direction,
        airline = _ref5.airline,
        cityAirport = _ref5.cityAirport;
      if (date) {
        dispatch(_flyRedux.FlyCreators.setFilterDateDeparture(date));
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlyFlightListFilters, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlyFlightListFilters, (0, _moment.default)(date).format("YYYY-MM-DD")));
      }
      if (filterLocation && (filterLocation == null ? undefined : filterLocation.length) > 0) {
        dispatch(_flyRedux.FlyCreators.setFlightSearchTerminal(filterLocation));
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlyFlightListFilters, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlyFlightListFilters, `${filterLocation.join("|")}`));
      }
      var flightFilterOptionSelected = {
        terminal: filterLocation,
        airline: airline,
        cityAirport: cityAirport
      };
      dispatch(_flyRedux.FlyCreators.setFlightFilterOption(flightFilterOptionSelected));
      _reactNative.InteractionManager.runAfterInteractions(function () {
        navigation.navigate("flightResultLandingScreen", {
          screen: direction,
          query: (0, _defineProperty2.default)({}, direction, {
            selectedDate: date,
            filterLocation: filterLocation,
            airline: airline,
            cityAirport: cityAirport
          }),
          sourcePage: _adobe.AdobeTagName.CAppFlightLanding
        });
      });
    };
    var loadEarlierFlightsHandler = function loadEarlierFlightsHandler() {
      if (flyPayload && !isEndEarlierFlightLanding) {
        var filters = filterPillItemSelected.tagCode === "all" ? [] : [filterPillItemSelected.tagName];
        dispatch(_flyRedux.FlyCreators.flyDeparturePaginationEarlierLandingRequest(_flightProps.FlightDirection.departure, flyPayload == null ? undefined : flyPayload.previousToken, undefined, filters));
      }
    };
    var earlierFlightsHandler = function earlierFlightsHandler() {
      clearInterval(FLY_CONTEXT_HANDLERS.flyRefreshRef);
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlyFlightListEarlierFlights, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlyFlightListEarlierFlights, "1"));
      loadEarlierFlightsHandler();
    };
    var handleButtonOnPress = /*#__PURE__*/function () {
      var _ref6 = (0, _asyncToGenerator2.default)(function* (navigationObj, redirect) {
        var type = navigationObj.type,
          value = navigationObj.value;
        if (!type || !value) return;
        handleNavigation(type, value, redirect);
      });
      return function handleButtonOnPress(_x2, _x3) {
        return _ref6.apply(this, arguments);
      };
    }();
    var headerComponent = function headerComponent() {
      if (departureLandingEarlierLoading) {
        return (0, _jsxRuntime.jsx)(_footerLoading.default, {
          containerStyle: styles.loadingHeaderContainerStyle
        });
      }
      if (isEndEarlierFlightLanding) {
        return (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "caption1Regular",
          tx: "flightLanding.noEarlierFlights",
          style: styles.noEarlierFlights
        });
      }
      return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {});
    };
    var bottomRender = _react.default.useCallback(function () {
      var _flyPayload$data2, _flyPayload$data3;
      //The conditions for rendering tx={"flightLanding.noMoreFlights"} remain the same
      if (flyPayload != null && (_flyPayload$data2 = flyPayload.data) != null && (_flyPayload$data2 = _flyPayload$data2[0]) != null && (_flyPayload$data2 = _flyPayload$data2.flightLanding) != null && _flyPayload$data2.length && (flyPayload == null || (_flyPayload$data3 = flyPayload.data) == null || (_flyPayload$data3 = _flyPayload$data3[0]) == null || (_flyPayload$data3 = _flyPayload$data3.flightLanding) == null || (_flyPayload$data3 = _flyPayload$data3[0]) == null ? undefined : _flyPayload$data3.state) !== "loading" && !departureLandingLoading && !(flyPayload != null && flyPayload.nextToken)) {
        return (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.noMoreFlights,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "caption1Regular",
            tx: "flightLanding.noMoreFlights"
          })
        });
      } else {
        //Change logic render <FooterLoading /> from arrivalLandingLoading (redux state) to loadingLoadMoreLocal (local state)
        if (loadingLoadMoreLocal) {
          var hasNextPage = flyPayload == null ? undefined : flyPayload.nextToken;
          if (!hasNextPage) {
            return (0, _jsxRuntime.jsx)(_reactNative.View, {
              style: styles.dotsContainer
            });
          }
          return (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: styles.dotsContainer,
            children: (0, _jsxRuntime.jsx)(_footerLoading.default, {})
          });
        }
        return (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.dotsContainer
        });
      }
    }, [loadingLoadMoreLocal, departureLandingLoading, flyPayload == null || (_flyPayload$data4 = flyPayload.data) == null || (_flyPayload$data4 = _flyPayload$data4[0]) == null || (_flyPayload$data4 = _flyPayload$data4.flightLanding) == null ? undefined : _flyPayload$data4.length, flyPayload == null || (_flyPayload$data5 = flyPayload.data) == null || (_flyPayload$data5 = _flyPayload$data5[0]) == null || (_flyPayload$data5 = _flyPayload$data5.flightLanding) == null || (_flyPayload$data5 = _flyPayload$data5[0]) == null ? undefined : _flyPayload$data5.state, flyPayload == null ? undefined : flyPayload.nextToken]);
    var onClosedSheet = function onClosedSheet() {
      if (!loadingSaveFlight) {
        closeModal();
      }
    };
    var travelOptionTapped = function travelOptionTapped(option) {
      setSelectedTravelOption(option);
    };
    var savedFlightTravelOptionsOnModalHide = function savedFlightTravelOptionsOnModalHide() {
      setSelectedTravelOption(_flightDetail.TravelOption.iAmTravelling);
      (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
    };
    var sectionList = (0, _react.useMemo)(function () {
      var _flyPayload$data6;
      //listen when UI update new list flight then change state loading loadmore local component = false 
      hideLoadingLoadMore();
      return flyPayload == null || (_flyPayload$data6 = flyPayload.data) == null ? undefined : _flyPayload$data6.map(function (item) {
        return {
          title: item.date,
          data: item.flightLanding,
          currentDate: item.currentDate
        };
      });
    }, [flyPayload == null ? undefined : flyPayload.data]);
    var renderSectionHeader = function renderSectionHeader(_ref7) {
      var _ref7$section = _ref7.section,
        title = _ref7$section.title,
        data = _ref7$section.data,
        currentDate = _ref7$section.currentDate;
      var index = sectionList.findIndex(function (item) {
        return item.title === title;
      });
      var isToday = (0, _moment.default)(currentDate).isSame((0, _moment.default)(), "day");
      var isBeforeDate = (0, _moment.default)(currentDate).isBefore((0, _moment.default)());
      var isNotEmptyFlightItem = (data == null ? undefined : data.length) > 0;
      if (!(data != null && data.length)) {
        return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            preset: "h4",
            text: title,
            style: styles.dateTextStyle
          }), (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: styles.renderEmptyStateView,
            children: (0, _jsxRuntime.jsx)(_flightsEmptyState.default, {
              item: {
                currentDate: currentDate
              },
              isLandingDefaultEmpty: isLandingDefaultEmpty,
              isLocationFilterApplied: false,
              isDateFilterApplied: false
            })
          })]
        });
      }
      if (isToday && index === 0 && isNotEmptyFlightItem) {
        return (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.emptyTitleFirstDate
        });
      }
      if (isBeforeDate && index === 0) {
        return (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "h4",
          text: title,
          style: [styles.dateTextStyle, departureLandingEarlierLoading ? {} : styles.dateTextStyleWhenNoLoading]
        });
      }
      return (0, _jsxRuntime.jsx)(_text.Text, {
        preset: "h4",
        text: title,
        style: styles.dateTextStyle
      });
    };
    var onFlightPress = (0, _react.useCallback)(function (item) {
      var flightDate = (0, _lodash.get)(item, "flightDate", "");
      var flightNumber = (0, _lodash.get)(item, "flightNumber", "");
      var direction = (0, _lodash.get)(item, "direction", "");
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlyFlightListFlightCardClicked, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlyFlightListFlightCardClicked, "1"));
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlyFlightListViewFlightDetails, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlyFlightListViewFlightDetails, `${direction}|${flightDate}|${flightNumber}`));
      //@ts-ignore
      navigation.navigate("flightDetails", {
        payload: {
          item: item
        },
        direction: _flightProps.FlightDirection.departure
      });
    }, []);
    var onFlightSave = /*#__PURE__*/function () {
      var _ref8 = (0, _asyncToGenerator2.default)(function* (item, isSaved) {
        if (isSaved && isLoggedIn) {
          onRemoveFlight({
            item: item
          });
        } else {
          if (!checkFlightCanSave(item == null ? undefined : item.flightStatus)) {
            notAbleToSaveAlert(item);
          } else {
            if (isLoggedIn) {
              openModal();
              setSaveFlightPayload({
                item: item
              });
              (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(true);
            } else {
              //@ts-ignore
              navigation.navigate(_constants.NavigationConstants.authScreen, {
                sourceSystem: _constants.SOURCE_SYSTEM.FLIGHTS,
                callBackAfterLoginSuccess: function callBackAfterLoginSuccess() {
                  openModal();
                  setSaveFlightPayload({
                    item: item
                  });
                  (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(true);
                },
                callBackAfterLoginCancel: function callBackAfterLoginCancel() {
                  return null;
                }
              });
            }
          }
        }
      });
      return function onFlightSave(_x4, _x5) {
        return _ref8.apply(this, arguments);
      };
    }();
    var onRenderItem = function onRenderItem(_ref9) {
      var item = _ref9.item,
        index = _ref9.index;
      var isSaved = false;
      if (myTravelFlightsPayload != null && myTravelFlightsPayload.getMyTravelFlightDetails) {
        isSaved = myTravelFlightsPayload.getMyTravelFlightDetails.findIndex(function (savedFlight) {
          return savedFlight.flightNumber === item.flightNumber && savedFlight.scheduledDate === item.scheduledDate && (savedFlight.flightDirection || savedFlight.direction) === (item.flightDirection || item.direction);
        }) >= 0;
      }
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.renderItemViewStyles,
        children: (0, _jsxRuntime.jsx)(_flightListingCard.default, Object.assign({}, item, {
          isLoggedIn: isLoggedIn,
          onPressed: function onPressed() {
            onFlightPress(item);
          },
          onSaved: function onSaved(isSaved) {
            onFlightSave(item, isSaved);
          },
          isSaved: isSaved,
          itemIndex: index,
          getSavedFlightsLoading: myTravelFlightsPayload == null ? undefined : myTravelFlightsPayload.loading
        }))
      });
    };
    if (!(0, _lodash.isEmpty)(sectionMaintenanceObj) && sectionMaintenanceObj != null && sectionMaintenanceObj.enableMode) {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.errorUnplannedMaintenance,
        children: (0, _jsxRuntime.jsx)(_errorUnplannedMaintenance.ErrorUnplannedMaintenance, {
          header: sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.header,
          subHeader: sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.subHeader,
          icon: sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.icon,
          buttonLabel: sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.buttonLabel,
          buttonLabel2: sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.buttonLabel2,
          onFirstButtonPress: function onFirstButtonPress() {
            return handleButtonOnPress(sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.navigationFirst, sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.redirectFirst);
          },
          onSecondButtonPress: function onSecondButtonPress() {
            return handleButtonOnPress(sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.navigationSecond, sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.redirectSecond);
          },
          testID: `${COMPONENT_NAME}__ErrorUnplannedMaintenance`,
          accessibilityLabel: `${COMPONENT_NAME}__ErrorUnplannedMaintenance`
        })
      });
    }
    if (isShowMaintenance) {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.maintenanceErrorContainer,
        children: (0, _jsxRuntime.jsx)(_errorCloud.ErrorCloudComponent, {
          skipStatusbar: true,
          style: {
            backgroundColor: "transparent"
          },
          titleStyle: {
            marginTop: 16
          },
          buttonStyle: {
            width: "auto"
          },
          errorData: errorData,
          onPress: fetchTickerbandMaintanance
        })
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.parentContainerStyle,
        children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.flightListingCardSectionStyle,
          onLayout: onLayoutTab,
          ref: viewRef,
          children: [(0, _jsxRuntime.jsx)(_flightFilterPill.default, {
            listFilterPills: filterPillList,
            itemPillOnPress: function itemPillOnPress(item) {
              return callLandingDepartureApi(_flightProps.FlightRequestType.FlightDefault, item);
            }
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.tapToLoadEarlierContainer,
            children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                text: (0, _i18n.translate)("flightLanding.updatedSystemTime") + " " + flyLastUpdatedTimeStamp,
                preset: "caption2Regular",
                style: styles.lastUpdatedTextStyle
              }), (0, _jsxRuntime.jsx)(_button.Button, {
                style: styles.tapToLoadEarlierButtonStyle,
                onPress: earlierFlightsHandler,
                sizePreset: "small",
                backgroundPreset: "light",
                textPreset: "buttonSmall",
                tx: "departureSection.earlierFlights",
                statePreset: "default",
                testID: `${COMPONENT_NAME}__ButtonFlightsHandler`,
                accessibilityLabel: `${COMPONENT_NAME}__ButtonFlightsHandler`
              })]
            }), (0, _jsxRuntime.jsx)(_reactNative.View, {
              style: styles.landingFilterContainer,
              children: (0, _jsxRuntime.jsx)(_filterFlight.default, {
                onFilterFlight: onFilterFlight,
                initialDate: new Date(),
                disableChangeDate: true,
                disableChangeFilterLocation: true,
                containerFilterStyle: styles.containerFilterStyle,
                displayDate: "",
                isDateSelected: !!filterDateDeparture,
                direction: _flightProps.FlightDirection.departure,
                isShowCalendar: false,
                isNewDesign: true
              })
            })]
          }), (0, _jsxRuntime.jsx)(_flightInformationDisclaimer.default, {
            marginTop: 16,
            marginBottom: 0
          }), (0, _jsxRuntime.jsx)(_saveFlightTraveOptionWrap.default, {
            onModalHide: savedFlightTravelOptionsOnModalHide,
            visible: isModalVisible,
            onClosed: onClosedSheet,
            loadingSaveFlight: loadingSaveFlight,
            onBackPressed: onClosedSheet,
            selectedOption: selectedTravelOption,
            savedFlightOnPress: savedFlightOnPress,
            onPress: function onPress(option) {
              return travelOptionTapped(option);
            },
            flightDirection: _flightProps.FlightDirection.departure
          }), showErrorMessage(), isNoConnection && (0, _lodash.isEmpty)(flyPayload == null ? undefined : flyPayload.data) && (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
            reload: true,
            header: false,
            hideScreenHeader: true,
            headerBackgroundColor: "transparent",
            visible: isNoConnection,
            testID: `DepartureLanding__ErrorOverlayNoConnection`,
            onReload: function onReload() {
              callLandingDepartureApi(_flightProps.FlightRequestType.FlightDefault, filterPillItemSelected);
            },
            storyMode: true,
            noInternetOverlayStyle: styles.noInternetOverlayStyle
          }), !(0, _lodash.isEmpty)(flyPayload == null ? undefined : flyPayload.data) && !(flyPayload != null && flyPayload.errorFlag) && (0, _jsxRuntime.jsx)(_reactNative.SectionList, {
            sections: sectionList,
            keyExtractor: function keyExtractor(_item, index) {
              return `${_item == null ? undefined : _item.flightNumber} ${_item == null ? undefined : _item.scheduledDate} ${index.toString()}`;
            },
            renderSectionHeader: renderSectionHeader,
            stickySectionHeadersEnabled: false,
            renderItem: onRenderItem,
            ListHeaderComponent: headerComponent,
            scrollEnabled: false,
            showsVerticalScrollIndicator: false,
            maxToRenderPerBatch: 10,
            windowSize: 5,
            initialNumToRender: 10
          }), bottomRender()]
        })
      }), (0, _jsxRuntime.jsx)(_alertApp.AlertApp, {
        ref: alertApp
      }), (0, _jsxRuntime.jsx)(_subscriptions.FlyListSubscription, {
        direction: _subscriptions.FlySubscriptionDirectionEnum.DEP,
        screen: _subscriptions.FlySubscriptionScreenEnum.landing
      })]
    });
  };
  var _default = exports.default = DepartureLandingScreen;
