  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _i18n = _$$_REQUIRE(_dependencyMap[4]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  var emptyView = {
    height: 50,
    justifyContent: "center",
    marginTop: -24
  };
  var todayEmptyView = {
    justifyContent: "center",
    alignItems: "center"
  };
  var isLandingDefaultEmptyView = {
    paddingTop: 16,
    paddingBottom: 40
  };
  var FlightsEmptyState = function FlightsEmptyState(props) {
    var item = props.item,
      isLandingDefaultEmpty = props.isLandingDefaultEmpty,
      isLocationFilterApplied = props.isLocationFilterApplied,
      isDateFilterApplied = props.isDateFilterApplied;
    var isToday = _react.default.useMemo(function () {
      if (item != null && item.currentDate) {
        return (0, _moment.default)(item.currentDate).isSame((0, _moment.default)(), "day");
      }
      return false;
    }, [item]);
    var todayWrapperStyle = Object.assign({}, isToday && {
      marginTop: -14
    });
    var todayEmptyHeight = {
      height: 50
    };
    if (isLandingDefaultEmpty) {
      // when flight null or empty
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: isLandingDefaultEmptyView,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "caption1Regular",
          tx: "flightLanding.currentlyUnableToDisplayFlight"
        })
      });
    }
    if (isLocationFilterApplied || isDateFilterApplied) {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: [todayEmptyView, todayEmptyHeight],
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "caption1Regular",
          tx: "flightLanding.noFlightsAvailableOnSelectedFilter"
        })
      });
    }
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: [emptyView, todayWrapperStyle],
      children: (0, _jsxRuntime.jsx)(_text.Text, {
        preset: "caption1Regular",
        text: isToday ? `${(0, _i18n.translate)("flightLanding.currentlyUnableToDisplayFlight")}` : `${(0, _i18n.translate)("flightLanding.noFlightsScheduledForThisDate")}`
      })
    });
  };
  var _default = exports.default = FlightsEmptyState;
