  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.TOP_TAB_PARENT_HEIGHT = exports.HeaderTabNavBar = undefined;
  var React = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _responsive = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _icons = _$$_REQUIRE(_dependencyMap[9]);
  var _isEmpty = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _constants = _$$_REQUIRE(_dependencyMap[11]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[12]);
  var _mytravelRedux = _$$_REQUIRE(_dependencyMap[13]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[14]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var TOP_TAB_PARENT_HEIGHT = exports.TOP_TAB_PARENT_HEIGHT = _responsive.default.isDynamicIsLand() ? 97 : 92;
  var HeaderTabNavBar = exports.HeaderTabNavBar = function HeaderTabNavBar(topTabProps) {
    var _topTabProps$props = topTabProps.props,
      state = _topTabProps$props.state,
      descriptors = _topTabProps$props.descriptors,
      navigation = _topTabProps$props.navigation,
      _topTabProps$props$te = _topTabProps$props.testID,
      testID = _topTabProps$props$te === undefined ? "HeaderTabNavBar" : _topTabProps$props$te,
      _topTabProps$props$ac = _topTabProps$props.accessibilityLabel,
      accessibilityLabel = _topTabProps$props$ac === undefined ? "HeaderTabNavBar" : _topTabProps$props$ac;
    var topTabParentStyle = topTabProps.topTabParentStyle,
      isFlyLanding = topTabProps.isFlyLanding;
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var myTravelFlightsPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.myTravelFlightsPayload);
    var handleNavigate = function handleNavigate() {
      navigation == null || navigation.navigate(_constants.NavigationConstants.saveFlightsScreen);
    };
    var savedFlightOnPress = function savedFlightOnPress() {
      if (!isLoggedIn) {
        navigation.navigate(_constants.NavigationConstants.authScreen, {
          sourceSystem: _constants.SOURCE_SYSTEM.FLIGHTS,
          callBackAfterLoginSuccess: handleNavigate
        });
      } else {
        handleNavigate();
      }
    };
    var renderSavedFlightButton = function renderSavedFlightButton() {
      if (isFlyLanding) {
        return (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.savedFlightButtonContainer,
          children: (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
            style: styles.buttonSavedStyles,
            onPress: function onPress() {
              return savedFlightOnPress();
            },
            children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: styles.iconContainer,
              children: [(0, _jsxRuntime.jsx)(_icons.FlightTypeHeader, {}), (0, _isEmpty.default)(myTravelFlightsPayload == null ? undefined : myTravelFlightsPayload.getMyTravelFlightDetails) ? (0, _jsxRuntime.jsx)(_icons.FlightAddHeader, {
                style: styles.leftIconStyles
              }) : (0, _jsxRuntime.jsx)(_icons.FlightSaveHeader, {
                style: styles.leftIconStyles
              })]
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              tx: "flyLanding.saved",
              preset: "textLink"
            })]
          })
        });
      }
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {});
    };
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: styles.wrapHeader,
      children: (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
        start: {
          x: 1,
          y: 0
        },
        end: {
          x: 0,
          y: 1
        },
        style: Object.assign({}, styles.topTabParentStyle, topTabParentStyle),
        colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: Object.assign({}, styles.topTabParentStyle, topTabParentStyle),
          children: [state.routes.map(function (route, index) {
            var options = descriptors[route.key].options;
            var label;
            if (options.tabBarLabel !== undefined) {
              label = options.tabBarLabel;
            } else if (options.title !== undefined) {
              label = options.title;
            } else {
              label = route.name;
            }
            var isFocused = state.index === index;
            var onPress = function onPress() {
              var event = navigation.emit({
                type: "tabPress",
                target: route.key
              });
              if (!isFocused && !event.defaultPrevented) {
                navigation.navigate(route.name);
              }
            };
            var onLongPress = function onLongPress() {
              navigation.emit({
                type: "tabLongPress",
                target: route.key
              });
            };
            var getMarginzLable = function getMarginzLable(idx) {
              return {
                marginLeft: idx === 0 ? 24 : 20
              };
            };
            return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              accessibilityRole: "button",
              testID: `${testID}__TouchableItemHeaderTabNavBar`,
              accessibilityLabel: `${accessibilityLabel}__TouchableItemHeaderTabNavBar`,
              onPress: onPress,
              onLongPress: onLongPress,
              style: [styles.topTabTouchableOpacityStyle, getMarginzLable(index)],
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                style: isFocused ? styles.topTabActiveLabelStyle : styles.topTabInActiveLabelStyle,
                preset: "h3",
                text: label
              })
            }, index);
          }), renderSavedFlightButton()]
        })
      })
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    buttonSavedStyles: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 31,
      flexDirection: "row",
      justifyContent: "center",
      marginBottom: 8,
      paddingLeft: 19,
      paddingRight: 12,
      paddingVertical: 4
    },
    iconContainer: {
      marginRight: 8
    },
    leftIconStyles: {
      left: -7,
      position: "absolute",
      top: 2
    },
    savedFlightButtonContainer: {
      alignItems: "center",
      flex: 1,
      flexDirection: "row",
      height: "100%",
      justifyContent: "flex-end",
      paddingRight: 24
    },
    topTabActiveLabelStyle: {
      color: _theme.color.palette.whiteGrey
    },
    topTabInActiveLabelStyle: {
      color: _theme.color.palette.almostWhiteGrey + "99"
    },
    topTabParentStyle: {
      alignItems: "center",
      borderBottomLeftRadius: 24,
      flexDirection: "row",
      height: TOP_TAB_PARENT_HEIGHT,
      justifyContent: "flex-start",
      paddingTop: _responsive.default.isDynamicIsLand() ? 24 : 20,
      width: "100%"
    },
    topTabTouchableOpacityStyle: {
      alignItems: "center",
      height: "100%",
      justifyContent: "center"
    },
    wrapHeader: {
      backgroundColor: _theme.color.palette.lightestGrey
    }
  });
