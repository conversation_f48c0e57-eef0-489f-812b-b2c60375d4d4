  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _itemTravelMadeConvenient = _$$_REQUIRE(_dependencyMap[5]);
  var _airportType = _$$_REQUIRE(_dependencyMap[6]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _constants = _$$_REQUIRE(_dependencyMap[8]);
  var _theme = _$$_REQUIRE(_dependencyMap[9]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[10]);
  var _adobe = _$$_REQUIRE(_dependencyMap[11]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  var loadingData = Array(5).fill(null);
  var lightGreyLoadingColors = [_theme.color.palette.lightGrey, _theme.color.background, _theme.color.palette.lightGrey];
  var lighterGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var ListTravelMadeConvenient = function ListTravelMadeConvenient(props) {
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("LIST_TRAVEL_MADE_CONVENIENT"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var type = props.type,
      travelMadeConvenientData = props.travelMadeConvenientData,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "RedeemNewRewards" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "RedeemNewRewards" : _props$accessibilityL;
    var travelMadeConvenientList = travelMadeConvenientData == null ? undefined : travelMadeConvenientData.contents;
    var travelMadeConvenientItemOnPress = function travelMadeConvenientItemOnPress(itemData) {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppAirportLanding, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppAirportLanding, `${travelMadeConvenientData == null ? undefined : travelMadeConvenientData.title} | ${(itemData == null ? undefined : itemData.title) || ""}`));
      var _itemData$navigation = itemData.navigation,
        navigationType = _itemData$navigation.type,
        value = _itemData$navigation.value;
      var _ref = itemData || {},
        redirect = _ref.redirect;
      if (!navigationType || !value) return;
      handleNavigation(navigationType, value, redirect);
    };
    var renderItemLoading = function renderItemLoading() {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.container,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lightGreyLoadingColors,
          shimmerStyle: styles.thumbnailStyles
        }), (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.cardContainer,
          children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: lighterGreyLoadingColors,
            shimmerStyle: styles.bottomContainer
          })
        })]
      });
    };
    if (type === _airportType.ComponentState.loading) {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lightGreyLoadingColors,
          shimmerStyle: styles.titleLoading
        }), (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
          data: loadingData,
          renderItem: renderItemLoading,
          horizontal: true,
          keyExtractor: function keyExtractor(_item, index) {
            return index.toString();
          },
          showsHorizontalScrollIndicator: false
        })]
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        text: travelMadeConvenientData == null ? undefined : travelMadeConvenientData.title,
        preset: "h4",
        style: styles.textTitle,
        numberOfLines: 1
      }), (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
        horizontal: true,
        data: travelMadeConvenientList,
        renderItem: function renderItem(_ref2) {
          var item = _ref2.item,
            index = _ref2.index;
          return (0, _jsxRuntime.jsx)(_itemTravelMadeConvenient.ItemTravelMadeConvenient, {
            item: item,
            onPress: function onPress(itemData) {
              return travelMadeConvenientItemOnPress(itemData);
            },
            testID: `${testID}__ItemTravelMadeConvenient__${index}`,
            accessibilityLabel: `${accessibilityLabel}__ItemTravelMadeConvenient__${index}`
          });
        },
        contentContainerStyle: styles.listContainer,
        keyExtractor: function keyExtractor(_, index) {
          return `key_travel_made_convenient_${index}`;
        },
        showsHorizontalScrollIndicator: false,
        testID: `${testID}__FlatList`,
        accessibilityLabel: `${accessibilityLabel}__FlatList`
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    bottomContainer: {
      margin: 12,
      width: "auto"
    },
    cardContainer: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderBottomLeftRadius: 16,
      borderBottomRightRadius: 16,
      elevation: 5,
      height: 130,
      justifyContent: "flex-end",
      position: "absolute",
      shadowColor: _theme.color.palette.almostBlackGrey,
      shadowOffset: {
        height: 6,
        width: 0
      },
      shadowOpacity: 0.2,
      shadowRadius: 20,
      width: 192
    },
    container: {
      marginLeft: 24
    },
    listContainer: {
      paddingLeft: 24
    },
    textTitle: {
      marginHorizontal: 24
    },
    thumbnailStyles: {
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      height: 120,
      width: 192
    },
    titleLoading: {
      borderRadius: 8,
      height: 22,
      marginBottom: 16,
      marginLeft: 24,
      width: "50%"
    }
  });
  var _default = exports.default = ListTravelMadeConvenient;
