  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var styles = _reactNative.StyleSheet.create({
    activeCheckboxStyles: {
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "bold"
      })
    },
    applyButtonStyle: {
      borderRadius: 60,
      flexGrow: 2
    },
    applyFilterButtonStyle: {
      color: _theme.color.palette.almostWhiteGrey,
      fontSize: 16,
      fontStyle: "normal",
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "bold"
      }),
      lineHeight: 24,
      textAlign: "center"
    },
    areaContainerCheckboxStyle: {
      display: "flex",
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "space-between"
    },
    areaContent: {
      paddingBottom: 32,
      paddingTop: 24
    },
    areaLabel: Object.assign({}, _text.presets.subTitleBold, {
      color: _theme.color.palette.almostBlackGrey,
      fontStyle: "normal",
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "bold"
      }),
      textAlign: "left"
    }),
    bottomSheetContainer: {
      paddingHorizontal: 24,
      paddingVertical: 22
    },
    bottomSheetStyle: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      height: 673,
      overflow: "hidden",
      width: "100%"
    },
    checkboxAreaContainer: {
      width: width / 2 - 31
    },
    checkboxAreaStyle: {
      paddingVertical: 16
    },
    checkboxContainer: {
      borderBottomColor: _theme.color.palette.lightGrey,
      borderBottomWidth: 1,
      paddingVertical: 16,
      width: "100%"
    },
    clearButtonStyle: {
      marginRight: 13,
      width: width / 2 - 31
    },
    clearFilterButtonStyle: {
      color: _theme.color.palette.gradientColor1Start,
      fontSize: 16,
      fontStyle: "normal",
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "bold"
      }),
      lineHeight: 24,
      textAlign: "center"
    },
    headerFilter: {
      display: "flex",
      flexDirection: "row",
      width: "100%"
    },
    locationContent: {
      height: 364,
      marginTop: 41
    },
    locationFooter: {
      display: "flex",
      flexDirection: "row",
      justifyContent: "space-between",
      paddingTop: 10
    },
    locationLabel: Object.assign({}, _text.presets.h3, {
      color: _theme.color.palette.almostBlackGrey,
      fontStyle: "normal",
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "bold"
      }),
      textAlign: "left"
    }),
    outlineStyle: {
      borderColor: _theme.color.palette.darkGrey
    },
    overlayStyle: {
      height: "100%",
      width: "100%"
    },
    rightHeader: {
      width: 24
    },
    textCheckboxAreaStyles: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey,
      fontWeight: "400",
      maxWidth: width / 2 - 60
    }),
    textCheckboxStyles: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey,
      fontWeight: "400",
      maxWidth: width - 84
    }),
    titleSheet: Object.assign({
      color: _theme.color.palette.almostBlackGrey
    }, _text.presets.subTitleBold, {
      flexGrow: 2,
      textAlign: "center"
    })
  });
  var _default = exports.default = styles;
