  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _lodash = _$$_REQUIRE(_dependencyMap[9]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _text = _$$_REQUIRE(_dependencyMap[12]);
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _checkbox = _$$_REQUIRE(_dependencyMap[14]);
  var _icons = _$$_REQUIRE(_dependencyMap[15]);
  var _button = _$$_REQUIRE(_dependencyMap[16]);
  var _aemRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[17]));
  var _loadingModal = _$$_REQUIRE(_dependencyMap[18]);
  var _i18n = _$$_REQUIRE(_dependencyMap[19]);
  var _errorOverlay = _$$_REQUIRE(_dependencyMap[20]);
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[21]);
  var _theme = _$$_REQUIRE(_dependencyMap[22]);
  var _index = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[23]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[24]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var TAG_NAME_ALL = "all_local";
  var locationFilterAll = {
    filterType: "locations",
    sequenceNumber: 1,
    tagName: TAG_NAME_ALL,
    tagTitle: (0, _i18n.translate)("airportLanding.all")
  };
  var FilterLocationFacilitiesAndServices = function FilterLocationFacilitiesAndServices(props) {
    var _props$testID = props.testID,
      testID = _props$testID === undefined ? "FilterLocationFacilitiesAndServices" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "FilterLocationFacilitiesAndServices" : _props$accessibilityL,
      closeSheet = props.closeSheet,
      isModalVisible = props.isModalVisible,
      handleApplyFilter = props.handleApplyFilter,
      handlesetAreaSelected = props.handlesetAreaSelected,
      handleSetLocationSelected = props.handleSetLocationSelected,
      listAreaSelected = props.listAreaSelected,
      listLocationSelected = props.listLocationSelected;
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useState = (0, _react.useState)({}),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      checkedLocationState = _useState2[0],
      setCheckedLocationState = _useState2[1];
    var _useState3 = (0, _react.useState)({}),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      checkedAreaState = _useState4[0],
      setCheckedAreaState = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isNoConnection = _useState6[0],
      setNoConnection = _useState6[1];
    var preCheckedState = (0, _react.useRef)({});
    var filterByLocationAirportAemData = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.FILTER_LOCATION_FACILITIES_SERVICE));
    var loading = (0, _lodash.get)(filterByLocationAirportAemData, "loading");
    var error = (0, _lodash.get)(filterByLocationAirportAemData, "error");
    var locationData = (0, _lodash.get)(filterByLocationAirportAemData, "data.list.0.childTags", []);
    var areaData = (0, _lodash.get)(filterByLocationAirportAemData, "data.list.1.childTags", []);
    var filterData = [].concat((0, _toConsumableArray2.default)(locationData), (0, _toConsumableArray2.default)(areaData));
    var syncCheckedState = function syncCheckedState() {
      if (listAreaSelected) {
        var tempArea = {};
        listAreaSelected == null || listAreaSelected.forEach(function (e) {
          var arrFind = areaData == null ? undefined : areaData.find(function (ex) {
            return (ex == null ? undefined : ex.tagTitle) === e;
          });
          if (arrFind != null && arrFind.tagName) {
            tempArea[arrFind == null ? undefined : arrFind.tagName] = true;
          }
        });
        setCheckedAreaState(tempArea);
      }
      if (listLocationSelected) {
        var tempLocation = {};
        listLocationSelected == null || listLocationSelected.forEach(function (e) {
          var arrFind = locationData.find(function (ex) {
            return (ex == null ? undefined : ex.tagCode) === e;
          });
          if (arrFind != null && arrFind.tagName) {
            tempLocation[arrFind == null ? undefined : arrFind.tagName] = true;
          }
        });
        if ((0, _lodash.size)(listLocationSelected) === (0, _lodash.size)(locationData)) {
          tempLocation[TAG_NAME_ALL] = true;
        }
        setCheckedLocationState(tempLocation);
      }
    };
    var fetchData = function fetchData() {
      dispatch(_aemRedux.default.getAemConfigData({
        name: _aemRedux.AEM_PAGE_NAME.FILTER_LOCATION_FACILITIES_SERVICE,
        pathName: "getLocationFilterFacilitiesService"
      }));
    };
    (0, _react.useEffect)(function () {
      if (isModalVisible && (0, _lodash.isEmpty)(filterByLocationAirportAemData)) {
        var checkInternet = /*#__PURE__*/function () {
          var _ref = (0, _asyncToGenerator2.default)(function* () {
            var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
              isConnected = _yield$NetInfo$fetch.isConnected;
            if (!isConnected) {
              setNoConnection(true);
            } else {
              fetchData();
              (0, _lodash.set)(preCheckedState, "current.checkedLocationState", checkedLocationState);
              (0, _lodash.set)(preCheckedState, "current.checkedAreaState", checkedAreaState);
            }
          });
          return function checkInternet() {
            return _ref.apply(this, arguments);
          };
        }();
        checkInternet();
      }
    }, [isModalVisible]);
    var handleOnCheckboxChange = function handleOnCheckboxChange(tagName, value) {
      var checkBoxState = Object.assign({}, checkedLocationState, (0, _defineProperty2.default)({}, tagName, value));
      var locationCheckedData = Object.entries(checkBoxState).filter(function (_ref2) {
        var _ref3 = (0, _slicedToArray2.default)(_ref2, 2),
          key = _ref3[0],
          newValue = _ref3[1];
        return newValue && key !== TAG_NAME_ALL;
      });
      var checkBoxAllValue = (locationCheckedData == null ? undefined : locationCheckedData.length) === (locationData == null ? undefined : locationData.length);
      setCheckedLocationState(Object.assign({}, checkBoxState, (0, _defineProperty2.default)({}, TAG_NAME_ALL, checkBoxAllValue)));
    };
    var handleOnCheckedAll = function handleOnCheckedAll(value) {
      var checkedAll = [locationFilterAll].concat((0, _toConsumableArray2.default)(locationData)).reduce(function (currentData, tag) {
        currentData[tag.tagName] = value;
        return currentData;
      }, {});
      setCheckedLocationState(checkedAll);
    };
    var getCheckedState = function getCheckedState(checkedState) {
      return Object.entries(checkedState).reduce(function (previousValue, _ref4) {
        var _ref5 = (0, _slicedToArray2.default)(_ref4, 2),
          key = _ref5[0],
          value = _ref5[1];
        if (value && key !== TAG_NAME_ALL) {
          // Recheck checked item existing filter list
          var isExistFilterList = filterData.some(function (item) {
            return (item == null ? undefined : item.tagName) === key;
          });
          if (isExistFilterList) {
            previousValue.push(key);
          }
        }
        return previousValue;
      }, []);
    };
    var setLocationSelected = function setLocationSelected(newCheckedLocationState) {
      return Object.entries(newCheckedLocationState).reduce(function (previousValue, _ref6) {
        var _ref7 = (0, _slicedToArray2.default)(_ref6, 2),
          key = _ref7[0],
          value = _ref7[1];
        if (value && key !== TAG_NAME_ALL) {
          var isExistFilterList = filterData.find(function (item) {
            return item.tagName === key;
          });
          if (isExistFilterList) {
            previousValue.push(isExistFilterList == null ? undefined : isExistFilterList.tagCode);
          }
        }
        return previousValue.sort();
      }, []);
    };
    var setAreaSelected = function setAreaSelected(newCheckedAreaState) {
      return Object.entries(newCheckedAreaState).reduce(function (previousValue, _ref8) {
        var _ref9 = (0, _slicedToArray2.default)(_ref8, 2),
          key = _ref9[0],
          value = _ref9[1];
        if (value) {
          var isExistFilterList = filterData.find(function (item) {
            return item.tagName === key;
          });
          if (isExistFilterList) {
            previousValue.push(isExistFilterList == null ? undefined : isExistFilterList.tagTitle);
          }
        }
        return previousValue.sort();
      }, []);
    };
    var onApplyFilter = function onApplyFilter() {
      handleSetLocationSelected(setLocationSelected(checkedLocationState));
      handlesetAreaSelected(setAreaSelected(checkedAreaState));
      var checkedLocation = getCheckedState(checkedLocationState);
      var checkedArea = getCheckedState(checkedAreaState);
      handleApplyFilter(checkedLocation, checkedArea);
    };
    var onClosedSheet = function onClosedSheet() {
      closeSheet();
    };
    var onClearAllFilter = function onClearAllFilter() {
      setCheckedLocationState({});
      setCheckedAreaState({});
    };
    var onReloadData = function onReloadData() {
      var onReload = /*#__PURE__*/function () {
        var _ref0 = (0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch2.isConnected;
          if (isConnected) {
            fetchData();
            setNoConnection(false);
          }
        });
        return function onReload() {
          return _ref0.apply(this, arguments);
        };
      }();
      onReload();
    };
    var isFilterApplied = (0, _react.useMemo)(function () {
      var checkedData = Object.entries(Object.assign({}, checkedLocationState, checkedAreaState)).filter(function (_ref1) {
        var _ref10 = (0, _slicedToArray2.default)(_ref1, 2),
          key = _ref10[0],
          newValue = _ref10[1];
        return newValue && key !== TAG_NAME_ALL;
      });
      return (checkedData == null ? undefined : checkedData.length) > 0;
    }, [checkedLocationState, checkedAreaState]);
    var renderContent = function renderContent() {
      if (isNoConnection) {
        return (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
          reload: true,
          header: false,
          headerBackgroundColor: "transparent",
          hideScreenHeader: false,
          visible: true,
          testID: `${testID}__ErrorOverlayNoConnection`,
          accessibilityLabel: `${accessibilityLabel}__ErrorOverlayNoConnection`,
          onReload: onReloadData,
          noInternetOverlayStyle: _index.default.overlayStyle
        });
      }
      if (error) {
        return (0, _jsxRuntime.jsx)(_errorOverlay.ErrorOverlay, {
          reload: true,
          header: false,
          headerBackgroundColor: "transparent",
          hideScreenHeader: false,
          visible: true,
          onReload: onReloadData,
          testID: `${testID}__ErrorOverlay`,
          accessibilityLabel: `${accessibilityLabel}__ErrorOverlay`,
          variant: _errorOverlay.ErrorOverlayVariant.VARIANTSECTION,
          overlayStyle: _index.default.overlayStyle
        });
      }
      return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _index.default.locationContent,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: _index.default.locationLabel,
            tx: "airportLanding.location"
          }), (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
            data: [locationFilterAll].concat((0, _toConsumableArray2.default)(locationData)),
            renderItem: function renderItem(_ref11) {
              var item = _ref11.item,
                index = _ref11.index;
              return (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: _index.default.checkboxContainer,
                children: (0, _jsxRuntime.jsx)(_checkbox.Checkbox, {
                  value: (0, _lodash.get)(checkedLocationState, item.tagName, false),
                  onToggle: function onToggle(value) {
                    if (index === 0) {
                      handleOnCheckedAll(value);
                      return;
                    }
                    handleOnCheckboxChange(item.tagName, value);
                  },
                  testID: `${testID}__CheckBoxFilterLocations__${index}`,
                  accessibilityLabel: `${accessibilityLabel}__CheckBoxFilterLocations__${index}`,
                  text: item.tagTitle,
                  textStyle: Object.assign({}, _index.default.textCheckboxStyles, (0, _lodash.get)(checkedLocationState, item.tagName, false) ? _index.default.activeCheckboxStyles : {}),
                  outlineStyle: _index.default.outlineStyle
                })
              });
            },
            horizontal: false,
            showsVerticalScrollIndicator: false,
            keyExtractor: function keyExtractor(_, index) {
              return index.toString();
            }
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _index.default.areaContent,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: _index.default.areaLabel,
            tx: "airportLanding.area"
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _index.default.areaContainerCheckboxStyle,
            children: areaData.map(function (tag, index) {
              return (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: _index.default.checkboxAreaContainer,
                children: (0, _jsxRuntime.jsx)(_checkbox.Checkbox, {
                  value: (0, _lodash.get)(checkedAreaState, tag.tagName, false),
                  onToggle: function onToggle(value) {
                    return setCheckedAreaState(Object.assign({}, checkedAreaState, (0, _defineProperty2.default)({}, tag.tagName, value)));
                  },
                  testID: `${testID}__CheckBoxFilterArea__${index}`,
                  accessibilityLabel: `${accessibilityLabel}__CheckBoxFilterArea__${index}`,
                  text: tag.tagTitle,
                  textStyle: Object.assign({}, _index.default.textCheckboxAreaStyles, (0, _lodash.get)(checkedAreaState, tag.tagName, false) ? _index.default.activeCheckboxStyles : {}),
                  style: _index.default.checkboxAreaStyle,
                  outlineStyle: _index.default.outlineStyle
                })
              }, `CheckBoxFilterArea__${index}`);
            })
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _index.default.locationFooter,
          children: [isFilterApplied ? (0, _jsxRuntime.jsx)(_button.Button, {
            tx: "airportLanding.clearAll",
            typePreset: "secondary",
            onPress: onClearAllFilter,
            statePreset: "default",
            backgroundPreset: "light",
            textPreset: "buttonLarge",
            textStyle: _index.default.clearFilterButtonStyle,
            style: _index.default.clearButtonStyle,
            testID: `${testID}__TouchableClearFilterLocations`,
            accessibilityLabel: `${accessibilityLabel}__TouchableClearFilterLocations`
          }) : null, (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
            style: _index.default.applyButtonStyle,
            start: {
              x: 0,
              y: 1
            },
            end: {
              x: 1,
              y: 0
            },
            colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
            children: (0, _jsxRuntime.jsx)(_button.Button, {
              tx: "airportLanding.applyFilters",
              typePreset: "secondary",
              onPress: onApplyFilter,
              statePreset: "default",
              backgroundPreset: "dark",
              textPreset: "buttonLarge",
              textStyle: _index.default.applyFilterButtonStyle,
              testID: `${testID}__TouchableApplyFiltersLocations`,
              accessibilityLabel: `${accessibilityLabel}__TouchableAppFiltersLocations`
            })
          })]
        })]
      });
    };
    if (loading) {
      return (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
        visible: true,
        isPopUp: false
      });
    }
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      children: (0, _jsxRuntime.jsx)(_bottomSheet.default, {
        isModalVisible: isModalVisible,
        onClosedSheet: onClosedSheet,
        containerStyle: _index.default.bottomSheetStyle,
        stopDragCollapse: true,
        onBackPressHandle: onClosedSheet,
        animationInTiming: 200,
        animationOutTiming: 200,
        onModalWillShow: syncCheckedState,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _index.default.bottomSheetContainer,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _index.default.headerFilter,
            children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: _index.default.rightHeader
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              style: _index.default.titleSheet,
              tx: "airportLanding.filterByLocation"
            }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              onPress: onClosedSheet,
              testID: `${testID}__CloseFilter`,
              accessibilityLabel: `${accessibilityLabel}__CloseFilter`,
              children: (0, _jsxRuntime.jsx)(_icons.CrossBlue, {
                width: 24,
                height: 24
              })
            })]
          }), renderContent()]
        })
      })
    });
  };
  var _default = exports.default = FilterLocationFacilitiesAndServices;
