  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _lodash = _$$_REQUIRE(_dependencyMap[7]);
  var _attractionsFacilitiesServices = _$$_REQUIRE(_dependencyMap[8]);
  var _text = _$$_REQUIRE(_dependencyMap[9]);
  var _exploreItemType = _$$_REQUIRE(_dependencyMap[10]);
  var _theme = _$$_REQUIRE(_dependencyMap[11]);
  var _search = _$$_REQUIRE(_dependencyMap[12]);
  var _search2 = _$$_REQUIRE(_dependencyMap[13]);
  var _icons = _$$_REQUIRE(_dependencyMap[14]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[15]);
  var _components = _$$_REQUIRE(_dependencyMap[16]);
  var _native = _$$_REQUIRE(_dependencyMap[17]);
  var _searchIndex = _$$_REQUIRE(_dependencyMap[18]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[19]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[20]);
  var _aemRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[21]));
  var _baseImageBackground = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[22]));
  var _adobe = _$$_REQUIRE(_dependencyMap[23]);
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[24]);
  var _saveFlightTravelOption = _$$_REQUIRE(_dependencyMap[25]);
  var _i18n = _$$_REQUIRE(_dependencyMap[26]);
  var _fly = _$$_REQUIRE(_dependencyMap[27]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[28]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[29]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[30]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var FacilitiesAndServices = function FacilitiesAndServices(props) {
    var _useContext;
    var _props$testID = props.testID,
      testID = _props$testID === undefined ? "FacilitiesAndServices" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "FacilitiesAndServices" : _props$accessibilityL,
      facilitiesAndServicesData = props.facilitiesAndServicesData,
      terminal = props.terminal,
      selectedTravelOption = props.selectedTravelOption;
    var navigation = (0, _native.useNavigation)();
    var dispatch = (0, _reactRedux.useDispatch)();
    var filterByLocationAirportAemData = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.FILTER_LOCATION_FACILITIES_SERVICE));
    var _useContext$Handlers = (_useContext = (0, _react.useContext)(_fly.FLY_CONTEXT)) == null ? undefined : _useContext.Handlers,
      flyDetailP1Flag = _useContext$Handlers.flyDetailP1Flag;
    var isFlightDetailP1 = (0, _remoteConfig.isFlagOnCondition)(flyDetailP1Flag);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isModalVisible = _useState2[0],
      setModalVisible = _useState2[1];
    var _useState3 = (0, _react.useState)(null),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      facilitiesServiceData = _useState4[0],
      setFacilitiesServiceData = _useState4[1];
    var listFacilitiesService = facilitiesAndServicesData == null ? undefined : facilitiesAndServicesData.contents;
    var _useState5 = (0, _react.useState)([]),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      listLocationSelected = _useState6[0],
      setLocationSelected = _useState6[1];
    var _useState7 = (0, _react.useState)([]),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      listAreaSelected = _useState8[0],
      setAreaSelected = _useState8[1];
    var locationData = (0, _lodash.get)(filterByLocationAirportAemData, "data.list.0.childTags", []);
    var areaData = (0, _lodash.get)(filterByLocationAirportAemData, "data.list.1.childTags", []);
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("ARIPORT_LANDING_FACILITIES_SERVICES"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var fetchData = function fetchData() {
      dispatch(_aemRedux.default.getAemConfigData({
        name: _aemRedux.AEM_PAGE_NAME.FILTER_LOCATION_FACILITIES_SERVICE,
        pathName: "getLocationFilterFacilitiesService"
      }));
    };
    (0, _react.useEffect)(function () {
      // Call AEM FILTER_LOCATION_FACILITIES_SERVICE API when navigating from the Flight Detail screen.
      // When filterByLocationAirportAemData is empty, it is not possible to compare the listLocationSelected parameters passed from Flight Detail.
      if (!isModalVisible && selectedTravelOption && (0, _lodash.isEmpty)(filterByLocationAirportAemData)) {
        var checkInternet = /*#__PURE__*/function () {
          var _ref = (0, _asyncToGenerator2.default)(function* () {
            var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
              isConnected = _yield$NetInfo$fetch.isConnected;
            if (isConnected) {
              fetchData();
            }
          });
          return function checkInternet() {
            return _ref.apply(this, arguments);
          };
        }();
        checkInternet();
      }
    }, [isModalVisible, selectedTravelOption, filterByLocationAirportAemData]);
    (0, _react.useEffect)(function () {
      var checkedLocation = [];
      var checkedArea = [];
      if (selectedTravelOption) {
        if (selectedTravelOption === _saveFlightTravelOption.TravelOption.iAmPicking) {
          setAreaSelected(["Public"]);
          checkedArea = ["public"];
        } else {
          setAreaSelected(["Public", "Transit"]);
          checkedArea = ["public", "transit"];
        }
        if (terminal) {
          setLocationSelected([`t${terminal}`]);
          checkedLocation = [`terminal-${terminal}`];
        } else {
          setLocationSelected(["t1", "t2", "t3", "t4", "tj"]);
          checkedLocation = ["terminal-1", "terminal-2", "terminal-3", "terminal-4", "terminal-j"];
        }
      } else {
        setAreaSelected([]);
        checkedArea = [];
        setLocationSelected([]);
        checkedLocation = [];
      }
      if (!((0, _lodash.isEmpty)(checkedLocation) && (0, _lodash.isEmpty)(checkedArea))) {
        handleApplyFilter(checkedLocation, checkedArea);
      }
    }, [terminal, selectedTravelOption]);
    var getLocationValueSelected = function getLocationValueSelected(checkedLocation) {
      if ((0, _lodash.isEmpty)(checkedLocation)) {
        return "Location (None)";
      }
      if (checkedLocation.length === (0, _lodash.size)(locationData)) {
        return "Location (All)";
      }
      var locationNameSelected = checkedLocation.map(function (locationName) {
        var matchingLocation = locationData.find(function (LocationObj) {
          return (LocationObj == null ? undefined : LocationObj.tagName) === locationName;
        });
        return matchingLocation == null ? undefined : matchingLocation.tagTitle;
      }).sort();
      return `Location (${locationNameSelected.join(", ")})`;
    };
    var getAreaValueSelected = function getAreaValueSelected(checkedArea) {
      if ((0, _lodash.isEmpty)(checkedArea)) {
        return "Area (None)";
      }
      if (checkedArea.length === (0, _lodash.size)(areaData)) {
        return "Area (All)";
      }
      var areaNameSelected = checkedArea.map(function (locationName) {
        var matchingArea = areaData.find(function (areaObj) {
          return (areaObj == null ? undefined : areaObj.tagName) === locationName;
        });
        return matchingArea == null ? undefined : matchingArea.tagTitle;
      }).sort();
      return `Area (${areaNameSelected.join(", ")})`;
    };
    var handleApplyFilter = function handleApplyFilter(checkedLocation, checkedArea) {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppAirportLanding, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppAirportLanding, `${facilitiesAndServicesData == null ? undefined : facilitiesAndServicesData.title} | Apply Filters | ${getLocationValueSelected(checkedLocation)}, ${getAreaValueSelected(checkedArea)}`));
      (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
      setModalVisible(false);
      if ((0, _lodash.isEmpty)(listFacilitiesService)) {
        return;
      }
      if ((checkedLocation == null ? undefined : checkedLocation.length) === 0 && (checkedArea == null ? undefined : checkedArea.length) === 0) {
        setFacilitiesServiceData(null);
        return;
      }
      var listFacilitiesServiceFilter = listFacilitiesService.filter(function (item) {
        var area = item.area,
          location = item.location;
        if ((checkedLocation == null ? undefined : checkedLocation.length) > 0 && (checkedArea == null ? undefined : checkedArea.length) > 0) {
          return filterData(location, checkedLocation) && filterData(area, checkedArea);
        }
        if ((checkedLocation == null ? undefined : checkedLocation.length) > 0) {
          return filterData(location, checkedLocation);
        }
        if ((checkedArea == null ? undefined : checkedArea.length) > 0) {
          return filterData(area, checkedArea);
        }
        return false;
      });
      setFacilitiesServiceData(listFacilitiesServiceFilter);
    };
    var filterData = function filterData(data, checkedFilter) {
      return data.some(function (item) {
        return checkedFilter.includes(item.tagName);
      });
    };
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      return function () {
        closeSheet();
      };
    }, []));
    var openSheet = function openSheet() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppAirportLanding, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppAirportLanding, `${facilitiesAndServicesData == null ? undefined : facilitiesAndServicesData.title} | ${getFilterLable()}`));
      (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(true);
      setModalVisible(true);
    };
    var closeSheet = function closeSheet() {
      (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
      setModalVisible(false);
    };
    var onItemFacilitiesServiceClick = function onItemFacilitiesServiceClick(item) {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppAirportLanding, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppAirportLanding, `${facilitiesAndServicesData == null ? undefined : facilitiesAndServicesData.title} | ${item == null ? undefined : item.title}`));
      if (item != null && item.navigation) {
        var _ref2 = (item == null ? undefined : item.navigation) || "",
          type = _ref2.type,
          value = _ref2.value;
        var _ref3 = item || {},
          redirect = _ref3.redirect;
        if (!type || !value) return;
        handleNavigation(type, value, redirect);
      }
    };
    var getLocationFilterText = function getLocationFilterText(newListLocationSelected) {
      if ((0, _lodash.isEmpty)(newListLocationSelected) || (0, _lodash.size)(locationData) === (0, _lodash.size)(newListLocationSelected)) {
        return "all terminals";
      }
      return (newListLocationSelected == null ? undefined : newListLocationSelected.map(function (item) {
        if (item === "tj") {
          var _locationData$find;
          return locationData == null || (_locationData$find = locationData.find(function (e) {
            return (e == null ? undefined : e.tagCode) === "tj";
          })) == null ? undefined : _locationData$find.tagTitle;
        }
        return item == null ? undefined : item.toUpperCase();
      }).join(", ")) + "";
    };
    var getAreasFilterText = function getAreasFilterText(listAreasSelected) {
      if ((0, _lodash.isEmpty)(listAreasSelected)) return "Public & Transit areas";
      return (listAreaSelected == null ? undefined : listAreaSelected.map(function (item) {
        return item;
      }).join(" & ")) + " areas";
    };
    var getFilterLable = function getFilterLable() {
      if ((0, _lodash.isEmpty)(listLocationSelected) && (0, _lodash.isEmpty)(listAreaSelected)) {
        return !isFlightDetailP1 ? facilitiesAndServicesData == null ? undefined : facilitiesAndServicesData.informativeText : selectedTravelOption === _saveFlightTravelOption.TravelOption.iAmPicking ? (0, _i18n.translate)("airportLanding.allLocationsInPublic") : (0, _i18n.translate)("airportLanding.allLocationsInPublicTransit");
      } else {
        return getAreasFilterText(listAreaSelected) + " in " + getLocationFilterText(listLocationSelected);
      }
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      children: [(0, _jsxRuntime.jsx)(_baseImageBackground.default, {
        source: {
          uri: (0, _screenHelper.getUriImage)(facilitiesAndServicesData == null ? undefined : facilitiesAndServicesData.heroImage)
        },
        imageStyle: styles.imageBackground,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          text: facilitiesAndServicesData == null ? undefined : facilitiesAndServicesData.title,
          preset: "h4",
          style: styles.textTitle
        })
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.searchBarContainer,
        children: (0, _jsxRuntime.jsx)(_search2.Search, {
          type: _search.SearchBarVariations.mainPage,
          style: styles.searchStyle,
          placeholderText: facilitiesAndServicesData == null ? undefined : facilitiesAndServicesData.placeholderText,
          testID: `${testID}__SearchFacilitiesAndServices`,
          accessibilityLabel: `${accessibilityLabel}__SearchFacilitiesAndServices`,
          onPressed: function onPressed() {
            (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppAirportLanding, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppAirportLanding, `${facilitiesAndServicesData == null ? undefined : facilitiesAndServicesData.title} | Search`));
            // @ts-ignore
            navigation.navigate("search", {
              module: _searchIndex.SearchIndex.airport,
              sourcePage: "CApp_Airport_Landing"
            });
          }
        })
      }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: styles.linkContainerStyle,
        testID: `${testID}__TouchableFacilitiesAndServices`,
        accessibilityLabel: `${accessibilityLabel}__TouchableFacilitiesAndServices`,
        onPress: openSheet,
        children: [(0, _jsxRuntime.jsx)(_icons.Location, {
          height: 24,
          width: 24
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          text: getFilterLable(),
          numberOfLines: 1,
          style: styles.linkTextStyle,
          preset: "textLink"
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
        data: facilitiesServiceData || listFacilitiesService,
        renderItem: function renderItem(_ref4) {
          var item = _ref4.item,
            index = _ref4.index;
          return (0, _jsxRuntime.jsx)(_attractionsFacilitiesServices.AttractionsFacilitiesServices, {
            type: _exploreItemType.ExploreItemTypeEnum.default,
            title: item == null ? undefined : item.title,
            onPressed: function onPressed() {
              return onItemFacilitiesServiceClick(item);
            },
            imageUrl: (0, _screenHelper.getUriImage)(item == null ? undefined : item.image),
            attractionId: undefined,
            locationDisplayText: item == null ? undefined : item.locationDisplayText,
            testID: `${testID}__ItemFacilitiesAndServices__${index}`,
            accessibilityLabel: `${accessibilityLabel}__ItemFacilitiesAndServices__${index}`
          });
        },
        keyExtractor: function keyExtractor(_, index) {
          return `key_travelling_item_${index}`;
        },
        numColumns: 2,
        style: styles.flatListStyle,
        contentContainerStyle: styles.contentContainerStyle,
        scrollEnabled: false,
        testID: `${testID}__FlatListFacilitiesAndServices`,
        accessibilityLabel: `${accessibilityLabel}__FlatListFacilitiesAndServices`
      }), (0, _jsxRuntime.jsx)(_components.FilterLocationFacilitiesAndServices, {
        handleSetLocationSelected: setLocationSelected,
        handlesetAreaSelected: setAreaSelected,
        testID: testID,
        accessibilityLabel: accessibilityLabel,
        closeSheet: closeSheet,
        handleApplyFilter: handleApplyFilter,
        isModalVisible: isModalVisible,
        listLocationSelected: listLocationSelected,
        listAreaSelected: listAreaSelected
      })]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    contentContainerStyle: {
      paddingBottom: 20
    },
    flatListStyle: {
      paddingBottom: 20,
      paddingHorizontal: 16,
      width: "100%"
    },
    imageBackground: {
      height: 150,
      width: "100%"
    },
    linkContainerStyle: {
      alignItems: "center",
      alignSelf: "flex-start",
      flexDirection: "row",
      height: 28,
      justifyContent: "center",
      marginBottom: 16,
      marginHorizontal: 20,
      marginTop: 18
    },
    linkTextStyle: {
      color: _theme.color.palette.gradientColor1Start,
      marginLeft: 4
    },
    searchBarContainer: {
      alignItems: "center",
      flexDirection: "row",
      marginHorizontal: 24,
      marginTop: 24
    },
    searchStyle: {
      flex: 1
    },
    textTitle: {
      color: _theme.color.palette.whiteGrey,
      marginLeft: 24,
      marginTop: 49,
      width: "70%"
    }
  });
  var _default = exports.default = FacilitiesAndServices;
