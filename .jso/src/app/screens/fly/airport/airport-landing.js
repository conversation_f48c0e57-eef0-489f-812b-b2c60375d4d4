  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.AirportLanding = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _navigationUtilities = _$$_REQUIRE(_dependencyMap[8]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[9]);
  var _shortcutLink = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _shortcutLink2 = _$$_REQUIRE(_dependencyMap[11]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[12]);
  var _announcements = _$$_REQUIRE(_dependencyMap[13]);
  var _announcements2 = _$$_REQUIRE(_dependencyMap[14]);
  var _component = _$$_REQUIRE(_dependencyMap[15]);
  var _listTravelMadeConvenient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _facilitiesServices = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _airportLandingRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[18]));
  var _airportType = _$$_REQUIRE(_dependencyMap[19]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[20]);
  var _constants = _$$_REQUIRE(_dependencyMap[21]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[22]));
  var _errorOverlay = _$$_REQUIRE(_dependencyMap[23]);
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[24]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[25]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[26]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[27]);
  var _bottomSheetVerifyEmail = _$$_REQUIRE(_dependencyMap[28]);
  var _adobe = _$$_REQUIRE(_dependencyMap[29]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[30]);
  var _native = _$$_REQUIRE(_dependencyMap[31]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[32]);
  var _lodash = _$$_REQUIRE(_dependencyMap[33]);
  var _bottomTabs = _$$_REQUIRE(_dependencyMap[34]);
  var _fab = _$$_REQUIRE(_dependencyMap[35]);
  var _reactNativeReanimated = _$$_REQUIRE(_dependencyMap[36]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[37]);
  var _mytravelRedux = _$$_REQUIRE(_dependencyMap[38]);
  var _suspend = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[39]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[40]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var loadingData = Array(5).fill(null);
  var shimmerColorsPrimary = [_theme.color.palette.lightestGrey, _theme.color.palette.almostWhiteGrey, _theme.color.palette.lightestGrey];
  var COMPONENT_NAME = "AirportLanding";
  var AirportLanding = exports.AirportLanding = function AirportLanding(_ref) {
    var _route$params2, _route$params3;
    var navigation = _ref.navigation,
      route = _ref.route;
    var moveToElement = (0, _react.useMemo)(function () {
      var _route$params;
      return route == null || (_route$params = route.params) == null ? undefined : _route$params.moveToElement;
    }, [route == null || (_route$params2 = route.params) == null ? undefined : _route$params2.moveToElement]);
    var isFocused = (0, _native.useIsFocused)();
    var scrollViewRef = (0, _react.useRef)(null);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isNoConnection = _useState2[0],
      setNoConnection = _useState2[1];
    var _useState3 = (0, _react.useState)(0),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      locationToScroll = _useState4[0],
      setLocationToScroll = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      showResend = _useState6[0],
      setShowResend = _useState6[1];
    var dispatch = (0, _reactRedux.useDispatch)();
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var getMetaDataAirportLandingLoading = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.getMetaDataAirportLandingLoading);
    var metaDataAirportLanding = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.metaDataAirportLanding);
    var getChangiGameUrlLoading = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.getChangiGameUrlLoading);
    var getChangiGameUrlPathData = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.getChangiGameUrlPathData);
    var metaDataAirportLandingError = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.metaDataAirportLandingError);
    var myTravelFlightsPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.myTravelFlightsPayload);
    var _useState7 = (0, _react.useState)(null),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      changiGame = _useState8[0],
      getChangiGame = _useState8[1];
    var _useState9 = (0, _react.useState)(null),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      shortcutLinksProcessed = _useState0[0],
      setShortcutLinksProcessed = _useState0[1];
    var _useHandleScroll = (0, _navigationUtilities.useHandleScroll)(),
      handleScroll = _useHandleScroll.handleScroll;
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("ANNOUCEMENT_AIRPORT"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var _useState1 = (0, _react.useState)(false),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      mapRMFlag = _useState10[0],
      setMapRMFlag = _useState10[1];
    var bottomTabHeight = (0, _bottomTabs.useBottomTabBarHeight)();
    var fabOpacity = (0, _reactNativeReanimated.useSharedValue)(0);
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)("Fly_AirportLanding");
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      (0, _adobe.commonTrackingScreen)("Fly_AirportLanding", (0, _screenHook.getPreviousScreen)(), isLoggedIn);
    }, []));
    (0, _react.useEffect)(function () {
      var checkInternet = /*#__PURE__*/function () {
        var _ref2 = (0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch.isConnected;
          if (!isConnected) {
            setNoConnection(true);
          } else {
            dispatch(_airportLandingRedux.default.getMetaDataAirportLandingRequest());
          }
        });
        return function checkInternet() {
          return _ref2.apply(this, arguments);
        };
      }();
      checkInternet();
      return function () {
        dispatch(_airportLandingRedux.default.clearChangiGameUrl());
      };
    }, []);
    (0, _react.useEffect)(function () {
      var _getChangiGameUrlPath;
      if (getChangiGameUrlPathData != null && (_getChangiGameUrlPath = getChangiGameUrlPathData.data) != null && (_getChangiGameUrlPath = _getChangiGameUrlPath.getChangiGames) != null && _getChangiGameUrlPath.url) {
        var _getChangiGameUrlPath2;
        var url = getChangiGameUrlPathData == null || (_getChangiGameUrlPath2 = getChangiGameUrlPathData.data) == null || (_getChangiGameUrlPath2 = _getChangiGameUrlPath2.getChangiGames) == null ? undefined : _getChangiGameUrlPath2.url;
        navigation.navigate(_constants.NavigationConstants.webview, {
          uri: url
        });
      }
    }, [getChangiGameUrlPathData]);
    (0, _react.useEffect)(function () {
      if (moveToElement === "facilitiesServices" && locationToScroll) {
        setTimeout(function () {
          var _scrollViewRef$curren;
          scrollViewRef == null || (_scrollViewRef$curren = scrollViewRef.current) == null || _scrollViewRef$curren.scrollTo(locationToScroll);
        }, 700);
      }
    }, [moveToElement, locationToScroll, route == null || (_route$params3 = route.params) == null ? undefined : _route$params3.rand]);
    var onScroll = function onScroll(event) {
      if (event.nativeEvent.contentOffset.y > 200) {
        fabOpacity.value = (0, _reactNativeReanimated.withTiming)(1, {
          duration: 250
        });
      } else {
        fabOpacity.value = (0, _reactNativeReanimated.withTiming)(0, {
          duration: 100
        });
      }
      handleScroll(event);
    };
    var contentContainerStyle = {
      paddingBottom: ((0, _reactNativeSafeAreaContext.useSafeAreaInsets)().bottom > 0 ? 50 : 18) + bottomTabHeight,
      paddingTop: 16
    };
    var handleRefresh = function handleRefresh() {
      dispatch(_airportLandingRedux.default.getMetaDataAirportLandingRequest());
    };
    var onPressTouch = function onPressTouch() {
      var _scrollViewRef$curren2;
      scrollViewRef == null || (_scrollViewRef$curren2 = scrollViewRef.current) == null || _scrollViewRef$curren2.scrollTo({
        y: 0,
        animated: true
      });
    };
    (0, _react.useEffect)(function () {
      if (changiGame && profilePayload) {
        var myTravelFlightDetails = myTravelFlightsPayload == null ? undefined : myTravelFlightsPayload.getMyTravelFlightDetails;
        var flightNo = "";
        if (myTravelFlightDetails && myTravelFlightDetails.length > 0) {
          var _myTravelFlightDetail;
          flightNo = (_myTravelFlightDetail = myTravelFlightDetails[myTravelFlightDetails.length - 1]) == null ? undefined : _myTravelFlightDetail.flightNumber;
        }
        getChangiGame(null);
        dispatch(_airportLandingRedux.default.getChangiGameUrlRequest("", flightNo, changiGame, profilePayload == null ? undefined : profilePayload.cardNo));
      }
    }, [changiGame, myTravelFlightsPayload, profilePayload]);
    (0, _react.useEffect)(function () {
      var fetchAtomRMConfig = function fetchAtomRMConfig() {
        var mapFlagEnable = (0, _remoteConfig.isFlagON)(_remoteConfig.REMOTE_CONFIG_FLAGS.ATOMS_MAP);
        setMapRMFlag(mapFlagEnable);
      };
      fetchAtomRMConfig();
    }, []);
    (0, _react.useEffect)(function () {
      var _metaDataAirportLandi;
      var links = metaDataAirportLanding == null || (_metaDataAirportLandi = metaDataAirportLanding[0]) == null ? undefined : _metaDataAirportLandi.contents;
      if (!(0, _lodash.isEmpty)(links)) {
        if (!mapRMFlag) {
          var _links;
          links = (_links = links) == null ? undefined : _links.filter(function (link) {
            var _link$navigation;
            return (link == null || (_link$navigation = link.navigation) == null ? undefined : _link$navigation.value) !== "atomMap";
          });
        }
        setShortcutLinksProcessed(links);
      }
    }, [metaDataAirportLanding, mapRMFlag]);
    var getShortcutLinkSectionTitle = (0, _react.useMemo)(function () {
      var _metaDataAirportLandi2;
      return (metaDataAirportLanding == null || (_metaDataAirportLandi2 = metaDataAirportLanding[0]) == null ? undefined : _metaDataAirportLandi2.title) || "SHORTCUT LINKS";
    }, [metaDataAirportLanding]);
    var shortcutLinkItemOnClick = function shortcutLinkItemOnClick(props) {
      var title = props.title,
        navigation = props.navigation;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppAirportLanding, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppAirportLanding, `${getShortcutLinkSectionTitle} | ${title || ""}`));
      var _ref3 = navigation || {},
        type = _ref3.type,
        value = _ref3.value;
      var _ref4 = props || {},
        redirect = _ref4.redirect;
      if (!type || !value) return;
      handleNavigation(type, value, redirect, {
        redirectFrom: _constants.CM24RedirectSource.QuickLinks,
        adobeTagName: _adobe.AdobeTagName.CAppFlyQuickLinks
      });
    };
    var announcementItemOnClick = function announcementItemOnClick(item) {
      if (item != null && item.navigation) {
        var _item$navigation = item == null ? undefined : item.navigation,
          type = _item$navigation.type,
          value = _item$navigation.value;
        var _ref5 = item || {},
          redirect = _ref5.redirect;
        if (!type || !value) return;
        handleNavigation(type, value, redirect);
      }
    };
    var renderViewByComponentType = function renderViewByComponentType(airportLandingItem) {
      var _airportLandingItem$c, _route$params4, _route$params5;
      switch (airportLandingItem.componentType) {
        case _airportType.componentType.shortcutLinkSession:
          return (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.shortcutLinkContainerStyle,
            children: (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
              data: shortcutLinksProcessed,
              showsHorizontalScrollIndicator: false,
              horizontal: true,
              keyExtractor: function keyExtractor(_, index) {
                return `${COMPONENT_NAME}-${airportLandingItem.componentType}-FlatList-${index}`;
              },
              contentContainerStyle: styles.paddingShortcutLink,
              renderItem: function renderItem(_ref6) {
                var item = _ref6.item,
                  index = _ref6.index;
                return (0, _jsxRuntime.jsx)(_shortcutLink.default, Object.assign({
                  style: styles.shortcutLinkStyle,
                  shimmerColorsPrimary: shimmerColorsPrimary,
                  type: _shortcutLink2.ShortcutLinkType.default
                }, item, {
                  icon: (0, _screenHelper.getUriImage)(item == null ? undefined : item.icon),
                  onPressed: function onPressed(props) {
                    return shortcutLinkItemOnClick(props);
                  },
                  testID: `${COMPONENT_NAME}__ShortcutLinkDefaultAirport`,
                  accessibilityLabel: `${COMPONENT_NAME}__ShortcutLinkDefaultAirport`
                }), `${COMPONENT_NAME}-${airportLandingItem.componentType}-FlatListItem-${index}`);
              }
            })
          }, `${COMPONENT_NAME}-${airportLandingItem.componentType}`);
        case _airportType.componentType.announcementSession:
          if ((airportLandingItem == null || (_airportLandingItem$c = airportLandingItem.contents) == null ? undefined : _airportLandingItem$c.length) > 0) {
            return (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: styles.announcenContainer,
              children: (0, _jsxRuntime.jsx)(_announcements.Announcements, {
                type: _announcements2.AnnouncementsType.default,
                announcementsData: airportLandingItem,
                onPressed: function onPressed(item) {
                  return announcementItemOnClick(item);
                },
                testID: `${COMPONENT_NAME}__Announcements`,
                accessibilityLabel: `${COMPONENT_NAME}__Announcements`
              })
            }, `${COMPONENT_NAME}-${airportLandingItem.componentType}`);
          } else {
            return (0, _jsxRuntime.jsx)(_reactNative2.View, {}, `${COMPONENT_NAME}-${airportLandingItem.componentType}`);
          }
        case _airportType.componentType.safeTravelSession:
          return (0, _jsxRuntime.jsx)(_component.SafeTravelComponent, {
            safeTravelData: airportLandingItem,
            testID: `${COMPONENT_NAME}__SafeTravelComponent`,
            accessibilityLabel: `${COMPONENT_NAME}__SafeTravelComponent`
          }, `${COMPONENT_NAME}-${airportLandingItem.componentType}`);
        case _airportType.componentType.travelMadeConvenientSession:
          return (0, _jsxRuntime.jsx)(_listTravelMadeConvenient.default, {
            travelMadeConvenientData: airportLandingItem,
            testID: `${COMPONENT_NAME}__ListTravelMadeConvenient`,
            accessibilityLabel: `${COMPONENT_NAME}__ListTravelMadeConvenient`
          }, `${COMPONENT_NAME}-${airportLandingItem.componentType}`);
        case _airportType.componentType.findYourWay:
          return (0, _jsxRuntime.jsx)(_component.FindYourWayComponent, {
            findYourWayData: airportLandingItem,
            testID: `${COMPONENT_NAME}__FindYourWayComponent`,
            accessibilityLabel: `${COMPONENT_NAME}__FindYourWayComponent`
          }, `${COMPONENT_NAME}-${airportLandingItem.componentType}`);
        case _airportType.componentType.specialAssistanceSession:
          return (0, _jsxRuntime.jsx)(_component.SpecialAssistanceComponent, {
            specialAssistanceData: airportLandingItem,
            testID: `${COMPONENT_NAME}__SpecialAssistanceComponent`,
            accessibilityLabel: `${COMPONENT_NAME}__SpecialAssistanceComponent`
          }, `${COMPONENT_NAME}-${airportLandingItem.componentType}`);
        case _airportType.componentType.facilitiesServiceSession:
          return (0, _jsxRuntime.jsx)(_reactNative2.View, {
            onLayout: function onLayout(e) {
              return setLocationToScroll(e.nativeEvent.layout.y);
            },
            children: (0, _jsxRuntime.jsx)(_facilitiesServices.default, {
              facilitiesAndServicesData: airportLandingItem,
              testID: `${COMPONENT_NAME}__FacilitiesServices`,
              accessibilityLabel: `${COMPONENT_NAME}__FacilitiesServices`,
              terminal: route == null || (_route$params4 = route.params) == null ? undefined : _route$params4.terminal,
              selectedTravelOption: route == null || (_route$params5 = route.params) == null ? undefined : _route$params5.selectedTravelOption
            })
          }, `${COMPONENT_NAME}-${airportLandingItem.componentType}`);
        default:
          break;
      }
    };
    if (getMetaDataAirportLandingLoading) {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.containerLoading,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: [styles.shortcutLinkContainerStyle, styles.loadingShortcutLink],
          children: loadingData.map(function (_, index) {
            return (0, _jsxRuntime.jsx)(_shortcutLink.default, {
              style: styles.shortcutLinkStyle,
              shimmerColorsPrimary: shimmerColorsPrimary,
              type: _shortcutLink2.ShortcutLinkType.loading
            }, index);
          })
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.announcenContainer,
          children: (0, _jsxRuntime.jsx)(_announcements.Announcements, {
            type: _announcements2.AnnouncementsType.loading
          })
        }), (0, _jsxRuntime.jsx)(_component.SafeTravelComponent, {
          type: _airportType.ComponentState.loading
        }), (0, _jsxRuntime.jsx)(_listTravelMadeConvenient.default, {
          type: _airportType.ComponentState.loading
        })]
      });
    }
    return (0, _jsxRuntime.jsxs)(_suspend.default, {
      freeze: !isFocused,
      children: [metaDataAirportLandingError || !metaDataAirportLanding ? (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.containerError,
        children: (0, _jsxRuntime.jsx)(_errorOverlay.ErrorOverlay, {
          reload: true,
          header: false,
          headerBackgroundColor: "transparent",
          hideScreenHeader: false,
          visible: true,
          onReload: handleRefresh,
          onBack: function onBack() {
            navigation.goBack();
          },
          testID: `${COMPONENT_NAME}__ErrorOverlay`,
          accessibilityLabel: `${COMPONENT_NAME}__ErrorOverlay`,
          variant: _errorOverlay.ErrorOverlayVariant.VARIANT1,
          ignoreShowNoInternet: true
        })
      }) : (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.container,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.ScrollView, {
          refreshControl: (0, _jsxRuntime.jsx)(_reactNative.RefreshControl, {
            refreshing: false,
            onRefresh: handleRefresh
          }),
          onScroll: onScroll,
          scrollEventThrottle: 1,
          ref: scrollViewRef,
          showsVerticalScrollIndicator: false,
          contentContainerStyle: contentContainerStyle,
          testID: `${COMPONENT_NAME}__ScrollViewAirportLanding`,
          accessibilityLabel: `${COMPONENT_NAME}__ScrollViewAirportLanding`,
          children: metaDataAirportLanding == null ? undefined : metaDataAirportLanding.map(function (airportLandingItem) {
            return renderViewByComponentType(airportLandingItem);
          })
        }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
          visible: getChangiGameUrlLoading
        }), (0, _jsxRuntime.jsx)(_bottomSheetVerifyEmail.BottomSheetVerifyEmail, {
          visible: showResend,
          email: profilePayload == null ? undefined : profilePayload.email,
          onHide: function onHide() {
            return setShowResend(false);
          },
          testID: `${COMPONENT_NAME}BottomSheetVerifyEmailVerifyEmail`
        }), (0, _jsxRuntime.jsx)(_fab.FAB, {
          opacity: fabOpacity,
          onPress: onPressTouch
        })]
      }), (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        header: false,
        headerBackgroundColor: "transparent",
        visible: isNoConnection,
        testID: `${COMPONENT_NAME}__ErrorOverlayNoConnection`,
        onReload: function onReload() {
          var checkConnection = /*#__PURE__*/function () {
            var _ref7 = (0, _asyncToGenerator2.default)(function* () {
              var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
                isConnected = _yield$NetInfo$fetch2.isConnected;
              if (isConnected) {
                handleRefresh();
                setNoConnection(false);
              }
            });
            return function checkConnection() {
              return _ref7.apply(this, arguments);
            };
          }();
          checkConnection();
        }
      })]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    announcenContainer: {
      marginBottom: 50,
      marginHorizontal: 24
    },
    container: {
      flex: 1
    },
    containerError: {
      flex: 1
    },
    containerLoading: {
      flex: 1,
      paddingTop: 16
    },
    fabArrowStyle: {
      position: "absolute",
      zIndex: 1
    },
    fabContainerViewStyle: {
      alignItems: "center",
      alignSelf: "flex-end",
      bottom: 12,
      justifyContent: "center",
      position: "absolute",
      right: 12
    },
    loadingShortcutLink: {
      flexDirection: "row"
    },
    paddingShortcutLink: {
      paddingHorizontal: 10
    },
    shortcutLinkContainerStyle: {
      marginBottom: 50
    },
    shortcutLinkStyle: {
      backgroundColor: _theme.color.palette.whiteGrey
    }
  });
