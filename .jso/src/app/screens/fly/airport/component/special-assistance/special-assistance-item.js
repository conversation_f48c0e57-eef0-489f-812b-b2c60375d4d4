  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.SpecialAssistanceItem = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _specialAssistanceStyles = _$$_REQUIRE(_dependencyMap[6]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[7]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SpecialAssistanceItem = exports.SpecialAssistanceItem = function SpecialAssistanceItem(props) {
    var _onPress = props.onPress,
      item = props.item,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "SpecialAssistanceItem" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "SpecialAssistanceItem" : _props$accessibilityL;
    var _useState = (0, _react.useState)(1),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      opacity = _useState2[0],
      setOpacity = _useState2[1];
    return (0, _jsxRuntime.jsx)(_reactNative2.TouchableWithoutFeedback, {
      onPress: function onPress() {
        return _onPress(item);
      },
      onPressIn: function onPressIn() {
        return setOpacity(0.2);
      },
      onPressOut: function onPressOut() {
        return setOpacity(1);
      },
      testID: `${testID}__TouchableWithoutFeedback`,
      accessibilityLabel: `${accessibilityLabel}__TouchableWithoutFeedback`,
      children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: [_specialAssistanceStyles.styles.container, {
          opacity: opacity
        }],
        children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
          resizeMode: "cover",
          style: _specialAssistanceStyles.styles.thumbnailStyles,
          source: {
            uri: (0, _screenHelper.getUriImage)(item == null ? undefined : item.illustrationImage)
          }
        }), (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _specialAssistanceStyles.styles.bottomContainer,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "bodyTextBold",
            text: item.title,
            style: _specialAssistanceStyles.styles.textStyles,
            numberOfLines: 3
          })
        })]
      })
    });
  };
