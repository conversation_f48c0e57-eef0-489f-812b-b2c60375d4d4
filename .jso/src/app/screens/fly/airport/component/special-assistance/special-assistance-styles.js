  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    bottomContainer: {
      flex: 1,
      margin: 16,
      marginTop: 12
    },
    container: Object.assign({
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      height: "auto",
      marginBottom: 50,
      marginRight: 12,
      marginTop: 16
    }, _reactNative.Platform.select({
      ios: {
        shadowRadius: 16,
        shadowOpacity: 0.08,
        shadowOffset: {
          width: 0,
          height: 3
        },
        backgroundColor: _theme.color.palette.whiteGrey
      },
      android: {
        elevation: 3,
        backgroundColor: _theme.color.palette.whiteGrey
      }
    }), {
      width: 160
    }),
    listContainer: {
      paddingLeft: 24
    },
    textStyles: {
      color: _theme.color.palette.almostBlackGrey
    },
    thumbnailStyles: {
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      height: 122,
      width: "100%"
    },
    titleStyles: {
      color: _theme.color.palette.almostBlackGrey,
      marginHorizontal: 24
    }
  });
