  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _specialAssistanceItem = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[7]);
  var _adobe = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  var SpecialAssistanceComponent = function SpecialAssistanceComponent(props) {
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("SPECIAL_ASSISTANCE_COMPONENT"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var specialAssistanceData = props.specialAssistanceData,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "SpecialAssistanceComponent" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "SpecialAssistanceComponent" : _props$accessibilityL;
    var specialAssistanceList = specialAssistanceData == null ? undefined : specialAssistanceData.contents;
    var specialAssistanceOnPress = function specialAssistanceOnPress(item) {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppAirportLanding, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppAirportLanding, `${specialAssistanceData == null ? undefined : specialAssistanceData.title} | ${(item == null ? undefined : item.title) || ""}`));
      var _item$navigation = item.navigation,
        type = _item$navigation.type,
        value = _item$navigation.value;
      var _ref = item || {},
        redirect = _ref.redirect;
      if (!type || !value) return;
      handleNavigation(type, value, redirect);
    };
    var renderItem = function renderItem(_ref2) {
      var item = _ref2.item,
        index = _ref2.index;
      return (0, _jsxRuntime.jsx)(_specialAssistanceItem.SpecialAssistanceItem, {
        item: item,
        index: index,
        onPress: function onPress() {
          return specialAssistanceOnPress(item);
        },
        testID: `${testID}__SpecialAssistanceItem__${index}`,
        accessibilityLabel: `${accessibilityLabel}__SpecialAssistanceItem__${index}`
      });
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        preset: "h4",
        text: specialAssistanceData == null ? undefined : specialAssistanceData.title,
        style: styles.titleStyles,
        numberOfLines: 1
      }), (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
        data: specialAssistanceList,
        renderItem: renderItem,
        horizontal: true,
        showsHorizontalScrollIndicator: false,
        contentContainerStyle: styles.listContainer,
        testID: `${testID}__FlatListSpecialAssistance`,
        keyExtractor: function keyExtractor(_, index) {
          return index.toString();
        },
        accessibilityLabel: `${accessibilityLabel}__FlatListSpecialAssistance`
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    listContainer: {
      paddingLeft: 24
    },
    titleStyles: {
      color: _theme.color.palette.almostBlackGrey,
      marginHorizontal: 24
    }
  });
  var _default = exports.default = SpecialAssistanceComponent;
