  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _findYourWayStyles = _$$_REQUIRE(_dependencyMap[6]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[7]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[8]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _native = _$$_REQUIRE(_dependencyMap[10]);
  var _adobe = _$$_REQUIRE(_dependencyMap[11]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  var FindYourWayComponent = function FindYourWayComponent(props) {
    var navigation = (0, _native.useNavigation)();
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("FIND_YOUR_WAY"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var findYourWayData = props.findYourWayData;
    var findYourWaList = findYourWayData == null ? undefined : findYourWayData.contents;
    var itemFindYourWayOnPress = function itemFindYourWayOnPress(item) {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppAirportLanding, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppAirportLanding, `${findYourWayData == null ? undefined : findYourWayData.title} | ${(item == null ? undefined : item.title) || ""}`));
      var _item$navigation = item.navigation,
        type = _item$navigation.type,
        value = _item$navigation.value;
      var _ref = item || {},
        redirect = _ref.redirect;
      if (!type || !value) return;
      var airportInfoL2Id = item.airportInfoL2Id;
      if (airportInfoL2Id) {
        var params = airportInfoL2Id ? {
          id: airportInfoL2Id
        } : undefined;
        navigation == null || navigation.navigate(value, params);
        return;
      }
      handleNavigation(type, value, redirect);
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _findYourWayStyles.styles.container,
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        preset: "h4",
        text: findYourWayData == null ? undefined : findYourWayData.title,
        numberOfLines: 1,
        style: _findYourWayStyles.styles.titleStyles
      }), findYourWaList.map(function (item, index) {
        return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
          style: _findYourWayStyles.styles.itemStyles,
          onPress: function onPress() {
            return itemFindYourWayOnPress(item);
          },
          children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
            source: {
              uri: (0, _screenHelper.getUriImage)(item == null ? undefined : item.image)
            },
            style: _findYourWayStyles.styles.imageStyles,
            resizeMode: "cover"
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _findYourWayStyles.styles.contentContainer,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              preset: "bodyTextBold",
              text: item == null ? undefined : item.title,
              style: _findYourWayStyles.styles.textContentStyles,
              numberOfLines: item != null && item.text && item.text.length > 0 ? 1 : 2
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              preset: "caption1Regular",
              text: item == null ? undefined : item.text,
              style: _findYourWayStyles.styles.textDescriptionStyles,
              numberOfLines: 2
            })]
          })]
        }, index);
      })]
    });
  };
  var _default = exports.default = FindYourWayComponent;
