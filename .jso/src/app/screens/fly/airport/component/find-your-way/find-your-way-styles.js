  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    container: {
      marginBottom: 50
    },
    contentContainer: {
      flex: 1,
      height: "100%",
      justifyContent: "center",
      marginHorizontal: 16
    },
    errorContainer: {
      marginTop: 0
    },
    imageStyles: {
      borderRadius: 12,
      height: 70,
      marginLeft: 8,
      width: 70
    },
    itemStyles: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      elevation: 5,
      flexDirection: "row",
      height: 86,
      marginBottom: 12,
      marginHorizontal: 24,
      shadowColor: _theme.color.palette.almostBlackGrey,
      shadowOffset: {
        height: 6,
        width: 0
      },
      shadowOpacity: 0.05,
      shadowRadius: 20
    },
    textContentStyles: {
      color: _theme.color.palette.almostBlackGrey
    },
    textDescriptionStyles: {
      color: _theme.color.palette.darkestGrey
    },
    titleStyles: {
      color: _theme.color.palette.almostBlackGrey,
      marginBottom: 16,
      marginHorizontal: 24
    }
  });
