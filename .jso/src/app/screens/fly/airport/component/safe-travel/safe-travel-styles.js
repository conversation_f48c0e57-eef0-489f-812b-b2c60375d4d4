  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    bottomContainer: {
      margin: 12,
      width: "auto"
    },
    cardContainer: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      bottom: 0,
      elevation: 5,
      height: 148,
      justifyContent: "flex-end",
      position: "absolute",
      shadowColor: _theme.color.palette.almostBlackGrey,
      shadowOffset: {
        height: 6,
        width: 0
      },
      shadowOpacity: 0.2,
      shadowRadius: 20,
      width: 140
    },
    container: {
      height: 172,
      marginBottom: 50,
      marginRight: 12,
      marginTop: 16,
      width: 140
    },
    listContainer: {
      paddingLeft: 24
    },
    textStyles: {
      color: _theme.color.palette.almostBlackGrey
    },
    thumbnailContainer: {
      elevation: 6,
      zIndex: 1
    },
    thumbnailStyles: {
      borderRadius: 12,
      height: 128,
      left: 12,
      position: "absolute",
      right: 0,
      top: 0,
      width: 116
    },
    titleLoading: {
      borderRadius: 8,
      height: 22,
      marginLeft: 24,
      width: '50%'
    },
    titleStyles: {
      marginHorizontal: 24
    }
  });
