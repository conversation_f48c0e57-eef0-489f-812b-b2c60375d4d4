  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _safeTravelItem = _$$_REQUIRE(_dependencyMap[5]);
  var _safeTravelStyles = _$$_REQUIRE(_dependencyMap[6]);
  var _airportType = _$$_REQUIRE(_dependencyMap[7]);
  var _constants = _$$_REQUIRE(_dependencyMap[8]);
  var _theme = _$$_REQUIRE(_dependencyMap[9]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[11]);
  var _navigationType = _$$_REQUIRE(_dependencyMap[12]);
  var _native = _$$_REQUIRE(_dependencyMap[13]);
  var _adobe = _$$_REQUIRE(_dependencyMap[14]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[15]);
  var loadingData = Array(5).fill(null);
  var lightGreyLoadingColors = [_theme.color.palette.lightGrey, _theme.color.background, _theme.color.palette.lightGrey];
  var lighterGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var SafeTravelComponent = function SafeTravelComponent(props) {
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("SAFE_TRAVEL_COMPONENT"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var navigation = (0, _native.useNavigation)();
    var type = props.type,
      safeTravelData = props.safeTravelData,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "SafeTravelComponent" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "SafeTravelComponent" : _props$accessibilityL;
    var safeTravelList = safeTravelData == null ? undefined : safeTravelData.contents;
    var safeTravelItemOnPress = function safeTravelItemOnPress(item) {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppAirportLanding, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppAirportLanding, `${safeTravelData == null ? undefined : safeTravelData.title} | ${item == null ? undefined : item.title}`));
      var _item$navigation = item.navigation,
        navigationType = _item$navigation.type,
        value = _item$navigation.value;
      var _ref = item || {},
        redirect = _ref.redirect;
      if (!navigationType || !value) return;
      var _ref2 = item || "",
        airportInfoL2Id = _ref2.airportInfoL2Id;
      if (navigationType === _navigationType.NavigationTypeEnum.inApp && airportInfoL2Id) {
        var params = airportInfoL2Id ? {
          id: airportInfoL2Id
        } : undefined;
        navigation == null || navigation.navigate(value, params);
        return;
      }
      handleNavigation(navigationType, value, redirect);
    };
    var renderItem = function renderItem(_ref3) {
      var item = _ref3.item,
        index = _ref3.index;
      return (0, _jsxRuntime.jsx)(_safeTravelItem.SafeTravelItem, {
        item: item,
        onPress: function onPress() {
          return safeTravelItemOnPress(item);
        },
        testID: `${testID}__SafeTravelItem__${index}`,
        accessibilityLabel: `${accessibilityLabel}__SafeTravelItem__${index}`
      });
    };
    var renderItemLoading = function renderItemLoading() {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _safeTravelStyles.styles.container,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lightGreyLoadingColors,
          shimmerStyle: [_safeTravelStyles.styles.thumbnailStyles, _safeTravelStyles.styles.thumbnailContainer]
        }), (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _safeTravelStyles.styles.cardContainer,
          children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: lighterGreyLoadingColors,
            shimmerStyle: _safeTravelStyles.styles.bottomContainer
          })
        })]
      });
    };
    if (type === _airportType.ComponentState.loading) {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lightGreyLoadingColors,
          shimmerStyle: _safeTravelStyles.styles.titleLoading
        }), (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
          data: loadingData,
          renderItem: renderItemLoading,
          horizontal: true,
          keyExtractor: function keyExtractor(_item, index) {
            return index.toString();
          },
          showsHorizontalScrollIndicator: false,
          contentContainerStyle: _safeTravelStyles.styles.listContainer
        })]
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        preset: "h4",
        text: safeTravelData == null ? undefined : safeTravelData.title,
        style: _safeTravelStyles.styles.titleStyles,
        numberOfLines: 1
      }), (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
        data: safeTravelList,
        renderItem: renderItem,
        horizontal: true,
        showsHorizontalScrollIndicator: false,
        contentContainerStyle: _safeTravelStyles.styles.listContainer,
        testID: `${testID}__FlatList`,
        accessibilityLabel: `${accessibilityLabel}__FlatList`
      })]
    });
  };
  var _default = exports.default = SafeTravelComponent;
