  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _lodash = _$$_REQUIRE(_dependencyMap[6]);
  var _subscriptions = _$$_REQUIRE(_dependencyMap[7]);
  var _awsAmplify = _$$_REQUIRE(_dependencyMap[8]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[9]);
  var _store = _$$_REQUIRE(_dependencyMap[10]);
  var _exploreRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _mytravelRedux = _$$_REQUIRE(_dependencyMap[12]);
  var _utils = _$$_REQUIRE(_dependencyMap[13]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[14]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var updateUpcomingEventFlight = function updateUpcomingEventFlight(_currentUpcomingEvent, flightSubscriptionData) {
    var currentUpcomingEvent = (0, _toConsumableArray2.default)(_currentUpcomingEvent || []);
    var _loop = function _loop() {
      var currentItem = currentUpcomingEvent[i];
      var matchingItemIndex = flightSubscriptionData.findIndex(function (item) {
        return (item == null ? undefined : item.flight_number) === (currentItem == null ? undefined : currentItem.flightNumber) && (item == null ? undefined : item.scheduled_date) === (currentItem == null ? undefined : currentItem.scheduledDate);
      });
      if (matchingItemIndex !== -1) {
        var _matchingItem$status_, _matchingItem$status_2, _matchingItem$status_3, _matchingItem$status_4, _matchingItem$status_5, _matchingItem$status_6;
        var matchingItem = flightSubscriptionData[matchingItemIndex];
        currentUpcomingEvent[i] = Object.assign({}, currentUpcomingEvent[i], {
          flightStatus: matchingItem == null ? undefined : matchingItem.flight_status,
          flightStatusMapping: matchingItem == null || (_matchingItem$status_ = matchingItem.status_mapping) == null ? undefined : _matchingItem$status_.details_status_en,
          upcomingStatusMapping: (matchingItem == null || (_matchingItem$status_2 = matchingItem.status_mapping) == null ? undefined : _matchingItem$status_2.details_status_en) || (matchingItem == null || (_matchingItem$status_3 = matchingItem.status_mapping) == null ? undefined : _matchingItem$status_3.listing_status_en),
          displayTimestamp: matchingItem == null ? undefined : matchingItem.display_timestamp,
          scheduledDate: matchingItem == null ? undefined : matchingItem.scheduled_date,
          scheduledTime: matchingItem == null ? undefined : matchingItem.scheduled_time,
          statusColor: matchingItem == null || (_matchingItem$status_4 = matchingItem.status_mapping) == null || (_matchingItem$status_4 = _matchingItem$status_4.status_text_color) == null ? undefined : _matchingItem$status_4.toLowerCase(),
          showGate: matchingItem == null || (_matchingItem$status_5 = matchingItem.status_mapping) == null ? undefined : _matchingItem$status_5.show_gate,
          timeOfFlight: matchingItem == null ? undefined : matchingItem.scheduled_time,
          beltStatusMapping: matchingItem == null || (_matchingItem$status_6 = matchingItem.status_mapping) == null ? undefined : _matchingItem$status_6.belt_status_en,
          technicalFlightStatus1: matchingItem == null ? undefined : matchingItem.technical_flight_status1,
          technicalFlightStatus2: matchingItem == null ? undefined : matchingItem.technical_flight_status2,
          displayGate: matchingItem == null ? undefined : matchingItem.display_gate
        });
      }
    };
    for (var i = 0; i < currentUpcomingEvent.length; i++) {
      _loop();
    }
    return currentUpcomingEvent;
  };
  var updateSavedFlight = function updateSavedFlight(_savedFlights, flightSubscriptionData) {
    var savedFlights = (0, _toConsumableArray2.default)(_savedFlights || []);
    var _loop2 = function _loop2() {
      var currentItem = savedFlights[i];
      var matchingItemIndex = flightSubscriptionData.findIndex(function (item) {
        return (item == null ? undefined : item.flight_number) === (currentItem == null ? undefined : currentItem.flightNumber) && (item == null ? undefined : item.scheduled_date) === (currentItem == null ? undefined : currentItem.scheduledDate);
      });
      if (matchingItemIndex !== -1) {
        var _matchingItem$status_7, _matchingItem$status_8, _matchingItem$status_9, _matchingItem$status_0, _matchingItem$status_1, _matchingItem$status_10;
        var matchingItem = flightSubscriptionData[matchingItemIndex];
        savedFlights[i] = Object.assign({}, savedFlights[i], {
          flightStatus: matchingItem == null ? undefined : matchingItem.flight_status,
          flightStatusMapping: matchingItem == null || (_matchingItem$status_7 = matchingItem.status_mapping) == null ? undefined : _matchingItem$status_7.details_status_en,
          upcomingStatusMapping: (matchingItem == null || (_matchingItem$status_8 = matchingItem.status_mapping) == null ? undefined : _matchingItem$status_8.details_status_en) || (matchingItem == null || (_matchingItem$status_9 = matchingItem.status_mapping) == null ? undefined : _matchingItem$status_9.listing_status_en),
          displayTimestamp: matchingItem == null ? undefined : matchingItem.display_timestamp,
          scheduledDate: matchingItem == null ? undefined : matchingItem.scheduled_date,
          scheduledTime: matchingItem == null ? undefined : matchingItem.scheduled_time,
          statusColor: matchingItem == null || (_matchingItem$status_0 = matchingItem.status_mapping) == null || (_matchingItem$status_0 = _matchingItem$status_0.status_text_color) == null ? undefined : _matchingItem$status_0.toLowerCase(),
          showGate: matchingItem == null || (_matchingItem$status_1 = matchingItem.status_mapping) == null ? undefined : _matchingItem$status_1.show_gate,
          timeOfFlight: matchingItem == null ? undefined : matchingItem.scheduled_time,
          beltStatusMapping: matchingItem == null || (_matchingItem$status_10 = matchingItem.status_mapping) == null ? undefined : _matchingItem$status_10.belt_status_en,
          technicalFlightStatus1: matchingItem == null ? undefined : matchingItem.technical_flight_status1,
          technicalFlightStatus2: matchingItem == null ? undefined : matchingItem.technical_flight_status2,
          displayGate: matchingItem == null ? undefined : matchingItem.display_gate
        });
      }
    };
    for (var i = 0; i < savedFlights.length; i++) {
      _loop2();
    }
    return savedFlights;
  };
  var INTERVAL_UPDATE_FLIGHT = 5000;
  var FlyDetailSubscription = function FlyDetailSubscription(props) {
    var direction = props.direction,
      flightNumber = props.flightNumber,
      scheduledDate = props.scheduledDate;
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useState = (0, _react.useState)(new Date().getTime()),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      toggleError = _useState2[0],
      setToggleError = _useState2[1];
    var subScriptionRef = (0, _react.useRef)(null);
    var appState = (0, _react.useRef)(_reactNative.AppState.currentState);
    var tempStoreFlight = (0, _react.useRef)({});
    var tempStoreFlightSaved = (0, _react.useRef)({});
    var intervalId = (0, _react.useRef)(null);
    var updateDataToStore = function updateDataToStore() {
      var data = (0, _toConsumableArray2.default)((0, _lodash.values)(Object.assign({}, tempStoreFlight.current)));
      var _store$getState = _store.store.getState(),
        exploreReducer = _store$getState.exploreReducer,
        mytravelReducer = _store$getState.mytravelReducer;
      if (!(0, _lodash.isEmpty)(data)) {
        // Continue handle for UpcomingEvents
        var currentUpcomingEventPayload = exploreReducer == null ? undefined : exploreReducer.upComingEvents;
        if (!(0, _lodash.isEmpty)(currentUpcomingEventPayload)) {
          var newData = updateUpcomingEventFlight(currentUpcomingEventPayload, data);
          _reactNative.InteractionManager.runAfterInteractions(function () {
            dispatch(_exploreRedux.default.updateUpcomingEventBySubscription(newData));
          });
          delete tempStoreFlight.current[`${flightNumber}_${scheduledDate}`];
        }
      }
      var dataSaved = (0, _toConsumableArray2.default)((0, _lodash.values)(Object.assign({}, tempStoreFlightSaved.current)));
      if (!(0, _lodash.isEmpty)(dataSaved)) {
        var _mytravelReducer$myTr;
        var myTravelFlightsPayload = (_mytravelReducer$myTr = mytravelReducer.myTravelFlightsPayload) == null ? undefined : _mytravelReducer$myTr.getMyTravelFlightDetails;
        if (!(0, _lodash.isEmpty)(myTravelFlightsPayload)) {
          var _newData = updateSavedFlight(myTravelFlightsPayload, dataSaved);
          _reactNative.InteractionManager.runAfterInteractions(function () {
            dispatch(_mytravelRedux.MytravelCreators.flyMyTravelFlightsUpdateSubcription(_newData));
          });
          delete tempStoreFlightSaved.current[`${flightNumber}_${scheduledDate}`];
        }
      }
    };
    var setIntervalSubscription = function setIntervalSubscription() {
      if (intervalId.current) return;
      _reactNative.InteractionManager.runAfterInteractions(function () {
        intervalId.current = setInterval(function () {
          _reactNative.InteractionManager.runAfterInteractions(function () {
            updateDataToStore();
          });
        }, INTERVAL_UPDATE_FLIGHT);
      });
    };
    var clearIntervalSubscription = function clearIntervalSubscription() {
      clearInterval(intervalId.current);
      intervalId.current = null;
    };
    (0, _react.useEffect)(function () {
      setIntervalSubscription();
      var subscription = _reactNative.AppState.addEventListener("change", function (nextAppState) {
        if ((0, _utils.ifAllTrue)([appState.current.match(/active/), nextAppState === "inactive"])) {
          clearIntervalSubscription();
        }
        if ((0, _utils.ifAllTrue)([appState.current.match(/inactive|background/), nextAppState === "active"])) {
          setIntervalSubscription();
        }
        appState.current = nextAppState;
      });
      return function () {
        subscription.remove();
        clearIntervalSubscription();
      };
    }, []);
    (0, _react.useEffect)(function () {
      subScriptionRef.current = _awsAmplify.API.graphql({
        query: _subscriptions.flightDetailUpdateSubscription,
        variables: {
          flightNumber: flightNumber,
          direction: direction,
          scheduledDate: scheduledDate
        }
      }).subscribe({
        next: function () {
          var _next = (0, _asyncToGenerator2.default)(function* (res) {
            var _res$value;
            var data = res == null || (_res$value = res.value) == null || (_res$value = _res$value.data) == null ? undefined : _res$value.flightDetailUpdate;
            var transformResponse = (0, _lodash.reduce)([data], function (obj, param) {
              obj[`${param == null ? undefined : param.flight_number}_${param == null ? undefined : param.scheduled_date}`] = param;
              return obj;
            }, {});
            tempStoreFlight.current = (0, _lodash.merge)(tempStoreFlight.current, transformResponse);
            tempStoreFlightSaved.current = (0, _lodash.merge)(tempStoreFlightSaved.current, transformResponse);
          });
          function next(_x) {
            return _next.apply(this, arguments);
          }
          return next;
        }(),
        error: function error(_err) {
          setToggleError(new Date().getTime());
        }
      });
      return function () {
        (subScriptionRef == null ? undefined : subScriptionRef.current) && (subScriptionRef == null ? undefined : subScriptionRef.current.unsubscribe());
      };
    }, [toggleError]);
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {});
  };
  var _default = exports.default = _react.default.memo(FlyDetailSubscription);
