  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _styles = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _flightListingContext = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  function FlightListToolbar(_ref) {
    var onGetEarlierFlights = _ref.onGetEarlierFlights,
      componentName = _ref.componentName,
      currentFilter = _ref.currentFilter;
    var _useFlightListingCont = (0, _flightListingContext.useFlightListingContext)(),
      isLoading = _useFlightListingCont.isLoading;
    var disabledLoadEarlierFlight = (0, _react.useMemo)(function () {
      return isLoading || !!(currentFilter != null && currentFilter.airline) || !!(currentFilter != null && currentFilter.airport);
    }, [isLoading, currentFilter == null ? undefined : currentFilter.airline, currentFilter == null ? undefined : currentFilter.airport]);
    var loadEarlierFlightsButtonStyle = (0, _react.useMemo)(function () {
      return [_styles.default.loadBtn, disabledLoadEarlierFlight && {
        borderColor: _theme.color.palette.greyCCCCCC
      }];
    }, [disabledLoadEarlierFlight]);
    var loadEarlierFlightsTextStyle = (0, _react.useMemo)(function () {
      return [_styles.default.loadBtnText, disabledLoadEarlierFlight && {
        color: _theme.color.palette.darkGrey999
      }];
    }, [disabledLoadEarlierFlight]);
    var getEarlierFlights = function getEarlierFlights() {
      if (onGetEarlierFlights && typeof onGetEarlierFlights === "function") {
        onGetEarlierFlights();
      }
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        style: _styles.default.notice,
        tx: "flightLanding.flightNotice"
      }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        disabled: disabledLoadEarlierFlight,
        onPress: getEarlierFlights,
        style: loadEarlierFlightsButtonStyle,
        testID: `${componentName != null ? componentName : ""}__ButtonFlightsHandler`,
        accessibilityLabel: `${componentName != null ? componentName : ""}__ButtonFlightsHandler`,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          preset: 'caption1Bold',
          style: loadEarlierFlightsTextStyle,
          tx: "flightLanding.loadEarlierFlights"
        })
      })]
    });
  }
  var _default = exports.default = FlightListToolbar;
