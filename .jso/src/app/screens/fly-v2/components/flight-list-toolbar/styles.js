  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var _default = exports.default = _reactNative.StyleSheet.create({
    notice: {
      fontWeight: _reactNative.Platform.select({
        ios: "400",
        android: "normal"
      }),
      fontFamily: _theme.typography.regular,
      fontSize: 12,
      lineHeight: 16,
      letterSpacing: 0,
      paddingHorizontal: 20,
      color: _theme.color.palette.darkestGrey
    },
    loadBtn: {
      alignSelf: "center",
      marginVertical: 20,
      width: width - 40,
      marginHorizontal: 20,
      borderRadius: 60,
      borderWidth: 1,
      borderColor: _theme.color.palette.purpleD5BBEA,
      backgroundColor: _theme.color.palette.whiteGrey,
      height: 36,
      justifyContent: 'center',
      alignItems: 'center'
    },
    loadBtnText: {
      color: _theme.color.palette.lightPurple
    }
  });
