  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _styles = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _flightListingContext = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  function FlyListingTab(_ref) {
    var state = _ref.state,
      descriptors = _ref.descriptors,
      navigation = _ref.navigation;
    var _useFlightListingCont = (0, _flightListingContext.useFlightListingContext)(),
      isLoading = _useFlightListingCont.isLoading,
      setSelectedTab = _useFlightListingCont.setSelectedTab;
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: _styles.default.tabContainer,
      children: state.routes.map(function (route, index) {
        var options = descriptors[route.key].options;
        var label = options.tabBarLabel !== undefined ? options.tabBarLabel : options.title !== undefined ? options.title : route.name;
        var isFocused = state.index === index;
        var onPress = function onPress() {
          var event = navigation.emit({
            type: "tabPress",
            target: route.key,
            canPreventDefault: true
          });
          if (!isFocused && !event.defaultPrevented) {
            navigation.navigate(route.name);
            setSelectedTab(route.name);
          }
        };
        if (isFocused) {
          return (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
            colors: ["#F6E9FF", "#FCFCFC"],
            start: {
              x: 0.5,
              y: 0
            },
            end: {
              x: 0.5,
              y: 1
            },
            style: [_styles.default.tabBtnActive, index === 0 ? _styles.default.tabBtnActiveLeft : _styles.default.tabBtnActiveRight],
            children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              style: _styles.default.tabBtnTouch,
              onPress: onPress,
              activeOpacity: 0.85,
              disabled: isLoading,
              children: (0, _jsxRuntime.jsx)(_reactNative.Text, {
                style: _styles.default.tabBtnTextActive,
                children: label
              })
            })
          }, route.key);
        }
        return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: [_styles.default.tabBtnInactive, index === 0 ? _styles.default.tabBtnInactiveLeft : _styles.default.tabBtnInactiveRight],
          onPress: onPress,
          activeOpacity: 0.85,
          disabled: isLoading,
          children: (0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: _styles.default.tabBtnTextInactive,
            children: label
          })
        }, route.key);
      })
    });
  }
  var _default = exports.default = FlyListingTab;
