  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _default = exports.default = _reactNative.StyleSheet.create({
    tabBtnInactive: {
      height: 32,
      backgroundColor: _theme.color.palette.lightestGrey,
      borderColor: _theme.color.palette.lighterGrey,
      borderWidth: 1,
      borderStyle: "solid",
      width: 102,
      justifyContent: "center",
      alignItems: "center"
    },
    tabBtnActive: {
      backgroundColor: "#f3eaff",
      width: 130,
      height: 38,
      borderTopLeftRadius: 8,
      borderTopRightRadius: 8
    },
    tabBtnTextActive: {
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.almostBlack<PERSON>rey,
      fontSize: 14,
      lineHeight: 18,
      letterSpacing: 0
    },
    tabBtnTextInactive: {
      color: _theme.color.palette.almostBlackGrey,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      fontFamily: _theme.typography.bold,
      fontSize: 12,
      lineHeight: 16,
      letterSpacing: 0,
      textAlign: "center"
    },
    tabBtnTouch: {
      flex: 1,
      width: "100%",
      justifyContent: "center",
      alignItems: "center"
    },
    tabContainer: {
      alignItems: "flex-end",
      flexDirection: "row",
      justifyContent: "center"
    },
    tabBtnInactiveLeft: {
      borderTopLeftRadius: 8,
      borderBottomLeftRadius: 0,
      borderTopRightRadius: 0,
      borderBottomRightRadius: 0,
      width: 102
    },
    tabBtnInactiveRight: {
      borderTopLeftRadius: 0,
      borderBottomLeftRadius: 0,
      borderTopRightRadius: 8,
      borderBottomRightRadius: 0,
      width: 121
    },
    tabBtnActiveLeft: {
      width: 108
    },
    tabBtnActiveRight: {
      width: 130
    }
  });
