  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.LoadingSkeleton = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _constants = _$$_REQUIRE(_dependencyMap[2]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _flightListToolbar = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  var lightGreyLoadingColors = [_theme.color.palette.lightGrey, _theme.color.background, _theme.color.palette.lightGrey];
  var lighterGreyLoadingColors = [_theme.color.palette.lightGrey, _theme.color.background, _theme.color.palette.lightGrey];
  var skeletonLayout = [{
    width: 48,
    height: 48,
    borderRadius: 8
  }, {
    width: 271,
    height: 12,
    marginBottom: _theme.spacing[3],
    borderRadius: 4
  }, {
    width: 148,
    height: 12,
    marginBottom: _theme.spacing[3],
    borderRadius: 4
  }, {
    width: 80,
    height: 12,
    borderRadius: 4
  }, {
    width: 110,
    height: 16,
    marginLeft: _theme.spacing[4],
    borderRadius: 4,
    marginBottom: _theme.spacing[5]
  }];
  var LoadingSkeleton = exports.LoadingSkeleton = function LoadingSkeleton() {
    var renderItem = function renderItem(_, i) {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.itemContainer,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lightGreyLoadingColors,
          shimmerStyle: skeletonLayout[0]
        }), (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.itemDetail,
          children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
            children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: lighterGreyLoadingColors,
              shimmerStyle: skeletonLayout[1]
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: lighterGreyLoadingColors,
              shimmerStyle: skeletonLayout[2]
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: lighterGreyLoadingColors,
              shimmerStyle: skeletonLayout[3]
            })]
          })
        })]
      }, i);
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      children: [(0, _jsxRuntime.jsx)(_flightListToolbar.default, {}), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lightGreyLoadingColors,
        shimmerStyle: skeletonLayout[4]
      }), (0, _toConsumableArray2.default)(Array(6)).map(renderItem)]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    itemContainer: {
      flexDirection: "row",
      alignSelf: "center",
      position: "relative",
      marginHorizontal: 20,
      marginBottom: _theme.spacing[5]
    },
    itemDetail: {
      flex: 1,
      marginBottom: _theme.spacing[0],
      borderBottomWidth: 1,
      borderColor: _theme.color.palette.lighterGrey,
      paddingBottom: 20,
      flexDirection: "row",
      marginLeft: _theme.spacing[4]
    }
  });
