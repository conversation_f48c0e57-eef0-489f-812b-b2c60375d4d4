  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var styles = _reactNative.StyleSheet.create({
    container: Object.assign({
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 12,
      paddingVertical: 12,
      paddingHorizontal: 16,
      flexDirection: "row",
      alignItems: "center",
      marginHorizontal: 16,
      marginVertical: 16
    }, _reactNative.Platform.select({
      ios: {
        shadowColor: _theme.color.palette.almostBlackGrey,
        shadowOffset: {
          width: 0,
          height: 6
        },
        shadowOpacity: 0.08,
        shadowRadius: 20
      },
      android: {
        elevation: 4
      }
    })),
    liveBadge: {
      marginRight: 8,
      display: "flex",
      flexDirection: "row",
      alignItems: "center",
      position: "relative"
    },
    liveTextContainer: {
      backgroundColor: _theme.color.palette.basegreen,
      borderRadius: 20,
      paddingHorizontal: 8,
      paddingVertical: 4,
      height: 22,
      lineHeight: 22,
      position: "relative",
      right: -4,
      zIndex: 10,
      width: 37
    },
    liveText: {
      color: _theme.color.palette.whiteGrey,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      fontSize: 11,
      lineHeight: 14,
      width: 37,
      fontFamily: _theme.typography.bold
    },
    content: {
      flex: 1
    },
    subTitle: {
      color: _theme.color.palette.darkestGrey,
      fontSize: 12,
      lineHeight: 16,
      fontWeight: _reactNative.Platform.select({
        ios: "400",
        android: "normal"
      }),
      fontFamily: _theme.typography.medium
    },
    title: {
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 12,
      lineHeight: 16,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      }),
      fontFamily: _theme.typography.black
    },
    buttonGroup: {
      flexDirection: "row",
      gap: 8,
      marginLeft: 10
    },
    button: {
      borderRadius: 8,
      borderWidth: 1,
      borderColor: _theme.color.palette.lightestPurple,
      paddingVertical: 8,
      paddingHorizontal: 12,
      width: 49,
      height: 48,
      gap: 4,
      display: "flex",
      justifyContent: "center",
      alignItems: "center"
    },
    buttonText: {
      color: _theme.color.palette.lightPurple,
      fontSize: 12,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      }),
      textAlign: "center",
      fontFamily: _theme.typography.black,
      width: 28
    },
    iconContainer: {
      width: 12,
      height: 12,
      borderRadius: 6,
      backgroundColor: _theme.color.palette.memberTierIconBackground,
      alignItems: "center",
      justifyContent: "center"
    }
  });
  var _default = exports.default = styles;
