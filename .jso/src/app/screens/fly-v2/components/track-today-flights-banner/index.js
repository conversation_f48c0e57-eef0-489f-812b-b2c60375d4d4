  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _native = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _icons = _$$_REQUIRE(_dependencyMap[5]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _styles = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _adobe = _$$_REQUIRE(_dependencyMap[9]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var TrackTodayFlightsBanner = function TrackTodayFlightsBanner() {
    var navigation = (0, _native.useNavigation)();
    var handleNavigate = (0, _react.useCallback)(function (tab) {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlightLanding, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlightLanding, `Flight track | ${tab}`));
      navigation.navigate("flightResultLandingScreen", {
        screen: tab
      });
    }, []);
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _styles.default.container,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _styles.default.liveBadge,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _styles.default.liveTextContainer,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: _styles.default.liveText,
            tx: "flightLanding.live"
          })
        }), (0, _jsxRuntime.jsx)(_icons.FlightTakeoff, {})]
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _styles.default.content,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: _styles.default.subTitle,
          tx: "flightLanding.realTimeUpdates"
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: _styles.default.title,
          tx: "flightLanding.trackTodayFlights"
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _styles.default.buttonGroup,
        children: [(0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
          style: _styles.default.button,
          onPress: function onPress() {
            return handleNavigate("ARR");
          },
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: _styles.default.buttonText,
            tx: "flightLanding.arr"
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _styles.default.iconContainer,
            children: (0, _jsxRuntime.jsx)(_icons.ArrowRight, {
              width: 16,
              height: 12
            })
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
          style: _styles.default.button,
          onPress: function onPress() {
            return handleNavigate("DEP");
          },
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: _styles.default.buttonText,
            tx: "flightLanding.dep"
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _styles.default.iconContainer,
            children: (0, _jsxRuntime.jsx)(_icons.ArrowRight, {
              width: 16,
              height: 12
            })
          })]
        })]
      })]
    });
  };
  var _default = exports.default = TrackTodayFlightsBanner;
