  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.FlyLandingAnimatedHeader = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _color = _$$_REQUIRE(_dependencyMap[6]);
  var _icons = _$$_REQUIRE(_dependencyMap[7]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[8]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var HEADER_HEIGHT_WITH_TICKERBAND = 54;
  var HEADER_HEIGHT_WITHOUT_TICKERBAND = 100;
  var _worklet_12426942751216_init_data = {
    code: "function flyLandingAnimatedHeaderTsx1(){const{scrollY}=this.__closure;return scrollY.value;}"
  };
  var _worklet_1078474838449_init_data = {
    code: "function flyLandingAnimatedHeaderTsx2(value,prev){const{runOnJS,setCanPress}=this.__closure;if(value!==prev){runOnJS(setCanPress)(value>100);}}"
  };
  var _worklet_15433258674919_init_data = {
    code: "function flyLandingAnimatedHeaderTsx3(){const{interpolate,scrollY,Extrapolation}=this.__closure;return{opacity:interpolate(scrollY.value,[0,40,50],[0,0,1],Extrapolation.CLAMP)};}"
  };
  var FlyLandingAnimatedHeader = exports.FlyLandingAnimatedHeader = function FlyLandingAnimatedHeader(_ref) {
    var scrollY = _ref.scrollY,
      onSearchPress = _ref.onSearchPress,
      onSavedFlightPress = _ref.onSavedFlightPress,
      isShowTickerband = _ref.isShowTickerband;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      canPress = _useState2[0],
      setCanPress = _useState2[1];
    (0, _reactNativeReanimated.useAnimatedReaction)(function () {
      var flyLandingAnimatedHeaderTsx1 = function flyLandingAnimatedHeaderTsx1() {
        return scrollY.value;
      };
      flyLandingAnimatedHeaderTsx1.__closure = {
        scrollY: scrollY
      };
      flyLandingAnimatedHeaderTsx1.__workletHash = 12426942751216;
      flyLandingAnimatedHeaderTsx1.__initData = _worklet_12426942751216_init_data;
      return flyLandingAnimatedHeaderTsx1;
    }(), function () {
      var flyLandingAnimatedHeaderTsx2 = function flyLandingAnimatedHeaderTsx2(value, prev) {
        if (value !== prev) {
          (0, _reactNativeReanimated.runOnJS)(setCanPress)(value > 100);
        }
      };
      flyLandingAnimatedHeaderTsx2.__closure = {
        runOnJS: _reactNativeReanimated.runOnJS,
        setCanPress: setCanPress
      };
      flyLandingAnimatedHeaderTsx2.__workletHash = 1078474838449;
      flyLandingAnimatedHeaderTsx2.__initData = _worklet_1078474838449_init_data;
      return flyLandingAnimatedHeaderTsx2;
    }(), []);
    var headerAnimatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var flyLandingAnimatedHeaderTsx3 = function flyLandingAnimatedHeaderTsx3() {
        return {
          opacity: (0, _reactNativeReanimated.interpolate)(scrollY.value, [0, 40, 50], [0, 0, 1], _reactNativeReanimated.Extrapolation.CLAMP)
        };
      };
      flyLandingAnimatedHeaderTsx3.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        scrollY: scrollY,
        Extrapolation: _reactNativeReanimated.Extrapolation
      };
      flyLandingAnimatedHeaderTsx3.__workletHash = 15433258674919;
      flyLandingAnimatedHeaderTsx3.__initData = _worklet_15433258674919_init_data;
      return flyLandingAnimatedHeaderTsx3;
    }());
    var handleSearchPress = function handleSearchPress() {
      onSearchPress && onSearchPress();
    };
    var handleSavedFlightPress = function handleSavedFlightPress() {
      onSavedFlightPress && onSavedFlightPress();
    };
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: flyHeaderStyles.container,
      pointerEvents: canPress ? "auto" : "none",
      children: (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
        style: [flyHeaderStyles.headerContainer, {
          height: isShowTickerband ? HEADER_HEIGHT_WITH_TICKERBAND : HEADER_HEIGHT_WITHOUT_TICKERBAND
        }, headerAnimatedStyle],
        pointerEvents: canPress ? "auto" : "none",
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: flyHeaderStyles.headerRow,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            tx: "flyLandingV2.fly",
            style: flyHeaderStyles.headerTitle
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: handleSearchPress,
            hitSlop: {
              top: 10,
              bottom: 10,
              left: 10,
              right: 10
            },
            children: (0, _jsxRuntime.jsx)(_icons.SearchIconV2, {
              width: 24,
              height: 24,
              color: _color.color.palette.darkestGrey
            })
          }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
            onPress: handleSavedFlightPress,
            style: flyHeaderStyles.savedFlightContainer,
            children: [(0, _jsxRuntime.jsx)(_icons.PlaneTilted, {
              width: 24,
              height: 24,
              color: _color.color.palette.darkestGrey
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              tx: "flyLanding.saved",
              style: flyHeaderStyles.savedFlightText
            })]
          })]
        })
      })
    });
  };
  var flyHeaderStyles = _reactNative2.StyleSheet.create({
    container: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      zIndex: 999,
      elevation: 5
    },
    headerContainer: {
      justifyContent: "flex-end",
      backgroundColor: "white",
      shadowColor: "#121212",
      shadowOffset: {
        width: 0,
        height: 2
      },
      shadowOpacity: 0.08,
      shadowRadius: 8,
      zIndex: 999,
      elevation: 5
    },
    headerRow: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingHorizontal: 16,
      marginBottom: 16
    },
    headerTitle: Object.assign({}, _text.newPresets.bodyTextBold, {
      color: _color.color.palette.almostBlackGrey,
      textAlign: "center",
      position: "absolute",
      left: 0,
      right: 0
    }),
    savedFlightContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between"
    },
    savedFlightText: Object.assign({}, _text.newPresets.caption1Bold, {
      color: _color.color.palette.almostBlackGrey,
      marginLeft: 4
    })
  });
