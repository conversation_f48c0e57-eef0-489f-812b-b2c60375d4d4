  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.SearchBottomSheet = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _lodash = _$$_REQUIRE(_dependencyMap[7]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _text = _$$_REQUIRE(_dependencyMap[10]);
  var _icons = _$$_REQUIRE(_dependencyMap[11]);
  var _theme = _$$_REQUIRE(_dependencyMap[12]);
  var _text2 = _$$_REQUIRE(_dependencyMap[13]);
  var _palette = _$$_REQUIRE(_dependencyMap[14]);
  var _searchBar = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _searchIndex = _$$_REQUIRE(_dependencyMap[16]);
  var _i18n = _$$_REQUIRE(_dependencyMap[17]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[18]);
  var _searchRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[19]));
  var _constants = _$$_REQUIRE(_dependencyMap[20]);
  var _native = _$$_REQUIRE(_dependencyMap[21]);
  var _adobe = _$$_REQUIRE(_dependencyMap[22]);
  var _flightTabs = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[23]));
  var _tabContent = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[24]));
  var _autocompleteSearch = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[25]));
  var _consts = _$$_REQUIRE(_dependencyMap[26]);
  var _calendar = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[27]));
  var _hooks = _$$_REQUIRE(_dependencyMap[28]);
  var _flightListingContext = _$$_REQUIRE(_dependencyMap[29]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[30]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  // COPIED FROM search-tab-flights/index.tsx (with modifications for new context)

  // COPIED FROM search-tab-flights/index.tsx (BottomSheet, Text, Cross, theme, etc. imports)

  // NEW/CHANGED IN search-bottom-sheet.tsx (constants for this component)
  var SCREEN_HEIGHT = _reactNative2.Dimensions.get('window').height;
  var BOTTOM_SHEET_HEIGHT = SCREEN_HEIGHT - 100;
  var SearchBottomSheet = exports.SearchBottomSheet = (0, _react.forwardRef)(function (props, ref) {
    // COPIED FROM search-tab-flights/index.tsx (redux, navigation, state, etc.)
    var dispatch = (0, _reactRedux.useDispatch)();
    var navigation = (0, _native.useNavigation)();
    var autoCompleteKeywork = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.searchKeyword);
    var _useState = (0, _react.useState)(autoCompleteKeywork),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      keySearch = _useState2[0],
      setKeySearch = _useState2[1];
    var _useState3 = (0, _react.useState)(_constants.FlightDirection.Arrival),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      selectedTab = _useState4[0],
      setSelectedTab = _useState4[1];
    var _useState5 = (0, _react.useState)((0, _moment.default)()),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      selectedDate = _useState6[0],
      setSelectedDate = _useState6[1];
    var _useState7 = (0, _react.useState)(false),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      isModalVisible = _useState8[0],
      setIsModalVisible = _useState8[1];
    var _useState9 = (0, _react.useState)(false),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      isVisible = _useState0[0],
      setIsVisible = _useState0[1];
    var _useCameraPermission = (0, _hooks.useCameraPermission)(),
      handleCameraPermission = _useCameraPermission.handleCameraPermission;
    var _useFlightListingCont = (0, _flightListingContext.useFlightListingContext)(),
      terminal = _useFlightListingCont.terminal,
      airline = _useFlightListingCont.airline,
      airport = _useFlightListingCont.airport,
      setShouldShowSearchBottomSheetOnFocus = _useFlightListingCont.setShouldShowSearchBottomSheetOnFocus;
    var handleClose = (0, _react.useCallback)(function () {
      setIsVisible(false);
      setKeySearch("");
      dispatch(_searchRedux.default.setSearchKeyword(""));
    }, [dispatch]);

    // NEW/CHANGED IN search-bottom-sheet.tsx (imperative handle for modal open/close)
    (0, _react.useImperativeHandle)(ref, function () {
      return {
        // show the bottom sheet, persist the selected tab and date
        open: function open() {
          setIsVisible(true);
        },
        close: handleClose,
        // show the bottom sheet, set the selected tab and reset date
        openWithDirection: function openWithDirection(direction) {
          setSelectedTab(direction);
          setSelectedDate((0, _moment.default)());
          setIsVisible(true);
        }
      };
    });

    // COPIED FROM search-tab-flights/index.tsx (modal open/close logic)
    var openModal = function openModal() {
      setIsModalVisible(true);
    };
    var closeModal = function closeModal() {
      setIsModalVisible(false);
    };
    var _useState1 = (0, _react.useState)(_consts.BottomSheetType.AutoComplete),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      bottomSheetType = _useState10[0],
      setBottomSheetType = _useState10[1];

    // COPIED FROM search-tab-flights/index.tsx (autocomplete, debounce, search keyword logic)
    var onGetAutoCompleteKeyword = (0, _react.useCallback)(function (newKeyword) {
      var param = {
        text: newKeyword.trim(),
        dataType: Object.values(_consts.SeachType).join(",")
      };
      dispatch(_searchRedux.default.getAutoCompleteKeywordRequest(param));
      dispatch(_searchRedux.default.setSearchKeyword(newKeyword));
    }, [dispatch]);
    var onDebounceKeySearch = (0, _react.useCallback)((0, _lodash.debounce)(onGetAutoCompleteKeyword, 200), [onGetAutoCompleteKeyword]);
    var handleSearchKeywordChange = (0, _react.useCallback)(function (newKeyword) {
      if (newKeyword.trim().length > 1) return onDebounceKeySearch(newKeyword);
      dispatch(_searchRedux.default.resetListAutoCompleteKeyword());
      dispatch(_searchRedux.default.setSearchKeyword(newKeyword));
    }, [onDebounceKeySearch]);
    var updateKeySearch = (0, _react.useCallback)(function (value) {
      setKeySearch(value);
      closeModal();
      dispatch(_searchRedux.default.setSearchKeyword(value));
      handleSearchKeywordChange(value);
    }, [handleSearchKeywordChange, dispatch]);

    // COPIED FROM search-tab-flights/index.tsx (onPressAutoCompleteItem, handleSearch, handleScanBoardingPass)
    var onPressAutoCompleteItem = function onPressAutoCompleteItem(item, index) {
      var itemPosition = `${index + 1}`;
      var tabLabel = (0, _i18n.translate)("search.tabTitles.flights");
      var flightDirectionLabel = selectedTab === _constants.FlightDirection.Arrival ? (0, _i18n.translate)("flightLanding.arrivalTabTitle") : (0, _i18n.translate)("flightLanding.departureTabTitle");
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppSearchAutoComplete, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppSearchAutoComplete, `${tabLabel}(${flightDirectionLabel}) | ${autoCompleteKeywork} | ${item == null ? undefined : item.name} | ${itemPosition}`));
      updateKeySearch(item.name);
    };
    var handleSearch = function handleSearch() {
      navigation.navigate(_constants.NavigationConstants.searchFlightsV2Result, {
        keyword: keySearch,
        date: selectedDate,
        direction: selectedTab,
        terminal: terminal.current,
        airline: airline.current,
        airport: airport.current,
        onGoBack: function onGoBack(params) {
          if (!params) return;
          params.direction && setSelectedTab(params.direction);
          params.date && setSelectedDate(params.date);
          params.keyword && setKeySearch(params.keyword);
        }
      });
      // We set this to true so that the search bottom sheet will be shown when the user returns to the flight listing screen.
      setShouldShowSearchBottomSheetOnFocus(true);
      setIsVisible(false);
    };
    var handleScanBoardingPass = function handleScanBoardingPass() {
      handleCameraPermission(function () {
        // We set this to true so that the search bottom sheet will be shown when the user returns to the flight listing screen.
        setShouldShowSearchBottomSheetOnFocus(true);
        setIsVisible(false);
        navigation.navigate("scanCode", {
          shouldTrackDetectedFlightNumber: true
        });
      });
    };
    // COPIED FROM search-tab-flights/index.tsx (renderTabContent)
    var renderTabContent = (0, _react.useCallback)(function () {
      return (0, _jsxRuntime.jsx)(_tabContent.default, {
        searchTerm: keySearch,
        date: selectedDate,
        onSearchTermPress: function onSearchTermPress() {
          openModal();
          handleSearchKeywordChange(keySearch);
          setBottomSheetType(_consts.BottomSheetType.AutoComplete);
        },
        onDatePress: function onDatePress() {
          openModal();
          setBottomSheetType(_consts.BottomSheetType.DatePicker);
        },
        onSearch: handleSearch,
        onScanBoardingPass: handleScanBoardingPass
      });
    }, [keySearch, selectedDate, handleSearch]);

    // COPIED FROM search-tab-flights/index.tsx (JSX structure for BottomSheet, header, FlightTabs, etc.)
    return (0, _jsxRuntime.jsxs)(_bottomSheet.default, {
      isModalVisible: isVisible,
      onClosedSheet: handleClose,
      stopDragCollapse: true,
      onBackPressHandle: handleClose,
      containerStyle: styles.container,
      animationInTiming: 300,
      animationOutTiming: 300,
      openPendingModal: true,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.headerContainer,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.headerTitle,
          tx: `flightListingV2.searchFlights`
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.headerIcon,
          children: (0, _jsxRuntime.jsx)(_icons.Cross, {
            width: 24,
            height: 24,
            onPress: handleClose
          })
        })]
      }), (0, _jsxRuntime.jsx)(_flightTabs.default, {
        selectedTab: selectedTab,
        onSelectTab: setSelectedTab,
        content: renderTabContent()
      }), (0, _jsxRuntime.jsx)(_reactNative2.Modal, {
        visible: isModalVisible,
        transparent: true,
        animationType: "slide",
        onRequestClose: closeModal,
        children: (0, _jsxRuntime.jsx)(_reactNative.Pressable, {
          style: bottomSheetStyles.modalOverlay,
          onPress: closeModal,
          children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: bottomSheetStyles.modalContent,
            children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: bottomSheetStyles.headerContainer,
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                style: bottomSheetStyles.headerTitle,
                tx: bottomSheetType === _consts.BottomSheetType.DatePicker ? "searchV2.flightsTab.datePickerTitle" : `searchV2.flightsTab.${selectedTab}.title`
              }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: bottomSheetStyles.headerIcon,
                children: (0, _jsxRuntime.jsx)(_icons.Cross, {
                  width: 24,
                  height: 24,
                  onPress: closeModal
                })
              })]
            }), bottomSheetType === _consts.BottomSheetType.DatePicker ? (0, _jsxRuntime.jsx)(_calendar.default, {
              selectedDate: selectedDate,
              onSelectDate: function onSelectDate(date) {
                setSelectedDate(date);
                closeModal();
              }
            }) : (0, _jsxRuntime.jsxs)(_reactNative.Pressable, {
              style: bottomSheetStyles.contentContainer,
              onPress: _reactNative2.Keyboard.dismiss,
              children: [(0, _jsxRuntime.jsx)(_searchBar.default, {
                useInputFieldV2: true,
                searchAutoCompleteFlag: _remoteConfig.REMOTE_FLAG_VALUE.ON,
                containerStyle: bottomSheetStyles.searchBar,
                inputContainerStyle: bottomSheetStyles.searchInput,
                inputProps: {
                  placeholder: (0, _i18n.translate)("searchV2.placeHolder.flights1"),
                  placeholderList: [(0, _i18n.translate)("searchV2.placeHolder.flights1"), (0, _i18n.translate)("searchV2.placeHolder.flights2")]
                },
                isShowBack: false,
                tab: _searchIndex.SearchIndex.flights,
                keyword: keySearch,
                onChangeKeyword: handleSearchKeywordChange,
                onSearchClear: function onSearchClear() {
                  dispatch(_searchRedux.default.setSearchKeyword(""));
                  handleSearchKeywordChange("");
                },
                onSubmitLocal: function onSubmitLocal(e) {
                  updateKeySearch(e.nativeEvent.text);
                  _reactNative2.Keyboard.dismiss();
                },
                returnKeyType: "previous",
                testID: "",
                accessibilityLabel: ""
              }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: bottomSheetStyles.textContainer,
                children: [(0, _jsxRuntime.jsx)(_text.Text, {
                  tx: `searchV2.flightsTab.${selectedTab}.information`,
                  style: bottomSheetStyles.informationText
                }), (0, _jsxRuntime.jsx)(_text.Text, {
                  style: bottomSheetStyles.askText,
                  tx: `searchV2.flightsTab.${selectedTab}.ask`,
                  onPress: function onPress() {
                    return setSelectedTab(selectedTab === _constants.FlightDirection.Arrival ? _constants.FlightDirection.Departure : _constants.FlightDirection.Arrival);
                  }
                })]
              }), (0, _jsxRuntime.jsx)(_autocompleteSearch.default, {
                containerStyle: tabContentStyles.autocompleteContainerStyle,
                keySearch: autoCompleteKeywork.trim(),
                flightType: selectedTab,
                handleItemOnPress: onPressAutoCompleteItem
              })]
            })]
          })
        })
      })]
    });
  });

  // COPIED FROM search-tab-flights/index.tsx (styles, with possible tweaks for this file)
  var styles = _reactNative2.StyleSheet.create({
    container: {
      height: BOTTOM_SHEET_HEIGHT,
      flexDirection: "column",
      justifyContent: "center",
      alignItems: "center",
      flexShrink: 0,
      backgroundColor: _theme.color.palette.whiteGrey,
      borderTopRightRadius: 16,
      borderTopLeftRadius: 16
    },
    headerContainer: {
      flexDirection: "row",
      justifyContent: "center",
      alignItems: "center",
      alignContent: "center",
      height: 64,
      position: "relative",
      width: "100%"
    },
    headerTitle: Object.assign({}, _text2.newPresets.bodyTextBold, {
      color: _palette.palette.almostBlackGrey,
      height: 22
    }),
    headerIcon: {
      position: "absolute",
      right: 20
    }
  });
  var tabContentStyles = _reactNative2.StyleSheet.create({
    autocompleteContainerStyle: {
      backgroundColor: _theme.color.palette.almostWhiteGrey
    }
  });
  var bottomSheetStyles = _reactNative2.StyleSheet.create({
    modalOverlay: {
      flex: 1,
      justifyContent: 'flex-end'
    },
    modalContent: {
      height: BOTTOM_SHEET_HEIGHT,
      backgroundColor: _theme.color.palette.whiteGrey,
      borderTopRightRadius: 16,
      borderTopLeftRadius: 16
    },
    container: {
      height: BOTTOM_SHEET_HEIGHT,
      display: "flex",
      flexDirection: "column",
      justifyContent: "center",
      alignItems: "center",
      flexShrink: 0,
      backgroundColor: _theme.color.palette.whiteGrey,
      borderTopRightRadius: 16,
      borderTopLeftRadius: 16
    },
    headerContainer: {
      flexDirection: "row",
      justifyContent: "center",
      alignItems: "center",
      alignContent: "center",
      height: 64,
      position: "relative",
      width: "100%",
      backgroundColor: _theme.color.palette.transparent
    },
    headerTitle: Object.assign({}, _text2.newPresets.bodyTextBold, {
      color: _palette.palette.almostBlackGrey,
      height: 22
    }),
    headerIcon: {
      position: "absolute",
      right: 20
    },
    contentContainer: {
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      flex: 1,
      width: "100%"
    },
    searchBar: {
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      marginTop: 0,
      paddingLeft: 20,
      paddingRight: 20
    },
    searchInput: {
      backgroundColor: _theme.color.palette.almostWhiteGrey
    },
    textContainer: {
      marginTop: _theme.spacing[5]
    },
    informationText: Object.assign({}, _text2.newPresets.caption1Regular, {
      textAlign: "center",
      color: _theme.color.palette.darkestGrey
    }),
    askText: Object.assign({}, _text2.newPresets.caption1Bold, {
      color: _theme.color.palette.lightPurple,
      textAlign: "center"
    })
  });
  var _default = exports.default = SearchBottomSheet;
