  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useCarouselData = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _$$_REQUIRE(_dependencyMap[2]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[3]);
  var _lodash = _$$_REQUIRE(_dependencyMap[4]);
  var _aemRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _envParams = _$$_REQUIRE(_dependencyMap[6]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[7]);
  var _fly = _$$_REQUIRE(_dependencyMap[8]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var useCarouselData = exports.useCarouselData = function useCarouselData() {
    var _useContext$Handlers, _useContext;
    var dispatch = (0, _reactRedux.useDispatch)();
    /**
     * We add a 1-second delay to the loading state after the API call completes
     * to ensure smooth UI transitions and allow other parallel API calls to finish,
     * preventing flickering or jarring UI updates.
     */
    var _useState = (0, _react.useState)(true),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      delayedLoading = _useState2[0],
      setDelayedLoading = _useState2[1];
    var _ref = (_useContext$Handlers = (_useContext = (0, _react.useContext)(_fly.FLY_CONTEXT)) == null ? undefined : _useContext.Handlers) != null ? _useContext$Handlers : {},
      flyPaidInsuranceFeatureFlag = _ref.flyPaidInsuranceFeatureFlag,
      flyDelayBenefitFeatureFlag = _ref.flyDelayBenefitFeatureFlag;
    var isFlightPaidInsuranceEnabled = (0, _remoteConfig.getFeatureFlagInit)(_remoteConfig.REMOTE_CONFIG_FLAGS.FLIGHT_PAID_INSURANCE, flyPaidInsuranceFeatureFlag);
    var isFlightDelayBenefitEnabled = (0, _remoteConfig.getFeatureFlagInit)(_remoteConfig.REMOTE_CONFIG_FLAGS.FLIGHT_DELAY_BENEFIT, flyDelayBenefitFeatureFlag);
    var aemData = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.FLY_LANDING_V2_CAROUSEL_SECTION));
    var loading = (0, _lodash.get)(aemData, "loading");
    var error = (0, _lodash.get)(aemData, "error");
    var rawData = (0, _lodash.get)(aemData, "data.flightLanding.contents", []);
    var transformedData = rawData.filter(function (item) {
      var _item$componentKey;
      if (!item.image) return false;
      switch ((_item$componentKey = item.componentKey) == null ? undefined : _item$componentKey.tagTitle) {
        case "Paid Insurance":
          return isFlightPaidInsuranceEnabled;
        case "Delay Benefit":
          return isFlightDelayBenefitEnabled;
        default:
          return true;
      }
    }).map(function (item) {
      var _env;
      return Object.assign({}, item, {
        image: item.image ? ((_env = (0, _envParams.env)()) == null ? undefined : _env.AEM_URL) + item.image : undefined
      });
    });
    var fetchData = function fetchData() {
      dispatch(_aemRedux.default.getAemConfigData({
        name: _aemRedux.AEM_PAGE_NAME.FLY_LANDING_V2_CAROUSEL_SECTION,
        pathName: "getFlyLandingV2Carousel",
        parameters: {}
      }));
    };
    (0, _react.useEffect)(function () {
      fetchData();
    }, []);
    (0, _react.useEffect)(function () {
      if (!loading) {
        var timer = setTimeout(function () {
          setDelayedLoading(false);
        }, 1000);
        return function () {
          return clearTimeout(timer);
        };
      }
    }, [loading]);
    return {
      loading: loading || delayedLoading,
      error: error,
      data: transformedData,
      refresh: fetchData
    };
  };
