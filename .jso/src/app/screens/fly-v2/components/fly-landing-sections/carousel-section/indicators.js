  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.Indicators = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _objectDestructuringEmpty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _react = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var AnimatedLinearGradient = _reactNativeReanimated.default.createAnimatedComponent(_reactNativeLinearGradient.default);
  var _worklet_17450900234071_init_data = {
    code: "function indicatorsTsx1(){const{withSpring,isActive}=this.__closure;return{width:withSpring(isActive?20:8,{damping:15,stiffness:120})};}"
  };
  var _worklet_5926671624001_init_data = {
    code: "function indicatorsTsx2(){const{progress}=this.__closure;return{width:progress.value+\"%\"};}"
  };
  var Indicator = (0, _react.forwardRef)(function (_ref, ref) {
    (0, _objectDestructuringEmpty2.default)(_ref);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isActive = _useState2[0],
      setIsActive = _useState2[1];
    var progress = (0, _reactNativeReanimated.useSharedValue)(0);
    var widthStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var indicatorsTsx1 = function indicatorsTsx1() {
        return {
          width: (0, _reactNativeReanimated.withSpring)(isActive ? 20 : 8, {
            damping: 15,
            stiffness: 120
          })
        };
      };
      indicatorsTsx1.__closure = {
        withSpring: _reactNativeReanimated.withSpring,
        isActive: isActive
      };
      indicatorsTsx1.__workletHash = 17450900234071;
      indicatorsTsx1.__initData = _worklet_17450900234071_init_data;
      return indicatorsTsx1;
    }());
    var progressStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var indicatorsTsx2 = function indicatorsTsx2() {
        return {
          width: `${progress.value}%`
        };
      };
      indicatorsTsx2.__closure = {
        progress: progress
      };
      indicatorsTsx2.__workletHash = 5926671624001;
      indicatorsTsx2.__initData = _worklet_5926671624001_init_data;
      return indicatorsTsx2;
    }());
    var startProgress = (0, _react.useCallback)(function (duration) {
      // Reset progress
      progress.value = 0;

      // Start animation after a small delay to ensure reset is visible
      setTimeout(function () {
        progress.value = (0, _reactNativeReanimated.withTiming)(100, {
          duration: duration
        });
      }, 50);
    }, []);
    (0, _react.useImperativeHandle)(ref, function () {
      return {
        setActive: function setActive(active) {
          setIsActive(active);
          if (!active) {
            progress.value = 0;
          }
        },
        startProgress: startProgress
      };
    });
    return (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
      style: [styles.indicator, widthStyle],
      children: (0, _jsxRuntime.jsx)(AnimatedLinearGradient, {
        colors: ["#E74391", "#7E1BCC"],
        start: {
          x: 0,
          y: 0
        },
        end: {
          x: 1,
          y: 0
        },
        style: [styles.progressBar, progressStyle]
      })
    });
  });
  var Indicators = exports.Indicators = (0, _react.forwardRef)(function (_ref2, ref) {
    var totalItems = _ref2.totalItems,
      _ref2$duration = _ref2.duration,
      duration = _ref2$duration === undefined ? 3000 : _ref2$duration;
    var indicatorRefs = (0, _react.useRef)([]);
    var setActiveIndex = function setActiveIndex(index) {
      var _indicatorRefs$curren;
      var normalizedIndex = (index % totalItems + totalItems) % totalItems;
      (_indicatorRefs$curren = indicatorRefs.current) == null || _indicatorRefs$curren.forEach(function (ref, index) {
        return ref == null ? undefined : ref.setActive(index === normalizedIndex);
      });
    };
    var startIndicatorProgress = function startIndicatorProgress(index) {
      var _indicatorRefs$curren2;
      var normalizedIndex = (index % totalItems + totalItems) % totalItems;
      (_indicatorRefs$curren2 = indicatorRefs.current[normalizedIndex]) == null || _indicatorRefs$curren2.startProgress(duration);
    };
    (0, _react.useImperativeHandle)(ref, function () {
      return {
        setActiveIndex: setActiveIndex,
        startIndicatorProgress: startIndicatorProgress
      };
    });
    (0, _react.useEffect)(function () {
      setActiveIndex(0);
      startIndicatorProgress(0);
    }, []);
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.container,
      children: Array.from({
        length: totalItems
      }).map(function (_, index) {
        return (0, _jsxRuntime.jsx)(Indicator, {
          ref: function ref(_ref3) {
            indicatorRefs.current[index] = _ref3;
          }
        }, index);
      })
    });
  });
  var styles = _reactNative.StyleSheet.create({
    container: {
      flexDirection: "row",
      justifyContent: "center",
      alignItems: "center",
      marginTop: 8,
      gap: 4
    },
    indicator: {
      width: 8,
      height: 4,
      borderRadius: 999,
      backgroundColor: "#45454533",
      overflow: "hidden"
    },
    progressBar: {
      position: "absolute",
      top: 0,
      left: 0,
      height: "100%"
    }
  });
