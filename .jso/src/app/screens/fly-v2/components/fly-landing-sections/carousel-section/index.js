  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.CarouselSection = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNativeReanimatedCarousel = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _react = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[7]));
  var _useCarouselData2 = _$$_REQUIRE(_dependencyMap[8]);
  var _indicators = _$$_REQUIRE(_dependencyMap[9]);
  var _constants = _$$_REQUIRE(_dependencyMap[10]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _constants2 = _$$_REQUIRE(_dependencyMap[12]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _text = _$$_REQUIRE(_dependencyMap[14]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[15]);
  var _reactNativeFastImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _useCarouselDimensions = _$$_REQUIRE(_dependencyMap[17]);
  var _adobe = _$$_REQUIRE(_dependencyMap[18]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[19]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _worklet_8035681397595_init_data = {
    code: "function indexTsx1(value){const{interpolate,INACTIVE_SCALE,LEFT_BANNER_OFFSET,CENTER_BANNER_OFFSET,RIGHT_BANNER_OFFSET,ACTIVE_HEIGHT,INACTIVE_HEIGHT,ACTIVE_WIDTH}=this.__closure;const scale=interpolate(value,[-1,0,1],[INACTIVE_SCALE,1,INACTIVE_SCALE]);const borderRadius=interpolate(value,[-1,0,1],[8,16,8]);const translateX=interpolate(value,[-1,0,1],[LEFT_BANNER_OFFSET,CENTER_BANNER_OFFSET,RIGHT_BANNER_OFFSET]);const translateY=interpolate(value,[-1,0,1],[ACTIVE_HEIGHT/2-INACTIVE_HEIGHT/2+4,0,ACTIVE_HEIGHT/2-INACTIVE_HEIGHT/2+4]);const opacity=interpolate(value,[-1,0,1],[0.2,1,0.2]);return{width:ACTIVE_WIDTH,height:ACTIVE_HEIGHT,borderRadius:borderRadius,overflow:\"hidden\",transform:[{translateX:translateX},{translateY:translateY},{scale:scale}],opacity:opacity};}"
  };
  /**
   * A carousel section component that displays promotional banners from AEM
   *
   * Features:
   * - Animated carousel with custom transitions
   * - Loading and error states with shimmer effect
   * - Pagination indicators for multiple items
   * - Auto-play functionality
   * - Feature flag support for paid insurance content
   * - Responsive sizing based on screen width
   *
   * States:
   * - Loading: Shows shimmer placeholder
   * - Error: Shows error message with gradient background
   * - Empty: Renders nothing
   * - Single Item: Shows static image without carousel
   * - Multiple Items: Shows full carousel with pagination
   *
   * @component
   * @example
   * ```tsx
   * <CarouselSection ref={carouselRef} />
   * ```
   */
  var CarouselSection = exports.CarouselSection = (0, _react.forwardRef)(function (_, ref) {
    var carouselRef = (0, _react.useRef)(null);
    var indicatorsRef = (0, _react.useRef)(null);
    var _useCarouselData = (0, _useCarouselData2.useCarouselData)(),
      loading = _useCarouselData.loading,
      error = _useCarouselData.error,
      carouselData = _useCarouselData.data,
      refresh = _useCarouselData.refresh;
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)(),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var _useCarouselDimension = (0, _useCarouselDimensions.useCarouselDimensions)(),
      CAROUSEL_WIDTH = _useCarouselDimension.CAROUSEL_WIDTH,
      CENTER_BANNER_OFFSET = _useCarouselDimension.CENTER_BANNER_OFFSET,
      LEFT_BANNER_OFFSET = _useCarouselDimension.LEFT_BANNER_OFFSET,
      RIGHT_BANNER_OFFSET = _useCarouselDimension.RIGHT_BANNER_OFFSET;
    var resetToFirstIndex = function resetToFirstIndex() {
      var _carouselRef$current, _indicatorsRef$curren, _indicatorsRef$curren2;
      (_carouselRef$current = carouselRef.current) == null || _carouselRef$current.scrollTo({
        index: 0,
        animated: false
      });
      (_indicatorsRef$curren = indicatorsRef.current) == null || _indicatorsRef$curren.setActiveIndex(0);
      (_indicatorsRef$curren2 = indicatorsRef.current) == null || _indicatorsRef$curren2.startIndicatorProgress(0);
    };
    (0, _react.useImperativeHandle)(ref, function () {
      return {
        refresh: refresh,
        resetToFirstIndex: resetToFirstIndex
      };
    });

    /**
     * Custom animation style for carousel items
     * @see https://rn-carousel.dev/custom-animations
     *
     * Animations:
     * - Width: Interpolates between inactive and active widths
     * - Height: Maintains aspect ratio during transitions
     * - Border Radius: Smoother corners when active
     * - Translation: Positions items with proper spacing
     * - Opacity: Fades items in/out during transitions
     * - Bottom: Slight vertical shift for active item
     *
     * @param value - Animation progress value (-1 to 1)
     * @returns Animated style object
     *
     * Note: Magic numbers in interpolation ranges (e.g., 24px offset, 4px bottom shift)
     * are derived from design specifications and are crucial for the exact animation
     * behavior required. These values are part of a complex animation system where
     * extracting them would make the code harder to understand.
     */
    var customCarouselAnimationStyle = (0, _react.useCallback)(function () {
      var indexTsx1 = function indexTsx1(value) {
        var scale = (0, _reactNativeReanimated.interpolate)(value, [-1, 0, 1], [_constants.INACTIVE_SCALE, 1, _constants.INACTIVE_SCALE]);
        var borderRadius = (0, _reactNativeReanimated.interpolate)(value, [-1, 0, 1], [8, 16, 8]);
        var translateX = (0, _reactNativeReanimated.interpolate)(value, [-1, 0, 1], [LEFT_BANNER_OFFSET, CENTER_BANNER_OFFSET, RIGHT_BANNER_OFFSET]);
        var translateY = (0, _reactNativeReanimated.interpolate)(value, [-1, 0, 1], [_constants.ACTIVE_HEIGHT / 2 - _constants.INACTIVE_HEIGHT / 2 + 4, 0, _constants.ACTIVE_HEIGHT / 2 - _constants.INACTIVE_HEIGHT / 2 + 4]);
        var opacity = (0, _reactNativeReanimated.interpolate)(value, [-1, 0, 1], [0.2, 1, 0.2]);
        return {
          width: _constants.ACTIVE_WIDTH,
          height: _constants.ACTIVE_HEIGHT,
          borderRadius: borderRadius,
          overflow: "hidden",
          transform: [{
            translateX: translateX
          }, {
            translateY: translateY
          }, {
            scale: scale
          }],
          opacity: opacity
        };
      };
      indexTsx1.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        INACTIVE_SCALE: _constants.INACTIVE_SCALE,
        LEFT_BANNER_OFFSET: LEFT_BANNER_OFFSET,
        CENTER_BANNER_OFFSET: CENTER_BANNER_OFFSET,
        RIGHT_BANNER_OFFSET: RIGHT_BANNER_OFFSET,
        ACTIVE_HEIGHT: _constants.ACTIVE_HEIGHT,
        INACTIVE_HEIGHT: _constants.INACTIVE_HEIGHT,
        ACTIVE_WIDTH: _constants.ACTIVE_WIDTH
      };
      indexTsx1.__workletHash = 8035681397595;
      indexTsx1.__initData = _worklet_8035681397595_init_data;
      return indexTsx1;
    }(), [LEFT_BANNER_OFFSET, CENTER_BANNER_OFFSET, RIGHT_BANNER_OFFSET]);
    var handlePress = function handlePress(item) {
      var _item$cta$navigation, _item$cta, _item$cta2;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlightLanding, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlightLanding, `Carousel banner | ${(item == null ? undefined : item.title) || "null"}`));
      var _ref = (_item$cta$navigation = item == null || (_item$cta = item.cta) == null ? undefined : _item$cta.navigation) != null ? _item$cta$navigation : {},
        type = _ref.type,
        value = _ref.value;
      var _ref2 = (_item$cta2 = item == null ? undefined : item.cta) != null ? _item$cta2 : {},
        redirect = _ref2.redirect;
      if (!type || !value) return;
      handleNavigation(type, value, redirect);
    };
    var renderItem = function renderItem(_ref3) {
      var item = _ref3.item;
      return (0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
        onPress: function onPress() {
          return handlePress(item);
        },
        children: (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
          style: styles.itemContent,
          children: (0, _jsxRuntime.jsx)(_reactNativeFastImage.default, {
            source: {
              uri: item.image
            },
            style: styles.itemImage,
            resizeMode: "cover"
          })
        })
      });
    };
    var handleProgressChange = function handleProgressChange(offsetProgress, absoluteProgress) {
      var _indicatorsRef$curren3;
      var index = Math.round(absoluteProgress) % carouselData.length;
      (_indicatorsRef$curren3 = indicatorsRef.current) == null || _indicatorsRef$curren3.setActiveIndex(index);
    };
    var handleSnapToItem = function handleSnapToItem(index) {
      var _indicatorsRef$curren4, _indicatorsRef$curren5;
      (_indicatorsRef$curren4 = indicatorsRef.current) == null || _indicatorsRef$curren4.setActiveIndex(index);
      (_indicatorsRef$curren5 = indicatorsRef.current) == null || _indicatorsRef$curren5.startIndicatorProgress(index);
    };
    var renderContent = function renderContent() {
      if (loading) {
        return (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.contentContainer,
          children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants2.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _constants.SHIMMER_COLORS,
            shimmerStyle: [styles.skeleton, {
              width: _constants.ACTIVE_WIDTH
            }]
          })
        });
      }
      if (error) {
        return (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.contentContainer,
          children: (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
            colors: ["#ABABAB", "#E5E5E5"],
            start: {
              x: 0,
              y: 0
            },
            end: {
              x: 1,
              y: 0
            },
            style: [styles.errorContainer, {
              width: _constants.ACTIVE_WIDTH
            }],
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.errorText,
              tx: "flyLandingV2.refreshToReload"
            })
          })
        });
      }
      if (carouselData.length === 0) {
        return null;
      }
      if (carouselData.length === 1) {
        return (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.contentContainer,
          children: (0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
            onPress: function onPress() {
              return handlePress(carouselData[0]);
            },
            children: (0, _jsxRuntime.jsx)(_reactNative.View, {
              style: {
                width: _constants.ACTIVE_WIDTH,
                height: _constants.ACTIVE_HEIGHT,
                alignSelf: "center"
              },
              children: (0, _jsxRuntime.jsx)(_reactNative.Image, {
                source: {
                  uri: carouselData[0].image
                },
                style: [styles.itemImage, {
                  borderRadius: 16
                }],
                resizeMode: "cover"
              })
            })
          })
        });
      }
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.contentContainer,
        children: [(0, _jsxRuntime.jsx)(_reactNativeReanimatedCarousel.default, {
          ref: carouselRef,
          loop: true,
          autoPlay: true,
          autoPlayInterval: _constants.AUTO_PLAY_INTERVAL,
          width: _constants.ACTIVE_WIDTH,
          height: _constants.ACTIVE_HEIGHT + 4,
          style: {
            width: CAROUSEL_WIDTH
          },
          defaultIndex: 0,
          data: carouselData,
          renderItem: renderItem,
          customAnimation: customCarouselAnimationStyle,
          snapEnabled: true,
          onSnapToItem: handleSnapToItem,
          onProgressChange: handleProgressChange
        }), carouselData.length > 1 && (0, _jsxRuntime.jsx)(_indicators.Indicators, {
          ref: indicatorsRef,
          totalItems: carouselData.length,
          duration: _constants.AUTO_PLAY_INTERVAL
        })]
      });
    };
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.sectionContainer,
      children: renderContent()
    });
  });

  /**
   * Component styles
   *
   * Layout Structure:
   * - sectionContainer: Main wrapper with background
   * - cloudContainer: Decorative cloud SVG at top
   * - contentContainer: Wrapper for carousel/loading/error states
   * - itemContent: Individual carousel item wrapper
   * - itemImage: Image within carousel item
   *
   * States:
   * - skeleton: Loading state shimmer
   * - errorContainer: Error state gradient
   * - errorText: Error message styling
   *
   * Note: This component contains several magic numbers for dimensions and spacings
   * (e.g., 40px padding, 76px height, 16px border radius) that are tightly coupled
   * with the design requirements. These values are intentionally not extracted to
   * constants as they are part of complex animations and layout calculations that
   * need to match exactly with the design specifications.
   */
  var styles = _reactNative.StyleSheet.create({
    sectionContainer: {
      backgroundColor: _theme.color.palette.whiteGrey
    },
    contentContainer: {
      height: 112
    },
    itemContent: {
      overflow: "hidden",
      alignItems: "center",
      justifyContent: "center"
    },
    itemImage: {
      width: "100%",
      height: "100%"
    },
    skeleton: {
      height: 76,
      borderRadius: 16,
      alignSelf: "center"
    },
    errorContainer: {
      height: 76,
      borderRadius: 16,
      alignSelf: "center",
      justifyContent: "center",
      paddingHorizontal: 24
    },
    errorText: {
      fontFamily: _theme.typography.bold,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      fontSize: 12,
      lineHeight: 16,
      fontStyle: "normal",
      letterSpacing: 0,
      color: _theme.color.palette.whiteGrey
    }
  });
