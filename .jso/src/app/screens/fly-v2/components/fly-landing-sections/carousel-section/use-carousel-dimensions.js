  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useCarouselDimensions = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _constants = _$$_REQUIRE(_dependencyMap[1]);
  var useCarouselDimensions = exports.useCarouselDimensions = function useCarouselDimensions() {
    var _useWindowDimensions = (0, _reactNative.useWindowDimensions)(),
      SCREEN_WIDTH = _useWindowDimensions.width;
    var CAROUSEL_WIDTH = SCREEN_WIDTH;

    // All banners are position from top left of the carousel
    // These offsets are applied to 3 banners: left, center, right to position them properly
    var CENTER_BANNER_OFFSET = CAROUSEL_WIDTH / 2 - _constants.ACTIVE_WIDTH / 2;
    var LEFT_BANNER_OFFSET = CAROUSEL_WIDTH / 2 - _constants.ACTIVE_WIDTH / 2 - (
    // move banner to center
    _constants.ACTIVE_WIDTH / 2 + _constants.INACTIVE_WIDTH / 2 + _constants.BANNER_GAP); // shift banner left
    var RIGHT_BANNER_OFFSET = CAROUSEL_WIDTH / 2 - _constants.ACTIVE_WIDTH / 2 + (
    // move banner to center
    _constants.ACTIVE_WIDTH / 2 + _constants.INACTIVE_WIDTH / 2 + _constants.BANNER_GAP); // shift banner right
    return {
      SCREEN_WIDTH: SCREEN_WIDTH,
      CAROUSEL_WIDTH: CAROUSEL_WIDTH,
      CENTER_BANNER_OFFSET: CENTER_BANNER_OFFSET,
      LEFT_BANNER_OFFSET: LEFT_BANNER_OFFSET,
      RIGHT_BANNER_OFFSET: RIGHT_BANNER_OFFSET
    };
  };
