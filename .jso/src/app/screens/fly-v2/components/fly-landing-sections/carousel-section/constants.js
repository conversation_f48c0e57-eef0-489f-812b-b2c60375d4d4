  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.SHIMMER_COLORS = exports.INACTIVE_WIDTH = exports.INACTIVE_SCALE = exports.INACTIVE_HEIGHT = exports.INACTIVE_BOTTOM_OFFSET = exports.CAROUSEL_HEIGHT = exports.BANNER_MARGIN = exports.BANNER_GAP = exports.AUTO_PLAY_INTERVAL = exports.ACTIVE_WIDTH = exports.ACTIVE_HEIGHT = exports.ACTIVE_BOTTOM_OFFSET = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var RATIO = 0.25675675675675674;
  var BANNER_MARGIN = exports.BANNER_MARGIN = 40;
  var BANNER_GAP = exports.BANNER_GAP = 16;
  var ACTIVE_WIDTH = exports.ACTIVE_WIDTH = 296;
  var ACTIVE_HEIGHT = exports.ACTIVE_HEIGHT = 76;
  var ACTIVE_BOTTOM_OFFSET = exports.ACTIVE_BOTTOM_OFFSET = 4;
  var INACTIVE_SCALE = exports.INACTIVE_SCALE = 0.5;
  var INACTIVE_WIDTH = exports.INACTIVE_WIDTH = ACTIVE_WIDTH * INACTIVE_SCALE;
  var INACTIVE_HEIGHT = exports.INACTIVE_HEIGHT = INACTIVE_WIDTH * RATIO;
  var INACTIVE_BOTTOM_OFFSET = exports.INACTIVE_BOTTOM_OFFSET = 0;
  var CAROUSEL_HEIGHT = exports.CAROUSEL_HEIGHT = ACTIVE_HEIGHT + ACTIVE_BOTTOM_OFFSET;
  var SHIMMER_COLORS = exports.SHIMMER_COLORS = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var AUTO_PLAY_INTERVAL = exports.AUTO_PLAY_INTERVAL = 3000;
