  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.QuickLinksSection = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _ViewQuickLinks = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _aemQuicklinkCategories = _$$_REQUIRE(_dependencyMap[8]);
  var _icons = _$$_REQUIRE(_dependencyMap[9]);
  var _native = _$$_REQUIRE(_dependencyMap[10]);
  var _constants = _$$_REQUIRE(_dependencyMap[11]);
  var _adobe = _$$_REQUIRE(_dependencyMap[12]);
  var _i18n = _$$_REQUIRE(_dependencyMap[13]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[14]);
  var QuickLinksSection = exports.QuickLinksSection = function QuickLinksSection(_ref) {
    var loading = _ref.loading,
      quickLinks = _ref.quickLinks,
      errorShortcutLinks = _ref.errorShortcutLinks;
    var navigation = (0, _native.useNavigation)();
    var onSeeAirportMap = function onSeeAirportMap() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlightLanding, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlightLanding, `${(0, _i18n.translate)("flyLanding.seeAirportMap")}`));
      navigation.navigate(_constants.NavigationConstants.changiMap);
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.container,
      children: [!loading && (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.headerRow,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          preset: "h6",
          tx: "flyLanding.exploreAndFly"
        }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
          style: styles.headerActionRow,
          onPress: onSeeAirportMap,
          children: [(0, _jsxRuntime.jsx)(_icons.FlightMapIcon, {
            width: 20,
            height: 20,
            style: styles.flightMapIcon
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "textLink",
            tx: "flyLanding.seeAirportMap",
            style: styles.headerAction
          })]
        })]
      }), (0, _jsxRuntime.jsx)(_ViewQuickLinks.default, {
        data: quickLinks,
        loading: loading,
        containerStyle: styles.quickLinksContainer,
        skeletonContainerStyle: styles.skeletonContainer,
        anchorCategoryTitle: _aemQuicklinkCategories.AllQuickLinksAnchorTitle.FlightAndTravel,
        errorShortcutLinks: errorShortcutLinks,
        errorContainerStyle: styles.errorContainer,
        trackingEvar: _adobe.AdobeTagName.FlyQuicklinksRevamped
      })]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    container: {
      marginHorizontal: 16,
      backgroundColor: "transparent"
    },
    headerRow: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      marginTop: 12
    },
    headerActionRow: {
      flexDirection: "row",
      alignItems: "center",
      gap: 8
    },
    headerAction: {
      color: _theme.color.palette.lightPurple,
      lineHeight: 20
    },
    flightMapIcon: {
      marginTop: 2
    },
    quickLinksContainer: {
      marginTop: 8,
      borderRadius: 0,
      backgroundColor: "transparent"
    },
    skeletonContainer: {
      marginTop: 16,
      paddingVertical: 16,
      paddingHorizontal: 8
    },
    errorContainer: {
      marginTop: 24,
      marginBottom: -16
    }
  });
