  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  var _carouselSection = _$$_REQUIRE(_dependencyMap[0]);
  Object.keys(_carouselSection).forEach(function (key) {
    if (key === "default" || key === "__esModule") return;
    if (key in exports && exports[key] === _carouselSection[key]) return;
    Object.defineProperty(exports, key, {
      enumerable: true,
      get: function get() {
        return _carouselSection[key];
      }
    });
  });
  var _quickLinksSections = _$$_REQUIRE(_dependencyMap[1]);
  Object.keys(_quickLinksSections).forEach(function (key) {
    if (key === "default" || key === "__esModule") return;
    if (key in exports && exports[key] === _quickLinksSections[key]) return;
    Object.defineProperty(exports, key, {
      enumerable: true,
      get: function get() {
        return _quickLinksSections[key];
      }
    });
  });
