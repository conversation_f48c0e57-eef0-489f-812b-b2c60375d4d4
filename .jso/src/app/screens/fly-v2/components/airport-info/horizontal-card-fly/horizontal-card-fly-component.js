  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _horizontalCardFlyStyles = _$$_REQUIRE(_dependencyMap[6]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[7]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[8]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _native = _$$_REQUIRE(_dependencyMap[10]);
  var _airportInfo = _$$_REQUIRE(_dependencyMap[11]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _constants = _$$_REQUIRE(_dependencyMap[13]);
  var _airportInfo2 = _$$_REQUIRE(_dependencyMap[14]);
  var _adobe = _$$_REQUIRE(_dependencyMap[15]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[16]);
  var HorizontalCardFly = function HorizontalCardFly(props) {
    var navigation = (0, _native.useNavigation)();
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("FIND_YOUR_WAY"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var data = props.data,
      type = props.type;
    var handleItemOnPress = function handleItemOnPress(item) {
      var _item$cta$navigation, _item$cta, _item$cta2;
      var _ref = item || {},
        airportInfoL2SectionId = _ref.airportInfoL2SectionId;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlightLanding, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlightLanding, `${data == null ? undefined : data.title} | ${item == null ? undefined : item.title}`));
      if (airportInfoL2SectionId) {
        var params = airportInfoL2SectionId ? {
          id: airportInfoL2SectionId
        } : undefined;
        navigation == null || navigation.navigate("airportInfoL2", params);
        return;
      }
      var _ref2 = (_item$cta$navigation = item == null || (_item$cta = item.cta) == null ? undefined : _item$cta.navigation) != null ? _item$cta$navigation : {},
        type = _ref2.type,
        value = _ref2.value;
      var _ref3 = (_item$cta2 = item == null ? undefined : item.cta) != null ? _item$cta2 : {},
        redirect = _ref3.redirect;
      if (!type || !value) return;
      handleNavigation(type, value, redirect);
    };
    var renderItemLoading = function renderItemLoading() {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _horizontalCardFlyStyles.styles.loadingCardContainer,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _airportInfo2.lightGreyLoadingColors,
          shimmerStyle: _horizontalCardFlyStyles.styles.imageStyles
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _horizontalCardFlyStyles.styles.textContainer,
          children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _airportInfo2.lighterGreyLoadingColors,
            shimmerStyle: _horizontalCardFlyStyles.styles.titlePlaceholder
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _airportInfo2.lighterGreyLoadingColors,
            shimmerStyle: _horizontalCardFlyStyles.styles.subtitlePlaceholder
          })]
        })]
      });
    };
    if (type === _airportInfo.ComponentState.loading) {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _airportInfo2.lightGreyLoadingColors,
          shimmerStyle: _horizontalCardFlyStyles.styles.titleLoading
        }), (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
          data: _airportInfo2.loadingSkeletonSmall,
          renderItem: renderItemLoading,
          horizontal: false,
          keyExtractor: function keyExtractor(_item, index) {
            return index.toString();
          },
          showsHorizontalScrollIndicator: false,
          contentContainerStyle: _horizontalCardFlyStyles.styles.loadingContainer
        })]
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _horizontalCardFlyStyles.styles.container,
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        preset: "h4",
        text: data == null ? undefined : data.title,
        numberOfLines: 1,
        style: _horizontalCardFlyStyles.styles.titleStyles
      }), data == null ? undefined : data.components.map(function (item, index) {
        return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
          style: _horizontalCardFlyStyles.styles.itemStyles,
          onPress: function onPress() {
            return handleItemOnPress(item);
          },
          children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
            source: {
              uri: (0, _screenHelper.getUriImage)(item == null ? undefined : item.image)
            },
            style: _horizontalCardFlyStyles.styles.imageStyles,
            resizeMode: "cover"
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _horizontalCardFlyStyles.styles.contentContainer,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              preset: "bodyTextBold",
              text: item == null ? undefined : item.title,
              style: _horizontalCardFlyStyles.styles.textContentStyles,
              numberOfLines: item != null && item.text && item.text.length > 0 ? 1 : 2
            }), !!(item != null && item.text) && (0, _jsxRuntime.jsx)(_text.Text, {
              preset: "caption1Regular",
              text: item == null ? undefined : item.text,
              style: _horizontalCardFlyStyles.styles.textDescriptionStyles,
              numberOfLines: 2
            })]
          })]
        }, index);
      })]
    });
  };
  var _default = exports.default = HorizontalCardFly;
