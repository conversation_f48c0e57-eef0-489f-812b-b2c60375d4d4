  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    container: {
      marginBottom: 40
    },
    contentContainer: {
      flex: 1,
      height: "100%",
      justifyContent: "center",
      marginHorizontal: 16
    },
    errorContainer: {
      marginTop: 0
    },
    imageStyles: {
      borderRadius: 12,
      height: 70,
      width: 70,
      marginLeft: 8
    },
    itemStyles: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      elevation: 5,
      flexDirection: "row",
      height: 86,
      marginBottom: 12,
      marginHorizontal: 24,
      shadowColor: _theme.color.palette.almostBlackGrey,
      shadowOffset: {
        height: 6,
        width: 0
      },
      shadowOpacity: 0.05,
      shadowRadius: 20
    },
    textContentStyles: {
      color: _theme.color.palette.almostBlackGrey
    },
    textDescriptionStyles: {
      color: _theme.color.palette.darkestGrey
    },
    titleStyles: {
      color: _theme.color.palette.almostBlackGrey,
      marginBottom: 16,
      marginHorizontal: 24,
      letterSpacing: 0,
      fontSize: 16,
      lineHeight: 20,
      textTransform: "none",
      fontFamily: _theme.typography.black,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      })
    },
    titleLoading: {
      borderRadius: 4,
      height: 20,
      marginLeft: 24,
      width: 152,
      marginBottom: 18
    },
    loadingContainer: {
      paddingRight: 12,
      paddingLeft: 24,
      gap: 12,
      marginBottom: 24
    },
    loadingCardContainer: {
      gap: 16,
      display: "flex",
      flexDirection: "row",
      alignItems: "center",
      height: 86,
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      elevation: 5,
      shadowColor: _theme.color.palette.almostBlackGrey,
      shadowOffset: {
        height: 6,
        width: 0
      },
      shadowOpacity: 0.05,
      shadowRadius: 20,
      padding: 8
    },
    textContainer: {
      gap: 4,
      flexDirection: "column"
    },
    titlePlaceholder: {
      borderRadius: 4,
      height: 18,
      width: 165
    },
    subtitlePlaceholder: {
      borderRadius: 4,
      height: 18,
      width: 63
    }
  });
