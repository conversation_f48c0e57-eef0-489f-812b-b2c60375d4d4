  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    bottomContainer: {
      margin: 12,
      width: 63,
      borderRadius: 4
    },
    cardContainer: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      bottom: 0,
      elevation: 5,
      height: 148,
      justifyContent: "flex-end",
      position: "absolute",
      shadowColor: _theme.color.palette.almostBlackGrey,
      shadowOffset: {
        height: 6,
        width: 0
      },
      shadowOpacity: 0.08,
      shadowRadius: 20,
      width: 140
    },
    container: {
      height: 172,
      width: 140,
      marginBottom: 40
    },
    listContainer: {
      paddingLeft: 24,
      gap: 12,
      paddingRight: 12
    },
    textStyles: {
      color: _theme.color.palette.almostBlackGrey,
      width: 116
    },
    thumbnailContainer: {
      elevation: 6,
      zIndex: 1
    },
    thumbnailStyles: {
      borderRadius: 12,
      height: 128,
      left: 12,
      position: "absolute",
      right: 0,
      top: 0,
      width: 116
    },
    titleLoading: {
      borderRadius: 4,
      height: 20,
      marginLeft: 24,
      width: 152,
      marginBottom: 18
    },
    titleStyles: {
      marginHorizontal: 24,
      marginBottom: 18,
      letterSpacing: 0,
      fontSize: 16,
      lineHeight: 20,
      textTransform: "none",
      fontFamily: _theme.typography.black,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      })
    }
  });
