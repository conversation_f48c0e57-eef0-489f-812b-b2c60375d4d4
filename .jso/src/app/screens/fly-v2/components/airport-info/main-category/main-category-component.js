  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _constants = _$$_REQUIRE(_dependencyMap[5]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[7]);
  var _native = _$$_REQUIRE(_dependencyMap[8]);
  var _mainCategoryStyles = _$$_REQUIRE(_dependencyMap[9]);
  var _mainCategoryItem = _$$_REQUIRE(_dependencyMap[10]);
  var _airportInfo = _$$_REQUIRE(_dependencyMap[11]);
  var _airportInfo2 = _$$_REQUIRE(_dependencyMap[12]);
  var _adobe = _$$_REQUIRE(_dependencyMap[13]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[14]);
  var MainCategory = function MainCategory(props) {
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("SAFE_TRAVEL_COMPONENT"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var navigation = (0, _native.useNavigation)();
    var type = props.type,
      data = props.data,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "MainCategoryComponent" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "MainCategoryComponent" : _props$accessibilityL;
    var handleItemOnPress = function handleItemOnPress(item) {
      var _item$cta$navigation, _item$cta, _item$cta2;
      var _ref = item || {},
        airportInfoL2SectionId = _ref.airportInfoL2SectionId;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlightLanding, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlightLanding, `${data == null ? undefined : data.title} | ${item == null ? undefined : item.title}`));
      if (airportInfoL2SectionId) {
        var params = airportInfoL2SectionId ? {
          id: airportInfoL2SectionId
        } : undefined;
        navigation == null || navigation.navigate("airportInfoL2", params);
        return;
      }
      var _ref2 = (_item$cta$navigation = item == null || (_item$cta = item.cta) == null ? undefined : _item$cta.navigation) != null ? _item$cta$navigation : {},
        type = _ref2.type,
        value = _ref2.value;
      var _ref3 = (_item$cta2 = item == null ? undefined : item.cta) != null ? _item$cta2 : {},
        redirect = _ref3.redirect;
      if (!type || !value) return;
      handleNavigation(type, value, redirect);
    };
    var renderItem = function renderItem(_ref4) {
      var item = _ref4.item,
        index = _ref4.index;
      return (0, _jsxRuntime.jsx)(_mainCategoryItem.MainCategoryItem, {
        item: item,
        onPress: function onPress() {
          return handleItemOnPress(item);
        },
        testID: `${testID}__MainCategoryItem__${index}`,
        accessibilityLabel: `${accessibilityLabel}__MainCategoryItem__${index}`
      });
    };
    var renderItemLoading = function renderItemLoading() {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _mainCategoryStyles.styles.container,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _airportInfo2.lightGreyLoadingColors,
          shimmerStyle: [_mainCategoryStyles.styles.thumbnailStyles, _mainCategoryStyles.styles.thumbnailContainer]
        }), (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _mainCategoryStyles.styles.cardContainer,
          children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _airportInfo2.lighterGreyLoadingColors,
            shimmerStyle: _mainCategoryStyles.styles.bottomContainer
          })
        })]
      });
    };
    if (type === _airportInfo.ComponentState.loading) {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _airportInfo2.lightGreyLoadingColors,
          shimmerStyle: _mainCategoryStyles.styles.titleLoading
        }), (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
          data: _airportInfo2.loadingSkeletonLarge,
          renderItem: renderItemLoading,
          horizontal: true,
          keyExtractor: function keyExtractor(_item, index) {
            return index.toString();
          },
          showsHorizontalScrollIndicator: false,
          contentContainerStyle: _mainCategoryStyles.styles.listContainer
        })]
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        preset: "h4",
        text: data == null ? undefined : data.title,
        style: _mainCategoryStyles.styles.titleStyles,
        numberOfLines: 1
      }), (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
        data: data == null ? undefined : data.components,
        renderItem: renderItem,
        horizontal: true,
        showsHorizontalScrollIndicator: false,
        contentContainerStyle: _mainCategoryStyles.styles.listContainer,
        testID: `${testID}__FlatList`,
        accessibilityLabel: `${accessibilityLabel}__FlatList`
      })]
    });
  };
  var _default = exports.default = MainCategory;
