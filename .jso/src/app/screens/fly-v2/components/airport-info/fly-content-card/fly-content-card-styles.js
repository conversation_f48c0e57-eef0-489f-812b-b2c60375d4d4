  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    bottomContainer: {
      flex: 1,
      margin: 16,
      marginTop: 12
    },
    container: Object.assign({
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      height: "auto",
      marginBottom: 40,
      marginRight: 12,
      marginTop: 16
    }, _reactNative.Platform.select({
      ios: {
        shadowRadius: 16,
        shadowOpacity: 0.08,
        shadowOffset: {
          width: 0,
          height: 3
        },
        backgroundColor: _theme.color.palette.whiteGrey
      },
      android: {
        elevation: 3,
        backgroundColor: _theme.color.palette.whiteGrey
      }
    }), {
      width: 192
    }),
    loadingContainer: {
      marginBottom: 40,
      paddingRight: 12,
      paddingLeft: 24,
      gap: 12
    },
    textStyles: {
      color: _theme.color.palette.almostBlackGrey
    },
    titleStyles: {
      color: _theme.color.palette.almostBlackGrey,
      marginHorizontal: 24,
      letterSpacing: 0,
      fontSize: 16,
      lineHeight: 20,
      textTransform: "none",
      fontFamily: _theme.typography.black,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      })
    },
    thumbnailStyles: {
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      height: 120
    },
    titleLoading: {
      borderRadius: 4,
      height: 20,
      marginLeft: 24,
      width: 152,
      marginBottom: 18
    },
    cardContainer: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderBottomLeftRadius: 16,
      borderBottomRightRadius: 16,
      elevation: 5,
      height: 88,
      shadowColor: _theme.color.palette.almostBlackGrey,
      shadowOffset: {
        height: 6,
        width: 0
      },
      shadowOpacity: 0.2,
      shadowRadius: 20,
      paddingHorizontal: 16,
      paddingTop: 12,
      gap: 8
    },
    titlePlaceholder: {
      borderRadius: 4,
      height: 13,
      width: 160
    },
    subtitlePlaceholder: {
      borderRadius: 4,
      height: 13,
      width: 80
    },
    listContainer: {
      marginLeft: 24,
      paddingRight: 24
    }
  });
