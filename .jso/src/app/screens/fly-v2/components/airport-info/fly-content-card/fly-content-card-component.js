  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _native = _$$_REQUIRE(_dependencyMap[5]);
  var _flyContentCardItem = _$$_REQUIRE(_dependencyMap[6]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[7]);
  var _airportInfo = _$$_REQUIRE(_dependencyMap[8]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _constants = _$$_REQUIRE(_dependencyMap[10]);
  var _airportInfo2 = _$$_REQUIRE(_dependencyMap[11]);
  var _flyContentCardStyles = _$$_REQUIRE(_dependencyMap[12]);
  var _adobe = _$$_REQUIRE(_dependencyMap[13]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[14]);
  var FlyContentCard = function FlyContentCard(props) {
    var navigation = (0, _native.useNavigation)();
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("SPECIAL_ASSISTANCE_COMPONENT"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var data = props.data,
      type = props.type,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "FlyContentCardComponent" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "FlyContentCardComponent" : _props$accessibilityL;
    var handleItemOnPress = function handleItemOnPress(item) {
      var _item$cta$navigation, _item$cta, _item$cta2;
      var _ref = item || {},
        airportInfoL2SectionId = _ref.airportInfoL2SectionId;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlightLanding, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlightLanding, `${data == null ? undefined : data.title} | ${item == null ? undefined : item.title}`));
      if (airportInfoL2SectionId) {
        var params = airportInfoL2SectionId ? {
          id: airportInfoL2SectionId
        } : undefined;
        navigation == null || navigation.navigate("airportInfoL2", params);
        return;
      }
      var _ref2 = (_item$cta$navigation = item == null || (_item$cta = item.cta) == null ? undefined : _item$cta.navigation) != null ? _item$cta$navigation : {},
        type = _ref2.type,
        value = _ref2.value;
      var _ref3 = (_item$cta2 = item == null ? undefined : item.cta) != null ? _item$cta2 : {},
        redirect = _ref3.redirect;
      if (!type || !value) return;
      handleNavigation(type, value, redirect);
    };
    var renderItem = function renderItem(_ref4) {
      var item = _ref4.item,
        index = _ref4.index;
      return (0, _jsxRuntime.jsx)(_flyContentCardItem.FlyContentCardItem, {
        item: item,
        index: index,
        onPress: function onPress() {
          return handleItemOnPress(item);
        },
        testID: `${testID}__SpecialAssistanceItem__${index}`,
        accessibilityLabel: `${accessibilityLabel}__SpecialAssistanceItem__${index}`
      });
    };
    var renderItemLoading = function renderItemLoading() {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _airportInfo2.lightGreyLoadingColors,
          shimmerStyle: _flyContentCardStyles.styles.thumbnailStyles
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: _flyContentCardStyles.styles.cardContainer,
          children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _airportInfo2.lighterGreyLoadingColors,
            shimmerStyle: _flyContentCardStyles.styles.titlePlaceholder
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _airportInfo2.lighterGreyLoadingColors,
            shimmerStyle: _flyContentCardStyles.styles.subtitlePlaceholder
          })]
        })]
      });
    };
    if (type === _airportInfo.ComponentState.loading) {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _airportInfo2.lightGreyLoadingColors,
          shimmerStyle: _flyContentCardStyles.styles.titleLoading
        }), (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
          data: _airportInfo2.loadingSkeletonLarge,
          renderItem: renderItemLoading,
          horizontal: true,
          keyExtractor: function keyExtractor(_item, index) {
            return index.toString();
          },
          showsHorizontalScrollIndicator: false,
          contentContainerStyle: _flyContentCardStyles.styles.loadingContainer
        })]
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        preset: "h4",
        text: data == null ? undefined : data.title,
        style: _flyContentCardStyles.styles.titleStyles,
        numberOfLines: 1
      }), (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
        data: data == null ? undefined : data.components,
        renderItem: renderItem,
        horizontal: true,
        showsHorizontalScrollIndicator: false,
        testID: `${testID}__FlatListSpecialAssistance`,
        keyExtractor: function keyExtractor(_, index) {
          return index.toString();
        },
        accessibilityLabel: `${accessibilityLabel}__FlatListSpecialAssistance`,
        contentContainerStyle: _flyContentCardStyles.styles.listContainer
      })]
    });
  };
  var _default = exports.default = FlyContentCard;
