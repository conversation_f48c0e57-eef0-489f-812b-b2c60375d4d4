  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[3]);
  var _lodash = _$$_REQUIRE(_dependencyMap[4]);
  var _cardWithIllustrations = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _aemRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _mainCategoryComponent = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _airportInfo = _$$_REQUIRE(_dependencyMap[8]);
  var _flyContentCardComponent = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _horizontalCardFlyComponent = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _airportInfoError = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  var COMPONENT_NAME = "AirportInfo";
  var AirportInfo = function AirportInfo() {
    var aemData = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig("airportLevel1"));
    var loading = (0, _lodash.get)(aemData, "loading");
    var error = (0, _lodash.get)(aemData, "error");
    var metaDataAirportInfo = (0, _lodash.get)(aemData, "data.list");
    var renderViewByComponentType = function renderViewByComponentType(airportInfoItem) {
      var _airportInfoItem$comp;
      switch ((_airportInfoItem$comp = airportInfoItem.components) == null || (_airportInfoItem$comp = _airportInfoItem$comp[0]) == null ? undefined : _airportInfoItem$comp.componentType.tagName) {
        case _airportInfo.componentType.mainCategory:
          return (0, _jsxRuntime.jsx)(_mainCategoryComponent.default, {
            data: airportInfoItem,
            testID: `${COMPONENT_NAME}__MainCategoryComponent`,
            accessibilityLabel: `${COMPONENT_NAME}__MainCategoryComponent`
          }, `${COMPONENT_NAME}-${airportInfoItem.sectionId}`);
        case _airportInfo.componentType.cardWithIllustrations:
          return (0, _jsxRuntime.jsx)(_cardWithIllustrations.default, {
            data: airportInfoItem,
            testID: `${COMPONENT_NAME}__CardWithIllustrationsComponent`,
            accessibilityLabel: `${COMPONENT_NAME}__CardWithIllustrationsComponent`
          }, `${COMPONENT_NAME}-${airportInfoItem.sectionId}`);
        case _airportInfo.componentType.flyContentCard:
          return (0, _jsxRuntime.jsx)(_flyContentCardComponent.default, {
            data: airportInfoItem,
            testID: `${COMPONENT_NAME}__FlyContentCardComponent`,
            accessibilityLabel: `${COMPONENT_NAME}__FlyContentCardComponent`
          }, `${COMPONENT_NAME}-${airportInfoItem.sectionId}`);
        case _airportInfo.componentType.horizontalCardFly:
          return (0, _jsxRuntime.jsx)(_horizontalCardFlyComponent.default, {
            data: airportInfoItem,
            testID: `${COMPONENT_NAME}__HorizontalCardFlyComponent`,
            accessibilityLabel: `${COMPONENT_NAME}__HorizontalCardFlyComponent`
          }, `${COMPONENT_NAME}-${airportInfoItem.sectionId}`);
        default:
          return null;
      }
    };
    if (loading) {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.loadingContainer,
        children: [(0, _jsxRuntime.jsx)(_mainCategoryComponent.default, {
          type: _airportInfo.ComponentState.loading
        }), (0, _jsxRuntime.jsx)(_cardWithIllustrations.default, {
          type: _airportInfo.ComponentState.loading
        }), (0, _jsxRuntime.jsx)(_flyContentCardComponent.default, {
          type: _airportInfo.ComponentState.loading
        }), (0, _jsxRuntime.jsx)(_horizontalCardFlyComponent.default, {
          type: _airportInfo.ComponentState.loading
        })]
      });
    }
    if (error) {
      return (0, _jsxRuntime.jsx)(_airportInfoError.default, {});
    }
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      testID: `${COMPONENT_NAME}__ScrollViewAirportLanding`,
      accessibilityLabel: `${COMPONENT_NAME}__ScrollViewAirportLanding`,
      style: {
        marginTop: 40,
        marginBottom: 52
      },
      children: metaDataAirportInfo == null ? undefined : metaDataAirportInfo.map(function (airportInfoItem) {
        return renderViewByComponentType(airportInfoItem);
      })
    });
  };
  var _default = exports.default = AirportInfo;
  var styles = _reactNative.StyleSheet.create({
    loadingContainer: {
      marginTop: 40,
      display: "flex",
      flexDirection: "column"
    }
  });
