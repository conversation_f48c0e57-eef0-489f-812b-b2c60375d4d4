  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _i18n = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[2]);
  var _theme = _$$_REQUIRE(_dependencyMap[3]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var AirportInfoError = function AirportInfoError() {
    var informativeCommon = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var EHR43 = informativeCommon == null ? undefined : informativeCommon.find(function (e) {
      return (e == null ? undefined : e.code) === "EHR43";
    });
    var errorMessage = (0, _react.useMemo)(function () {
      var str = EHR43 == null ? undefined : EHR43.subHeader;
      if (!str) {
        return `${(0, _i18n.translate)("errorAirport.content")}\n \n${(0, _i18n.translate)("errorAirport.subContent")}`;
      }
      if (str.includes("<enter>")) {
        return str.replace("<enter>", "\n \n");
      }
      return str;
    }, [EHR43 == null ? undefined : EHR43.subHeader]);
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: styles.title,
        children: (EHR43 == null ? undefined : EHR43.header) || (0, _i18n.translate)("errorAirport.title")
      }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: styles.message,
        children: errorMessage
      })]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    container: {
      marginHorizontal: 16,
      alignItems: "center",
      justifyContent: "center",
      backgroundColor: "transparent",
      marginTop: 64
    },
    title: {
      fontFamily: _theme.typography.bold,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      fontSize: 24,
      lineHeight: 32,
      letterSpacing: 0,
      textAlign: "center",
      color: _theme.color.palette.almostBlackGrey,
      marginBottom: 12
    },
    message: {
      fontFamily: _theme.typography.regular,
      fontWeight: _reactNative2.Platform.select({
        ios: "400",
        android: "normal"
      }),
      fontSize: 16,
      lineHeight: 20,
      letterSpacing: 0,
      textAlign: "center",
      color: _theme.color.palette.darkestGrey
    }
  });
  var _default = exports.default = AirportInfoError;
