  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _native = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _constants = _$$_REQUIRE(_dependencyMap[7]);
  var _theme = _$$_REQUIRE(_dependencyMap[8]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[9]);
  var _airportInfo = _$$_REQUIRE(_dependencyMap[10]);
  var _cardWithIllustrationsItem = _$$_REQUIRE(_dependencyMap[11]);
  var _airportInfo2 = _$$_REQUIRE(_dependencyMap[12]);
  var _adobe = _$$_REQUIRE(_dependencyMap[13]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[14]);
  var CardWithIllustrations = function CardWithIllustrations(props) {
    var _data$components;
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("LIST_TRAVEL_MADE_CONVENIENT"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var navigation = (0, _native.useNavigation)();
    var type = props.type,
      data = props.data,
      testID = props.testID,
      accessibilityLabel = props.accessibilityLabel;
    var contents = (_data$components = data == null ? undefined : data.components) != null ? _data$components : [];
    var handleItemOnPress = function handleItemOnPress(item) {
      var _item$cta$navigation, _item$cta, _item$cta2;
      var _ref = item || {},
        airportInfoL2SectionId = _ref.airportInfoL2SectionId;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlightLanding, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlightLanding, `${data == null ? undefined : data.title} | ${item == null ? undefined : item.title}`));
      if (airportInfoL2SectionId) {
        var params = airportInfoL2SectionId ? {
          id: airportInfoL2SectionId
        } : undefined;
        navigation == null || navigation.navigate("airportInfoL2", params);
        return;
      }
      var _ref2 = (_item$cta$navigation = item == null || (_item$cta = item.cta) == null ? undefined : _item$cta.navigation) != null ? _item$cta$navigation : {},
        type = _ref2.type,
        value = _ref2.value;
      var _ref3 = (_item$cta2 = item == null ? undefined : item.cta) != null ? _item$cta2 : {},
        redirect = _ref3.redirect;
      if (!type || !value) return;
      handleNavigation(type, value, redirect);
    };
    var renderItemLoading = function renderItemLoading() {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _airportInfo2.lightGreyLoadingColors,
          shimmerStyle: styles.thumbnailStyles
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.cardContainer,
          children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _airportInfo2.lighterGreyLoadingColors,
            shimmerStyle: styles.titlePlaceholder
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _airportInfo2.lighterGreyLoadingColors,
            shimmerStyle: styles.titlePlaceholder
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _airportInfo2.lighterGreyLoadingColors,
            shimmerStyle: styles.subtitlePlaceholder
          })]
        })]
      });
    };
    if (type === _airportInfo.ComponentState.loading) {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _airportInfo2.lightGreyLoadingColors,
          shimmerStyle: styles.titleLoading
        }), (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
          data: _airportInfo2.loadingSkeletonLarge,
          renderItem: renderItemLoading,
          horizontal: true,
          keyExtractor: function keyExtractor(_item, index) {
            return index.toString();
          },
          showsHorizontalScrollIndicator: false,
          contentContainerStyle: styles.loadingContainer
        })]
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        text: data == null ? undefined : data.title,
        preset: "h4",
        style: styles.textTitle,
        numberOfLines: 1
      }), (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
        horizontal: true,
        data: contents,
        renderItem: function renderItem(_ref4) {
          var item = _ref4.item,
            index = _ref4.index;
          return (0, _jsxRuntime.jsx)(_cardWithIllustrationsItem.CardWithIllustrationsItem, {
            item: item,
            onPress: function onPress(itemData) {
              return handleItemOnPress(itemData);
            },
            testID: `${testID}__ItemTravelMadeConvenient__${index}`,
            accessibilityLabel: `${accessibilityLabel}__ItemTravelMadeConvenient__${index}`
          });
        },
        contentContainerStyle: styles.listContainer,
        keyExtractor: function keyExtractor(_, index) {
          return `key_travel_made_convenient_${index}`;
        },
        showsHorizontalScrollIndicator: false,
        testID: `${testID}__FlatList`,
        accessibilityLabel: `${accessibilityLabel}__FlatList`
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    bottomContainer: {
      borderRadius: 4,
      height: 13
    },
    cardContainer: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderBottomLeftRadius: 16,
      borderBottomRightRadius: 16,
      elevation: 5,
      height: 88,
      shadowColor: _theme.color.palette.almostBlackGrey,
      shadowOffset: {
        height: 6,
        width: 0
      },
      shadowOpacity: 0.2,
      shadowRadius: 20,
      paddingHorizontal: 16,
      paddingTop: 12,
      gap: 8
    },
    listContainer: {
      marginLeft: 24,
      paddingRight: 24,
      marginBottom: 40
    },
    textTitle: {
      marginHorizontal: 24,
      marginBottom: 18,
      letterSpacing: 0,
      fontSize: 16,
      lineHeight: 20,
      textTransform: "none",
      fontFamily: _theme.typography.black,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      })
    },
    thumbnailStyles: {
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      height: 120
    },
    titleLoading: {
      borderRadius: 4,
      height: 20,
      marginLeft: 24,
      width: 152,
      marginBottom: 18
    },
    loadingContainer: {
      marginBottom: 40,
      paddingRight: 12,
      paddingLeft: 24,
      gap: 12
    },
    titlePlaceholder: {
      borderRadius: 4,
      height: 13,
      width: 160
    },
    subtitlePlaceholder: {
      borderRadius: 4,
      height: 13,
      width: 104
    }
  });
  var _default = exports.default = CardWithIllustrations;
