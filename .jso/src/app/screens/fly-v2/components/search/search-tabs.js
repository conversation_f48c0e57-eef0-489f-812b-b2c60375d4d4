  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.SearchTabs = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _constants = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _searchSvgHeaders = _$$_REQUIRE(_dependencyMap[7]);
  var _palette = _$$_REQUIRE(_dependencyMap[8]);
  var _icons = _$$_REQUIRE(_dependencyMap[9]);
  var _text = _$$_REQUIRE(_dependencyMap[10]);
  var _theme = _$$_REQUIRE(_dependencyMap[11]);
  var _adobe = _$$_REQUIRE(_dependencyMap[12]);
  var _i18n = _$$_REQUIRE(_dependencyMap[13]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[14]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  // SEARCH TABS COMPONENT DOCUMENTATION
  //
  // This component implements a custom animated tab switcher for flight search (Arrival/Departure).
  // It is visually complex, combining SVG headers (from Figma) and animated content transitions.
  //
  // STRUCTURE OVERVIEW:
  // 1. Inactive Headers: Static UI showing both tabs (not interactive, for background only).
  // 2. Active Headers: Two animated SVG headers (Arrival and Departure). Only one is visible at a time, and they animate horizontally when switching tabs. Each header has an invisible touch area to trigger tab switching.
  // 3. Content: Contains the tab content area, which is animated horizontally to match the selected tab. The content area includes:
  //    - Content Frame: The outer border/frame (with rounded corners and border).
  //    - Content Background: The white background with rounded corners, offset to align with the SVG header.
  //    - Content Row: A row containing two copies of the content (one for each tab), which slides horizontally during tab switch.
  //
  // ANIMATION & LAYOUT NOTES:
  // - SVG headers are exported at a fixed size from Figma and scaled to fit the screen width.
  // - The content and headers are offset and layered to create a seamless visual transition between tabs and headers.
  // - The content for both tabs is rendered (cloned) and animated horizontally; only the active tab's content is visible at a time.
  // - Tab switching is handled with reanimated transitions and invisible touchable areas over the headers.
  //
  // DEVELOPER NOTES:
  // - The component uses several workarounds to align SVG and native content, including manual offsets and scaling factors.
  // - All layout constants are calculated based on screen width and original SVG dimensions for responsiveness.
  // - The component is memoized for performance.
  //
  // Props:
  // - selectedTab: Which tab is currently active (FlightDirection.Arrival or Departure)
  // - onSelectTab: Callback when a tab is selected
  // - children: The content to render inside the tab area (will be cloned for both tabs)
  var SCREEN_WIDTH = _reactNative2.Dimensions.get("screen").width;
  var BORDER_WIDTH = 1;
  var CONTAINER_WIDTH = Math.min(SCREEN_WIDTH - 32, 376);
  var CONTENT_WIDTH = CONTAINER_WIDTH - 2;
  var SCALE_FACTOR = CONTAINER_WIDTH / _searchSvgHeaders.ORIGINAL_ACTIVE_HEADER.WIDTH;
  var ORIGINAL_INACTIVE_HEADER = {
    HEIGHT: 48
  };
  // SVG Headers are exported with fixed size. Need to scale according to screen size
  var SCALED_ACTIVE_HEADER = {
    WIDTH: Math.floor(CONTAINER_WIDTH),
    HEIGHT: Math.floor(_searchSvgHeaders.ORIGINAL_ACTIVE_HEADER.HEIGHT * SCALE_FACTOR + 1),
    // need to add 1 to make header display properly with scaled size,
    CONTENT_OFFSET: Math.floor(_searchSvgHeaders.ORIGINAL_ACTIVE_HEADER.CONTENT_OFFSET * SCALE_FACTOR)
  };
  var CONTENT_FRAME_OFFSET = 4;
  var CONTENT_BACKGROUND_OFFSET = SCALED_ACTIVE_HEADER.CONTENT_OFFSET - CONTENT_FRAME_OFFSET;
  var _worklet_2849852132290_init_data = {
    code: "function searchTabsTsx1(){const{arrivalHeaderX}=this.__closure;return{transform:[{translateX:arrivalHeaderX.value}],position:\"absolute\"};}"
  };
  var _worklet_1121003987809_init_data = {
    code: "function searchTabsTsx2(){const{departureHeaderX}=this.__closure;return{transform:[{translateX:departureHeaderX.value}],position:\"absolute\"};}"
  };
  var _worklet_184151500280_init_data = {
    code: "function searchTabsTsx3(){const{contentX}=this.__closure;return{transform:[{translateX:contentX.value}]};}"
  };
  var _worklet_2743745740206_init_data = {
    code: "function searchTabsTsx4(finished){const{runOnJS,onSelectTab,direction}=this.__closure;return finished&&runOnJS(onSelectTab)(direction);}"
  };
  var _worklet_9018087183023_init_data = {
    code: "function searchTabsTsx5(finished){const{runOnJS,onSelectTab,direction}=this.__closure;return finished&&runOnJS(onSelectTab)(direction);}"
  };
  var SearchTabs = exports.SearchTabs = (0, _react.memo)(function (props) {
    var selectedTab = props.selectedTab,
      onSelectTab = props.onSelectTab,
      children = props.children;
    var arrivalHeaderX = (0, _reactNativeReanimated.useSharedValue)(selectedTab === _constants.FlightDirection.Arrival ? 0 : CONTAINER_WIDTH);
    var departureHeaderX = (0, _reactNativeReanimated.useSharedValue)(selectedTab === _constants.FlightDirection.Arrival ? -CONTAINER_WIDTH : 0);
    var contentX = (0, _reactNativeReanimated.useSharedValue)(0);
    (0, _react.useEffect)(function () {
      arrivalHeaderX.value = selectedTab === _constants.FlightDirection.Arrival ? 0 : CONTAINER_WIDTH;
      departureHeaderX.value = selectedTab === _constants.FlightDirection.Arrival ? -CONTAINER_WIDTH : 0;
      contentX.value = selectedTab === _constants.FlightDirection.Arrival ? 0 : -CONTAINER_WIDTH;
    }, [selectedTab]);
    var animatedArrivalStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var searchTabsTsx1 = function searchTabsTsx1() {
        return {
          transform: [{
            translateX: arrivalHeaderX.value
          }],
          position: "absolute"
        };
      };
      searchTabsTsx1.__closure = {
        arrivalHeaderX: arrivalHeaderX
      };
      searchTabsTsx1.__workletHash = 2849852132290;
      searchTabsTsx1.__initData = _worklet_2849852132290_init_data;
      return searchTabsTsx1;
    }());
    var animatedDepartureStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var searchTabsTsx2 = function searchTabsTsx2() {
        return {
          transform: [{
            translateX: departureHeaderX.value
          }],
          position: "absolute"
        };
      };
      searchTabsTsx2.__closure = {
        departureHeaderX: departureHeaderX
      };
      searchTabsTsx2.__workletHash = 1121003987809;
      searchTabsTsx2.__initData = _worklet_1121003987809_init_data;
      return searchTabsTsx2;
    }());
    var animatedContentRowStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var searchTabsTsx3 = function searchTabsTsx3() {
        return {
          transform: [{
            translateX: contentX.value
          }]
        };
      };
      searchTabsTsx3.__closure = {
        contentX: contentX
      };
      searchTabsTsx3.__workletHash = 184151500280;
      searchTabsTsx3.__initData = _worklet_184151500280_init_data;
      return searchTabsTsx3;
    }());
    var selectTab = (0, _react.useCallback)(function (direction) {
      _reactNative2.Keyboard.dismiss();
      var trackingLabelTx = direction === _constants.FlightDirection.Arrival ? "flightLanding.arrivalTabTitle" : "flightLanding.departureTabTitle";
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlightLanding, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlightLanding, `Flight Search | ${(0, _i18n.translate)(trackingLabelTx)} tab`));
      if (direction === _constants.FlightDirection.Arrival) {
        arrivalHeaderX.value = (0, _reactNativeReanimated.withSequence)((0, _reactNativeReanimated.withTiming)(CONTAINER_WIDTH / 2, {
          duration: 0
        }), (0, _reactNativeReanimated.withTiming)(0, {
          duration: 300
        }));
        departureHeaderX.value = (0, _reactNativeReanimated.withTiming)(-CONTAINER_WIDTH, {
          duration: 0
        });
        contentX.value = (0, _reactNativeReanimated.withTiming)(0, {
          duration: 300
        }, function () {
          var searchTabsTsx4 = function searchTabsTsx4(finished) {
            return finished && (0, _reactNativeReanimated.runOnJS)(onSelectTab)(direction);
          };
          searchTabsTsx4.__closure = {
            runOnJS: _reactNativeReanimated.runOnJS,
            onSelectTab: onSelectTab,
            direction: direction
          };
          searchTabsTsx4.__workletHash = 2743745740206;
          searchTabsTsx4.__initData = _worklet_2743745740206_init_data;
          return searchTabsTsx4;
        }());
      } else {
        arrivalHeaderX.value = (0, _reactNativeReanimated.withTiming)(CONTAINER_WIDTH, {
          duration: 0
        });
        departureHeaderX.value = (0, _reactNativeReanimated.withSequence)((0, _reactNativeReanimated.withTiming)(-(CONTAINER_WIDTH / 2), {
          duration: 0
        }), (0, _reactNativeReanimated.withTiming)(0, {
          duration: 300
        }));
        contentX.value = (0, _reactNativeReanimated.withTiming)(-CONTAINER_WIDTH, {
          duration: 300
        }, function () {
          var searchTabsTsx5 = function searchTabsTsx5(finished) {
            return finished && (0, _reactNativeReanimated.runOnJS)(onSelectTab)(direction);
          };
          searchTabsTsx5.__closure = {
            runOnJS: _reactNativeReanimated.runOnJS,
            onSelectTab: onSelectTab,
            direction: direction
          };
          searchTabsTsx5.__workletHash = 9018087183023;
          searchTabsTsx5.__initData = _worklet_9018087183023_init_data;
          return searchTabsTsx5;
        }());
      }
    }, [arrivalHeaderX, departureHeaderX, onSelectTab, selectedTab]);
    var clonedContent = (0, _react.useMemo)(function () {
      return _react.default.cloneElement(children);
    }, [children]);
    return (0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.inactiveHeadersContainer,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.inactiveHeaderButton,
          children: [(0, _jsxRuntime.jsx)(_icons.FlightArrivalIcon, {
            width: 24,
            height: 24,
            color: _theme.color.palette.midGrey
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            tx: "flightLanding.arrivalTabTitle",
            style: styles.inactiveHeaderTitle
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.inactiveHeaderButton,
          children: [(0, _jsxRuntime.jsx)(_icons.FlightDepartureIcon, {
            width: 24,
            height: 24,
            color: _theme.color.palette.midGrey
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            tx: "flightLanding.departureTabTitle",
            style: styles.inactiveHeaderTitle
          })]
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.activeHeadersContainer,
        children: [(0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
          style: animatedArrivalStyle,
          children: [(0, _jsxRuntime.jsx)(_searchSvgHeaders.ActiveArrivalHeader, {
            width: SCALED_ACTIVE_HEADER.WIDTH,
            height: SCALED_ACTIVE_HEADER.HEIGHT
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: styles.pseudoDepartureHeaderButton,
            onPress: function onPress() {
              return selectTab(_constants.FlightDirection.Departure);
            },
            testID: "FlyLandingV2__SearchTabs__DepartureTab",
            accessibilityLabel: "FlyLandingV2__SearchTabs__DepartureTab"
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
          style: animatedDepartureStyle,
          children: [(0, _jsxRuntime.jsx)(_searchSvgHeaders.ActiveDepartureHeader, {
            width: SCALED_ACTIVE_HEADER.WIDTH,
            height: SCALED_ACTIVE_HEADER.HEIGHT
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: styles.pseudoArrivalHeaderButton,
            onPress: function onPress() {
              return selectTab(_constants.FlightDirection.Arrival);
            },
            testID: "FlyLandingV2__SearchTabs__ArrivalTab",
            accessibilityLabel: "FlyLandingV2__SearchTabs__ArrivalTab"
          })]
        })]
      }), (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
        style: styles.contentFrame,
        children: (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
          style: styles.contentBackground,
          children: (0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
            style: [animatedContentRowStyle, {
              flexDirection: "row"
            }],
            children: [(0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
              style: styles.content,
              children: props.children
            }), (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
              style: styles.content,
              children: clonedContent
            })]
          })
        })
      })]
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    container: {
      shadowColor: "#121212",
      shadowOpacity: 0.08,
      shadowOffset: {
        width: 0,
        height: 6
      },
      shadowRadius: 20
    },
    inactiveHeadersContainer: {
      position: "absolute",
      top: 4,
      left: 1,
      right: 1,
      height: ORIGINAL_INACTIVE_HEADER.HEIGHT * SCALE_FACTOR,
      flexDirection: "row",
      alignItems: "stretch",
      backgroundColor: _palette.palette.lightestGrey,
      borderTopRightRadius: 16,
      borderTopLeftRadius: 16,
      borderColor: _palette.palette.lighterGrey,
      borderWidth: 1,
      borderBottomWidth: 0
    },
    inactiveHeaderButton: {
      flex: 1,
      flexDirection: "row",
      gap: 2,
      paddingLeft: 24,
      paddingTop: 5,
      transform: [{
        scale: SCALE_FACTOR
      }, {
        translateY: -ORIGINAL_INACTIVE_HEADER.HEIGHT * (1 - SCALE_FACTOR) / 2
      }, {
        translateX: -(CONTAINER_WIDTH / 2 * (1 - SCALE_FACTOR)) / 2
      }]
    },
    inactiveHeaderTitle: Object.assign({}, _text.newPresets.bodyTextBold, {
      fontSize: 14,
      lineHeight: 18,
      marginTop: 4,
      color: _palette.palette.darkestGrey
    }),
    activeHeadersContainer: {
      width: SCALED_ACTIVE_HEADER.WIDTH,
      height: SCALED_ACTIVE_HEADER.HEIGHT,
      overflow: "hidden"
    },
    pseudoDepartureHeaderButton: {
      width: SCALED_ACTIVE_HEADER.WIDTH / 2,
      height: SCALED_ACTIVE_HEADER.HEIGHT,
      position: "absolute",
      top: 0,
      right: 0
    },
    pseudoArrivalHeaderButton: {
      width: SCALED_ACTIVE_HEADER.WIDTH / 2,
      height: SCALED_ACTIVE_HEADER.HEIGHT,
      position: "absolute",
      left: 0,
      right: 0
    },
    contentFrame: {
      width: Math.floor(CONTAINER_WIDTH),
      borderWidth: 1,
      borderTopWidth: 0,
      borderColor: "#D5BBEA",
      borderBottomRightRadius: 16,
      borderBottomLeftRadius: 16,
      marginTop: -4
    },
    contentBackground: {
      width: Math.floor(CONTENT_WIDTH),
      overflow: "hidden",
      backgroundColor: "white",
      marginTop: -CONTENT_BACKGROUND_OFFSET,
      borderRadius: 16
    },
    content: {
      padding: 20,
      width: Math.floor(CONTAINER_WIDTH)
    }
  });
