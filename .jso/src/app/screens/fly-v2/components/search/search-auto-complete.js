  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.SearchAutoComplete = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _theme = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _apis = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _envParams = _$$_REQUIRE(_dependencyMap[9]);
  var _request = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _responsive = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _lodash = _$$_REQUIRE(_dependencyMap[12]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }
  var SearchAutoComplete = exports.SearchAutoComplete = (0, _react.memo)(function (props) {
    var search = props.search,
      onKeywordPress = props.onKeywordPress,
      visible = props.visible,
      flightDirection = props.flightDirection;
    var _useState = (0, _react.useState)([]),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      autoCompleteKeywords = _useState2[0],
      setAutoCompleteKeywords = _useState2[1];
    var fetchAutoCompleteKeywordsRef = (0, _react.useRef)((0, _lodash.debounce)(/*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* (searchKeyword) {
        var params = {
          text: searchKeyword,
          data_type: "flights,airlines,airports"
        };
        try {
          var _env, _response$data;
          // NOTE: this code is copied from searchSaga
          var paramsArray = _apis.default.getAutoCompleteKeyword.split(" ");
          var method = paramsArray[0] || "GET";
          var url = ((_env = (0, _envParams.env)()) == null ? undefined : _env.SEARCH_GATEWAY_URL) + paramsArray[1];
          var response = yield (0, _request.default)({
            url: url,
            method: method,
            parameters: params
          });
          // @ts-ignore
          if (!response.success || response.errors) {
            setAutoCompleteKeywords([]);
            return;
          }
          var _autoCompleteKeywords = (response == null || (_response$data = response.data) == null ? undefined : _response$data.list) || [];
          setAutoCompleteKeywords(_autoCompleteKeywords);
        } catch (error) {
          setAutoCompleteKeywords([]);
        }
      });
      return function (_x) {
        return _ref.apply(this, arguments);
      };
    }(), 300));
    (0, _react.useEffect)(function () {
      var trimmedSearch = search.trim();
      if (trimmedSearch.length <= 1) {
        setAutoCompleteKeywords([]);
      } else {
        fetchAutoCompleteKeywordsRef.current(trimmedSearch);
      }
      return function () {
        fetchAutoCompleteKeywordsRef.current.cancel();
      };
    }, [search]);
    var handleKeywordPress = function handleKeywordPress(keyword) {
      onKeywordPress && onKeywordPress(keyword);
    };
    var results = (0, _react.useMemo)(function () {
      return autoCompleteKeywords.filter(function (item) {
        var _item$data_type, _item$direction;
        return ((_item$data_type = item.data_type) == null ? undefined : _item$data_type.toLowerCase()) !== "flights" || ((_item$direction = item.direction) == null ? undefined : _item$direction.toLowerCase()) === flightDirection.toLowerCase();
      }).slice(0, 4);
    }, [autoCompleteKeywords, flightDirection]);
    var renderAutoCompleteKeyword = (0, _react.useCallback)(function (searchKeyword, item, index) {
      var _item$data_type2;
      var regex = new RegExp(`(${escapeRegExp(searchKeyword)})`, "i"); // case-insensitive
      var parts = item.name.split(regex);
      return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: styles.keywordContainer,
        onPress: function onPress() {
          return handleKeywordPress(item.name);
        },
        testID: `FlyLandingV2__AutoComplete__Suggestion__${index}`,
        accessibilityLabel: `FlyLandingV2__AutoComplete__Suggestion__${index}`,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.keywordText,
          children: parts.map(function (part, index) {
            var isMatch = part.toLowerCase() === searchKeyword.toLowerCase();
            return (0, _jsxRuntime.jsx)(_text.Text, {
              style: isMatch && {
                color: _theme.color.palette.almostBlackGrey
              },
              preset: isMatch ? "bodyTextBold" : undefined,
              children: part
            }, index);
          })
        }), ((_item$data_type2 = item.data_type) == null ? undefined : _item$data_type2.toLowerCase()) === "flights" && (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.keywordDotSeparator,
            children: "\xB7"
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "caption2Regular",
            children: item.text
          })]
        })]
      });
    }, [handleKeywordPress]);
    if (!visible || results.length === 0) return (0, _jsxRuntime.jsx)(_reactNative2.View, {});
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: styles.listKeywordsContainer,
      testID: "FlyLandingV2__AutoComplete__Container",
      accessibilityLabel: "FlyLandingV2__AutoComplete__Container",
      children: results.map(function (item, index) {
        return (0, _jsxRuntime.jsx)(_react.default.Fragment, {
          children: renderAutoCompleteKeyword(search.trim(), item, index)
        }, item.id || item.name);
      })
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    listKeywordsContainer: {
      position: "absolute",
      backgroundColor: "white",
      top: 60,
      left: 0,
      right: 0,
      borderWidth: 1,
      borderBottomWidth: 0,
      borderColor: _theme.color.palette.lighterGrey
    },
    keywordContainer: {
      height: 54,
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: 20,
      borderBottomWidth: 1,
      borderBottomColor: _theme.color.palette.lighterGrey
    },
    keywordText: {
      color: _theme.color.palette.almostBlackGrey,
      fontSize: _responsive.default.getFontSize(16)
    },
    keywordDotSeparator: {
      marginHorizontal: 4,
      fontSize: 16,
      fontWeight: 700
    }
  });
