  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.SearchButton = undefined;
  var _button = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var _theme = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  var SearchButton = exports.SearchButton = function SearchButton(props) {
    var disabled = props.disabled,
      onPress = props.onPress;
    var activeGradient = [_theme.color.palette.gradientColor1End, _theme.color.palette.gradientColor1Start];
    var disabledGradient = [_theme.color.palette.lighterGrey, _theme.color.palette.lighterGrey];
    var handlePress = function handlePress() {
      onPress && onPress();
    };
    return (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
      style: {
        borderRadius: 60,
        width: "100%"
      },
      start: {
        x: 1,
        y: 0
      },
      end: {
        x: 0,
        y: 1
      },
      colors: disabled ? disabledGradient : activeGradient,
      children: (0, _jsxRuntime.jsx)(_button.Button, {
        sizePreset: "large",
        textPreset: "buttonLarge",
        typePreset: "secondary",
        tx: "searchV2.flightsTab.searchFlights",
        statePreset: "default",
        backgroundPreset: "light",
        onPress: handlePress,
        disabled: disabled,
        testID: "FlyLandingV2__SearchButton__Button",
        accessibilityLabel: "FlyLandingV2__SearchButton__Button",
        style: [{
          height: 40
        }, disabled && {
          borderColor: _theme.color.palette.greyCCCCCC,
          borderWidth: 1
        }],
        textStyle: Object.assign({}, _text.newPresets.bodyTextBold, {
          fontSize: 14,
          lineHeight: 18,
          color: disabled ? _theme.color.palette.darkGrey999 : _theme.color.palette.almostWhiteGrey
        })
      })
    });
  };
