  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.SearchTextInput = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _placeholderCarousel = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _i18n = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _textField = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SearchTextInput = exports.SearchTextInput = function SearchTextInput(_ref) {
    var value = _ref.value,
      onChangeText = _ref.onChangeText,
      onFocus = _ref.onFocus,
      onBlur = _ref.onBlur;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      focused = _useState2[0],
      setFocused = _useState2[1];
    var currentPlaceholderIndex = (0, _react.useRef)(0);
    var inputRef = (0, _react.useRef)(null);
    (0, _react.useEffect)(function () {
      var keyboardDidHideListener = _reactNative.Keyboard.addListener('keyboardDidHide', function () {
        var _inputRef$current;
        (_inputRef$current = inputRef.current) == null || _inputRef$current.blur();
      });
      return function () {
        keyboardDidHideListener == null || keyboardDidHideListener.remove();
      };
    }, []);
    var handleFocus = function handleFocus() {
      onFocus && onFocus();
      setFocused(true);
    };
    var handleBlur = function handleBlur() {
      onBlur && onBlur();
      setFocused(false);
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: {
        marginBottom: 16
      },
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        preset: "bodyTextBold",
        tx: "searchV2.flightsTab.searchTermLabel",
        style: styles.inputLabel
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: [styles.inputContainerGlow, {
          backgroundColor: focused ? _theme.color.palette.lightestPurple : "transparent"
        }],
        children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: [styles.inputContainer, {
            borderColor: focused ? _theme.color.palette.lightPurple : _theme.color.palette.lightGrey
          }],
          children: [(0, _jsxRuntime.jsx)(_textField.TextField, {
            forwardedRef: inputRef,
            style: {
              flex: 1
            },
            inputStyle: styles.inputText,
            placeholderTextColor: _theme.color.palette.midGrey,
            secureTextEntry: false,
            preset: "noMargin",
            editable: true,
            allowFontScaling: false,
            onFocus: handleFocus,
            onBlur: handleBlur,
            value: value,
            onChangeText: onChangeText,
            maxLength: 50,
            testID: "FlyLandingV2__SearchTextInput__Input",
            accessibilityLabel: "FlyLandingV2__SearchTextInput__Input"
          }), !value && (0, _jsxRuntime.jsx)(_placeholderCarousel.default, {
            isStop: focused,
            placeholderList: [(0, _i18n.translate)("searchV2.placeHolder.flights1"), (0, _i18n.translate)("searchV2.placeHolder.flights2")],
            itemHeight: 44,
            textStyle: styles.placeholderText,
            currentPlaceholderIndex: currentPlaceholderIndex
          })]
        })
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    inputLabel: Object.assign({}, _text.newPresets.bodyTextBold, {
      marginBottom: 6,
      fontSize: 14,
      lineHeight: 18,
      color: _theme.color.palette.almostBlackGrey
    }),
    inputContainerGlow: {
      margin: -4,
      padding: 4,
      borderRadius: 10
    },
    inputContainer: {
      flexDirection: "row",
      alignItems: "center",
      borderWidth: 1,
      borderRadius: 8,
      paddingHorizontal: 12,
      height: 36,
      backgroundColor: _theme.color.palette.whiteGrey
    },
    inputText: Object.assign({}, _text.newPresets.bodyTextRegular, {
      fontSize: 14,
      lineHeight: 18,
      color: _theme.color.palette.almostBlackGrey,
      backgroundColor: "transparent",
      textAlignVertical: "center",
      minHeight: 36,
      paddingTop: 0,
      paddingBottom: 0
    }),
    placeholderText: {
      fontSize: 14,
      lineHeight: 18,
      paddingStart: _theme.spacing[3]
    }
  });
