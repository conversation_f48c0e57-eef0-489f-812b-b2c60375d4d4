  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ORIGINAL_ACTIVE_HEADER = exports.ActiveDepartureHeader = exports.ActiveArrivalHeader = undefined;
  var _reactNativeSvg = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[0]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[1]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var ORIGINAL_ACTIVE_HEADER = exports.ORIGINAL_ACTIVE_HEADER = {
    WIDTH: 346,
    HEIGHT: 66,
    CONTENT_OFFSET: 23
  };
  var ActiveArrivalHeader = exports.ActiveArrivalHeader = function ActiveArrivalHeader(props) {
    return (0, _jsxRuntime.jsxs)(_reactNativeSvg.default, Object.assign({
      style: {
        width: props.width,
        height: props.height
      },
      viewBox: `0 0 ${ORIGINAL_ACTIVE_HEADER.WIDTH} ${ORIGINAL_ACTIVE_HEADER.HEIGHT}`,
      fill: "none"
    }, props, {
      children: [(0, _jsxRuntime.jsx)(_reactNativeSvg.Mask, {
        id: "b",
        fill: "#fff",
        children: (0, _jsxRuntime.jsx)(_reactNativeSvg.Path, {
          d: "M155.59 0c5.655 0 8.483 0 10.801 1.004 2.043.885 3.8 2.314 5.077 4.127 1.448 2.056 2.002 4.806 3.111 10.304L177.518 30l.097.348c2.115 7.23 8.28 9.652 15.031 9.652h127.536c9.037 0 13.556 0 17.008 1.744a16.068 16.068 0 0 1 7.053 6.992C345.894 51.95 345.994 56.093 346 64H.044V40H0V19.2C0 12.48 0 9.12 1.32 6.552a12.053 12.053 0 0 1 5.288-5.244C9.198 0 12.588 0 19.366 0H155.59Z"
        })
      }), (0, _jsxRuntime.jsx)(_reactNativeSvg.Path, {
        fill: "url(#a)",
        d: "M155.59 0c5.655 0 8.483 0 10.801 1.004 2.043.885 3.8 2.314 5.077 4.127 1.448 2.056 2.002 4.806 3.111 10.304L177.518 30l.097.348c2.115 7.23 8.28 9.652 15.031 9.652h127.536c9.037 0 13.556 0 17.008 1.744a16.068 16.068 0 0 1 7.053 6.992C345.894 51.95 345.994 56.093 346 64H.044V40H0V19.2C0 12.48 0 9.12 1.32 6.552a12.053 12.053 0 0 1 5.288-5.244C9.198 0 12.588 0 19.366 0H155.59Z"
      }), (0, _jsxRuntime.jsx)(_reactNativeSvg.Path, {
        fill: "#D5BBEA",
        d: "m166.391 1.004.397-.918-.397.918Zm5.077 4.127.817-.576-.817.576Zm3.111 10.304-.98.197.98-.197ZM177.518 30l-.981.198.008.036.01.036.963-.27Zm.097.348-.963.27.003.01.96-.28ZM192.646 40v1-1Zm127.536 0v-1 1Zm17.008 1.744.451-.892-.451.892Zm7.053 6.992.89-.457-.89.457ZM346 64v1h1.001L347 64h-1ZM.044 64h-1v1h1v-1Zm0-24h1v-1h-1v1ZM0 40h-1v1h1v-1ZM1.32 6.552l-.89-.457.89.457Zm5.288-5.244-.45-.893.45.893ZM19.366 0v1-1ZM155.59 0v1c2.842 0 4.92 0 6.578.123 1.646.12 2.809.358 3.825.798l.398-.917.397-.918c-1.302-.564-2.712-.828-4.473-.958C160.566-1 158.403-1 155.59-1v1Zm10.801 1.004-.398.917a11.084 11.084 0 0 1 4.657 3.786l.818-.576.817-.576a13.08 13.08 0 0 0-5.497-4.469l-.397.918Zm5.077 4.127-.818.576c.633.899 1.095 1.98 1.538 3.555.445 1.588.853 3.607 1.411 6.37l.98-.197.98-.198c-.551-2.735-.975-4.84-1.446-6.515-.474-1.689-1.013-3.01-1.828-4.167l-.817.576Zm3.111 10.304-.98.197 2.938 14.566.981-.198.98-.198-2.939-14.565-.98.198ZM177.518 30l-.963.27.097.348.963-.27.963-.27-.098-.348-.962.27Zm.097.348-.96.28c1.13 3.863 3.366 6.487 6.242 8.126 2.849 1.623 6.266 2.246 9.749 2.246v-1l.001-1c-3.269 0-6.31-.588-8.76-1.984-2.423-1.38-4.327-3.582-5.312-6.95l-.96.282ZM192.646 40v1h127.536v-2H192.646v1Zm127.536 0v1c4.535 0 7.877 0 10.518.215 2.63.213 4.477.632 6.04 1.422l.45-.893.451-.892c-1.889-.955-4.027-1.408-6.78-1.63-2.74-.223-6.177-.222-10.679-.222v1Zm17.008 1.744-.45.893a15.07 15.07 0 0 1 6.614 6.556l.889-.457.89-.457a17.075 17.075 0 0 0-7.492-7.427l-.451.892Zm7.053 6.992-.889.457c.745 1.451 1.163 3.152 1.389 5.51.227 2.37.254 5.334.257 9.298l1-.001h1c-.003-3.944-.029-7.006-.266-9.487-.239-2.494-.696-4.472-1.601-6.234l-.89.457ZM346 64v-1H.044v2H346v-1ZM.044 64h1V40h-2v24h1Zm0-24v-1H0v2h.044v-1ZM0 40h1V19.2h-2V40h1Zm0-20.8h1c0-3.377 0-5.849.162-7.8.16-1.938.472-3.273 1.047-4.391l-.89-.457-.89-.457C-.314 7.543-.66 9.173-.83 11.236-1.002 13.288-1 15.856-1 19.2h1ZM1.32 6.552l.889.457A11.053 11.053 0 0 1 7.059 2.2l-.45-.892-.452-.893A13.053 13.053 0 0 0 .43 6.095l.89.457Zm5.288-5.244.451.892c1.131-.571 2.48-.881 4.438-1.04C13.466 1 15.961 1 19.367 1v-2c-3.374 0-5.963 0-8.031.167-2.08.168-3.72.511-5.178 1.248l.45.893ZM19.366 0v1H155.59v-2H19.366v1Z",
        mask: "url(#b)"
      }), (0, _jsxRuntime.jsxs)(_reactNativeSvg.G, {
        fill: "#7A35B0",
        clipPath: "url(#c)",
        children: [(0, _jsxRuntime.jsx)(_reactNativeSvg.Path, {
          fillRule: "evenodd",
          d: "M25.005 28a1 1 0 0 1 1-1h12a1 1 0 1 1 0 2h-12a1 1 0 0 1-1-1Z",
          clipRule: "evenodd"
        }), (0, _jsxRuntime.jsx)(_reactNativeSvg.Path, {
          d: "M40.634 22.958c-.29 1.263-1.66 2.055-3.926 1.592l-13.804-2.82c-.54-.11-.917-.59-.9-1.146l.141-4.602a.604.604 0 0 1 .377-.538l2.816-1.143c.368-.15.766.1.785.495l.142 2.832 3.42.699.783-7.548a.6.6 0 0 1 .71-.528l2.366.483a.56.56 0 0 1 .445.48l1.08 8.213 2.692.55c2.266.462 3.164 1.718 2.873 2.98Z"
        })]
      }), (0, _jsxRuntime.jsx)(_reactNativeSvg.Path, {
        fill: "#7A35B0",
        d: "M59.833 26H58.17a.76.76 0 0 1-.464-.136.835.835 0 0 1-.256-.352l-.864-2.36h-4.792l-.864 2.36a.748.748 0 0 1-.256.336.692.692 0 0 1-.456.152h-1.672l4.544-11.568h2.2L59.833 26Zm-7.488-4.368h3.688l-1.408-3.848c-.064-.17-.136-.37-.216-.6-.075-.235-.15-.488-.224-.76-.075.272-.15.525-.224.76-.07.235-.139.44-.208.616l-1.408 3.832ZM60.912 26v-8.208h1.16c.203 0 .344.037.424.**************.203.16.384l.12.992c.294-.507.638-.907 1.032-1.2.395-.293.838-.44 1.328-.44.406 0 .742.093 1.008.28l-.256 1.48c-.016.096-.05.165-.104.208a.37.37 0 0 1-.216.056c-.08 0-.189-.019-.328-.056a2.196 2.196 0 0 0-.552-.056c-.41 0-.762.115-1.056.344-.293.224-.54.555-.744.992V26h-1.976Zm6.532 0v-8.208h1.16c.202 0 .344.037.424.112.08.075.133.203.16.384l.12.992c.293-.507.637-.907 1.032-1.2.394-.293.837-.44 1.328-.44.405 0 .741.093 1.008.28l-.256 1.48c-.016.096-.051.165-.104.208a.37.37 0 0 1-.216.056c-.08 0-.19-.019-.328-.056a2.197 2.197 0 0 0-.552-.056c-.411 0-.763.115-1.056.344-.294.224-.542.555-.744.992V26h-1.976Zm8.635-8.208V26h-1.976v-8.208h1.976Zm.296-2.392c0 .17-.035.33-.104.48-.07.15-.163.28-.28.392a1.326 1.326 0 0 1-.4.272c-.155.064-.32.096-.496.096-.17 0-.333-.032-.488-.096a1.357 1.357 0 0 1-.664-.664 1.284 1.284 0 0 1 0-.976c.07-.155.16-.288.272-.4.112-.112.243-.2.392-.264a1.18 1.18 0 0 1 .488-.104c.176 0 .341.035.496.104.155.064.288.152.4.264.117.112.21.245.28.4.07.155.104.32.104.496ZM82.43 26h-1.792l-3.264-8.208h1.64a.6.6 0 0 1 .36.104c.101.07.17.157.208.264l1.584 4.384c.09.256.165.507.224.752.064.245.12.49.168.736.048-.245.101-.49.16-.736a7.63 7.63 0 0 1 .24-.752l1.624-4.384a.54.54 0 0 1 .2-.264.572.572 0 0 1 .344-.104h1.56L82.43 26Zm10.85 0h-.887a.998.998 0 0 1-.44-.08c-.107-.059-.187-.173-.24-.344l-.176-.584a6.964 6.964 0 0 1-.616.496 3.732 3.732 0 0 1-.616.352c-.213.096-.44.168-.68.216a4.09 4.09 0 0 1-.8.072c-.347 0-.667-.045-.96-.136a2.158 2.158 0 0 1-.76-.424 1.932 1.932 0 0 1-.488-.696 2.47 2.47 0 0 1-.176-.968c0-.31.08-.613.24-.912.165-.304.437-.576.816-.816.379-.245.883-.448 1.512-.608.63-.16 1.41-.25 2.344-.272v-.48c0-.55-.117-.955-.352-1.216-.23-.267-.565-.4-1.008-.4-.32 0-.587.037-.8.112-.213.075-.4.16-.56.256-.155.09-.299.173-.432.248a.885.885 0 0 1-.44.112.572.572 0 0 1-.344-.104.82.82 0 0 1-.232-.256l-.36-.632c.944-.864 2.083-1.296 3.416-1.296.48 0 .907.08 1.28.24.379.155.699.373.96.656.261.277.459.61.592 1 .139.39.208.816.208 1.28V26Zm-3.84-1.232c.204 0 .39-.019.56-.056.172-.037.332-.093.48-.168.156-.075.302-.165.44-.272.145-.112.289-.243.433-.392v-1.384c-.576.027-1.059.077-1.448.152-.384.07-.693.16-.928.272-.235.112-.403.243-.504.392a.885.885 0 0 0-.144.488c0 .347.101.595.304.744.208.15.477.224.808.224Zm7.888-10.656V26h-1.976V14.112h1.976Z"
      }), (0, _jsxRuntime.jsxs)(_reactNativeSvg.Defs, {
        children: [(0, _jsxRuntime.jsxs)(_reactNativeSvg.LinearGradient, {
          id: "a",
          x1: 27.5,
          x2: 188.5,
          y1: -27.5,
          y2: 247.5,
          gradientUnits: "userSpaceOnUse",
          children: [(0, _jsxRuntime.jsx)(_reactNativeSvg.Stop, {
            stopColor: "#ECE0F5"
          }), (0, _jsxRuntime.jsx)(_reactNativeSvg.Stop, {
            offset: 0.511,
            stopColor: "#fff"
          })]
        }), (0, _jsxRuntime.jsx)(_reactNativeSvg.ClipPath, {
          id: "c",
          children: (0, _jsxRuntime.jsx)(_reactNativeSvg.Path, {
            fill: "#fff",
            d: "M20.005 8h24v24h-24z"
          })
        })]
      })]
    }));
  };
  var ActiveDepartureHeader = exports.ActiveDepartureHeader = function ActiveDepartureHeader(props) {
    return (0, _jsxRuntime.jsxs)(_reactNativeSvg.default, Object.assign({
      style: {
        width: props.width,
        height: props.height
      },
      viewBox: `0 0 ${ORIGINAL_ACTIVE_HEADER.WIDTH} ${ORIGINAL_ACTIVE_HEADER.HEIGHT}`,
      fill: "none"
    }, props, {
      children: [(0, _jsxRuntime.jsx)(_reactNativeSvg.Mask, {
        id: "b",
        fill: "#fff",
        children: (0, _jsxRuntime.jsx)(_reactNativeSvg.Path, {
          d: "M190.41 0c-5.655 0-8.483 0-10.801 1.004a12.088 12.088 0 0 0-5.077 4.127c-1.448 2.056-2.002 4.806-3.111 10.304L168.482 30l-.097.348c-2.115 7.23-8.28 9.652-15.031 9.652H25.818c-9.037 0-13.556 0-17.008 1.744a16.07 16.07 0 0 0-7.053 6.992C.106 51.95.007 56.093 0 64h345.956V40H346V19.2c0-6.72 0-10.081-1.319-12.648a12.057 12.057 0 0 0-5.289-5.244C336.803 0 333.412 0 326.634 0H190.41Z"
        })
      }), (0, _jsxRuntime.jsx)(_reactNativeSvg.Path, {
        fill: "url(#a)",
        d: "M190.41 0c-5.655 0-8.483 0-10.801 1.004a12.088 12.088 0 0 0-5.077 4.127c-1.448 2.056-2.002 4.806-3.111 10.304L168.482 30l-.097.348c-2.115 7.23-8.28 9.652-15.031 9.652H25.818c-9.037 0-13.556 0-17.008 1.744a16.07 16.07 0 0 0-7.053 6.992C.106 51.95.007 56.093 0 64h345.956V40H346V19.2c0-6.72 0-10.081-1.319-12.648a12.057 12.057 0 0 0-5.289-5.244C336.803 0 333.412 0 326.634 0H190.41Z"
      }), (0, _jsxRuntime.jsx)(_reactNativeSvg.Path, {
        fill: "#D5BBEA",
        d: "m179.609 1.004-.397-.918.397.918Zm-5.077 4.127-.817-.576.817.576Zm-3.111 10.304.98.197-.98-.197ZM168.482 30l.981.198-.008.036-.01.036-.963-.27Zm-.097.348.963.27-.003.01-.96-.28ZM153.354 40v1-1ZM25.818 40v-1 1ZM8.81 41.744l-.451-.892.45.892Zm-7.053 6.992-.89-.457.89.457ZM0 64v1h-1v-1h1Zm345.956 0h1v1h-1v-1Zm0-24h-1v-1h1v1Zm.044 0h1v1h-1v-1Zm-1.319-33.448.889-.457-.889.457Zm-5.289-5.244.45-.893h.001l-.451.893ZM326.634 0v1-1ZM190.41 0v1c-2.842 0-4.92 0-6.578.123-1.646.12-2.809.358-3.825.798l-.398-.917-.397-.918c1.302-.564 2.712-.828 4.473-.958C185.434-1 187.597-1 190.41-1v1Zm-10.801 1.004.398.917a11.084 11.084 0 0 0-4.657 3.786l-.818-.576-.817-.576a13.08 13.08 0 0 1 5.497-4.469l.397.918Zm-5.077 4.127.818.576c-.633.899-1.095 1.98-1.538 3.555-.445 1.588-.853 3.607-1.411 6.37l-.98-.197-.98-.198c.551-2.735.975-4.84 1.446-6.515.474-1.689 1.013-3.01 1.828-4.167l.817.576Zm-3.111 10.304.98.197-2.938 14.566-.981-.198-.98-.198 2.939-14.565.98.198ZM168.482 30l.963.27-.097.348-.963-.27-.963-.27.098-.348.962.27Zm-.097.348.96.28c-1.13 3.863-3.366 6.487-6.242 8.126-2.849 1.623-6.266 2.246-9.749 2.246v-1l-.001-1c3.269 0 6.31-.588 8.76-1.984 2.423-1.38 4.327-3.582 5.312-6.95l.96.282ZM153.354 40v1H25.818v-2h127.536v1ZM25.818 40v1c-4.535 0-7.877 0-10.518.215-2.63.213-4.477.632-6.04 1.422l-.45-.893-.451-.892c1.89-.955 4.027-1.408 6.78-1.63 2.74-.223 6.177-.222 10.68-.222v1ZM8.81 41.744l.45.893a15.07 15.07 0 0 0-6.614 6.556l-.89-.457-.889-.457a17.07 17.07 0 0 1 7.492-7.427l.45.892Zm-7.053 6.992.89.457c-.746 1.451-1.164 3.152-1.39 5.51-.227 2.37-.254 5.334-.257 9.298L0 64h-1c.003-3.944.029-7.006.266-9.487.239-2.494.696-4.472 1.601-6.234l.89.457ZM0 64v-1h345.956v2H0v-1Zm345.956 0h-1V40h2v24h-1Zm0-24v-1H346v2h-.044v-1Zm.044 0h-1V19.2h2V40h-1Zm0-20.8h-1c0-3.377-.001-5.849-.162-7.8-.159-1.938-.472-3.273-1.047-4.391l.89-.457.889-.457c.745 1.448 1.092 3.078 1.262 5.141.169 2.052.168 4.62.168 7.964h-1Zm-1.319-12.648-.89.457a11.054 11.054 0 0 0-4.85-4.809l.451-.892.451-.893a13.054 13.054 0 0 1 5.727 5.68l-.889.457Zm-5.289-5.244-.451.892c-1.131-.571-2.481-.881-4.438-1.04-1.969-.16-4.464-.16-7.869-.16v-2c3.373 0 5.962 0 8.03.167 2.08.168 3.72.511 5.178 1.248l-.45.893ZM326.634 0v1H190.41v-2h136.224v1Z",
        mask: "url(#b)"
      }), (0, _jsxRuntime.jsxs)(_reactNativeSvg.G, {
        fill: "#7A35B0",
        clipPath: "url(#c)",
        children: [(0, _jsxRuntime.jsx)(_reactNativeSvg.Path, {
          d: "M217.346 17.177c.27 1.268-.635 2.564-2.885 3.102l-13.703 3.279a1.125 1.125 0 0 1-1.3-.659l-1.817-4.23a.605.605 0 0 1 .114-.648l2.069-2.225a.566.566 0 0 1 .921.116l1.326 2.507 3.395-.812-2.481-7.172a.6.6 0 0 1 .421-.778l2.348-.562a.561.561 0 0 1 .606.246l4.45 6.987 2.672-.639c2.25-.538 3.594.22 3.864 1.488Z"
        }), (0, _jsxRuntime.jsx)(_reactNativeSvg.Path, {
          fillRule: "evenodd",
          d: "M200.647 28a1 1 0 0 1 1-1h12a1 1 0 0 1 0 2h-12a1 1 0 0 1-1-1Z",
          clipRule: "evenodd"
        })]
      }), (0, _jsxRuntime.jsx)(_reactNativeSvg.Path, {
        fill: "#7A35B0",
        d: "M235.18 20.216c0 .848-.141 1.627-.424 2.336-.282.71-.68 1.32-1.192 1.832a5.333 5.333 0 0 1-1.848 1.192c-.72.283-1.517.424-2.392.424h-4.416V14.432h4.416c.875 0 1.672.144 2.392.432.72.283 1.336.68 1.848 1.192.512.507.91 1.115 1.192 1.824.283.71.424 1.488.424 2.336Zm-2.2 0c0-.635-.085-1.203-.256-1.704a3.558 3.558 0 0 0-.728-1.28 3.1 3.1 0 0 0-1.152-.808 3.917 3.917 0 0 0-1.52-.28h-2.248v8.144h2.248c.566 0 1.072-.093 1.52-.28a3.143 3.143 0 0 0 1.152-.8c.315-.352.558-.779.728-1.28a5.337 5.337 0 0 0 .256-1.712Zm7.162-2.552c.517 0 .992.083 1.424.248a3.1 3.1 0 0 1 1.128.728c.314.315.56.704.736 1.168.176.459.264.984.264 1.576 0 .15-.008.275-.024.376a.55.55 0 0 1-.072.232.302.302 0 0 1-.************ 0 0 1-.224.032h-5.072c.058.843.285 1.461.68 1.856.394.395.917.592 1.568.592.32 0 .594-.037.824-.112.234-.075.437-.157.608-.248.176-.09.328-.173.456-.248a.784.784 0 0 1 .384-.112c.08 0 .149.016.208.048a.47.47 0 0 1 .152.136l.576.72a3.39 3.39 0 0 1-.736.648c-.272.17-.558.31-.856.416a4.772 4.772 0 0 1-.904.216c-.304.043-.6.064-.888.064a4.436 4.436 0 0 1-1.592-.28 3.626 3.626 0 0 1-1.28-.84 3.985 3.985 0 0 1-.856-1.376c-.208-.55-.312-1.184-.312-1.904 0-.56.09-1.085.272-1.576.181-.496.44-.925.776-1.288a3.687 3.687 0 0 1 1.24-.872c.49-.213 1.042-.32 1.656-.32Zm.04 1.416c-.576 0-1.027.163-1.352.488-.326.325-.534.787-.624 1.384h3.712c0-.256-.035-.496-.104-.72-.07-.23-.176-.43-.32-.6a1.48 1.48 0 0 0-.544-.4 1.813 1.813 0 0 0-.768-.152Zm5.114 9.6V17.792h1.208c.128 0 .238.03.328.088.091.059.15.15.176.272l.16.76a4.18 4.18 0 0 1 1.136-.92c.432-.235.936-.352 1.512-.352.448 0 .856.093 1.224.28.374.187.694.459.96.816.272.352.48.79.624 1.312.15.517.224 1.112.224 1.784 0 .613-.082 1.181-.248 1.704a4.108 4.108 0 0 1-.712 1.36 3.257 3.257 0 0 1-1.112.904c-.432.213-.917.32-1.456.32-.464 0-.858-.07-1.184-.208a2.967 2.967 0 0 1-.864-.592v3.36h-1.976Zm3.84-9.48c-.41 0-.762.088-1.056.264a3.01 3.01 0 0 0-.808.728v3.68c.224.277.467.472.728.584.267.107.552.16.856.16.299 0 .568-.056.808-.168.246-.112.451-.283.616-.512.171-.23.302-.517.392-.864.091-.352.136-.765.136-1.24 0-.48-.04-.885-.12-1.216-.074-.336-.184-.608-.328-.816a1.304 1.304 0 0 0-.528-.456 1.603 1.603 0 0 0-.696-.144Zm11.639 6.8h-.888c-.186 0-.333-.027-.44-.08-.106-.059-.186-.173-.24-.344l-.176-.584a6.921 6.921 0 0 1-.616.496 3.695 3.695 0 0 1-.616.352c-.213.096-.44.168-.68.216-.24.048-.506.072-.8.072-.346 0-.666-.045-.96-.136a2.166 2.166 0 0 1-.76-.424 1.939 1.939 0 0 1-.488-.696 2.475 2.475 0 0 1-.176-.968c0-.31.08-.613.24-.912.166-.304.438-.576.816-.816.379-.245.883-.448 1.512-.608.63-.16 1.411-.25 2.344-.272v-.48c0-.55-.117-.955-.352-1.216-.229-.267-.565-.4-1.008-.4-.32 0-.586.037-.8.112-.213.075-.4.16-.56.256-.154.09-.298.173-.432.248a.884.884 0 0 1-.44.112.573.573 0 0 1-.344-.104.823.823 0 0 1-.232-.256l-.36-.632c.944-.864 2.083-1.296 3.416-1.296.48 0 .907.08 1.28.24.379.155.699.373.96.656.262.277.459.61.592 1 .139.39.208.816.208 1.28V26Zm-3.84-1.232c.203 0 .39-.019.56-.056.171-.037.331-.093.48-.168.155-.075.302-.165.44-.272.144-.112.288-.243.432-.392v-1.384c-.576.027-1.058.077-1.448.152-.384.07-.693.16-.928.272-.234.112-.402.243-.504.392a.885.885 0 0 0-.144.488c0 .347.102.595.304.744.208.15.478.224.808.224ZM262.718 26v-8.208h1.16c.203 0 .344.037.424.**************.203.16.384l.12.992c.294-.507.638-.907 1.032-1.2.395-.293.838-.44 1.328-.44.406 0 .742.093 1.008.28l-.256 1.48c-.016.096-.05.165-.104.208a.37.37 0 0 1-.216.056c-.08 0-.189-.019-.328-.056a2.19 2.19 0 0 0-.552-.056c-.41 0-.762.115-1.056.344-.293.224-.541.555-.744.992V26h-1.976Zm9.228.128c-.71 0-1.256-.2-1.64-.6-.384-.405-.576-.963-.576-1.672v-4.584h-.832a.414.414 0 0 1-.28-.104c-.075-.07-.112-.173-.112-.312v-.784l1.32-.216.416-2.24a.4.4 0 0 1 .144-.248.485.485 0 0 1 .296-.088h1.024v2.584h2.16v1.408h-2.16v4.448c0 .256.064.456.192.6a.65.65 0 0 0 .512.216.978.978 0 0 0 .304-.04c.085-.032.157-.064.216-.096.064-.032.12-.061.168-.088a.258.258 0 0 1 .144-.048c.058 0 .106.016.144.048.037.027.077.07.12.128l.592.96c-.288.24-.619.421-.992.544-.374.123-.76.184-1.16.184Zm5.299-8.336v5.216c0 .501.115.89.344 1.168.235.272.584.408 1.048.408.341 0 .661-.075.96-.224a3.44 3.44 0 0 0 .848-.632v-5.936h1.976V26h-1.208c-.256 0-.424-.12-.504-.36l-.136-.656c-.171.17-.347.328-.528.472a3.419 3.419 0 0 1-.584.36c-.203.096-.424.17-.664.224a3.128 3.128 0 0 1-.76.088 2.99 2.99 0 0 1-1.192-.224 2.435 2.435 0 0 1-.864-.648 2.876 2.876 0 0 1-.536-.984 4.321 4.321 0 0 1-.176-1.264v-5.216h1.976ZM284.531 26v-8.208h1.16c.202 0 .344.037.424.112.08.075.133.203.16.384l.12.992c.293-.507.637-.907 1.032-1.2a2.17 2.17 0 0 1 1.328-.44c.405 0 .741.093 1.008.28l-.256 1.48c-.016.096-.051.165-.104.208a.374.374 0 0 1-.216.056c-.08 0-.19-.019-.328-.056a2.2 2.2 0 0 0-.552-.056c-.411 0-.763.115-1.056.344-.294.224-.542.555-.744.992V26h-1.976Zm9.923-8.336c.517 0 .992.083 1.424.248a3.1 3.1 0 0 1 1.128.728c.315.315.56.704.736 1.168.176.459.264.984.264 1.576 0 .15-.008.275-.024.376a.55.55 0 0 1-.072.232.297.297 0 0 1-.136.12.665.665 0 0 1-.224.032h-5.072c.059.843.285 1.461.68 1.856.395.395.917.592 1.568.592.32 0 .595-.037.824-.112a3.68 3.68 0 0 0 .608-.248c.176-.09.328-.173.456-.248a.784.784 0 0 1 .384-.112c.08 0 .149.016.208.048a.46.46 0 0 1 .152.136l.576.72a3.39 3.39 0 0 1-.736.648c-.272.17-.557.31-.856.416a4.754 4.754 0 0 1-.904.216c-.304.043-.6.064-.888.064a4.44 4.44 0 0 1-1.592-.28 3.635 3.635 0 0 1-1.28-.84 3.985 3.985 0 0 1-.856-1.376c-.208-.55-.312-1.184-.312-1.904 0-.56.091-1.085.272-1.576.181-.496.44-.925.776-1.288a3.697 3.697 0 0 1 1.24-.872 4.116 4.116 0 0 1 1.656-.32Zm.04 1.416c-.576 0-1.027.163-1.352.488-.325.325-.533.787-.624 1.384h3.712c0-.256-.035-.496-.104-.72a1.661 1.661 0 0 0-.32-.6 1.472 1.472 0 0 0-.544-.4 1.813 1.813 0 0 0-.768-.152Z"
      }), (0, _jsxRuntime.jsxs)(_reactNativeSvg.Defs, {
        children: [(0, _jsxRuntime.jsxs)(_reactNativeSvg.LinearGradient, {
          id: "a",
          x1: 318.5,
          x2: 157.5,
          y1: -27.5,
          y2: 247.5,
          gradientUnits: "userSpaceOnUse",
          children: [(0, _jsxRuntime.jsx)(_reactNativeSvg.Stop, {
            stopColor: "#ECE0F5"
          }), (0, _jsxRuntime.jsx)(_reactNativeSvg.Stop, {
            offset: 0.511,
            stopColor: "#fff"
          })]
        }), (0, _jsxRuntime.jsx)(_reactNativeSvg.ClipPath, {
          id: "c",
          children: (0, _jsxRuntime.jsx)(_reactNativeSvg.Path, {
            fill: "#fff",
            d: "M195.647 8h24v24h-24z"
          })
        })]
      })]
    }));
  };
