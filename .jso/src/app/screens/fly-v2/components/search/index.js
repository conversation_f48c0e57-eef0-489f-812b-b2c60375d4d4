  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.Search = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _constants = _$$_REQUIRE(_dependencyMap[6]);
  var _icons = _$$_REQUIRE(_dependencyMap[7]);
  var _theme = _$$_REQUIRE(_dependencyMap[8]);
  var _text = _$$_REQUIRE(_dependencyMap[9]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _hooks = _$$_REQUIRE(_dependencyMap[11]);
  var _adobe = _$$_REQUIRE(_dependencyMap[12]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[13]));
  var _searchButton = _$$_REQUIRE(_dependencyMap[14]);
  var _searchAutoComplete = _$$_REQUIRE(_dependencyMap[15]);
  var _searchTabs = _$$_REQUIRE(_dependencyMap[16]);
  var _searchTextInput = _$$_REQUIRE(_dependencyMap[17]);
  var _searchDateInput = _$$_REQUIRE(_dependencyMap[18]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[19]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var COLLAPSED_CONTENT_HEIGHT = 0;
  var EXPANED_CONTENT_HEIGHT = 132;
  var _worklet_17393361191577_init_data = {
    code: "function indexTsx1(){const{contentHeight}=this.__closure;return{height:contentHeight.value,overflow:\"hidden\"};}"
  };
  var Search = exports.Search = (0, _react.forwardRef)(function (props, ref) {
    var navigation = props.navigation;
    var _useState = (0, _react.useState)(_constants.FlightDirection.Arrival),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      selectedTab = _useState2[0],
      setSelectedTab = _useState2[1];
    var _useState3 = (0, _react.useState)(""),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      search = _useState4[0],
      setSearch = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      searchAutoCompleteVisible = _useState6[0],
      setSearchAutoCompleteVisible = _useState6[1];
    var _useState7 = (0, _react.useState)((0, _moment.default)()),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      date = _useState8[0],
      setDate = _useState8[1];
    var _useCameraPermission = (0, _hooks.useCameraPermission)(),
      handleCameraPermission = _useCameraPermission.handleCameraPermission;
    var contentHeight = (0, _reactNativeReanimated.useSharedValue)(COLLAPSED_CONTENT_HEIGHT);
    var animatedContentStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var indexTsx1 = function indexTsx1() {
        return {
          height: contentHeight.value,
          overflow: "hidden"
        };
      };
      indexTsx1.__closure = {
        contentHeight: contentHeight
      };
      indexTsx1.__workletHash = 17393361191577;
      indexTsx1.__initData = _worklet_17393361191577_init_data;
      return indexTsx1;
    }());
    var collapse = function collapse() {
      contentHeight.value = (0, _reactNativeReanimated.withTiming)(COLLAPSED_CONTENT_HEIGHT, {
        duration: 300
      });
      setSearchAutoCompleteVisible(false);
    };
    var reset = function reset() {
      collapse();
      setSearch("");
      setDate((0, _moment.default)());
      setSelectedTab(_constants.FlightDirection.Arrival);
    };
    (0, _react.useImperativeHandle)(ref, function () {
      return {
        collapse: collapse,
        reset: reset
      };
    });
    var handleScanBoardingPass = (0, _react.useCallback)(function () {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppSearchResultFlyScanBoardingPass, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppSearchResultFlyScanBoardingPass, '1'));
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlightLanding, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlightLanding, "Flight Search | Boarding pass scanner"));
      handleCameraPermission(function () {
        navigation.navigate("scanCode", {
          shouldTrackDetectedFlightNumber: true
        });
      });
    }, [handleCameraPermission, navigation]);
    var handleFocus = function handleFocus() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlightLanding, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlightLanding, "Flight Search | Search bar"));
      contentHeight.value = (0, _reactNativeReanimated.withTiming)(EXPANED_CONTENT_HEIGHT, {
        duration: 300
      });
      setSearchAutoCompleteVisible(true);
    };
    var handleBlur = function handleBlur() {
      setSearchAutoCompleteVisible(false);
    };
    var handleSearchChange = function handleSearchChange(value) {
      setSearchAutoCompleteVisible(true);
      setSearch(value);
    };
    var handleKeywordPress = function handleKeywordPress(keyword) {
      setSearchAutoCompleteVisible(false);
      setSearch(keyword);

      // If Keyboard.dismiss() is called immediately after setSearch,
      // TextInput's onChangeText may be triggered again with the old value.
      // We delay the dismiss slightly to ensure the new value is applied first.
      setTimeout(_reactNative2.Keyboard.dismiss, 50);
    };
    var handleSearch = function handleSearch() {
      navigation.navigate(_constants.NavigationConstants.searchFlightsV2Result, {
        keyword: search,
        date: date,
        direction: selectedTab,
        onGoBack: function onGoBack(params) {
          if (!params) return;
          params.direction && setSelectedTab(params.direction);
          params.date && setDate(params.date);
          params.keyword && setSearch(params.keyword);
        }
      });
    };
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: {
        flex: 1
      },
      testID: "FlyLandingV2__SearchContainer",
      accessibilityLabel: "FlyLandingV2__SearchContainer",
      children: (0, _jsxRuntime.jsx)(_searchTabs.SearchTabs, {
        selectedTab: selectedTab,
        onSelectTab: setSelectedTab,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          children: [(0, _jsxRuntime.jsx)(_searchTextInput.SearchTextInput, {
            value: search,
            onChangeText: handleSearchChange,
            onFocus: handleFocus,
            onBlur: handleBlur
          }), (0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
            style: animatedContentStyle,
            children: [(0, _jsxRuntime.jsx)(_searchDateInput.SearchDateInput, {
              value: date,
              onChangeDate: setDate
            }), (0, _jsxRuntime.jsx)(_searchButton.SearchButton, {
              disabled: !search,
              onPress: handleSearch
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.bottomContainer,
            children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: styles.bottomCaption,
              children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: styles.divider
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                preset: "caption2Bold",
                tx: "searchV2.flightsTab.or"
              }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: styles.divider
              })]
            }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
              style: styles.scanButtonContainer,
              onPress: handleScanBoardingPass,
              testID: "FlyLandingV2__ScanButton__Button",
              accessibilityLabel: "FlyLandingV2__ScanButton__Button",
              children: [(0, _jsxRuntime.jsx)(_icons.Scan, {
                width: 20,
                height: 20,
                color: _theme.color.palette.lightPurple
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                preset: "bodyTextBold",
                style: styles.textStyle,
                tx: "searchV2.flightsTab.scanBoardingPass"
              })]
            })]
          }), (0, _jsxRuntime.jsx)(_searchAutoComplete.SearchAutoComplete, {
            search: search,
            visible: searchAutoCompleteVisible,
            flightDirection: selectedTab,
            onKeywordPress: handleKeywordPress
          })]
        })
      })
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    opacityButton: {
      pointerEvents: "box-only"
    },
    textAlign: {
      paddingStart: _theme.spacing[3]
    },
    calendarIcon: {
      alignSelf: "center",
      marginRight: _theme.spacing[2]
    },
    bottomContainer: {
      display: "flex",
      paddingTop: 8,
      flexDirection: "column",
      alignItems: "flex-start",
      gap: 20
    },
    bottomCaption: {
      flexDirection: "row",
      gap: 18,
      justifyContent: "center",
      alignItems: "center"
    },
    divider: {
      borderColor: _theme.color.palette.lighterGrey,
      borderBottomWidth: 1,
      flex: 1
    },
    scanButtonContainer: {
      flexDirection: "row",
      justifyContent: "center",
      alignItems: "center",
      gap: 8,
      alignContent: "center",
      width: "100%"
    },
    textStyle: {
      color: _theme.color.palette.lightPurple
    }
  });
