  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.bottomSheetStyles = exports.SearchDateInput = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _icons = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _dateTime = _$$_REQUIRE(_dependencyMap[9]);
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _palette = _$$_REQUIRE(_dependencyMap[11]);
  var _calendar = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SearchDateInput = exports.SearchDateInput = function SearchDateInput(props) {
    var value = props.value,
      onChangeDate = props.onChangeDate;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      modalVisible = _useState2[0],
      setModalVisible = _useState2[1];
    var dateFormatted = (0, _react.useMemo)(function () {
      var inputDate = (0, _moment.default)(value);
      return inputDate.calendar(null, {
        sameDay: `[Today:] ${_dateTime.DateFormats.DayDateMonthYear}`,
        nextDay: `[Tomorrow:] ${_dateTime.DateFormats.DayDateMonthYear}`,
        nextWeek: _dateTime.DateFormats.DayDateMonthYear,
        lastDay: _dateTime.DateFormats.DayDateMonthYear,
        lastWeek: _dateTime.DateFormats.DayDateMonthYear,
        sameElse: _dateTime.DateFormats.DayDateMonthYear
      });
    }, [value]);
    var showModal = function showModal() {
      setModalVisible(true);
    };
    var closeModal = function closeModal() {
      setModalVisible(false);
    };
    var handleSelectDate = function handleSelectDate(date) {
      onChangeDate(date);
      closeModal();
    };
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        style: styles.opacityButton,
        onPress: showModal,
        testID: "FlyLandingV2__SearchDateInput__Button",
        accessibilityLabel: "FlyLandingV2__SearchDateInput__Button",
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: {
            marginBottom: 16
          },
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            tx: "searchV2.flightsTab.date",
            style: styles.inputLabel
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.inputContainer,
            children: [(0, _jsxRuntime.jsx)(_icons.CalenderDob, {
              width: 20,
              height: 20,
              style: styles.calendarIcon
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.inputText,
              children: dateFormatted
            })]
          })]
        })
      }), (0, _jsxRuntime.jsxs)(_bottomSheet.default, {
        isModalVisible: modalVisible,
        onClosedSheet: closeModal,
        stopDragCollapse: true,
        onBackPressHandle: closeModal,
        containerStyle: bottomSheetStyles.container,
        animationInTiming: 300,
        animationOutTiming: 300,
        openPendingModal: true,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: bottomSheetStyles.headerContainer,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: bottomSheetStyles.headerTitle,
            tx: "searchV2.flightsTab.datePickerTitle"
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: bottomSheetStyles.headerIcon,
            children: (0, _jsxRuntime.jsx)(_icons.Cross, {
              width: 24,
              height: 24,
              onPress: closeModal
            })
          })]
        }), (0, _jsxRuntime.jsx)(_calendar.default, {
          selectedDate: value,
          onSelectDate: handleSelectDate
        })]
      })]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    inputLabel: Object.assign({
      marginBottom: 6
    }, _text.newPresets.bodyTextBold, {
      fontSize: 14,
      lineHeight: 18,
      color: _theme.color.palette.almostBlackGrey
    }),
    inputContainer: {
      borderColor: _theme.color.palette.lightGrey,
      flexDirection: "row",
      alignItems: "center",
      borderWidth: 1,
      borderRadius: 8,
      paddingHorizontal: 12,
      height: 36,
      backgroundColor: _theme.color.palette.whiteGrey
    },
    inputText: Object.assign({}, _text.newPresets.bodyTextRegular, {
      fontSize: 14,
      lineHeight: 18,
      color: _theme.color.palette.almostBlackGrey
    }),
    opacityButton: {
      pointerEvents: "box-only"
    },
    textAlign: {
      paddingStart: _theme.spacing[3]
    },
    calendarIcon: {
      alignSelf: "center",
      marginRight: _theme.spacing[2]
    }
  });
  var bottomSheetStyles = exports.bottomSheetStyles = _reactNative2.StyleSheet.create({
    container: {
      height: 749,
      display: "flex",
      flexDirection: "column",
      justifyContent: "center",
      alignItems: "center",
      flexShrink: 0,
      backgroundColor: _theme.color.palette.whiteGrey,
      borderTopRightRadius: 16,
      borderTopLeftRadius: 16
    },
    headerContainer: {
      flexDirection: "row",
      justifyContent: "center",
      alignItems: "center",
      alignContent: "center",
      height: 64,
      position: "relative",
      width: "100%",
      backgroundColor: _theme.color.palette.transparent
    },
    headerTitle: Object.assign({}, _text.newPresets.bodyTextBold, {
      color: _palette.palette.almostBlackGrey,
      height: 22
    }),
    headerIcon: {
      position: "absolute",
      right: 20
    }
  });
