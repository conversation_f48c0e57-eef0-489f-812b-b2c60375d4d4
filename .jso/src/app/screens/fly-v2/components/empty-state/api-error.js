  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ApiError = undefined;
  var _icons = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _button = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _i18n = _$$_REQUIRE(_dependencyMap[9]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  var ApiError = exports.ApiError = function ApiError(_ref) {
    var onPressReload = _ref.onPressReload,
      containerStyle = _ref.containerStyle;
    var dataCommonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon) || [];
    var ehr42 = dataCommonAEM == null ? undefined : dataCommonAEM.find(function (e) {
      return (e == null ? undefined : e.code) === "EHR42";
    });
    var title = (ehr42 == null ? undefined : ehr42.header) || (0, _i18n.translate)("errors.EHR42.title");
    var description = (ehr42 == null ? undefined : ehr42.subHeader) || (0, _i18n.translate)("errors.EHR42.description");
    var cta = (ehr42 == null ? undefined : ehr42.buttonLabel) || (0, _i18n.translate)("errors.EHR42.cta");
    var handleReload = function handleReload() {
      onPressReload && onPressReload();
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: [styles.container, containerStyle],
      children: [(0, _jsxRuntime.jsx)(_icons.ErrorCloudV2, {
        width: 120,
        height: 120
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        style: styles.title,
        text: title
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        style: styles.description,
        text: description
      }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
        style: styles.buttonGradient,
        start: {
          x: 1,
          y: 0
        },
        end: {
          x: 0,
          y: 1
        },
        colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
        children: (0, _jsxRuntime.jsx)(_button.Button, {
          onPress: handleReload,
          sizePreset: "large",
          textPreset: "buttonLarge",
          typePreset: "primary",
          text: cta,
          backgroundPreset: "light",
          statePreset: "default"
        })
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    container: {
      alignItems: "center"
    },
    title: {
      fontFamily: _theme.typography.black,
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 18,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      }),
      lineHeight: 22,
      marginTop: 16,
      textAlign: "center"
    },
    description: {
      fontFamily: _theme.typography.regular,
      color: _theme.color.palette.darkestGrey,
      fontSize: 14,
      fontWeight: _reactNative.Platform.select({
        ios: "400",
        android: "normal"
      }),
      lineHeight: 18,
      textAlign: "center",
      marginTop: 8
    },
    buttonGradient: {
      borderRadius: 999,
      alignSelf: "stretch",
      marginTop: 24
    }
  });
