  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.General = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _icons = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _button = _$$_REQUIRE(_dependencyMap[7]);
  var _theme = _$$_REQUIRE(_dependencyMap[8]);
  var _i18n = _$$_REQUIRE(_dependencyMap[9]);
  var _envParams = _$$_REQUIRE(_dependencyMap[10]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var General = exports.General = function General(_ref) {
    var callback = _ref.callback,
      containerStyle = _ref.containerStyle,
      textTitle = _ref.textTitle,
      textDescription = _ref.textDescription,
      _ref$textButtonTitle = _ref.textButtonTitle,
      textButtonTitle = _ref$textButtonTitle === undefined ? (0, _i18n.translate)("errorOverlay.variant3.retry") : _ref$textButtonTitle,
      icon = _ref.icon;
    var _useState = (0, _react.useState)(""),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      iconUri = _useState2[0],
      setIconUri = _useState2[1];
    var _useState3 = (0, _react.useState)(true),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      loadingIcon = _useState4[0],
      setLoadingIcon = _useState4[1];
    var _useState5 = (0, _react.useState)({
        width: 0,
        height: 0,
        resizeMode: "contain"
      }),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      iconStyle = _useState6[0],
      setIconStyle = _useState6[1];
    var handleCallback = function handleCallback() {
      callback && callback();
    };
    (0, _react.useEffect)(function () {
      var isMounted = true;
      if (!icon && icon === "") {
        if (isMounted) {
          setLoadingIcon(true);
        }
      } else if (icon) {
        var _env, _env2;
        setIconUri(`${(_env = (0, _envParams.env)()) == null ? undefined : _env.AEM_URL}${icon}`);
        _reactNative.Image.getSize(`${(_env2 = (0, _envParams.env)()) == null ? undefined : _env2.AEM_URL}${icon}`, function (width, height) {
          var maxWidth = 191;
          var newWidth = width;
          var newHeight = height;
          var rate = width / height;
          if (width > maxWidth) {
            newWidth = maxWidth;
            newHeight = maxWidth / rate;
          }
          if (isMounted) {
            setIconStyle({
              width: newWidth,
              height: newHeight,
              resizeMode: "contain"
            });
          }
        });
        if (isMounted) {
          setLoadingIcon(false);
        }
      } else {
        if (isMounted) {
          setLoadingIcon(false);
        }
      }
      return function () {
        isMounted = false;
      };
    }, [icon]);
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: [styles.container, containerStyle],
      children: [!loadingIcon && (icon && iconUri ? (0, _jsxRuntime.jsx)(_baseImage.default, {
        source: {
          uri: iconUri
        },
        style: iconStyle
      }) : (0, _jsxRuntime.jsx)(_icons.ErrorCloudV2, {
        width: 120,
        height: 120
      })), (0, _jsxRuntime.jsx)(_text.Text, {
        style: [styles.title, icon && iconUri && {
          marginTop: 46
        }],
        text: textTitle != null ? textTitle : ""
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        style: styles.description,
        text: textDescription != null ? textDescription : ""
      }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
        style: styles.buttonGradient,
        start: {
          x: 1,
          y: 0
        },
        end: {
          x: 0,
          y: 1
        },
        colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
        children: (0, _jsxRuntime.jsx)(_button.Button, {
          onPress: handleCallback,
          sizePreset: "large",
          textPreset: "buttonLarge",
          typePreset: "primary",
          text: textButtonTitle != null ? textButtonTitle : "",
          backgroundPreset: "light",
          statePreset: "default"
        })
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    container: {
      alignItems: "center"
    },
    title: {
      fontFamily: _theme.typography.black,
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 18,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      }),
      lineHeight: 22,
      marginTop: 24,
      textAlign: "center"
    },
    description: {
      fontFamily: _theme.typography.regular,
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 14,
      fontWeight: _reactNative.Platform.select({
        ios: "400",
        android: "normal"
      }),
      lineHeight: 18,
      textAlign: "center",
      marginTop: 8
    },
    buttonGradient: {
      borderRadius: 999,
      alignSelf: "stretch",
      marginTop: 24
    }
  });
