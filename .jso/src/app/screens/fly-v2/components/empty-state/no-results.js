  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.NoResults = undefined;
  var _icons = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var _theme = _$$_REQUIRE(_dependencyMap[3]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  var NoResults = exports.NoResults = function NoResults(_ref) {
    var containerStyle = _ref.containerStyle;
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: [styles.container, containerStyle],
      children: [(0, _jsxRuntime.jsx)(_icons.ErrorCloudNoResults, {
        width: 120,
        height: 120
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        style: styles.title,
        tx: "errors.noResults.title"
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        style: styles.description,
        tx: "errors.noResults.description"
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    container: {
      alignItems: "center"
    },
    title: {
      fontFamily: _theme.typography.black,
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 18,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      }),
      lineHeight: 22,
      marginTop: 8,
      textAlign: "center"
    },
    description: {
      fontFamily: _theme.typography.regular,
      color: _theme.color.palette.darkestGrey,
      fontSize: 14,
      fontWeight: _reactNative.Platform.select({
        ios: "400",
        android: "normal"
      }),
      lineHeight: 18,
      textAlign: "center",
      marginTop: 8
    }
  });
