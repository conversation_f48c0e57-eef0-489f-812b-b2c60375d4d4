  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.NoInternet = undefined;
  var _icons = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _button = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  var NoInternet = exports.NoInternet = function NoInternet(_ref) {
    var onPressReload = _ref.onPressReload,
      containerStyle = _ref.containerStyle;
    var handleReload = function handleReload() {
      onPressReload && onPressReload();
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: [styles.container, containerStyle],
      children: [(0, _jsxRuntime.jsx)(_icons.ErrorCloudV2, {
        width: 120,
        height: 120
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        style: styles.title,
        tx: "errorOverlay.variant3.title"
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        style: styles.description,
        tx: "errorOverlay.variant3.message"
      }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
        style: styles.buttonGradient,
        start: {
          x: 1,
          y: 0
        },
        end: {
          x: 0,
          y: 1
        },
        colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
        children: (0, _jsxRuntime.jsx)(_button.Button, {
          onPress: handleReload,
          sizePreset: "large",
          textPreset: "buttonLarge",
          typePreset: "primary",
          tx: "errorOverlay.variant3.retry",
          backgroundPreset: "light",
          statePreset: "default"
        })
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    container: {
      alignItems: "center"
    },
    title: {
      fontFamily: _theme.typography.black,
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 18,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      }),
      lineHeight: 22,
      marginTop: 24,
      textAlign: "center"
    },
    description: {
      fontFamily: _theme.typography.regular,
      color: _theme.color.palette.darkestGrey,
      fontSize: 14,
      fontWeight: _reactNative.Platform.select({
        ios: "400",
        android: "normal"
      }),
      lineHeight: 18,
      textAlign: "center",
      marginTop: 8
    },
    buttonGradient: {
      borderRadius: 999,
      alignSelf: "stretch",
      marginTop: 24
    }
  });
