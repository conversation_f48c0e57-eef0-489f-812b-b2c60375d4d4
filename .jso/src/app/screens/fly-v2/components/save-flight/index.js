  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _native = _$$_REQUIRE(_dependencyMap[3]);
  var _i18n = _$$_REQUIRE(_dependencyMap[4]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[5]);
  var _storage = _$$_REQUIRE(_dependencyMap[6]);
  var _lodash = _$$_REQUIRE(_dependencyMap[7]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _confirmPopupSaveFlight = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[10]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[11]);
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[12]);
  var _constants = _$$_REQUIRE(_dependencyMap[13]);
  var _feedbackToast = _$$_REQUIRE(_dependencyMap[14]);
  var _mytravelRedux = _$$_REQUIRE(_dependencyMap[15]);
  var _dateTime = _$$_REQUIRE(_dependencyMap[16]);
  var _flyRedux = _$$_REQUIRE(_dependencyMap[17]);
  var _useFlightDetailV = _$$_REQUIRE(_dependencyMap[18]);
  var _dineShopV = _$$_REQUIRE(_dependencyMap[19]);
  var _addReturnCalendar = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[20]));
  var _flightDetail = _$$_REQUIRE(_dependencyMap[21]);
  var _saveFlightTraveOptionWrap = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[22]));
  var _styles = _$$_REQUIRE(_dependencyMap[23]);
  var _modalManagerRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[24]));
  var _useModal2 = _$$_REQUIRE(_dependencyMap[25]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[26]);
  var _flightListingContext = _$$_REQUIRE(_dependencyMap[27]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[28]);
  var _envParams = _$$_REQUIRE(_dependencyMap[29]);
  var _searchRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[30]));
  var _searchDepartureFlight = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[31]));
  var _screenHelper = _$$_REQUIRE(_dependencyMap[32]);
  var _panResponder = _$$_REQUIRE(_dependencyMap[33]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[34]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SaveFlight = function SaveFlight(_ref) {
    var _dataCommonAEM$data;
    var route = _ref.route;
    var dispatch = (0, _reactRedux.useDispatch)();
    var navigation = (0, _native.useNavigation)();
    var isFocused = (0, _native.useIsFocused)();
    var toastForSavedFlight = (0, _react.useRef)(null);
    var _useFlightListingCont = (0, _flightListingContext.useFlightListingContext)(),
      saveFlightPayload = _useFlightListingCont.saveFlightPayload,
      isModalVisible = _useFlightListingCont.isModalVisible,
      closeModal = _useFlightListingCont.closeModal,
      selectedTravelOption = _useFlightListingCont.selectedTravelOption,
      setSelectedTravelOption = _useFlightListingCont.setSelectedTravelOption;
    var _useContext = (0, _react.useContext)(_panResponder.PanResponderContext),
      conditionTimeRef = _useContext.conditionTimeRef,
      idleTimeRef = _useContext.idleTimeRef;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      loadingSaveFlight = _useState2[0],
      setLoadingSaveFlight = _useState2[1];
    var _useState3 = (0, _react.useState)(null),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      selectedFlightType = _useState4[0],
      setSelectedFlightType = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      showCalendarModal = _useState6[0],
      setShowCalendarModal = _useState6[1];
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var myTravelFlightsPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.myTravelFlightsPayload);
    var _useModal = (0, _useModal2.useModal)("saveConnectingFlightListingV2"),
      isModalVisibleConnectingFlight = _useModal.isModalVisible,
      openModalConnectingFlight = _useModal.openModal,
      closeModalConnectingFlight = _useModal.closeModal;
    var insertFlightPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.insertFlightPayload);
    var dataCommonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getMessagesCommon);
    var msg47 = dataCommonAEM == null || (_dataCommonAEM$data = dataCommonAEM.data) == null || (_dataCommonAEM$data = _dataCommonAEM$data.messages) == null ? undefined : _dataCommonAEM$data.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG47";
    });
    var handleTriggerAppRatingIdle = function handleTriggerAppRatingIdle() {
      (0, _screenHelper.resetInactivityTimeout)({
        conditionTimeRef: conditionTimeRef,
        idleTimeRef: idleTimeRef,
        callback: function callback() {
          return (0, _screenHelper.trackingShowRatingPopup)({
            route: route
          });
        }
      });
    };
    (0, _react.useEffect)(function () {
      var _insertFlightPayload$;
      if (insertFlightPayload != null && (_insertFlightPayload$ = insertFlightPayload.insertFlightData) != null && _insertFlightPayload$.success || insertFlightPayload != null && insertFlightPayload.recordExist) {
        if (insertFlightPayload != null && insertFlightPayload.isInsertSuccessfully && isFocused) {
          var _env, _insertFlightPayload$2;
          setLoadingSaveFlight(false);
          (0, _storage.save)(_storage.StorageKey.isSaveFlightTriggered, true);
          handleTriggerAppRatingIdle();
          var timeStamp = new Date().getTime();
          // if user hasnt save any flight within 24hours or user has only 1 saved flight
          // show save connecting flight modal
          // else show native share popup and finish save
          var showConnectingFlight = ((0, _mmkvStorage.getLastSavedFlightTime)() + ((_env = (0, _envParams.env)()) == null ? undefined : _env.FLIGHT_SHOW_POPUP_ADD_RETURN) < timeStamp || (0, _lodash.size)(myTravelFlightsPayload == null ? undefined : myTravelFlightsPayload.getMyTravelFlightDetails) === 1) && (insertFlightPayload == null || (_insertFlightPayload$2 = insertFlightPayload.flightData) == null ? undefined : _insertFlightPayload$2.isPassenger);
          if (!showConnectingFlight) {
            var _toastForSavedFlight$;
            closeModal();
            toastForSavedFlight == null || (_toastForSavedFlight$ = toastForSavedFlight.current) == null || _toastForSavedFlight$.show(_feedbackToast.DURATION.LENGTH_LONG);
          } else {
            openModalConnectingFlight();
            (0, _mmkvStorage.setLastSavedFlightTime)(0);
            dispatch(_searchRedux.default.resetAutoCompleteFlight());
          }
        }
      }
      if (insertFlightPayload != null && insertFlightPayload.errorFlag) {
        setLoadingSaveFlight(false);
        closeModal();
        handleTriggerAppRatingIdle();
      }
    }, [insertFlightPayload]);
    var savedFlightOnPress = function savedFlightOnPress() {
      setLoadingSaveFlight(true);
      var data = {
        enterpriseUserId: profilePayload == null ? undefined : profilePayload.email,
        countryOfResidence: profilePayload == null ? undefined : profilePayload.residentialCountry,
        flightNumber: saveFlightPayload == null ? undefined : saveFlightPayload.flightNumber,
        flightScheduledDate: saveFlightPayload == null ? undefined : saveFlightPayload.scheduledDate,
        flightDirection: saveFlightPayload.direction,
        // check param
        flightPax: selectedTravelOption === _flightDetail.TravelOption.iAmTravelling
      };
      if (isLoggedIn) {
        dispatch(_mytravelRedux.MytravelCreators.flyMyTravelInsertFlightRequest(data, {
          item: saveFlightPayload
        }));
      } else {
        //@ts-ignore
        navigation.navigate(_constants.NavigationConstants.authScreen, {
          sourceSystem: _constants.SOURCE_SYSTEM.FLIGHTS
        });
      }
    };
    var showToastForSaveFlightSuccess = function showToastForSaveFlightSuccess() {
      var _toastForSavedFlight$2;
      toastForSavedFlight == null || (_toastForSavedFlight$2 = toastForSavedFlight.current) == null || _toastForSavedFlight$2.show(_feedbackToast.DURATION.LENGTH_LONG);
    };
    var openSearchDepartureFlightModal = function openSearchDepartureFlightModal() {
      dispatch(_modalManagerRedux.default.openModal("searchDepartureFlight"));
    };
    var travelOptionTapped = function travelOptionTapped(option) {
      setSelectedTravelOption(option);
    };
    var savedFlightTravelOptionsOnModalHide = function savedFlightTravelOptionsOnModalHide() {
      setSelectedTravelOption(null);
      (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
    };
    var onDateSelectedAddReturnCalendar = function onDateSelectedAddReturnCalendar(dateString) {
      var country = (saveFlightPayload == null ? undefined : saveFlightPayload.country) || "";
      var date = (0, _moment.default)(dateString);
      setShowCalendarModal(false);
      dispatch(_flyRedux.FlyCreators.setFlightSearchDate(date.format("YYYY-MM-DD")));
      navigation.navigate(_constants.NavigationConstants.searchFlightsV2Result, {
        keyword: country,
        date: date,
        direction: (saveFlightPayload == null ? undefined : saveFlightPayload.direction) === _constants.FlightDirection.Departure ? _constants.FlightDirection.Arrival : _constants.FlightDirection.Departure
      });
    };
    var handleConnectingFlightOnPress = function handleConnectingFlightOnPress() {
      var _insertFlightPayload$3, _insertFlightPayload$4;
      var connectingFlightPayloadData = {
        isConnecting: true,
        flightConnecting: Object.assign({}, insertFlightPayload == null || (_insertFlightPayload$3 = insertFlightPayload.flightData) == null ? undefined : _insertFlightPayload$3.item, {
          isPassenger: insertFlightPayload == null || (_insertFlightPayload$4 = insertFlightPayload.flightData) == null ? undefined : _insertFlightPayload$4.isPassenger
        })
      };
      dispatch(_flyRedux.FlyCreators.setConnectingFlightPayload(connectingFlightPayloadData));
      setSelectedFlightType(_useFlightDetailV.FLIGHT_TYPE.CONNECTING);
      closeModalConnectingFlight();
    };
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_feedbackToast.FeedBackToast, {
        ref: toastForSavedFlight,
        style: _styles.rootStyles.feedBackToastStyle,
        textButtonStyle: _styles.rootStyles.toastButtonStyle,
        position: "custom",
        positionValue: {
          bottom: 8
        },
        textStyle: _styles.rootStyles.toastTextStyle,
        type: _feedbackToast.FeedBackToastType.smallFeedBack,
        text: (0, _i18n.translate)("flightDetails.newPopupConfirmSaveFlight.toastFlightSaved"),
        onCallback: function onCallback() {
          return dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
        }
      }), (0, _jsxRuntime.jsx)(_saveFlightTraveOptionWrap.default, {
        onModalHide: savedFlightTravelOptionsOnModalHide,
        visible: isModalVisible,
        onClosed: closeModal,
        loadingSaveFlight: loadingSaveFlight,
        onBackPressed: closeModal,
        selectedOption: selectedTravelOption,
        savedFlightOnPress: savedFlightOnPress,
        onPress: function onPress(option) {
          return travelOptionTapped(option);
        },
        flightDirection: saveFlightPayload == null ? undefined : saveFlightPayload.direction
      }), (0, _jsxRuntime.jsx)(_confirmPopupSaveFlight.default, {
        imageUrl: (0, _mediaHelper.handleImageUrl)(msg47 == null ? undefined : msg47.icon),
        visible: isModalVisibleConnectingFlight,
        title: (0, _i18n.translate)("flightDetails.newPopupConfirmSaveFlight.title"),
        messageText: (0, _i18n.translate)("flightDetails.newPopupConfirmSaveFlight.arrivalMessage"),
        onClose: function onClose() {
          closeModalConnectingFlight();
          (0, _storage.save)(_storage.StorageKey.isSaveFlightTriggered, true);
        },
        onButtonPressed: function onButtonPressed() {
          setSelectedFlightType(_useFlightDetailV.FLIGHT_TYPE.RETURN);
          closeModalConnectingFlight();
        },
        textButtonConfirm: (0, _i18n.translate)("flightDetails.newPopupConfirmSaveFlight.addReturnFlightButton"),
        textButtonCancel: (0, _i18n.translate)("flightDetails.newPopupConfirmSaveFlight.cancelButton"),
        onModalHide: function onModalHide() {
          var timeStamp = new Date().getTime();
          (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
          (0, _mmkvStorage.setLastSavedFlightTime)(timeStamp);
          dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
          if (selectedFlightType) {
            if (saveFlightPayload.direction === _constants.FlightDirection.Arrival && selectedFlightType === _useFlightDetailV.FLIGHT_TYPE.CONNECTING) {
              openSearchDepartureFlightModal();
            } else {
              setShowCalendarModal(true);
            }
            setSelectedFlightType(null);
          } else {
            var _toastForSavedFlight$3;
            toastForSavedFlight == null || (_toastForSavedFlight$3 = toastForSavedFlight.current) == null || _toastForSavedFlight$3.show(_feedbackToast.DURATION.LENGTH_LONG);
            dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
          }
        },
        isShowButtonConnection: (saveFlightPayload == null ? undefined : saveFlightPayload.direction) === _constants.FlightDirection.Arrival,
        onButtonConnectionPressed: handleConnectingFlightOnPress,
        textButtonConnection: (0, _i18n.translate)("flightDetails.newPopupConfirmSaveFlight.addConnectingFlightButton"),
        disableCloseButton: true,
        openPendingModal: true
      }), (0, _jsxRuntime.jsx)(_addReturnCalendar.default, {
        isVisible: showCalendarModal,
        filterDate: (0, _moment.default)(saveFlightPayload == null ? undefined : saveFlightPayload.displayTimestamp).format(_dateTime.DateFormats.YearMonthDay),
        initialMinDate: (0, _moment.default)(saveFlightPayload == null ? undefined : saveFlightPayload.displayTimestamp).format(_dateTime.DateFormats.YearMonthDay),
        onClosedCalendarModal: function onClosedCalendarModal() {
          var _toastForSavedFlight$4;
          setShowCalendarModal(false);
          toastForSavedFlight == null || (_toastForSavedFlight$4 = toastForSavedFlight.current) == null || _toastForSavedFlight$4.show(_feedbackToast.DURATION.LENGTH_LONG);
          var connectingFlightPayloadToClear = {
            isConnecting: false,
            flightConnecting: null
          };
          dispatch(_flyRedux.FlyCreators.setConnectingFlightPayload(connectingFlightPayloadToClear));
          dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
          (0, _storage.save)(_storage.StorageKey.isSaveFlightTriggered, true);
        },
        onDateSelected: onDateSelectedAddReturnCalendar,
        testID: `${_dineShopV.SCREEN_NAME}__AddReturnCalendar`,
        accessibilityLabel: `${_dineShopV.SCREEN_NAME}__AddReturnCalendar`
      }), (0, _jsxRuntime.jsx)(_searchDepartureFlight.default, {
        handleOnClose: showToastForSaveFlightSuccess,
        displayTimestamp: saveFlightPayload == null ? undefined : saveFlightPayload.displayTimestamp
      })]
    });
  };
  var _default = exports.default = SaveFlight;
