  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _worklet_16884805394087_init_data = {
    code: "function indexTsx1(){const{index,progress,interpolate}=this.__closure;const inputRange=[0,0.5,1];const outputRange=[0,-10,0];const phase=index*0.2%1;const shiftedProgress=(progress.value+phase)%1;const translateY=interpolate(shiftedProgress,inputRange,outputRange);return{transform:[{translateY:translateY}]};}"
  };
  var Dot = function Dot(_ref) {
    var index = _ref.index;
    var progress = (0, _reactNativeReanimated.useSharedValue)(0);
    _react.default.useEffect(function () {
      progress.value = (0, _reactNativeReanimated.withRepeat)((0, _reactNativeReanimated.withTiming)(1, {
        duration: 1000,
        easing: _reactNativeReanimated.Easing.linear
      }), -1, false);
    }, []);
    var animatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var indexTsx1 = function indexTsx1() {
        var inputRange = [0, 0.5, 1];
        var outputRange = [0, -10, 0];
        var phase = index * 0.2 % 1;
        var shiftedProgress = (progress.value + phase) % 1;
        var translateY = (0, _reactNativeReanimated.interpolate)(shiftedProgress, inputRange, outputRange);
        return {
          transform: [{
            translateY: translateY
          }]
        };
      };
      indexTsx1.__closure = {
        index: index,
        progress: progress,
        interpolate: _reactNativeReanimated.interpolate
      };
      indexTsx1.__workletHash = 16884805394087;
      indexTsx1.__initData = _worklet_16884805394087_init_data;
      return indexTsx1;
    }());
    return (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
      style: [styles.dot, animatedStyle]
    });
  };
  var LoadingIndicator = function LoadingIndicator() {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsx)(Dot, {
        index: 0
      }), (0, _jsxRuntime.jsx)(Dot, {
        index: 1
      }), (0, _jsxRuntime.jsx)(Dot, {
        index: 2
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    container: {
      flexDirection: 'row',
      gap: 8,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 20
    },
    dot: {
      width: 8,
      height: 8,
      borderRadius: 8,
      backgroundColor: _theme.color.palette.lightPurple
    }
  });
  var _default = exports.default = LoadingIndicator;
