  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _alertApp = _$$_REQUIRE(_dependencyMap[5]);
  var _alertApp2 = _$$_REQUIRE(_dependencyMap[6]);
  var _feedbackToast = _$$_REQUIRE(_dependencyMap[7]);
  var _text = _$$_REQUIRE(_dependencyMap[8]);
  var _useModal2 = _$$_REQUIRE(_dependencyMap[9]);
  var _i18n = _$$_REQUIRE(_dependencyMap[10]);
  var _aemRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[11]));
  var _flightListingRedux = _$$_REQUIRE(_dependencyMap[12]);
  var _flyRedux = _$$_REQUIRE(_dependencyMap[13]);
  var _mytravelRedux = _$$_REQUIRE(_dependencyMap[14]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[15]);
  var _flightProps = _$$_REQUIRE(_dependencyMap[16]);
  var _flightResultStyles = _$$_REQUIRE(_dependencyMap[17]);
  var _flightResults = _$$_REQUIRE(_dependencyMap[18]);
  var _category = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[19]));
  var _flightItem = _$$_REQUIRE(_dependencyMap[20]);
  var _adobe = _$$_REQUIRE(_dependencyMap[21]);
  var _utils = _$$_REQUIRE(_dependencyMap[22]);
  var _constants = _$$_REQUIRE(_dependencyMap[23]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[24]);
  var _lodash = _$$_REQUIRE(_dependencyMap[25]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[26]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[27]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[28]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[29]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[30]);
  var _components = _$$_REQUIRE(_dependencyMap[31]);
  var _emptyState = _$$_REQUIRE(_dependencyMap[32]);
  var _flightListToolbar = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[33]));
  var _loadingIndicator = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[34]));
  var _flightListingContext = _$$_REQUIRE(_dependencyMap[35]);
  var _flightListing = _$$_REQUIRE(_dependencyMap[36]);
  var _hooks = _$$_REQUIRE(_dependencyMap[37]);
  var _reactNativeGestureHandler = _$$_REQUIRE(_dependencyMap[38]);
  var _flightListing2 = _$$_REQUIRE(_dependencyMap[39]);
  var _flightListingSubscription = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[40]));
  var _useTickerbandMaintanence = _$$_REQUIRE(_dependencyMap[41]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[42]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var COMPONENT_NAME = "ArrivalListingScreen__";
  var AnimatedSectionList = _reactNativeReanimated.default.createAnimatedComponent(_reactNative.SectionList);
  var _worklet_12638292459899_init_data = {
    code: "function indexTsx1(event){const{scrollY,currentScrollPosition,willBeRefreshed,canLoadMore,runOnJS,enableLoadMore}=this.__closure;const{contentOffset:contentOffset,contentSize:contentSize,layoutMeasurement:layoutMeasurement}=event;scrollY.value=event.contentOffset.y;currentScrollPosition.value=event.contentOffset.y;if(event.contentOffset.y<=20){willBeRefreshed.value=true;}else{willBeRefreshed.value=false;}if(layoutMeasurement.height+contentOffset.y<=contentSize.height-100&&!canLoadMore){runOnJS(enableLoadMore)();}}"
  };
  var _worklet_12537891428510_init_data = {
    code: "function indexTsx2(){const{sharedRefreshing}=this.__closure;sharedRefreshing.value=0;}"
  };
  var _worklet_13342916963843_init_data = {
    code: "function indexTsx3(){const{sharedRefreshing,selectedTab,FlightDirection,contentPosition,withTiming,GESTURE_THRESHOLD,runOnJS,onRefresh}=this.__closure;if(sharedRefreshing.value===1&&selectedTab===FlightDirection.arrival){contentPosition.value=withTiming(Math.max(contentPosition.value,GESTURE_THRESHOLD));runOnJS(onRefresh)();}else{contentPosition.value=withTiming(0);}}"
  };
  var _worklet_6392193651030_init_data = {
    code: "function indexTsx4(e){const{currentScrollPosition,positionStartRefresh,GESTURE_THRESHOLD,contentPosition,interpolate,Extrapolation,sharedRefreshing}=this.__closure;if(currentScrollPosition.value===0){if(positionStartRefresh.value>0){if(positionStartRefresh.value+GESTURE_THRESHOLD<=e.y){contentPosition.value=interpolate(e.translationY,[0,GESTURE_THRESHOLD*3],[0,GESTURE_THRESHOLD],Extrapolation.CLAMP);sharedRefreshing.value=1;}else{sharedRefreshing.value=0;}}else{positionStartRefresh.value=e.y;sharedRefreshing.value=0;contentPosition.value=0;}}else{sharedRefreshing.value=0;contentPosition.value=0;}}"
  };
  var _worklet_14955339992050_init_data = {
    code: "function indexTsx5(e){const{currentScrollPosition,positionStartRefresh}=this.__closure;if(currentScrollPosition.value===0){positionStartRefresh.value=e.y;}}"
  };
  function ArrivalScreen(_ref, ref) {
    var navigation = _ref.navigation;
    // state
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isNoConnection = _useState2[0],
      setNoConnection = _useState2[1];
    var _useTickerbandMaintan = (0, _useTickerbandMaintanence.useTickerbandMaintanance)(_useTickerbandMaintanence.MaintananceTickerBandType.FLY_LISTING),
      isShowMaintenance = _useTickerbandMaintan.isShowMaintenance,
      fetchTickerbandMaintanance = _useTickerbandMaintan.fetchTickerbandMaintanance;
    var _useFlightListingCont = (0, _flightListingContext.useFlightListingContext)(),
      openSearchBottomSheet = _useFlightListingCont.openSearchBottomSheet,
      setTerminal = _useFlightListingCont.setTerminal,
      setAirline = _useFlightListingCont.setAirline,
      setAirport = _useFlightListingCont.setAirport,
      handleSelectTravelOptionAndOpenModal = _useFlightListingCont.handleSelectTravelOptionAndOpenModal,
      selectedTab = _useFlightListingCont.selectedTab,
      currentScrollPosition = _useFlightListingCont.currentScrollPosition,
      sharedRefreshing = _useFlightListingCont.sharedRefreshing,
      contentPosition = _useFlightListingCont.contentPosition,
      setIsRefreshing = _useFlightListingCont.setIsRefreshing;
    var flightListingFilter = (0, _reactRedux.useSelector)(_flightListingRedux.FlightListingSelectors.flightListingFilter);
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var flightFilterOptions = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flightFilterOptions);
    var dataCommonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getMessagesCommon);
    var errorCommonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var _React$useContext = _react.default.useContext(_flightResults.FlyContext),
      isLoadFlightAfter24h = _React$useContext.isLoadFlightAfter24h;
    var _useFlightListingCont2 = (0, _flightListingContext.useFlightListingContext)(),
      setLastUpdated = _useFlightListingCont2.setLastUpdated,
      scrollY = _useFlightListingCont2.scrollY,
      isRefreshing = _useFlightListingCont2.isRefreshing;
    var _useArrivalFlightV = (0, _hooks.useArrivalFlightV2)(),
      sectionList = _useArrivalFlightV.sectionList,
      isLoading = _useArrivalFlightV.isLoading,
      isEndLoadMore = _useArrivalFlightV.isEndLoadMore,
      isFocusedRef = _useArrivalFlightV.isFocusedRef,
      lastUpdatedTime = _useArrivalFlightV.lastUpdatedTime,
      startLoopApiCall = _useArrivalFlightV.startLoopApiCall,
      getFlyArrivalList = _useArrivalFlightV.getFlyArrivalList,
      removeSavedFlight = _useArrivalFlightV.removeSavedFlight,
      cancelLoopApiJob = _useArrivalFlightV.cancelLoopApiJob,
      getEarlierFlights = _useArrivalFlightV.getEarlierFlights,
      isLoadingEarlierFlights = _useArrivalFlightV.isLoadingEarlierFlights,
      isEndEarlierFlights = _useArrivalFlightV.isEndEarlierFlights,
      networkFailedAttempt = _useArrivalFlightV.networkFailedAttempt,
      isNetworkError = _useArrivalFlightV.isNetworkError,
      canLoadMore = _useArrivalFlightV.canLoadMore,
      setCanLoadMore = _useArrivalFlightV.setCanLoadMore,
      setNetworkError = _useArrivalFlightV.setNetworkError,
      setPreviousToken = _useArrivalFlightV.setPreviousToken,
      isFirstLoadRef = _useArrivalFlightV.isFirstLoadRef;
    var _useModal = (0, _useModal2.useModal)("flightResultCategoryFilter"),
      isFilterModalVisible = _useModal.isModalVisible;
    var willBeRefreshed = (0, _reactNativeReanimated.useSharedValue)(true);
    var positionStartRefresh = (0, _reactNativeReanimated.useSharedValue)(0);
    var scrollRef = (0, _react.useRef)(null);
    var alertApp = (0, _react.useRef)(null);
    var isAfterLogin = (0, _react.useRef)(false);
    var filterRef = (0, _react.useRef)({
      date: flightListingFilter == null ? undefined : flightListingFilter.date,
      keyword: flightListingFilter == null ? undefined : flightListingFilter.keyword,
      terminal: flightListingFilter == null ? undefined : flightListingFilter.terminal,
      airline: flightListingFilter == null ? undefined : flightListingFilter.airline,
      airport: flightListingFilter == null ? undefined : flightListingFilter.airport
    });
    var toastForRemoveFlight = (0, _react.useRef)(null);
    var toastForRefresh = (0, _react.useRef)(null);
    var categoryRef = (0, _react.useRef)(null);
    var terminalList = (flightFilterOptions == null ? undefined : flightFilterOptions.terminal) || [];
    var msg58 = dataCommonAEM == null ? undefined : dataCommonAEM.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG58";
    });
    var msg48 = dataCommonAEM == null ? undefined : dataCommonAEM.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG48";
    });
    var msg50 = dataCommonAEM == null ? undefined : dataCommonAEM.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG50";
    });
    var ehr44 = errorCommonAEM == null ? undefined : errorCommonAEM.find(function (e) {
      return (e == null ? undefined : e.code) === "EHR44";
    });
    var listNotEmpty = (0, _react.useMemo)(function () {
      return sectionList == null ? undefined : sectionList.some(function (el) {
        var _el$data;
        return (el == null || (_el$data = el.data) == null ? undefined : _el$data.length) > 0;
      });
    }, [sectionList]);

    // func
    var handleInternetConnection = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* (_ref2) {
        var onSuccess = _ref2.onSuccess,
          onFail = _ref2.onFail;
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        if (isConnected) {
          setNoConnection(false);
          if (onSuccess && typeof onSuccess === "function") {
            onSuccess();
          }
        } else {
          setLastUpdated("");
          setIsRefreshing(false);
          contentPosition.value = (0, _reactNativeReanimated.withTiming)(0);
          if (onFail && typeof onFail === "function") {
            onFail();
          }
          if (!isFirstLoadRef.current) {
            var _toastForRefresh$curr, _toastForRefresh$curr2;
            toastForRefresh == null || (_toastForRefresh$curr = toastForRefresh.current) == null || _toastForRefresh$curr.closeNow();
            toastForRefresh == null || (_toastForRefresh$curr2 = toastForRefresh.current) == null || _toastForRefresh$curr2.show(_constants.TOAST_MESSAGE_DURATION);
          } else {
            setNoConnection(true);
          }
        }
      });
      return function handleInternetConnection(_x) {
        return _ref3.apply(this, arguments);
      };
    }();
    var loadArrivalResults = function loadArrivalResults(_ref4) {
      var terminal = _ref4.terminal,
        airline = _ref4.airline,
        airport = _ref4.airport;
      getFlyArrivalList({
        direction: _flightProps.FlightDirection.arrival,
        filterDate: (0, _moment.default)(),
        filters: (0, _utils.simpleCondition)({
          condition: (terminal == null ? undefined : terminal.length) === terminalList.length - 1 || (0, _lodash.isEmpty)(terminal),
          ifValue: [],
          elseValue: terminal
        }),
        isFilter: false,
        isLoadFlightAfter24h: isLoadFlightAfter24h,
        filterAirline: airline != null ? airline : "",
        filterCityAirport: airport != null ? airport : ""
      });
      startLoopApiCall(handleRefresh);
    };
    var refreshArrivalFlights = function refreshArrivalFlights(_ref5) {
      var terminal = _ref5.terminal,
        airline = _ref5.airline,
        airport = _ref5.airport;
      // prevent refresh data while user typing search keyword
      // if (userTyping.current) {
      //   return
      // }
      loadArrivalResults({
        terminal: terminal,
        airline: airline,
        airport: airport
      });
      dispatch(_aemRedux.default.getAemConfigData({
        name: _aemRedux.AEM_PAGE_NAME.TICKER_BAND_FLIGHT,
        pathName: "getTickerbandFly",
        forceRequest: true
      }));
    };
    var handleRefresh = /*#__PURE__*/function () {
      var _ref6 = (0, _asyncToGenerator2.default)(function* () {
        handleInternetConnection({
          onSuccess: function onSuccess() {
            if (willBeRefreshed.value) {
              setNetworkError(false);
              refreshArrivalFlights({
                terminal: filterRef.current.terminal,
                airline: filterRef.current.airline,
                airport: filterRef.current.airport
              });
              dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
            }
          }
        });
      });
      return function handleRefresh() {
        return _ref6.apply(this, arguments);
      };
    }();
    var scrollListToTop = function scrollListToTop() {
      willBeRefreshed.value = true;
      if (sectionList.length > 0) {
        var _scrollRef$current;
        (_scrollRef$current = scrollRef.current) == null || _scrollRef$current.scrollToLocation({
          animated: true,
          sectionIndex: 0,
          itemIndex: 0,
          viewPosition: 0,
          viewOffset: 120
        });
      }
    };
    var onFilter = /*#__PURE__*/function () {
      var _ref8 = (0, _asyncToGenerator2.default)(function* (_ref7) {
        var terminal = _ref7.terminal,
          airline = _ref7.airline,
          airport = _ref7.airport;
        handleInternetConnection({
          onSuccess: function onSuccess() {
            // if (date) {
            //   trackAction(AdobeTagName.CAppFlightListingFlightListFilter, {
            //     [AdobeTagName.CAppFlightListingFlightListFilter]: moment(date).format("YYYY-MM-DD"),
            //   })
            // }
            // if (keyword) {
            //   trackAction(AdobeTagName.CAppFlightListingFlightListFilter, {
            //     [AdobeTagName.CAppFlightListingFlightListFilter]: `${keyword}`,
            //   })
            // }
            if (terminal) {
              filterRef.current.terminal = terminal;
              (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlightListingFlightListFilter, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlightListingFlightListFilter, `${terminal.join("|")}`));
            }
            if (airline) {
              filterRef.current.airline = airline;
            }
            if (airport) {
              filterRef.current.airport = airport;
            }
            scrollListToTop();
            setTimeout(function () {
              scrollY.value = (0, _reactNativeReanimated.withTiming)(0, {
                duration: 300
              });
            }, 300);
            refreshArrivalFlights({
              terminal: terminal,
              airline: airline,
              airport: airport
            });

            // Update the context values so that the search bottom sheet can navigate to SearchResult screen with the correct filters.
            setTerminal(terminal);
            setAirline(airline);
            setAirport(airport);
          }
        });
      });
      return function onFilter(_x2) {
        return _ref8.apply(this, arguments);
      };
    }();
    var onFlightPress = function onFlightPress(item) {
      var flightDate = item.flightDate;
      var flightNumber = item.flightNumber;
      var direction = item.direction;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlightListingFlightCardClicked, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlightListingFlightCardClicked, "1"));
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlightListingViewFlightDetails, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlightListingViewFlightDetails, `${direction}|${flightDate}|${flightNumber}`));
      //@ts-ignore
      navigation.navigate("flightDetails", {
        payload: {
          item: item
        },
        direction: _flightProps.FlightDirection.arrival
      });
    };
    var handleMessage58 = function handleMessage58(message, flyItem) {
      if (message) {
        var _flyItem$flightStatus, _status;
        var status = flyItem == null || (_flyItem$flightStatus = flyItem.flightStatus) == null ? undefined : _flyItem$flightStatus.toLowerCase();
        if ((_status = status) != null && _status.includes("cancelled")) {
          status = `been ${status}`;
        }
        return message.replace("<Flight No.>", flyItem == null ? undefined : flyItem.flightNumber).replace("<departed/landed/been cancelled>", status);
      }
      return message;
    };
    var handleMessage48 = function handleMessage48(message, number, place) {
      if (message) {
        return message.replace("<Flight No.>", number).replace("<country>", place);
      }
      return message;
    };
    var notAbleToSaveAlert = function notAbleToSaveAlert(flyItem) {
      var _flyItem$flightStatus2, _alertApp$current;
      var temp = flyItem == null || (_flyItem$flightStatus2 = flyItem.flightStatus) == null ? undefined : _flyItem$flightStatus2.split(" ");
      var status = (temp == null ? undefined : temp.length) > 0 ? temp[0] : "";
      var message = handleMessage58(msg58 == null ? undefined : msg58.message, flyItem) || `${(0, _i18n.translate)("flightLanding.flight")} ${flyItem == null ? undefined : flyItem.flightNumber} ${(0, _i18n.translate)("flightLanding.has")} ${status} ${(0, _i18n.translate)("flightLanding.notSaveMessage")}`;
      alertApp == null || (_alertApp$current = alertApp.current) == null || _alertApp$current.show({
        title: (msg58 == null ? undefined : msg58.title) || (0, _i18n.translate)("flightLanding.alert"),
        description: message,
        labelAccept: (msg58 == null ? undefined : msg58.firstButton) || (0, _i18n.translate)("flightLanding.okay"),
        onAccept: function onAccept() {
          return null;
        },
        type: _alertApp2.AlertTypes.ALERT
      });
    };
    var onRemoveFlight = function onRemoveFlight(payload) {
      var item = payload == null ? undefined : payload.item;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlightListingRemoveFlight, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlightListingRemoveFlight, "1"));
      _reactNative.Alert.alert((msg48 == null ? undefined : msg48.title) || (0, _i18n.translate)("flightLanding.areYouSure"), msg48 != null && msg48.message ? handleMessage48(msg48 == null ? undefined : msg48.message, item == null ? undefined : item.flightNumber, item == null ? undefined : item.destinationPlace) : `${(0, _i18n.translate)("flightLanding.removeMessage1")} ${item == null ? undefined : item.flightNumber} ${(0, _i18n.translate)("flightLanding.to")} ${item == null ? undefined : item.destinationPlace} ${(0, _i18n.translate)("flightLanding.removeMessage2")}`, [{
        text: (msg48 == null ? undefined : msg48.firstButton) || (0, _i18n.translate)("flightLanding.cancel")
      }, {
        text: (msg48 == null ? undefined : msg48.secondButton) || (0, _i18n.translate)("flightLanding.remove"),
        style: "cancel",
        onPress: function onPress() {
          removeSavedFlight(payload, function () {
            var _toastForRemoveFlight;
            var action = "Unsave";
            var flyProfile = "flying";
            var flightStatus = "Successful";
            (0, _screenHelper.trackActionOldFormat)(_adobe.AdobeTagName.CAppFlightListingSaveFlight, {
              pageName: _adobe.AdobeTagName.CAppFlightListing,
              flightNumber: item == null ? undefined : item.flightNumber,
              flightDirection: _flightProps.FlightDirection.arrival,
              flightDate: item == null ? undefined : item.flightDate,
              flyProfile: flyProfile,
              action: action,
              flightStatus: flightStatus
            });
            toastForRemoveFlight == null || (_toastForRemoveFlight = toastForRemoveFlight.current) == null || _toastForRemoveFlight.show(_constants.TOAST_MESSAGE_DURATION);
            dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
          }, function () {
            var action = "Unsave";
            var flyProfile = "flying";
            var flightStatus = "Failed";
            (0, _screenHelper.trackActionOldFormat)(_adobe.AdobeTagName.CAppFlightListingSaveFlight, {
              pageName: _adobe.AdobeTagName.CAppFlightListing,
              flightNumber: item == null ? undefined : item.flightNumber,
              flightDirection: _flightProps.FlightDirection.arrival,
              flightDate: item == null ? undefined : item.flightDate,
              flyProfile: flyProfile,
              action: action,
              flightStatus: flightStatus
            });
          });
        }
      }]);
    };
    var onSaveFlight = function onSaveFlight(_ref9) {
      var flight = _ref9.flight,
        isSaved = _ref9.isSaved,
        canSaveFlight = _ref9.canSaveFlight;
      if (isLoggedIn && isSaved) {
        onRemoveFlight({
          item: flight
        });
      } else {
        if (!canSaveFlight) {
          notAbleToSaveAlert(flight);
        } else {
          var saveFlight = flight;
          if (isLoggedIn) {
            handleSelectTravelOptionAndOpenModal(saveFlight);
          } else {
            isAfterLogin.current = true;
            navigation.navigate(_constants.NavigationConstants.authScreen, {
              sourceSystem: _constants.SOURCE_SYSTEM.FLIGHTS,
              callBackAfterLoginSuccess: function callBackAfterLoginSuccess() {
                handleSelectTravelOptionAndOpenModal(saveFlight);
              },
              callBackAfterLoginCancel: function callBackAfterLoginCancel() {
                return null;
              }
            });
          }
        }
      }
    };
    var onRenderItem = function onRenderItem(_ref0) {
      var item = _ref0.item,
        index = _ref0.index;
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: _flightResultStyles.commonComponentStyles.flatListItemStyle,
        children: (0, _jsxRuntime.jsx)(_flightItem.FlightItem, {
          flight: item,
          isLoggedIn: isLoggedIn,
          onPressed: onFlightPress,
          onSaveFlight: onSaveFlight,
          isFirstFlight: index === 0
        })
      });
    };
    var renderSectionHeader = function renderSectionHeader(_ref1) {
      var _ref1$section = _ref1.section,
        title = _ref1$section.title,
        data = _ref1$section.data;
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          preset: "caption2Regular",
          text: title == null ? undefined : title.toUpperCase(),
          style: [_flightListing.tabScreenStyles.sectionContainer, (data == null ? undefined : data.length) === 0 && {
            marginBottom: 8
          }]
        }), (data == null ? undefined : data.length) === 0 && (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "caption2Bold",
          tx: "flightListingV2.noFlightsForTheDay",
          style: _flightListing.tabScreenStyles.noFlightsText
        })]
      });
    };
    var handleLoadMore = function handleLoadMore() {
      if (isEndLoadMore || isLoading || !canLoadMore) {
        return;
      }
      handleInternetConnection({
        onSuccess: function onSuccess() {
          var _filterRef$current, _filterRef$current2, _filterRef$current3, _filterRef$current4, _filterRef$current5, _filterRef$current$ai, _filterRef$current6, _filterRef$current$ai2, _filterRef$current7;
          getFlyArrivalList({
            direction: _flightProps.FlightDirection.arrival,
            filterDate: (_filterRef$current = filterRef.current) != null && _filterRef$current.date ? (0, _moment.default)((_filterRef$current2 = filterRef.current) == null ? undefined : _filterRef$current2.date) : (0, _moment.default)(),
            filters: (0, _utils.simpleCondition)({
              condition: ((_filterRef$current3 = filterRef.current) == null || (_filterRef$current3 = _filterRef$current3.terminal) == null ? undefined : _filterRef$current3.length) === terminalList.length - 1 || (0, _lodash.isEmpty)((_filterRef$current4 = filterRef.current) == null ? undefined : _filterRef$current4.terminal),
              ifValue: [],
              elseValue: (_filterRef$current5 = filterRef.current) == null ? undefined : _filterRef$current5.terminal
            }),
            isFilter: false,
            isLoadFlightAfter24h: isLoadFlightAfter24h,
            isLoadMore: true,
            filterAirline: (_filterRef$current$ai = (_filterRef$current6 = filterRef.current) == null ? undefined : _filterRef$current6.airline) != null ? _filterRef$current$ai : "",
            filterCityAirport: (_filterRef$current$ai2 = (_filterRef$current7 = filterRef.current) == null ? undefined : _filterRef$current7.airport) != null ? _filterRef$current$ai2 : ""
          });
        },
        onFail: function onFail() {
          setCanLoadMore(false);
        }
      });
    };
    var handleSearchPress = function handleSearchPress() {
      openSearchBottomSheet(_constants.FlightDirection.Arrival);
    };
    var footerListFlight = function footerListFlight() {
      var isEndPage = isEndLoadMore;
      if (isLoading) {
        return (0, _jsxRuntime.jsx)(_loadingIndicator.default, {});
      }
      if (isEndPage && listNotEmpty) {
        return (0, _jsxRuntime.jsx)(_text.Text, {
          tx: "flightLanding.noMoreFlights",
          preset: "caption1Regular",
          style: _flightResultStyles.commonComponentStyles.noFlightListingStyle
        });
      }
      return null;
    };
    var onFacetStateChange = function onFacetStateChange(val) {
      if (val) {
        cancelLoopApiJob();
        willBeRefreshed.value = false;
      } else {
        startLoopApiCall(handleRefresh);
        willBeRefreshed.value = true;
      }
    };
    var onGetEarlierFlights = function onGetEarlierFlights() {
      if (isLoadingEarlierFlights) {
        return;
      }
      handleInternetConnection({
        onSuccess: function onSuccess() {
          var _filterRef$current8, _filterRef$current9, _filterRef$current0, _filterRef$current1, _filterRef$current10, _filterRef$current$ai3, _filterRef$current11, _filterRef$current$ai4, _filterRef$current12;
          getEarlierFlights({
            direction: _flightProps.FlightDirection.arrival,
            filterDate: (_filterRef$current8 = filterRef.current) != null && _filterRef$current8.date ? (0, _moment.default)((_filterRef$current9 = filterRef.current) == null ? undefined : _filterRef$current9.date) : (0, _moment.default)(),
            filters: (0, _utils.simpleCondition)({
              condition: ((_filterRef$current0 = filterRef.current) == null || (_filterRef$current0 = _filterRef$current0.terminal) == null ? undefined : _filterRef$current0.length) === terminalList.length - 1 || (0, _lodash.isEmpty)((_filterRef$current1 = filterRef.current) == null ? undefined : _filterRef$current1.terminal),
              ifValue: [],
              elseValue: (_filterRef$current10 = filterRef.current) == null ? undefined : _filterRef$current10.terminal
            }),
            isFilter: false,
            isLoadFlightAfter24h: isLoadFlightAfter24h,
            filterAirline: (_filterRef$current$ai3 = (_filterRef$current11 = filterRef.current) == null ? undefined : _filterRef$current11.airline) != null ? _filterRef$current$ai3 : "",
            filterCityAirport: (_filterRef$current$ai4 = (_filterRef$current12 = filterRef.current) == null ? undefined : _filterRef$current12.airport) != null ? _filterRef$current$ai4 : ""
          });
        }
      });
    };
    var onRefresh = /*#__PURE__*/function () {
      var _ref10 = (0, _asyncToGenerator2.default)(function* () {
        handleInternetConnection({
          onSuccess: function onSuccess() {
            var _toastForRefresh$curr3;
            toastForRefresh == null || (_toastForRefresh$curr3 = toastForRefresh.current) == null || _toastForRefresh$curr3.closeNow();
            willBeRefreshed.value = true;
            handleRefresh();
          }
        });
      });
      return function onRefresh() {
        return _ref10.apply(this, arguments);
      };
    }();
    var enableLoadMore = function enableLoadMore() {
      setCanLoadMore(true);
    };
    var scrollHandler = (0, _reactNativeReanimated.useAnimatedScrollHandler)({
      onScroll: function () {
        var indexTsx1 = function indexTsx1(event) {
          var contentOffset = event.contentOffset,
            contentSize = event.contentSize,
            layoutMeasurement = event.layoutMeasurement;
          scrollY.value = event.contentOffset.y;
          currentScrollPosition.value = event.contentOffset.y;
          if (event.contentOffset.y <= 20) {
            willBeRefreshed.value = true;
          } else {
            willBeRefreshed.value = false;
          }
          if (layoutMeasurement.height + contentOffset.y <= contentSize.height - 100 && !canLoadMore) {
            (0, _reactNativeReanimated.runOnJS)(enableLoadMore)();
          }
        };
        indexTsx1.__closure = {
          scrollY: scrollY,
          currentScrollPosition: currentScrollPosition,
          willBeRefreshed: willBeRefreshed,
          canLoadMore: canLoadMore,
          runOnJS: _reactNativeReanimated.runOnJS,
          enableLoadMore: enableLoadMore
        };
        indexTsx1.__workletHash = 12638292459899;
        indexTsx1.__initData = _worklet_12638292459899_init_data;
        return indexTsx1;
      }()
    });
    var onRenderListHeader = function onRenderListHeader() {
      return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsx)(_flightListToolbar.default, {
          onGetEarlierFlights: onGetEarlierFlights,
          componentName: COMPONENT_NAME,
          currentFilter: flightListingFilter
        }), isLoadingEarlierFlights && !isEndEarlierFlights && (0, _jsxRuntime.jsx)(_loadingIndicator.default, {}), isEndEarlierFlights && (0, _jsxRuntime.jsx)(_text.Text, {
          tx: "flightLanding.noEarlierFlights",
          preset: "caption2Bold",
          style: _flightListing.tabScreenStyles.noEarlierFlights
        })]
      });
    };
    var renderList = function renderList() {
      if (isNoConnection) {
        return (0, _jsxRuntime.jsx)(_emptyState.EmptyState.NoInternet, {
          containerStyle: _flightListing.tabScreenStyles.errorContainer,
          onPressReload: handleRefresh
        });
      }
      if (isNetworkError) {
        var _ehr44$header, _ehr44$subHeader, _ehr44$buttonLabel;
        return (0, _jsxRuntime.jsx)(_emptyState.EmptyState.General, {
          containerStyle: _flightListing.tabScreenStyles.errorContainer,
          textTitle: (_ehr44$header = ehr44 == null ? undefined : ehr44.header) != null ? _ehr44$header : (0, _i18n.translate)("flightListingV2.currentlyUnavailable"),
          textDescription: (_ehr44$subHeader = ehr44 == null ? undefined : ehr44.subHeader) != null ? _ehr44$subHeader : (0, _i18n.translate)("flightListingV2.responseErrorDescription"),
          textButtonTitle: (_ehr44$buttonLabel = ehr44 == null ? undefined : ehr44.buttonLabel) != null ? _ehr44$buttonLabel : (0, _i18n.translate)("errorOverlay.variant3.retry"),
          callback: handleRefresh,
          icon: ehr44 == null ? undefined : ehr44.icon
        });
      }
      if (isRefreshing) {
        return (0, _jsxRuntime.jsx)(_components.LoadingSkeleton, {});
      }
      if (isShowMaintenance) {
        var _ehr44$header2, _ehr44$subHeader2, _ehr44$buttonLabel2;
        return (0, _jsxRuntime.jsx)(_emptyState.EmptyState.General, {
          containerStyle: _flightListing.tabScreenStyles.errorContainer,
          textTitle: (_ehr44$header2 = ehr44 == null ? undefined : ehr44.header) != null ? _ehr44$header2 : (0, _i18n.translate)("flightListingV2.currentlyUnavailable"),
          textDescription: (_ehr44$subHeader2 = ehr44 == null ? undefined : ehr44.subHeader) != null ? _ehr44$subHeader2 : (0, _i18n.translate)("flightListingV2.responseErrorDescription"),
          textButtonTitle: (_ehr44$buttonLabel2 = ehr44 == null ? undefined : ehr44.buttonLabel) != null ? _ehr44$buttonLabel2 : (0, _i18n.translate)("errorOverlay.variant3.retry"),
          icon: ehr44 == null ? undefined : ehr44.icon,
          callback: fetchTickerbandMaintanance
        });
      }
      return (0, _jsxRuntime.jsx)(_reactNativeGestureHandler.GestureDetector, {
        gesture: _reactNativeGestureHandler.Gesture.Simultaneous(gesture, _reactNativeGestureHandler.Gesture.Native()),
        children: (0, _jsxRuntime.jsx)(AnimatedSectionList, {
          sections: sectionList,
          keyExtractor: function keyExtractor(_item, index) {
            return `${_item == null ? undefined : _item.flightNumber} ${_item == null ? undefined : _item.scheduledDate} ${index.toString()}`;
          }
          // @ts-ignore
          ,
          renderSectionHeader: renderSectionHeader,
          stickySectionHeadersEnabled: false,
          renderItem: onRenderItem,
          ListFooterComponent: footerListFlight,
          showsVerticalScrollIndicator: false,
          scrollEventThrottle: 16,
          onEndReachedThreshold: 0.4,
          onEndReached: handleLoadMore,
          maxToRenderPerBatch: 15,
          style: _flightListing.tabScreenStyles.listStyle,
          contentContainerStyle: _flightListing.tabScreenStyles.listContainer,
          windowSize: 15,
          ref: scrollRef,
          onScroll: scrollHandler
          // refreshControl={<RefreshControl refreshing={false} onRefresh={onRefresh} />}
          ,
          ListHeaderComponent: onRenderListHeader,
          bounces: false,
          scrollEnabled: !isRefreshing
        })
      });
    };
    var startLoopIfNoInternet = /*#__PURE__*/function () {
      var _ref11 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch2.isConnected;
        if (!isConnected) {
          startLoopApiCall(handleRefresh);
        }
      });
      return function startLoopIfNoInternet() {
        return _ref11.apply(this, arguments);
      };
    }();
    (0, _react.useImperativeHandle)(ref, function () {
      return {
        refreshList: function refreshList() {
          onRefresh();
        }
      };
    });
    (0, _react.useEffect)(function () {
      filterRef.current = {
        date: flightListingFilter == null ? undefined : flightListingFilter.date,
        keyword: flightListingFilter == null ? undefined : flightListingFilter.keyword,
        terminal: flightListingFilter == null ? undefined : flightListingFilter.terminal,
        airline: flightListingFilter == null ? undefined : flightListingFilter.airline,
        airport: flightListingFilter == null ? undefined : flightListingFilter.airport
      };
    }, [flightListingFilter]);
    (0, _react.useEffect)(function () {
      if (isFilterModalVisible) {
        cancelLoopApiJob();
        willBeRefreshed.value = false;
      } else {
        startLoopApiCall(handleRefresh);
        willBeRefreshed.value = true;
      }
    }, [isFilterModalVisible]);
    (0, _react.useEffect)(function () {
      if (networkFailedAttempt > 0) {
        var _toastForRefresh$curr4, _toastForRefresh$curr5;
        toastForRefresh == null || (_toastForRefresh$curr4 = toastForRefresh.current) == null || _toastForRefresh$curr4.closeNow();
        toastForRefresh == null || (_toastForRefresh$curr5 = toastForRefresh.current) == null || _toastForRefresh$curr5.show(_constants.TOAST_MESSAGE_DURATION);
      }
    }, [networkFailedAttempt]);
    (0, _react.useEffect)(function () {
      if (selectedTab === _flightProps.FlightDirection.arrival) {
        setPreviousToken('');
        setNetworkError(false);
        startLoopIfNoInternet();
        if (!isAfterLogin.current) {
          onFilter({
            terminal: filterRef.current.terminal,
            airline: filterRef.current.airline,
            airport: filterRef.current.airport
          });
        } else {
          isAfterLogin.current = false;
        }
        isFocusedRef.current = true;
      } else {
        var _categoryRef$current;
        (_categoryRef$current = categoryRef.current) == null || _categoryRef$current.unselectTab();
      }
      return function () {
        isFocusedRef.current = false;
      };
    }, [selectedTab]);
    var gesture = _reactNativeGestureHandler.Gesture.Pan().onBegin(function () {
      var indexTsx5 = function indexTsx5(e) {
        if (currentScrollPosition.value === 0) {
          positionStartRefresh.value = e.y;
        }
      };
      indexTsx5.__closure = {
        currentScrollPosition: currentScrollPosition,
        positionStartRefresh: positionStartRefresh
      };
      indexTsx5.__workletHash = 14955339992050;
      indexTsx5.__initData = _worklet_14955339992050_init_data;
      return indexTsx5;
    }()).onChange(function () {
      var indexTsx4 = function indexTsx4(e) {
        if (currentScrollPosition.value === 0) {
          if (positionStartRefresh.value > 0) {
            if (positionStartRefresh.value + _flightListing2.GESTURE_THRESHOLD <= e.y) {
              contentPosition.value = (0, _reactNativeReanimated.interpolate)(e.translationY, [0, _flightListing2.GESTURE_THRESHOLD * 3], [0, _flightListing2.GESTURE_THRESHOLD], _reactNativeReanimated.Extrapolation.CLAMP);
              sharedRefreshing.value = 1;
            } else {
              sharedRefreshing.value = 0;
            }
          } else {
            positionStartRefresh.value = e.y;
            sharedRefreshing.value = 0;
            contentPosition.value = 0;
          }
        } else {
          sharedRefreshing.value = 0;
          contentPosition.value = 0;
        }
      };
      indexTsx4.__closure = {
        currentScrollPosition: currentScrollPosition,
        positionStartRefresh: positionStartRefresh,
        GESTURE_THRESHOLD: _flightListing2.GESTURE_THRESHOLD,
        contentPosition: contentPosition,
        interpolate: _reactNativeReanimated.interpolate,
        Extrapolation: _reactNativeReanimated.Extrapolation,
        sharedRefreshing: sharedRefreshing
      };
      indexTsx4.__workletHash = 6392193651030;
      indexTsx4.__initData = _worklet_6392193651030_init_data;
      return indexTsx4;
    }()).onEnd(function () {
      var indexTsx3 = function indexTsx3() {
        if (sharedRefreshing.value === 1 && selectedTab === _flightProps.FlightDirection.arrival) {
          contentPosition.value = (0, _reactNativeReanimated.withTiming)(Math.max(contentPosition.value, _flightListing2.GESTURE_THRESHOLD));
          (0, _reactNativeReanimated.runOnJS)(onRefresh)();
        } else {
          contentPosition.value = (0, _reactNativeReanimated.withTiming)(0);
        }
      };
      indexTsx3.__closure = {
        sharedRefreshing: sharedRefreshing,
        selectedTab: selectedTab,
        FlightDirection: _flightProps.FlightDirection,
        contentPosition: contentPosition,
        withTiming: _reactNativeReanimated.withTiming,
        GESTURE_THRESHOLD: _flightListing2.GESTURE_THRESHOLD,
        runOnJS: _reactNativeReanimated.runOnJS,
        onRefresh: onRefresh
      };
      indexTsx3.__workletHash = 13342916963843;
      indexTsx3.__initData = _worklet_13342916963843_init_data;
      return indexTsx3;
    }()).onFinalize(function () {
      var indexTsx2 = function indexTsx2() {
        sharedRefreshing.value = 0;
      };
      indexTsx2.__closure = {
        sharedRefreshing: sharedRefreshing
      };
      indexTsx2.__workletHash = 12537891428510;
      indexTsx2.__initData = _worklet_12537891428510_init_data;
      return indexTsx2;
    }());
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: _flightListing.tabScreenStyles.container,
      children: [(0, _jsxRuntime.jsx)(_category.default, {
        ref: categoryRef,
        componentName: COMPONENT_NAME,
        onSearchPress: handleSearchPress,
        filter: {
          terminal: flightListingFilter.terminal,
          direction: _constants.FlightDirection.Arrival,
          airline: flightListingFilter.airline,
          cityAirport: flightListingFilter.airport
        },
        onFilterFlight: function onFilterFlight(options) {
          dispatch(_flightListingRedux.FlightListingCreators.setFlightListingFilter(Object.assign({}, flightListingFilter, {
            terminal: options.terminal,
            airline: options.airline,
            airport: options.cityAirport
          })));
          onFilter({
            terminal: options.terminal,
            airline: options.airline,
            airport: options.cityAirport
          });
        },
        isShowDirection: false,
        isPaneAbsoluteToRoot: false,
        containerStyle: _flightListing.tabScreenStyles.filterContainer,
        onFacetStateChange: onFacetStateChange,
        disabledCategory: isLoading,
        selectedFlightTab: selectedTab
      }), renderList(), (0, _jsxRuntime.jsx)(_feedbackToast.FeedBackToast, {
        ref: toastForRemoveFlight,
        style: _flightListing.tabScreenStyles.feedBackToastStyle,
        textButtonStyle: _flightListing.tabScreenStyles.toastButtonStyle,
        position: "custom",
        positionValue: {
          bottom: 8
        },
        textStyle: _flightListing.tabScreenStyles.toastTextStyle,
        type: _feedbackToast.FeedBackToastType.smallFeedBack,
        text: (msg50 == null ? undefined : msg50.message) || (0, _i18n.translate)("flyLanding.removeFlight")
      }), (0, _jsxRuntime.jsx)(_feedbackToast.FeedBackToast, {
        ref: toastForRefresh,
        style: _flightListing.tabScreenStyles.feedBackToastStyle,
        textButtonStyle: _flightListing.tabScreenStyles.toastButtonStyle,
        position: "custom",
        textStyle: _flightListing.tabScreenStyles.toastTextStyle,
        type: _feedbackToast.FeedBackToastType.fullWidthFeedBack,
        text: (0, _i18n.translate)("flightLanding.feedBackToastErrorMessage") + lastUpdatedTime
      }), (0, _jsxRuntime.jsx)(_alertApp.AlertApp, {
        ref: alertApp
      }), (0, _jsxRuntime.jsx)(_flightListingSubscription.default, {
        direction: _flightProps.FlightDirection.arrival
      })]
    });
  }
  var _default = exports.default = (0, _react.forwardRef)(ArrivalScreen);
