  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useFlightListingContext = exports.FlightListingContextProvider = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _constants = _$$_REQUIRE(_dependencyMap[3]);
  var _dateTime = _$$_REQUIRE(_dependencyMap[4]);
  var _useModal2 = _$$_REQUIRE(_dependencyMap[5]);
  var _flightDetail = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativeReanimated = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var FlightListingContext = (0, _react.createContext)(undefined);
  var FlightListingContextProvider = exports.FlightListingContextProvider = function FlightListingContextProvider(_ref) {
    var children = _ref.children;
    var _useState = (0, _react.useState)((0, _dateTime.flyModuleUpdatedTime)()),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      lastUpdated = _useState2[0],
      setLastUpdated = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isLoading = _useState4[0],
      setIsLoading = _useState4[1];
    var _useState5 = (0, _react.useState)(true),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isRefreshing = _useState6[0],
      setIsRefreshing = _useState6[1];
    var _useState7 = (0, _react.useState)(_constants.FlightDirection.Arrival),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      selectedTab = _useState8[0],
      setSelectedTab = _useState8[1];
    var searchBottomSheetRef = (0, _react.useRef)(null);
    var _useState9 = (0, _react.useState)(null),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      saveFlightPayload = _useState0[0],
      setSaveFlightPayload = _useState0[1];
    var _useState1 = (0, _react.useState)(null),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      selectedTravelOption = _useState10[0],
      setSelectedTravelOption = _useState10[1];
    var _useModal = (0, _useModal2.useModal)("saveFlightTravelOptionLightListingV2"),
      isModalVisible = _useModal.isModalVisible,
      openModal = _useModal.openModal,
      closeModal = _useModal.closeModal;

    // This ref is used to control whether the search bottom sheet should be shown when FlightListing regains focus.
    var shouldShowSearchBottomSheetOnFocus = (0, _react.useRef)(false);
    var terminal = (0, _react.useRef)(undefined);
    var airline = (0, _react.useRef)(undefined);
    var airport = (0, _react.useRef)(undefined);
    var scrollY = (0, _reactNativeReanimated.useSharedValue)(0);
    var currentScrollPosition = (0, _reactNativeReanimated.useSharedValue)(0);
    var positionStartRefresh = (0, _reactNativeReanimated.useSharedValue)(0); //finger position when start pull to refresh
    var sharedRefreshing = (0, _reactNativeReanimated.useSharedValue)(0);
    var contentPosition = (0, _reactNativeReanimated.useSharedValue)(0);
    var setTerminal = function setTerminal(val) {
      terminal.current = val;
    };
    var setAirline = function setAirline(val) {
      airline.current = val;
    };
    var setAirport = function setAirport(val) {
      airport.current = val;
    };
    var openSearchBottomSheet = function openSearchBottomSheet(direction) {
      var _searchBottomSheetRef2;
      if (!direction) {
        var _searchBottomSheetRef;
        return (_searchBottomSheetRef = searchBottomSheetRef.current) == null ? undefined : _searchBottomSheetRef.open();
      }
      (_searchBottomSheetRef2 = searchBottomSheetRef.current) == null || _searchBottomSheetRef2.openWithDirection(direction);
    };
    var closeSearchBottomSheet = function closeSearchBottomSheet() {
      var _searchBottomSheetRef3;
      (_searchBottomSheetRef3 = searchBottomSheetRef.current) == null || _searchBottomSheetRef3.close();
    };
    var setShouldShowSearchBottomSheetOnFocus = function setShouldShowSearchBottomSheetOnFocus(val) {
      shouldShowSearchBottomSheetOnFocus.current = val;
    };
    var handleSelectTravelOptionAndOpenModal = function handleSelectTravelOptionAndOpenModal(saveFlight) {
      var travelOption = (saveFlight == null ? undefined : saveFlight.direction) === _constants.FlightDirection.Departure ? _flightDetail.TravelOption.iAmTravelling : _flightDetail.TravelOption.iAmPicking;
      setSelectedTravelOption(travelOption);
      setSaveFlightPayload(saveFlight);
      openModal();
    };
    var value = {
      openSearchBottomSheet: openSearchBottomSheet,
      closeSearchBottomSheet: closeSearchBottomSheet,
      searchBottomSheetRef: searchBottomSheetRef,
      shouldShowSearchBottomSheetOnFocus: shouldShowSearchBottomSheetOnFocus,
      setShouldShowSearchBottomSheetOnFocus: setShouldShowSearchBottomSheetOnFocus,
      terminal: terminal,
      setTerminal: setTerminal,
      airline: airline,
      setAirline: setAirline,
      airport: airport,
      setAirport: setAirport,
      lastUpdated: lastUpdated,
      setLastUpdated: setLastUpdated,
      isLoading: isLoading,
      setIsLoading: setIsLoading,
      saveFlightPayload: saveFlightPayload,
      setSaveFlightPayload: setSaveFlightPayload,
      isModalVisible: isModalVisible,
      openModal: openModal,
      closeModal: closeModal,
      selectedTravelOption: selectedTravelOption,
      setSelectedTravelOption: setSelectedTravelOption,
      handleSelectTravelOptionAndOpenModal: handleSelectTravelOptionAndOpenModal,
      scrollY: scrollY,
      isRefreshing: isRefreshing,
      setIsRefreshing: setIsRefreshing,
      selectedTab: selectedTab,
      setSelectedTab: setSelectedTab,
      currentScrollPosition: currentScrollPosition,
      positionStartRefresh: positionStartRefresh,
      sharedRefreshing: sharedRefreshing,
      contentPosition: contentPosition
    };
    return (0, _jsxRuntime.jsx)(FlightListingContext.Provider, {
      value: value,
      children: children
    });
  };
  var useFlightListingContext = exports.useFlightListingContext = function useFlightListingContext() {
    var context = (0, _react.useContext)(FlightListingContext);
    if (context === undefined) {
      throw new Error("useFlightListingContext must be used within a FlightListingContextProvider");
    }
    return context;
  };
