  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.GESTURE_THRESHOLD = exports.FlightListing = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _materialTopTabs = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _native = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _i18n = _$$_REQUIRE(_dependencyMap[6]);
  var _flightListingRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _searchRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _theme = _$$_REQUIRE(_dependencyMap[9]);
  var _icons = _$$_REQUIRE(_dependencyMap[10]);
  var _lodash = _$$_REQUIRE(_dependencyMap[11]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[12]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[13]);
  var _reactNativeGestureHandler = _$$_REQUIRE(_dependencyMap[14]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[15]));
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[16]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[17]);
  var _arrival = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[18]));
  var _flyListingTab = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[19]));
  var _saveFlight = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[20]));
  var _searchBottomSheet = _$$_REQUIRE(_dependencyMap[21]);
  var _flightListingContext = _$$_REQUIRE(_dependencyMap[22]);
  var _departure = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[23]));
  var _flightListing = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[24]));
  var _useTickerbandMaintanence = _$$_REQUIRE(_dependencyMap[25]);
  var _tickerBand = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[26]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[27]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var GESTURE_THRESHOLD = exports.GESTURE_THRESHOLD = 90;
  var MARGIN_TOP = _reactNative2.Platform.select({
    ios: 0,
    android: 20
  });
  var Tab = (0, _materialTopTabs.createMaterialTopTabNavigator)();
  var SCREEN_NAME = "FlightListing";
  var _worklet_12314692973864_init_data = {
    code: "function flightListingTsx1(){const{titleHeight,interpolate,scrollY,MARGIN_TOP,isShowTickerband,topInset,contentPosition,GESTURE_THRESHOLD,withSpring}=this.__closure;const clampMarginTop=Math.min(Math.max(titleHeight,60),120);const marginTop=interpolate(scrollY.value,[0,clampMarginTop],[clampMarginTop+MARGIN_TOP+(isShowTickerband?topInset:0),MARGIN_TOP+(isShowTickerband?topInset:0)],\"clamp\");const translateY=interpolate(contentPosition.value,[0,GESTURE_THRESHOLD],[0,60]);const springMarginTop=withSpring(marginTop,{damping:15,stiffness:50});return{marginTop:springMarginTop,transform:[{translateY:translateY}]};}"
  };
  var _worklet_33141848212_init_data = {
    code: "function flightListingTsx2(){const{interpolate,scrollY,titleHeight}=this.__closure;const opacity=interpolate(scrollY.value,[0,titleHeight],[1,0],\"clamp\");return{opacity:opacity};}"
  };
  var _worklet_3503588301642_init_data = {
    code: "function flightListingTsx3(){const{interpolate,contentPosition,GESTURE_THRESHOLD}=this.__closure;const polateOpacity=interpolate(contentPosition.value,[0,GESTURE_THRESHOLD],[0,1]);return{opacity:polateOpacity};}"
  };
  var FlightListingContent = function FlightListingContent(_ref) {
    var _route$params;
    var navigation = _ref.navigation;
    var route = (0, _native.useRoute)();
    var initialRouteName = (_route$params = route.params) == null ? undefined : _route$params.screen;
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useSafeAreaInsets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)(),
      top = _useSafeAreaInsets.top;
    var _useTickerbandMaintan = (0, _useTickerbandMaintanence.useTickerbandMaintanance)(_useTickerbandMaintanence.MaintananceTickerBandType.FLY_LISTING),
      isShowTickerband = _useTickerbandMaintan.isShowTickerband,
      tickerBand = _useTickerbandMaintan.tickerBand,
      tickerBandDescription = _useTickerbandMaintan.tickerBandDescription,
      tickerBandButtonText = _useTickerbandMaintan.tickerBandButtonText,
      onPressCTA = _useTickerbandMaintan.onPressCTA,
      onCloseTickerBand = _useTickerbandMaintan.onCloseTickerBand;
    var _useFlightListingCont = (0, _flightListingContext.useFlightListingContext)(),
      searchBottomSheetRef = _useFlightListingCont.searchBottomSheetRef,
      shouldShowSearchBottomSheetOnFocus = _useFlightListingCont.shouldShowSearchBottomSheetOnFocus,
      isRefreshing = _useFlightListingCont.isRefreshing,
      lastUpdated = _useFlightListingCont.lastUpdated,
      scrollY = _useFlightListingCont.scrollY,
      setSelectedTab = _useFlightListingCont.setSelectedTab,
      contentPosition = _useFlightListingCont.contentPosition;
    var _useState = (0, _react.useState)(0),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      titleHeight = _useState2[0],
      setTitleHeight = _useState2[1];
    var arrivalListRef = (0, _react.useRef)(null);
    var departureListRef = (0, _react.useRef)(null);
    var topInset = (0, _react.useMemo)(function () {
      return isShowTickerband ? 24 : top;
    }, [isShowTickerband, top]);
    var containerAnimatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var flightListingTsx1 = function flightListingTsx1() {
        var clampMarginTop = Math.min(Math.max(titleHeight, 60), 120);
        var marginTop = (0, _reactNativeReanimated.interpolate)(scrollY.value, [0, clampMarginTop], [clampMarginTop + MARGIN_TOP + (isShowTickerband ? topInset : 0), MARGIN_TOP + (isShowTickerband ? topInset : 0)], "clamp");
        var translateY = (0, _reactNativeReanimated.interpolate)(contentPosition.value, [0, GESTURE_THRESHOLD], [0, 60]);
        var springMarginTop = (0, _reactNativeReanimated.withSpring)(marginTop, {
          damping: 15,
          stiffness: 50
        });
        return {
          marginTop: springMarginTop,
          transform: [{
            translateY: translateY
          }]
        };
      };
      flightListingTsx1.__closure = {
        titleHeight: titleHeight,
        interpolate: _reactNativeReanimated.interpolate,
        scrollY: scrollY,
        MARGIN_TOP: MARGIN_TOP,
        isShowTickerband: isShowTickerband,
        topInset: topInset,
        contentPosition: contentPosition,
        GESTURE_THRESHOLD: GESTURE_THRESHOLD,
        withSpring: _reactNativeReanimated.withSpring
      };
      flightListingTsx1.__workletHash = 12314692973864;
      flightListingTsx1.__initData = _worklet_12314692973864_init_data;
      return flightListingTsx1;
    }());
    var headerAnimatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var flightListingTsx2 = function flightListingTsx2() {
        var opacity = (0, _reactNativeReanimated.interpolate)(scrollY.value, [0, titleHeight], [1, 0], "clamp");
        return {
          opacity: opacity
        };
      };
      flightListingTsx2.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        scrollY: scrollY,
        titleHeight: titleHeight
      };
      flightListingTsx2.__workletHash = 33141848212;
      flightListingTsx2.__initData = _worklet_33141848212_init_data;
      return flightListingTsx2;
    }());
    var backButtonStyle = (0, _react.useMemo)(function () {
      return {
        flexDirection: "row",
        alignItems: "center",
        position: "absolute",
        zIndex: 999,
        top: topInset + MARGIN_TOP,
        height: 32,
        left: 16
      };
    }, [topInset]);
    var headerStyle = (0, _react.useMemo)(function () {
      return {
        position: "absolute",
        alignSelf: "center",
        top: topInset + MARGIN_TOP,
        width: "100%"
      };
    }, [topInset, headerAnimatedStyle]);
    var refreshIndicatorAnimatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var flightListingTsx3 = function flightListingTsx3() {
        var polateOpacity = (0, _reactNativeReanimated.interpolate)(contentPosition.value, [0, GESTURE_THRESHOLD], [0, 1]);
        return {
          opacity: polateOpacity
        };
      };
      flightListingTsx3.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        contentPosition: contentPosition,
        GESTURE_THRESHOLD: GESTURE_THRESHOLD
      };
      flightListingTsx3.__workletHash = 3503588301642;
      flightListingTsx3.__initData = _worklet_3503588301642_init_data;
      return flightListingTsx3;
    }());
    var onLayoutHeader = function onLayoutHeader(e) {
      setTitleHeight(e.nativeEvent.layout.height);
    };
    (0, _native.useFocusEffect)(function () {
      // We hide the search bottom sheet when the user navigates away from the flight listing screen.
      // When the user returns to the flight listing screen, we show the search bottom sheet if the user had previously opened it.
      if (shouldShowSearchBottomSheetOnFocus.current) {
        var _searchBottomSheetRef;
        (_searchBottomSheetRef = searchBottomSheetRef.current) == null || _searchBottomSheetRef.open();
        shouldShowSearchBottomSheetOnFocus.current = false;
      }
    });
    (0, _react.useEffect)(function () {
      dispatch(_searchRedux.default.resetSearchKeywordCollection());
      dispatch(_searchRedux.default.popularSearchKeywordRequest());
      setSelectedTab(initialRouteName);
      return function () {
        dispatch(_searchRedux.default.setSearchKeyword(""));
        dispatch(_flightListingRedux.FlightListingCreators.setFlightListingFilter({
          direction: initialRouteName
        }));
      };
    }, []);
    (0, _react.useEffect)(function () {
      if (contentPosition.value && !isRefreshing) {
        _reactNative2.InteractionManager.runAfterInteractions(function () {
          contentPosition.value = (0, _reactNativeReanimated.withTiming)(0);
        });
      }
    }, [isRefreshing]);
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [isShowTickerband && (0, _jsxRuntime.jsx)(_tickerBand.default, {
        urgent: false,
        title: tickerBand,
        description: tickerBandDescription,
        buttonText: tickerBandButtonText,
        onCTAPress: onPressCTA,
        onClose: onCloseTickerBand,
        isLanding: false,
        tickerStyle: {
          paddingTop: topInset + 6,
          paddingBottom: 38
        }
      }), (0, _jsxRuntime.jsx)(_reactNativeGestureHandler.GestureHandlerRootView, {
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _flightListing.default.container,
          testID: `${SCREEN_NAME}__FlightListingScreen`,
          accessibilityLabel: `${SCREEN_NAME}__FlightListingScreen`,
          children: [(0, _jsxRuntime.jsx)(_reactNative2.StatusBar, {
            translucent: true,
            backgroundColor: "transparent"
          }), (0, _jsxRuntime.jsx)(_reactNative2.Image, {
            source: _$$_REQUIRE(_dependencyMap[28]),
            style: _flightListing.default.fixedBackground,
            resizeMode: "cover"
          }), (0, _jsxRuntime.jsxs)(_reactNativeSafeAreaContext.SafeAreaView, {
            style: _flightListing.default.headerSafeArea,
            edges: !isShowTickerband ? ["top", "left", "right"] : ["left", "right"],
            children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              style: backButtonStyle,
              onPress: function onPress() {
                return navigation.goBack();
              },
              children: (0, _jsxRuntime.jsx)(_icons.ArrowLeftV2, {
                color: _theme.color.palette.whiteGrey
              })
            }), (0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
              style: [headerStyle, headerAnimatedStyle],
              onLayout: onLayoutHeader,
              children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: _flightListing.default.headerRow,
                children: (0, _jsxRuntime.jsx)(_text.Text, {
                  style: _flightListing.default.headerTitle,
                  tx: "flightLanding.flightInformation"
                })
              }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: _flightListing.default.realtimeCard,
                children: [(0, _jsxRuntime.jsx)(_text.Text, {
                  style: [_flightListing.default.liveText, {
                    opacity: !isRefreshing ? 1 : 0
                  }],
                  text: (0, _lodash.isEmpty)(lastUpdated) ? "-" : (0, _i18n.translate)("flightListingV2.lastUpdated") + " " + lastUpdated
                }), (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
                  style: [_flightListing.default.loadingIndicatorContainer, refreshIndicatorAnimatedStyle],
                  children: (0, _jsxRuntime.jsx)(_reactNative2.ActivityIndicator, {
                    style: _flightListing.default.loadingIndicator,
                    size: "small",
                    color: _theme.color.palette.whiteGrey
                  })
                })]
              })]
            }), (0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
              style: [_flightListing.default.flightContainer, containerAnimatedStyle],
              children: [(0, _jsxRuntime.jsxs)(Tab.Navigator, {
                initialRouteName: initialRouteName,
                tabBar: function tabBar(props) {
                  return (0, _jsxRuntime.jsx)(_flyListingTab.default, Object.assign({}, props));
                },
                screenOptions: {
                  swipeEnabled: false,
                  sceneStyle: {
                    backgroundColor: "transparent",
                    borderTopLeftRadius: 16,
                    borderTopRightRadius: 16
                  },
                  animationEnabled: false
                },
                children: [(0, _jsxRuntime.jsx)(Tab.Screen, {
                  name: "ARR",
                  options: {
                    tabBarLabel: (0, _i18n.translate)("flightLanding.arrivalTabTitle"),
                    tabBarTestID: `${SCREEN_NAME}_arrivalTab`,
                    tabBarAccessibilityLabel: `${SCREEN_NAME}_arrivalTab`,
                    lazy: true
                  },
                  children: function children(props) {
                    return (0, _jsxRuntime.jsx)(_arrival.default, Object.assign({}, props, {
                      ref: arrivalListRef
                    }));
                  }
                }), (0, _jsxRuntime.jsx)(Tab.Screen, {
                  name: "DEP",
                  options: {
                    tabBarLabel: (0, _i18n.translate)("flightLanding.departureTabTitle"),
                    tabBarTestID: `${SCREEN_NAME}_departureTab`,
                    tabBarAccessibilityLabel: `${SCREEN_NAME}_departureTab`,
                    lazy: true
                  },
                  children: function children(props) {
                    return (0, _jsxRuntime.jsx)(_departure.default, Object.assign({}, props, {
                      ref: departureListRef
                    }));
                  }
                })]
              }), (0, _jsxRuntime.jsx)(_saveFlight.default, {
                route: route
              })]
            })]
          }), (0, _jsxRuntime.jsx)(_searchBottomSheet.SearchBottomSheet, {
            ref: searchBottomSheetRef
          })]
        })
      })]
    });
  };
  var FlightListing = exports.FlightListing = function FlightListing(_ref2) {
    var navigation = _ref2.navigation;
    return (0, _jsxRuntime.jsx)(_flightListingContext.FlightListingContextProvider, {
      children: (0, _jsxRuntime.jsx)(FlightListingContent, {
        navigation: navigation
      })
    });
  };
