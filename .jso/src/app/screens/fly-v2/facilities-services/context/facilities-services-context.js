  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useFacilitiesServicesContext = exports.initialFilterState = exports.FacilitiesServicesProvider = exports.FacilitiesServicesContext = exports.AVAILABLE_LOCATIONS = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var FacilitiesServicesContext = exports.FacilitiesServicesContext = (0, _react.createContext)(null);
  var AVAILABLE_LOCATIONS = exports.AVAILABLE_LOCATIONS = ['Jewel', 'T1', 'T2', 'T3', 'T4'];
  var initialFilterState = exports.initialFilterState = {
    locations: [],
    publicArea: false,
    transitArea: false
  };
  var FacilitiesServicesProvider = exports.FacilitiesServicesProvider = function FacilitiesServicesProvider(_ref) {
    var children = _ref.children;
    var _useState = (0, _react.useState)(initialFilterState),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      state = _useState2[0],
      setState = _useState2[1];
    var _useState3 = (0, _react.useState)(new Set()),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 1),
      scrollListeners = _useState4[0];
    var toggleLocation = function toggleLocation(location) {
      setState(function (prev) {
        return Object.assign({}, prev, {
          locations: prev.locations.includes(location) ? prev.locations.filter(function (loc) {
            return loc !== location;
          }) : [].concat((0, _toConsumableArray2.default)(prev.locations), [location])
        });
      });
    };
    var setLocations = function setLocations(locations) {
      setState(function (prev) {
        return Object.assign({}, prev, {
          locations: locations
        });
      });
    };
    var togglePublicArea = function togglePublicArea() {
      setState(function (prev) {
        return Object.assign({}, prev, {
          publicArea: !prev.publicArea
        });
      });
    };
    var toggleTransitArea = function toggleTransitArea() {
      setState(function (prev) {
        return Object.assign({}, prev, {
          transitArea: !prev.transitArea
        });
      });
    };
    var resetFilters = function resetFilters() {
      setState(initialFilterState);
    };
    var onFiltersApplied = function onFiltersApplied(callback) {
      scrollListeners.add(callback);
      return function () {
        scrollListeners.delete(callback);
      };
    };
    var emitFiltersApplied = function emitFiltersApplied() {
      scrollListeners.forEach(function (listener) {
        return listener();
      });
    };
    var contextValue = (0, _react.useMemo)(function () {
      return Object.assign({}, state, {
        setLocations: setLocations,
        toggleLocation: toggleLocation,
        togglePublicArea: togglePublicArea,
        toggleTransitArea: toggleTransitArea,
        resetFilters: resetFilters,
        onFiltersApplied: onFiltersApplied,
        emitFiltersApplied: emitFiltersApplied
      });
    }, [state, setLocations, toggleLocation, togglePublicArea, toggleTransitArea, resetFilters, onFiltersApplied, emitFiltersApplied]);
    return (0, _jsxRuntime.jsx)(FacilitiesServicesContext.Provider, {
      value: contextValue,
      children: children
    });
  };
  var useFacilitiesServicesContext = exports.useFacilitiesServicesContext = function useFacilitiesServicesContext() {
    var context = (0, _react.useContext)(FacilitiesServicesContext);
    if (!context) {
      throw new Error('useFacilitiesServicesContext must be used within a FacilitiesServicesProvider');
    }
    return context;
  };
