  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.mapApiItemToFacilityServiceItem = undefined;
  var _envParams = _$$_REQUIRE(_dependencyMap[0]);
  var mapLocationTag = function mapLocationTag(tagTitle) {
    if (!tagTitle) return null;
    var lower = tagTitle.trim().toLowerCase();
    if (lower === "all") return null;
    if (lower === "terminal 1") return "T1";
    if (lower === "terminal 2") return "T2";
    if (lower === "terminal 3") return "T3";
    if (lower === "terminal 4") return "T4";
    if (lower === "jewel") return "Jewel";
    return null;
  };
  var mapApiItemToFacilityServiceItem = exports.mapApiItemToFacilityServiceItem = function mapApiItemToFacilityServiceItem(item) {
    var _item$location, _item$area, _env;
    // Extract and normalize locations
    var locations = (_item$location = item.location) == null ? undefined : _item$location.map(function (loc) {
      return mapLocationTag(loc.tagTitle);
    }).filter(function (loc) {
      return loc !== null;
    });

    // Extract and normalize areas
    var areas = ((_item$area = item.area) == null ? undefined : _item$area.map(function (a) {
      return a.tagTitle.toLowerCase();
    })) || [];
    var isPublicArea = areas.some(function (area) {
      return area.includes("public");
    });
    var isTransitArea = areas.some(function (area) {
      return area.includes("transit");
    });
    return {
      id: item.contentId,
      title: item.title,
      locations: locations || [],
      locationDisplayText: locations == null ? undefined : locations.join(", "),
      isPublicArea: isPublicArea,
      isTransitArea: isTransitArea,
      image: item.image ? ((_env = (0, _envParams.env)()) == null ? undefined : _env.AEM_URL) + item.image : undefined,
      navigation: item.navigation ? {
        type: item.navigation.type,
        value: item.navigation.value
      } : undefined
    };
  };
