  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useFacilitiesServices = useFacilitiesServices;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _$$_REQUIRE(_dependencyMap[4]);
  var _apis = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _request = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _envParams = _$$_REQUIRE(_dependencyMap[7]);
  var _dataMapping = _$$_REQUIRE(_dependencyMap[8]);
  var _facilitiesServicesContext = _$$_REQUIRE(_dependencyMap[9]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[10]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  function useFacilitiesServices() {
    var _useState = (0, _react.useState)([]),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      allFacilities = _useState2[0],
      setAllFacilities = _useState2[1];
    var _useState3 = (0, _react.useState)(true),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      loading = _useState4[0],
      setLoading = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      refreshing = _useState6[0],
      setRefreshing = _useState6[1];
    var _useState7 = (0, _react.useState)(false),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      hasApiError = _useState8[0],
      setHasApiError = _useState8[1];
    var _useFacilitiesService = (0, _facilitiesServicesContext.useFacilitiesServicesContext)(),
      locations = _useFacilitiesService.locations,
      publicArea = _useFacilitiesService.publicArea,
      transitArea = _useFacilitiesService.transitArea;
    var fetchFacilities = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* () {
        setHasApiError(false);
        try {
          var _env, _response$data, _response$data2;
          var paramsArray = _apis.default.getFacilitiesServices.split(" ");
          var method = paramsArray[0] || "GET";
          var url = ((_env = (0, _envParams.env)()) == null ? undefined : _env.AEM_URL) + paramsArray[1];
          var response = yield (0, _request.default)({
            url: url,
            method: method
          });
          var items = Array.isArray(response == null || (_response$data = response.data) == null ? undefined : _response$data.list) ? response == null || (_response$data2 = response.data) == null ? undefined : _response$data2.list : [];
          setAllFacilities(items.map(_dataMapping.mapApiItemToFacilityServiceItem));
        } catch (err) {
          setHasApiError(true);
        }
      });
      return function fetchFacilities() {
        return _ref.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      var initialFetch = /*#__PURE__*/function () {
        var _ref2 = (0, _asyncToGenerator2.default)(function* () {
          setLoading(true);
          yield fetchFacilities();
          setLoading(false);
        });
        return function initialFetch() {
          return _ref2.apply(this, arguments);
        };
      }();
      _reactNative.InteractionManager.runAfterInteractions(function () {
        initialFetch();
      });
    }, []);

    // Filter facilities based on selected filters
    var facilities = (0, _react.useMemo)(function () {
      return allFacilities.filter(function (facility) {
        // Filter by location
        if (locations.length > 0 && locations.length !== 5) {
          var hasSelectedLocation = facility.locations.some(function (loc) {
            return locations.includes(loc);
          });
          if (!hasSelectedLocation) return false;
        }
        // Filter by area type
        if (!publicArea && !transitArea) return true; // both are not selected, show all
        if (publicArea && transitArea) return true; // both are selected, show all
        if (publicArea) return facility.isPublicArea; // only public area selected
        if (transitArea) return facility.isTransitArea; // only transit area selected

        return true;
      });
    }, [allFacilities, locations, publicArea, transitArea]);
    var reload = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* () {
        setLoading(true);
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        if (!isConnected) return setLoading(false);
        yield fetchFacilities();
        setLoading(false);
      });
      return function reload() {
        return _ref3.apply(this, arguments);
      };
    }();
    var refresh = /*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* () {
        setRefreshing(true);
        var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch2.isConnected;
        if (!isConnected) return setRefreshing(false);
        yield Promise.all([fetchFacilities(),
        // Wait for at least 1 second to show loading indicator. Without this, the fetching might be too fast and the loading indicator will not be shown.
        new Promise(function (resolve) {
          return setTimeout(resolve, 1000);
        })]);
        setRefreshing(false);
      });
      return function refresh() {
        return _ref4.apply(this, arguments);
      };
    }();
    // Group facilities by alphabet
    var groupedFacilities = (0, _react.useMemo)(function () {
      // Sort facilities by title
      var sorted = (0, _toConsumableArray2.default)(facilities).sort(function (a, b) {
        return a.title.localeCompare(b.title, undefined, {
          sensitivity: 'base'
        });
      });

      // Group by first letter
      return sorted.reduce(function (acc, facility) {
        var firstChar = facility.title.charAt(0).toUpperCase();
        // Check if the character is a letter A-Z
        var isLetter = /[A-Z]/.test(firstChar);
        var groupKey = isLetter ? firstChar : '#';
        if (!acc[groupKey]) {
          acc[groupKey] = [];
        }
        acc[groupKey].push(facility);
        return acc;
      }, {});
    }, [facilities]);

    // Get active letters
    var activeLetters = (0, _react.useMemo)(function () {
      var letters = Object.keys(groupedFacilities);
      // Filter out '#' first, then sort alphabetically, then add '#' at the beginning if it exists
      var alphabetLetters = letters.filter(function (l) {
        return l !== '#';
      }).sort();
      var orderedLetters = groupedFacilities['#'] ? ['#'].concat((0, _toConsumableArray2.default)(alphabetLetters)) : alphabetLetters;
      return new Set(orderedLetters);
    }, [groupedFacilities]);
    return {
      facilities: facilities,
      loading: loading,
      reload: reload,
      hasApiError: hasApiError,
      refreshing: refreshing,
      refresh: refresh,
      groupedFacilities: groupedFacilities,
      activeLetters: activeLetters
    };
  }
