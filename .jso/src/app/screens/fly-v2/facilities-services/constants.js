  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.SCREEN_WIDTH = exports.SCALE_FACTOR = exports.HEADER_TOP = exports.HEADER_HEIGHT = exports.HEADER_BOTTOM = exports.HEADER_BACKGROUND_WIDTH = exports.HEADER_BACKGROUND_HEIGHT_SCALED = exports.HEADER_BACKGROUND_HEIGHT = exports.FILTER_BAR_TOP = exports.FILTER_BAR_HEIGHT = exports.FILTER_BAR_BOTTOM = exports.CONTENT_TOP = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var SCREEN_WIDTH = exports.SCREEN_WIDTH = _reactNative.Dimensions.get("window").width;
  var HEADER_BACKGROUND_WIDTH = exports.HEADER_BACKGROUND_WIDTH = 375;
  var HEADER_BACKGROUND_HEIGHT = exports.HEADER_BACKGROUND_HEIGHT = 245;
  var SCALE_FACTOR = exports.SCALE_FACTOR = SCREEN_WIDTH / HEADER_BACKGROUND_WIDTH;
  var HEADER_BACKGROUND_HEIGHT_SCALED = exports.HEADER_BACKGROUND_HEIGHT_SCALED = HEADER_BACKGROUND_HEIGHT * SCALE_FACTOR;
  var HEADER_TOP = exports.HEADER_TOP = 0;
  var HEADER_HEIGHT = exports.HEADER_HEIGHT = 100;
  var HEADER_BOTTOM = exports.HEADER_BOTTOM = HEADER_TOP + HEADER_HEIGHT;
  var FILTER_BAR_TOP = exports.FILTER_BAR_TOP = HEADER_BOTTOM + 15;
  var FILTER_BAR_HEIGHT = exports.FILTER_BAR_HEIGHT = 70;
  var FILTER_BAR_BOTTOM = exports.FILTER_BAR_BOTTOM = FILTER_BAR_TOP + FILTER_BAR_HEIGHT;
  var CONTENT_TOP = exports.CONTENT_TOP = FILTER_BAR_BOTTOM + 16;
