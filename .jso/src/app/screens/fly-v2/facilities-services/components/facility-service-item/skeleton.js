  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.FacilityServiceItemSkeleton = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _constants = _$$_REQUIRE(_dependencyMap[5]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  var FacilityServiceItemSkeleton = exports.FacilityServiceItemSkeleton = function FacilityServiceItemSkeleton() {
    var shimmerColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: shimmerColors,
        shimmerStyle: styles.imagePlaceholder
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.content,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: shimmerColors,
          shimmerStyle: styles.placeholder1
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: shimmerColors,
          shimmerStyle: styles.placeholder2
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: shimmerColors,
          shimmerStyle: styles.placeholder3
        })]
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    container: {
      flexDirection: "row",
      alignItems: "flex-start",
      paddingLeft: 20,
      paddingRight: 40,
      height: 84,
      gap: 16
    },
    imagePlaceholder: {
      width: 48,
      height: 48,
      borderRadius: 8,
      backgroundColor: _theme.color.palette.lighterGrey
    },
    content: {
      flex: 1,
      alignSelf: "stretch",
      borderBottomWidth: 1,
      borderBottomColor: _theme.color.palette.lighterGrey
    },
    placeholder1: {
      width: "100%",
      height: 12,
      borderRadius: 4,
      backgroundColor: _theme.color.palette.lighterGrey,
      marginBottom: 12
    },
    placeholder2: {
      width: "50%",
      height: 12,
      borderRadius: 4,
      backgroundColor: _theme.color.palette.lighterGrey,
      marginBottom: 12
    },
    placeholder3: {
      width: "30%",
      height: 12,
      borderRadius: 4,
      backgroundColor: _theme.color.palette.lighterGrey
    }
  });
