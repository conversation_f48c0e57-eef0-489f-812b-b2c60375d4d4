  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.FacilityServiceItem = undefined;
  Object.defineProperty(exports, "FacilityServiceItemSkeleton", {
    enumerable: true,
    get: function get() {
      return _skeleton.FacilityServiceItemSkeleton;
    }
  });
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _constants = _$$_REQUIRE(_dependencyMap[6]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[7]);
  var _itemImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  var _skeleton = _$$_REQUIRE(_dependencyMap[10]);
  // TODO: Consider moving this to a shared component

  var getAreaString = function getAreaString(isPublicArea, isTransitArea) {
    var areas = [];
    if (isPublicArea) areas.push("Public");
    if (isTransitArea) areas.push("Transit");
    return areas.length ? areas.join(", ") : "-";
  };
  var FacilityServiceItem = exports.FacilityServiceItem = function FacilityServiceItem(_ref) {
    var _item$title;
    var item = _ref.item;
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("FLIGHT_LANDING_V2_FACILITIES_SERVICES"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var hasLocation = !!(item != null && item.locationDisplayText);
    var hasArea = item.isPublicArea || item.isTransitArea;
    var showDot = hasLocation && hasArea;
    var handlePress = function handlePress() {
      var _item$navigation, _item$navigation2;
      var type = item == null || (_item$navigation = item.navigation) == null ? undefined : _item$navigation.type;
      var value = item == null || (_item$navigation2 = item.navigation) == null ? undefined : _item$navigation2.value;
      if (type && value) {
        handleNavigation(type, value, item == null ? undefined : item.redirect);
      }
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      style: styles.container,
      onPress: handlePress,
      children: [(0, _jsxRuntime.jsx)(_itemImage.default, {
        defaultSource: _$$_REQUIRE(_dependencyMap[11]),
        style: styles.image,
        source: {
          uri: item == null ? undefined : item.image
        }
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.content,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.textColumn,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.title,
            numberOfLines: 2,
            textBreakStrategy: "highQuality",
            children: (_item$title = item == null ? undefined : item.title) != null ? _item$title : "-"
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.locationAreaRow,
            children: [hasLocation && (0, _jsxRuntime.jsx)(_text.Text, {
              preset: "caption1Bold",
              style: styles.location,
              children: item.locationDisplayText
            }), showDot && (0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.dot,
              children: (0, _constants.getDotUnicode)()
            }), hasArea && (0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.area,
              preset: "caption1Regular",
              children: getAreaString(item.isPublicArea, item.isTransitArea)
            })]
          })]
        })
      })]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    container: {
      flexDirection: "row",
      alignItems: "flex-start",
      paddingLeft: 20,
      paddingRight: 40,
      gap: 16
    },
    image: {
      width: 48,
      height: 48,
      borderRadius: 8
    },
    content: {
      flex: 1,
      alignItems: "flex-start",
      borderBottomWidth: 1,
      borderBottomColor: _theme.color.palette.lighterGrey,
      minHeight: 66,
      paddingBottom: 24
    },
    textColumn: {
      flex: 1
    },
    title: Object.assign({}, _text.newPresets.bodyTextBlackBold, {
      color: _theme.color.palette.almostBlackGrey,
      marginBottom: 4,
      fontWeight: "900"
    }),
    locationAreaRow: {
      flexDirection: "row",
      flexWrap: "wrap"
    },
    location: {
      color: _theme.color.palette.almostBlackGrey,
      textAlign: "left"
    },
    area: {
      color: _theme.color.palette.darkestGrey,
      textAlign: "left"
    },
    dot: Object.assign({}, _text.newPresets.caption1Regular, {
      color: _theme.color.palette.darkestGrey,
      marginHorizontal: 4
    })
  });
