  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.FilterPill = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  var Select = function Select(_ref) {
    var label = _ref.label,
      _ref$active = _ref.active,
      active = _ref$active === undefined ? false : _ref$active,
      onPress = _ref.onPress,
      icon = _ref.icon,
      testID = _ref.testID;
    return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      style: [styles.selectContainer, active && styles.selectContainerActive],
      onPress: onPress,
      testID: testID,
      accessibilityLabel: testID,
      children: [icon && _react.default.cloneElement(icon, {
        width: 16,
        height: 16,
        color: active ? _theme.color.palette.lightPurple : _theme.color.palette.darkestGrey
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        style: [styles.text, active && styles.textActive],
        children: label
      }), (0, _jsxRuntime.jsx)(_icons.ArrowDown, {
        width: 16,
        height: 16,
        color: active ? _theme.color.palette.lightPurple : _theme.color.palette.darkestGrey
      })]
    });
  };
  var Toggle = function Toggle(_ref2) {
    var label = _ref2.label,
      _ref2$active = _ref2.active,
      active = _ref2$active === undefined ? false : _ref2$active,
      onPress = _ref2.onPress,
      icon = _ref2.icon,
      testID = _ref2.testID;
    return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      style: [styles.toggleContainer, active && styles.toggleContainerActive],
      onPress: onPress,
      testID: testID,
      accessibilityLabel: testID,
      children: [icon && (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: {
          marginRight: 3
        },
        children: _react.default.cloneElement(icon, {
          width: 16,
          height: 16,
          color: active ? _theme.color.palette.lightPurple : _theme.color.palette.darkestGrey
        })
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        style: [styles.text, active && styles.textActive],
        children: label
      }), active && (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: {
          marginLeft: 2
        },
        children: (0, _jsxRuntime.jsx)(_icons.CloseCross, {
          width: 16,
          height: 16,
          color: _theme.color.palette.lightPurple,
          opacity: 0.5
        })
      })]
    });
  };
  var FilterPill = exports.FilterPill = {
    Select: Select,
    Toggle: Toggle
  };
  var styles = _reactNative2.StyleSheet.create({
    selectContainer: {
      flexDirection: "row",
      alignItems: "center",
      height: 30,
      paddingHorizontal: 10,
      borderRadius: 99,
      borderWidth: 1,
      gap: 2,
      borderColor: _theme.color.palette.lighterGrey,
      backgroundColor: _theme.color.palette.whiteGrey
    },
    toggleContainer: {
      flexDirection: "row",
      alignItems: "center",
      height: 30,
      paddingHorizontal: 9,
      borderRadius: 99,
      borderWidth: 1,
      borderColor: _theme.color.palette.lighterGrey,
      backgroundColor: _theme.color.palette.whiteGrey
    },
    selectContainerActive: {
      backgroundColor: _theme.color.palette.lightestPurple,
      borderColor: _theme.color.palette.purpleD5BBEA
    },
    toggleContainerActive: {
      backgroundColor: _theme.color.palette.lightestPurple,
      borderColor: _theme.color.palette.purpleD5BBEA,
      paddingRight: 5
    },
    text: Object.assign({}, _text.newPresets.caption1Bold, {
      color: _theme.color.palette.darkestGrey,
      fontSize: 14
    }),
    textActive: {
      color: _theme.color.palette.lightPurple
    }
  });
