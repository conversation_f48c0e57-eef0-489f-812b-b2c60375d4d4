  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.FilterBar = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeReanimated = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _color = _$$_REQUIRE(_dependencyMap[5]);
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _filterPill = _$$_REQUIRE(_dependencyMap[7]);
  var _filterSheet = _$$_REQUIRE(_dependencyMap[8]);
  var _facilitiesServicesContext = _$$_REQUIRE(_dependencyMap[9]);
  var _i18n = _$$_REQUIRE(_dependencyMap[10]);
  var _useModal2 = _$$_REQUIRE(_dependencyMap[11]);
  var _constants = _$$_REQUIRE(_dependencyMap[12]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  var FilterBar = exports.FilterBar = function FilterBar(props) {
    var onSearchPress = props.onSearchPress,
      scrollViewRef = props.scrollViewRef,
      onScroll = props.onScroll;
    var _useModal = (0, _useModal2.useModal)("facilitiesServicesFilter"),
      openFilterSheet = _useModal.openModal;
    var _useFacilitiesService = (0, _facilitiesServicesContext.useFacilitiesServicesContext)(),
      locations = _useFacilitiesService.locations,
      publicArea = _useFacilitiesService.publicArea,
      transitArea = _useFacilitiesService.transitArea,
      togglePublicArea = _useFacilitiesService.togglePublicArea,
      toggleTransitArea = _useFacilitiesService.toggleTransitArea,
      emitFiltersApplied = _useFacilitiesService.emitFiltersApplied;
    var hasLocationFilter = locations.length > 0;
    var hasFilter = hasLocationFilter || publicArea || transitArea;
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: styles.filtersContainer,
      children: (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.ScrollView, {
        ref: scrollViewRef,
        horizontal: true,
        showsHorizontalScrollIndicator: false,
        contentContainerStyle: {
          paddingHorizontal: 20
        },
        onScroll: onScroll,
        scrollEventThrottle: 16,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.filtersRow,
          children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: styles.filterIconButton,
            onPress: onSearchPress,
            children: (0, _jsxRuntime.jsx)(_icons.SearchIconV2, {
              width: 16,
              height: 16,
              color: _color.color.palette.darkestGrey
            })
          }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
            style: [styles.filterIconButton, hasFilter && styles.activeFilterIconButton],
            onPress: openFilterSheet,
            children: [(0, _jsxRuntime.jsx)(_icons.FilterV2, {
              width: 16,
              height: 16,
              color: hasFilter ? _color.color.palette.lightPurple : _color.color.palette.darkestGrey
            }), hasFilter && (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: styles.activeDot
            })]
          }), (0, _jsxRuntime.jsx)(_filterPill.FilterPill.Select, {
            label: hasLocationFilter ? locations.sort().join(", ") : (0, _i18n.translate)("facilitiesServices.location"),
            icon: (0, _jsxRuntime.jsx)(_icons.LocationOutline, {}),
            active: hasLocationFilter,
            testID: "facilities-services-location-filter",
            onPress: openFilterSheet
          }), (0, _jsxRuntime.jsx)(_filterPill.FilterPill.Toggle, {
            label: (0, _i18n.translate)("facilitiesServices.public"),
            active: publicArea,
            onPress: function onPress() {
              togglePublicArea();
              emitFiltersApplied();
            },
            testID: "facilities-services-public-filter"
          }), (0, _jsxRuntime.jsx)(_filterPill.FilterPill.Toggle, {
            label: (0, _i18n.translate)("facilitiesServices.transit"),
            active: transitArea,
            onPress: function onPress() {
              toggleTransitArea();
              emitFiltersApplied();
            },
            testID: "facilities-services-transit-filter"
          }), (0, _jsxRuntime.jsx)(_filterSheet.FilterSheet, {})]
        })
      })
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    filtersContainer: {
      height: _constants.FILTER_BAR_HEIGHT,
      backgroundColor: _color.color.palette.whiteGrey,
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20
    },
    filtersRow: {
      flexDirection: "row",
      gap: 4,
      alignItems: "center"
    },
    filterIconButton: {
      height: 30,
      width: 36,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      borderRadius: 99,
      borderWidth: 1,
      borderColor: _color.color.palette.lighterGrey
    },
    activeFilterIconButton: {
      backgroundColor: _color.color.palette.lightestPurple,
      borderColor: _color.color.palette.purpleD5BBEA
    },
    activeDot: {
      width: 7,
      height: 7,
      borderWidth: 1,
      borderColor: _color.color.palette.whiteGrey,
      backgroundColor: _color.color.palette.lightPurple,
      borderRadius: 10,
      position: "absolute",
      top: 0,
      right: 0
    }
  });
