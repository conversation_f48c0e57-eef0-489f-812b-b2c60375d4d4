  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.FilterSheet = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _text = _$$_REQUIRE(_dependencyMap[8]);
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _filterPill = _$$_REQUIRE(_dependencyMap[10]);
  var _facilitiesServicesContext = _$$_REQUIRE(_dependencyMap[11]);
  var _checkbox = _$$_REQUIRE(_dependencyMap[12]);
  var _reactNativeLinearGradient = _$$_REQUIRE(_dependencyMap[13]);
  var _i18n = _$$_REQUIRE(_dependencyMap[14]);
  var _useModal2 = _$$_REQUIRE(_dependencyMap[15]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[16]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SCREEN_HEIGHT = _reactNative2.Dimensions.get("window").height;
  var SHEET_HEIGHT = SCREEN_HEIGHT - 100;
  var FilterSheet = exports.FilterSheet = function FilterSheet() {
    var _useModal = (0, _useModal2.useModal)("facilitiesServicesFilter"),
      isModalVisible = _useModal.isModalVisible,
      closeModal = _useModal.closeModal;
    var _useFacilitiesService = (0, _facilitiesServicesContext.useFacilitiesServicesContext)(),
      contextLocations = _useFacilitiesService.locations,
      contextPublicArea = _useFacilitiesService.publicArea,
      contextTransitArea = _useFacilitiesService.transitArea,
      setContextLocations = _useFacilitiesService.setLocations,
      togglePublicArea = _useFacilitiesService.togglePublicArea,
      toggleTransitArea = _useFacilitiesService.toggleTransitArea,
      emitFiltersApplied = _useFacilitiesService.emitFiltersApplied;
    // Local state for the sheet
    var _useState = (0, _react.useState)(contextLocations),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      locations = _useState2[0],
      setLocations = _useState2[1];
    var _useState3 = (0, _react.useState)(contextPublicArea),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      publicArea = _useState4[0],
      setPublicArea = _useState4[1];
    var _useState5 = (0, _react.useState)(contextTransitArea),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      transitArea = _useState6[0],
      setTransitArea = _useState6[1];

    // Sync local state with context when context changes
    (0, _react.useEffect)(function () {
      if (!isModalVisible) {
        setLocations(contextLocations);
        setPublicArea(contextPublicArea);
        setTransitArea(contextTransitArea);
      }
    }, [isModalVisible, contextLocations, contextPublicArea, contextTransitArea]);
    var handleClearAll = function handleClearAll() {
      setLocations([]);
      setPublicArea(false);
      setTransitArea(false);
    };
    var handleApply = function handleApply() {
      setContextLocations(locations);
      if (publicArea !== contextPublicArea) togglePublicArea();
      if (transitArea !== contextTransitArea) toggleTransitArea();
      closeModal();
      emitFiltersApplied();
    };
    var toggleLocation = function toggleLocation(location) {
      setLocations(function (prev) {
        return prev.includes(location) ? prev.filter(function (loc) {
          return loc !== location;
        }) : [].concat((0, _toConsumableArray2.default)(prev), [location]);
      });
    };
    var toggleAllLocations = function toggleAllLocations() {
      setLocations(locations.length === _facilitiesServicesContext.AVAILABLE_LOCATIONS.length ? [] : (0, _toConsumableArray2.default)(_facilitiesServicesContext.AVAILABLE_LOCATIONS));
    };
    return (0, _jsxRuntime.jsxs)(_bottomSheet.default, {
      isModalVisible: Boolean(isModalVisible),
      onClosedSheet: closeModal,
      stopDragCollapse: true,
      onBackPressHandle: closeModal,
      containerStyle: styles.container,
      animationInTiming: 300,
      animationOutTiming: 300,
      openPendingModal: true,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.headerContainer,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.headerTitle,
          tx: "facilitiesServices.filters"
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: styles.headerIcon,
          onPress: closeModal,
          hitSlop: {
            top: 20,
            bottom: 20,
            left: 20,
            right: 20
          },
          children: (0, _jsxRuntime.jsx)(_icons.CloseCross, {
            width: 24,
            height: 24,
            color: _theme.color.palette.darkestGrey
          })
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.content,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          tx: "facilitiesServices.location",
          style: styles.sectionTitle
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.pillsRow,
          children: [(0, _jsxRuntime.jsx)(_filterPill.FilterPill.Toggle, {
            icon: (0, _jsxRuntime.jsx)(_icons.AirportIcon, {}),
            label: (0, _i18n.translate)("facilitiesServices.publicArea"),
            active: publicArea,
            onPress: function onPress() {
              return setPublicArea(!publicArea);
            },
            testID: "filter-sheet-public-area"
          }), (0, _jsxRuntime.jsx)(_filterPill.FilterPill.Toggle, {
            icon: (0, _jsxRuntime.jsx)(_icons.PlaneIconV2, {}),
            label: (0, _i18n.translate)("facilitiesServices.transitArea"),
            active: transitArea,
            onPress: function onPress() {
              return setTransitArea(!transitArea);
            },
            testID: "filter-sheet-transit-area"
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.checkboxList,
          children: [(0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
            style: styles.checkboxRow,
            onPress: toggleAllLocations,
            children: [(0, _jsxRuntime.jsx)(_checkbox.Checkbox, {
              value: locations.length === _facilitiesServicesContext.AVAILABLE_LOCATIONS.length,
              onToggle: toggleAllLocations,
              outlineStyle: styles.checkboxOutline,
              fillStyle: styles.checkboxFill,
              icon: (0, _jsxRuntime.jsx)(_icons.Check, {
                fill: _theme.color.palette.whiteGrey,
                width: 21,
                height: 21
              })
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              tx: "facilitiesServices.all",
              style: styles.checkboxLabel
            })]
          }), _facilitiesServicesContext.AVAILABLE_LOCATIONS.map(function (location) {
            return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
              style: styles.checkboxRow,
              onPress: function onPress() {
                return toggleLocation(location);
              },
              children: [(0, _jsxRuntime.jsx)(_checkbox.Checkbox, {
                value: locations.includes(location),
                onToggle: function onToggle() {
                  return toggleLocation(location);
                },
                outlineStyle: styles.checkboxOutline,
                fillStyle: styles.checkboxFill,
                icon: (0, _jsxRuntime.jsx)(_icons.Check, {
                  fill: _theme.color.palette.whiteGrey,
                  width: 21,
                  height: 21
                })
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                text: location,
                style: styles.checkboxLabel
              })]
            }, location);
          })]
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.footer,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.footerRow,
          children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: styles.clearButton,
            onPress: handleClearAll,
            testID: "filter-sheet-clear-button",
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              tx: "facilitiesServices.clearAll",
              style: styles.clearButtonText
            })
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: handleApply,
            testID: "filter-sheet-apply-button",
            children: (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.LinearGradient, {
              style: styles.linearGradient,
              start: {
                x: 0,
                y: 1
              },
              end: {
                x: 1,
                y: 0
              },
              colors: [_theme.color.palette.gradientColor1End, _theme.color.palette.gradientColor1Start],
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                tx: "facilitiesServices.applyFilters",
                style: styles.applyButtonText
              })
            })
          })]
        })
      })]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    container: {
      height: SHEET_HEIGHT,
      backgroundColor: _theme.color.palette.whiteGrey,
      borderTopRightRadius: 16,
      borderTopLeftRadius: 16
    },
    headerContainer: {
      height: 64,
      flexDirection: "row",
      justifyContent: "center",
      alignItems: "center",
      position: "relative",
      width: "100%"
    },
    headerTitle: Object.assign({}, _text.newPresets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey
    }),
    headerIcon: {
      position: "absolute",
      right: 16,
      top: 20
    },
    content: {
      flex: 1,
      paddingHorizontal: 20,
      paddingTop: 16
    },
    sectionTitle: Object.assign({}, _text.newPresets.bodyTextBlackBold, {
      marginBottom: 12,
      color: _theme.color.palette.almostBlackGrey
    }),
    pillsRow: {
      flexDirection: "row",
      gap: 8,
      marginBottom: 16
    },
    checkboxList: {
      gap: 12,
      marginTop: 12
    },
    checkboxRow: {
      flexDirection: "row",
      alignItems: "flex-start",
      gap: 12,
      height: 30,
      borderBottomWidth: 1,
      borderBottomColor: _theme.color.palette.lighterGrey
    },
    checkboxOutline: {
      borderColor: _theme.color.palette.midGrey,
      width: 16,
      height: 16,
      borderRadius: 4,
      justifyContent: "center",
      alignItems: "center",
      borderWidth: 1
    },
    checkboxFill: {
      width: 16,
      height: 16,
      borderRadius: 4,
      justifyContent: "center",
      alignItems: "center",
      borderWidth: 1,
      borderColor: _theme.color.palette.lightPurple
    },
    checkboxLabel: Object.assign({}, _text.newPresets.bodyTextRegular, {
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 14,
      lineHeight: 18
    }),
    footer: {
      height: 96,
      justifyContent: "flex-start",
      borderTopWidth: 1,
      borderTopColor: _theme.color.palette.lighterGrey
    },
    footerRow: {
      flexDirection: "row",
      paddingHorizontal: 20,
      marginTop: 11
    },
    clearButton: {
      flex: 1,
      height: 44,
      justifyContent: "center",
      alignItems: "flex-start"
    },
    clearButtonText: Object.assign({}, _text.newPresets.bodyTextBold, {
      fontSize: 14,
      lineHeight: 18,
      color: _theme.color.palette.lightPurple
    }),
    applyButton: {
      flex: 1,
      height: 48,
      justifyContent: "center",
      alignItems: "center",
      borderRadius: 8,
      backgroundColor: _theme.color.palette.basePurple
    },
    applyButtonText: Object.assign({}, _text.newPresets.bodyTextBold, {
      color: _theme.color.palette.whiteGrey
    }),
    linearGradient: {
      height: 44,
      width: 176,
      justifyContent: "center",
      alignItems: "center",
      borderRadius: 999
    }
  });
