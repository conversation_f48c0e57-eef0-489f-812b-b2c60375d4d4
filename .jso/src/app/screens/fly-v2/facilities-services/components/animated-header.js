  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.AnimatedHeader = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _color = _$$_REQUIRE(_dependencyMap[6]);
  var _icons = _$$_REQUIRE(_dependencyMap[7]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[8]));
  var _constants = _$$_REQUIRE(_dependencyMap[9]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _worklet_2095213008932_init_data = {
    code: "function animatedHeaderTsx1(){const{scrollY}=this.__closure;return scrollY.value;}"
  };
  var _worklet_5790798083338_init_data = {
    code: "function animatedHeaderTsx2(value){const{runOnJS,setStatusBarStyle}=this.__closure;if(value>=15){runOnJS(setStatusBarStyle)(\"dark-content\");}else{runOnJS(setStatusBarStyle)(\"light-content\");}}"
  };
  var _worklet_7618275939507_init_data = {
    code: "function animatedHeaderTsx3(){const{interpolate,scrollY,Extrapolation}=this.__closure;return{opacity:interpolate(scrollY.value,[14,15],[0,1],Extrapolation.CLAMP)};}"
  };
  var AnimatedHeader = exports.AnimatedHeader = function AnimatedHeader(props) {
    var scrollY = props.scrollY,
      onBackPress = props.onBackPress;
    var _useState = (0, _react.useState)("light-content"),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      statusBarStyle = _useState2[0],
      setStatusBarStyle = _useState2[1];
    (0, _reactNativeReanimated.useAnimatedReaction)(function () {
      var animatedHeaderTsx1 = function animatedHeaderTsx1() {
        return scrollY.value;
      };
      animatedHeaderTsx1.__closure = {
        scrollY: scrollY
      };
      animatedHeaderTsx1.__workletHash = 2095213008932;
      animatedHeaderTsx1.__initData = _worklet_2095213008932_init_data;
      return animatedHeaderTsx1;
    }(), function () {
      var animatedHeaderTsx2 = function animatedHeaderTsx2(value) {
        if (value >= 15) {
          (0, _reactNativeReanimated.runOnJS)(setStatusBarStyle)("dark-content");
        } else {
          (0, _reactNativeReanimated.runOnJS)(setStatusBarStyle)("light-content");
        }
      };
      animatedHeaderTsx2.__closure = {
        runOnJS: _reactNativeReanimated.runOnJS,
        setStatusBarStyle: setStatusBarStyle
      };
      animatedHeaderTsx2.__workletHash = 5790798083338;
      animatedHeaderTsx2.__initData = _worklet_5790798083338_init_data;
      return animatedHeaderTsx2;
    }());
    var headerAnimatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var animatedHeaderTsx3 = function animatedHeaderTsx3() {
        return {
          opacity: (0, _reactNativeReanimated.interpolate)(scrollY.value, [14, 15], [0, 1], _reactNativeReanimated.Extrapolation.CLAMP)
        };
      };
      animatedHeaderTsx3.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        scrollY: scrollY,
        Extrapolation: _reactNativeReanimated.Extrapolation
      };
      animatedHeaderTsx3.__workletHash = 7618275939507;
      animatedHeaderTsx3.__initData = _worklet_7618275939507_init_data;
      return animatedHeaderTsx3;
    }());
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_reactNative2.StatusBar, {
        barStyle: statusBarStyle,
        translucent: true,
        backgroundColor: "transparent"
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.staticHeaderContainer,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.headerRow,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            tx: "facilitiesServices.title",
            style: styles.staticHeaderTitle
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: onBackPress,
            children: (0, _jsxRuntime.jsx)(_icons.ArrowLeftV2, {
              width: 24,
              height: 24,
              color: _color.color.palette.whiteGrey
            })
          })]
        })
      }), (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
        style: [styles.fadingHeaderContainer, headerAnimatedStyle],
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.headerRow,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            tx: "facilitiesServices.title",
            style: styles.fadingHeaderTitle
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: onBackPress,
            children: (0, _jsxRuntime.jsx)(_icons.ArrowLeftV2, {
              width: 24,
              height: 24,
              color: _color.color.palette.darkestGrey
            })
          })]
        })
      })]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    staticHeaderContainer: {
      height: _constants.HEADER_HEIGHT,
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      zIndex: 1000
    },
    fadingHeaderContainer: {
      height: 100,
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      backgroundColor: "white",
      shadowColor: "#121212",
      shadowOffset: {
        width: 0,
        height: 2
      },
      shadowOpacity: 0.08,
      shadowRadius: 8,
      zIndex: 1000,
      elevation: 5
    },
    headerRow: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: 16,
      marginTop: 61
    },
    staticHeaderTitle: Object.assign({}, _text.newPresets.subTitleBold, {
      position: "absolute",
      left: 0,
      right: 0,
      color: _color.color.palette.whiteGrey,
      textAlign: "center"
    }),
    fadingHeaderTitle: Object.assign({}, _text.newPresets.subTitleBold, {
      position: "absolute",
      left: 0,
      right: 0,
      color: _color.color.palette.darkestGrey,
      textAlign: "center"
    })
  });
