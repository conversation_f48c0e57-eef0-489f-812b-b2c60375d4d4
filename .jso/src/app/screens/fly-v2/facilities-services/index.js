  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.FacilitiesServices = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[0]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[2]);
  var _native = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[5]);
  var _color = _$$_REQUIRE(_dependencyMap[6]);
  var _constants = _$$_REQUIRE(_dependencyMap[7]);
  var _useFacilitiesServices = _$$_REQUIRE(_dependencyMap[8]);
  var _facilitiesServicesContext = _$$_REQUIRE(_dependencyMap[9]);
  var _facilityServiceItem = _$$_REQUIRE(_dependencyMap[10]);
  var _animatedHeader = _$$_REQUIRE(_dependencyMap[11]);
  var _constants2 = _$$_REQUIRE(_dependencyMap[12]);
  var _filterBar = _$$_REQUIRE(_dependencyMap[13]);
  var _useInternetConnection = _$$_REQUIRE(_dependencyMap[14]);
  var _emptyState = _$$_REQUIRE(_dependencyMap[15]);
  var _alphabeticalSidebar = _$$_REQUIRE(_dependencyMap[16]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[17]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var ENABLE_SCROLL_ANIMATION = false;
  var _worklet_4334477956272_init_data = {
    code: "function indexTsx1(event){const{scrollY}=this.__closure;scrollY.value=event.contentOffset.y;}"
  };
  var _worklet_17085465588178_init_data = {
    code: "function indexTsx2(event){const{filterScrollX}=this.__closure;filterScrollX.value=event.contentOffset.x;}"
  };
  var _worklet_10978137702667_init_data = {
    code: "function indexTsx3(){const{interpolate,scrollY,Extrapolation}=this.__closure;return{borderTopLeftRadius:interpolate(scrollY.value,[0,15],[20,0],Extrapolation.CLAMP),borderTopRightRadius:interpolate(scrollY.value,[0,15],[20,0],Extrapolation.CLAMP)};}"
  };
  var _worklet_16276464303807_init_data = {
    code: "function indexTsx4(){const{scrollY}=this.__closure;return scrollY.value;}"
  };
  var _worklet_9700413091947_init_data = {
    code: "function indexTsx5(value){const{mainFilterRef,stickyFilterRef,scrollTo,filterScrollX}=this.__closure;try{const filterToBeScrolled=value<=15?mainFilterRef:stickyFilterRef;scrollTo(filterToBeScrolled,filterScrollX.value,0,false);}catch(error){console.log(\"error\",error);}}"
  };
  var _worklet_684487886647_init_data = {
    code: "function indexTsx6(){const{scrollY,interpolate,Extrapolation}=this.__closure;return{opacity:scrollY.value>15?1:0,pointerEvents:scrollY.value>15?\"auto\":\"none\",borderTopLeftRadius:interpolate(scrollY.value,[0,15],[20,0],Extrapolation.CLAMP),borderTopRightRadius:interpolate(scrollY.value,[0,15],[20,0],Extrapolation.CLAMP)};}"
  };
  var FacilitiesServicesContent = function FacilitiesServicesContent() {
    var navigation = (0, _native.useNavigation)();
    var _useFacilitiesService = (0, _useFacilitiesServices.useFacilitiesServices)(),
      facilities = _useFacilitiesService.facilities,
      loading = _useFacilitiesService.loading,
      hasApiError = _useFacilitiesService.hasApiError,
      refreshing = _useFacilitiesService.refreshing,
      refresh = _useFacilitiesService.refresh,
      groupedFacilities = _useFacilitiesService.groupedFacilities,
      activeLetters = _useFacilitiesService.activeLetters,
      reload = _useFacilitiesService.reload;
    var _useInternetConnectio = (0, _useInternetConnection.useInternetConnection)(),
      isInternetConnected = _useInternetConnectio.isInternetConnected,
      checkInternetConnection = _useInternetConnectio.checkInternetConnection;
    var _useFacilitiesService2 = (0, _facilitiesServicesContext.useFacilitiesServicesContext)(),
      onFiltersApplied = _useFacilitiesService2.onFiltersApplied;
    var insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    var scrollViewRef = (0, _react.useRef)(null);
    var sectionRefs = (0, _react.useRef)({});
    var _useWindowDimensions = (0, _reactNative2.useWindowDimensions)(),
      width = _useWindowDimensions.width;

    /**
     * Filter Bar Implementation Note:
     * 
     * Challenge:
     * We need a filter bar with specific scroll behaviors:
     * 1. When scrolling up: Filter bar should stick to the top of the screen
     * 2. When pulling to refresh (iOS): Filter bar should follow the content down
     *    - This maintains the natural "rubber band" effect of iOS
     *    - Prevents the filter bar from looking disconnected during pull-to-refresh
     * 
     * Initially tried using a single filter bar and animating its position based on scroll offset,
     * but this approach had issues:
     * - Animation wasn't smooth due to complex layout transitions
     * - Performance issues with animated position changes
     * - Visual jank during rapid scrolling
     * - Pull-to-refresh interaction felt unnatural with position animation
     * 
     * Current Solution:
     * 1. Render two separate filter bars:
     *    - Main filter bar: Part of the scrollable content
     *    - Sticky filter bar: Fixed position, shows/hides based on scroll
     * 2. Sync their horizontal scroll positions for a seamless experience
     * 3. Fade in/out the sticky bar based on scroll position
     * 
     * This approach provides better performance and smoother transitions
     * as we avoid complex position animations.
     */
    var mainFilterRef = (0, _reactNativeReanimated.useAnimatedRef)();
    var stickyFilterRef = (0, _reactNativeReanimated.useAnimatedRef)();
    var filterScrollX = (0, _reactNativeReanimated.useSharedValue)(0);
    var scrollToTop = function scrollToTop() {
      var _scrollViewRef$curren;
      (_scrollViewRef$curren = scrollViewRef.current) == null || _scrollViewRef$curren.scrollTo({
        y: 0,
        animated: false
      });
    };
    (0, _react.useEffect)(function () {
      return onFiltersApplied(scrollToTop);
    }, [onFiltersApplied]);
    var scrollY = (0, _reactNativeReanimated.useSharedValue)(0);
    var scrollHandler = (0, _reactNativeReanimated.useAnimatedScrollHandler)({
      onScroll: function () {
        var indexTsx1 = function indexTsx1(event) {
          scrollY.value = event.contentOffset.y;
        };
        indexTsx1.__closure = {
          scrollY: scrollY
        };
        indexTsx1.__workletHash = 4334477956272;
        indexTsx1.__initData = _worklet_4334477956272_init_data;
        return indexTsx1;
      }()
    });
    var filterScrollHandler = (0, _reactNativeReanimated.useAnimatedScrollHandler)({
      onScroll: function () {
        var indexTsx2 = function indexTsx2(event) {
          filterScrollX.value = event.contentOffset.x;
        };
        indexTsx2.__closure = {
          filterScrollX: filterScrollX
        };
        indexTsx2.__workletHash = 17085465588178;
        indexTsx2.__initData = _worklet_17085465588178_init_data;
        return indexTsx2;
      }()
    });
    var borderRadiusStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var indexTsx3 = function indexTsx3() {
        return {
          borderTopLeftRadius: (0, _reactNativeReanimated.interpolate)(scrollY.value, [0, 15], [20, 0], _reactNativeReanimated.Extrapolation.CLAMP),
          borderTopRightRadius: (0, _reactNativeReanimated.interpolate)(scrollY.value, [0, 15], [20, 0], _reactNativeReanimated.Extrapolation.CLAMP)
        };
      };
      indexTsx3.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        scrollY: scrollY,
        Extrapolation: _reactNativeReanimated.Extrapolation
      };
      indexTsx3.__workletHash = 10978137702667;
      indexTsx3.__initData = _worklet_10978137702667_init_data;
      return indexTsx3;
    }());
    // Apply stored scroll position when switching between bars
    (0, _reactNativeReanimated.useAnimatedReaction)(function () {
      var indexTsx4 = function indexTsx4() {
        return scrollY.value;
      };
      indexTsx4.__closure = {
        scrollY: scrollY
      };
      indexTsx4.__workletHash = 16276464303807;
      indexTsx4.__initData = _worklet_16276464303807_init_data;
      return indexTsx4;
    }(), function () {
      var indexTsx5 = function indexTsx5(value) {
        try {
          var filterToBeScrolled = value <= 15 ? mainFilterRef : stickyFilterRef;
          (0, _reactNativeReanimated.scrollTo)(filterToBeScrolled, filterScrollX.value, 0, false);
        } catch (error) {}
      };
      indexTsx5.__closure = {
        mainFilterRef: mainFilterRef,
        stickyFilterRef: stickyFilterRef,
        scrollTo: _reactNativeReanimated.scrollTo,
        filterScrollX: filterScrollX
      };
      indexTsx5.__workletHash = 9700413091947;
      indexTsx5.__initData = _worklet_9700413091947_init_data;
      return indexTsx5;
    }());
    /**
     * Animated style for the sticky filter bar:
     * - Show when scrolling up (scrollY > 15)
     * - Hide when at top or pulling to refresh (scrollY <= 15)
     * - Disable interaction when hidden to prevent accidental touches
     * 
     * This creates a smooth transition between the main filter bar and sticky filter bar:
     * 1. When pulling down: Only the main filter bar is visible and follows content
     * 2. When scrolling up: Sticky bar fades in as main bar scrolls out of view
     */
    var stickyFilterBarAnimatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var indexTsx6 = function indexTsx6() {
        return {
          opacity: scrollY.value > 15 ? 1 : 0,
          pointerEvents: scrollY.value > 15 ? "auto" : "none",
          borderTopLeftRadius: (0, _reactNativeReanimated.interpolate)(scrollY.value, [0, 15], [20, 0], _reactNativeReanimated.Extrapolation.CLAMP),
          borderTopRightRadius: (0, _reactNativeReanimated.interpolate)(scrollY.value, [0, 15], [20, 0], _reactNativeReanimated.Extrapolation.CLAMP)
        };
      };
      indexTsx6.__closure = {
        scrollY: scrollY,
        interpolate: _reactNativeReanimated.interpolate,
        Extrapolation: _reactNativeReanimated.Extrapolation
      };
      indexTsx6.__workletHash = 684487886647;
      indexTsx6.__initData = _worklet_684487886647_init_data;
      return indexTsx6;
    }());
    var shouldShowFilterBar = isInternetConnected && !loading && !refreshing && !hasApiError;
    var shouldShowAlphabetSidebar = isInternetConnected && !loading && !refreshing && !hasApiError;
    var handleLetterSelect = function handleLetterSelect(letter) {
      var sectionRef = sectionRefs.current[letter];
      if (sectionRef && scrollViewRef.current) {
        sectionRef.measureLayout(scrollViewRef.current, function (_x, y) {
          // The top of content is already offset by CONTENT_TOP
          // so we need to subtract it from the y position
          // 20 is just a fine-tuning value
          scrollViewRef.current.scrollTo({
            y: y - _constants2.CONTENT_TOP + 20,
            animated: ENABLE_SCROLL_ANIMATION
          });
        });
      }
    };
    var handleGoBack = function handleGoBack() {
      navigation == null || navigation.goBack();
    };
    var handleSearch = function handleSearch() {
      // @ts-ignore
      navigation.navigate(_constants.NavigationConstants.search, {
        focusTextInput: true
      });
    };
    var handleRefresh = function handleRefresh() {
      checkInternetConnection();
      refresh();
    };
    var handleReload = function handleReload() {
      checkInternetConnection();
      reload();
    };
    function renderContent() {
      if (!isInternetConnected) {
        return (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.noInternetContainer,
          children: (0, _jsxRuntime.jsx)(_emptyState.EmptyState.NoInternet, {
            onPressReload: handleReload
          })
        });
      }
      if (loading || refreshing) {
        return (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.listSkeletonsContainer,
          children: Array.from({
            length: 5
          }).map(function (_, index) {
            return (0, _jsxRuntime.jsx)(_facilityServiceItem.FacilityServiceItemSkeleton, {}, index);
          })
        });
      }
      if (hasApiError) {
        return (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.apiErrorContainer,
          children: (0, _jsxRuntime.jsx)(_emptyState.EmptyState.ApiError, {
            onPressReload: handleReload
          })
        });
      }
      if (facilities.length === 0) {
        return (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.noResultsContainer,
          children: (0, _jsxRuntime.jsx)(_emptyState.EmptyState.NoResults, {})
        });
      }
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.listItemsContainer,
        children: Array.from(activeLetters).map(function (letter) {
          return (0, _jsxRuntime.jsx)(_reactNative2.View, {
            ref: function ref(_ref) {
              if (_ref) {
                sectionRefs.current[letter] = _ref;
              }
            },
            style: {
              gap: 20
            },
            children: groupedFacilities[letter].map(function (item) {
              return (0, _jsxRuntime.jsx)(_facilityServiceItem.FacilityServiceItem, {
                item: item
              }, item.id);
            })
          }, letter);
        })
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.screenContainer,
      children: [(0, _jsxRuntime.jsx)(_reactNative2.Image, {
        source: _$$_REQUIRE(_dependencyMap[18]),
        style: [styles.fixedBackground, {
          width: width,
          height: width * 245 / 375
        }],
        resizeMode: "cover"
      }), (0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.ScrollView, {
        ref: scrollViewRef,
        showsVerticalScrollIndicator: false,
        onScroll: scrollHandler,
        scrollEventThrottle: 16,
        contentContainerStyle: {
          paddingBottom: insets.bottom
        },
        refreshControl: (0, _jsxRuntime.jsx)(_reactNative.RefreshControl, {
          refreshing: refreshing,
          onRefresh: handleRefresh,
          progressViewOffset: 100,
          tintColor: _color.color.palette.whiteGrey
        }),
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.pseduoHeader
        }), (0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
          style: [styles.contentContainer, borderRadiusStyle],
          children: [shouldShowFilterBar ? (0, _jsxRuntime.jsx)(_filterBar.FilterBar, {
            onSearchPress: handleSearch,
            scrollViewRef: mainFilterRef,
            onScroll: filterScrollHandler
          }) : (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.pseudoFilterBar
          }), renderContent()]
        })]
      }), shouldShowAlphabetSidebar && (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.alphabetSidebarContainer,
        children: (0, _jsxRuntime.jsx)(_alphabeticalSidebar.AlphabeticalSidebar, {
          activeLetters: activeLetters,
          onSelectLetter: handleLetterSelect
        })
      }), shouldShowFilterBar && (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
        style: [styles.stickyFilterBar, stickyFilterBarAnimatedStyle],
        children: (0, _jsxRuntime.jsx)(_filterBar.FilterBar, {
          onSearchPress: handleSearch,
          scrollViewRef: stickyFilterRef,
          onScroll: filterScrollHandler
        })
      }), (0, _jsxRuntime.jsx)(_animatedHeader.AnimatedHeader, {
        scrollY: scrollY,
        onBackPress: handleGoBack
      })]
    });
  };
  var FacilitiesServices = exports.FacilitiesServices = function FacilitiesServices() {
    return (0, _jsxRuntime.jsx)(_facilitiesServicesContext.FacilitiesServicesProvider, {
      children: (0, _jsxRuntime.jsx)(FacilitiesServicesContent, {})
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    screenContainer: {
      flex: 1,
      backgroundColor: _color.color.palette.whiteGrey
    },
    fixedBackground: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0
    },
    pseduoHeader: {
      height: _constants2.HEADER_HEIGHT,
      marginBottom: 15
    },
    contentContainer: {
      flex: 1,
      backgroundColor: _color.color.palette.whiteGrey,
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      paddingBottom: 20
    },
    listSkeletonsContainer: {
      flex: 1,
      gap: 24,
      marginTop: 16
    },
    listItemsContainer: {
      flex: 1,
      gap: 20,
      marginTop: 16
    },
    stickyFilterBar: {
      position: "absolute",
      top: _constants2.HEADER_BOTTOM,
      left: 0,
      right: 0,
      height: _constants2.FILTER_BAR_HEIGHT,
      zIndex: 1000,
      backgroundColor: _color.color.palette.whiteGrey
    },
    noInternetContainer: {
      flex: 1,
      justifyContent: "center",
      paddingHorizontal: 24,
      marginTop: 36
    },
    apiErrorContainer: {
      flex: 1,
      justifyContent: "center",
      paddingHorizontal: 24,
      marginTop: 40
    },
    noResultsContainer: {
      flex: 1,
      justifyContent: "center",
      paddingHorizontal: 24,
      marginTop: 40
    },
    pseudoFilterBar: {
      height: _constants2.FILTER_BAR_HEIGHT
    },
    alphabetSidebarContainer: {
      position: "absolute",
      right: 0,
      top: _constants2.CONTENT_TOP,
      zIndex: 100
    }
  });
