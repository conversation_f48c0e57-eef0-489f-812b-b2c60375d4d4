  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.tabScreenStyles = exports.default = exports.BG_HEIGHT = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var BG_HEIGHT = exports.BG_HEIGHT = width * 0.688;
  var _default = exports.default = _reactNative.StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: _theme.color.palette.almostWhiteGrey
    },
    fixedBackground: {
      position: "absolute",
      top: 0,
      left: 0,
      width: '100%',
      height: BG_HEIGHT,
      right: 0
    },
    headerSafeArea: {
      position: "relative",
      top: 0,
      left: 0,
      width: '100%',
      zIndex: 3,
      flex: 1
    },
    headerRow: {
      display: "flex",
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      height: 32,
      paddingHorizontal: 16
    },
    headerTitle: {
      flex: 1,
      textAlign: "center",
      color: _theme.color.palette.whiteGrey,
      fontSize: 18,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      fontFamily: _theme.typography.bold,
      lineHeight: 22,
      letterSpacing: 0
    },
    realtimeCard: {
      marginBottom: 14
    },
    liveText: {
      color: _theme.color.palette.lighterGrey,
      textAlign: "center",
      fontWeight: _reactNative.Platform.select({
        ios: "400",
        android: "normal"
      }),
      fontFamily: _theme.typography.medium,
      fontSize: 11,
      lineHeight: 14,
      letterSpacing: 0
    },
    realtimeTitle: {
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      fontFamily: _theme.typography.bold,
      fontSize: 14,
      color: "#222"
    },
    flightContainer: {
      flex: 1
    },
    loadingIndicator: {
      marginBottom: 10
    },
    loadingIndicatorContainer: {
      alignItems: "center",
      opacity: 0,
      overflow: "hidden",
      position: "absolute",
      height: 48,
      alignSelf: "center",
      top: 40
    }
  });
  var tabScreenStyles = exports.tabScreenStyles = _reactNative.StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: _theme.color.palette.almostWhiteGrey
    },
    lottieLoading: {
      alignItems: "center",
      alignSelf: "center",
      backgroundColor: _theme.color.palette.transparent,
      marginLeft: 10,
      width: "70%",
      height: _reactNative.Dimensions.get("window").height
    },
    lottieLoadingBackground: {
      backgroundColor: _theme.color.palette.whiteColorOpacity,
      bottom: 0,
      left: 0,
      position: "absolute",
      right: 0,
      top: 0
    },
    listStyle: {
      backgroundColor: _theme.color.palette.almostWhiteGrey
    },
    sectionContainer: {
      paddingHorizontal: 20,
      marginBottom: 16
    },
    filterContainer: {
      paddingTop: 20,
      marginBottom: 20
    },
    feedBackToastStyle: {
      bottom: 24,
      paddingHorizontal: 16,
      width: "100%"
    },
    toastButtonStyle: Object.assign({}, _text.presets.textLink, {
      alignItems: "flex-end",
      color: _theme.color.palette.lightBlue,
      fontWeight: "normal"
    }),
    toastTextStyle: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.whiteGrey,
      width: "80%"
    }),
    noEarlierFlights: {
      marginBottom: 20,
      marginHorizontal: 20
    },
    errorContainer: {
      flex: 1,
      paddingHorizontal: 24,
      marginTop: 30
    },
    noFlightsText: {
      paddingHorizontal: 20,
      marginBottom: 40
    },
    listContainer: {
      paddingBottom: 20
    }
  });
