  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  var _useArrivalFlightV = _$$_REQUIRE(_dependencyMap[0]);
  Object.keys(_useArrivalFlightV).forEach(function (key) {
    if (key === "default" || key === "__esModule") return;
    if (key in exports && exports[key] === _useArrivalFlightV[key]) return;
    Object.defineProperty(exports, key, {
      enumerable: true,
      get: function get() {
        return _useArrivalFlightV[key];
      }
    });
  });
  var _useDepartureFlightV = _$$_REQUIRE(_dependencyMap[1]);
  Object.keys(_useDepartureFlightV).forEach(function (key) {
    if (key === "default" || key === "__esModule") return;
    if (key in exports && exports[key] === _useDepartureFlightV[key]) return;
    Object.defineProperty(exports, key, {
      enumerable: true,
      get: function get() {
        return _useDepartureFlightV[key];
      }
    });
  });
