  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useInternetConnection = useInternetConnection;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _$$_REQUIRE(_dependencyMap[3]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  function useInternetConnection() {
    var _useState = (0, _react.useState)(true),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isInternetConnected = _useState2[0],
      setIsInternetConnected = _useState2[1];
    (0, _react.useEffect)(function () {
      checkInternetConnection();
    }, []);
    var checkInternetConnection = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        setIsInternetConnected(isConnected);
        return isConnected;
      });
      return function checkInternetConnection() {
        return _ref.apply(this, arguments);
      };
    }();
    return {
      isInternetConnected: isInternetConnected,
      checkInternetConnection: checkInternetConnection
    };
  }
