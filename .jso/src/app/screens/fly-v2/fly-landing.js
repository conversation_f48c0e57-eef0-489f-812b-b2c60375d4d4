  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.FlyLanding = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _color = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _native = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _search = _$$_REQUIRE(_dependencyMap[8]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[9]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[10]));
  var _trackTodayFlightsBanner = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _text = _$$_REQUIRE(_dependencyMap[13]);
  var _constants = _$$_REQUIRE(_dependencyMap[14]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[15]);
  var _theme = _$$_REQUIRE(_dependencyMap[16]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[17]);
  var _icons = _$$_REQUIRE(_dependencyMap[18]);
  var _mytravelRedux = _$$_REQUIRE(_dependencyMap[19]);
  var _navigationUtilities = _$$_REQUIRE(_dependencyMap[20]);
  var _flyLandingAnimatedHeader = _$$_REQUIRE(_dependencyMap[21]);
  var _searchIndex = _$$_REQUIRE(_dependencyMap[22]);
  var _flyLandingSections = _$$_REQUIRE(_dependencyMap[23]);
  var _emptyState = _$$_REQUIRE(_dependencyMap[24]);
  var _useInternetConnection = _$$_REQUIRE(_dependencyMap[25]);
  var _useTickerbandMaintanence = _$$_REQUIRE(_dependencyMap[26]);
  var _tickerBand = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[27]));
  var _airportInfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[28]));
  var _aemRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[29]));
  var _cloudSvg = _$$_REQUIRE(_dependencyMap[30]);
  var _forYouRedux = _$$_REQUIRE(_dependencyMap[31]);
  var _aemQuicklinkCategories = _$$_REQUIRE(_dependencyMap[32]);
  var _useFetchQuickLinks2 = _$$_REQUIRE(_dependencyMap[33]);
  var _adobe = _$$_REQUIRE(_dependencyMap[34]);
  var _useStatusBarStyle = _$$_REQUIRE(_dependencyMap[35]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[36]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SCREEN_NAME = "FlyLanding";
  var parentContainerStyle = {
    flex: 1,
    backgroundColor: _color.color.palette.whiteGrey
  };
  var FlyLanding = exports.FlyLanding = function FlyLanding(_ref) {
    var navigation = _ref.navigation,
      route = _ref.route;
    (0, _useStatusBarStyle.useStatusBarStyle)('dark');
    var insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var savedCount = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.countSavedFlight);
    var searchRef = (0, _react.useRef)(null);
    var _useInternetConnectio = (0, _useInternetConnection.useInternetConnection)(),
      isInternetConnected = _useInternetConnectio.isInternetConnected,
      checkInternetConnection = _useInternetConnectio.checkInternetConnection;
    var _useTickerbandMaintan = (0, _useTickerbandMaintanence.useTickerbandMaintanance)(_useTickerbandMaintanence.MaintananceTickerBandType.FLY_LANDING_V2),
      isShowTickerband = _useTickerbandMaintan.isShowTickerband,
      tickerBand = _useTickerbandMaintan.tickerBand,
      tickerBandDescription = _useTickerbandMaintan.tickerBandDescription,
      tickerBandButtonText = _useTickerbandMaintan.tickerBandButtonText,
      onPressCTA = _useTickerbandMaintan.onPressCTA,
      onCloseTickerBand = _useTickerbandMaintan.onCloseTickerBand;
    var scrollY = (0, _reactNativeReanimated.useSharedValue)(0);
    var _useHandleScroll = (0, _navigationUtilities.useHandleScroll)(),
      handleScroll = _useHandleScroll.handleScroll,
      isTabVisible = _useHandleScroll.isTabVisible;
    var dispatch = (0, _reactRedux.useDispatch)();
    var carouselRef = (0, _react.useRef)(null);
    var rewardsData = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.rewardsData);
    var _useFetchQuickLinks = (0, _useFetchQuickLinks2.useFetchQuickLinks)(_aemQuicklinkCategories.ScreenType.Fly),
      flyQuicklinks = _useFetchQuickLinks.data,
      isLoadingFlyQuicklinks = _useFetchQuickLinks.loading,
      isErrorFlyQuicklinks = _useFetchQuickLinks.error,
      refetchFlyQuicklinks = _useFetchQuickLinks.refetch;
    var fetchAirportLevel1Config = (0, _react.useCallback)(function () {
      dispatch(_aemRedux.default.getAemConfigData({
        name: "airportLevel1",
        pathName: "airportLevel1",
        forceRequest: true
      }));
    }, [dispatch]);
    (0, _react.useEffect)(function () {
      fetchAirportLevel1Config();
    }, [fetchAirportLevel1Config]);

    // Reset carousel to first index when screen comes into focus
    (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
      var _carouselRef$current;
      (_carouselRef$current = carouselRef.current) == null || _carouselRef$current.resetToFirstIndex();
    }, []));
    var onScroll = (0, _react.useCallback)(function (event) {
      var _route$params;
      scrollY.value = event.nativeEvent.contentOffset.y;
      handleScroll(event);
      route == null || (_route$params = route.params) == null || _route$params.setOptions == null || _route$params.setOptions({
        tabBarVisible: isTabVisible.current
      });
    }, [handleScroll, scrollY, route, isTabVisible]);
    var resetSearchIfNotFlyTab = (0, _react.useCallback)(function () {
      try {
        var _searchRef$current;
        var isFlyTab = (0, _navigationUtilities.getActiveRouteName)(navigation.getState()) === "fly";
        if (!isFlyTab && (_searchRef$current = searchRef.current) != null && _searchRef$current.reset) {
          searchRef.current.reset();
        }
      } catch (error) {
        console.error("Error parsing navigation state:", error);
      }
    }, [navigation]);
    (0, _react.useEffect)(function () {
      var unsubscribe = navigation.addListener("state", resetSearchIfNotFlyTab);
      return unsubscribe;
    }, [navigation, resetSearchIfNotFlyTab]);
    var handleNavigateToSavedFlights = function handleNavigateToSavedFlights() {
      navigation == null || navigation.navigate(_constants.NavigationConstants.saveFlightsScreen);
    };
    var onPressSavedFlight = function onPressSavedFlight() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlightLanding, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlightLanding, "Save Flight Folio Entrypoint"));
      if (!isLoggedIn) {
        navigation.navigate(_constants.NavigationConstants.authScreen, {
          sourceSystem: _constants.SOURCE_SYSTEM.FLIGHTS,
          callBackAfterLoginSuccess: handleNavigateToSavedFlights
        });
        return;
      }
      handleNavigateToSavedFlights();
    };
    var handleNavigateToGlobalSearch = function handleNavigateToGlobalSearch() {
      navigation.navigate(_constants.NavigationConstants.search, {
        screen: _searchIndex.SearchIndex.flights
      });
    };
    var onRefresh = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var hasInternet = yield checkInternetConnection();
        if (hasInternet) {
          var _carouselRef$current2;
          fetchAirportLevel1Config();
          (_carouselRef$current2 = carouselRef.current) == null || _carouselRef$current2.refresh();
          refetchFlyQuicklinks();
        }
      });
      return function onRefresh() {
        return _ref2.apply(this, arguments);
      };
    }();
    if (!isInternetConnected) {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.noInternetContainer,
        children: [(0, _jsxRuntime.jsx)(_emptyState.EmptyState.NoInternet, {
          onPressReload: checkInternetConnection
        }), (0, _jsxRuntime.jsx)(_reactNative2.StatusBar, {
          barStyle: "dark-content",
          translucent: true,
          backgroundColor: "transparent"
        })]
      });
    }
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [isShowTickerband && (0, _jsxRuntime.jsx)(_tickerBand.default, {
        urgent: false,
        title: tickerBand,
        description: tickerBandDescription,
        buttonText: tickerBandButtonText,
        onCTAPress: onPressCTA,
        onClose: onCloseTickerBand,
        isLanding: true,
        tickerStyle: {
          paddingTop: insets.top + 6,
          paddingBottom: 38
        }
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: parentContainerStyle,
        testID: `${SCREEN_NAME}__FlyLandingScreen`,
        accessibilityLabel: `${SCREEN_NAME}__FlyLandingScreen`,
        children: [(0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.ScrollView, {
          keyboardShouldPersistTaps: "handled",
          contentContainerStyle: {
            paddingTop: isShowTickerband ? 0 : insets.top
          },
          onScroll: onScroll,
          scrollEventThrottle: 16,
          showsVerticalScrollIndicator: false,
          refreshControl: (0, _jsxRuntime.jsx)(_reactNative.RefreshControl, {
            refreshing: false,
            onRefresh: onRefresh,
            progressViewOffset: isShowTickerband ? 0 : insets.top
          }),
          children: [(0, _jsxRuntime.jsxs)(_reactNativeLinearGradient.default, {
            colors: ["#FFFFFF", "#FFFFFF", "#F9F5FC", "#DEE6F6"],
            locations: [0, 0.5, 0.75, 1],
            style: {
              alignItems: "center",
              flex: 1
            },
            children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: styles.headerContainer,
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                preset: "h6",
                tx: "flyLandingV2.myFlight"
              }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                children: [savedCount > 0 && isLoggedIn && (0, _jsxRuntime.jsx)(_reactNative2.View, {
                  style: styles.badge,
                  children: (0, _jsxRuntime.jsx)(_text.Text, {
                    style: styles.badgeText,
                    children: savedCount > 9 ? "9+" : savedCount
                  })
                }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                  style: styles.savedFlightContainer,
                  onPress: onPressSavedFlight,
                  activeOpacity: 0.8,
                  children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                    style: styles.buttonContent,
                    children: [(0, _jsxRuntime.jsx)(_icons.PlaneSaved, {
                      style: styles.icon
                    }), (0, _jsxRuntime.jsx)(_text.Text, {
                      tx: "flyLanding.saved",
                      preset: "textLink",
                      style: styles.text
                    })]
                  })
                })]
              })]
            }), (0, _jsxRuntime.jsx)(_search.Search, {
              ref: searchRef,
              navigation: navigation
            }), (0, _jsxRuntime.jsx)(_trackTodayFlightsBanner.default, {}), (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: {
                height: 35
              }
            }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: {
                position: "absolute",
                left: 0,
                right: 0,
                bottom: -64
              },
              children: (0, _jsxRuntime.jsx)(_cloudSvg.CloudSVG, {})
            })]
          }), (0, _jsxRuntime.jsx)(_flyLandingSections.CarouselSection, {
            ref: carouselRef
          }), (0, _jsxRuntime.jsx)(_flyLandingSections.QuickLinksSection, {
            loading: isLoadingFlyQuicklinks,
            quickLinks: flyQuicklinks == null ? undefined : flyQuicklinks.shortcutLinks,
            errorShortcutLinks: isErrorFlyQuicklinks
          }), (0, _jsxRuntime.jsx)(_airportInfo.default, {})]
        }), (0, _jsxRuntime.jsx)(_flyLandingAnimatedHeader.FlyLandingAnimatedHeader, {
          scrollY: scrollY,
          onSearchPress: handleNavigateToGlobalSearch,
          onSavedFlightPress: onPressSavedFlight,
          isShowTickerband: isShowTickerband
        })]
      })]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    noInternetContainer: {
      flex: 1,
      alignItems: "stretch",
      justifyContent: "center",
      marginBottom: 90,
      // Bottom tab bar height
      paddingHorizontal: 24
    },
    headerContainer: {
      alignSelf: "stretch",
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingHorizontal: 25,
      paddingVertical: 12
    },
    savedFlightContainer: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: _color.color.palette.lightPurple,
      borderRadius: 40,
      width: 94,
      height: 32,
      borderColor: _color.color.palette.purple8F58BE,
      borderStyle: "solid",
      borderWidth: 1
    },
    buttonContent: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center"
    },
    icon: {
      backgroundColor: "transparent",
      position: "relative",
      left: 7,
      marginRight: 6
    },
    text: {
      color: _color.color.palette.whiteGrey,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      fontSize: 16
    },
    badge: {
      position: "absolute",
      left: -6,
      top: -6,
      backgroundColor: _color.color.palette.whiteGrey,
      borderColor: _color.color.palette.lighterPurple,
      borderWidth: 1,
      width: 22,
      height: 22,
      borderRadius: 11,
      alignItems: "center",
      justifyContent: "center",
      zIndex: 2
    },
    badgeText: {
      color: _color.color.palette.lightPurple,
      fontSize: 11,
      lineHeight: 14,
      fontWeight: _reactNative2.Platform.select({
        ios: "900",
        android: "normal"
      }),
      fontFamily: _theme.typography.bold
    }
  });
