  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.tabBarStyles = exports.loadingPageStyles = exports.lighterGreyLoadingColors = exports.crPrivilegesContentCardsStyles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var _theme = _$$_REQUIRE(_dependencyMap[2]);
  var crPrivilegesContentCardsStyles = exports.crPrivilegesContentCardsStyles = _reactNative.StyleSheet.create({
    scrollViewContainerStyle: {
      flex: 1,
      backgroundColor: _theme.color.palette.almostWhiteGrey
    },
    flexRow: {
      display: "flex",
      flexDirection: "row",
      alignItems: "center"
    },
    contentCards: {
      paddingHorizontal: 20,
      marginVertical: 24
    },
    card: {
      display: "flex",
      flexDirection: "row",
      alignItems: "flex-start",
      borderBottomWidth: 1,
      borderColor: _theme.color.palette.lighterGrey,
      marginBottom: 24,
      paddingBottom: 16
    },
    cardImage: {
      width: 48,
      height: 48,
      marginRight: 16
    },
    cardTitle: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey,
      marginBottom: 4
    }),
    carđDescription: Object.assign({}, _text.presets.caption1Regular),
    cardLinkContainer: {
      display: "flex",
      flexDirection: "row",
      alignItems: "center",
      marginTop: 16
    },
    cardLink: Object.assign({}, _text.presets.caption2Bold, {
      color: _theme.color.palette.lightPurple,
      fontSize: 14,
      marginRight: 10
    }),
    errorOverlay: {
      marginTop: -165
    },
    wrapContent: {
      flex: 1
    }
  });
  var tabBarStyles = exports.tabBarStyles = _reactNative.StyleSheet.create({
    headerContainer: Object.assign({
      height: 56,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      marginBottom: 0
    }, _theme.shadow.noShadow),
    backButtonHeaderStyles: {
      position: "absolute",
      left: 16,
      top: 0,
      bottom: 0,
      justifyContent: "center",
      alignItems: "center"
    },
    titleHeaderStyles: {
      color: _theme.color.palette.almostBlackGrey,
      textAlign: "center"
    },
    topTabParentStyle: Object.assign({
      backgroundColor: _theme.color.palette.whiteGrey,
      flexDirection: "row",
      height: 72,
      justifyContent: "center"
    }, _theme.shadow.secondaryShadow, {
      alignItems: "flex-end",
      elevation: 10
    }),
    topTabTouchableOpacityStyle: {
      alignItems: "center",
      height: "auto",
      flex: 1
    },
    topTabActiveIndicatorStyle: {
      backgroundColor: _theme.color.palette.lightPurple,
      height: 2
    },
    topTabInActiveIndicatorStyle: {
      backgroundColor: _theme.color.palette.lighterGrey,
      height: 1
    },
    topTabActiveStyle: Object.assign({}, _text.presets.caption2Bold, {
      color: _theme.color.palette.almostBlackGrey,
      marginTop: 8
    }),
    topTabInActiveStyle: Object.assign({}, _text.presets.caption2Bold, {
      color: _theme.color.palette.darkGrey999,
      marginTop: 8
    }),
    topIconInActiveStyle: {
      opacity: 0.5
    },
    topIconActiveStyle: {
      opacity: 1
    }
  });
  var loadingPageStyles = exports.loadingPageStyles = _reactNative.StyleSheet.create({
    loadingStyle: {
      backgroundColor: _theme.color.palette.lightGrey,
      borderRadius: 4,
      height: 12,
      marginBottom: 8
    }
  });
  var lighterGreyLoadingColors = exports.lighterGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
