  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _native = _$$_REQUIRE(_dependencyMap[6]);
  var _validate = _$$_REQUIRE(_dependencyMap[7]);
  var _styles = _$$_REQUIRE(_dependencyMap[8]);
  var _icons = _$$_REQUIRE(_dependencyMap[9]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[10]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[11]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[14]);
  var _loadingCrPage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _errorOverlay = _$$_REQUIRE(_dependencyMap[16]);
  var _focusStatusBar = _$$_REQUIRE(_dependencyMap[17]);
  var _aemRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[18]));
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[19]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[20]);
  var _constants = _$$_REQUIRE(_dependencyMap[21]);
  var _adobe = _$$_REQUIRE(_dependencyMap[22]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[23]);
  var _theme = _$$_REQUIRE(_dependencyMap[24]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[25]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[26]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var CrPrivilegesContentCards = function CrPrivilegesContentCards(_ref) {
    var _useSelector, _useSelector2, _useSelector3;
    var testID = _ref.testID,
      accessibilityLabel = _ref.accessibilityLabel,
      tier = _ref.tier,
      handleScroll = _ref.handleScroll;
    var navigation = (0, _native.useNavigation)();
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)(),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var crPrivilegesContentCardsData = (_useSelector = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.CR_PRIVILEGES_CONTENT_CARD))) == null || (_useSelector = _useSelector.data) == null ? undefined : _useSelector.list;
    var loadingPage = (_useSelector2 = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.CR_PRIVILEGES_CONTENT_CARD))) == null ? undefined : _useSelector2.loading;
    var errorPage = (_useSelector3 = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.CR_PRIVILEGES_CONTENT_CARD))) == null ? undefined : _useSelector3.error;
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var crPrivilegesContentCards = (0, _react.useMemo)(function () {
      if ((0, _validate.isArray)(crPrivilegesContentCardsData) && tier) {
        var _crPrivilegesContentC;
        return crPrivilegesContentCardsData == null || (_crPrivilegesContentC = crPrivilegesContentCardsData.find(function (ele) {
          return (ele == null ? undefined : ele.tier) === tier;
        })) == null ? undefined : _crPrivilegesContentC.privileges;
      }
      return [];
    }, [crPrivilegesContentCardsData, tier]);
    var _useState = (0, _react.useState)(true),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isConnected = _useState2[0],
      setConnected = _useState2[1];
    var checkConnection = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnectedNetInfo = _yield$NetInfo$fetch.isConnected;
        setConnected(isConnectedNetInfo);
      });
      return function checkConnection() {
        return _ref2.apply(this, arguments);
      };
    }();
    var retry = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
          isConnectedNetInfo = _yield$NetInfo$fetch2.isConnected;
        setConnected(isConnectedNetInfo);
        if (isConnectedNetInfo) {
          dispatch(_aemRedux.default.getAemConfigData({
            name: _aemRedux.AEM_PAGE_NAME.CR_PRIVILEGES_CONTENT_CARD,
            pathName: "getCrPrivilegesContentCards",
            forceRequest: true
          }));
        }
      });
      return function retry() {
        return _ref3.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      checkConnection();
    }, []);
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      setTimeout(function () {
        return dispatch(_aemRedux.default.getAemConfigData({
          name: _aemRedux.AEM_PAGE_NAME.CR_PRIVILEGES_CONTENT_CARD,
          pathName: "getCrPrivilegesContentCards"
        }));
      }, 100);
      return function () {
        dispatch(_aemRedux.default.clearAemConfigData(_aemRedux.AEM_PAGE_NAME.CR_PRIVILEGES_CONTENT_CARD));
      };
    }, []));
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)(_constants.TrackingScreenName.ChangiRewardsPrivileges);
    (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
      (0, _adobe.commonTrackingScreen)(_constants.TrackingScreenName.ChangiRewardsPrivileges, (0, _screenHook.getPreviousScreen)(), isLoggedIn);
    }, [navigation, isLoggedIn]));
    var onCtaPress = function onCtaPress(cta) {
      var _cta$navigation, _cta$navigation2;
      var fallbackURL = "https://www.ishopchangi.com/en/pages/changi-rewards/shop-with-points?utm_source=ichangi_app&utm_medium=member-page&utm_content=shop_with_points";
      handleNavigation(cta == null || (_cta$navigation = cta.navigation) == null ? undefined : _cta$navigation.type, cta == null || (_cta$navigation2 = cta.navigation) == null ? undefined : _cta$navigation2.value, Object.assign({}, cta == null ? undefined : cta.redirect, {
        fallbackURL: fallbackURL
      }), {
        filters: cta == null ? undefined : cta.filterOptions
      });
    };
    var CrPrivilegesCard = function CrPrivilegesCard(_ref4) {
      var _item$cta, _item$secondCta;
      var item = _ref4.item,
        index = _ref4.index;
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: Object.assign({}, _styles.crPrivilegesContentCardsStyles.card, {
          borderBottomWidth: index + 1 !== crPrivilegesContentCards.length ? 1 : 0
        }),
        children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
          style: _styles.crPrivilegesContentCardsStyles.cardImage,
          source: {
            uri: (0, _mediaHelper.handleImageUrl)(item == null ? undefined : item.icon)
          },
          resizeMode: "cover"
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _styles.crPrivilegesContentCardsStyles.wrapContent,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: _styles.crPrivilegesContentCardsStyles.cardTitle,
            children: item == null ? undefined : item.name
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: _styles.crPrivilegesContentCardsStyles.carđDescription,
            children: item == null ? undefined : item.description
          }), !(0, _validate.isEmpty)(item == null ? undefined : item.cta) && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: function onPress() {
              return onCtaPress(item == null ? undefined : item.cta);
            },
            style: _styles.crPrivilegesContentCardsStyles.cardLinkContainer,
            children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: Object.assign({}, _styles.crPrivilegesContentCardsStyles.flexRow, {
                justifyContent: "center"
              }),
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                style: _styles.crPrivilegesContentCardsStyles.cardLink,
                children: (_item$cta = item.cta) == null ? undefined : _item$cta.text
              }), (0, _jsxRuntime.jsx)(_icons.ArrowRightV2, {
                width: 10,
                height: 10,
                color: _theme.color.palette.lightPurple
              })]
            })
          }), !(0, _validate.isEmpty)(item == null ? undefined : item.secondCta) && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: function onPress() {
              return onCtaPress(item == null ? undefined : item.secondCta);
            },
            style: _styles.crPrivilegesContentCardsStyles.cardLinkContainer,
            children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: Object.assign({}, _styles.crPrivilegesContentCardsStyles.flexRow, {
                justifyContent: "center"
              }),
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                style: _styles.crPrivilegesContentCardsStyles.cardLink,
                children: (_item$secondCta = item.secondCta) == null ? undefined : _item$secondCta.text
              }), (0, _jsxRuntime.jsx)(_icons.ArrowRightV2, {
                width: 10,
                height: 10,
                color: _theme.color.palette.lightPurple
              })]
            })
          })]
        })]
      });
    };
    if (!isConnected) {
      return (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        onReload: retry,
        header: false,
        noInternetOverlayStyle: Object.assign({}, _styles.crPrivilegesContentCardsStyles.scrollViewContainerStyle, _styles.crPrivilegesContentCardsStyles.errorOverlay),
        headerBackgroundColor: "transparent",
        testID: `${testID}__ErrorOverlayNoConnection`,
        accessibilityLabel: `${accessibilityLabel}__ErrorOverlayNoConnection`,
        visible: !isConnected
      });
    }
    if (!(0, _validate.isEmpty)(errorPage)) {
      return (0, _jsxRuntime.jsx)(_errorOverlay.ErrorOverlay, {
        reload: true,
        header: false,
        hideScreenHeader: true,
        visible: true,
        onReload: retry,
        overlayStyle: Object.assign({}, _styles.crPrivilegesContentCardsStyles.scrollViewContainerStyle, _styles.crPrivilegesContentCardsStyles.errorOverlay),
        variant: _errorOverlay.ErrorOverlayVariant.VARIANT2,
        testID: `${testID}__ErrorOverlay`,
        accessibilityLabel: `${accessibilityLabel}__ErrorOverlay`,
        extendCode: _errorOverlay.ERROR_HANDLING_CODE.ERROR_PAGE_LEVEL
      });
    }
    return (0, _jsxRuntime.jsxs)(_react.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_focusStatusBar.FocusAwareStatusBar, {
        translucent: true,
        backgroundColor: "transparent",
        barStyle: "dark-content"
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _styles.crPrivilegesContentCardsStyles.scrollViewContainerStyle,
        children: loadingPage ? (0, _jsxRuntime.jsx)(_loadingCrPage.default, {}) : (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _styles.crPrivilegesContentCardsStyles.contentCards,
          children: (0, _validate.isArray)(crPrivilegesContentCards) && crPrivilegesContentCards.length > 0 && (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
            data: crPrivilegesContentCards,
            numColumns: 1,
            renderItem: function renderItem(_ref5) {
              var item = _ref5.item,
                index = _ref5.index;
              return (0, _jsxRuntime.jsx)(CrPrivilegesCard, {
                item: item,
                index: index
              });
            },
            showsVerticalScrollIndicator: false,
            keyExtractor: function keyExtractor(item) {
              return item == null ? undefined : item.name;
            },
            onScroll: handleScroll,
            bounces: false
          })
        })
      })]
    });
  };
  var _default = exports.default = _react.default.memo(CrPrivilegesContentCards);
