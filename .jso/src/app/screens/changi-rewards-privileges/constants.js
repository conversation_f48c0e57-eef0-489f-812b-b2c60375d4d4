  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.SCREEN_NAME = exports.MAP_TAB_BY_TIER = exports.DURATION = exports.ANIMATION_VALUE = exports.ANDROID_TAB_LABEL_DURATION = exports.ANDROID_DURATION = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _enum = _$$_REQUIRE(_dependencyMap[2]);
  var _constants = _$$_REQUIRE(_dependencyMap[3]);
  var DURATION = exports.DURATION = 185;
  var ANDROID_DURATION = exports.ANDROID_DURATION = 50;
  var ANDROID_TAB_LABEL_DURATION = exports.ANDROID_TAB_LABEL_DURATION = 0;
  var SCREEN_NAME = exports.SCREEN_NAME = "ChangiRewardsPrivilegesScreen";
  var MAP_TAB_BY_TIER = exports.MAP_TAB_BY_TIER = (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({}, _enum.Tier.Member, _constants.NavigationConstants.changiRewardsPrivilegesMemberTab), _enum.Tier.StaffMember, _constants.NavigationConstants.changiRewardsPrivilegesMemberTab), _enum.Tier.Gold, _constants.NavigationConstants.changiRewardsPrivilegesGoldTab), _enum.Tier.StaffGold, _constants.NavigationConstants.changiRewardsPrivilegesGoldTab), _enum.Tier.Platinum, _constants.NavigationConstants.changiRewardsPrivilegesPlatinumTab), _enum.Tier.StaffPlatinum, _constants.NavigationConstants.changiRewardsPrivilegesPlatinumTab), _enum.Tier.Monarch, _constants.NavigationConstants.changiRewardsPrivilegesMonarchTab), _enum.Tier.StaffMonarch, _constants.NavigationConstants.changiRewardsPrivilegesMonarchTab);
  var ANIMATION_VALUE = exports.ANIMATION_VALUE = {
    SCALE_ICON: {
      START_VALUE: 1,
      END_VALUE: 0
    },
    HEIGHT: {
      START_VALUE: 72,
      END_VALUE: 40
    },
    TAB_LABEL: {
      START_VALUE: 0,
      END_VALUE: -15
    }
  };
