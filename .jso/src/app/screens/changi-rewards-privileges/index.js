  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _native = _$$_REQUIRE(_dependencyMap[3]);
  var _validate = _$$_REQUIRE(_dependencyMap[4]);
  var _styles = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _i18n = _$$_REQUIRE(_dependencyMap[7]);
  var _materialTopTabs = _$$_REQUIRE(_dependencyMap[8]);
  var _icons = _$$_REQUIRE(_dependencyMap[9]);
  var _constants = _$$_REQUIRE(_dependencyMap[10]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[11]);
  var _text = _$$_REQUIRE(_dependencyMap[12]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[13]);
  var _crPrivilegesContentCards = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _enum = _$$_REQUIRE(_dependencyMap[15]);
  var _constants2 = _$$_REQUIRE(_dependencyMap[16]);
  var _reactNativeReanimated = _$$_REQUIRE(_dependencyMap[17]);
  var _animatedTopTab = _$$_REQUIRE(_dependencyMap[18]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[19]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var ChangiRewardsPrivilegesScreen = function ChangiRewardsPrivilegesScreen() {
    var RedeemTab = (0, _materialTopTabs.createMaterialTopTabNavigator)();
    var navigation = (0, _native.useNavigation)();
    var _useRoute = (0, _native.useRoute)(),
      params = _useRoute.params;
    var activeTab = _constants2.MAP_TAB_BY_TIER[params == null ? undefined : params.tierMember] || _constants.NavigationConstants.changiRewardsPrivilegesMemberTab;
    var inset = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    (0, _react.useEffect)(function () {
      if (!(0, _validate.isEmpty)(navigation)) {
        navigation.setOptions({
          headerShown: false
        });
      }
    }, [navigation]);
    var animatedProgress = (0, _reactNativeReanimated.useSharedValue)(0);
    var handleScroll = function handleScroll(event) {
      if (event.nativeEvent.contentOffset.y > 10) {
        // hide top tabs
        animatedProgress.value = (0, _reactNativeReanimated.withTiming)(1);
      } else {
        // show top tabs
        animatedProgress.value = (0, _reactNativeReanimated.withTiming)(0);
      }
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: {
        flex: 1,
        backgroundColor: _theme.color.palette.whiteGrey
      },
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: [_styles.tabBarStyles.headerContainer, {
          marginTop: inset == null ? undefined : inset.top
        }],
        children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: _styles.tabBarStyles.backButtonHeaderStyles,
          onPress: function onPress() {
            navigation.goBack();
          },
          hitSlop: {
            top: 5,
            left: 5,
            right: 5,
            bottom: 5
          },
          children: (0, _jsxRuntime.jsx)(_icons.ArrowLeftGray, {
            width: 24,
            height: 24
          })
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          tx: "changiRewardsPrivileges.headerTitle",
          preset: "bodyTextBold",
          style: _styles.tabBarStyles.titleHeaderStyles
        })]
      }), (0, _jsxRuntime.jsxs)(RedeemTab.Navigator, {
        initialRouteName: activeTab,
        backBehavior: "none",
        tabBar: function tabBar(props) {
          return (0, _jsxRuntime.jsx)(_animatedTopTab.TabNavBarWithTopIconAnimationV2, {
            props: Object.assign({}, props),
            isCenter: true,
            topTabParentStyle: Object.assign({}, _styles.tabBarStyles.topTabParentStyle),
            topIconActiveStyle: _styles.tabBarStyles.topIconActiveStyle,
            topIconInActiveStyle: _styles.tabBarStyles.topIconInActiveStyle,
            topTabTouchableOpacityStyle: _styles.tabBarStyles.topTabTouchableOpacityStyle,
            topTabActiveIndicatorStyle: _styles.tabBarStyles.topTabActiveIndicatorStyle,
            topTabActiveLabelStyle: _styles.tabBarStyles.topTabActiveStyle,
            topTabInActiveLabelStyle: _styles.tabBarStyles.topTabInActiveStyle,
            topTabInActiveIndicatorStyle: _styles.tabBarStyles.topTabInActiveIndicatorStyle,
            animatedProgress: animatedProgress
          });
        },
        children: [(0, _jsxRuntime.jsx)(RedeemTab.Screen, {
          name: _constants.NavigationConstants.changiRewardsPrivilegesMemberTab,
          options: {
            swipeEnabled: false,
            tabBarLabel: (0, _i18n.translate)("changiRewardsPrivileges.memberTabTitle"),
            tabBarIcon: _icons.ChangiRewardsPrivilegesMemberIcon,
            tabBarTestID: `${_constants2.SCREEN_NAME}__${_constants.NavigationConstants.changiRewardsPrivilegesMemberTab}`,
            tabBarAccessibilityLabel: `${_constants2.SCREEN_NAME}__${_constants.NavigationConstants.changiRewardsPrivilegesMemberTab}`
          },
          children: function children(props) {
            return (0, _jsxRuntime.jsx)(_crPrivilegesContentCards.default, Object.assign({}, props, {
              tier: _enum.Tier.Member,
              testID: `${_constants2.SCREEN_NAME}__${_constants.NavigationConstants.changiRewardsPrivilegesMemberTab}`,
              accessibilityLabel: `${_constants2.SCREEN_NAME}__${_constants.NavigationConstants.changiRewardsPrivilegesMemberTab}`,
              handleScroll: handleScroll
            }));
          }
        }), (0, _jsxRuntime.jsx)(RedeemTab.Screen, {
          name: _constants.NavigationConstants.changiRewardsPrivilegesGoldTab,
          options: {
            swipeEnabled: false,
            tabBarLabel: (0, _i18n.translate)("changiRewardsPrivileges.goldTabTitle"),
            tabBarIcon: _icons.ChangiRewardsPrivilegesGoldIcon,
            tabBarTestID: `${_constants2.SCREEN_NAME}__${_constants.NavigationConstants.changiRewardsPrivilegesGoldTab}`,
            tabBarAccessibilityLabel: `${_constants2.SCREEN_NAME}__${_constants.NavigationConstants.changiRewardsPrivilegesGoldTab}`
          },
          children: function children(props) {
            return (0, _jsxRuntime.jsx)(_crPrivilegesContentCards.default, Object.assign({}, props, {
              tier: _enum.Tier.Gold,
              testID: `${_constants2.SCREEN_NAME}__${_constants.NavigationConstants.changiRewardsPrivilegesGoldTab}`,
              accessibilityLabel: `${_constants2.SCREEN_NAME}__${_constants.NavigationConstants.changiRewardsPrivilegesGoldTab}`,
              handleScroll: handleScroll
            }));
          }
        }), (0, _jsxRuntime.jsx)(RedeemTab.Screen, {
          name: _constants.NavigationConstants.changiRewardsPrivilegesPlatinumTab,
          options: {
            swipeEnabled: false,
            tabBarLabel: (0, _i18n.translate)("changiRewardsPrivileges.platinumTabTitle"),
            tabBarIcon: _icons.ChangiRewardsPrivilegesPlatinumIcon,
            tabBarTestID: `${_constants2.SCREEN_NAME}__${_constants.NavigationConstants.changiRewardsPrivilegesPlatinumTab}`,
            tabBarAccessibilityLabel: `${_constants2.SCREEN_NAME}__${_constants.NavigationConstants.changiRewardsPrivilegesPlatinumTab}`
          },
          children: function children(props) {
            return (0, _jsxRuntime.jsx)(_crPrivilegesContentCards.default, Object.assign({}, props, {
              tier: _enum.Tier.Platinum,
              testID: `${_constants2.SCREEN_NAME}__${_constants.NavigationConstants.changiRewardsPrivilegesPlatinumTab}`,
              accessibilityLabel: `${_constants2.SCREEN_NAME}__${_constants.NavigationConstants.changiRewardsPrivilegesPlatinumTab}`,
              handleScroll: handleScroll
            }));
          }
        }), (0, _jsxRuntime.jsx)(RedeemTab.Screen, {
          name: _constants.NavigationConstants.changiRewardsPrivilegesMonarchTab,
          options: {
            swipeEnabled: false,
            tabBarLabel: (0, _i18n.translate)("changiRewardsPrivileges.monarchTabTitle"),
            tabBarIcon: _icons.ChangiRewardsPrivilegesMonarchIcon,
            tabBarTestID: `${_constants2.SCREEN_NAME}__${_constants.NavigationConstants.changiRewardsPrivilegesMonarchTab}`,
            tabBarAccessibilityLabel: `${_constants2.SCREEN_NAME}__${_constants.NavigationConstants.changiRewardsPrivilegesMonarchTab}`
          },
          children: function children(props) {
            return (0, _jsxRuntime.jsx)(_crPrivilegesContentCards.default, Object.assign({}, props, {
              tier: _enum.Tier.Monarch,
              testID: `${_constants2.SCREEN_NAME}__${_constants.NavigationConstants.changiRewardsPrivilegesMonarchTab}`,
              accessibilityLabel: `${_constants2.SCREEN_NAME}__${_constants.NavigationConstants.changiRewardsPrivilegesMonarchTab}`,
              handleScroll: handleScroll
            }));
          }
        })]
      })]
    });
  };
  var _default = exports.default = ChangiRewardsPrivilegesScreen;
