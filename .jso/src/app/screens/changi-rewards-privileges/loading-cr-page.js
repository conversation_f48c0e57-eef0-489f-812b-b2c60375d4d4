  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _constants = _$$_REQUIRE(_dependencyMap[2]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _styles = _$$_REQUIRE(_dependencyMap[5]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var LoadingCrPage = function LoadingCrPage() {
    return (0, _jsxRuntime.jsx)(_react.Fragment, {
      children: (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: _styles.crPrivilegesContentCardsStyles.contentCards,
        children: (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
          data: [1, 2, 3, 4, 5],
          numColumns: 1,
          renderItem: function renderItem(_ref) {
            var index = _ref.index;
            return (0, _jsxRuntime.jsxs)(_reactNative.View, {
              style: Object.assign({}, _styles.crPrivilegesContentCardsStyles.card, {
                borderBottomWidth: index !== 4 ? 1 : 0
              }),
              children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                shimmerColors: _styles.lighterGreyLoadingColors,
                shimmerStyle: Object.assign({}, _styles.loadingPageStyles.loadingStyle, _styles.crPrivilegesContentCardsStyles.cardImage, {
                  borderRadius: 12
                })
              }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
                style: {
                  flex: 1
                },
                children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                  duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                  shimmerColors: _styles.lighterGreyLoadingColors,
                  shimmerStyle: Object.assign({}, _styles.loadingPageStyles.loadingStyle, {
                    width: 74
                  })
                }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                  duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                  shimmerColors: _styles.lighterGreyLoadingColors,
                  shimmerStyle: Object.assign({}, _styles.loadingPageStyles.loadingStyle, {
                    width: "100%"
                  })
                })]
              })]
            });
          },
          showsVerticalScrollIndicator: false,
          keyExtractor: function keyExtractor(item) {
            return item.toString();
          }
        })
      })
    });
  };
  var _default = exports.default = LoadingCrPage;
