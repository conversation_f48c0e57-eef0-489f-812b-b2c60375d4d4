  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var styles = _reactNative.StyleSheet.create({
    container: {
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1
    },
    containerHeader: {
      display: "flex",
      flexDirection: "row",
      justifyContent: "space-between",
      paddingHorizontal: 12,
      position: "absolute",
      top: "23%",
      width: "100%"
    },
    containerStyle: {
      backgroundColor: _theme.color.palette.lightestGrey
    },
    imageBackground: {
      height: "100%",
      width: "100%"
    },
    imageContainer: {
      width: "100%",
      height: 250
    },
    leftHeader: {
      width: 70
    },
    rightHeader: {
      display: "flex",
      flexDirection: "row",
      width: 70
    },
    shareIcon: {
      marginRight: 12
    },
    title: {
      color: _theme.color.palette.whiteGrey,
      fontSize: 18,
      fontFamily: _theme.typography.bold,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      textAlign: "center",
      width: 180
    }
  });
  var _default = exports.default = styles;
