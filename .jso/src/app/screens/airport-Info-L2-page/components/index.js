  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _native = _$$_REQUIRE(_dependencyMap[6]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _lodash = _$$_REQUIRE(_dependencyMap[9]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[10]);
  var _icons = _$$_REQUIRE(_dependencyMap[11]);
  var _aemRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[12]));
  var _utils = _$$_REQUIRE(_dependencyMap[13]);
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[14]);
  var _errorOverlay = _$$_REQUIRE(_dependencyMap[15]);
  var _airportInfoL2Content = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _airportInfoL = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _text = _$$_REQUIRE(_dependencyMap[18]);
  var _baseImageBackground = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[19]));
  var _fly = _$$_REQUIRE(_dependencyMap[20]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[21]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[22]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var AirportInfoL2Screen = function AirportInfoL2Screen(props) {
    var _useContext$Handlers, _useContext;
    var _props$testID = props.testID,
      testID = _props$testID === undefined ? "AirportInfoL2Screen__" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "AirportInfoL2Screen__" : _props$accessibilityL,
      name = props.name,
      pathName = props.pathName,
      parameters = props.parameters;
    var navigation = (0, _native.useNavigation)();
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isNoConnection = _useState2[0],
      setIsNoConnection = _useState2[1];
    var _ref = (_useContext$Handlers = (_useContext = (0, _react.useContext)(_fly.FLY_CONTEXT)) == null ? undefined : _useContext.Handlers) != null ? _useContext$Handlers : {},
      flyLandingFeatureFlag = _ref.flyLandingFeatureFlag;
    var isFlyLandingV2 = (0, _remoteConfig.getFeatureFlagInit)(_remoteConfig.REMOTE_CONFIG_FLAGS.FLY_LANDING, flyLandingFeatureFlag);
    var aemData = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(name));
    var loading = (0, _lodash.get)(aemData, "loading");
    var error = (0, _lodash.get)(aemData, "error");
    var airportInfoL2Data = isFlyLandingV2 ? (0, _lodash.get)(aemData, "data.list.0") : (0, _lodash.get)(aemData, "data");
    var sectionsData = (0, _lodash.get)(airportInfoL2Data, "sections");
    var title = airportInfoL2Data == null ? undefined : airportInfoL2Data.title;
    var imageUrl = airportInfoL2Data == null ? undefined : airportInfoL2Data.image;
    var fetchData = (0, _react.useCallback)(function () {
      dispatch(_aemRedux.default.getAemConfigData({
        name: name,
        pathName: pathName,
        parameters: isFlyLandingV2 ? {
          id: encodeURIComponent(parameters.id)
        } : parameters,
        forceRequest: isFlyLandingV2
      }));
    }, [dispatch]);
    (0, _react.useEffect)(function () {
      var checkInternet = /*#__PURE__*/function () {
        var _ref2 = (0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch.isConnected;
          if (!isConnected) {
            setIsNoConnection(true);
          } else {
            fetchData();
          }
        });
        return function checkInternet() {
          return _ref2.apply(this, arguments);
        };
      }();
      checkInternet();
    }, [fetchData]);
    if (isNoConnection) {
      return (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        header: true,
        headerBackgroundColor: "transparent",
        hideScreenHeader: false,
        visible: true,
        onBack: function onBack() {
          return navigation.goBack();
        },
        testID: `${testID}__ErrorOverlayNoConnection`,
        onReload: function onReload() {
          var onReload = /*#__PURE__*/function () {
            var _ref3 = (0, _asyncToGenerator2.default)(function* () {
              var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
                isConnected = _yield$NetInfo$fetch2.isConnected;
              if (isConnected) {
                fetchData();
                setIsNoConnection(false);
              }
            });
            return function onReload() {
              return _ref3.apply(this, arguments);
            };
          }();
          onReload();
        }
      });
    }
    if (loading) {
      return (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
        visible: true,
        isPopUp: false
      });
    }
    if (error) {
      return (0, _jsxRuntime.jsx)(_errorOverlay.ErrorOverlay, {
        reload: true,
        header: true,
        hideScreenHeader: false,
        headerTx: "errorOverlay.header",
        visible: true,
        onReload: fetchData,
        onBack: function onBack() {
          navigation.goBack();
        },
        testID: `${testID}__ErrorOverlay`,
        accessibilityLabel: `${accessibilityLabel}__ErrorOverlay`,
        variant: _errorOverlay.ErrorOverlayVariant.VARIANT1
      });
    }
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: _airportInfoL.default.container,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.ScrollView, {
        showsVerticalScrollIndicator: false,
        contentContainerStyle: _airportInfoL.default.containerStyle,
        testID: `${testID}__ScrollView`,
        accessibilityLabel: `${accessibilityLabel}__ScrollView`,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _airportInfoL.default.imageContainer,
          children: (0, _jsxRuntime.jsx)(_baseImageBackground.default, {
            resizeMode: "cover",
            imageStyle: _airportInfoL.default.imageBackground,
            source: {
              uri: (0, _utils.mappingUrlAem)(imageUrl)
            },
            children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _airportInfoL.default.containerHeader,
              children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: _airportInfoL.default.leftHeader,
                children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                  onPress: function onPress() {
                    return navigation.goBack();
                  },
                  testID: `${testID}__TouchableBack`,
                  accessibilityLabel: `${accessibilityLabel}__TouchableBack`,
                  children: (0, _jsxRuntime.jsx)(_icons.BackButton, {})
                })
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                style: _airportInfoL.default.title,
                numberOfLines: 1,
                text: title
              }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: _airportInfoL.default.rightHeader
              })]
            })
          })
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          children: (0, _jsxRuntime.jsx)(_airportInfoL2Content.default, {
            sections: sectionsData
          })
        })]
      })
    });
  };
  var _default = exports.default = AirportInfoL2Screen;
