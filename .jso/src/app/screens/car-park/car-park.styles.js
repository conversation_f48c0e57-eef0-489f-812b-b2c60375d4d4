  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    width = _Dimensions$get.width,
    height = _Dimensions$get.height;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    carParkHeader: {
      color: _theme.color.palette.whiteGrey,
      width: "65%",
      alignItems: "center",
      justifyContent: "center",
      textAlign: "center"
    },
    container: {
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1
    },
    contentContainerStyle: {
      backgroundColor: _theme.color.palette.lightestGrey
    },
    heroImage: {
      height: 250,
      width: width
    },
    scrollView: {
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1
    },
    space: {
      width: 20
    },
    tabBarLabel: {},
    textLabelTabBarActive: {
      color: _theme.color.palette.lightPurple
    },
    textLabelTabBarInActive: {
      color: _theme.color.palette.darkestGrey
    },
    wrapHeaderCarPark: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.transparent,
      flexDirection: "row",
      justifyContent: "space-between",
      paddingHorizontal: 20,
      paddingTop: 60,
      position: "absolute",
      width: "100%"
    },
    wrapLeftAction: {
      width: "15%"
    },
    wrapRightAction: {
      flexDirection: "row",
      width: "15%"
    }
  });
