  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    buttonSelectTerminal: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      borderColor: _theme.color.palette.lightGrey,
      borderRadius: 12,
      borderWidth: 1,
      flexDirection: "row",
      justifyContent: "space-between",
      marginTop: 9,
      paddingHorizontal: 16,
      paddingVertical: 12
    },
    caption1Regular: {
      marginTop: 5
    },
    checkBox: {
      marginTop: 24
    },
    contentButtonSelectTerminal: {
      color: _theme.color.palette.almostBlackGrey
    },
    contentCaculatorTab: {
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1,
      marginBottom: 50,
      paddingBottom: 24,
      paddingHorizontal: 24,
      paddingTop: 30
    },
    contentTotalFee: {
      alignItems: "flex-end",
      flexDirection: "row",
      justifyContent: "space-between"
    },
    description: {
      marginTop: 8
    },
    errorUnplannedMaintenance: {
      marginTop: 30
    },
    h1: {
      color: _theme.color.palette.almostBlackGrey
    },
    selectSpecifyDay: {
      marginTop: 42
    },
    stepValue: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "space-between"
    },
    textCheckBox: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.almostBlackGrey
    }),
    thumb: {
      backgroundColor: _theme.color.palette.lightPurple,
      borderRadius: 12,
      height: 24,
      width: 24
    },
    thumbInactive: {
      backgroundColor: _theme.color.palette.lightestPurple,
      borderRadius: 12,
      height: 24,
      width: 24
    },
    titleDropdown: {
      color: _theme.color.palette.almostBlackGrey,
      marginBottom: 9,
      marginTop: 30
    },
    titleSelectSpecifyEndDate: {
      color: _theme.color.palette.almostBlackGrey,
      marginTop: 30
    },
    titleSelectSpecifyStartDate: {
      color: _theme.color.palette.almostBlackGrey
    },
    titleTotalFee: {
      color: _theme.color.palette.almostBlackGrey,
      marginTop: 50
    },
    toolTipLeft: {
      backgroundColor: _theme.color.palette.lightPurple,
      borderRadius: 8,
      left: -15,
      padding: 8,
      position: "absolute",
      top: -43,
      width: 89
    },
    toolTipRight: {
      backgroundColor: _theme.color.palette.lightPurple,
      borderRadius: 8,
      padding: 8,
      position: "absolute",
      right: -15,
      top: -43,
      width: 89
    },
    trackMark: {
      left: 5,
      position: "absolute",
      top: 12
    },
    trackStyle: {
      backgroundColor: _theme.color.palette.lightestPurple,
      borderRadius: 4,
      height: 8
    },
    trackStyleInactive: {
      backgroundColor: _theme.color.palette.lightestPurple,
      borderRadius: 4,
      height: 8,
      opacity: 0.5
    },
    triangle: {
      backgroundColor: "transparent",
      borderBottomColor: _theme.color.palette.lightPurple,
      borderBottomWidth: 10,
      borderLeftColor: "transparent",
      borderLeftWidth: 10,
      borderRightColor: "transparent",
      borderRightWidth: 10,
      borderStyle: "solid",
      height: 0,
      left: 2,
      position: "absolute",
      top: -12,
      transform: [{
        rotate: "180deg"
      }],
      width: 0
    },
    triangleInactive: {
      backgroundColor: "transparent",
      borderBottomColor: _theme.color.palette.lightPurple,
      borderBottomWidth: 10,
      borderLeftColor: "transparent",
      borderLeftWidth: 10,
      borderRightColor: "transparent",
      borderRightWidth: 10,
      borderStyle: "solid",
      height: 0,
      left: 2,
      position: "absolute",
      top: -12,
      transform: [{
        rotate: "180deg"
      }],
      width: 0
    }
  });
