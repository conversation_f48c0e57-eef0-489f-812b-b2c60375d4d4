  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _text = _$$_REQUIRE(_dependencyMap[8]);
  var _caculator = _$$_REQUIRE(_dependencyMap[9]);
  var _reactNativeSlider = _$$_REQUIRE(_dependencyMap[10]);
  var _icons = _$$_REQUIRE(_dependencyMap[11]);
  var _checkbox = _$$_REQUIRE(_dependencyMap[12]);
  var _lodash = _$$_REQUIRE(_dependencyMap[13]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[14]);
  var _airportLandingRedux = _$$_REQUIRE(_dependencyMap[15]);
  var _selectPicker = _$$_REQUIRE(_dependencyMap[16]);
  var _reactNativeDatePicker = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[18]));
  var _adobe = _$$_REQUIRE(_dependencyMap[19]);
  var _native = _$$_REQUIRE(_dependencyMap[20]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[21]);
  var _currency = _$$_REQUIRE(_dependencyMap[22]);
  var _utils = _$$_REQUIRE(_dependencyMap[23]);
  var _errorUnplannedMaintenance = _$$_REQUIRE(_dependencyMap[24]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[25]);
  var _constants = _$$_REQUIRE(_dependencyMap[26]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[27]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[28]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[29]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[30]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  /* eslint-disable no-new-func */

  var MINIMUM_VALUE = 0;
  var MAXIMUM_VALUE = 24;
  var STEP = 1;
  var TRACK_MARKS = [0, 6, 12, 18, 24];
  var SCREEN_NAME = "CarParkCaculator__";
  var initPrice = {
    duration: 0,
    price: "0",
    durationHour: "",
    durationMinute: ""
  };
  var COMPONENT_NAME = "CarParkCaculator__";
  var WIDTH = _reactNative2.Dimensions.get("window").width;
  var TAB_HEIGHT = 600;
  var TAB_HEIGHT_WITH_MORE_TIME = 850;
  var calculateCarparkFareV1 = function calculateCarparkFareV1(durationMin, config) {
    var _fare, _fare2, _fare3;
    var fare = 0;
    var remainDuration = durationMin;
    if (config != null && config.cappedAt) {
      var spentDuration = Math.floor(remainDuration / 60 / 24);
      fare = (config == null ? undefined : config.cappedAt) * spentDuration;
      remainDuration = remainDuration - spentDuration * 60 * 24;
    }
    if (remainDuration <= 0) return (0, _currency.withCommas)((_fare = fare) == null ? undefined : _fare.toFixed(2));
    if (config != null && config.min && config != null && config.minFare) {
      var _spentDuration = Math.min(remainDuration, (config == null ? undefined : config.limitMin) || remainDuration);
      remainDuration = remainDuration - _spentDuration;
      fare = fare + (config == null ? undefined : config.minFare) / (config == null ? undefined : config.min) * _spentDuration;
    }
    if (config != null && config.cappedAt) {
      var day = Math.ceil(durationMin / 60 / 24);
      fare = Math.min(fare, (config == null ? undefined : config.cappedAt) * day);
    }
    if (remainDuration <= 0) return (0, _currency.withCommas)((_fare2 = fare) == null ? undefined : _fare2.toFixed(2));
    if (config != null && config.hour && config != null && config.hourFare) {
      var _spentDuration2 = Math.ceil(remainDuration / 60 / (config == null ? undefined : config.hour));
      fare = fare + (config == null ? undefined : config.hourFare) * _spentDuration2;
    }
    if (config != null && config.cappedAt) {
      var _day = Math.ceil(durationMin / 60 / 24);
      fare = Math.min(fare, (config == null ? undefined : config.cappedAt) * _day);
    }
    return (0, _currency.withCommas)((_fare3 = fare) == null ? undefined : _fare3.toFixed(2));
  };
  var calculateCarparkFareV2 = function calculateCarparkFareV2(durationMin, config) {
    var _ref = config || 0,
      limitMin = _ref.limitMin,
      hourFare = _ref.hourFare,
      minFare = _ref.minFare,
      min = _ref.min,
      hour = _ref.hour;
    if (config != null && config.limitMin) {
      if (durationMin <= limitMin) {
        return (0, _currency.withCommas)((Math.ceil(durationMin / min) * Number(minFare)).toFixed(2));
      } else {
        var basePrice = (minFare * (limitMin / min)).toFixed(2);
        var extendPrice = Math.ceil((durationMin - limitMin) / (hour * 60)) * hourFare;
        return (0, _currency.withCommas)((Number(basePrice) + Number(extendPrice)).toFixed(2));
      }
    } else {
      return (0, _currency.withCommas)((Math.ceil(durationMin / min) * Number(minFare)).toFixed(2));
    }
  };
  var _worklet_6623582326112_init_data = {
    code: "function caculatorTsx1(){const{animatedPosition}=this.__closure;return{transform:[{translateX:animatedPosition.value}]};}"
  };
  var CaculatorTab = function CaculatorTab(_ref2) {
    var _terminalSelected$val;
    var caculateHeight = _ref2.caculateHeight,
      enableV2 = _ref2.enableV2,
      slideAnimation = _ref2.slideAnimation,
      timeOut = _ref2.timeOut;
    var initSpecifyDate = {
      startDate: new Date(),
      endDate: new Date()
    };
    var navigation = (0, _native.useNavigation)();
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("CACULATOR"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var _useState = (0, _react.useState)(0),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      position = _useState2[0],
      setPosition = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      needShowToolTip = _useState4[0],
      setNeedShowToolTip = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      moreTime = _useState6[0],
      setMoreTime = _useState6[1];
    var _useState7 = (0, _react.useState)(initSpecifyDate),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      specifyDate = _useState8[0],
      setSpecifyDate = _useState8[1];
    var _useState9 = (0, _react.useState)([{
        label: null,
        value: null
      }]),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      terminalOption = _useState0[0],
      setTerminalOption = _useState0[1];
    var _useState1 = (0, _react.useState)({
        label: null,
        value: null
      }),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      terminalSelected = _useState10[0],
      setTerminalSelected = _useState10[1];
    var _useState11 = (0, _react.useState)(initPrice),
      _useState12 = (0, _slicedToArray2.default)(_useState11, 2),
      price = _useState12[0],
      setPrice = _useState12[1];
    var _useState13 = (0, _react.useState)(false),
      _useState14 = (0, _slicedToArray2.default)(_useState13, 2),
      startDateVisible = _useState14[0],
      setStartDateVisible = _useState14[1];
    var _useState15 = (0, _react.useState)(false),
      _useState16 = (0, _slicedToArray2.default)(_useState15, 2),
      endDateVisible = _useState16[0],
      setEndDateVisible = _useState16[1];
    var _useState17 = (0, _react.useState)(false),
      _useState18 = (0, _slicedToArray2.default)(_useState17, 2),
      disable24Hour = _useState18[0],
      setDisable24Hour = _useState18[1];
    var dataErrorMaintenanceConfig = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.GET_ERROR_MAINTENANCE));
    var listErrorMaintenance = (0, _lodash.get)(dataErrorMaintenanceConfig, "data.list", []);
    var sectionMaintenanceObj = (0, _react.useMemo)(function () {
      var data = listErrorMaintenance == null ? undefined : listErrorMaintenance.find(function (obj) {
        return obj.sectionName === _constants.sectionTagName.calculator;
      });
      return data || {};
    }, [listErrorMaintenance]);
    var getCarParkFareDetailPayload = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.getCarParkFareDetailPayload);
    (0, _react.useEffect)(function () {
      if (moreTime) {
        caculateHeight(TAB_HEIGHT_WITH_MORE_TIME);
      } else {
        caculateHeight(TAB_HEIGHT);
      }
    }, [moreTime]);
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)("Carpark_Caculator");
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)("Carpark_Caculator", (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribeFocus;
    }, [navigation]);
    (0, _react.useEffect)(function () {
      setPrice(initPrice);
      setSpecifyDate(initSpecifyDate);
      setPosition(0);
    }, [moreTime, terminalSelected]);
    (0, _react.useEffect)(function () {
      if (!(0, _lodash.isEmpty)(getCarParkFareDetailPayload) && !(terminalSelected != null && terminalSelected.value)) {
        var _getCarParkFareDetail, _getCarParkFareDetail2;
        setTerminalSelected({
          label: getCarParkFareDetailPayload == null || (_getCarParkFareDetail = getCarParkFareDetailPayload[0]) == null ? undefined : _getCarParkFareDetail.name,
          value: getCarParkFareDetailPayload == null ? undefined : getCarParkFareDetailPayload[0]
        });
        if (getCarParkFareDetailPayload && (getCarParkFareDetailPayload == null || (_getCarParkFareDetail2 = getCarParkFareDetailPayload[0]) == null ? undefined : _getCarParkFareDetail2.terminalCode.toLowerCase()) === "t1") {
          setDisable24Hour(true);
          setMoreTime(false);
        }
      }
    }, [getCarParkFareDetailPayload]);
    (0, _react.useEffect)(function () {
      if (getCarParkFareDetailPayload) {
        var terminalOptionData = getCarParkFareDetailPayload == null ? undefined : getCarParkFareDetailPayload.map(function (item) {
          return {
            value: item,
            label: item.name
          };
        });
        setTerminalOption(terminalOptionData);
      }
    }, [getCarParkFareDetailPayload]);
    var calculateCarparkFare = function calculateCarparkFare(time, config) {
      if (enableV2) {
        return calculateCarparkFareV2(time, config);
      }
      return calculateCarparkFareV1(time, config);
    };
    (0, _react.useEffect)(function () {
      if (specifyDate != null && specifyDate.startDate && specifyDate != null && specifyDate.endDate) {
        var rangeTime = Math.round(_moment.default.duration((0, _moment.default)(specifyDate == null ? undefined : specifyDate.endDate).diff((0, _moment.default)(specifyDate == null ? undefined : specifyDate.startDate))).asMinutes());
        var time = toHoursAndMinutes(rangeTime);
        var _price = calculateCarparkFare(rangeTime, terminalSelected == null ? undefined : terminalSelected.value);
        setPrice({
          duration: rangeTime,
          price: _price,
          durationHour: time[0],
          durationMinute: time[1]
        });
      }
    }, [specifyDate]);
    var handleChangePicker = function handleChangePicker(value) {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppCarparkCalculatorCarPickChosen, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppCarparkCalculatorCarPickChosen, (value == null ? undefined : value.name) || ""));
      if (value != null && value.terminalCode && (value == null ? undefined : value.terminalCode.toLowerCase()) === "t1") {
        setDisable24Hour(true);
        setMoreTime(false);
      } else {
        setDisable24Hour(false);
      }
      setTerminalSelected({
        label: value == null ? undefined : value.name,
        value: value
      });
      setPosition(0);
      setPrice(initPrice);
    };
    var handleWidth = function handleWidth(number) {
      var baseWidth = 79;
      if (number) {
        var _number$toString;
        return baseWidth + ((_number$toString = number.toString()) == null ? undefined : _number$toString.length) * 10;
      }
      return baseWidth;
    };
    var thumComponent = function thumComponent() {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        children: [needShowToolTip && !moreTime && (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: [position > 12 ? _caculator.styles.toolTipRight : _caculator.styles.toolTipLeft, {
            width: handleWidth(price == null ? undefined : price.price)
          }],
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "caption1Bold",
            children: `${price == null ? undefined : price.duration}hr = $${price == null ? undefined : price.price}`
          })
        }), needShowToolTip && !moreTime && (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: moreTime ? _caculator.styles.triangle : _caculator.styles.triangleInactive
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: moreTime ? _caculator.styles.thumbInactive : _caculator.styles.thumb
        })]
      });
    };
    var trackMark = function trackMark(index) {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _caculator.styles.trackMark,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          text: `${TRACK_MARKS[index]}h`,
          preset: "caption2Regular"
        })
      });
    };
    var handleSlider = function handleSlider(value) {
      if (value && value <= 0) return 0;
      var time = toHoursAndMinutes(value * 60);
      setPrice({
        duration: value,
        price: calculateCarparkFare(value * 60, terminalSelected == null ? undefined : terminalSelected.value),
        durationHour: time[0],
        durationMinute: time[1]
      });
      setPosition(value);
    };
    function toHoursAndMinutes(totalMinutes) {
      var minutes = totalMinutes % 60;
      var hours = Math.floor(totalMinutes / 60);
      return [`${padTo2Digits(hours)}`, `${padTo2Digits(minutes)}`];
    }
    function padTo2Digits(num) {
      return num.toString().padStart(2, "0");
    }
    var handleConfirmPickerDate = function handleConfirmPickerDate(type, date) {
      if (type === "startDate") {
        if ((0, _moment.default)(date).isAfter((0, _moment.default)(specifyDate.endDate))) {
          setSpecifyDate(Object.assign({}, specifyDate, {
            startDate: date,
            endDate: date
          }));
        } else {
          setSpecifyDate(Object.assign({}, specifyDate, {
            startDate: date
          }));
        }
      } else {
        if ((0, _moment.default)(date).isBefore((0, _moment.default)(specifyDate.startDate))) {
          setSpecifyDate(Object.assign({}, specifyDate, {
            startDate: date,
            endDate: date
          }));
        } else {
          setSpecifyDate(Object.assign({}, specifyDate, {
            endDate: date
          }));
        }
      }
      handleCancelPickerDate();
    };
    var handleCancelPickerDate = function handleCancelPickerDate() {
      if (endDateVisible) {
        setEndDateVisible(false);
      } else {
        setStartDateVisible(false);
      }
    };
    var handleButtonOnPress = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* (navigationObj, redirect) {
        var type = navigationObj.type,
          value = navigationObj.value;
        if (!type || !value) return;
        handleNavigation(type, value, redirect);
      });
      return function handleButtonOnPress(_x, _x2) {
        return _ref3.apply(this, arguments);
      };
    }();
    var animatedPosition = (0, _reactNativeReanimated.useSharedValue)(slideAnimation === "RTL" ? WIDTH : -WIDTH);
    var animatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var caculatorTsx1 = function caculatorTsx1() {
        return {
          transform: [{
            translateX: animatedPosition.value
          }]
        };
      };
      caculatorTsx1.__closure = {
        animatedPosition: animatedPosition
      };
      caculatorTsx1.__workletHash = 6623582326112;
      caculatorTsx1.__initData = _worklet_6623582326112_init_data;
      return caculatorTsx1;
    }());
    (0, _react.useEffect)(function () {
      animatedPosition.value = (0, _reactNativeReanimated.withTiming)(0, {
        duration: timeOut
      });
    }, []);
    if (!(0, _lodash.isEmpty)(sectionMaintenanceObj) && sectionMaintenanceObj != null && sectionMaintenanceObj.enableMode) {
      return (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
        style: [_caculator.styles.errorUnplannedMaintenance, animatedStyle],
        children: (0, _jsxRuntime.jsx)(_errorUnplannedMaintenance.ErrorUnplannedMaintenance, {
          header: sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.header,
          subHeader: sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.subHeader,
          icon: sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.icon,
          buttonLabel: sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.buttonLabel,
          buttonLabel2: sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.buttonLabel2,
          onFirstButtonPress: function onFirstButtonPress() {
            return handleButtonOnPress(sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.navigationFirst, sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.redirectFirst);
          },
          onSecondButtonPress: function onSecondButtonPress() {
            return handleButtonOnPress(sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.navigationSecond, sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.redirectSecond);
          },
          testID: `${COMPONENT_NAME}__ErrorUnplannedMaintenance`,
          accessibilityLabel: `${COMPONENT_NAME}__ErrorUnplannedMaintenance`
        })
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
      style: [_caculator.styles.contentCaculatorTab, animatedStyle],
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        preset: "h4",
        tx: "carParkScreen.caculatorTab.title"
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        preset: "caption1Regular",
        tx: enableV2 ? "carParkScreen.caculatorTab.description" : "carParkScreen.caculatorTab.oldDescription",
        style: _caculator.styles.description
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        preset: "bodyTextBold",
        tx: "carParkScreen.caculatorTab.titleDropdown",
        style: _caculator.styles.titleDropdown
      }), (terminalSelected == null ? undefined : terminalSelected.value) && (0, _jsxRuntime.jsx)(_selectPicker.SelectPicker, {
        options: terminalOption,
        value: (terminalSelected == null ? undefined : terminalSelected.value) || {},
        onChangeValue: function onChangeValue(value) {
          return handleChangePicker(value);
        },
        testID: `${SCREEN_NAME}__InputSelectTerminal`,
        accessibilityLabel: `${SCREEN_NAME}__InputSelectTerminal`
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        preset: "caption1Regular",
        text: terminalSelected == null || (_terminalSelected$val = terminalSelected.value) == null ? undefined : _terminalSelected$val.message,
        style: _caculator.styles.caption1Regular
      }), (0, _jsxRuntime.jsx)(_reactNativeSlider.Slider, {
        trackStyle: moreTime ? _caculator.styles.trackStyleInactive : _caculator.styles.trackStyle,
        renderThumbComponent: function renderThumbComponent() {
          return thumComponent();
        },
        minimumTrackTintColor: moreTime ? _theme.color.palette.lightestPurple : _theme.color.palette.lightPurple,
        value: position,
        minimumValue: MINIMUM_VALUE,
        maximumValue: MAXIMUM_VALUE,
        onValueChange: function onValueChange(value) {
          return handleSlider(value == null ? undefined : value[0]);
        },
        step: STEP,
        onSlidingStart: function onSlidingStart() {
          return setNeedShowToolTip(true);
        },
        onSlidingComplete: function onSlidingComplete() {
          return setNeedShowToolTip(false);
        },
        trackMarks: TRACK_MARKS,
        renderTrackMarkComponent: function renderTrackMarkComponent(index) {
          return trackMark(index);
        },
        containerStyle: {
          marginTop: 55
        },
        disabled: (0, _lodash.isEmpty)(terminalSelected == null ? undefined : terminalSelected.value) || moreTime
      }), !disable24Hour && (0, _jsxRuntime.jsx)(_checkbox.Checkbox, {
        style: _caculator.styles.checkBox,
        value: moreTime,
        onToggle: function onToggle() {
          return setMoreTime(!moreTime);
        },
        tx: "carParkScreen.caculatorTab.moreTime",
        textStyle: _caculator.styles.textCheckBox,
        outlineStyle: {
          borderColor: moreTime ? _theme.color.palette.lightPurple : _theme.color.palette.darkGrey
        },
        testID: `${COMPONENT_NAME}CheckBoxMoreTime`,
        accessibilityLabel: `${COMPONENT_NAME}CheckBoxMoreTime`
      }), moreTime && (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _caculator.styles.selectSpecifyDay,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          tx: "carParkScreen.caculatorTab.arriveOn",
          preset: "bodyTextBold",
          style: _caculator.styles.titleSelectSpecifyStartDate
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: function onPress() {
            return setStartDateVisible(true);
          },
          disabled: !moreTime,
          testID: `${COMPONENT_NAME}__SelectStartDate`,
          accessibilityLabel: `${COMPONENT_NAME}__SelectStartDate`,
          children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _caculator.styles.buttonSelectTerminal,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              text: (0, _moment.default)(specifyDate == null ? undefined : specifyDate.startDate).format("DD MMM YYYY, HH:mm"),
              preset: "bodyTextRegular",
              style: _caculator.styles.contentButtonSelectTerminal
            }), (0, _jsxRuntime.jsx)(_icons.CalendarCarPark, {})]
          })
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          tx: "carParkScreen.caculatorTab.leaveOn",
          preset: "bodyTextBold",
          style: _caculator.styles.titleSelectSpecifyEndDate
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: function onPress() {
            return setEndDateVisible(true);
          },
          disabled: !moreTime,
          testID: `${COMPONENT_NAME}SelectEndDate`,
          accessibilityLabel: `${COMPONENT_NAME}SelectEndDate`,
          children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _caculator.styles.buttonSelectTerminal,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              text: (0, _moment.default)(specifyDate == null ? undefined : specifyDate.endDate).format("DD MMM YYYY, HH:mm"),
              preset: "bodyTextRegular",
              style: _caculator.styles.contentButtonSelectTerminal
            }), (0, _jsxRuntime.jsx)(_icons.CalendarCarPark, {})]
          })
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          tx: "carParkScreen.caculatorTab.totalFee",
          preset: "bodyTextBold",
          style: _caculator.styles.titleTotalFee
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _caculator.styles.contentTotalFee,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            text: `For ${(0, _utils.simpleCondition)({
              condition: price == null ? undefined : price.durationHour,
              ifValue: {
                condition: Number(price == null ? undefined : price.durationHour) > 1,
                ifValue: (price == null ? undefined : price.durationHour) + " hours",
                elseValue: (price == null ? undefined : price.durationHour) + " hour"
              },
              elseValue: "00 hour"
            })} ${(0, _utils.simpleCondition)({
              condition: price == null ? undefined : price.durationMinute,
              ifValue: {
                condition: Number(price == null ? undefined : price.durationMinute) > 1,
                ifValue: (price == null ? undefined : price.durationMinute) + " mins",
                elseValue: (price == null ? undefined : price.durationMinute) + " min"
              },
              elseValue: "00 min"
            })}`,
            preset: "caption1Regular"
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            text: `S$${price == null ? undefined : price.price}`,
            preset: "h1",
            style: _caculator.styles.h1
          })]
        })]
      }), (0, _jsxRuntime.jsx)(_reactNativeDatePicker.default, {
        modal: true,
        open: startDateVisible,
        date: specifyDate == null ? undefined : specifyDate.startDate,
        onConfirm: function onConfirm(date) {
          return handleConfirmPickerDate("startDate", date);
        },
        onCancel: function onCancel() {
          return handleCancelPickerDate();
        },
        testID: `${COMPONENT_NAME}DatePickerStartDate`,
        accessibilityLabel: `${COMPONENT_NAME}DatePickerStartDate`,
        theme: "light"
      }), (0, _jsxRuntime.jsx)(_reactNativeDatePicker.default, {
        modal: true,
        open: endDateVisible,
        date: specifyDate == null ? undefined : specifyDate.endDate,
        onConfirm: function onConfirm(date) {
          return handleConfirmPickerDate("endDate", date);
        },
        onCancel: function onCancel() {
          return handleCancelPickerDate();
        },
        testID: `${COMPONENT_NAME}DatePickerEndDate`,
        accessibilityLabel: `${COMPONENT_NAME}DatePickerEndDate`,
        theme: "light"
      })]
    });
  };
  var _default = exports.default = CaculatorTab;
