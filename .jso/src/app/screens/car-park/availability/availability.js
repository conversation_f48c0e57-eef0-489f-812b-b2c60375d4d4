  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[7]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[8]);
  var _availability = _$$_REQUIRE(_dependencyMap[9]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[10]);
  var _airportLandingRedux = _$$_REQUIRE(_dependencyMap[11]);
  var _lodash = _$$_REQUIRE(_dependencyMap[12]);
  var _theme = _$$_REQUIRE(_dependencyMap[13]);
  var _adobe = _$$_REQUIRE(_dependencyMap[14]);
  var _utils = _$$_REQUIRE(_dependencyMap[15]);
  var _carPark = _$$_REQUIRE(_dependencyMap[16]);
  var _native = _$$_REQUIRE(_dependencyMap[17]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[18]);
  var _errorUnplannedMaintenance = _$$_REQUIRE(_dependencyMap[19]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[20]);
  var _constants = _$$_REQUIRE(_dependencyMap[21]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[22]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[23]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[24]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[25]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var COMPONENT_NAME = "Availability__";
  var WIDTH = _reactNative2.Dimensions.get("window").width;
  var LocationTerminal = /*#__PURE__*/function (LocationTerminal) {
    LocationTerminal["All"] = "All";
    LocationTerminal["T1"] = "T1/Jewel";
    LocationTerminal["T2"] = "T2";
    LocationTerminal["T3"] = "T3";
    LocationTerminal["T4"] = "T4";
    return LocationTerminal;
  }(LocationTerminal || {});
  var arrData = [LocationTerminal.All, LocationTerminal.T1, LocationTerminal.T2, LocationTerminal.T3, LocationTerminal.T4];
  var fullColor = {
    color: _theme.color.palette.baseRed
  };
  var defaultColor = {
    color: _theme.color.palette.almostBlackGrey
  };
  var handleOpenMap = function handleOpenMap(latitude, longitude, label) {
    var scheme = _reactNative2.Platform.select({
      ios: "maps:0,0?q=",
      android: "geo:0,0?q="
    });
    var latLng = `${latitude},${longitude}`;
    var url = _reactNative2.Platform.select({
      ios: `${scheme}${encodeURIComponent(label)}@${latLng}`,
      android: `${scheme}${latLng}(${encodeURIComponent(label)})`
    });
    _reactNative2.Linking.openURL(url);
  };
  var TerminalCard = function TerminalCard(_ref) {
    var _element$general_spac, _element$general_spac2, _element$accessible_s, _element$accessible_s2, _element$accessible_s3, _element$accessible_s4;
    var element = _ref.element,
      index = _ref.index;
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _availability.styles.cardTerminal,
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        text: element == null ? undefined : element.name,
        preset: "subTitleBold",
        style: _availability.styles.titleCardTerminal,
        numberOfLines: 2
      }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        style: _availability.styles.touchableLocation,
        onPress: function onPress() {
          return handleOpenMap(element == null ? undefined : element.lat, element == null ? undefined : element.lng, element == null ? undefined : element.name);
        },
        children: (0, _jsxRuntime.jsx)(_icons.Location, {})
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        text: element == null ? undefined : element.message,
        preset: "caption2Regular",
        style: _availability.styles.messageCardTerminal,
        numberOfLines: 3
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _availability.styles.divider
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _availability.styles.informationFirstRow,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _availability.styles.leftInformation,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            text: "General spaces",
            preset: "XSmallRegular",
            style: _availability.styles.titleInformation
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            text: element == null ? undefined : element.general_spaces,
            preset: "textLink",
            style: (0, _utils.handleCondition)((element == null || (_element$general_spac = element.general_spaces) == null ? undefined : _element$general_spac.toLowerCase()) === "full", _availability.styles.valueInformationRed, (0, _utils.handleCondition)((element == null || (_element$general_spac2 = element.general_spaces) == null ? undefined : _element$general_spac2.toLowerCase()) === "n/a", _availability.styles.valueInformationNA, _availability.styles.valueInformation))
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _availability.styles.rightInformation,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            text: "Accessible spaces",
            preset: "XSmallRegular",
            style: _availability.styles.titleInformation
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _availability.styles.valueWithIconHandicap,
            children: [(element == null || (_element$accessible_s = element.accessible_spaces) == null ? undefined : _element$accessible_s.toLowerCase()) === "full" || (element == null || (_element$accessible_s2 = element.accessible_spaces) == null ? undefined : _element$accessible_s2.toLowerCase()) === "n/a" ? (0, _jsxRuntime.jsx)(_icons.Handicap, {
              style: fullColor
            }) : (0, _jsxRuntime.jsx)(_icons.Handicap, {
              style: defaultColor
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              text: element == null ? undefined : element.accessible_spaces,
              preset: "textLink",
              style: (0, _utils.handleCondition)((element == null || (_element$accessible_s3 = element.accessible_spaces) == null ? undefined : _element$accessible_s3.toLowerCase()) === "full", _availability.styles.valueInformationWithHandiCapRed, (0, _utils.handleCondition)((element == null || (_element$accessible_s4 = element.accessible_spaces) == null ? undefined : _element$accessible_s4.toLowerCase()) === "n/a", _availability.styles.valueInformationWithHandiCapNA, _availability.styles.valueInformationWithHandiCap))
            })]
          })]
        })]
      })]
    }, index);
  };
  var TerminalSection = function TerminalSection(_ref2) {
    var _data$list;
    var data = _ref2.data,
      index = _ref2.index;
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _availability.styles.wrapTerminalSection,
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        text: data == null ? undefined : data.terminalName,
        preset: "h4",
        style: _availability.styles.titleTerminalSection
      }), data == null || (_data$list = data.list) == null ? undefined : _data$list.map(function (e, i) {
        return (0, _jsxRuntime.jsx)(TerminalCard, {
          element: e,
          index: index
        }, i);
      })]
    }, index);
  };
  var _worklet_11634815363329_init_data = {
    code: "function availabilityTsx1(){const{animatedPosition}=this.__closure;return{transform:[{translateX:animatedPosition.value}]};}"
  };
  var AvailabilityTab = function AvailabilityTab(_ref3) {
    var _Object$values;
    var setHeightTab = _ref3.setHeightTab,
      enableV2 = _ref3.enableV2,
      slideAnimation = _ref3.slideAnimation,
      timeOut = _ref3.timeOut;
    var navigation = (0, _native.useNavigation)();
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("AVAILABILITY"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var carParkAvailability = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.getCarParkAvailability);
    var _useState = (0, _react.useState)("All"),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      selectedTerminal = _useState2[0],
      setSelectedTerminal = _useState2[1];
    var _useState3 = (0, _react.useState)([]),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      dataListTerminal = _useState4[0],
      setDataListTerminal = _useState4[1];
    var dataErrorMaintenanceConfig = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.GET_ERROR_MAINTENANCE));
    var listErrorMaintenance = (0, _lodash.get)(dataErrorMaintenanceConfig, "data.list", []);
    var sectionMaintenanceObj = (0, _react.useMemo)(function () {
      var data = listErrorMaintenance == null ? undefined : listErrorMaintenance.find(function (obj) {
        return obj.sectionName === _constants.sectionTagName.availability;
      });
      return data || {};
    }, [listErrorMaintenance]);
    var totalTerminalCard = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.totalTerminalCard);
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)("Carpark_Availability");
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)("Carpark_Availability", (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribeFocus;
    }, [navigation]);
    (0, _react.useEffect)(function () {
      var _listTerminal, _listTerminal2, _listTerminal3, _listTerminal4;
      var listTerminal = [];
      var tHeight = 0;
      switch (selectedTerminal) {
        case LocationTerminal.All:
          listTerminal = carParkAvailability;
          tHeight = totalTerminalCard;
          break;
        case LocationTerminal.T1:
          listTerminal = [carParkAvailability == null ? undefined : carParkAvailability.t1];
          tHeight = (_listTerminal = listTerminal) == null || (_listTerminal = _listTerminal[0]) == null || (_listTerminal = _listTerminal.list) == null ? undefined : _listTerminal.length;
          break;
        case LocationTerminal.T2:
          listTerminal = [carParkAvailability == null ? undefined : carParkAvailability.t2];
          tHeight = (_listTerminal2 = listTerminal) == null || (_listTerminal2 = _listTerminal2[0]) == null || (_listTerminal2 = _listTerminal2.list) == null ? undefined : _listTerminal2.length;
          break;
        case LocationTerminal.T3:
          listTerminal = [carParkAvailability == null ? undefined : carParkAvailability.t3];
          tHeight = (_listTerminal3 = listTerminal) == null || (_listTerminal3 = _listTerminal3[0]) == null || (_listTerminal3 = _listTerminal3.list) == null ? undefined : _listTerminal3.length;
          break;
        case LocationTerminal.T4:
          listTerminal = [carParkAvailability == null ? undefined : carParkAvailability.t4];
          tHeight = (_listTerminal4 = listTerminal) == null || (_listTerminal4 = _listTerminal4[0]) == null || (_listTerminal4 = _listTerminal4.list) == null ? undefined : _listTerminal4.length;
          break;
      }
      setDataListTerminal(listTerminal);
      var sHeight = (0, _carPark.handleHeight)(tHeight + 1, 1, 0);
      setHeightTab(sHeight);
    }, [selectedTerminal, carParkAvailability]);
    var handleSelectTerminal = function handleSelectTerminal(element) {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppCarparkAvailabilityFiltersApplied, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppCarparkAvailabilityFiltersApplied, element));
      if (selectedTerminal === element) {
        setSelectedTerminal("All");
      } else {
        setSelectedTerminal(element);
      }
    };
    var wrapContent = {
      backgroundColor: _theme.color.palette.lightestGrey,
      marginBottom: 50
    };
    var handleButtonOnPress = /*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* (navigationObj, redirect) {
        var type = navigationObj.type,
          value = navigationObj.value;
        if (!type || !value) return;
        handleNavigation(type, value, redirect);
      });
      return function handleButtonOnPress(_x, _x2) {
        return _ref4.apply(this, arguments);
      };
    }();
    var animatedPosition = (0, _reactNativeReanimated.useSharedValue)(slideAnimation === "RTL" ? WIDTH : -WIDTH);
    var animatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var availabilityTsx1 = function availabilityTsx1() {
        return {
          transform: [{
            translateX: animatedPosition.value
          }]
        };
      };
      availabilityTsx1.__closure = {
        animatedPosition: animatedPosition
      };
      availabilityTsx1.__workletHash = 11634815363329;
      availabilityTsx1.__initData = _worklet_11634815363329_init_data;
      return availabilityTsx1;
    }());
    (0, _react.useEffect)(function () {
      animatedPosition.value = (0, _reactNativeReanimated.withTiming)(0, {
        duration: timeOut
      });
    }, []);
    if (!(0, _lodash.isEmpty)(sectionMaintenanceObj) && sectionMaintenanceObj != null && sectionMaintenanceObj.enableMode) {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: [_availability.styles.errorUnplannedMaintenance, animatedStyle],
        children: (0, _jsxRuntime.jsx)(_errorUnplannedMaintenance.ErrorUnplannedMaintenance, {
          header: sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.header,
          subHeader: sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.subHeader,
          icon: sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.icon,
          buttonLabel: sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.buttonLabel,
          buttonLabel2: sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.buttonLabel2,
          onFirstButtonPress: function onFirstButtonPress() {
            return handleButtonOnPress(sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.navigationFirst, sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.redirectFirst);
          },
          onSecondButtonPress: function onSecondButtonPress() {
            return handleButtonOnPress(sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.navigationSecond, sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.redirectSecond);
          },
          testID: `${COMPONENT_NAME}__ErrorUnplannedMaintenance`,
          accessibilityLabel: `${COMPONENT_NAME}__ErrorUnplannedMaintenance`
        })
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
      style: [wrapContent, animatedStyle],
      children: [(0, _jsxRuntime.jsx)(ListFilter, {
        handleSelect: function handleSelect(element) {
          return handleSelectTerminal(element);
        },
        selectedTerminal: selectedTerminal
      }), !(0, _lodash.isEmpty)(dataListTerminal) && ((_Object$values = Object.values(dataListTerminal)) == null ? undefined : _Object$values.map(function (e, i) {
        return (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _availability.styles.wrapViewTerminalSection,
          children: (0, _jsxRuntime.jsx)(TerminalSection, {
            data: e,
            index: i
          })
        }, i);
      }))]
    });
  };
  var ListFilter = function ListFilter(_ref5) {
    var handleSelect = _ref5.handleSelect,
      selectedTerminal = _ref5.selectedTerminal;
    var defaultView = function defaultView() {
      var arr = [];
      arrData.forEach(function (element, index) {
        arr.push((0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: selectedTerminal === element ? _availability.styles.touchableItemFilterActive : _availability.styles.touchableItemFilterInActive,
          onPress: function onPress() {
            return handleSelect(element);
          },
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            text: element,
            style: selectedTerminal === element ? _availability.styles.textItemFilterActive : _availability.styles.textItemFilterInActive,
            preset: "bodyTextBold"
          })
        }, `${index}`));
      });
      return arr;
    };
    return (0, _jsxRuntime.jsx)(_reactNative2.ScrollView, {
      horizontal: true,
      showsHorizontalScrollIndicator: false,
      showsVerticalScrollIndicator: false,
      style: _availability.styles.scrollViewFilterStyle,
      children: defaultView()
    });
  };
  var _default = exports.default = AvailabilityTab;
