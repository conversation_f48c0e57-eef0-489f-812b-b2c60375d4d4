  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _i18n = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _findMyCar = _$$_REQUIRE(_dependencyMap[8]);
  var _bottomSheetConfirm = _$$_REQUIRE(_dependencyMap[9]);
  var _icons = _$$_REQUIRE(_dependencyMap[10]);
  var _myCarInMap = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _reactNativeOtpInput = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _loadingModal = _$$_REQUIRE(_dependencyMap[14]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[15]);
  var _airportLandingRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[16]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[17]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var linearActiveButton = [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End];
  var linearInActiveButton = [_theme.color.palette.lightGrey, _theme.color.palette.lightGrey];
  var COMPONENT_NAME = "LocateYourVehice__";
  var ERROR_MESSAGE_CODE = {
    CARPARK_FIND_001: "CARPARK-FIND-001",
    CARPARK_FIND_002: "CARPARK-FIND-002",
    CARPARK_FIND_003: "CARPARK-FIND-003"
  };
  var HEIGHT_MESSAGE_POPUP = {
    NO_VEHICE_FOUND: 345,
    SYSTEM_ERROR: 300
  };
  var LocateYourVehice = function LocateYourVehice() {
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useState = (0, _react.useState)(""),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      numberOTP = _useState2[0],
      setNumberOTP = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      visibleModalLocation = _useState4[0],
      setVisibleModalLocation = _useState4[1];
    var _useState5 = (0, _react.useState)(true),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      disableSubmit = _useState6[0],
      setDisableSubmit = _useState6[1];
    var _useState7 = (0, _react.useState)(false),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      visibleNoVehiceFound = _useState8[0],
      setVisibleNoVehiceFound = _useState8[1];
    var _useState9 = (0, _react.useState)(false),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      visibleSystemError = _useState0[0],
      setVisibleSystemError = _useState0[1];
    var getMyCarFetching = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.getMyCarFetching);
    var getMyCarPayload = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.getMyCarPayload);
    (0, _react.useEffect)(function () {
      if (getMyCarPayload) {
        if ((getMyCarPayload == null ? undefined : getMyCarPayload.status) === ERROR_MESSAGE_CODE.CARPARK_FIND_003) {
          setVisibleNoVehiceFound(true);
        } else if ((getMyCarPayload == null ? undefined : getMyCarPayload.status) === ERROR_MESSAGE_CODE.CARPARK_FIND_002 || (getMyCarPayload == null ? undefined : getMyCarPayload.status) === ERROR_MESSAGE_CODE.CARPARK_FIND_001) {
          setVisibleSystemError(true);
        }
      }
    }, [getMyCarPayload]);
    var onSubmit = function onSubmit() {
      dispatch(_airportLandingRedux.default.getMyCarRequest(numberOTP));
    };
    var onCloseModal = function onCloseModal() {
      setVisibleModalLocation(false);
    };
    var onCloseBottomSheetConfirm = function onCloseBottomSheetConfirm() {
      dispatch(_airportLandingRedux.default.getMyCarReset());
      if (visibleNoVehiceFound) {
        setVisibleNoVehiceFound(false);
      }
      if (visibleSystemError) {
        setVisibleSystemError(false);
      }
    };
    (0, _react.useEffect)(function () {
      if (numberOTP) {
        setDisableSubmit(false);
      } else {
        setDisableSubmit(true);
      }
    }, [numberOTP]);
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _findMyCar.styles.viewRoot,
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        tx: "carParkScreen.findMyCarTab.title",
        preset: "h4"
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        tx: "carParkScreen.findMyCarTab.description",
        preset: "caption1Regular",
        style: _findMyCar.styles.description
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _findMyCar.styles.listInputNumber,
        children: (0, _jsxRuntime.jsx)(_reactNativeOtpInput.default, {
          style: _findMyCar.styles.wrapInputNumber,
          pinCount: 4,
          code: numberOTP == null ? undefined : numberOTP.toString(),
          onCodeChanged: function onCodeChanged(code) {
            setNumberOTP(code);
          },
          autoFocusOnLoad: false,
          codeInputFieldStyle: _findMyCar.styles.inputNumber,
          selectionColor: _theme.color.palette.lightPurple
        })
      }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
        style: _findMyCar.styles.linearButtonContainer,
        start: {
          x: 0,
          y: 1
        },
        end: {
          x: 1,
          y: 0
        },
        colors: !disableSubmit ? linearActiveButton : linearInActiveButton,
        children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: _findMyCar.styles.touchableSubmit,
          disabled: disableSubmit,
          onPress: onSubmit,
          testID: `${COMPONENT_NAME}OnSubmit`,
          accessibilityLabel: `${COMPONENT_NAME}OnSubmit`,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            text: (0, _i18n.translate)("carParkScreen.findMyCarTab.submit"),
            style: disableSubmit ? _findMyCar.styles.textSubmitInActive : _findMyCar.styles.textSubmitActive
          })
        })
      }), (0, _jsxRuntime.jsx)(_myCarInMap.default, {
        visible: visibleModalLocation,
        onCloseModal: onCloseModal
      }), (0, _jsxRuntime.jsx)(_bottomSheetConfirm.BottomSheetConfirm, {
        visible: visibleNoVehiceFound || visibleSystemError,
        hideOnConfirm: true,
        title: visibleNoVehiceFound ? (0, _i18n.translate)("carParkScreen.findMyCarTab.titleNotFound") : (0, _i18n.translate)("carParkScreen.findMyCarTab.titleSystemError"),
        message: visibleNoVehiceFound ? (0, _i18n.translate)("carParkScreen.findMyCarTab.descriptionNotFound") : (0, _i18n.translate)("carParkScreen.findMyCarTab.descriptionSystemError"),
        confirmButtonText: visibleNoVehiceFound ? (0, _i18n.translate)("carParkScreen.findMyCarTab.buttonNotFound") : (0, _i18n.translate)("carParkScreen.findMyCarTab.buttonSystemError"),
        height: visibleNoVehiceFound ? HEIGHT_MESSAGE_POPUP.NO_VEHICE_FOUND : HEIGHT_MESSAGE_POPUP.SYSTEM_ERROR,
        onConfirm: onCloseBottomSheetConfirm,
        onHide: onCloseBottomSheetConfirm,
        testID: `${COMPONENT_NAME}BottomSheetNoVehiceFound`,
        isClose: true,
        icon: (0, _jsxRuntime.jsx)(_icons.InfoRed, {}),
        hasCancel: false
      }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
        visible: getMyCarFetching
      })]
    });
  };
  var _default = exports.default = LocateYourVehice;
