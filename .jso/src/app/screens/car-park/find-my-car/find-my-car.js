  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _findMyCar = _$$_REQUIRE(_dependencyMap[5]);
  var _locateYourVehice = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _selectYourVehice = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _airportLandingRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[9]);
  var _lodash = _$$_REQUIRE(_dependencyMap[10]);
  var _native = _$$_REQUIRE(_dependencyMap[11]);
  var _adobe = _$$_REQUIRE(_dependencyMap[12]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[13]);
  var _errorUnplannedMaintenance = _$$_REQUIRE(_dependencyMap[14]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[15]);
  var _constants = _$$_REQUIRE(_dependencyMap[16]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[17]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[18]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[19]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[20]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var COMPONENT_NAME = "FindMyCar__";
  var WIDTH = _reactNative.Dimensions.get("window").width;
  var OK = "OK";
  var _worklet_2745699554609_init_data = {
    code: "function findMyCarTsx1(){const{animatedPosition}=this.__closure;return{transform:[{translateX:animatedPosition.value}]};}"
  };
  var FindMyCarTab = function FindMyCarTab(props) {
    var slideAnimation = props.slideAnimation,
      timeOut = props.timeOut;
    var navigation = (0, _native.useNavigation)();
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("FIND_MY_CAR"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var getMyCarPayload = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.getMyCarPayload);
    var dataErrorMaintenanceConfig = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.GET_ERROR_MAINTENANCE));
    var listErrorMaintenance = (0, _lodash.get)(dataErrorMaintenanceConfig, "data.list", []);
    var sectionMaintenanceObj = (0, _react.useMemo)(function () {
      var data = listErrorMaintenance == null ? undefined : listErrorMaintenance.find(function (obj) {
        return obj.sectionName === _constants.sectionTagName.findMyCar;
      });
      return data || {};
    }, [listErrorMaintenance]);
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)("Carpark_FindMyCar");
    _react.default.useEffect(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)("Carpark_FindMyCar", (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribeFocus;
    }, [navigation]);
    var animatedPosition = (0, _reactNativeReanimated.useSharedValue)(slideAnimation === "RTL" ? WIDTH : -WIDTH);
    var animatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var findMyCarTsx1 = function findMyCarTsx1() {
        return {
          transform: [{
            translateX: animatedPosition.value
          }]
        };
      };
      findMyCarTsx1.__closure = {
        animatedPosition: animatedPosition
      };
      findMyCarTsx1.__workletHash = 2745699554609;
      findMyCarTsx1.__initData = _worklet_2745699554609_init_data;
      return findMyCarTsx1;
    }());
    (0, _react.useEffect)(function () {
      animatedPosition.value = (0, _reactNativeReanimated.withTiming)(0, {
        duration: timeOut
      });
    }, []);
    (0, _react.useEffect)(function () {
      if (getMyCarPayload && !(0, _lodash.isEmpty)(getMyCarPayload == null ? undefined : getMyCarPayload.items)) {
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppCarparkFindMyCarRequestCar, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppCarparkFindMyCarRequestCar, "1"));
      }
    }, [getMyCarPayload]);
    var handleButtonOnPress = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* (navigationObj, redirect) {
        var type = navigationObj.type,
          value = navigationObj.value;
        if (!type || !value) return;
        handleNavigation(type, value, redirect);
      });
      return function handleButtonOnPress(_x, _x2) {
        return _ref.apply(this, arguments);
      };
    }();
    var ContentPage = (0, _react.useCallback)(function () {
      var _getMyCarPayload$stat;
      return !getMyCarPayload || (getMyCarPayload == null || (_getMyCarPayload$stat = getMyCarPayload.status) == null ? undefined : _getMyCarPayload$stat.toUpperCase()) !== OK ? (0, _jsxRuntime.jsx)(_locateYourVehice.default, {}) : (0, _jsxRuntime.jsx)(_selectYourVehice.default, {
        listVehice: getMyCarPayload
      });
    }, [getMyCarPayload == null ? undefined : getMyCarPayload.status]);
    if (!(0, _lodash.isEmpty)(sectionMaintenanceObj) && sectionMaintenanceObj != null && sectionMaintenanceObj.enableMode) {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: _findMyCar.styles.errorUnplannedMaintenance,
        children: (0, _jsxRuntime.jsx)(_errorUnplannedMaintenance.ErrorUnplannedMaintenance, {
          header: sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.header,
          subHeader: sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.subHeader,
          icon: sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.icon,
          buttonLabel: sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.buttonLabel,
          buttonLabel2: sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.buttonLabel2,
          onFirstButtonPress: function onFirstButtonPress() {
            return handleButtonOnPress(sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.navigationFirst, sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.redirectFirst);
          },
          onSecondButtonPress: function onSecondButtonPress() {
            return handleButtonOnPress(sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.navigationSecond, sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.redirectSecond);
          },
          testID: `${COMPONENT_NAME}__ErrorUnplannedMaintenance`,
          accessibilityLabel: `${COMPONENT_NAME}__ErrorUnplannedMaintenance`
        })
      });
    }
    return (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
      style: [_findMyCar.styles.container, animatedStyle],
      children: (0, _jsxRuntime.jsx)(ContentPage, {})
    });
  };
  var _default = exports.default = FindMyCarTab;
