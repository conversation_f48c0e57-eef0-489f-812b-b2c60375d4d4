  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[4]);
  var _icons = _$$_REQUIRE(_dependencyMap[5]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _cardPrivileges = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  var SCREEN_NAME = "CardPrivileges";
  var CardPrivileges = function CardPrivileges(_ref) {
    var contentParkingPrivilegesData = _ref.contentParkingPrivilegesData,
      onDismissCard = _ref.onDismissCard,
      onFindMore = _ref.onFindMore;
    // dismissible

    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _cardPrivileges.styles.container,
      children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
        source: {
          uri: (0, _mediaHelper.handleImageUrl)(contentParkingPrivilegesData == null ? undefined : contentParkingPrivilegesData.icon)
        },
        style: _cardPrivileges.styles.imageCard
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _cardPrivileges.styles.contentText,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          text: contentParkingPrivilegesData == null ? undefined : contentParkingPrivilegesData.title,
          preset: "bodyTextBold",
          style: _cardPrivileges.styles.bodyTextBold,
          numberOfLines: 1
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          text: contentParkingPrivilegesData == null ? undefined : contentParkingPrivilegesData.text,
          preset: "caption1Regular",
          style: _cardPrivileges.styles.caption1Regular,
          numberOfLines: 3
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: onFindMore,
          testID: `${SCREEN_NAME}__FindMoreOutButton`,
          accessibilityLabel: `${SCREEN_NAME}__FindMoreOutButton`,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            text: contentParkingPrivilegesData == null ? undefined : contentParkingPrivilegesData.linkText,
            preset: "caption1Bold",
            style: _cardPrivileges.styles.caption1Bold,
            numberOfLines: 1
          })
        })]
      }), (contentParkingPrivilegesData == null ? undefined : contentParkingPrivilegesData.dismissible) && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        style: _cardPrivileges.styles.crossPriviligeCard,
        onPress: function onPress() {
          return onDismissCard();
        },
        testID: `${SCREEN_NAME}__DismissButton`,
        accessibilityLabel: `${SCREEN_NAME}__DismissButton`,
        children: (0, _jsxRuntime.jsx)(_icons.CrossPurple, {})
      })]
    });
  };
  var _default = exports.default = CardPrivileges;
