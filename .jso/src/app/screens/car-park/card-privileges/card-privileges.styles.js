  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var cardHeight = 118;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    bodyTextBold: {
      color: _theme.color.palette.almostBlackGrey,
      width: "90%"
    },
    caption1Bold: {
      color: _theme.color.palette.lightPurple,
      textAlign: "left"
    },
    caption1Regular: {
      color: _theme.color.palette.almostBlackGrey,
      paddingBottom: 8,
      paddingTop: 4
    },
    container: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.lightestPurple,
      borderRadius: 18,
      flexDirection: "row",
      // height: cardHeight,
      marginBottom: 17,
      marginHorizontal: 24,
      marginTop: -67.**************,
      padding: 16
    },
    contentText: {
      width: "70%"
    },
    crossPriviligeCard: {
      position: "absolute",
      right: 10,
      top: 10
    },
    imageCard: {
      height: 90,
      marginRight: 10,
      width: 90
    }
  });
