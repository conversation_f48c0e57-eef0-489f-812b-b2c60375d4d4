  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ViewBackgroundRotate = undefined;
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _theme = _$$_REQUIRE(_dependencyMap[2]);
  var _backgrounds = _$$_REQUIRE(_dependencyMap[3]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeColorMatrixImageFilters = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  var ratio = 0.49866666666666665;
  var widthScreen = _reactNative.Dimensions.get("window").width;
  var ViewBackgroundRotate = exports.ViewBackgroundRotate = _react.default.memo(function (props) {
    var isErrorMathead = props.isErrorMathead,
      ref = props.ref,
      images = props.images,
      isDarkApp = props.isDarkApp;
    var ContainerComponent = isDarkApp ? _reactNativeColorMatrixImageFilters.Grayscale : _reactNative.View;
    if (isErrorMathead) {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.viewWhite
      });
    }
    return (0, _jsxRuntime.jsx)(_reactNative.ScrollView, {
      ref: ref,
      horizontal: true,
      scrollEnabled: true,
      style: {
        position: 'absolute',
        top: widthScreen * ratio - 0.5
      },
      contentContainerStyle: {
        width: widthScreen * (images == null ? undefined : images.length)
      },
      pagingEnabled: true,
      showsHorizontalScrollIndicator: false,
      children: images.map(function (image, index) {
        return (0, _jsxRuntime.jsxs)(ContainerComponent, {
          style: styles.container,
          children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
            source: image ? {
              uri: image
            } : _backgrounds.BackgroundDefaultExploreV2,
            style: styles.image,
            resizeMode: "cover"
          }), (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: styles.viewWhiteBottom
          })]
        }, `${image}-${index}2`);
      })
    });
  });
  var styles = _reactNative.StyleSheet.create({
    viewWhite: {
      width: "100%",
      height: widthScreen * ratio,
      position: "absolute",
      top: widthScreen * ratio
    },
    container: {
      width: widthScreen,
      height: widthScreen * ratio
    },
    image: {
      flex: 1,
      transform: [{
        rotate: "180deg"
      }, {
        scaleX: -1
      }]
    },
    viewBlack: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0
    },
    viewWhiteBottom: {
      width: "100%",
      height: 57,
      position: "absolute",
      bottom: -10,
      backgroundColor: _theme.color.palette.almostWhiteGrey
    }
  });
