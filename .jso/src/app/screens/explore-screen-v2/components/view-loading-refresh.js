  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ViewLoadingRefresh = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  var ViewLoadingRefresh = exports.ViewLoadingRefresh = _react.default.memo(function () {
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.container,
      children: (0, _jsxRuntime.jsx)(_reactNative.ActivityIndicator, {
        size: 'small',
        color: _theme.color.palette.almostWhiteGrey
      })
    });
  });
  var styles = _reactNative.StyleSheet.create({
    container: {
      width: '100%',
      position: 'absolute',
      top: 80,
      alignItems: 'center'
    }
  });
