  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ViewMemberInfoError = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _icons = _$$_REQUIRE(_dependencyMap[3]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  var ViewMemberInfoError = exports.ViewMemberInfoError = _react.default.memo(function (props) {
    var onReload = props.onReload;
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.viewTxt,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.title,
          children: "Hi"
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: [styles.viewRow, {
            gap: 12
          }],
          children: [(0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
            style: styles.viewRow,
            activeOpacity: 0.7,
            onPress: onReload,
            children: [(0, _jsxRuntime.jsx)(_icons.ReloadIcon, {
              color: _theme.color.palette.midGrey
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.txtPoint,
              tx: "common.reload"
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
            style: styles.viewRow,
            activeOpacity: 0.7,
            onPress: onReload,
            children: [(0, _jsxRuntime.jsx)(_icons.ReloadIcon, {
              color: _theme.color.palette.midGrey
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.txtPoint,
              tx: "common.reload"
            })]
          })]
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: styles.button,
        onPress: onReload,
        activeOpacity: 0.7,
        children: [(0, _jsxRuntime.jsx)(_icons.ReloadIcon, {
          color: _theme.color.palette.midGrey
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.txtButton,
          tx: "common.reload"
        })]
      })]
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    container: {
      paddingHorizontal: 18,
      paddingVertical: 16,
      flexDirection: "row",
      alignItems: "center",
      gap: 12
    },
    viewTxt: {
      flex: 1
    },
    title: {
      fontFamily: _theme.typography.black,
      color: _theme.color.palette.darkGrey999,
      fontSize: 16,
      fontWeight: _reactNative2.Platform.select({
        ios: "900",
        android: "normal"
      }),
      lineHeight: 20,
      marginBottom: 8
    },
    viewRow: {
      flexDirection: "row",
      alignItems: "center",
      gap: 4
    },
    txtPoint: {
      fontFamily: _theme.typography.regular,
      fontSize: 12,
      fontWeight: _reactNative2.Platform.select({
        ios: "400",
        android: "normal"
      }),
      lineHeight: 16,
      color: _theme.color.palette.darkGrey999,
      marginLeft: 4
    },
    button: {
      width: 120,
      height: 32,
      borderRadius: 99,
      backgroundColor: _theme.color.palette.lighterGrey,
      justifyContent: "center",
      alignItems: "center",
      flexDirection: "row"
    },
    txtButton: {
      fontFamily: _theme.typography.bold,
      fontSize: 12,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 16,
      color: _theme.color.palette.darkGrey999,
      marginLeft: 6
    }
  });
