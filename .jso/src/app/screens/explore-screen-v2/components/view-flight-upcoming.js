  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ViewFlightUpcoming = undefined;
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _exploreRedux = _$$_REQUIRE(_dependencyMap[2]);
  var _mytravelRedux = _$$_REQUIRE(_dependencyMap[3]);
  var _utils = _$$_REQUIRE(_dependencyMap[4]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _lodash = _$$_REQUIRE(_dependencyMap[9]);
  var _upcomingEvent = _$$_REQUIRE(_dependencyMap[10]);
  var _upcomingEventProps = _$$_REQUIRE(_dependencyMap[11]);
  var _theme = _$$_REQUIRE(_dependencyMap[12]);
  var _icons = _$$_REQUIRE(_dependencyMap[13]);
  var _text = _$$_REQUIRE(_dependencyMap[14]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[15]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var ViewFlightUpcoming = exports.ViewFlightUpcoming = function ViewFlightUpcoming() {
    var dispatch = (0, _reactRedux.useDispatch)();
    var userProfile = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var getUpComingEventError = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.getUpComingEventError);
    var getUpComingLoading = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.getUpComingLoading);
    var myTravelFlightsPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.myTravelFlightsPayload);
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var upComingEventData = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.upComingEventsData);
    var getUpcomingEventsSuccess = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.getUpcomingEventsSuccess);
    var profileError = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profileError);
    var isShowUpcomingEventView = (0, _react.useMemo)(function () {
      if ((0, _utils.ifAllTrue)([isLoggedIn, !(0, _lodash.isEmpty)(upComingEventData) || getUpComingEventError])) return true;
      return false;
    }, [isLoggedIn, upComingEventData, getUpComingEventError, getUpcomingEventsSuccess, profileError]);
    var isError = getUpComingEventError !== null && isLoggedIn;
    var onReLoadUpComingEvent = function onReLoadUpComingEvent() {
      dispatch(_mytravelRedux.MytravelCreators.flyMyTravelFlightsRequest(userProfile == null ? undefined : userProfile.email));
    };
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: [styles.container, {
          marginLeft: isError ? 0 : -8
        }],
        children: (0, _utils.handleCondition)(isError, (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.upcomingEventErrContainer,
          children: [1, 2].map(function (index) {
            return (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: styles.viewError,
              children: (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
                style: styles.buttonReload,
                onPress: onReLoadUpComingEvent,
                children: [(0, _jsxRuntime.jsx)(_icons.ReloadIcon, {
                  color: _theme.color.palette.darkGrey,
                  width: 26,
                  height: 26
                }), (0, _jsxRuntime.jsx)(_text.Text, {
                  style: styles.txtButton,
                  tx: "common.reload"
                })]
              })
            }, index);
          })
        }), isShowUpcomingEventView && (0, _jsxRuntime.jsx)(_upcomingEvent.UpComingEventComponent, {
          data: upComingEventData,
          type: _upcomingEventProps.UpComingEventState.default,
          styleContainer: styles.viewList
        }))
      })
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    container: {
      alignItems: 'center'
    },
    viewList: {
      position: 'relative',
      bottom: 0,
      marginTop: -8,
      marginBottom: 16
    },
    upcomingEventErrContainer: {
      width: '100%',
      flexDirection: 'row',
      paddingLeft: 16,
      gap: 12,
      marginBottom: 16
    },
    viewError: {
      width: 280,
      height: 100,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      shadowColor: _theme.color.palette.almostBlackGrey,
      shadowOffset: {
        width: 0,
        height: 6
      },
      shadowOpacity: 0.08,
      shadowRadius: 20,
      elevation: 4
    },
    buttonReload: {
      justifyContent: 'center',
      alignItems: 'center'
    },
    txtButton: {
      fontFamily: _theme.typography.bold,
      fontSize: 16,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 24,
      color: _theme.color.palette.lightPurple
    }
  });
