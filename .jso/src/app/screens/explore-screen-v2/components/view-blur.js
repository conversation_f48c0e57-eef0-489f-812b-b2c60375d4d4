  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ViewBlurBackground = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _backgrounds = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeColorMatrixImageFilters = _$$_REQUIRE(_dependencyMap[4]);
  var _lodash = _$$_REQUIRE(_dependencyMap[5]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  var widthScreen = _reactNative.Dimensions.get('window').width;
  var ViewBlurBackground = exports.ViewBlurBackground = _react.default.memo(function (props) {
    var ref = props.ref,
      isConnectInternet = props.isConnectInternet,
      isDarkApp = props.isDarkApp,
      images = props.images;
    var ContainerComponent = isDarkApp ? _reactNativeColorMatrixImageFilters.Grayscale : _reactNative.View;
    if ((0, _lodash.isEmpty)(images)) {
      return (0, _jsxRuntime.jsxs)(ContainerComponent, {
        style: styles.emptyContainer,
        children: [(0, _jsxRuntime.jsx)(_reactNative.Image, {
          source: _backgrounds.BackgroundDefaultExploreV2,
          style: styles.image,
          resizeMode: "cover",
          blurRadius: 4
        }), (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.backdropContainerStyle
        })]
      });
    }
    return (0, _jsxRuntime.jsx)(_reactNative.ScrollView, {
      ref: ref,
      horizontal: true,
      scrollEnabled: true,
      style: {
        height: widthScreen,
        position: 'absolute',
        top: 0
      },
      contentContainerStyle: {
        width: widthScreen * (images == null ? undefined : images.length)
      },
      pagingEnabled: true,
      showsHorizontalScrollIndicator: false,
      children: images.map(function (image, index) {
        return (0, _jsxRuntime.jsxs)(ContainerComponent, {
          style: styles.container,
          children: [(0, _jsxRuntime.jsx)(_reactNative.Image, {
            source: image && isConnectInternet ? {
              uri: image
            } : _backgrounds.BackgroundDefaultExploreV2,
            style: styles.image,
            resizeMode: "cover",
            blurRadius: 4
          }), (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: styles.backdropContainerStyle
          })]
        }, `${image}-${index}1`);
      })
    });
  });
  var styles = _reactNative.StyleSheet.create({
    emptyContainer: {
      width: widthScreen,
      height: widthScreen,
      position: 'absolute',
      top: 0
    },
    container: {
      width: widthScreen,
      height: widthScreen
    },
    image: {
      width: widthScreen,
      height: widthScreen
    },
    backdropContainerStyle: {
      width: widthScreen,
      height: widthScreen,
      position: 'absolute',
      top: 0,
      right: 0,
      left: 0,
      backgroundColor: 'rgba(18,18,18, 0.85)'
    }
  });
