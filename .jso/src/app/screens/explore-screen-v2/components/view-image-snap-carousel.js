  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ViewImageSnapCarousel = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _color = _$$_REQUIRE(_dependencyMap[5]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[7]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[8]);
  var _reactNativeReanimatedCarousel = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[10]);
  var _text = _$$_REQUIRE(_dependencyMap[11]);
  var _theme = _$$_REQUIRE(_dependencyMap[12]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[13]);
  var _reactNativeVideo = _$$_REQUIRE(_dependencyMap[14]);
  var _lottieReactNative = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[16]);
  var _aemGroupTwo = _$$_REQUIRE(_dependencyMap[17]);
  var _adobe = _$$_REQUIRE(_dependencyMap[18]);
  var _lodash = _$$_REQUIRE(_dependencyMap[19]);
  var _authentication = _$$_REQUIRE(_dependencyMap[20]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[21]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var ratio = 0.*****************;
  var _Dimensions$get = _reactNative2.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var DURATION = 5000;
  var _worklet_11732265836198_init_data = {
    code: "function viewImageSnapCarouselTsx1(isFinished){const{runOnJS,nextSlide,progress}=this.__closure;if(isFinished){runOnJS(nextSlide)();progress.value=0;}}"
  };
  var _worklet_3781595687774_init_data = {
    code: "function viewImageSnapCarouselTsx2(){const{progress}=this.__closure;return{width:progress.value*100+\"%\"};}"
  };
  var ViewImageSnapCarousel = exports.ViewImageSnapCarousel = _react.default.memo(function (props) {
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("EXPLORE_SCREEN_V2"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var carouselRef = (0, _react.useRef)(null);
    var videoRefs = (0, _react.useRef)([]);
    var dataMathead = props.dataMathead,
      isErrorMathead = props.isErrorMathead,
      isLoadingMathead = props.isLoadingMathead,
      isFocused = props.isFocused,
      onProgressChange = props.onProgressChange;
    var isDarkApp = (0, _reactRedux.useSelector)(_aemGroupTwo.AemGroupTwoSelectors.isDarkApp);
    var _useState = (0, _react.useState)(0),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      active = _useState2[0],
      setActive = _useState2[1];
    var progress = (0, _reactNativeReanimated.useSharedValue)(0);
    var currentDuration = (0, _reactNativeReanimated.useSharedValue)(0);
    var nextSlide = function nextSlide() {
      var _carouselRef$current;
      (_carouselRef$current = carouselRef.current) == null || _carouselRef$current.next == null || _carouselRef$current.next();
    };
    var startProgress = function startProgress() {
      var duration = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : DURATION;
      currentDuration.value = duration;
      progress.value = (0, _reactNativeReanimated.withTiming)(1, {
        duration: duration
      }, function () {
        var viewImageSnapCarouselTsx1 = function viewImageSnapCarouselTsx1(isFinished) {
          if (isFinished) {
            (0, _reactNativeReanimated.runOnJS)(nextSlide)();
            progress.value = 0;
          }
        };
        viewImageSnapCarouselTsx1.__closure = {
          runOnJS: _reactNativeReanimated.runOnJS,
          nextSlide: nextSlide,
          progress: progress
        };
        viewImageSnapCarouselTsx1.__workletHash = 11732265836198;
        viewImageSnapCarouselTsx1.__initData = _worklet_11732265836198_init_data;
        return viewImageSnapCarouselTsx1;
      }());
    };
    (0, _react.useEffect)(function () {
      if (dataMathead && dataMathead.length > 1 && isFocused) {
        var _dataMathead$active;
        if ((_dataMathead$active = dataMathead[active]) != null && _dataMathead$active.videoUrl) {
          var _dataMathead$active2, _dataMathead$active3, _dataMathead$active4, _videoRefs$current$ac;
          var duration = (_dataMathead$active2 = dataMathead[active]) != null && _dataMathead$active2.videoDuration && ((_dataMathead$active3 = dataMathead[active]) == null ? undefined : _dataMathead$active3.videoDuration) > 5 ? (_dataMathead$active4 = dataMathead[active]) == null ? undefined : _dataMathead$active4.videoDuration : 5;
          (_videoRefs$current$ac = videoRefs.current[active]) == null || _videoRefs$current$ac.resume();
          startProgress(duration * 1000);
        } else {
          startProgress();
        }
      }
    }, [active, dataMathead, progress, isFocused]);
    (0, _react.useEffect)(function () {
      if (!isFocused) {
        var _videoRefs$current$ac2;
        (_videoRefs$current$ac2 = videoRefs.current[active]) == null || _videoRefs$current$ac2.pause();
      }
    }, [isFocused, active]);
    var handleOnclick = function handleOnclick(item) {
      var _item$cta$navigation, _item$cta, _item$cta2;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppHomeExploreMasthead, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppHomeExploreMasthead, `${active + 1} | ${item == null ? undefined : item.title}`));
      var _ref = (_item$cta$navigation = item == null || (_item$cta = item.cta) == null ? undefined : _item$cta.navigation) != null ? _item$cta$navigation : {},
        type = _ref.type,
        value = _ref.value;
      var _ref2 = (_item$cta2 = item == null ? undefined : item.cta) != null ? _item$cta2 : {},
        redirect = _ref2.redirect;
      if (!type || !value) return;
      handleNavigation(type, value, Object.assign({}, redirect, {
        aaTag: `Explore | Masthead | ${item.title}`,
        isLoggedInAtTriggerTime: (0, _authentication.checkLoginState)(),
        pageSource: _navigationHelper.NavigationPageSource.Masthead
      }));
    };
    var renderCarouselItem = function renderCarouselItem(_ref3) {
      var item = _ref3.item,
        index = _ref3.index;
      return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: styles.viewItem,
        activeOpacity: 0.9,
        onPress: function onPress() {
          return handleOnclick(item);
        },
        disabled: (0, _lodash.isEmpty)(item == null ? undefined : item.cta),
        children: [item != null && item.videoUrl ? (0, _jsxRuntime.jsx)(_reactNativeVideo.Video, {
          ref: function ref(_ref4) {
            videoRefs.current[index] = _ref4;
          },
          paused: true,
          source: {
            uri: (0, _mediaHelper.handleImageUrl)(item == null ? undefined : item.videoUrl)
          },
          style: styles.viewItemVideo,
          repeat: true,
          resizeMode: "cover",
          poster: {
            source: {
              uri: (0, _mediaHelper.handleImageUrl)(item == null ? undefined : item.image)
            },
            resizeMode: "cover"
          }
        }) : item != null && item.lottie ? (0, _jsxRuntime.jsx)(_lottieReactNative.default, {
          style: styles.lottieStyle,
          source: {
            uri: (0, _mediaHelper.handleImageUrl)(item == null ? undefined : item.lottie)
          },
          autoPlay: true,
          loop: true,
          resizeMode: "contain"
        }) : (0, _jsxRuntime.jsx)(_baseImage.default, {
          source: {
            uri: (0, _mediaHelper.handleImageUrl)(item == null ? undefined : item.image)
          },
          style: styles.viewItem
        }), (item == null ? undefined : item.videoUrl) && _reactNative2.Platform.OS === 'android' && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: styles.viewButtonVideo,
          activeOpacity: 0,
          onPress: function onPress() {
            return handleOnclick(item);
          }
        })]
      });
    };
    var progressAnimStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var viewImageSnapCarouselTsx2 = function viewImageSnapCarouselTsx2() {
        return {
          width: `${progress.value * 100}%`
        };
      };
      viewImageSnapCarouselTsx2.__closure = {
        progress: progress
      };
      viewImageSnapCarouselTsx2.__workletHash = 3781595687774;
      viewImageSnapCarouselTsx2.__initData = _worklet_3781595687774_init_data;
      return viewImageSnapCarouselTsx2;
    }());
    if (isDarkApp) {
      return null;
    }
    if (isLoadingMathead) {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.containerError
      });
    }
    if (isErrorMathead) {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.containerError,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          tx: "exploreScreenV2.titleErrorMathead",
          style: styles.txtTitleError
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          tx: "exploreScreenV2.contentErrorMathead",
          style: styles.txtContentError
        })]
      });
    }
    if (!isLoadingMathead && (!dataMathead || dataMathead.length === 0) && !isErrorMathead) {
      return null;
    }
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsx)(_reactNativeReanimatedCarousel.default, {
        ref: carouselRef,
        autoPlayReverse: true,
        data: dataMathead,
        style: styles.container,
        renderItem: renderCarouselItem,
        width: width + 1,
        minScrollDistancePerSwipe: 1,
        onConfigurePanGesture: function onConfigurePanGesture(panGesture) {
          panGesture.activeOffsetX([-8, 8]);
          panGesture.onBegin(function (e) {
            var _videoRefs$current$ac3;
            (0, _reactNativeReanimated.cancelAnimation)(progress);
            (_videoRefs$current$ac3 = videoRefs.current[active]) == null || _videoRefs$current$ac3.pause();
          }).onFinalize(function () {
            var _videoRefs$current$ac4;
            (_videoRefs$current$ac4 = videoRefs.current[active]) == null || _videoRefs$current$ac4.resume();
            startProgress((1 - progress.value / 1) * currentDuration.value);
          }).runOnJS(true);
        },
        onScrollEnd: function onScrollEnd(index) {
          setActive(function (prev) {
            if (prev !== index) {
              var _videoRefs$current$ac5, _videoRefs$current$ac6;
              progress.value = 0;
              (_videoRefs$current$ac5 = videoRefs.current[active]) == null || _videoRefs$current$ac5.seek(0);
              (_videoRefs$current$ac6 = videoRefs.current[active]) == null || _videoRefs$current$ac6.pause();
            }
            return index;
          });
        },
        onProgressChange: onProgressChange,
        scrollAnimationDuration: 200
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.viewRow,
        children: [(dataMathead == null ? undefined : dataMathead.length) > 1 && (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.viewBlur
        }), (dataMathead == null ? undefined : dataMathead.length) > 1 && (dataMathead == null ? undefined : dataMathead.map(function (item, index) {
          return (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: active === index ? styles.viewActive : styles.viewInActive,
            children: active === index ? (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
              style: [styles.progressBar, progressAnimStyle]
            }) : null
          }, index);
        }))]
      })]
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    viewBlur: {
      position: 'absolute',
      borderRadius: 99,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: "rgba(18, 18, 18, 0.2)"
    },
    containerError: {
      width: '100%',
      height: width * ratio,
      borderTopRightRadius: 20,
      borderTopLeftRadius: 20,
      backgroundColor: _color.color.palette.lightestGrey,
      justifyContent: 'center',
      alignItems: 'center'
    },
    container: {
      width: '100%',
      height: width * ratio,
      borderTopRightRadius: 20,
      borderTopLeftRadius: 20,
      backgroundColor: _color.color.palette.almostBlackGrey30
    },
    viewItem: {
      flex: 1
    },
    viewItemVideo: {
      flex: 1,
      backgroundColor: 'black'
    },
    viewButtonVideo: {
      flex: 1,
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0
    },
    lottieStyle: {
      width: '100%',
      height: width * ratio
    },
    viewRow: {
      position: 'absolute',
      bottom: 16,
      right: 12,
      padding: 4,
      gap: 4,
      flexDirection: 'row'
    },
    progressBar: {
      position: 'absolute',
      left: 0,
      bottom: 0,
      height: 4,
      backgroundColor: _color.color.palette.whiteGrey,
      borderRadius: 99
    },
    viewInActive: {
      width: 8,
      height: 4,
      borderRadius: 99,
      backgroundColor: '#FCFCFC99'
    },
    viewActive: {
      width: 20,
      height: 4,
      borderRadius: 99,
      backgroundColor: '#FCFCFC99'
    },
    txtTitleError: {
      fontFamily: _theme.typography.bold,
      color: _color.color.palette.almostBlackGrey,
      fontSize: 11,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 14
    },
    txtContentError: {
      fontFamily: _theme.typography.bold,
      color: _color.color.palette.darkGrey999,
      fontSize: 11,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 14,
      marginTop: 4
    }
  });
