  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.CurvedView = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNativeSvg = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[3]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var width = _reactNative.Dimensions.get('window').width + 2;
  var height = _reactNative.Dimensions.get('window').height;
  var MAX_TRANSLATE = 179;
  var MAX_CURVE = 60;
  var AnimatedPath = _reactNativeReanimated.default.createAnimatedComponent(_reactNativeSvg.Path);
  var _worklet_3125503466531_init_data = {
    code: "function viewCurveTsx1(){const{interpolate,translateY,MAX_TRANSLATE,MAX_CURVE,width,height}=this.__closure;const rawCurveY=interpolate(translateY.value,[140,MAX_TRANSLATE],[0,MAX_CURVE]);const curveY=Math.min(rawCurveY,MAX_CURVE);return{d:\"\\n        M0,0 \\n        Q\"+width/2+\",\"+curveY+\" \"+width+\",0\\n        L\"+width+\",\"+height+\" \\n        L0,\"+height+\" \\n        Z\\n      \"};}"
  };
  var CurvedView = exports.CurvedView = function CurvedView(_ref) {
    var translateY = _ref.translateY;
    //@ts-ignore
    var animatedProps = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var viewCurveTsx1 = function viewCurveTsx1() {
        var rawCurveY = (0, _reactNativeReanimated.interpolate)(translateY.value, [140, MAX_TRANSLATE], [0, MAX_CURVE]);
        var curveY = Math.min(rawCurveY, MAX_CURVE);
        return {
          d: `
        M0,0 
        Q${width / 2},${curveY} ${width},0
        L${width},${height} 
        L0,${height} 
        Z
      `
        };
      };
      viewCurveTsx1.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        translateY: translateY,
        MAX_TRANSLATE: MAX_TRANSLATE,
        MAX_CURVE: MAX_CURVE,
        width: width,
        height: height
      };
      viewCurveTsx1.__workletHash = 3125503466531;
      viewCurveTsx1.__initData = _worklet_3125503466531_init_data;
      return viewCurveTsx1;
    }());
    return (0, _jsxRuntime.jsx)(_reactNativeSvg.default, {
      width: width,
      height: height,
      style: {
        position: 'absolute',
        top: 0
      },
      children: (0, _jsxRuntime.jsx)(AnimatedPath
      //@ts-ignore
      , {
        animatedProps: animatedProps,
        fill: "#121212"
      })
    });
  };
