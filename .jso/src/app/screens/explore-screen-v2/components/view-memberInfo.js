  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ViewMemberInfo = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _useRewardTier2 = _$$_REQUIRE(_dependencyMap[8]);
  var _lodash = _$$_REQUIRE(_dependencyMap[9]);
  var _icons = _$$_REQUIRE(_dependencyMap[10]);
  var _viewMemberInfoError = _$$_REQUIRE(_dependencyMap[11]);
  var _constants = _$$_REQUIRE(_dependencyMap[12]);
  var _native = _$$_REQUIRE(_dependencyMap[13]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _adobe = _$$_REQUIRE(_dependencyMap[15]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[16]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var LOADING_COLORS = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var transformRewardPoint = function transformRewardPoint(rewardsData) {
    var points = (0, _lodash.get)(rewardsData, "reward.point");
    if (points) {
      var pointSubstr = points.substring(0, 8);
      if (points.length > 10) {
        return `${pointSubstr}...`;
      }
      return points;
    }
    return "";
  };
  var ViewMemberInfoLoading = function ViewMemberInfoLoading() {
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.viewTxt,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: LOADING_COLORS,
          shimmerStyle: styles.loadingTitle
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: LOADING_COLORS,
          shimmerStyle: styles.loadingSubTitle
        })]
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: LOADING_COLORS,
        shimmerStyle: styles.loadingButton
      })]
    });
  };
  var ViewMemberInfo = exports.ViewMemberInfo = _react.default.memo(function (props) {
    var profileFetching = props.profileFetching,
      profileError = props.profileError,
      userProfile = props.userProfile,
      rewardsFetching = props.rewardsFetching,
      rewardsError = props.rewardsError,
      onShowRewardCard = props.onShowRewardCard,
      onChangiPayPressed = props.onChangiPayPressed,
      isLoggedIn = props.isLoggedIn,
      rewardsData = props.rewardsData,
      onReLoadProfileAndReward = props.onReLoadProfileAndReward,
      isShowWhiteBackground = props.isShowWhiteBackground;
    var navigation = (0, _native.useNavigation)();
    var _useRewardTier = (0, _useRewardTier2.useRewardTier)(),
      memberIconInfo = _useRewardTier.memberIconInfo;
    var BFIcon = memberIconInfo == null ? undefined : memberIconInfo.bfIcon;
    var colorTextButton = isShowWhiteBackground ? _theme.color.palette.almostBlackGrey : memberIconInfo == null ? undefined : memberIconInfo.colorTextRewardCard;
    var rewardPoints = (0, _react.useMemo)(function () {
      return transformRewardPoint(rewardsData);
    }, [rewardsData]);
    var firstName = (0, _lodash.get)(userProfile, "firstName");
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      initComponent = _useState2[0],
      setInit = _useState2[1];
    (0, _react.useEffect)(function () {
      if (rewardPoints && userProfile) {
        setInit(true);
      }
    }, [rewardPoints, userProfile]);
    var errorState = profileError || rewardsError;
    var fetchingState = (profileFetching || rewardsFetching) && (!initComponent || errorState);
    var renderTitle = function renderTitle() {
      if (isLoggedIn) {
        return `Hi ${firstName}`;
      } else {
        return "Hi!";
      }
    };
    var onLogin = function onLogin() {
      navigation.navigate(_constants.NavigationConstants.authScreen, {
        sourceSystem: _constants.SOURCE_SYSTEM.OTHERS,
        callBackAfterLoginSuccess: undefined,
        callBackAfterLoginCancel: undefined
      });
    };
    var onPressPoint = function onPressPoint() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppHomeExploreMembershipCard, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppHomeExploreMembershipCard, "Changi Rewards Points"));
      navigation.navigate(_constants.NavigationConstants.redemptionCatalogueScreen, {
        screen: _constants.NavigationConstants.pointsTab
      });
    };
    var onPressChangiPay = function onPressChangiPay() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppHomeExploreMembershipCard, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppHomeExploreMembershipCard, "Wallet"));
      onChangiPayPressed == null || onChangiPayPressed();
    };
    var onPressRewardCard = function onPressRewardCard() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppHomeExploreMembershipCard, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppHomeExploreMembershipCard, "Rewards Card"));
      onShowRewardCard == null || onShowRewardCard();
    };
    var titleStyle = isShowWhiteBackground ? styles.titleBlack : styles.title;
    var pointStyle = isShowWhiteBackground ? styles.txtPointBlack : styles.txtPoint;
    var buttonStyle = isShowWhiteBackground ? styles.buttonBlack : styles.button;
    var renderContentSubTitle = function renderContentSubTitle() {
      if (isLoggedIn) {
        return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: [styles.viewRow, {
            gap: 12
          }],
          children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: onPressPoint,
            children: (0, _jsxRuntime.jsxs)(_text.Text, {
              style: pointStyle,
              children: [rewardPoints, " pts"]
            })
          }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
            style: styles.viewRow,
            onPress: onPressChangiPay,
            children: [(0, _jsxRuntime.jsx)(_icons.Wallet, {
              color: isShowWhiteBackground ? _theme.color.palette.darkGrey999 : _theme.color.palette.whiteGrey
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              style: pointStyle,
              tx: "homepageMasthead.wallet"
            })]
          })]
        });
      } else {
        return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: onLogin,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            tx: "homepageMasthead.logInText",
            style: pointStyle
          })
        });
      }
    };
    if (fetchingState) {
      return (0, _jsxRuntime.jsx)(ViewMemberInfoLoading, {});
    }
    if (errorState) {
      return (0, _jsxRuntime.jsx)(_viewMemberInfoError.ViewMemberInfoError, {
        onReload: onReLoadProfileAndReward
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.viewTxt,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: titleStyle,
          numberOfLines: 1,
          children: renderTitle()
        }), renderContentSubTitle()]
      }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: buttonStyle,
        onPress: onPressRewardCard,
        activeOpacity: 0.7,
        children: [isShowWhiteBackground ? (0, _jsxRuntime.jsx)(_icons.CRBFIconDark, {
          style: {
            marginRight: 4
          }
        }) : (0, _jsxRuntime.jsx)(BFIcon, {}), (0, _jsxRuntime.jsx)(_text.Text, {
          tx: "homepageMasthead.rewardsCard",
          style: [styles.txtButton, {
            color: colorTextButton
          }]
        })]
      })]
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    container: {
      paddingHorizontal: 18,
      paddingVertical: 16,
      flexDirection: "row",
      alignItems: "center",
      gap: 12
    },
    title: {
      fontFamily: _theme.typography.black,
      color: _theme.color.palette.whiteGrey,
      fontSize: 16,
      fontWeight: _reactNative2.Platform.select({
        ios: "900",
        android: "normal"
      }),
      lineHeight: 20,
      marginBottom: 8
    },
    titleBlack: {
      fontFamily: _theme.typography.black,
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 16,
      fontWeight: _reactNative2.Platform.select({
        ios: "900",
        android: "normal"
      }),
      lineHeight: 20,
      marginBottom: 8
    },
    button: {
      width: 120,
      height: 32,
      borderRadius: 99,
      backgroundColor: _theme.color.palette.whiteGrey,
      justifyContent: "center",
      alignItems: "center",
      flexDirection: "row"
    },
    buttonBlack: {
      width: 120,
      height: 32,
      borderRadius: 99,
      backgroundColor: _theme.color.palette.whiteGrey,
      borderColor: _theme.color.palette.lighterGrey,
      borderWidth: 1,
      justifyContent: "center",
      alignItems: "center",
      flexDirection: "row"
    },
    viewTxt: {
      flex: 1
    },
    viewRow: {
      flexDirection: "row",
      alignItems: "center",
      gap: 4
    },
    txtButton: {
      fontFamily: _theme.typography.bold,
      fontSize: 12,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 16
    },
    txtPoint: {
      fontFamily: _theme.typography.regular,
      fontSize: 12,
      fontWeight: _reactNative2.Platform.select({
        ios: "400",
        android: "normal"
      }),
      lineHeight: 16,
      color: _theme.color.palette.almostWhiteGrey80
    },
    txtPointBlack: {
      fontFamily: _theme.typography.regular,
      fontSize: 12,
      fontWeight: _reactNative2.Platform.select({
        ios: "400",
        android: "normal"
      }),
      lineHeight: 16,
      color: _theme.color.palette.darkestGrey
    },
    loadingTitle: {
      width: 88,
      height: 16,
      borderRadius: 4,
      marginBottom: 8
    },
    loadingSubTitle: {
      width: 48,
      height: 12,
      borderRadius: 4
    },
    loadingButton: {
      width: 120,
      height: 32,
      borderRadius: 99
    }
  });
