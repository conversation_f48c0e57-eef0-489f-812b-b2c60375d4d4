  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ViewBlurBackgroundSecondFloor = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _backgrounds = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeColorMatrixImageFilters = _$$_REQUIRE(_dependencyMap[4]);
  var _lodash = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var widthScreen = _reactNative.Dimensions.get('window').width;
  var _worklet_14954035134759_init_data = {
    code: "function viewBlurSecondFloorTsx1(){const{interpolate,translateY}=this.__closure;const opacity=interpolate(translateY.value,[0,5],[0,1]);return{opacity:opacity};}"
  };
  var ViewBlurBackgroundSecondFloor = exports.ViewBlurBackgroundSecondFloor = _react.default.memo(function (props) {
    var ref = props.ref,
      isConnectInternet = props.isConnectInternet,
      isDarkApp = props.isDarkApp,
      images = props.images,
      translateY = props.translateY;
    var ContainerComponent = isDarkApp ? _reactNativeColorMatrixImageFilters.Grayscale : _reactNative.View;
    var animatedShowViewBlack = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var viewBlurSecondFloorTsx1 = function viewBlurSecondFloorTsx1() {
        var opacity = (0, _reactNativeReanimated.interpolate)(translateY.value, [0, 5], [0, 1]);
        return {
          opacity: opacity
        };
      };
      viewBlurSecondFloorTsx1.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        translateY: translateY
      };
      viewBlurSecondFloorTsx1.__workletHash = 14954035134759;
      viewBlurSecondFloorTsx1.__initData = _worklet_14954035134759_init_data;
      return viewBlurSecondFloorTsx1;
    }());
    if ((0, _lodash.isEmpty)(images)) {
      return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsxs)(ContainerComponent, {
          style: styles.emptyContainer,
          children: [(0, _jsxRuntime.jsx)(_reactNative.Image, {
            source: _backgrounds.BackgroundDefaultExploreV2,
            style: styles.image,
            resizeMode: "cover",
            blurRadius: 4
          }), (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: styles.backdropContainerStyle
          })]
        }), (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
          style: [styles.viewBlack, animatedShowViewBlack]
        })]
      });
    }
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_reactNative.ScrollView, {
        ref: ref,
        horizontal: true,
        scrollEnabled: true,
        style: {
          height: widthScreen,
          position: 'absolute',
          top: 0,
          zIndex: -1
        },
        contentContainerStyle: {
          width: widthScreen * (images == null ? undefined : images.length)
        },
        pagingEnabled: true,
        showsHorizontalScrollIndicator: false,
        children: images.map(function (image, index) {
          return (0, _jsxRuntime.jsxs)(ContainerComponent, {
            style: styles.container,
            children: [(0, _jsxRuntime.jsx)(_reactNative.Image, {
              source: image && isConnectInternet ? {
                uri: image
              } : _backgrounds.BackgroundDefaultExploreV2,
              style: styles.image,
              resizeMode: "cover",
              blurRadius: 4
            }), (0, _jsxRuntime.jsx)(_reactNative.View, {
              style: styles.backdropContainerStyle
            })]
          }, `${image}-${index}1`);
        })
      }), (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
        style: [styles.viewBlack, animatedShowViewBlack]
      })]
    });
  });
  var styles = _reactNative.StyleSheet.create({
    emptyContainer: {
      width: widthScreen,
      height: widthScreen,
      position: "absolute",
      top: 0,
      zIndex: -1
    },
    container: {
      width: widthScreen,
      height: widthScreen
    },
    image: {
      width: widthScreen,
      height: widthScreen
    },
    backdropContainerStyle: {
      width: widthScreen,
      height: widthScreen,
      position: "absolute",
      top: 0,
      right: 0,
      left: 0,
      backgroundColor: "rgba(18,18,18, 0.85)"
    },
    viewBlack: {
      position: "absolute",
      zIndex: -1,
      width: widthScreen,
      height: widthScreen,
      backgroundColor: "#121212",
      top: -1
    }
  });
