  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ViewHeaderSecondFloor = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _typography = _$$_REQUIRE(_dependencyMap[6]);
  var _icons = _$$_REQUIRE(_dependencyMap[7]);
  var _native = _$$_REQUIRE(_dependencyMap[8]);
  var _constants = _$$_REQUIRE(_dependencyMap[9]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[11]));
  var _theme = _$$_REQUIRE(_dependencyMap[12]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[13]);
  var _aemGroupTwo = _$$_REQUIRE(_dependencyMap[14]);
  var _adobe = _$$_REQUIRE(_dependencyMap[15]);
  var _searchIndex = _$$_REQUIRE(_dependencyMap[16]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[17]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var widthScreen = _reactNative2.Dimensions.get('window').width;
  var widthViewSearchDefault = widthScreen - 32 - 36 - 12;
  var height_screen = _reactNative2.Dimensions.get("window").height;
  var _worklet_14615274275744_init_data = {
    code: "function viewHeaderTsx1(){const{interpolate,translationY,positionScroll}=this.__closure;const opacity=interpolate(translationY.value,[0,positionScroll],[0,1]);return{opacity:opacity};}"
  };
  var _worklet_589951470335_init_data = {
    code: "function viewHeaderTsx2(){const{interpolate,translationY,positionScroll,widthViewSearchDefault}=this.__closure;const width=interpolate(translationY.value,[0,positionScroll,positionScroll+1],[widthViewSearchDefault,widthViewSearchDefault-48,widthViewSearchDefault-48]);return{width:width};}"
  };
  var _worklet_15791986916748_init_data = {
    code: "function viewHeaderTsx3(){const{interpolate,translationY}=this.__closure;const top=interpolate(translationY.value,[-1,0,translationY.value],[0,0,-translationY.value]);return{top:top};}"
  };
  var _worklet_12642188331625_init_data = {
    code: "function viewHeaderTsx4(){const{height_screen,interpolate,translateY}=this.__closure;const height_screen_20_percent=height_screen*20/100;const opacity=interpolate(translateY.value,[0,height_screen_20_percent-1,height_screen_20_percent],[0,0,1]);return{opacity:opacity};}"
  };
  var ViewHeaderSecondFloor = exports.ViewHeaderSecondFloor = _react.default.memo(function (props) {
    var translationY = props.translationY,
      colors = props.colors,
      positionScroll = props.positionScroll,
      onOpenRewardCard = props.onOpenRewardCard,
      isLoggedIn = props.isLoggedIn,
      loadingRefresh = props.loadingRefresh,
      translateY = props.translateY;
    var navigation = (0, _native.useNavigation)();
    var isDarkApp = (0, _reactRedux.useSelector)(_aemGroupTwo.AemGroupTwoSelectors.isDarkApp);
    var navigateToSearch = function navigateToSearch() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppHomeExploreHeader, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppHomeExploreHeader, "Search Bar"));
      navigation.navigate(_constants.NavigationConstants.search, {
        sourcePage: _adobe.AdobeTagName.CAppHomePage,
        screen: _searchIndex.SearchIndex.flights
      });
    };
    var navigateToRedeem = function navigateToRedeem() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppHomeExploreHeader, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppHomeExploreHeader, "Perks Icon"));
      if (isLoggedIn) {
        navigation.navigate(_constants.NavigationConstants.redemptionCatalogueScreen, {
          screen: _constants.NavigationConstants.perksTab
        });
      } else {
        navigation.navigate(_constants.NavigationConstants.authScreen, {
          sourceSystem: _constants.SOURCE_SYSTEM.OTHERS,
          callBackAfterLoginSuccess: function callBackAfterLoginSuccess() {
            navigation.navigate(_constants.NavigationConstants.redemptionCatalogueScreen, {
              screen: _constants.NavigationConstants.perksTab
            });
          },
          callBackAfterLoginCancel: undefined
        });
      }
    };
    var onPressCRCard = function onPressCRCard() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppHomeExploreHeader, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppHomeExploreHeader, "Rewards Card"));
      onOpenRewardCard == null || onOpenRewardCard();
    };
    var animatedBackground = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var viewHeaderTsx1 = function viewHeaderTsx1() {
        var opacity = (0, _reactNativeReanimated.interpolate)(translationY.value, [0, positionScroll], [0, 1]);
        return {
          opacity: opacity
        };
      };
      viewHeaderTsx1.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        translationY: translationY,
        positionScroll: positionScroll
      };
      viewHeaderTsx1.__workletHash = 14615274275744;
      viewHeaderTsx1.__initData = _worklet_14615274275744_init_data;
      return viewHeaderTsx1;
    }());
    var animatedWidthSearchBar = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var viewHeaderTsx2 = function viewHeaderTsx2() {
        var width = (0, _reactNativeReanimated.interpolate)(translationY.value, [0, positionScroll, positionScroll + 1], [widthViewSearchDefault, widthViewSearchDefault - 48, widthViewSearchDefault - 48]);
        return {
          width: width
        };
      };
      viewHeaderTsx2.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        translationY: translationY,
        positionScroll: positionScroll,
        widthViewSearchDefault: widthViewSearchDefault
      };
      viewHeaderTsx2.__workletHash = 589951470335;
      viewHeaderTsx2.__initData = _worklet_589951470335_init_data;
      return viewHeaderTsx2;
    }());
    var animatedPosition = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var viewHeaderTsx3 = function viewHeaderTsx3() {
        var top = (0, _reactNativeReanimated.interpolate)(translationY.value, [-1, 0, translationY.value], [0, 0, -translationY.value]);
        return {
          top: top
        };
      };
      viewHeaderTsx3.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        translationY: translationY
      };
      viewHeaderTsx3.__workletHash = 15791986916748;
      viewHeaderTsx3.__initData = _worklet_15791986916748_init_data;
      return viewHeaderTsx3;
    }());
    var animatedLoading = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var viewHeaderTsx4 = function viewHeaderTsx4() {
        var height_screen_20_percent = height_screen * 20 / 100;
        var opacity = (0, _reactNativeReanimated.interpolate)(translateY.value, [0, height_screen_20_percent - 1, height_screen_20_percent], [0, 0, 1]);
        return {
          opacity: opacity
        };
      };
      viewHeaderTsx4.__closure = {
        height_screen: height_screen,
        interpolate: _reactNativeReanimated.interpolate,
        translateY: translateY
      };
      viewHeaderTsx4.__workletHash = 12642188331625;
      viewHeaderTsx4.__initData = _worklet_12642188331625_init_data;
      return viewHeaderTsx4;
    }());
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
        style: [loadingRefresh ? styles.containerLoading : styles.container, animatedPosition],
        children: [loadingRefresh && (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.viewLoadingRefresh,
          children: (0, _jsxRuntime.jsx)(_reactNative2.ActivityIndicator, {
            size: "small",
            color: _theme.color.palette.whiteGrey
          })
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.viewRowContent,
          children: [(0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
            style: styles.viewSearchWhite,
            onPress: navigateToSearch,
            activeOpacity: 0.7,
            children: [(0, _jsxRuntime.jsx)(_icons.SearchBox, {
              width: 16,
              height: 16,
              color: "#FCFCFC99"
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              tx: "exploreScreenV2.titleSearch",
              style: styles.txtTitle,
              numberOfLines: 1
            })]
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: styles.marginIcon,
            onPress: navigateToRedeem,
            activeOpacity: 0.7,
            children: (0, _jsxRuntime.jsx)(_reactNative2.Image, {
              source: _icons.RewardStarIcon,
              style: styles.iconReward
            })
          })]
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
        style: [styles.container, animatedBackground],
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: [styles.viewAbsolute],
          children: (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
            colors: isDarkApp ? ["rgb(59,59,59)", "rgb(97,97,97)", "rgb(252,252,252)"] : colors,
            locations: [0, 1, 1],
            start: {
              x: 0.5,
              y: 0
            },
            end: {
              x: 0.5,
              y: 1
            },
            style: styles.viewLinear
          })
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.viewRowContent,
          children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: navigateToSearch,
            activeOpacity: 0.7,
            children: (0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
              style: [styles.viewSearch, animatedWidthSearchBar],
              children: [(0, _jsxRuntime.jsx)(_icons.SearchBox, {
                width: 16,
                height: 16,
                color: "#FCFCFC99"
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                tx: "searchV2.header",
                style: styles.txtTitle,
                numberOfLines: 1
              })]
            })
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: styles.marginIcon,
            onPress: navigateToRedeem,
            activeOpacity: 0.7,
            children: (0, _jsxRuntime.jsx)(_reactNative2.Image, {
              source: _icons.RewardStarIcon,
              style: styles.iconReward
            })
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: styles.marginIcon,
            onPress: onPressCRCard,
            activeOpacity: 0.7,
            children: (0, _jsxRuntime.jsx)(_icons.CRCardNoColor, {
              width: 36,
              height: 36
            })
          })]
        })]
      }), !loadingRefresh && (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
        style: [styles.viewLoading, animatedLoading],
        children: (0, _jsxRuntime.jsx)(_reactNative2.ActivityIndicator, {
          size: "small",
          color: _theme.color.palette.whiteGrey
        })
      })]
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    viewLoading: {
      width: '100%',
      position: 'absolute',
      top: 10,
      alignItems: 'center'
    },
    viewAbsolute: {
      position: 'absolute',
      top: 0,
      bottom: 0,
      left: 0,
      right: 0
    },
    viewLinear: {
      flex: 1
    },
    containerLoading: {
      position: 'absolute',
      top: 0,
      width: '100%',
      height: 120,
      justifyContent: 'flex-end',
      paddingBottom: 8,
      paddingHorizontal: 16
    },
    container: {
      position: 'absolute',
      top: 0,
      width: '100%',
      height: 100,
      justifyContent: 'flex-end',
      paddingBottom: 8,
      paddingHorizontal: 16
    },
    viewRowContent: {
      flexDirection: 'row',
      alignItems: 'center'
    },
    marginIcon: {
      marginLeft: 12
    },
    viewSearchWhite: {
      height: 36,
      width: widthViewSearchDefault,
      borderRadius: 8,
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 8,
      backgroundColor: '#FFFFFF1A'
    },
    viewSearch: {
      height: 36,
      width: widthViewSearchDefault,
      borderRadius: 8,
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 8,
      backgroundColor: _theme.color.palette.almostBlackGrey30
    },
    txtTitle: {
      fontFamily: _typography.typography.regular,
      color: "#FCFCFC99",
      fontSize: 14,
      fontWeight: _reactNative2.Platform.select({
        ios: "400",
        android: "normal"
      }),
      lineHeight: 18,
      marginLeft: 6
    },
    iconReward: {
      width: 36,
      height: 36
    },
    viewLoadingRefresh: {
      width: "100%",
      paddingVertical: 12,
      justifyContent: "center",
      alignItems: "center"
    }
  });
