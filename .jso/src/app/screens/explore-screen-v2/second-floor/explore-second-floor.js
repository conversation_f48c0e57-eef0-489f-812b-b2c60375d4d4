  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _useFunction2 = _$$_REQUIRE(_dependencyMap[7]);
  var _components = _$$_REQUIRE(_dependencyMap[8]);
  var _theme = _$$_REQUIRE(_dependencyMap[9]);
  var _focusStatusBar = _$$_REQUIRE(_dependencyMap[10]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _useRewardTier2 = _$$_REQUIRE(_dependencyMap[12]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[13]);
  var _constants = _$$_REQUIRE(_dependencyMap[14]);
  var _scrollBuddy = _$$_REQUIRE(_dependencyMap[15]);
  var _exploreContainer = _$$_REQUIRE(_dependencyMap[16]);
  var _tabBarContainer = _$$_REQUIRE(_dependencyMap[17]);
  var _exploreChangiModalFilter = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[18]));
  var _errorCloudV = _$$_REQUIRE(_dependencyMap[19]);
  var _icons = _$$_REQUIRE(_dependencyMap[20]);
  var _ViewQuickLinks = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[21]));
  var _aemQuicklinkCategories = _$$_REQUIRE(_dependencyMap[22]);
  var _button = _$$_REQUIRE(_dependencyMap[23]);
  var _i18n = _$$_REQUIRE(_dependencyMap[24]);
  var _utils = _$$_REQUIRE(_dependencyMap[25]);
  var _reactNativeDeviceInfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[26]));
  var _grantPermissionModalOldVersion = _$$_REQUIRE(_dependencyMap[27]);
  var _grantPermissionModal = _$$_REQUIRE(_dependencyMap[28]);
  var _tickerBand = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[29]));
  var _cartToast = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[30]));
  var _monarchOverlay = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[31]));
  var _exploreScreen = _$$_REQUIRE(_dependencyMap[32]);
  var _bottomSheetError = _$$_REQUIRE(_dependencyMap[33]);
  var _icons2 = _$$_REQUIRE(_dependencyMap[34]);
  var _adobe = _$$_REQUIRE(_dependencyMap[35]);
  var _reactNativeGestureHandler = _$$_REQUIRE(_dependencyMap[36]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[37]));
  var _text = _$$_REQUIRE(_dependencyMap[38]);
  var _viewCurve = _$$_REQUIRE(_dependencyMap[39]);
  var _bottomNavigator = _$$_REQUIRE(_dependencyMap[40]);
  var _useStatusBarStyle = _$$_REQUIRE(_dependencyMap[41]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[42]);
  var _aemGroupTwo = _$$_REQUIRE(_dependencyMap[43]);
  var _forYouScreen = _$$_REQUIRE(_dependencyMap[44]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[45]));
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[46]);
  var _backgrounds = _$$_REQUIRE(_dependencyMap[47]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[48]);
  var _pageConfigSaga = _$$_REQUIRE(_dependencyMap[49]);
  var _systemRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[50]));
  var _playpassWebview = _$$_REQUIRE(_dependencyMap[51]);
  var _exploreRedux = _$$_REQUIRE(_dependencyMap[52]);
  var _viewHeader = _$$_REQUIRE(_dependencyMap[53]);
  var _viewBlurSecondFloor = _$$_REQUIRE(_dependencyMap[54]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[55]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var ratio = 0.*****************;
  var width_screen = _reactNative2.Dimensions.get("screen").width;
  var height_screen = _reactNative2.Dimensions.get("screen").height;
  var ButtonRetry = _react.default.memo(function (props) {
    var handlePressReload = props.handlePressReload;
    return (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
      style: styles.reloadButtonStyle,
      start: {
        x: 1,
        y: 0
      },
      end: {
        x: 0,
        y: 1
      },
      colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
      children: (0, _jsxRuntime.jsx)(_button.Button, {
        onPress: handlePressReload,
        sizePreset: "large",
        textPreset: "buttonLarge",
        typePreset: "primary",
        tx: "errorOverlay.variant3.retry",
        backgroundPreset: "light",
        statePreset: "default"
      })
    });
  });
  var _worklet_17214413467328_init_data = {
    code: "function exploreSecondFloorTsx1(){const{translateY}=this.__closure;return{transform:[{translateY:translateY.value}]};}"
  };
  var _worklet_13378341547429_init_data = {
    code: "function exploreSecondFloorTsx2(){const{interpolate,translateY,height_screen}=this.__closure;const opacity=interpolate(translateY.value,[401,height_screen],[0,1]);return{opacity:opacity};}"
  };
  var _worklet_2125486700523_init_data = {
    code: "function exploreSecondFloorTsx3(){const{height_screen,interpolate,translateY}=this.__closure;const height_screen_25_percent=height_screen*25/100;const opacity=interpolate(translateY.value,[0,height_screen_25_percent,height_screen_25_percent+1],[0,0,1]);return{opacity:opacity};}"
  };
  var _worklet_5850223770620_init_data = {
    code: "function exploreSecondFloorTsx4(){const{interpolate,translateY}=this.__closure;const top=interpolate(translateY.value,[-1,0,translateY.value],[0,0,translateY.value-150]);return{top:top};}"
  };
  var _worklet_2174880071398_init_data = {
    code: "function exploreSecondFloorTsx5(){const{interpolate,translateY,height_screen}=this.__closure;const opacity=interpolate(translateY.value,[0,height_screen-1,height_screen],[1,1,0]);return{opacity:opacity};}"
  };
  var _worklet_4823164561328_init_data = {
    code: "function exploreSecondFloorTsx6(e){const{runOnJS,setIsScroll,translationY,translateY,withTiming,isConnectInternet,loadingRefresh,setLoadingRefresh,onRefresh,handleRefetchQuicklinks,hideBottombar,setShowWebviewGame,trackingGameOpen,height_screen}=this.__closure;runOnJS(setIsScroll)(true);if(translationY.value===0){if(e.translationY<100){translateY.value=withTiming(0);}else if(e.translationY<400&&isConnectInternet){if(!loadingRefresh){runOnJS(setLoadingRefresh)(true);runOnJS(onRefresh)();runOnJS(handleRefetchQuicklinks)();}translateY.value=withTiming(0);}else{runOnJS(hideBottombar)();runOnJS(setShowWebviewGame)(true);runOnJS(trackingGameOpen)();translateY.value=withTiming(height_screen,{duration:200});}}else{translateY.value=withTiming(0);}}"
  };
  var _worklet_12418178846156_init_data = {
    code: "function exploreSecondFloorTsx7(e){const{isScroll,translationY,runOnJS,setIsScroll,translateY}=this.__closure;if(isScroll&&translationY.value===0){runOnJS(setIsScroll)(false);}if(translationY.value===0){translateY.value=e.translationY;}}"
  };
  var ExploreScreenV2SecondFloor = function ExploreScreenV2SecondFloor(_ref) {
    var route = _ref.route;
    var _useContext = (0, _react.useContext)(_bottomNavigator.BottomNavContext),
      hideBottombar = _useContext.hideBottombar;
    var _ref2 = (route == null ? undefined : route.params) || {},
      _ref2$initialFilterLo = _ref2.initialFilterLocation,
      initialFilterLocation = _ref2$initialFilterLo === undefined ? "" : _ref2$initialFilterLo;
    var _useRewardTier = (0, _useRewardTier2.useRewardTier)(),
      memberIconInfo = _useRewardTier.memberIconInfo;
    (0, _useStatusBarStyle.useStatusBarStyle)('light');
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)(),
      getGamificationAATag = _useHandleNavigation.getGamificationAATag,
      getGamificationLandingPage = _useHandleNavigation.getGamificationLandingPage,
      getGamificationUtmParameters = _useHandleNavigation.getGamificationUtmParameters;
    var _useFunction = (0, _useFunction2.useFunction)({
        routeParams: route == null ? undefined : route.params
      }),
      profileFetching = _useFunction.profileFetching,
      profileError = _useFunction.profileError,
      userProfile = _useFunction.userProfile,
      rewardsData = _useFunction.rewardsData,
      rewardsFetching = _useFunction.rewardsFetching,
      rewardsError = _useFunction.rewardsError,
      translationY = _useFunction.translationY,
      scrollHandler = _useFunction.scrollHandler,
      onShowRewardCard = _useFunction.onShowRewardCard,
      isLoggedIn = _useFunction.isLoggedIn,
      onChangiPayPressed = _useFunction.onChangiPayPressed,
      loadingGetConfig = _useFunction.loadingGetConfig,
      announcementPayload = _useFunction.announcementPayload,
      isExistScrollBuddy = _useFunction.isExistScrollBuddy,
      isShowCardToast = _useFunction.isShowCardToast,
      scrollBuddyOpacity = _useFunction.scrollBuddyOpacity,
      scrollBuddyData = _useFunction.scrollBuddyData,
      scrollBuddyOnPress = _useFunction.scrollBuddyOnPress,
      onRefresh = _useFunction.onRefresh,
      loadingRefresh = _useFunction.loadingRefresh,
      onReLoadProfileAndReward = _useFunction.onReLoadProfileAndReward,
      dataMathead = _useFunction.dataMathead,
      isErrorMathead = _useFunction.isErrorMathead,
      isLoadingMathead = _useFunction.isLoadingMathead,
      isFocused = _useFunction.isFocused,
      checkedFilterLocationState = _useFunction.checkedFilterLocationState,
      setCheckedFilterLocationState = _useFunction.setCheckedFilterLocationState,
      exploreChangiFilterDate = _useFunction.exploreChangiFilterDate,
      setExploreChangiFilterDate = _useFunction.setExploreChangiFilterDate,
      onShowExploreChangiFilterModal = _useFunction.onShowExploreChangiFilterModal,
      titleErrorContentExplore = _useFunction.titleErrorContentExplore,
      messageErrorContentExplore = _useFunction.messageErrorContentExplore,
      isExploreCategoriesError = _useFunction.isExploreCategoriesError,
      isConnectInternet = _useFunction.isConnectInternet,
      shortcutLinksExploreV2 = _useFunction.shortcutLinksExploreV2,
      loadShortcutLinksExploreV2 = _useFunction.loadShortcutLinksExploreV2,
      errorShortcutLinksExploreV2 = _useFunction.errorShortcutLinksExploreV2,
      onRefreshListExplore = _useFunction.onRefreshListExplore,
      showGrantPermissionFlow = _useFunction.showGrantPermissionFlow,
      isDisplayTickerBand = _useFunction.isDisplayTickerBand,
      tickerBand = _useFunction.tickerBand,
      tickerBandDescription = _useFunction.tickerBandDescription,
      tickerBandButtonText = _useFunction.tickerBandButtonText,
      onPressCTA = _useFunction.onPressCTA,
      onCloseTickerBand = _useFunction.onCloseTickerBand,
      SCREEN_NAME = _useFunction.SCREEN_NAME,
      toastRef = _useFunction.toastRef,
      setIsShowCardToast = _useFunction.setIsShowCardToast,
      loadingExploreCategories = _useFunction.loadingExploreCategories,
      isDarkApp = _useFunction.isDarkApp,
      handleRefetchQuicklinks = _useFunction.handleRefetchQuicklinks,
      playPassUrlPayload = _useFunction.playPassUrlPayload,
      onResetPlayPassUrl = _useFunction.onResetPlayPassUrl,
      exploreChangiTabHeight = _useFunction.exploreChangiTabHeight,
      onExploreChangiTabLayout = _useFunction.onExploreChangiTabLayout,
      onUpdateExploreChangiPageNumber = _useFunction.onUpdateExploreChangiPageNumber,
      scrollViewRef = _useFunction.scrollViewRef,
      currentScrollOffset = _useFunction.currentScrollOffset,
      onExploreContainerLayout = _useFunction.onExploreContainerLayout,
      onMemberInfoLayout = _useFunction.onMemberInfoLayout,
      backgroundEffectImages = _useFunction.backgroundEffectImages,
      translateY = _useFunction.translateY,
      setLoadingRefresh = _useFunction.setLoadingRefresh,
      isScroll = _useFunction.isScroll,
      setIsScroll = _useFunction.setIsScroll,
      dispatch = _useFunction.dispatch,
      navigation = _useFunction.navigation;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      showWebviewGame = _useState2[0],
      setShowWebviewGame = _useState2[1];
    var _useState3 = (0, _react.useState)(null),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      dataWebviewGame = _useState4[0],
      setDataWebviewGame = _useState4[1];
    var accountBannerList = (0, _reactRedux.useSelector)(_aemGroupTwo.AemGroupTwoSelectors.accountBannerList);
    var dataGamificationCard = (0, _react.useMemo)(function () {
      return accountBannerList == null ? undefined : accountBannerList.find(function (item) {
        return (item == null ? undefined : item.type) === _forYouScreen.ACCOUNT_CM24_CARD_TYPE.EXPLORE_GAMIFICATION;
      });
    }, [accountBannerList]);
    (0, _exploreScreen.useBottomSheetError)({
      route: route
    });
    var scrollGesture = _reactNativeGestureHandler.Gesture.Native();
    var isShowWhiteBackground = isDarkApp || (!isConnectInternet || profileFetching || profileError || rewardsFetching || rewardsError) && (!rewardsData || !userProfile);
    var renderScrollBuddyComponent = (0, _react.useMemo)(function () {
      var _announcementPayload$;
      var validAnnouncementLength = announcementPayload == null || announcementPayload.filter == null || (_announcementPayload$ = announcementPayload.filter(function (item) {
        var _item$extraJsonData;
        return item == null || (_item$extraJsonData = item.extraJsonData) == null || (_item$extraJsonData = _item$extraJsonData.screens) == null || _item$extraJsonData.some == null ? undefined : _item$extraJsonData.some(function (scr) {
          return (scr == null ? undefined : scr.tagName) === _constants.NotificationL1Page.Explore;
        });
      })) == null ? undefined : _announcementPayload$.length;
      if (!isConnectInternet || isShowCardToast || !isExistScrollBuddy || validAnnouncementLength) {
        return null;
      }
      return (0, _jsxRuntime.jsx)(_scrollBuddy.ScrollBuddy, {
        opacity: scrollBuddyOpacity,
        disableScrollHideComponent: true,
        scrollBuddyImage: scrollBuddyData == null ? undefined : scrollBuddyData.image,
        onPress: scrollBuddyOnPress
      });
    }, [announcementPayload, isShowCardToast, isExistScrollBuddy, scrollBuddyData, scrollBuddyOnPress, isConnectInternet]);
    var grantPermissionContent = (0, _react.useCallback)(function () {
      return (0, _utils.handleCondition)(_reactNative2.Platform.OS === "android", function () {
        var androidLevel = _reactNativeDeviceInfo.default.getSystemVersion();
        return (0, _utils.handleCondition)(Number(androidLevel) < 13, (0, _jsxRuntime.jsx)(_grantPermissionModalOldVersion.GrantPermissionModalOldVersion, {}), (0, _jsxRuntime.jsx)(_grantPermissionModal.GrantPermissionModal, {}));
      }(), (0, _jsxRuntime.jsx)(_grantPermissionModal.GrantPermissionModal, {}));
    }, []);
    var getLinkGame = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* () {
        var _dataGamificationCard;
        var ecid = yield (0, _adobe.getExperienceCloudId)();
        var navValue = (dataGamificationCard == null || (_dataGamificationCard = dataGamificationCard.navigation) == null ? undefined : _dataGamificationCard.value) || _navigationHelper.NavigationValueDeepLink.gameMain;
        var input = {
          isLoggedInAtTriggerTime: isLoggedIn,
          aaTag: "Explore | Second Floor",
          pageSource: _navigationHelper.NavigationPageSource.SecondFloor
        };
        var gamificationInput = {
          aaTag: getGamificationAATag(navValue, input, input == null ? undefined : input.isLoggedInAtTriggerTime),
          ecid: ecid,
          landingPage: getGamificationLandingPage(navValue),
          staff: userProfile != null && userProfile.airportStaff ? 1 : 0,
          utmParameters: getGamificationUtmParameters(navValue, input)
        };
        try {
          var response = yield (0, _pageConfigSaga.getDeepLinkV2)({
            stateCode: _constants.StateCode.GAMIFICATION,
            input: gamificationInput
          }, true);
          setDataWebviewGame(response);
        } catch (error) {
          dispatch(_systemRedux.default.setBottomSheetErrorData({
            visible: true
          }));
          setDataWebviewGame(null);
        } finally {}
      });
      return function getLinkGame() {
        return _ref3.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      if (showWebviewGame) {
        dispatch(_exploreRedux.ExploreActions.Creators.setShowGameExploreSecoundFloor(true));
        getLinkGame();
      } else {
        dispatch(_exploreRedux.ExploreActions.Creators.setShowGameExploreSecoundFloor(false));
        setDataWebviewGame(null);
      }
    }, [showWebviewGame]);
    var renderUINoInternet = function renderUINoInternet() {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.containerError,
        children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.viewContainerNoInternet,
          children: (0, _jsxRuntime.jsx)(_errorCloudV.ErrorCloudComponentV2, {
            customIcon: (0, _jsxRuntime.jsx)(_icons.ErrorCloudV2, {
              width: 120,
              height: 120
            }),
            title: (0, _i18n.translate)("errorOverlay.variant3.title"),
            titleStyle: styles.titleError,
            content: (0, _i18n.translate)("errorOverlay.variant3.message"),
            contentTextStyle: styles.contentError,
            styleIconContainer: styles.styleIconContainer,
            styleTextContainer: styles.styleTextContainer,
            style: styles.errorContainer,
            customButton: (0, _jsxRuntime.jsx)(ButtonRetry, {
              handlePressReload: function handlePressReload() {
                onRefresh();
                handleRefetchQuicklinks();
              }
            })
          })
        })
      });
    };
    var blurRef = (0, _react.useRef)(null);
    var reversedBlurRef = (0, _react.useRef)(null);
    var onProgressChange = function onProgressChange(progress, absoluteProgress) {
      var _blurRef$current, _reversedBlurRef$curr;
      var nextIndex = Math.round(absoluteProgress);
      var scrollToX = width_screen * nextIndex - Math.abs(progress) > 50 ? Math.floor(Math.abs(progress)) : Math.floor(width_screen * nextIndex);
      (_blurRef$current = blurRef.current) == null || _blurRef$current.scrollTo({
        x: scrollToX,
        animated: false
      });
      (_reversedBlurRef$curr = reversedBlurRef.current) == null || _reversedBlurRef$curr.scrollTo({
        x: scrollToX,
        animated: false
      });
    };
    var animatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var exploreSecondFloorTsx1 = function exploreSecondFloorTsx1() {
        return {
          transform: [{
            translateY: translateY.value
          }]
        };
      };
      exploreSecondFloorTsx1.__closure = {
        translateY: translateY
      };
      exploreSecondFloorTsx1.__workletHash = 17214413467328;
      exploreSecondFloorTsx1.__initData = _worklet_17214413467328_init_data;
      return exploreSecondFloorTsx1;
    }());
    var animatedButtonBack = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var exploreSecondFloorTsx2 = function exploreSecondFloorTsx2() {
        var opacity = (0, _reactNativeReanimated.interpolate)(translateY.value, [401, height_screen], [0, 1]);
        return {
          opacity: opacity
        };
      };
      exploreSecondFloorTsx2.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        translateY: translateY,
        height_screen: height_screen
      };
      exploreSecondFloorTsx2.__workletHash = 13378341547429;
      exploreSecondFloorTsx2.__initData = _worklet_13378341547429_init_data;
      return exploreSecondFloorTsx2;
    }());
    var animatedShowTextStatusScroll = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var exploreSecondFloorTsx3 = function exploreSecondFloorTsx3() {
        var height_screen_25_percent = height_screen * 25 / 100;
        var opacity = (0, _reactNativeReanimated.interpolate)(translateY.value, [0, height_screen_25_percent, height_screen_25_percent + 1], [0, 0, 1]);
        return {
          opacity: opacity
        };
      };
      exploreSecondFloorTsx3.__closure = {
        height_screen: height_screen,
        interpolate: _reactNativeReanimated.interpolate,
        translateY: translateY
      };
      exploreSecondFloorTsx3.__workletHash = 2125486700523;
      exploreSecondFloorTsx3.__initData = _worklet_2125486700523_init_data;
      return exploreSecondFloorTsx3;
    }());
    var positionViewCurve = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var exploreSecondFloorTsx4 = function exploreSecondFloorTsx4() {
        var top = (0, _reactNativeReanimated.interpolate)(translateY.value, [-1, 0, translateY.value], [0, 0, translateY.value - 150]);
        return {
          top: top
        };
      };
      exploreSecondFloorTsx4.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        translateY: translateY
      };
      exploreSecondFloorTsx4.__workletHash = 5850223770620;
      exploreSecondFloorTsx4.__initData = _worklet_5850223770620_init_data;
      return exploreSecondFloorTsx4;
    }());
    var opacityViewCurve = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var exploreSecondFloorTsx5 = function exploreSecondFloorTsx5() {
        var opacity = (0, _reactNativeReanimated.interpolate)(translateY.value, [0, height_screen - 1, height_screen], [1, 1, 0]);
        return {
          opacity: opacity
        };
      };
      exploreSecondFloorTsx5.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        translateY: translateY,
        height_screen: height_screen
      };
      exploreSecondFloorTsx5.__workletHash = 2174880071398;
      exploreSecondFloorTsx5.__initData = _worklet_2174880071398_init_data;
      return exploreSecondFloorTsx5;
    }());
    var trackingGameOpen = (0, _react.useCallback)(function () {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppSecondFloor, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppSecondFloor, "Explore | Gamification landing page"));
    }, []);
    var panGesture = _reactNativeGestureHandler.Gesture.Pan().onUpdate(function () {
      var exploreSecondFloorTsx7 = function exploreSecondFloorTsx7(e) {
        if (isScroll && translationY.value === 0) {
          (0, _reactNativeReanimated.runOnJS)(setIsScroll)(false);
        }
        if (translationY.value === 0) {
          translateY.value = e.translationY;
        }
      };
      exploreSecondFloorTsx7.__closure = {
        isScroll: isScroll,
        translationY: translationY,
        runOnJS: _reactNativeReanimated.runOnJS,
        setIsScroll: setIsScroll,
        translateY: translateY
      };
      exploreSecondFloorTsx7.__workletHash = 12418178846156;
      exploreSecondFloorTsx7.__initData = _worklet_12418178846156_init_data;
      return exploreSecondFloorTsx7;
    }()).onEnd(function () {
      var exploreSecondFloorTsx6 = function exploreSecondFloorTsx6(e) {
        (0, _reactNativeReanimated.runOnJS)(setIsScroll)(true);
        if (translationY.value === 0) {
          if (e.translationY < 100) {
            translateY.value = (0, _reactNativeReanimated.withTiming)(0);
          } else if (e.translationY < 400 && isConnectInternet) {
            if (!loadingRefresh) {
              (0, _reactNativeReanimated.runOnJS)(setLoadingRefresh)(true);
              (0, _reactNativeReanimated.runOnJS)(onRefresh)();
              (0, _reactNativeReanimated.runOnJS)(handleRefetchQuicklinks)();
            }
            translateY.value = (0, _reactNativeReanimated.withTiming)(0);
          } else {
            (0, _reactNativeReanimated.runOnJS)(hideBottombar)();
            (0, _reactNativeReanimated.runOnJS)(setShowWebviewGame)(true);
            (0, _reactNativeReanimated.runOnJS)(trackingGameOpen)();
            translateY.value = (0, _reactNativeReanimated.withTiming)(height_screen, {
              duration: 200
            });
          }
        } else {
          translateY.value = (0, _reactNativeReanimated.withTiming)(0);
        }
      };
      exploreSecondFloorTsx6.__closure = {
        runOnJS: _reactNativeReanimated.runOnJS,
        setIsScroll: setIsScroll,
        translationY: translationY,
        translateY: translateY,
        withTiming: _reactNativeReanimated.withTiming,
        isConnectInternet: isConnectInternet,
        loadingRefresh: loadingRefresh,
        setLoadingRefresh: setLoadingRefresh,
        onRefresh: onRefresh,
        handleRefetchQuicklinks: handleRefetchQuicklinks,
        hideBottombar: hideBottombar,
        setShowWebviewGame: setShowWebviewGame,
        trackingGameOpen: trackingGameOpen,
        height_screen: height_screen
      };
      exploreSecondFloorTsx6.__workletHash = 4823164561328;
      exploreSecondFloorTsx6.__initData = _worklet_4823164561328_init_data;
      return exploreSecondFloorTsx6;
    }()).activeOffsetY(10) // Only activate when drag Y > 10px
    .failOffsetX([-10, 10]) // Allow horizontal scroll to pass through
    .simultaneousWithExternalGesture();
    var onGoBack = function onGoBack() {
      setShowWebviewGame(false);
      translateY.value = (0, _reactNativeReanimated.withTiming)(0);
    };
    var goToGame = function goToGame() {
      _reactNative2.InteractionManager.runAfterInteractions(function () {
        (0, _reactNativeReanimated.runOnJS)(setShowWebviewGame)(true);
        (0, _reactNativeReanimated.runOnJS)(hideBottombar)();
        translateY.value = (0, _reactNativeReanimated.withTiming)(height_screen, {
          duration: 200
        });
      });
    };
    (0, _react.useEffect)(function () {
      if (!isFocused) {
        onGoBack();
      }
    }, [isFocused]);
    var renderComponentWebviewGame = function renderComponentWebviewGame() {
      if (showWebviewGame && dataWebviewGame != null && dataWebviewGame.redirectUri) {
        return (0, _jsxRuntime.jsx)(_playpassWebview.PlayPassWebView, {
          route: {
            params: {
              uri: (dataWebviewGame == null ? undefined : dataWebviewGame.redirectUri) || "",
              needBackButton: true,
              needCloseButton: true,
              basicAuthCredential: dataWebviewGame == null ? undefined : dataWebviewGame.basicAuth,
              isGamification: true,
              hideHeader: true
            }
          },
          navigation: navigation,
          customBackAndroidDevice: onGoBack,
          customBackBottom: onGoBack,
          goToGame: goToGame
        });
      } else {
        return (0, _jsxRuntime.jsx)(_baseImage.default, {
          source: dataGamificationCard != null && dataGamificationCard.bgImage ? {
            uri: (0, _mediaHelper.handleImageUrl)(dataGamificationCard == null ? undefined : dataGamificationCard.bgImage)
          } : _backgrounds.SwipeDownBg,
          resizeMode: "cover",
          style: styles.viewImage
        });
      }
    };
    return (0, _jsxRuntime.jsxs)(_reactNativeGestureHandler.GestureHandlerRootView, {
      style: {
        flex: 1
      },
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.viewContainerGame,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.containerContentWebview,
          children: renderComponentWebviewGame()
        }), (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
          style: animatedButtonBack,
          children: (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
            style: styles.buttonBackViewGame,
            onPress: onGoBack,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.txtBackBottom,
              children: "Exit Game"
            }), (0, _jsxRuntime.jsx)(_icons.CloseIconWhite, {
              width: 24,
              height: 24
            })]
          })
        }), !showWebviewGame && (0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
          style: [styles.curveView, positionViewCurve, opacityViewCurve],
          children: [(0, _jsxRuntime.jsx)(_viewCurve.CurvedView, {
            translateY: translateY
          }), (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
            style: [styles.viewStatusScroll, animatedShowTextStatusScroll],
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.txtStatusScroll,
              children: "Keep pulling... magic\u2019s loading"
            })
          })]
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
        style: [styles.containerStyle, animatedStyle],
        children: [isDisplayTickerBand && (0, _jsxRuntime.jsx)(_tickerBand.default, {
          urgent: false,
          title: tickerBand,
          description: tickerBandDescription,
          buttonText: tickerBandButtonText,
          onCTAPress: onPressCTA,
          onClose: function onClose() {
            return onCloseTickerBand();
          },
          tickerStyle: {
            paddingTop: 50
          }
        }), (0, _jsxRuntime.jsx)(_focusStatusBar.FocusAwareStatusBar, {
          translucent: true,
          backgroundColor: "transparent",
          barStyle: "light-content"
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.viewRoot,
          children: (0, _jsxRuntime.jsx)(_reactNativeGestureHandler.GestureDetector, {
            gesture: _reactNativeGestureHandler.Gesture.Simultaneous(panGesture, scrollGesture),
            children: (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
              children: [!isConnectInternet ? renderUINoInternet() : (0, _jsxRuntime.jsx)(_reactNative2.ScrollView, {
                ref: scrollViewRef,
                style: styles.container,
                onScroll: scrollHandler,
                showsVerticalScrollIndicator: false,
                onScrollEndDrag: onUpdateExploreChangiPageNumber,
                contentContainerStyle: styles.contentContainerStyle,
                bounces: false,
                scrollEnabled: isScroll,
                children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                  style: [styles.viewContent, {
                    marginTop: loadingRefresh ? 34 : 14
                  }],
                  children: [(0, _jsxRuntime.jsx)(_components.ViewImageSnapCarousel, {
                    dataMathead: dataMathead,
                    isErrorMathead: isErrorMathead,
                    isLoadingMathead: isLoadingMathead,
                    isFocused: isFocused,
                    onProgressChange: onProgressChange
                  }), (0, _jsxRuntime.jsx)(_components.ViewBackgroundRotate, {
                    ref: reversedBlurRef,
                    images: backgroundEffectImages,
                    isErrorMathead: isErrorMathead || (dataMathead == null ? undefined : dataMathead.length) === 0
                  }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                    style: styles.viewShadow,
                    children: [(0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
                      colors: isShowWhiteBackground ? [_theme.color.palette.almostWhiteGrey, _theme.color.palette.almostWhiteGrey] : memberIconInfo == null ? undefined : memberIconInfo.backgroundMemberInfoExploreV2,
                      locations: [0, 0.6173, 0.9497],
                      style: styles.viewMemberInfo,
                      onLayout: onMemberInfoLayout,
                      children: (0, _jsxRuntime.jsx)(_components.ViewMemberInfo, {
                        loadingRefresh: loadingRefresh,
                        rewardsData: rewardsData,
                        rewardsFetching: rewardsFetching,
                        profileFetching: profileFetching,
                        profileError: profileError,
                        userProfile: userProfile,
                        rewardsError: rewardsError,
                        onShowRewardCard: onShowRewardCard,
                        isLoggedIn: isLoggedIn,
                        onChangiPayPressed: onChangiPayPressed,
                        onReLoadProfileAndReward: onReLoadProfileAndReward,
                        isShowWhiteBackground: isShowWhiteBackground
                      })
                    }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                      style: styles.viewMargin,
                      children: [(0, _jsxRuntime.jsx)(_components.ViewFlightUpcoming, {}), (0, _jsxRuntime.jsx)(_ViewQuickLinks.default, {
                        data: shortcutLinksExploreV2 == null ? undefined : shortcutLinksExploreV2.shortcutLinks,
                        containerStyle: {
                          marginHorizontal: 16
                        },
                        loading: loadShortcutLinksExploreV2 && !(shortcutLinksExploreV2 != null && shortcutLinksExploreV2.shortcutLinks),
                        errorShortcutLinks: errorShortcutLinksExploreV2,
                        anchorCategoryTitle: _aemQuicklinkCategories.AllQuickLinksAnchorTitle.ForYou,
                        errorContainerStyle: styles.errorQuickLinkContainer,
                        isDarkMode: isDarkApp,
                        trackingEvar: _adobe.AdobeTagName.HomeQuicklinksRevamped
                      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
                        onLayout: onExploreContainerLayout,
                        children: !loadingExploreCategories && isExploreCategoriesError ? (0, _jsxRuntime.jsx)(_errorCloudV.ErrorCloudComponentV2, {
                          customIcon: (0, _jsxRuntime.jsx)(_icons.ErrorCloudV2, {
                            width: 120,
                            height: 120
                          }),
                          title: titleErrorContentExplore,
                          titleStyle: styles.titleError,
                          content: messageErrorContentExplore,
                          contentTextStyle: styles.contentError,
                          styleIconContainer: styles.styleIconContainer,
                          styleTextContainer: styles.styleTextContainer,
                          style: styles.errorContainer,
                          customButton: (0, _jsxRuntime.jsx)(ButtonRetry, {
                            handlePressReload: onRefreshListExplore
                          })
                        }) : (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
                          children: [(0, _jsxRuntime.jsx)(_tabBarContainer.TabBarContainer, {
                            scrollOffset: currentScrollOffset,
                            testID: `${SCREEN_NAME}__ShortCutLinksExplore`,
                            accessibilityLabel: `${SCREEN_NAME}__ShortCutLinksExplore`,
                            setHeightExploreChangiTab: onExploreChangiTabLayout,
                            showFilterModal: onShowExploreChangiFilterModal
                          }), (0, _jsxRuntime.jsx)(_exploreContainer.ExploreContainer, {
                            scrollViewRef: scrollViewRef,
                            exploreChangiTabHeight: exploreChangiTabHeight,
                            showFilterModal: onShowExploreChangiFilterModal,
                            testID: `${SCREEN_NAME}__ShortCutLinksExplore`,
                            accessibilityLabel: `${SCREEN_NAME}__ShortCutLinksExplore`,
                            isErrorV2: isExploreCategoriesError
                          })]
                        })
                      })]
                    }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
                      style: styles.viewBottom
                    })]
                  })]
                })
              }), (0, _jsxRuntime.jsx)(_viewBlurSecondFloor.ViewBlurBackgroundSecondFloor, {
                ref: blurRef,
                images: backgroundEffectImages,
                isConnectInternet: isConnectInternet,
                isDarkApp: isDarkApp,
                translateY: translateY
              }), renderScrollBuddyComponent, isLoggedIn && (0, _jsxRuntime.jsx)(_cartToast.default, {
                toastRef: toastRef,
                testID: `${SCREEN_NAME}__CartToast`,
                accessibilityLabel: `${SCREEN_NAME}__CartToast`,
                setIsShowCardToast: setIsShowCardToast
              }), !showWebviewGame && (0, _jsxRuntime.jsx)(_viewHeader.ViewHeaderSecondFloor, {
                translationY: translationY,
                colors: isShowWhiteBackground ? _theme.color.palette.headerMemberExploreV2 : memberIconInfo == null ? undefined : memberIconInfo.colorHeaderAnimatedExploreV2,
                positionScroll: (dataMathead == null ? undefined : dataMathead.length) > 0 ? width_screen * ratio + 105 : 105,
                onOpenRewardCard: onShowRewardCard,
                isLoggedIn: isLoggedIn,
                loadingRefresh: loadingRefresh,
                translateY: translateY
              }), (0, _jsxRuntime.jsx)(_exploreChangiModalFilter.default, {
                filterDate: exploreChangiFilterDate,
                setFilterDate: setExploreChangiFilterDate,
                checkedLocationState: checkedFilterLocationState,
                setCheckedLocationState: setCheckedFilterLocationState,
                initialFilterLocation: initialFilterLocation,
                testID: `${SCREEN_NAME}__ModalFilter`,
                accessibilityLabel: `${SCREEN_NAME}__ModalFilter`
              }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
                visible: loadingGetConfig
              }), showGrantPermissionFlow && grantPermissionContent(), (0, _jsxRuntime.jsx)(_monarchOverlay.default, {}), (0, _jsxRuntime.jsx)(_bottomSheetError.BottomSheetError, {
                icon: (0, _jsxRuntime.jsx)(_icons2.InfoRed, {}),
                visible: Boolean(playPassUrlPayload == null ? undefined : playPassUrlPayload.error) && isFocused,
                title: (0, _i18n.translate)("popupError.somethingWrongOneline"),
                errorMessage: (0, _i18n.translate)("popupError.networkErrorMessage"),
                onClose: onResetPlayPassUrl,
                onButtonPressed: onResetPlayPassUrl,
                buttonText: (0, _i18n.translate)("subscription.close"),
                testID: `${SCREEN_NAME}__BottomSheetErrorSomethingWrong__Playpass`,
                accessibilityLabel: `${SCREEN_NAME}__BottomSheetErrorSomethingWrong__Playpass`
              })]
            })
          })
        })]
      })]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    viewContainerGame: {
      width: width_screen,
      height: height_screen,
      backgroundColor: _theme.color.palette.whiteGrey,
      position: "absolute",
      top: 0,
      left: 0,
      bottom: 0,
      right: 0
    },
    buttonBackViewGame: {
      position: "absolute",
      top: 56,
      right: 16,
      paddingLeft: 12,
      paddingRight: 6,
      height: 32,
      borderRadius: 99,
      backgroundColor: "#12121233",
      alignItems: "center",
      flexDirection: "row",
      zIndex: 20
    },
    containerStyle: {
      flex: 1
    },
    viewRoot: {
      flex: 1,
      backgroundColor: _theme.color.palette.almostWhiteGrey
    },
    container: {
      flex: 1,
      paddingTop: 100
    },
    contentContainerStyle: {
      paddingBottom: 200
    },
    containerError: {
      width: '100%',
      height: height_screen - 100,
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      marginTop: 108
    },
    viewContainerNoInternet: {
      flex: 1,
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      paddingTop: 80
    },
    viewContent: {
      flex: 1,
      marginTop: 14,
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      minHeight: _reactNative2.Dimensions.get("window").height - 100
    },
    viewMemberInfo: {
      width: "100%",
      height: 172,
      borderTopLeftRadius: 18,
      borderTopRightRadius: 18
    },
    viewMargin: {
      flex: 1,
      marginTop: -90
    },
    titleError: {
      fontFamily: _theme.typography.black,
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 18,
      fontWeight: _reactNative2.Platform.select({
        ios: "900",
        android: "normal"
      }),
      lineHeight: 22,
      marginBottom: 8,
      textAlign: "center",
      marginTop: 0
    },
    contentError: {
      fontFamily: _theme.typography.regular,
      color: _theme.color.palette.darkestGrey,
      fontSize: 14,
      fontWeight: _reactNative2.Platform.select({
        ios: "400",
        android: "normal"
      }),
      lineHeight: 18,
      textAlign: "center"
    },
    styleIconContainer: {
      width: 120,
      height: 120
    },
    styleTextContainer: {
      rowGap: 0
    },
    errorContainer: {
      justifyContent: 'flex-start',
      minHeight: 0,
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      marginTop: 32,
      rowGap: 16
    },
    reloadButtonStyle: {
      width: width_screen - 48,
      borderRadius: 60,
      marginTop: 0
    },
    viewShadow: {
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      shadowColor: '#000000',
      shadowOffset: {
        width: 0,
        height: 0
      },
      shadowOpacity: 0.5,
      shadowRadius: 8,
      elevation: 8 // Android
    },
    viewBottom: {
      width: '100%',
      height: 100,
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      marginBottom: _reactNative2.Platform.OS === 'ios' ? -100 : -99
    },
    errorQuickLinkContainer: {
      height: 76,
      marginTop: 10,
      width: width_screen - 32
    },
    viewStatusScroll: {
      width: width_screen,
      justifyContent: 'center',
      alignItems: 'center',
      top: 50
    },
    txtStatusScroll: {
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.almostWhiteGrey80,
      fontSize: 11,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 14,
      textAlign: 'center'
    },
    curveView: {
      position: 'absolute',
      top: -30
    },
    txtBackBottom: {
      fontSize: 12,
      lineHeight: 16,
      color: _theme.color.palette.whiteGrey,
      fontWeight: _reactNative2.Platform.select({
        ios: "900",
        android: "normal"
      }),
      fontFamily: _theme.typography.black
    },
    containerContentWebview: {
      position: 'absolute',
      width: width_screen,
      height: height_screen
    },
    viewImage: {
      position: 'absolute',
      width: width_screen,
      height: height_screen
    }
  });
  var _default = exports.default = ExploreScreenV2SecondFloor;
