  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _forYouCm = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _gamificationBanner = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _miffyGamificationBanner = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _text2 = _$$_REQUIRE(_dependencyMap[7]);
  var _theme = _$$_REQUIRE(_dependencyMap[8]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[9]);
  var _forYouScreen = _$$_REQUIRE(_dependencyMap[10]);
  var _account = _$$_REQUIRE(_dependencyMap[11]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[12]);
  var _aemGroupTwo = _$$_REQUIRE(_dependencyMap[13]);
  var _constants = _$$_REQUIRE(_dependencyMap[14]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[16]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var styles = _reactNative.StyleSheet.create({
    spendNWinContainerStyle: {
      gap: 16,
      marginTop: 50
    },
    spendNWinCTitleTextStyle: Object.assign({}, _text2.newPresets.caption2Bold, {
      color: _theme.color.palette.darkestGrey
    }),
    titleStyle: {
      borderRadius: 16,
      height: 16,
      width: 70
    }
  });
  var SpendAndWinSection = function SpendAndWinSection(_ref) {
    var miffyProps = _ref.miffyProps;
    var _useContext = (0, _react.useContext)(_account.AccountContext),
      accountCM24FeatureFlag = _useContext.accountCM24FeatureFlag,
      masterGameAccountFlag = _useContext.masterGameAccountFlag;
    var isCM24FeatureEnabled = (0, _remoteConfig.isFlagOnCondition)(accountCM24FeatureFlag);
    var accountBannerList = (0, _reactRedux.useSelector)(_aemGroupTwo.AemGroupTwoSelectors.accountBannerList);
    var isLoading = (0, _reactRedux.useSelector)(_aemGroupTwo.AemGroupTwoSelectors.aemGroupTwoLoading);
    var isMasterGameAccountFlagEnable = (0, _remoteConfig.getFeatureFlagInit)(_remoteConfig.REMOTE_CONFIG_FLAGS.MASTERGAME_ACCOUNT, masterGameAccountFlag);
    var hasEntryBannerCard = (0, _react.useMemo)(function () {
      return accountBannerList == null || accountBannerList.some == null ? undefined : accountBannerList.some(function (item) {
        return (item == null ? undefined : item.type) === _forYouScreen.ACCOUNT_CM24_CARD_TYPE.ENTRY_BANNER;
      });
    }, [accountBannerList]);
    var hasGamificationCard = (0, _react.useMemo)(function () {
      return accountBannerList == null || accountBannerList.some == null ? undefined : accountBannerList.some(function (item) {
        return (item == null ? undefined : item.type) === _forYouScreen.ACCOUNT_CM24_CARD_TYPE.ACCOUNT_GAMIFICATION;
      });
    }, [accountBannerList]);
    var hasCM24EntryBanner = isCM24FeatureEnabled && hasEntryBannerCard;
    var hasMasterGamification = isMasterGameAccountFlagEnable && hasGamificationCard;
    var shouldShowTitle = hasCM24EntryBanner || hasMasterGamification;
    var renderTitle = function renderTitle() {
      if (isLoading) {
        return (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _theme.color.shimmerPlacholderColor,
          shimmerStyle: styles.titleStyle
        });
      }
      if (shouldShowTitle) {
        return (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.spendNWinCTitleTextStyle,
          tx: "forYouScreen.forYouCM24.title"
        });
      }
      return null;
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.spendNWinContainerStyle,
      children: [renderTitle(), (0, _jsxRuntime.jsx)(_gamificationBanner.default, {}), (0, _jsxRuntime.jsx)(_miffyGamificationBanner.default, Object.assign({}, miffyProps)), (0, _jsxRuntime.jsx)(_forYouCm.default, {})]
    });
  };
  var _default = exports.default = (0, _react.memo)(SpendAndWinSection);
