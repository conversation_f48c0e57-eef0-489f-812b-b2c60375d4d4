  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[1]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  var MiffyGamificationBtn = function MiffyGamificationBtn(props) {
    var accessibilityLabel = props.accessibilityLabel,
      btnImageSource = props.btnImageSource,
      onPress = props.onPress,
      style = props.style,
      testID = props.testID;
    return (0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
      accessibilityLabel: accessibilityLabel,
      androidRippleColor: "transparent",
      onPress: onPress,
      style: [styles.contentContainerStyle, style ? {
        height: style == null ? undefined : style.height,
        width: style == null ? undefined : style.width
      } : undefined],
      testID: testID,
      children: (0, _jsxRuntime.jsx)(_baseImage.default, {
        resizeMode: "contain",
        style: style,
        source: btnImageSource
      })
    });
  };
  var styles = _reactNative.StyleSheet.create({
    contentContainerStyle: {
      alignItems: "center",
      justifyContent: "center"
    }
  });
  var _default = exports.default = MiffyGamificationBtn;
