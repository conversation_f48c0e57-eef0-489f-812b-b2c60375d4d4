  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var _react = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _miffyGamificationBanner = _$$_REQUIRE(_dependencyMap[5]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  var MiffySticker = function MiffySticker(props) {
    var accessibilityLabel = props.accessibilityLabel,
      _props$iconStyle = props.iconStyle,
      iconStyle = _props$iconStyle === undefined ? {} : _props$iconStyle,
      PlaceholderIcon = props.PlaceholderIcon,
      _props$quantity = props.quantity,
      quantity = _props$quantity === undefined ? 0 : _props$quantity,
      quantityBadgeLabelStyle = props.quantityBadgeLabelStyle,
      customQuantityBadgeStyle = props.quantityBadgeStyle,
      StickerIcon = props.StickerIcon,
      style = props.style,
      testID = props.testID;
    var iconPositionStyle = (0, _react.useMemo)(function () {
      var _iconStyle$height = iconStyle.height,
        height = _iconStyle$height === undefined ? 0 : _iconStyle$height,
        _iconStyle$width = iconStyle.width,
        width = _iconStyle$width === undefined ? 0 : _iconStyle$width;
      return {
        left: -(Number(width) / 2),
        position: "absolute",
        top: -(Number(height) / 2)
      };
    }, [iconStyle]);
    var quantityLabel = (0, _react.useMemo)(function () {
      if (!quantity && quantity !== 0) return "";
      return `${quantity}`;
    }, [quantity]);
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      accessibilityLabel: accessibilityLabel,
      style: style,
      testID: testID,
      children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
        children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
          source: quantity > 0 ? StickerIcon : PlaceholderIcon,
          style: [iconPositionStyle, iconStyle]
        }), quantity >= 1 && (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: [_miffyGamificationBanner.styles.quantityBadgeStyle, customQuantityBadgeStyle],
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: quantityBadgeLabelStyle,
            text: quantityLabel
          })
        })]
      })
    });
  };
  var _default = exports.default = MiffySticker;
