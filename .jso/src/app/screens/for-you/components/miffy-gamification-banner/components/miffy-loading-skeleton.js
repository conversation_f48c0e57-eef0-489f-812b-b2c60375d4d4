  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _baseImageBackground = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _miffyGamificationBanner = _$$_REQUIRE(_dependencyMap[2]);
  var _icons = _$$_REQUIRE(_dependencyMap[3]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _constants = _$$_REQUIRE(_dependencyMap[5]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  var MiffyLoadingSkeleton = function MiffyLoadingSkeleton() {
    return (0, _jsxRuntime.jsxs)(_baseImageBackground.default, {
      imageStyle: _miffyGamificationBanner.styles.loadingSkeletonBgImageStyle,
      source: _icons.MiffyLoadingBg,
      style: _miffyGamificationBanner.styles.loadingSkeletonContainerStyle,
      children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
        shimmerStyle: _miffyGamificationBanner.styles.longLoadingTile
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
        shimmerStyle: _miffyGamificationBanner.styles.shortLoadingTile
      })]
    });
  };
  var _default = exports.default = MiffyLoadingSkeleton;
