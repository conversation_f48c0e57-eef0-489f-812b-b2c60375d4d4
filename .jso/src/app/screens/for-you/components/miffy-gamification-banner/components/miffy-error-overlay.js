  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _miffyGamificationBanner = _$$_REQUIRE(_dependencyMap[2]);
  var _iconCloud = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _errorOverlay = _$$_REQUIRE(_dependencyMap[8]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[10]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[11]);
  var _responsive = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  var MiffyErrorOverlay = function MiffyErrorOverlay(props) {
    var onReload = props.onReload;
    var errorData = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("MIFFY_ERROR_OVERLAY"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var errorSection = errorData != null && errorData.length ? errorData.find(function (el) {
      return el.code === _errorOverlay.ERROR_HANDLING_CODE.ERROR_SECTION_LEVEL;
    }) : null;
    var handlePressReload = function handlePressReload() {
      var _errorSection$navigat;
      if (errorSection != null && (_errorSection$navigat = errorSection.navigationFirst) != null && _errorSection$navigat.value) {
        var _errorSection$navigat2, _errorSection$navigat3;
        handleNavigation(errorSection == null || (_errorSection$navigat2 = errorSection.navigationFirst) == null ? undefined : _errorSection$navigat2.type, errorSection == null || (_errorSection$navigat3 = errorSection.navigationFirst) == null ? undefined : _errorSection$navigat3.value, errorSection == null ? undefined : errorSection.redirectFirst);
        return;
      }
      onReload == null || onReload();
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      accessibilityLabel: `${_miffyGamificationBanner.COMPONENT_NAME}__ErrorOverlay`,
      style: styles.containerStyle,
      testID: `${_miffyGamificationBanner.COMPONENT_NAME}__ErrorOverlay`,
      children: [(0, _jsxRuntime.jsx)(_iconCloud.default, {}), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.contentContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.titleTextStyle,
          text: errorSection == null ? undefined : errorSection.header
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.messageTextStyle,
          text: errorSection == null ? undefined : errorSection.subHeader
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        children: (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
          style: styles.reloadBtnContainerStyle,
          start: {
            x: 0,
            y: 1
          },
          end: {
            x: 1,
            y: 0
          },
          colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
          children: (0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
            accessibilityLabel: `${_miffyGamificationBanner.COMPONENT_NAME}__ErrorOverlay__ReloadBtn`,
            androidRippleColor: "transparent",
            onPress: handlePressReload,
            testID: `${_miffyGamificationBanner.COMPONENT_NAME}__ErrorOverlay__ReloadBtn`,
            style: styles.reloadBtnContainerStyle,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.reloadBtnLabelStyle,
              text: errorSection == null ? undefined : errorSection.buttonLabel
            })
          })
        })
      })]
    });
  };
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    screenWidth = _Dimensions$get.width;
  var styles = _reactNative.StyleSheet.create({
    containerStyle: {
      alignItems: "center",
      paddingVertical: 48
    },
    contentContainerStyle: {
      gap: 16,
      marginBottom: 24,
      marginTop: 72
    },
    titleTextStyle: Object.assign({}, _text.presets.h2, {
      textAlign: "center"
    }),
    messageTextStyle: Object.assign({}, _text.presets.caption1Regular, {
      color: _theme.color.palette.darkestGrey,
      textAlign: "center"
    }),
    reloadBtnContainerStyle: {
      alignItems: "center",
      borderRadius: 60,
      height: 44,
      justifyContent: "center",
      // paddingHorizontal: 24,
      // paddingVertical: 16,
      width: screenWidth - 32
    },
    reloadBtnLabelStyle: Object.assign({}, _text.newPresets.bodyTextBold, {
      color: _theme.color.palette.almostWhiteGrey,
      lineHeight: _responsive.default.getFontSize(24)
    })
  });
  var _default = exports.default = MiffyErrorOverlay;
