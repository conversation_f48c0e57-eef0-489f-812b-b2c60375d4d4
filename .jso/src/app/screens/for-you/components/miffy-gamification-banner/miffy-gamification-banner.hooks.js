  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useMiffyGamificationEffects = exports.useMiffyGamification = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _$$_REQUIRE(_dependencyMap[3]);
  var _awsAmplify = _$$_REQUIRE(_dependencyMap[4]);
  var _request = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _envParams = _$$_REQUIRE(_dependencyMap[6]);
  var _queries = _$$_REQUIRE(_dependencyMap[7]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[9]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _profileRedux = _$$_REQUIRE(_dependencyMap[11]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[12]);
  var _account = _$$_REQUIRE(_dependencyMap[13]);
  var useMiffyGamification = exports.useMiffyGamification = function useMiffyGamification() {
    var _useContext;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      loading = _useState2[0],
      setLoading = _useState2[1];
    var _useState3 = (0, _react.useState)(),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      data = _useState4[0],
      setData = _useState4[1];
    var _useState5 = (0, _react.useState)(),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      error = _useState6[0],
      setError = _useState6[1];
    var _useState7 = (0, _react.useState)(false),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      noInternet = _useState8[0],
      setNoInternet = _useState8[1];
    var isMiffyAccount = (0, _remoteConfig.isFlagOnCondition)((_useContext = (0, _react.useContext)(_account.AccountContext)) == null ? undefined : _useContext.miffyGameFeatureFlag);
    var fetchData = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      if (!isMiffyAccount) return;
      setLoading(true);
      var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
        isConnected = _yield$NetInfo$fetch.isConnected;
      setNoInternet(!isConnected);
      if (!isConnected) {
        setLoading(false);
        return;
      }
      try {
        var _env, _env2, _response$data;
        var response = yield (0, _request.default)({
          url: (_env = (0, _envParams.env)()) == null ? undefined : _env.APPSYNC_GRAPHQL_URL,
          method: "post",
          data: (0, _awsAmplify.graphqlOperation)(_queries.getGamificationPlayerInfoQuery),
          parameters: {},
          headers: {
            "x-api-key": (_env2 = (0, _envParams.env)()) == null ? undefined : _env2.APPSYNC_GRAPHQL_API_KEY
          }
        });
        var responseData = (_response$data = response.data) == null || (_response$data = _response$data.data) == null ? undefined : _response$data.getGamificationPlayerInfo;
        if (response.success && responseData) {
          setData(responseData);
          setError(undefined);
          setLoading(false);
        } else {
          throw {
            error: "Failed to get data"
          };
        }
      } catch (error) {
        setLoading(false);
        setError(error);
        setData(undefined);
      }
    }), [isMiffyAccount]);
    return {
      data: data,
      error: error,
      fetchData: fetchData,
      isMiffyAccount: isMiffyAccount,
      loading: loading,
      noInternet: noInternet,
      setNoInternet: setNoInternet
    };
  };
  var useMiffyGamificationEffects = exports.useMiffyGamificationEffects = function useMiffyGamificationEffects(props) {
    var fetchData = props.fetchData,
      noInternet = props.noInternet,
      setNoInternet = props.setNoInternet;
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var profileFetching = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profileFetching);
    var checkInternet = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch2.isConnected;
        setNoInternet(!isConnected);
      });
      return function checkInternet() {
        return _ref2.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      checkInternet();
    }, [isLoggedIn, profileFetching]);
    (0, _react.useEffect)(function () {
      if (isLoggedIn && !noInternet) {
        fetchData();
      }
    }, [isLoggedIn, noInternet]);
  };
