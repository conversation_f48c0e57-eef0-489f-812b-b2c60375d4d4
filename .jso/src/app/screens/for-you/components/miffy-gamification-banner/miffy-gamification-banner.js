  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _baseImageBackground = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _miffyGamificationBanner = _$$_REQUIRE(_dependencyMap[3]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[4]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[5]);
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[7]);
  var _miffyBtn = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _miffySticker = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _miffyLoadingSkeleton = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _miffyErrorOverlay = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _miffyGamificationBanner2 = _$$_REQUIRE(_dependencyMap[12]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[13]);
  var _navigationType = _$$_REQUIRE(_dependencyMap[14]);
  var _authentication = _$$_REQUIRE(_dependencyMap[15]);
  var _miffyGamificationBanner3 = _$$_REQUIRE(_dependencyMap[16]);
  var _adobe = _$$_REQUIRE(_dependencyMap[17]);
  var _forYouRedux = _$$_REQUIRE(_dependencyMap[18]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[19]);
  var MiffyGamificationBanner = function MiffyGamificationBanner(props) {
    var _data$stickerInventor;
    var data = props.data,
      error = props.error,
      fetchData = props.fetchData,
      isMiffyAccount = props.isMiffyAccount,
      loading = props.loading,
      noInternet = props.noInternet,
      setNoInternet = props.setNoInternet;
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)(),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var rewardsError = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.rewardsError);
    var hasMoreThanOneStickerAType = data == null || (_data$stickerInventor = data.stickerInventory) == null || _data$stickerInventor.some == null ? undefined : _data$stickerInventor.some(function (sticker) {
      return (sticker == null ? undefined : sticker.quantity) > 1;
    });
    var onPressEnterQuest = function onPressEnterQuest() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppAccountGamification, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppAccountGamification, _adobe.AdobeValueByTagName.CAppAccountMiffyGameEnterQuest));
      handleNavigation(_navigationType.NavigationTypeEnum.deepLink, _navigationHelper.NavigationValueDeepLink.gameMain, {
        aaTag: _navigationHelper.NavigationAATag.miffyGameMainEnterQuest,
        isLoggedInAtTriggerTime: (0, _authentication.checkLoginState)(),
        pageSource: _navigationHelper.NavigationPageSource.accountLanding
      });
    };
    var onPressGiftAFriend = function onPressGiftAFriend() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppAccountGamification, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppAccountGamification, _adobe.AdobeValueByTagName.CAppAccountMiffyGameGiftFriend));
      handleNavigation(_navigationType.NavigationTypeEnum.deepLink, _navigationHelper.NavigationValueDeepLink.gameShare, {
        aaTag: _navigationHelper.NavigationAATag.miffyGameMainGiftFriend,
        isLoggedInAtTriggerTime: (0, _authentication.checkLoginState)(),
        pageSource: _navigationHelper.NavigationPageSource.accountLanding
      });
    };
    var onPressLoginToJoin = function onPressLoginToJoin() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppAccountGamification, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppAccountGamification, _adobe.AdobeValueByTagName.CAppAccountMiffyGameLoginToJoinQuest));
      handleNavigation(_navigationType.NavigationTypeEnum.deepLink, _navigationHelper.NavigationValueDeepLink.gameMain, {
        aaTag: _navigationHelper.NavigationAATag.miffyGameMainLoginToJoin,
        isLoggedInAtTriggerTime: (0, _authentication.checkLoginState)(),
        pageSource: _navigationHelper.NavigationPageSource.accountLanding
      });
    };
    (0, _miffyGamificationBanner3.useMiffyGamificationEffects)({
      fetchData: fetchData,
      noInternet: noInternet,
      setNoInternet: setNoInternet
    });
    if (!isMiffyAccount || rewardsError) return null;
    if (loading) {
      return (0, _jsxRuntime.jsx)(_miffyLoadingSkeleton.default, {});
    }
    if (error) {
      return (0, _jsxRuntime.jsx)(_miffyErrorOverlay.default, {
        onReload: fetchData
      });
    }
    return (0, _jsxRuntime.jsx)(_baseImageBackground.default, {
      accessibilityLabel: `${_miffyGamificationBanner2.COMPONENT_NAME}__Wrapper`,
      imageStyle: _miffyGamificationBanner.styles.bgImageStyle,
      source: !isLoggedIn ? _icons.MiffyBgNonlogin : _icons.MiffyBg,
      style: _miffyGamificationBanner.styles.containerStyle,
      testID: `${_miffyGamificationBanner2.COMPONENT_NAME}__Wrapper`,
      children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _miffyGamificationBanner.styles.contentContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _miffyGamificationBanner.styles.friendCollectionContainerStyle,
          children: isLoggedIn && Object.keys(_miffyGamificationBanner.MIFFY_STICKER_STYLE_CONFIGS).map(function (key) {
            var _data$stickerInventor2;
            var configItem = _miffyGamificationBanner.MIFFY_STICKER_STYLE_CONFIGS[key];
            var quantity = Number(data == null || (_data$stickerInventor2 = data.stickerInventory) == null || _data$stickerInventor2.find == null || (_data$stickerInventor2 = _data$stickerInventor2.find(function (item) {
              return (item == null ? undefined : item.name) === key;
            })) == null ? undefined : _data$stickerInventor2.quantity);
            return (0, _jsxRuntime.jsx)(_miffySticker.default, {
              accessibilityLabel: `${_miffyGamificationBanner2.COMPONENT_NAME}__Sticker__${key}`,
              iconStyle: configItem.iconStyle,
              PlaceholderIcon: configItem.placeholderImg,
              quantity: quantity,
              quantityBadgeLabelStyle: _miffyGamificationBanner.styles.quantityBadgeLabelStyle,
              quantityBadgeStyle: configItem.quantityBadgeStyle,
              StickerIcon: configItem.iconImg,
              style: configItem.container,
              testID: `${_miffyGamificationBanner2.COMPONENT_NAME}__Sticker__${key}`
            }, `${_miffyGamificationBanner2.COMPONENT_NAME}__Sticker__${key}`);
          })
        }), (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: {
            flex: 1
          }
        }), (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _miffyGamificationBanner.styles.btnGroupContainerStyle,
          children: !isLoggedIn ? (0, _jsxRuntime.jsx)(_miffyBtn.default, {
            accessibilityLabel: `${_miffyGamificationBanner2.COMPONENT_NAME}__Btn__LoginToJoinQuest`,
            btnImageSource: _icons.MGBtnLoginToEnterQuest,
            onPress: onPressLoginToJoin,
            style: _miffyGamificationBanner.styles.loginToJoinQuestBtnContainerStyle,
            testID: `${_miffyGamificationBanner2.COMPONENT_NAME}__Btn__LoginToJoinQuest`
          }) : (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
            children: [(0, _jsxRuntime.jsx)(_miffyBtn.default, {
              accessibilityLabel: `${_miffyGamificationBanner2.COMPONENT_NAME}__Btn__EnterQuest`,
              btnImageSource: _icons.MGBtnEnterQuest,
              onPress: onPressEnterQuest,
              style: _miffyGamificationBanner.styles.enterQuestBtnContainerStyle,
              testID: `${_miffyGamificationBanner2.COMPONENT_NAME}__Btn__EnterQuest`
            }), hasMoreThanOneStickerAType && (0, _jsxRuntime.jsx)(_miffyBtn.default, {
              accessibilityLabel: `${_miffyGamificationBanner2.COMPONENT_NAME}__Btn__GiftAFriend`,
              btnImageSource: _icons.MGBtnGiftAFriend,
              onPress: onPressGiftAFriend,
              style: _miffyGamificationBanner.styles.giftAFriendBtnContainerStyle,
              testID: `${_miffyGamificationBanner2.COMPONENT_NAME}__Btn__GiftAFriend`
            })]
          })
        })]
      })
    });
  };
  var _default = exports.default = MiffyGamificationBanner;
