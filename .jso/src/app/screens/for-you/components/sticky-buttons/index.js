  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.STICKY_BUTTONS_CONTAINER_HEIGHT = exports.ForYouStickyButtons = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _native = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _notificationRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _adobe = _$$_REQUIRE(_dependencyMap[7]);
  var _theme = _$$_REQUIRE(_dependencyMap[8]);
  var _utils = _$$_REQUIRE(_dependencyMap[9]);
  var _constants = _$$_REQUIRE(_dependencyMap[10]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[11]);
  var _icons = _$$_REQUIRE(_dependencyMap[12]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[13]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[14]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[15]);
  var STICKY_BUTTONS_CONTAINER_HEIGHT = exports.STICKY_BUTTONS_CONTAINER_HEIGHT = 100;
  var ForYouStickyButtons = exports.ForYouStickyButtons = function ForYouStickyButtons(props) {
    var navigation = (0, _native.useNavigation)();
    var notificationsCountPayload = (0, _reactRedux.useSelector)(_notificationRedux.NotificationSelectors.notificationsCountPayload);
    var eventAndPromotionNotificationCount = (0, _reactRedux.useSelector)(_notificationRedux.NotificationSelectors.eventAndPromotionNotificationCount);
    var allL1AdvisoriesPayload = (0, _reactRedux.useSelector)(_notificationRedux.NotificationSelectors.allL1AdvisoriesPayload);
    var allAnnouncementsPayload = (0, _reactRedux.useSelector)(_notificationRedux.NotificationSelectors.allAnnouncementsPayload);
    var unReadAnnouncementCount = (0, _screenHelper.countUnreadAnnouncements)([].concat((0, _toConsumableArray2.default)(allL1AdvisoriesPayload || []), (0, _toConsumableArray2.default)(allAnnouncementsPayload || [])));
    var countNotification = notificationsCountPayload + eventAndPromotionNotificationCount + unReadAnnouncementCount;
    var handleSettingPress = function handleSettingPress() {
      var trackingDataToBeSent = `${_adobe.AdobeValueByTagName.CAppAccountTopNavigation} | ${_adobe.AdobeValueByTagName.CAppAccountSettings}`;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppAccount, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppAccount, trackingDataToBeSent));
      navigation.navigate(_constants.NavigationConstants.settingScreen);
    };
    var handleNotificationPress = function handleNotificationPress() {
      var trackingDataToBeSent = `${_adobe.AdobeValueByTagName.CAppAccountTopNavigation} | ${_adobe.AdobeValueByTagName.CAppAccountNotifications}`;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppAccount, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppAccount, trackingDataToBeSent));
      navigation.navigate(_constants.NavigationConstants.notificationsScreen);
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        style: styles.settingButton,
        onPress: handleSettingPress,
        children: (0, _jsxRuntime.jsx)(_icons.AccountSetting, {
          width: 24,
          height: 24
        })
      }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: styles.notifyButton,
        onPress: handleNotificationPress,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.bellContainer,
          children: (0, _jsxRuntime.jsx)(_icons.BellOutline, {
            width: 24,
            height: 24,
            color: _theme.color.palette.whiteGrey
          })
        }), (0, _utils.handleCondition)(countNotification > 0, (0, _utils.handleCondition)(countNotification < 100, (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.badgeContainer,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "XSmallBold",
            style: styles.badgeText,
            text: countNotification
          })
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.badgeContainer,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "XSmallBold",
            style: styles.badgeText,
            tx: "notificationsScreen.greaterThan99"
          })
        })), (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {}))]
      })]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    container: {
      flexDirection: "row",
      alignItems: "center",
      gap: 16,
      position: "absolute",
      top: 56,
      right: 12,
      zIndex: 999
    },
    settingButton: {
      width: 32,
      height: 32,
      borderRadius: 999,
      backgroundColor: "#12121233",
      alignItems: "center",
      justifyContent: "center"
    },
    notifyButton: {},
    bellContainer: {
      width: 32,
      height: 32,
      borderRadius: 999,
      backgroundColor: "#12121233",
      alignItems: "center",
      justifyContent: "center"
    },
    badgeContainer: {
      position: "absolute",
      right: 0,
      top: -1,
      paddingHorizontal: 4,
      paddingVertical: 1.5,
      height: 17,
      borderRadius: 999,
      backgroundColor: _theme.color.palette.lightRed,
      alignItems: "center",
      justifyContent: "center"
    },
    badgeText: {
      color: _theme.color.palette.almostWhiteGrey
    }
  });
