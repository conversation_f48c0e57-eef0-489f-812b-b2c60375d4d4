  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _react = _$$_REQUIRE(_dependencyMap[5]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[6]);
  var _authentication = _$$_REQUIRE(_dependencyMap[7]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[9]);
  var _aemGroupTwo = _$$_REQUIRE(_dependencyMap[10]);
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[11]);
  var _forYouScreen = _$$_REQUIRE(_dependencyMap[12]);
  var _adobe = _$$_REQUIRE(_dependencyMap[13]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _constants = _$$_REQUIRE(_dependencyMap[15]);
  var _theme = _$$_REQUIRE(_dependencyMap[16]);
  var _account = _$$_REQUIRE(_dependencyMap[17]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[18]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[19]);
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    screenWidth = _Dimensions$get.width;
  var styles = _reactNative.StyleSheet.create({
    containerStyle: {
      width: screenWidth - 32
    },
    cardContainerStyle: {
      borderRadius: 16,
      width: "100%"
    },
    cardStyle: {
      borderRadius: 16,
      height: 170,
      width: "100%"
    }
  });
  var GamificationBanner = function GamificationBanner() {
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)(),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var _useContext = (0, _react.useContext)(_account.AccountContext),
      masterGameAccountFlag = _useContext.masterGameAccountFlag;
    var accountBannerList = (0, _reactRedux.useSelector)(_aemGroupTwo.AemGroupTwoSelectors.accountBannerList);
    var aemGroupTwoLoading = (0, _reactRedux.useSelector)(_aemGroupTwo.AemGroupTwoSelectors.aemGroupTwoLoading);
    var _useState = (0, _react.useState)(null),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      heightImage = _useState2[0],
      setHeightImage = _useState2[1];
    var _useState3 = (0, _react.useState)("idle"),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      imageStatus = _useState4[0],
      setImageStatus = _useState4[1];
    var isMasterGameAccountFlagEnable = (0, _remoteConfig.getFeatureFlagInit)(_remoteConfig.REMOTE_CONFIG_FLAGS.MASTERGAME_ACCOUNT, masterGameAccountFlag);
    var entryBannerData = (0, _react.useMemo)(function () {
      return accountBannerList == null || accountBannerList.find == null ? undefined : accountBannerList.find(function (item) {
        return (item == null ? undefined : item.type) === _forYouScreen.ACCOUNT_CM24_CARD_TYPE.ACCOUNT_GAMIFICATION;
      });
    }, [JSON.stringify(accountBannerList)]);
    var handlePressCard = function handlePressCard() {
      var _entryBannerData$navi, _entryBannerData$navi2;
      var gamificationBannerTag = (0, _authentication.checkLoginState)() ? _adobe.AdobeValueByTagName.CAppAccountGamificationBannerLoggedIn : _adobe.AdobeValueByTagName.CAppAccountGamificationBannerNonLoggedIn;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppAccountGamification, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppAccountGamification, gamificationBannerTag));
      handleNavigation(entryBannerData == null || (_entryBannerData$navi = entryBannerData.navigation) == null ? undefined : _entryBannerData$navi.type, entryBannerData == null || (_entryBannerData$navi2 = entryBannerData.navigation) == null ? undefined : _entryBannerData$navi2.value, {
        aaTag: _navigationHelper.NavigationAATag.accountGameBanner,
        isLoggedInAtTriggerTime: (0, _authentication.checkLoginState)(),
        pageSource: _navigationHelper.NavigationPageSource.accountGameBanner
      });
    };
    var getSizeImage = function getSizeImage(url) {
      setImageStatus("loading");
      _reactNative.Image.getSize(url, function (width, height) {
        var ratioCurrentCard = height / width;
        var paddingViewRoot = 32;
        var heightImage = ratioCurrentCard * (screenWidth - paddingViewRoot);
        setHeightImage(heightImage);
        setImageStatus("done");
      }, function () {
        setImageStatus("done");
      });
    };
    (0, _react.useEffect)(function () {
      if ((0, _mediaHelper.handleImageUrl)(entryBannerData == null ? undefined : entryBannerData.bgImage)) {
        getSizeImage((0, _mediaHelper.handleImageUrl)(entryBannerData == null ? undefined : entryBannerData.bgImage));
      }
    }, [(0, _mediaHelper.handleImageUrl)(entryBannerData == null ? undefined : entryBannerData.bgImage)]);
    if (!isMasterGameAccountFlagEnable) {
      return null;
    }
    if (imageStatus === "loading" || aemGroupTwoLoading) {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.containerStyle,
        children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _theme.color.shimmerPlacholderColor,
          shimmerStyle: styles.cardStyle
        })
      });
    }
    if (!entryBannerData || imageStatus !== "done") {
      return null;
    }
    return (0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
      onPress: handlePressCard,
      children: (0, _jsxRuntime.jsx)(_baseImage.default, {
        source: {
          uri: (0, _mediaHelper.handleImageUrl)(entryBannerData == null ? undefined : entryBannerData.bgImage)
        },
        style: [styles.cardContainerStyle, {
          height: heightImage
        }],
        resizeMode: "contain"
      })
    });
  };
  var _default = exports.default = GamificationBanner;
