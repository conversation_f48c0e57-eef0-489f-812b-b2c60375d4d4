  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeKeyboardAwareScrollView = _$$_REQUIRE(_dependencyMap[6]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _reactNativeDynamicRender = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _native = _$$_REQUIRE(_dependencyMap[10]);
  var _theme = _$$_REQUIRE(_dependencyMap[11]);
  var _forYouHeader = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _forYouPointsInfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _logout = _$$_REQUIRE(_dependencyMap[14]);
  var _aemRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[15]));
  var _utils = _$$_REQUIRE(_dependencyMap[16]);
  var _nativeAuthRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[17]));
  var _profileRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[18]));
  var _adobe = _$$_REQUIRE(_dependencyMap[19]);
  var _moreMenuOptionV = _$$_REQUIRE(_dependencyMap[20]);
  var _forYouPointsPerks = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[21]));
  var _forYouMonarchConcierge = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[22]));
  var _text = _$$_REQUIRE(_dependencyMap[23]);
  var _palette = _$$_REQUIRE(_dependencyMap[24]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[25]));
  var _accountV2Info = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[26]));
  var _useRewardTier2 = _$$_REQUIRE(_dependencyMap[27]);
  var _forYouNonLogin = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[28]));
  var _jewelPrivilegesDetailControler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[29]));
  var _i18n = _$$_REQUIRE(_dependencyMap[30]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[31]));
  var _JPCBackground = _$$_REQUIRE(_dependencyMap[32]);
  var _ChangiRewardBackground = _$$_REQUIRE(_dependencyMap[33]);
  var _StaffPrivilegesBackground = _$$_REQUIRE(_dependencyMap[34]);
  var _constants = _$$_REQUIRE(_dependencyMap[35]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[36]);
  var _reactNativeDeviceInfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[37]));
  var _forYouRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[38]));
  var _notificationRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[39]));
  var _reactNativeGestureHandler = _$$_REQUIRE(_dependencyMap[40]);
  var _bottomTabs = _$$_REQUIRE(_dependencyMap[41]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[42]);
  var _navigators = _$$_REQUIRE(_dependencyMap[43]);
  var _panResponder = _$$_REQUIRE(_dependencyMap[44]);
  var _suspend = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[45]));
  var _lottieReactNative = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[46]));
  var _miffyGamificationBanner = _$$_REQUIRE(_dependencyMap[47]);
  var _pageConfigRedux = _$$_REQUIRE(_dependencyMap[48]);
  var _pageConfig = _$$_REQUIRE(_dependencyMap[49]);
  var _spendAndWinSection = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[50]));
  var _useStatusBarStyle = _$$_REQUIRE(_dependencyMap[51]);
  var _stickyButtons = _$$_REQUIRE(_dependencyMap[52]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[53]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var CR_PRIVILEGES_IMAGE_URL = _$$_REQUIRE(_dependencyMap[54]);
  var CONTAINER_SPACE = 16;
  var styles = _reactNative2.StyleSheet.create({
    containerStyle: {
      backgroundColor: _theme.color.palette.lightestGrey
    },
    sectionContainerStyle: {
      paddingLeft: CONTAINER_SPACE,
      paddingRight: CONTAINER_SPACE
    },
    captionStyle: {
      color: "#454545",
      fontFamily: 'Lato-Regular',
      marginTop: 12,
      paddingHorizontal: 8,
      marginBottom: 52
    },
    spendNWinContainerStyle: {
      gap: 16,
      marginTop: 50
    },
    spendNWinCTitleTextStyle: Object.assign({}, _text.newPresets.caption2Bold, {
      color: _theme.color.palette.darkestGrey
    })
  });
  var Components = {
    view: _reactNative2.View,
    moreOptions: _moreMenuOptionV.MoreMenuOptionV2,
    logout: _logout.Logout
  };
  var AnimatedPressable = _reactNativeReanimated.default.createAnimatedComponent(_reactNative.Pressable);
  var AnimatedLottieView = _reactNativeReanimated.default.createAnimatedComponent(_lottieReactNative.default);
  var AnimatedKeyboardAwareScrollView = _reactNativeReanimated.default.createAnimatedComponent(_reactNativeKeyboardAwareScrollView.KeyboardAwareScrollView);
  var CARD_VISIBLE_HEIGHT = 36;
  var FIRST_CARD_ADD_HEIGHT = 3;
  var POSITION_JPC = CARD_VISIBLE_HEIGHT;
  var POSITION_STAFF_PRIVILEGES = 72;
  var POSITION_CR_PRIVILEGES_IMAGE = -14;
  var CR_PRIVILEGES_IMAGE_WIDTH = 100;
  var CR_PRIVILEGES_IMAGE_HEIGHT = 64;
  var CR_PRIVILEGES_IMAGE_POSITION_RIGHT = 8;
  var ANIMATION_DURATION = 500;
  var CARD_BORDER_WIDTH = 2;
  var ANIMATION_PROGRESS_START = 0;
  var ANIMATION_PROGRESS_END = 3;
  var _worklet_16175654523297_init_data = {
    code: "function forYouScreenV2Tsx1(){const{isLoggedIn,isRefreshing}=this.__closure;if(!isLoggedIn)return;isRefreshing.value=0;}"
  };
  var _worklet_2741536355947_init_data = {
    code: "function forYouScreenV2Tsx2(){const{isLoggedIn,isRefreshing,runOnJS,handleOverallLoading}=this.__closure;if(!isLoggedIn)return;if(isRefreshing.value===1){runOnJS(handleOverallLoading)();}}"
  };
  var _worklet_11727722719514_init_data = {
    code: "function forYouScreenV2Tsx3(e){const{isLoggedIn,currentScrollPosition,positionStartRefresh,THRESH_HOLD,contentPosition,interpolate,Extrapolation,isRefreshing}=this.__closure;if(!isLoggedIn)return;if(currentScrollPosition.value===0){if(positionStartRefresh.value>0){if(positionStartRefresh.value+THRESH_HOLD<=e.y){contentPosition.value=interpolate(e.y-positionStartRefresh.value,[0,THRESH_HOLD*3],[0,THRESH_HOLD],Extrapolation.CLAMP);isRefreshing.value=1;}else{isRefreshing.value=0;contentPosition.value=0;}}else{positionStartRefresh.value=e.y;isRefreshing.value=0;contentPosition.value=0;}}else{isRefreshing.value=0;contentPosition.value=0;}}"
  };
  var _worklet_13935238356591_init_data = {
    code: "function forYouScreenV2Tsx4(e){const{isLoggedIn,currentScrollPosition,positionStartRefresh}=this.__closure;if(!isLoggedIn)return;if(currentScrollPosition.value===0){positionStartRefresh.value=e.y;}}"
  };
  var _worklet_12631704767501_init_data = {
    code: "function forYouScreenV2Tsx5(){const{CR_PRIVILEGES_IMAGE_WIDTH,CR_PRIVILEGES_IMAGE_HEIGHT,interpolate,animatedProgress,CARD_VISIBLE_HEIGHT,POSITION_CR_PRIVILEGES_IMAGE,Extrapolation,CR_PRIVILEGES_IMAGE_POSITION_RIGHT}=this.__closure;return{width:CR_PRIVILEGES_IMAGE_WIDTH,height:CR_PRIVILEGES_IMAGE_HEIGHT,top:interpolate(animatedProgress.value,[2,3],[CARD_VISIBLE_HEIGHT,POSITION_CR_PRIVILEGES_IMAGE],Extrapolation.CLAMP),opacity:interpolate(animatedProgress.value,[0,2,3],[0,1,1],Extrapolation.CLAMP),right:CR_PRIVILEGES_IMAGE_POSITION_RIGHT,zIndex:11};}"
  };
  var _worklet_1377017983840_init_data = {
    code: "function forYouScreenV2Tsx6(){const{crPrivilegesPosition}=this.__closure;return{top:crPrivilegesPosition.value,borderColor:'rgba(255, 255, 255, 0.4)'};}"
  };
  var _worklet_16957193464834_init_data = {
    code: "function forYouScreenV2Tsx7(){const{interpolate,animatedProgress,POSITION_JPC,FIRST_CARD_ADD_HEIGHT,Extrapolation}=this.__closure;return{top:interpolate(animatedProgress.value,[0.5,2],[0,POSITION_JPC+FIRST_CARD_ADD_HEIGHT],Extrapolation.CLAMP),borderColor:'rgba(255, 255, 255, 0.6)',zIndex:10};}"
  };
  var _worklet_14917753115797_init_data = {
    code: "function forYouScreenV2Tsx8(){const{interpolate,animatedProgress,POSITION_STAFF_PRIVILEGES,FIRST_CARD_ADD_HEIGHT,Extrapolation}=this.__closure;return{top:interpolate(animatedProgress.value,[0,1],[0,POSITION_STAFF_PRIVILEGES+FIRST_CARD_ADD_HEIGHT],Extrapolation.CLAMP),borderColor:'rgba(255, 255, 255, 0.4)',zIndex:12};}"
  };
  var _worklet_**************_init_data = {
    code: "function forYouScreenV2Tsx9(isFinished){const{runOnJS,playLottie}=this.__closure;if(isFinished){runOnJS(playLottie)();}}"
  };
  var _worklet_10446198215642_init_data = {
    code: "function forYouScreenV2Tsx10(){const{LOADING_ANIMATION_HEIGHT,CARD_VISIBLE_HEIGHT,ANIMATION_HEIGHT,CARD_SECTIONS,FIRST_CARD_ADD_HEIGHT,isLoadingPrivilegeCards,interpolate,animatedProgress,CARD_BORDER_WIDTH,Extrapolation}=this.__closure;let startY=-(LOADING_ANIMATION_HEIGHT-CARD_VISIBLE_HEIGHT*2);let endY=-(ANIMATION_HEIGHT-(CARD_VISIBLE_HEIGHT*CARD_SECTIONS.length+FIRST_CARD_ADD_HEIGHT));if(!isLoadingPrivilegeCards){startY=-(ANIMATION_HEIGHT-CARD_VISIBLE_HEIGHT);}return{transform:[{translateY:interpolate(animatedProgress.value,[0,1],[startY,endY+CARD_BORDER_WIDTH],Extrapolation.CLAMP)}],zIndex:15};}"
  };
  var ForYouScreenV2 = function ForYouScreenV2(_ref) {
    var _memberIconInfo$cardG, _memberIconInfo$cardG2;
    var route = _ref.route;
    (0, _useStatusBarStyle.useStatusBarStyle)('dark');
    var scrollViewRef = (0, _react.useRef)(null);
    var accountPageConfiguration = (0, _reactRedux.useSelector)(_pageConfigRedux.PageConfigSelectors.accountPagePayload);
    var accountPageConfigurationLoading = (0, _reactRedux.useSelector)(_pageConfigRedux.PageConfigSelectors.accountPageLoading);
    var isJustLoggedOut = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isJustLoggedOut);
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var profileFetching = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profileFetching);
    var isProfileCardNoMissing = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profileCardNoMissing);
    var rewardsError = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.rewardsError);
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var isJustLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isJustLoggedIn);
    var animatedRef = (0, _react.useRef)(null);
    var isFocused = (0, _native.useIsFocused)();
    var dispatch = (0, _reactRedux.useDispatch)();
    var navigation = (0, _native.useNavigation)();
    var bottomTabHeight = (0, _bottomTabs.useBottomTabBarHeight)();
    var _useHandleScroll = (0, _navigators.useHandleScroll)(),
      handleScroll = _useHandleScroll.handleScroll;
    var _useRewardTier = (0, _useRewardTier2.useRewardTier)(),
      memberIconInfo = _useRewardTier.memberIconInfo,
      rewardsFetching = _useRewardTier.rewardsFetching;
    var feedbackSectionData = Object.assign({}, accountPageConfiguration, {
      children: accountPageConfiguration == null ? undefined : accountPageConfiguration.children.filter(function (item) {
        return item.name === "moreOptions";
      })
    });
    var _useWindowDimensions = (0, _reactNative2.useWindowDimensions)(),
      width = _useWindowDimensions.width;
    var cardWidth = width - 32 + 4;
    // read svg file
    var ACTUAL_CARD_HEIGHT = 96 * cardWidth / 347;
    var rewardsData = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.rewardsData);
    var isLoadingPrivilegeCards = !rewardsData && !rewardsError || profileFetching;
    var notificationsCountLoading = (0, _reactRedux.useSelector)(_notificationRedux.NotificationSelectors.notificationsCountLoading);
    var moreOptionsFetching = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.moreOptionsFetching);
    var accountInfoValuesFetching = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.accountInfoValuesFetching);
    var totalPlaypassPerksFetching = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.totalPlaypassPerksFetching);
    var overallLoading = profileFetching || rewardsFetching || accountPageConfigurationLoading || notificationsCountLoading || moreOptionsFetching || accountInfoValuesFetching || totalPlaypassPerksFetching;
    var currentScrollPosition = (0, _reactNativeReanimated.useSharedValue)(0);
    var positionStartRefresh = (0, _reactNativeReanimated.useSharedValue)(0); //finger position when start pull to refresh
    var isRefreshing = (0, _reactNativeReanimated.useSharedValue)(0);
    var contentPosition = (0, _reactNativeReanimated.useSharedValue)(0);
    var THRESH_HOLD = 80;
    var triggerOverallLoading = (0, _react.useRef)(false);
    var triggerScrollToTop = (0, _react.useRef)(true);
    var _useContext = (0, _react.useContext)(_panResponder.PanResponderContext),
      conditionTimeRef = _useContext.conditionTimeRef,
      idleTimeRef = _useContext.idleTimeRef,
      isPageLoadingRef = _useContext.isPageLoadingRef;
    var miffyProps = (0, _miffyGamificationBanner.useMiffyGamification)();
    var handleJewelPrivilegesPress = (0, _react.useCallback)(function () {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppAccount, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppAccount, (0, _i18n.translate)("forYouScreen.accountSection.jewelPrivilegeCard")));
      _jewelPrivilegesDetailControler.default.showModal(navigation);
    }, [navigation]);
    var handleChangiRewardsPress = (0, _react.useCallback)(function () {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppAccount, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppAccount, (0, _i18n.translate)("forYouScreen.accountSection.changiRewards")));

      //@ts-ignore
      navigation.navigate(_constants.NavigationConstants.changiRewardsPrivilegesScreen, {
        tierMember: memberIconInfo == null ? undefined : memberIconInfo.title
      });
    }, [navigation, memberIconInfo == null ? undefined : memberIconInfo.title]);
    var handleStaffPrivilegesNavigation = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      var UID = yield (0, _screenHelper.getViewerUID)({
        shouldReturnNull: true
      });
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppGalaxyEntry, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppGalaxyEntry, `Explore | ${(0, _i18n.translate)("exploreScreen.staffPerk.viewAll")} | Listing | ${UID}`));
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppAccount, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppAccount, (0, _i18n.translate)("forYouScreen.accountSection.staffPrivileges")));

      //@ts-ignore
      navigation.navigate(_constants.NavigationConstants.staffPerkListing);
    }), [navigation]);
    var handleOverallLoading = function handleOverallLoading() {
      var deviceId = _reactNativeDeviceInfo.default.getUniqueIdSync();
      dispatch(_notificationRedux.default.eventAndPromotionNotificationRequest());
      dispatch(_notificationRedux.default.notificationsCountRequest(deviceId));
      dispatch(_forYouRedux.default.moreOptionsRequest());
      dispatch(_aemRedux.default.getAemConfigData({
        forceRequest: true,
        name: _aemRedux.AEM_PAGE_NAME.AEM_COMMON_DATA,
        pathName: "getAemCommonData"
      }));
      if (isLoggedIn) {
        var _rewardsData$reward, _rewardsData$reward2, _rewardsData$reward3;
        dispatch(_profileRedux.default.profileRequest());
        dispatch(_forYouRedux.default.rewardsRequest(rewardsData == null || (_rewardsData$reward = rewardsData.reward) == null ? undefined : _rewardsData$reward.cardNo));
        (0, _pageConfig.requestAccountLandingPage)();
        dispatch(_forYouRedux.default.accountInfoValuesRequest({
          cardNo: rewardsData == null || (_rewardsData$reward2 = rewardsData.reward) == null ? undefined : _rewardsData$reward2.cardNo
        }));
        dispatch(_forYouRedux.default.totalPlaypassPerksRequest({
          cardNo: rewardsData == null || (_rewardsData$reward3 = rewardsData.reward) == null ? undefined : _rewardsData$reward3.cardNo
        }));
        miffyProps == null || miffyProps.fetchData == null || miffyProps.fetchData();
      }
      triggerOverallLoading.current = true; // To prevent running overallLoading side effect before execute this funtion
    };
    var gesture = _reactNativeGestureHandler.Gesture.Pan().onBegin(function () {
      var forYouScreenV2Tsx4 = function forYouScreenV2Tsx4(e) {
        if (!isLoggedIn) return;
        if (currentScrollPosition.value === 0) {
          positionStartRefresh.value = e.y;
        }
      };
      forYouScreenV2Tsx4.__closure = {
        isLoggedIn: isLoggedIn,
        currentScrollPosition: currentScrollPosition,
        positionStartRefresh: positionStartRefresh
      };
      forYouScreenV2Tsx4.__workletHash = 13935238356591;
      forYouScreenV2Tsx4.__initData = _worklet_13935238356591_init_data;
      return forYouScreenV2Tsx4;
    }()).onChange(function () {
      var forYouScreenV2Tsx3 = function forYouScreenV2Tsx3(e) {
        if (!isLoggedIn) return;
        if (currentScrollPosition.value === 0) {
          if (positionStartRefresh.value > 0) {
            if (positionStartRefresh.value + THRESH_HOLD <= e.y) {
              contentPosition.value = (0, _reactNativeReanimated.interpolate)(e.y - positionStartRefresh.value, [0, 240], [0, THRESH_HOLD], _reactNativeReanimated.Extrapolation.CLAMP);
              isRefreshing.value = 1;
            } else {
              isRefreshing.value = 0;
              contentPosition.value = 0;
            }
          } else {
            positionStartRefresh.value = e.y;
            isRefreshing.value = 0;
            contentPosition.value = 0;
          }
        } else {
          isRefreshing.value = 0;
          contentPosition.value = 0;
        }
      };
      forYouScreenV2Tsx3.__closure = {
        isLoggedIn: isLoggedIn,
        currentScrollPosition: currentScrollPosition,
        positionStartRefresh: positionStartRefresh,
        THRESH_HOLD: THRESH_HOLD,
        contentPosition: contentPosition,
        interpolate: _reactNativeReanimated.interpolate,
        Extrapolation: _reactNativeReanimated.Extrapolation,
        isRefreshing: isRefreshing
      };
      forYouScreenV2Tsx3.__workletHash = 11727722719514;
      forYouScreenV2Tsx3.__initData = _worklet_11727722719514_init_data;
      return forYouScreenV2Tsx3;
    }()).onEnd(function () {
      var forYouScreenV2Tsx2 = function forYouScreenV2Tsx2() {
        if (!isLoggedIn) return;
        if (isRefreshing.value === 1) {
          //refresh
          (0, _reactNativeReanimated.runOnJS)(handleOverallLoading)();
        }
      };
      forYouScreenV2Tsx2.__closure = {
        isLoggedIn: isLoggedIn,
        isRefreshing: isRefreshing,
        runOnJS: _reactNativeReanimated.runOnJS,
        handleOverallLoading: handleOverallLoading
      };
      forYouScreenV2Tsx2.__workletHash = 2741536355947;
      forYouScreenV2Tsx2.__initData = _worklet_2741536355947_init_data;
      return forYouScreenV2Tsx2;
    }()).onFinalize(function () {
      var forYouScreenV2Tsx1 = function forYouScreenV2Tsx1() {
        if (!isLoggedIn) return;
        isRefreshing.value = 0;
      };
      forYouScreenV2Tsx1.__closure = {
        isLoggedIn: isLoggedIn,
        isRefreshing: isRefreshing
      };
      forYouScreenV2Tsx1.__workletHash = 16175654523297;
      forYouScreenV2Tsx1.__initData = _worklet_16175654523297_init_data;
      return forYouScreenV2Tsx1;
    }());
    (0, _react.useEffect)(function () {
      var checkInternet = /*#__PURE__*/function () {
        var _ref3 = (0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch.isConnected;
          if ((0, _utils.ifAllTrue)([isLoggedIn, isFocused, isConnected])) {
            dispatch(_nativeAuthRedux.default.nativeAuthTokenVerifyRequest());
          }
        });
        return function checkInternet() {
          return _ref3.apply(this, arguments);
        };
      }();
      checkInternet();
    }, [isLoggedIn, isFocused]);
    (0, _react.useEffect)(function () {
      if (isLoggedIn && triggerScrollToTop != null && triggerScrollToTop.current) {
        var _scrollViewRef$curren;
        _reactNative2.InteractionManager.runAfterInteractions(function () {
          dispatch(_profileRedux.default.profileRequest());
        });
        scrollViewRef == null || (_scrollViewRef$curren = scrollViewRef.current) == null || _scrollViewRef$curren.scrollToPosition(0, 0, true);
        triggerScrollToTop.current = false;
      } else {
        triggerScrollToTop.current = true;
      }
    }, [isLoggedIn]);
    (0, _react.useEffect)(function () {
      if (isFocused && isLoggedIn && !rewardsFetching && !profileFetching) {
        _reactNative2.InteractionManager.runAfterInteractions(function () {
          setTimeout(function () {
            startCardEffect();
          }, 500);
        });
      } else if (!isLoggedIn || profileFetching) {
        reset();
      }
    }, [isFocused, isLoggedIn, rewardsFetching, profileFetching]);
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)("Account_Landing");
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        _reactNative2.InteractionManager.runAfterInteractions(function () {
          (0, _adobe.commonTrackingScreen)("Account_Landing", (0, _screenHook.getPreviousScreen)(), isLoggedIn);
        });
      });
      return unsubscribeFocus;
    }, [navigation]);
    (0, _react.useEffect)(function () {
      if (isJustLoggedOut) {
        scrollViewRef.current.scrollToPosition(0, 0, true);
      }
    }, [isJustLoggedOut]);
    (0, _react.useEffect)(function () {
      var unsubscribe = navigation.addListener("blur", function () {
        if (isJustLoggedOut) {
          dispatch(_nativeAuthRedux.default.nativeAuthLoginReset());
        }
      });
      return unsubscribe;
    }, [isJustLoggedOut]);
    var jpcImageTopPosition = (0, _reactNativeReanimated.useSharedValue)(CARD_VISIBLE_HEIGHT);
    var animatedProgress = (0, _reactNativeReanimated.useSharedValue)(ANIMATION_PROGRESS_START);
    var crPrivilegesPosition = (0, _reactNativeReanimated.useSharedValue)(0);
    var crPrivilegesAnimatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var forYouScreenV2Tsx5 = function forYouScreenV2Tsx5() {
        return {
          width: CR_PRIVILEGES_IMAGE_WIDTH,
          height: CR_PRIVILEGES_IMAGE_HEIGHT,
          top: (0, _reactNativeReanimated.interpolate)(animatedProgress.value, [2, 3], [CARD_VISIBLE_HEIGHT, POSITION_CR_PRIVILEGES_IMAGE], _reactNativeReanimated.Extrapolation.CLAMP),
          opacity: (0, _reactNativeReanimated.interpolate)(animatedProgress.value, [0, 2, 3], [0, 1, 1], _reactNativeReanimated.Extrapolation.CLAMP),
          right: CR_PRIVILEGES_IMAGE_POSITION_RIGHT,
          zIndex: 11
        };
      };
      forYouScreenV2Tsx5.__closure = {
        CR_PRIVILEGES_IMAGE_WIDTH: CR_PRIVILEGES_IMAGE_WIDTH,
        CR_PRIVILEGES_IMAGE_HEIGHT: CR_PRIVILEGES_IMAGE_HEIGHT,
        interpolate: _reactNativeReanimated.interpolate,
        animatedProgress: animatedProgress,
        CARD_VISIBLE_HEIGHT: CARD_VISIBLE_HEIGHT,
        POSITION_CR_PRIVILEGES_IMAGE: POSITION_CR_PRIVILEGES_IMAGE,
        Extrapolation: _reactNativeReanimated.Extrapolation,
        CR_PRIVILEGES_IMAGE_POSITION_RIGHT: CR_PRIVILEGES_IMAGE_POSITION_RIGHT
      };
      forYouScreenV2Tsx5.__workletHash = 12631704767501;
      forYouScreenV2Tsx5.__initData = _worklet_12631704767501_init_data;
      return forYouScreenV2Tsx5;
    }(), []);
    var crPrivilegesCardAnimatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var forYouScreenV2Tsx6 = function forYouScreenV2Tsx6() {
        return {
          top: crPrivilegesPosition.value,
          borderColor: 'rgba(255, 255, 255, 0.4)'
        };
      };
      forYouScreenV2Tsx6.__closure = {
        crPrivilegesPosition: crPrivilegesPosition
      };
      forYouScreenV2Tsx6.__workletHash = 1377017983840;
      forYouScreenV2Tsx6.__initData = _worklet_1377017983840_init_data;
      return forYouScreenV2Tsx6;
    }(), []);
    var jpcCardAnimatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var forYouScreenV2Tsx7 = function forYouScreenV2Tsx7() {
        return {
          top: (0, _reactNativeReanimated.interpolate)(animatedProgress.value, [0.5, 2], [0, 39], _reactNativeReanimated.Extrapolation.CLAMP),
          borderColor: 'rgba(255, 255, 255, 0.6)',
          zIndex: 10
        };
      };
      forYouScreenV2Tsx7.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        animatedProgress: animatedProgress,
        POSITION_JPC: POSITION_JPC,
        FIRST_CARD_ADD_HEIGHT: FIRST_CARD_ADD_HEIGHT,
        Extrapolation: _reactNativeReanimated.Extrapolation
      };
      forYouScreenV2Tsx7.__workletHash = 16957193464834;
      forYouScreenV2Tsx7.__initData = _worklet_16957193464834_init_data;
      return forYouScreenV2Tsx7;
    }(), []);
    var staffPrivilegesCardAnimatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var forYouScreenV2Tsx8 = function forYouScreenV2Tsx8() {
        return {
          top: (0, _reactNativeReanimated.interpolate)(animatedProgress.value, [0, 1], [0, 75], _reactNativeReanimated.Extrapolation.CLAMP),
          borderColor: 'rgba(255, 255, 255, 0.4)',
          zIndex: 12
        };
      };
      forYouScreenV2Tsx8.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        animatedProgress: animatedProgress,
        POSITION_STAFF_PRIVILEGES: POSITION_STAFF_PRIVILEGES,
        FIRST_CARD_ADD_HEIGHT: FIRST_CARD_ADD_HEIGHT,
        Extrapolation: _reactNativeReanimated.Extrapolation
      };
      forYouScreenV2Tsx8.__workletHash = 14917753115797;
      forYouScreenV2Tsx8.__initData = _worklet_14917753115797_init_data;
      return forYouScreenV2Tsx8;
    }(), []);
    var reset = function reset() {
      jpcImageTopPosition.value = POSITION_JPC;
      animatedProgress.value = (0, _reactNativeReanimated.withTiming)(ANIMATION_PROGRESS_START, {
        duration: ANIMATION_DURATION
      });
    };
    var startCardEffect = function startCardEffect() {
      if (animatedProgress.value === ANIMATION_PROGRESS_END) {
        return;
      }
      animatedProgress.value = (0, _reactNativeReanimated.withTiming)(ANIMATION_PROGRESS_END, {
        duration: ANIMATION_DURATION
      }, function () {
        var forYouScreenV2Tsx9 = function forYouScreenV2Tsx9(isFinished) {
          if (isFinished) {
            (0, _reactNativeReanimated.runOnJS)(playLottie)();
          }
        };
        forYouScreenV2Tsx9.__closure = {
          runOnJS: _reactNativeReanimated.runOnJS,
          playLottie: playLottie
        };
        forYouScreenV2Tsx9.__workletHash = **************;
        forYouScreenV2Tsx9.__initData = _worklet_**************_init_data;
        return forYouScreenV2Tsx9;
      }());
    };
    var playLottie = function playLottie() {
      var _animatedRef$current;
      (_animatedRef$current = animatedRef.current) == null || _animatedRef$current.play();
    };
    var CARD_SECTIONS_NON_STAFF = [{
      title: isProfileCardNoMissing ? "forYouScreen.accountSection.refreshToReload" : "forYouScreen.accountSection.changiRewards",
      styles: [cardStyles.container, crPrivilegesCardAnimatedStyle, {
        height: ACTUAL_CARD_HEIGHT
      }],
      disabled: isProfileCardNoMissing,
      onPress: handleChangiRewardsPress,
      linearBackgroundColor: (_memberIconInfo$cardG = memberIconInfo == null ? undefined : memberIconInfo.cardGradientColor) != null ? _memberIconInfo$cardG : ["#6950A1", "#B28DC1"],
      background: (0, _jsxRuntime.jsx)(_ChangiRewardBackground.ChangiRewards, {
        startColor: isProfileCardNoMissing ? "#ABABAB" : memberIconInfo != null && memberIconInfo.cardGradientColor ? memberIconInfo == null ? undefined : memberIconInfo.cardGradientColor[0] : "#6950A1",
        endColor: isProfileCardNoMissing ? "#E5E5E5" : memberIconInfo != null && memberIconInfo.cardGradientColor ? memberIconInfo == null ? undefined : memberIconInfo.cardGradientColor[1] : "#B28DC1",
        style: {
          position: 'absolute'
        },
        width: cardWidth,
        isLoggedIn: true,
        height: ACTUAL_CARD_HEIGHT
      })
    }, {
      title: "forYouScreen.accountSection.jewelPrivilegeCard",
      styles: [cardStyles.container, jpcCardAnimatedStyle, {
        height: ACTUAL_CARD_HEIGHT
      }],
      onPress: handleJewelPrivilegesPress,
      linearBackgroundColor: ["#018FBB", "#CDD52D"],
      background: (0, _jsxRuntime.jsx)(_JPCBackground.JPCBackground, {
        style: {
          position: 'absolute'
        },
        width: cardWidth,
        height: ACTUAL_CARD_HEIGHT
      })
    }];
    var CARD_SECTIONS_STAFF = [{
      title: isProfileCardNoMissing ? "forYouScreen.accountSection.refreshToReload" : "forYouScreen.accountSection.changiRewards",
      styles: [cardStyles.container, crPrivilegesCardAnimatedStyle, {
        height: ACTUAL_CARD_HEIGHT
      }],
      disabled: isProfileCardNoMissing,
      onPress: handleChangiRewardsPress,
      linearBackgroundColor: (_memberIconInfo$cardG2 = memberIconInfo == null ? undefined : memberIconInfo.cardGradientColor) != null ? _memberIconInfo$cardG2 : ["#6950A1", "#B28DC1"],
      background: (0, _jsxRuntime.jsx)(_ChangiRewardBackground.ChangiRewards, {
        startColor: isProfileCardNoMissing ? "#ABABAB" : memberIconInfo != null && memberIconInfo.cardGradientColor ? memberIconInfo == null ? undefined : memberIconInfo.cardGradientColor[0] : "#6950A1",
        endColor: isProfileCardNoMissing ? "#E5E5E5" : memberIconInfo != null && memberIconInfo.cardGradientColor ? memberIconInfo == null ? undefined : memberIconInfo.cardGradientColor[1] : "#B28DC1",
        style: {
          position: 'absolute'
        },
        width: cardWidth,
        isLoggedIn: true,
        height: ACTUAL_CARD_HEIGHT
      })
    }, {
      title: "forYouScreen.accountSection.jewelPrivilegeCard",
      styles: [cardStyles.container, jpcCardAnimatedStyle, {
        height: ACTUAL_CARD_HEIGHT
      }],
      onPress: handleJewelPrivilegesPress,
      linearBackgroundColor: ["#018FBB", "#CDD52D"],
      background: (0, _jsxRuntime.jsx)(_JPCBackground.JPCBackground, {
        style: {
          position: 'absolute'
        },
        width: cardWidth,
        height: ACTUAL_CARD_HEIGHT
      })
    }, {
      title: "forYouScreen.accountSection.staffPrivileges",
      styles: [cardStyles.container, staffPrivilegesCardAnimatedStyle, {
        height: ACTUAL_CARD_HEIGHT
      }],
      onPress: handleStaffPrivilegesNavigation,
      linearBackgroundColor: ["#E14194", "#682EB1"],
      background: (0, _jsxRuntime.jsx)(_StaffPrivilegesBackground.StaffPrivileges, {
        style: {
          position: 'absolute'
        },
        width: cardWidth,
        height: ACTUAL_CARD_HEIGHT
      })
    }];
    var CARD_SECTIONS = profilePayload != null && profilePayload.airportStaff ? CARD_SECTIONS_STAFF : CARD_SECTIONS_NON_STAFF;
    var ANIMATION_HEIGHT = CARD_VISIBLE_HEIGHT * (CARD_SECTIONS.length - 1) + ACTUAL_CARD_HEIGHT;
    var LOADING_ANIMATION_HEIGHT = CARD_VISIBLE_HEIGHT + ACTUAL_CARD_HEIGHT;
    var animatedRestContainer = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var forYouScreenV2Tsx10 = function forYouScreenV2Tsx10() {
        var startY = -(LOADING_ANIMATION_HEIGHT - 72);
        var endY = -(ANIMATION_HEIGHT - (CARD_VISIBLE_HEIGHT * CARD_SECTIONS.length + FIRST_CARD_ADD_HEIGHT));
        if (!isLoadingPrivilegeCards) {
          startY = -(ANIMATION_HEIGHT - CARD_VISIBLE_HEIGHT);
        }
        return {
          transform: [{
            translateY: (0, _reactNativeReanimated.interpolate)(animatedProgress.value, [0, 1], [startY, endY + CARD_BORDER_WIDTH], _reactNativeReanimated.Extrapolation.CLAMP)
          }],
          zIndex: 15
        };
      };
      forYouScreenV2Tsx10.__closure = {
        LOADING_ANIMATION_HEIGHT: LOADING_ANIMATION_HEIGHT,
        CARD_VISIBLE_HEIGHT: CARD_VISIBLE_HEIGHT,
        ANIMATION_HEIGHT: ANIMATION_HEIGHT,
        CARD_SECTIONS: CARD_SECTIONS,
        FIRST_CARD_ADD_HEIGHT: FIRST_CARD_ADD_HEIGHT,
        isLoadingPrivilegeCards: isLoadingPrivilegeCards,
        interpolate: _reactNativeReanimated.interpolate,
        animatedProgress: animatedProgress,
        CARD_BORDER_WIDTH: CARD_BORDER_WIDTH,
        Extrapolation: _reactNativeReanimated.Extrapolation
      };
      forYouScreenV2Tsx10.__workletHash = 10446198215642;
      forYouScreenV2Tsx10.__initData = _worklet_10446198215642_init_data;
      return forYouScreenV2Tsx10;
    }(), [CARD_SECTIONS, isLoadingPrivilegeCards]);
    var CARD_LOADING = (0, _react.useMemo)(function () {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: [cardStyles.loadingContainerStyle, {
          height: ACTUAL_CARD_HEIGHT
        }],
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: [cardStyles.loadingSection1Style, {
            height: ACTUAL_CARD_HEIGHT
          }],
          children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _theme.color.shimmerPlacholderColor,
            shimmerStyle: cardStyles.loadingShimmer
          })
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: [cardStyles.loadingSection2Style, {
            height: ACTUAL_CARD_HEIGHT
          }],
          children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _theme.color.shimmerPlacholderColor,
            shimmerStyle: cardStyles.loadingShimmer
          })
        })]
      });
    }, [ACTUAL_CARD_HEIGHT]);
    (0, _react.useEffect)(function () {
      if (!overallLoading && contentPosition.value && triggerOverallLoading.current) {
        contentPosition.value = (0, _reactNativeReanimated.withTiming)(0);
        triggerOverallLoading.current = false;
      }
    }, [overallLoading]);
    (0, _react.useEffect)(function () {
      // Set page loading state for app rating logic
      isPageLoadingRef.current = !!overallLoading;
      if (!overallLoading) {
        (0, _screenHelper.resetInactivityTimeout)({
          conditionTimeRef: conditionTimeRef,
          idleTimeRef: idleTimeRef,
          callback: function callback() {
            return (0, _screenHelper.trackingShowRatingPopup)({
              route: route
            });
          }
        });
      }
    }, [overallLoading]);
    (0, _react.useEffect)(function () {
      if (!rewardsData && isJustLoggedIn) {
        handleOverallLoading();
      }
    }, [rewardsData, isJustLoggedIn]);
    var renderLoggedIn = function renderLoggedIn() {
      return (0, _jsxRuntime.jsxs)(_react.Fragment, {
        children: [(0, _jsxRuntime.jsx)(_forYouPointsPerks.default, {}), (0, _jsxRuntime.jsx)(_forYouMonarchConcierge.default, {}), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: [cardStyles.infoContainer, {
            height: isLoadingPrivilegeCards ? LOADING_ANIMATION_HEIGHT : ANIMATION_HEIGHT
          }],
          children: isLoadingPrivilegeCards ? CARD_LOADING : (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
            children: [CARD_SECTIONS.map(function (section) {
              return (0, _jsxRuntime.jsx)(AnimatedPressable, {
                style: section.styles,
                onPress: section.onPress,
                disabled: section == null ? undefined : section.disabled,
                children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                  style: [cardStyles.cardBackground, {
                    height: ACTUAL_CARD_HEIGHT
                  }],
                  children: [section.background, (0, _jsxRuntime.jsx)(_reactNative2.View, {
                    style: cardStyles.titleContainer,
                    children: (0, _jsxRuntime.jsx)(_text.Text, {
                      style: cardStyles.titleText,
                      tx: section.title
                    })
                  })]
                })
              }, section.title);
            }), (0, _jsxRuntime.jsx)(_reactNative.Pressable, {
              disabled: isProfileCardNoMissing,
              onPress: handleChangiRewardsPress,
              children: (0, _jsxRuntime.jsx)(AnimatedLottieView, {
                loop: false,
                source: CR_PRIVILEGES_IMAGE_URL,
                autoPlay: false,
                ref: animatedRef,
                style: [cardStyles.crPrivilegesImage, crPrivilegesAnimatedStyle]
              })
            })]
          })
        }), (0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
          style: animatedRestContainer,
          children: [(0, _jsxRuntime.jsx)(_accountV2Info.default, {
            isLoadingPrivilegeCards: isLoadingPrivilegeCards
          }), (0, _jsxRuntime.jsx)(_forYouPointsInfo.default, {}), (0, _jsxRuntime.jsx)(_spendAndWinSection.default, {
            miffyProps: miffyProps
          }), (0, _jsxRuntime.jsx)(_reactNativeDynamicRender.default, Object.assign({}, feedbackSectionData, {
            mapComponents: Components,
            refParent: scrollViewRef
          })), (0, _jsxRuntime.jsx)(_logout.Logout, {
            data: scrollViewRef
          }), (0, _jsxRuntime.jsxs)(_text.Text, {
            style: styles.captionStyle,
            children: ["v", _reactNativeDeviceInfo.default.getVersion(), " (", _reactNativeDeviceInfo.default.getBuildNumber(), ")"]
          })]
        })]
      });
    };
    var renderNonLoggedIn = function renderNonLoggedIn() {
      return (0, _jsxRuntime.jsxs)(_react.Fragment, {
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: cardStyles.infoContainerNonLogin,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: [cardStyles.cardBackground, {
              height: ACTUAL_CARD_HEIGHT,
              zIndex: 1
            }],
            children: [(0, _jsxRuntime.jsx)(_ChangiRewardBackground.ChangiRewards, {
              style: {
                position: "absolute"
              },
              width: "100%",
              height: ACTUAL_CARD_HEIGHT,
              isLoggedIn: false
            }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: cardStyles.titleContainer,
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                style: cardStyles.titleText,
                tx: "forYouScreen.accountSection.changiRewards"
              })
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: [cardStyles.cardBackground, {
              height: ACTUAL_CARD_HEIGHT,
              zIndex: 2,
              top: -(ACTUAL_CARD_HEIGHT - CARD_VISIBLE_HEIGHT - FIRST_CARD_ADD_HEIGHT)
            }],
            children: [(0, _jsxRuntime.jsx)(_JPCBackground.JPCBackground, {
              style: {
                position: "absolute"
              },
              width: "100%",
              height: ACTUAL_CARD_HEIGHT
            }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: cardStyles.titleContainer,
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                style: cardStyles.titleText,
                tx: "forYouScreen.accountSection.jewelPrivilegeCard"
              })
            })]
          }), (0, _jsxRuntime.jsx)(AnimatedLottieView, {
            style: Object.assign({}, cardStyles.crPrivilegesImage, {
              top: -9,
              right: CR_PRIVILEGES_IMAGE_POSITION_RIGHT,
              zIndex: 1,
              width: CR_PRIVILEGES_IMAGE_WIDTH,
              height: CR_PRIVILEGES_IMAGE_HEIGHT
            }),
            autoPlay: true,
            loop: false,
            source: CR_PRIVILEGES_IMAGE_URL
          })]
        }), (0, _jsxRuntime.jsx)(_forYouNonLogin.default, {}), (0, _jsxRuntime.jsx)(_spendAndWinSection.default, {
          miffyProps: miffyProps
        }), (0, _jsxRuntime.jsx)(_reactNativeDynamicRender.default, Object.assign({}, feedbackSectionData, {
          mapComponents: Components,
          refParent: scrollViewRef
        })), (0, _jsxRuntime.jsx)(_logout.Logout, {
          data: scrollViewRef
        }), (0, _jsxRuntime.jsxs)(_text.Text, {
          style: [styles.captionStyle, {
            paddingBottom: 52 + bottomTabHeight
          }],
          children: ["v", _reactNativeDeviceInfo.default.getVersion(), " (", _reactNativeDeviceInfo.default.getBuildNumber(), ")"]
        })]
      });
    };
    return (0, _jsxRuntime.jsxs)(_reactNativeGestureHandler.GestureHandlerRootView, {
      children: [(0, _jsxRuntime.jsx)(_stickyButtons.ForYouStickyButtons, {}), (0, _jsxRuntime.jsx)(_reactNativeGestureHandler.GestureDetector, {
        gesture: _reactNativeGestureHandler.Gesture.Simultaneous(gesture, _reactNativeGestureHandler.Gesture.Native()),
        children: (0, _jsxRuntime.jsxs)(AnimatedKeyboardAwareScrollView, {
          showsVerticalScrollIndicator: false,
          style: styles.containerStyle,
          ref: scrollViewRef,
          bounces: false,
          onScroll: function onScroll(e) {
            if (isFocused) {
              handleScroll(e);
            }
            currentScrollPosition.value = e.nativeEvent.contentOffset.y;
          },
          onMomentumScrollEnd: function onMomentumScrollEnd() {
            if (currentScrollPosition.value <= 0) {
              scrollViewRef.current.scrollToPosition(0, 0, true);
            }
          },
          children: [(0, _jsxRuntime.jsx)(_forYouHeader.default, {
            positionY: contentPosition
          }), (0, _jsxRuntime.jsx)(_suspend.default, {
            freeze: !isFocused,
            children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: styles.sectionContainerStyle,
              children: isLoggedIn ? renderLoggedIn() : renderNonLoggedIn()
            })
          })]
        })
      })]
    });
  };
  var cardStyles = _reactNative2.StyleSheet.create({
    container: {
      flex: 1,
      borderRadius: 12,
      position: 'absolute',
      top: 0,
      right: 0,
      bottom: 0,
      left: -2
    },
    titleContainer: {
      height: CARD_VISIBLE_HEIGHT,
      marginHorizontal: 16,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center'
    },
    titleText: Object.assign({}, _text.presets.XSmallBold, {
      fontSize: 12,
      textTransform: 'none',
      color: _palette.palette.whiteGrey,
      textAlign: 'center',
      marginTop: 4
    }),
    crPrivilegesImage: {
      position: 'absolute'
    },
    titleTierText: {
      fontSize: 12,
      textTransform: 'uppercase',
      fontFamily: _theme.typography.bold,
      fontStyle: "normal",
      letterSpacing: 1.4,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 16,
      color: _palette.palette.whiteGrey,
      marginTop: 4
    },
    titleTierContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center'
    },
    infoContainer: {
      marginTop: 21
    },
    infoContainerNonLogin: {
      marginTop: 40,
      height: 72,
      width: _reactNative2.Dimensions.get('window').width - 30,
      left: -2
    },
    cardBackground: {
      borderRadius: 9
    },
    borderCardStyle: {
      borderWidth: 2,
      borderRadius: 12,
      borderColor: "rgba(255, 255, 255, 0.4)"
    },
    loadingContainer: {
      flex: 1,
      borderRadius: 12,
      position: 'absolute',
      top: 0,
      right: 0,
      bottom: 0,
      left: 0,
      backgroundColor: 'white',
      borderWidth: CARD_BORDER_WIDTH,
      borderColor: 'rgba(255, 255, 255, 0.6)'
    },
    loadingShimmer: {
      marginTop: 12,
      marginHorizontal: 16,
      height: 12,
      width: 129,
      borderRadius: 4
    },
    loadingContainerStyle: {
      position: 'relative'
    },
    loadingSection1Style: Object.assign({}, _theme.shadow.primaryShadow, {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 12,
      bottom: 0,
      left: 0,
      position: 'absolute',
      right: 0,
      top: 0
    }),
    loadingSection2Style: Object.assign({}, _theme.shadow.primaryShadow, {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderColor: _theme.color.palette.lighterGrey,
      borderRadius: 12,
      borderTopWidth: CARD_BORDER_WIDTH,
      bottom: 0,
      left: 0,
      position: "absolute",
      right: 0,
      shadowOffset: {
        width: 0,
        height: -4
      },
      shadowOpacity: 0.2,
      top: 36
    })
  });
  var _default = exports.default = (0, _react.memo)(ForYouScreenV2);
