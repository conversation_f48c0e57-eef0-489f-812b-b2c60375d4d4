  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _forYouScreenV = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _screenHook = _$$_REQUIRE(_dependencyMap[3]);
  var _constants = _$$_REQUIRE(_dependencyMap[4]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  var ForYouScreenWrapper = function ForYouScreenWrapper(props) {
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)(_constants.TrackingScreenName.AccountLanding);
    return (0, _jsxRuntime.jsx)(_forYouScreenV.default, Object.assign({}, props));
  };
  var _default = exports.default = ForYouScreenWrapper;
