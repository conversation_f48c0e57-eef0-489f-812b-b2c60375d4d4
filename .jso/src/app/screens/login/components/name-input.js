  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.validateName = exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _react = _$$_REQUIRE(_dependencyMap[3]);
  var _inputWrapper = _$$_REQUIRE(_dependencyMap[4]);
  var _inputField = _$$_REQUIRE(_dependencyMap[5]);
  var _nativeLoginStyle = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _viewShadowWrap = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _validate = _$$_REQUIRE(_dependencyMap[9]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  var validateName = exports.validateName = function validateName(name) {
    return _validate.profileNameRegex.test(name);
  };
  var NameInput = (0, _react.forwardRef)(function (_ref, ref) {
    var name = _ref.name,
      setName = _ref.setName,
      labelTx = _ref.labelTx,
      styleContainer = _ref.styleContainer,
      _ref$isLastName = _ref.isLastName,
      isLastName = _ref$isLastName === undefined ? false : _ref$isLastName,
      onEndEdit = _ref.onEndEdit,
      _ref$blurOnSubmit = _ref.blurOnSubmit,
      blurOnSubmit = _ref$blurOnSubmit === undefined ? true : _ref$blurOnSubmit,
      labelStyleProps = _ref.labelStyleProps;
    var _useState = (0, _react.useState)(""),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      errorName = _useState2[0],
      setErrorName = _useState2[1];
    var _useState3 = (0, _react.useState)(true),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isFirstFocus = _useState4[0],
      setIsFirstFocus = _useState4[1];
    var internalRef = (0, _react.useRef)(null);
    var inputRef = ref || internalRef;
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isFocused = _useState6[0],
      setIsFocused = _useState6[1];
    var onBlurInput = function onBlurInput() {
      setIsFocused(false);
      handleNameError();
      if (isFirstFocus) setIsFirstFocus(false);
    };
    var handleNameError = (0, _react.useCallback)(function () {
      if (!name) {
        if (isLastName) setErrorName("nativeLoginScreen.supplementData.error.emptyLastName");else setErrorName("nativeLoginScreen.supplementData.error.emptyFirstName");
      } else if (!validateName(name)) {
        setErrorName("nativeLoginScreen.supplementData.error.invalidName");
      } else {
        setErrorName("");
      }
    }, [name]);
    (0, _react.useEffect)(function () {
      if (!isFirstFocus) {
        handleNameError();
      }
    }, [name, isFirstFocus]);
    return (0, _jsxRuntime.jsx)(_inputWrapper.InputWrapper, {
      isInvalid: isFirstFocus ? false : !validateName(name),
      labelTx: labelTx,
      helpTextTx: errorName,
      style: Object.assign({}, _nativeLoginStyle.default.inputEmail, styleContainer, {
        marginBottom: 0
      }),
      numberOfLinesError: 2,
      labelStyleProps: labelStyleProps,
      children: (0, _jsxRuntime.jsx)(_viewShadowWrap.default, {
        isFocused: isFocused,
        isInvalid: isFirstFocus ? false : !validateName(name),
        children: (0, _jsxRuntime.jsx)(_inputField.InputField, {
          forwardedRef: inputRef,
          isInvalid: isFirstFocus ? false : !validateName(name),
          onChangeText: setName,
          onBlur: onBlurInput,
          onFocus: function onFocus() {
            return setIsFocused(true);
          },
          onSubmitEditing: onEndEdit || null,
          textContentType: isLastName ? "familyName" : "givenName",
          highlightOnFocused: true,
          autoComplete: isLastName ? "family-name" : "given-name",
          maxLength: 50,
          numberOfLines: 1,
          inputMode: "text",
          autoCapitalize: "words",
          enterKeyHint: "next",
          blurOnSubmit: blurOnSubmit,
          autoCorrect: false,
          cursorColor: _theme.color.palette.overlayColor,
          selectionColor: _reactNative.Platform.select({
            ios: _theme.color.palette.overlayColor,
            android: _theme.color.palette.lightestIrisBlue
          }),
          value: name
        })
      })
    });
  });
  var _default = exports.default = NameInput;
