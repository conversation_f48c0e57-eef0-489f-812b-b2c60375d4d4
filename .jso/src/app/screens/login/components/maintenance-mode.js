  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _button = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _modalTopBar = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _nativeLoginStyle = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _resetPassword = _$$_REQUIRE(_dependencyMap[8]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _text = _$$_REQUIRE(_dependencyMap[10]);
  var _utils = _$$_REQUIRE(_dependencyMap[11]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[12]);
  var _reactNativeRenderHtml = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[13]));
  var _htmlContent = _$$_REQUIRE(_dependencyMap[14]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[15]);
  var _react = _$$_REQUIRE(_dependencyMap[16]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[17]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _Dimensions$get = _reactNative.Dimensions.get("screen"),
    width = _Dimensions$get.width;
  var CONTENT_WIDTH = width - 48;
  var systemFonts = [].concat((0, _toConsumableArray2.default)(_reactNativeRenderHtml.defaultSystemFonts), ["Lato-Regular", "Lato-Bold"]);
  var tagStyle = {
    u: {
      textAlignVertical: "center",
      textAlign: "center",
      textDecorationLine: "underline"
    },
    a: Object.assign({}, _text.newPresets.bodyTextBold, {
      color: _theme.color.palette.lightPurple,
      textAlignVertical: "center",
      textAlign: "center",
      textDecorationLine: "none"
    }),
    p: Object.assign({}, _text.newPresets.bodyTextRegular, {
      color: _theme.color.palette.almostBlackGrey,
      textAlignVertical: "center",
      textAlign: "center"
    }),
    div: Object.assign({}, _text.newPresets.bodyTextRegular, {
      color: _theme.color.palette.almostBlackGrey,
      textAlignVertical: "center",
      textAlign: "center"
    })
  };
  var MaintenanceMode = function MaintenanceMode(_ref) {
    var data = _ref.data;
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)(""),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var _useState = (0, _react.useState)(true),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      loading = _useState2[0],
      setLoading = _useState2[1];
    var handleOnPress = function handleOnPress() {
      var _data$navigation, _data$navigation2;
      handleNavigation(data == null || (_data$navigation = data.navigation) == null ? undefined : _data$navigation.type, data == null || (_data$navigation2 = data.navigation) == null ? undefined : _data$navigation2.value, data == null ? undefined : data.redirect);
    };
    (0, _react.useEffect)(function () {
      setTimeout(function () {
        setLoading(false);
      }, 1500);
    }, []);
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: _nativeLoginStyle.default.containerPage,
      children: [(0, _jsxRuntime.jsx)(_modalTopBar.default, {
        onPressBack: handleOnPress
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: innerStyles.content,
        children: [(data == null ? undefined : data.icon) && (0, _jsxRuntime.jsx)(_reactNative.Image, {
          source: {
            uri: (0, _utils.mappingUrlAem)(data == null ? undefined : data.icon)
          },
          style: innerStyles.image
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: Object.assign({}, _text.presets.h2, {
            color: _theme.color.palette.almostBlackGrey
          }),
          text: data == null ? undefined : data.title
        }), (0, _jsxRuntime.jsx)(_reactNativeRenderHtml.default, {
          systemFonts: systemFonts,
          tagsStyles: tagStyle,
          contentWidth: CONTENT_WIDTH,
          source: {
            html: (0, _htmlContent.formatHtmlContent)(data == null ? undefined : data.description, true)
          }
        }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
          end: {
            x: 0,
            y: 1
          },
          colors: _resetPassword.BTN_COLOR.ACTIVE,
          start: {
            x: 0,
            y: 0
          },
          style: Object.assign({}, _nativeLoginStyle.default.btnLogin, {
            width: "100%"
          }),
          children: (0, _jsxRuntime.jsx)(_button.Button, {
            onPress: handleOnPress,
            text: (data == null ? undefined : data.buttonLabel) || "",
            textStyle: Object.assign({}, _nativeLoginStyle.default.textBtn, {
              color: "white"
            })
          })
        })]
      }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
        visible: loading
      })]
    });
  };
  var _default = exports.default = MaintenanceMode;
  var innerStyles = _reactNative.StyleSheet.create({
    image: {
      width: 120,
      height: 120,
      marginBottom: 24
    },
    content: Object.assign({}, _nativeLoginStyle.default.content, {
      alignItems: "center",
      marginTop: 24
    })
  });
