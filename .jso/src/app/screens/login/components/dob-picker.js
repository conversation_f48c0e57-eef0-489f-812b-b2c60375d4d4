  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = DobPicker;
  exports.validateDob = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _inputWrapper = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _icons = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _nativeLoginStyle = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _inputField = _$$_REQUIRE(_dependencyMap[8]);
  var _text = _$$_REQUIRE(_dependencyMap[9]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[10]));
  var _datetimepicker = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _reactNativeModal = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[14]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var HEIGHT = _reactNative2.Dimensions.get("window").height;
  var TODAY = new Date();
  var MINIMUM_DATE = new Date(TODAY.getFullYear() - 120, TODAY.getMonth(), TODAY.getDate());
  var validateDob = exports.validateDob = function validateDob(dob) {
    if (!dob) return false;
    var minAge = 16;
    var sixteenthBirthday = new Date(dob.getFullYear() + minAge, dob.getMonth(), dob.getDate());
    return sixteenthBirthday <= TODAY;
  };
  function DobPicker(_ref) {
    var dob = _ref.dob,
      setDob = _ref.setDob;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      showPicker = _useState2[0],
      setShowPicker = _useState2[1];
    var _useState3 = (0, _react.useState)(""),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      errorDob = _useState4[0],
      setErrorDob = _useState4[1];
    var _useState5 = (0, _react.useState)(true),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isFirstFocus = _useState6[0],
      setIsFirstFocus = _useState6[1];
    var invalidDob = isFirstFocus ? false : !validateDob(dob);
    var colorScheme = (0, _reactNative2.useColorScheme)();
    var textColor = colorScheme === "dark" ? _theme.color.palette.whiteGrey : _theme.color.palette.almostBlackGrey;
    var onBlurInput = function onBlurInput() {
      if (isFirstFocus) setIsFirstFocus(false);
    };
    var handleMailError = function handleMailError() {
      if (!dob) {
        setErrorDob("nativeLoginScreen.supplementData.error.emptyDob");
      } else if (!validateDob(dob)) {
        setErrorDob("nativeLoginScreen.supplementData.error.under16");
      } else {
        setErrorDob("");
      }
    };
    (0, _react.useEffect)(function () {
      if (!isFirstFocus) handleMailError();
    }, [dob, isFirstFocus]);
    var showDatePicker = function showDatePicker() {
      _reactNative2.Keyboard.dismiss();
      setShowPicker(true);
    };
    var closeDatePicker = function closeDatePicker() {
      setShowPicker(false);
      onBlurInput();
    };
    var onChange = function onChange(event, selectedDate) {
      var currentDate = selectedDate;
      if (_reactNative2.Platform.OS === "ios") {
        setDob(currentDate);
      } else {
        closeDatePicker();
        if (event.type === "set") {
          setDob(currentDate);
        } else setDob(dob || null);
      }
    };
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
        children: (0, _jsxRuntime.jsxs)(_inputWrapper.InputWrapper, {
          isInvalid: invalidDob,
          labelTx: "nativeLoginScreen.supplementData.dob",
          helpTextTx: errorDob,
          style: innerStyles.inputWrap,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: innerStyles.warningTxt,
            tx: "nativeLoginScreen.supplementData.warningDob"
          }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
            onPress: showDatePicker,
            style: !invalidDob ? Object.assign({}, innerStyles.container) : Object.assign({}, innerStyles.container, {
              borderColor: _theme.color.palette.baseRed
            }),
            children: [(0, _jsxRuntime.jsx)(_icons.CalenderDob, {
              width: "24",
              height: "24",
              color: _theme.color.palette.midGrey
            }), (0, _jsxRuntime.jsx)(_reactNative2.TextInput, {
              onPressIn: showDatePicker,
              editable: false,
              placeholder: "DD / MMM / YYYY",
              style: innerStyles.txtDob,
              numberOfLines: 1,
              value: dob ? (0, _moment.default)(dob).format("DD MMM YYYY") : ""
            })]
          })]
        })
      }), showPicker && (_reactNative2.Platform.OS === "ios" ? (0, _jsxRuntime.jsx)(_reactNativeModal.default, {
        isVisible: showPicker,
        style: innerStyles.containerBts,
        onBackdropPress: closeDatePicker,
        animationInTiming: 500,
        animationOutTiming: 500,
        children: (0, _jsxRuntime.jsx)(_datetimepicker.default, {
          value: dob || TODAY,
          mode: "date",
          is24Hour: true,
          display: "spinner",
          onChange: onChange,
          maximumDate: TODAY,
          minimumDate: MINIMUM_DATE,
          textColor: _theme.color.palette.almostBlackGrey
        })
      }) : (0, _jsxRuntime.jsx)(_datetimepicker.default, {
        value: dob || TODAY,
        mode: "date",
        is24Hour: true,
        display: "spinner",
        onChange: onChange,
        maximumDate: TODAY,
        minimumDate: MINIMUM_DATE,
        positiveButton: {
          label: "Confirm",
          textColor: textColor
        },
        negativeButton: {
          label: "Cancel",
          textColor: textColor
        }
      }))]
    });
  }
  var innerStyles = _reactNative2.StyleSheet.create({
    warningTxt: Object.assign({}, _text.presets.caption1Regular, {
      paddingBottom: 6,
      marginTop: -6
    }),
    txtBtnOk: {
      alignSelf: "center",
      marginVertical: 10,
      color: "#4676ee",
      fontSize: 20
    },
    containerBts: {
      borderRadius: 16,
      backgroundColor: _theme.color.palette.whiteGrey,
      marginHorizontal: 8,
      marginBottom: HEIGHT / 2.9,
      marginTop: HEIGHT / 2.9,
      alignSelf: "center",
      width: "80%"
    },
    txtDob: Object.assign({}, _text.presets.bodyTextRegular, {
      paddingStart: 10,
      width: "100%",
      color: _theme.color.palette.almostBlackGrey
    }),
    inputWrap: Object.assign({}, _nativeLoginStyle.default.inputEmail, {
      marginBottom: 0
    }),
    container: Object.assign({}, _inputField.inputGroupStyle, {
      alignItems: "center",
      paddingStart: 12
    }),
    viewDivider: {
      height: 1,
      backgroundColor: _theme.color.palette.lightGrey
    }
  });
