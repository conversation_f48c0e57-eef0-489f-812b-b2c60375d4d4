  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _icons = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNativeWebview = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _termsOfUsePolicy = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _react = _$$_REQUIRE(_dependencyMap[8]);
  var _text = _$$_REQUIRE(_dependencyMap[9]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  var WebViewPolicy = function WebViewPolicy(_ref) {
    var uri = _ref.uri,
      close = _ref.close;
    var _useState = (0, _react.useState)(""),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      title = _useState2[0],
      setTitle = _useState2[1];
    var checkEncodeURI = function checkEncodeURI(url) {
      return /%/i.test(url);
    };
    var onNavigationStateChange = function onNavigationStateChange(navState) {
      setTitle(navState == null ? undefined : navState.title);
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.headerContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.leftHeaderWebview,
          children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPressIn: close,
            hitSlop: {
              top: 10,
              left: 10,
              bottom: 10,
              right: 10
            },
            testID: "BackButtonWebViewScreen",
            accessibilityLabel: "BackButtonWebViewScreen",
            children: (0, _jsxRuntime.jsx)(_icons.ArrowLeft, {})
          })
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.middleHeaderWebview,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "subTitleBold",
            text: title || "",
            style: styles.headerTitleTextStyle,
            numberOfLines: 1,
            testID: "HeaderWebViewScreen",
            accessibilityLabel: "HeaderWebViewScreen"
          })
        })]
      }), (0, _jsxRuntime.jsx)(_reactNativeWebview.default, {
        containerStyle: _termsOfUsePolicy.styles.containerStyleWebview,
        source: {
          uri: _reactNative2.Platform.OS === "ios" && !checkEncodeURI(uri) ? encodeURI(uri) : uri
        },
        originWhitelist: ["*"],
        style: _termsOfUsePolicy.styles.containerWebview,
        automaticallyAdjustContentInsets: false,
        showsVerticalScrollIndicator: false,
        scrollEnabled: true,
        onNavigationStateChange: onNavigationStateChange,
        showsHorizontalScrollIndicator: false,
        javaScriptEnabled: true
      })]
    });
  };
  var _default = exports.default = WebViewPolicy;
  var styles = _reactNative2.StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: _theme.color.palette.whiteGrey
    },
    leftHeaderWebview: {
      alignItems: "flex-start",
      justifyContent: "center"
    },
    headerContainerStyle: {
      alignItems: "center",
      justifyContent: "flex-start",
      flexDirection: "row",
      padding: 16
    },
    middleHeaderWebview: {
      width: "82%",
      alignItems: "center",
      justifyContent: "center",
      paddingStart: 20
    },
    headerTitleTextStyle: {
      color: _theme.color.palette.almostBlackGrey,
      flex: 1,
      textAlign: "center",
      fontSize: 16
    }
  });
