  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.isValidEmail = exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _inputWrapper = _$$_REQUIRE(_dependencyMap[4]);
  var _inputField = _$$_REQUIRE(_dependencyMap[5]);
  var _react = _$$_REQUIRE(_dependencyMap[6]);
  var _nativeLoginStyle = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _theme = _$$_REQUIRE(_dependencyMap[8]);
  var _viewShadowWrap = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _validate = _$$_REQUIRE(_dependencyMap[10]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  var isValidEmail = exports.isValidEmail = function isValidEmail(email) {
    var regex = /^(?!.*@(moosbay\.com|my2ducks\.com)$)(?=(.{1,64}@.{1,255}))([!#$%&\+\'\-\/=?\^_{|}~a-zA-Z0-9]+(\.[!#$%&\'\-\/=?\^_{|}~a-zA-Z0-9]+){0,})@((\[(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}\])|([a-zA-Z0-9-]{1,63}(\.[a-zA-Z0-9-]{2,63}){1,}))$/;
    return regex.test(email);
  };
  var EmailInputSignUp = (0, _react.forwardRef)(function (_ref, ref) {
    var email = _ref.email,
      setEmail = _ref.setEmail,
      emailError = _ref.emailError,
      setEmailError = _ref.setEmailError,
      setEmailBlocklistChecked = _ref.setEmailBlocklistChecked,
      onSubmitEdit = _ref.onSubmitEdit,
      onCheckBlockEmail = _ref.onCheckBlockEmail;
    var _useState = (0, _react.useState)(true),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isFirstFocus = _useState2[0],
      setIsFirstFocus = _useState2[1];
    var internalRef = (0, _react.useRef)(null);
    var inputRef = ref || internalRef;
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isFocused = _useState4[0],
      setIsFocused = _useState4[1];
    var onBlurInput = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        if (email != null && email.length) {
          setEmail(email == null ? undefined : email.toLowerCase());
        }
        setIsFocused(false);
        var isValid = handleMailError(email == null ? undefined : email.toLowerCase());
        if (isValid) {
          var isBlock = yield onCheckBlockEmail(email == null ? undefined : email.toLowerCase());
          setEmailBlocklistChecked(true);
          if (isBlock) {
            setEmailError("nativeLoginScreen.invalidEmailError");
          } else {
            setEmailError("");
          }
        }
        if (isFirstFocus) setIsFirstFocus(false);
      });
      return function onBlurInput() {
        return _ref2.apply(this, arguments);
      };
    }();
    var onFocusInput = function onFocusInput() {
      setIsFocused(true);
      setEmailBlocklistChecked(false);
    };
    var handleMailError = (0, _react.useCallback)(function (input) {
      if (input.length === 0) {
        setEmailError("nativeLoginScreen.emptyEmailError");
        return false;
      } else if (!isValidEmail(input)) {
        setEmailError("nativeLoginScreen.invalidEmailError");
        return false;
      } else {
        setEmailError("");
        return true;
      }
    }, [setEmailError]);
    var submitEdit = function submitEdit() {
      onSubmitEdit == null || onSubmitEdit();
    };
    var onEmailChange = function onEmailChange(email) {
      setEmail(email);
      if (isFirstFocus) {
        return;
      }
      if (email.length === 0) {
        setEmailError("nativeLoginScreen.emptyEmailError");
      } else if (!isValidEmail(email)) {
        setEmailError("nativeLoginScreen.invalidEmailError");
      } else {
        setEmailError("");
      }
    };
    var isInvalid = (0, _react.useMemo)(function () {
      return isFirstFocus ? false : !(0, _validate.isEmpty)(emailError);
    }, [isFirstFocus, emailError]);
    return (0, _jsxRuntime.jsx)(_inputWrapper.InputWrapper, {
      isInvalid: isInvalid,
      labelTx: "nativeLoginScreen.emailAddress",
      helpTextTx: emailError,
      style: _nativeLoginStyle.default.inputEmail,
      numberOfLinesError: 1,
      children: (0, _jsxRuntime.jsx)(_viewShadowWrap.default, {
        isFocused: isFocused,
        isInvalid: isInvalid,
        children: (0, _jsxRuntime.jsx)(_inputField.InputField, {
          forwardedRef: inputRef,
          isInvalid: isInvalid,
          onChangeText: onEmailChange,
          value: email,
          onBlur: onBlurInput,
          onSubmitEditing: submitEdit,
          onFocus: onFocusInput,
          textContentType: "emailAddress",
          highlightOnFocused: true,
          autoComplete: "email",
          inputMode: "email",
          autoCapitalize: "none",
          enterKeyHint: "next",
          autoCorrect: false,
          cursorColor: _theme.color.palette.overlayColor,
          selectionColor: _reactNative.Platform.select({
            ios: _theme.color.palette.overlayColor,
            android: _theme.color.palette.lightestIrisBlue
          })
        })
      })
    });
  });
  var _default = exports.default = EmailInputSignUp;
