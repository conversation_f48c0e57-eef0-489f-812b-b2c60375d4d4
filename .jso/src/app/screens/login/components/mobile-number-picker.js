  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.validateMobileNumber = exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _inputWrapper = _$$_REQUIRE(_dependencyMap[2]);
  var _nativeLoginStyle = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _react = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _icons = _$$_REQUIRE(_dependencyMap[7]);
  var _inputPhone = _$$_REQUIRE(_dependencyMap[8]);
  var _text = _$$_REQUIRE(_dependencyMap[9]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  var validateMobileNumber = exports.validateMobileNumber = function validateMobileNumber(mobileNumber, countryCode) {
    var regex = /^\d{5,15}$/;
    var totalLength = mobileNumber.length + countryCode.length;
    return regex.test(mobileNumber) && totalLength <= 15;
  };
  var MobileNumberPicker = (0, _react.forwardRef)(function (_ref, ref) {
    var mobileNumber = _ref.mobileNumber,
      setMobileNumber = _ref.setMobileNumber,
      countryCode = _ref.countryCode,
      countryCodeOptions = _ref.countryCodeOptions,
      setCountryCode = _ref.setCountryCode,
      _ref$selectionColor = _ref.selectionColor,
      selectionColor = _ref$selectionColor === undefined ? _theme.color.palette.overlayColor : _ref$selectionColor;
    var _useState = (0, _react.useState)(""),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      errorMobileNumber = _useState2[0],
      setErrorMobileNumber = _useState2[1];
    var _useState3 = (0, _react.useState)(true),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isFirstFocus = _useState4[0],
      setIsFirstFocus = _useState4[1];
    var internalRef = (0, _react.useRef)(null);
    var inputRef = ref || internalRef;
    var onBlurInput = function onBlurInput() {
      handlePhoneError();
      if (isFirstFocus) setIsFirstFocus(false);
    };
    var handlePhoneError = (0, _react.useCallback)(function () {
      if (!mobileNumber) {
        setErrorMobileNumber("nativeLoginScreen.supplementData.error.emptyMobileNumber");
      } else if (!validateMobileNumber(mobileNumber, countryCode)) {
        setErrorMobileNumber("nativeLoginScreen.supplementData.error.invalidMobileNumber");
      } else {
        setErrorMobileNumber("");
      }
    }, [mobileNumber, countryCode]);
    (0, _react.useEffect)(function () {
      if (!isFirstFocus) {
        handlePhoneError();
      }
    }, [mobileNumber, isFirstFocus, countryCode]);
    return (0, _jsxRuntime.jsx)(_inputWrapper.InputWrapper, {
      isInvalid: isFirstFocus ? false : !validateMobileNumber(mobileNumber, countryCode),
      labelTx: "nativeLoginScreen.supplementData.mobileNumber",
      helpTextTx: errorMobileNumber,
      style: internalStyles.container,
      children: (0, _jsxRuntime.jsx)(_inputPhone.InputPhone, {
        forwardedRef: inputRef,
        isInvalid: isFirstFocus ? false : !validateMobileNumber(mobileNumber, countryCode),
        countryOptions: countryCodeOptions,
        phoneNumber: mobileNumber,
        countryCode: countryCode,
        maxLength: 15,
        textContentType: "telephoneNumber",
        autoComplete: "tel-country-code",
        inputStyle: internalStyles.input,
        onPhoneChange: setMobileNumber,
        onBlurInput: onBlurInput,
        onBlur: onBlurInput,
        onCountryCodeChange: setCountryCode,
        highlightOnFocused: true,
        selectionColor: selectionColor,
        cursorColor: _theme.color.palette.overlayColor,
        iconDownArrow: (0, _jsxRuntime.jsx)(_icons.DownArrowGrey, {
          width: 24,
          height: 24,
          color: _theme.color.palette.almostBlackGrey
        })
      })
    });
  });
  var _default = exports.default = MobileNumberPicker;
  var internalStyles = _reactNative.StyleSheet.create({
    input: Object.assign({}, _text.presets.bodyTextRegular, {
      marginTop: _reactNative.Platform.OS === "ios" ? -2 : 2,
      backgroundColor: "transparent",
      textAlignVertical: "center",
      color: _theme.color.palette.almostBlackGrey,
      paddingStart: 4
    }),
    container: Object.assign({}, _nativeLoginStyle.default.inputEmail, {
      marginBottom: 0
    })
  });
