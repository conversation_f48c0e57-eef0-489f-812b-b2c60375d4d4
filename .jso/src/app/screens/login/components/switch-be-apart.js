  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = SwitchBeApart;
  var _switch = _$$_REQUIRE(_dependencyMap[1]);
  var _inputWrapper = _$$_REQUIRE(_dependencyMap[2]);
  var _nativeLoginStyle = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  function SwitchBeApart(_ref) {
    var enableBeApart = _ref.enableBeApart,
      setEnableBeApart = _ref.setEnableBeApart,
      labelTx = _ref.labelTx,
      descriptionTx = _ref.descriptionTx;
    var onChangeSwitch = function onChangeSwitch() {
      setEnableBeApart(function (prev) {
        return !prev;
      });
    };
    return (0, _jsxRuntime.jsx)(_inputWrapper.InputWrapper, {
      labelTx: labelTx,
      style: innerStyles.wrapContainer,
      children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: innerStyles.viewContainer,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          tx: descriptionTx,
          style: innerStyles.txDescription
        }), (0, _jsxRuntime.jsx)(_switch.Switch, {
          value: enableBeApart,
          onToggle: onChangeSwitch
        })]
      })
    });
  }
  var innerStyles = _reactNative.StyleSheet.create({
    wrapContainer: Object.assign({}, _nativeLoginStyle.default.inputEmail, {
      marginBottom: 0
    }),
    viewContainer: {
      flexDirection: "row",
      alignItems: "center",
      width: "100%"
    },
    txDescription: Object.assign({}, _nativeLoginStyle.default.txErrorLogin, {
      color: _theme.color.palette.almostBlackGrey,
      paddingRight: 24
    })
  });
