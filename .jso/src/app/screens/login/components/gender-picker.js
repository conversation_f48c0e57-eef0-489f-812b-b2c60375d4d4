  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = GenderPicker;
  var _inputWrapper = _$$_REQUIRE(_dependencyMap[1]);
  var _selectPicker = _$$_REQUIRE(_dependencyMap[2]);
  var _icons = _$$_REQUIRE(_dependencyMap[3]);
  var _nativeLoginStyle = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _i18n = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[7]);
  var _theme = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  var genderOptions = [{
    value: "m",
    label: (0, _i18n.translate)("profile.male"),
    color: _theme.color.palette.darkestGrey
  }, {
    value: "f",
    label: (0, _i18n.translate)("profile.female"),
    color: _theme.color.palette.darkestGrey
  }, {
    value: "u",
    label: (0, _i18n.translate)("profile.genderNone"),
    color: _theme.color.palette.darkestGrey
  }];
  function GenderPicker(_ref) {
    var gender = _ref.gender,
      setGender = _ref.setGender;
    var placeHolderAndroid = {
      color: _theme.color.palette.midGrey,
      label: (0, _i18n.translate)("nativeLoginScreen.supplementData.selectAnOption"),
      value: ""
    };
    return (0, _jsxRuntime.jsxs)(_inputWrapper.InputWrapper, {
      labelTx: "profile.gender",
      style: innerStyles.container,
      numberOfLinesError: 1,
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        style: innerStyles.txOptional,
        tx: "nativeLoginScreen.supplementData.optional"
      }), (0, _jsxRuntime.jsx)(_selectPicker.SelectPicker, {
        options: genderOptions,
        value: gender,
        onChangeValue: setGender,
        placeholderObj: _reactNative.Platform.OS === "android" ? placeHolderAndroid : {},
        highlightOnFocused: true,
        placeholderTx: "nativeLoginScreen.supplementData.selectAnOption",
        icon: _icons.DownArrowGrey
      })]
    });
  }
  var innerStyles = _reactNative.StyleSheet.create({
    txOptional: Object.assign({}, _text.presets.caption1Regular, {
      position: "absolute",
      right: 0,
      color: _theme.color.palette.darkGrey999
    }),
    container: Object.assign({}, _nativeLoginStyle.default.inputEmail, {
      marginBottom: 0
    })
  });
