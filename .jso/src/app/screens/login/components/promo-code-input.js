  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _inputWrapper = _$$_REQUIRE(_dependencyMap[2]);
  var _inputField = _$$_REQUIRE(_dependencyMap[3]);
  var _nativeLoginStyle = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[6]);
  var _react = _$$_REQUIRE(_dependencyMap[7]);
  var _viewShadowWrap = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  var PromoCodeInput = (0, _react.forwardRef)(function (_ref, ref) {
    var code = _ref.code,
      setCode = _ref.setCode,
      styleContainer = _ref.styleContainer,
      errorPromo = _ref.errorPromo,
      labelTx = _ref.labelTx,
      onEndEdit = _ref.onEndEdit,
      _ref$blurOnSubmit = _ref.blurOnSubmit,
      blurOnSubmit = _ref$blurOnSubmit === undefined ? true : _ref$blurOnSubmit,
      onBlur = _ref.onBlur,
      _ref$isDisable = _ref.isDisable,
      isDisable = _ref$isDisable === undefined ? false : _ref$isDisable;
    var internalRef = (0, _react.useRef)(null);
    var inputRef = ref || internalRef;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isFocused = _useState2[0],
      setIsFocused = _useState2[1];
    var onBlurInput = function onBlurInput() {
      setIsFocused(false);
      onBlur();
    };
    return (0, _jsxRuntime.jsx)(_inputWrapper.InputWrapper, {
      isInvalid: !!errorPromo,
      labelTx: labelTx,
      style: Object.assign({}, _nativeLoginStyle.default.inputEmail, styleContainer, {
        marginBottom: 0
      }),
      numberOfLinesError: 1,
      helpTextTx: errorPromo,
      children: (0, _jsxRuntime.jsx)(_viewShadowWrap.default, {
        isFocused: isFocused,
        isInvalid: !!errorPromo,
        children: (0, _jsxRuntime.jsx)(_inputField.InputField, {
          forwardedRef: inputRef,
          isInvalid: !!errorPromo,
          onChangeText: setCode,
          value: code,
          highlightOnFocused: true,
          blurOnSubmit: blurOnSubmit,
          inputMode: "text",
          autoCapitalize: "none",
          enterKeyHint: "next",
          autoCorrect: false,
          selectionColor: _reactNative.Platform.select({
            ios: _theme.color.palette.overlayColor,
            android: _theme.color.palette.lightestIrisBlue
          }),
          onBlur: onBlurInput,
          onFocus: function onFocus() {
            return setIsFocused(true);
          },
          isDisabled: isDisable,
          disableStyle: _nativeLoginStyle.default.disabledField,
          cursorColor: _theme.color.palette.overlayColor
        })
      })
    });
  });
  var _default = exports.default = PromoCodeInput;
