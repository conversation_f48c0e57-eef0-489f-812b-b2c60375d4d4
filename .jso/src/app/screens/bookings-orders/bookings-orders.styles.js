  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    containerStyle: {
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      flex: 1
    },
    emptySection: {
      flex: 1,
      marginTop: 120
    },
    bottomSpaceContainerStyle: {
      height: 97
    },
    errorOverlayStyle: {
      top: 80,
      width: "100%",
      position: "absolute"
    },
    reloadSectionIcon: {
      marginBottom: 10
    },
    scrollViewContentContainerStyle: {
      paddingTop: 12
    },
    errorContainer: {
      flex: 1,
      paddingHorizontal: 24,
      marginTop: 165
    },
    apiErrorContainer: {
      justifyContent: "center",
      paddingHorizontal: 24,
      marginTop: 165
    }
  });
