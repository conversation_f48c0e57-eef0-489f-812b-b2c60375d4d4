  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _creditsCard = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[3]);
  var CreditsListing = function CreditsListing(_ref) {
    var creditsDetailsData = _ref.creditsDetailsData,
      navigation = _ref.navigation;
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: creditsDetailsData.map(function (item, index) {
        return (0, _jsxRuntime.jsx)(_creditsCard.default, {
          item: item,
          navigation: navigation
        }, `credits-card-${index}-${item.creditName}`);
      })
    });
  };
  var _default = exports.default = CreditsListing;
