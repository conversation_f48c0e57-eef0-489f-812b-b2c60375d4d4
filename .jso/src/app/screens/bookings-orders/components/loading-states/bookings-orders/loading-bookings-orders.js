  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _exploreStaffPerkItem = _$$_REQUIRE(_dependencyMap[4]);
  var _constants = _$$_REQUIRE(_dependencyMap[5]);
  var _loadingBookingsOrders = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  var LoadingElement = function LoadingElement() {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: _loadingBookingsOrders.styles.elementWrapper,
      children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        shimmerColors: _exploreStaffPerkItem.lightGreyLoadingColors,
        shimmerStyle: _loadingBookingsOrders.styles.elementSquare,
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _loadingBookingsOrders.styles.elementRight,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          shimmerColors: _exploreStaffPerkItem.lightGreyLoadingColors,
          shimmerStyle: _loadingBookingsOrders.styles.elementRightFirstRow,
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          shimmerColors: _exploreStaffPerkItem.lightGreyLoadingColors,
          shimmerStyle: _loadingBookingsOrders.styles.elementRightSecondRow,
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS
        })]
      })]
    });
  };
  var LoadingBookingsOrders = function LoadingBookingsOrders(props) {
    var customStyle = props.customStyle;
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: [_loadingBookingsOrders.styles.wrapper, customStyle],
      children: [(0, _jsxRuntime.jsx)(LoadingElement, {}), (0, _jsxRuntime.jsx)(LoadingElement, {}), (0, _jsxRuntime.jsx)(LoadingElement, {})]
    });
  };
  var _default = exports.default = LoadingBookingsOrders;
