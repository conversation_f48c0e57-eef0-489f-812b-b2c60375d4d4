  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var deviceWidth = _reactNative.Dimensions.get("window").width;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    wrapper: {
      flex: 1,
      margin: 16
    },
    elementWrapper: Object.assign({
      padding: 16,
      marginBottom: 12,
      borderRadius: 12,
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: _theme.color.palette.whiteGrey
    }, _theme.shadow.primaryShadow),
    elementSquare: {
      width: 80,
      height: 80
    },
    elementRight: {
      marginLeft: 12
    },
    elementRightFirstRow: {
      height: 12,
      borderRadius: 4,
      width: deviceWidth - 156
    },
    elementRightSecondRow: {
      width: 74,
      height: 12,
      marginTop: 12,
      borderRadius: 4
    }
  });
