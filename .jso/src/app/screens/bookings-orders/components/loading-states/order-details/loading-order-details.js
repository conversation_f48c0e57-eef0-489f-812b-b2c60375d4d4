  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _constants = _$$_REQUIRE(_dependencyMap[5]);
  var _loadingOrderDetails = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  var shimmerColors = [_theme.color.palette.whiteGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var LoadingHeader = function LoadingHeader() {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: _loadingOrderDetails.styles.headerWrapper,
      children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        shimmerColors: shimmerColors,
        shimmerStyle: _loadingOrderDetails.styles.headerFullWidthRow,
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        shimmerColors: shimmerColors,
        shimmerStyle: _loadingOrderDetails.styles.headerFullWidthRow,
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        shimmerColors: shimmerColors,
        shimmerStyle: _loadingOrderDetails.styles.headerShortRow,
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS
      })]
    });
  };
  var LoadingElement = function LoadingElement() {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: _loadingOrderDetails.styles.elementWrapper,
      children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        shimmerColors: shimmerColors,
        shimmerStyle: _loadingOrderDetails.styles.elementSquare,
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _loadingOrderDetails.styles.elementRight,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          shimmerColors: shimmerColors,
          shimmerStyle: _loadingOrderDetails.styles.elementRightFirstRow,
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          shimmerColors: shimmerColors,
          shimmerStyle: _loadingOrderDetails.styles.elementRightSecondRow,
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS
        })]
      })]
    });
  };
  var LoadingOrderDetails = function LoadingOrderDetails(props) {
    var customStyle = props.customStyle;
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: [_loadingOrderDetails.styles.wrapper, customStyle],
      children: [(0, _jsxRuntime.jsx)(LoadingHeader, {}), (0, _jsxRuntime.jsx)(LoadingElement, {}), (0, _jsxRuntime.jsx)(LoadingElement, {})]
    });
  };
  var _default = exports.default = LoadingOrderDetails;
