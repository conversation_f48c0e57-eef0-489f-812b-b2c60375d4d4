  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _default = exports.default = _reactNative.StyleSheet.create({
    card: {
      boxSizing: "content-box",
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      marginTop: 12,
      marginHorizontal: 16,
      padding: 16,
      shadowColor: _theme.color.palette.almostBlackGrey,
      shadowOffset: {
        width: 0,
        height: 6
      },
      shadowOpacity: 0.08,
      shadowRadius: 20,
      elevation: 8
    },
    cardHeader: {
      flexDirection: "row",
      alignItems: "flex-start",
      marginBottom: 24,
      gap: 12
    },
    image: {
      width: 60,
      height: 60
    },
    mainInfo: {
      flex: 1,
      justifyContent: "center"
    },
    bookingType: {
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.darkestGrey,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      fontSize: 11,
      lineHeight: 14,
      marginBottom: 4
    },
    policyName: {
      color: _theme.color.palette.almostBlackGrey,
      marginBottom: 4
    },
    insured: {
      color: _theme.color.palette.lightPurple
    },
    row: {
      alignItems: "center",
      flexDirection: "row",
      gap: 4
    },
    destination: {
      color: _theme.color.palette.darkestGrey
    },
    duration: {
      color: _theme.color.palette.darkestGrey
    },
    cardFooter: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginTop: 12
    },
    planName: {
      color: _theme.color.palette.almostBlackGrey
    },
    totalCost: {
      color: _theme.color.palette.almostBlackGrey
    }
  });
