  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _insuranceBookingCard = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _icons = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  var InsuranceBookingCard = function InsuranceBookingCard(_ref) {
    var order = _ref.order,
      _onPress = _ref.onPress;
    return (0, _jsxRuntime.jsxs)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
      onPress: function onPress() {
        return _onPress == null ? undefined : _onPress(order);
      },
      style: _insuranceBookingCard.default.card,
      children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _insuranceBookingCard.default.cardHeader,
        children: [(0, _jsxRuntime.jsx)(_reactNative.Image, {
          style: _insuranceBookingCard.default.image,
          source: _$$_REQUIRE(_dependencyMap[9]),
          resizeMode: "cover"
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: _insuranceBookingCard.default.mainInfo,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: _insuranceBookingCard.default.bookingType,
            tx: "bookingsOrdersScreen.orderCardTitle"
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "bodyTextBold",
            text: order.policyName,
            style: _insuranceBookingCard.default.policyName
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "caption2Bold",
            style: _insuranceBookingCard.default.insured,
            children: order.insuredAdultsChildrenCounts
          })]
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _insuranceBookingCard.default.row,
        children: [(0, _jsxRuntime.jsx)(_icons.LocationOutline, {
          width: 12,
          height: 12,
          color: _theme.color.palette.darkestGrey
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "caption2Regular",
          style: _insuranceBookingCard.default.destination,
          children: order.destination
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _insuranceBookingCard.default.row,
        children: [(0, _jsxRuntime.jsx)(_icons.CalendarOutline, {
          color: _theme.color.palette.darkestGrey,
          height: 12,
          width: 12
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "caption2Regular",
          style: _insuranceBookingCard.default.duration,
          children: order.duration
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _insuranceBookingCard.default.cardFooter,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          preset: "bodyTextBold",
          style: _insuranceBookingCard.default.planName,
          children: order.planName
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "bodyTextBold",
          style: _insuranceBookingCard.default.totalCost,
          children: order.totalCost
        })]
      })]
    });
  };
  var _default = exports.default = InsuranceBookingCard;
