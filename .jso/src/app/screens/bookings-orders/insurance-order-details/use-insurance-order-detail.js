  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useInsuranceOrderDetail = useInsuranceOrderDetail;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _$$_REQUIRE(_dependencyMap[3]);
  var _request = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _services = _$$_REQUIRE(_dependencyMap[5]);
  var _queries = _$$_REQUIRE(_dependencyMap[6]);
  var _useInternetConnection = _$$_REQUIRE(_dependencyMap[7]);
  var validateInsuranceData = function validateInsuranceData(data) {
    var requiredFields = ['orderId', 'policyName', 'policyNumber', 'insuredAdultsChildrenCounts', 'destination', 'duration', 'planName', 'totalCost', 'subtotalAmount'];
    var missingFields = requiredFields.filter(function (field) {
      var value = data == null ? undefined : data[field];
      return value === undefined || value === null || value === '';
    });
    if (missingFields.length > 0) {
      throw new Error(`Missing or empty required fields: ${missingFields.join(', ')}`);
    }
  };
  function useInsuranceOrderDetail(orderId) {
    var _useInternetConnectio = (0, _useInternetConnection.useInternetConnection)(),
      isInternetConnected = _useInternetConnectio.isInternetConnected,
      checkInternetConnection = _useInternetConnectio.checkInternetConnection;
    var _useState = (0, _react.useState)(null),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      data = _useState2[0],
      setData = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      loading = _useState4[0],
      setLoading = _useState4[1];
    var _useState5 = (0, _react.useState)(null),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      error = _useState6[0],
      setError = _useState6[1];
    var fetchInsuranceOrderDetail = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      if (!orderId) return;
      setLoading(true);
      setError(null);
      try {
        var _response$data, _response$data2, _response$data3, _response$data4;
        var response = yield (0, _request.default)((0, _services.generateGraphQLRequestBody)({
          graphqlInput: {
            input: {
              orderId: orderId
            }
          },
          query: _queries.getInsuranceOrderDetails
        }));
        if (!response.success || response != null && (_response$data = response.data) != null && _response$data.errors || !(response != null && (_response$data2 = response.data) != null && (_response$data2 = _response$data2.data) != null && _response$data2.getBookingsOrdersDetails) || (response == null || (_response$data3 = response.data) == null || (_response$data3 = _response$data3.data) == null || (_response$data3 = _response$data3.getBookingsOrdersDetails) == null ? undefined : _response$data3.status) === "ERROR") {
          throw new Error("Error fetching insurance order details");
        }
        var responseData = response == null || (_response$data4 = response.data) == null || (_response$data4 = _response$data4.data) == null || (_response$data4 = _response$data4.getBookingsOrdersDetails) == null ? undefined : _response$data4.data;
        if (!responseData) {
          throw new Error('No data received from API');
        }

        // Validate the response data - will throw if invalid
        validateInsuranceData(responseData);

        // If validation passes, set the data
        setData(responseData);
      } catch (err) {
        setError(err);
        setData(null);
      } finally {
        setLoading(false);
      }
    }), [orderId]);
    var handleFetchData = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      var isConnected = yield checkInternetConnection();
      if (isConnected) {
        fetchInsuranceOrderDetail();
      }
    }), [fetchInsuranceOrderDetail]);
    (0, _react.useEffect)(function () {
      handleFetchData();
    }, [handleFetchData]);
    return {
      data: data,
      loading: loading,
      error: error,
      isInternetConnected: isInternetConnected,
      handleFetchData: handleFetchData
    };
  }
