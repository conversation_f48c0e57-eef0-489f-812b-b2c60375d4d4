  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[2]);
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _icons = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _insuranceOrderDetailsBg = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _PaymentSummary = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _PlanDetails = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[9]);
  var _loadingPlayPassDetail = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _emptyState = _$$_REQUIRE(_dependencyMap[11]);
  var _useInsuranceOrderDetail = _$$_REQUIRE(_dependencyMap[12]);
  var _i18n = _$$_REQUIRE(_dependencyMap[13]);
  var _contactChangiAssureSheet = _$$_REQUIRE(_dependencyMap[14]);
  var _react = _$$_REQUIRE(_dependencyMap[15]);
  var _focusStatusBar = _$$_REQUIRE(_dependencyMap[16]);
  var _native = _$$_REQUIRE(_dependencyMap[17]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[18]);
  var _forYouRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[19]));
  var _useStatusBarStyle = _$$_REQUIRE(_dependencyMap[20]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[21]);
  var HEADER_WIDTH = 375;
  var HEADER_HEIGHT = 228;
  var InsuranceOrderDetailsScreen = function InsuranceOrderDetailsScreen(_ref) {
    var _route$params$orderId, _route$params;
    var navigation = _ref.navigation,
      route = _ref.route;
    (0, _useStatusBarStyle.useStatusBarStyle)("dark");
    var dispatch = (0, _reactRedux.useDispatch)();
    var insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    var orderId = (_route$params$orderId = route == null || (_route$params = route.params) == null ? undefined : _route$params.orderId) != null ? _route$params$orderId : "";
    var _useInsuranceOrderDet = (0, _useInsuranceOrderDetail.useInsuranceOrderDetail)(orderId),
      data = _useInsuranceOrderDet.data,
      loading = _useInsuranceOrderDet.loading,
      error = _useInsuranceOrderDet.error,
      isInternetConnected = _useInsuranceOrderDet.isInternetConnected,
      handleFetchData = _useInsuranceOrderDet.handleFetchData;
    var contactSheetRef = (0, _react.useRef)(null);
    var _useWindowDimensions = (0, _reactNative.useWindowDimensions)(),
      width = _useWindowDimensions.width;
    (0, _react.useEffect)(function () {
      navigation.setOptions({
        title: (0, _i18n.translate)("insuranceOrderDetailsScreen.title")
      });
    }, []);
    (0, _native.useFocusEffect)(function () {
      return function () {
        // set cache data to true when go back to bookings orders screen
        dispatch(_forYouRedux.default.dataBookingAndOrderCacheData(true));
      };
    });
    var renderContent = function renderContent() {
      if (!isInternetConnected) {
        return (0, _jsxRuntime.jsx)(_emptyState.EmptyState.NoInternet, {
          containerStyle: styles.errorContainer,
          onPressReload: handleFetchData
        });
      }
      if (error) {
        return (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.apiErrorContainer,
          children: (0, _jsxRuntime.jsx)(_emptyState.EmptyState.ApiError, {
            onPressReload: handleFetchData
          })
        });
      }
      return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
        children: (0, _jsxRuntime.jsxs)(_reactNative.ScrollView, {
          style: styles.scrollView,
          contentContainerStyle: {
            paddingBottom: insets.bottom + 24
          },
          children: [(0, _jsxRuntime.jsx)(_insuranceOrderDetailsBg.default, {
            width: Math.ceil(width),
            height: Math.ceil(width * HEADER_HEIGHT / HEADER_WIDTH),
            style: styles.backgroundImage
          }), loading && (0, _jsxRuntime.jsx)(_loadingPlayPassDetail.default, {}), data && (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
            children: [(0, _jsxRuntime.jsx)(_PlanDetails.default, {
              data: data
            }), (0, _jsxRuntime.jsx)(_PaymentSummary.default, {
              data: data
            }), (0, _jsxRuntime.jsxs)(_reactNative2.TouchableOpacity, {
              style: styles.editButton,
              accessibilityLabel: "InsuranceOrderDetails__EnquiriesButton",
              testID: "InsuranceOrderDetails__EnquiriesButton",
              onPress: function onPress() {
                var _contactSheetRef$curr;
                return (_contactSheetRef$curr = contactSheetRef.current) == null ? undefined : _contactSheetRef$curr.open();
              },
              children: [(0, _jsxRuntime.jsx)(_icons.ServiceCustomerSolid, {
                width: 16,
                height: 16,
                color: _theme.color.palette.darkestGrey
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                style: styles.editButtonText,
                tx: "insuranceOrderDetailsScreen.forEnquiries"
              })]
            })]
          })]
        })
      });
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.screenContainer,
      children: [(0, _jsxRuntime.jsx)(_focusStatusBar.FocusAwareStatusBar, {
        backgroundColor: "transparent",
        barStyle: "dark-content",
        translucent: true
      }), renderContent(), (0, _jsxRuntime.jsx)(_contactChangiAssureSheet.ContactChangiAssureSheet, {
        ref: contactSheetRef
      })]
    });
  };
  var _default = exports.default = InsuranceOrderDetailsScreen;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    screenContainer: {
      flex: 1,
      backgroundColor: _theme.color.palette.lightestGrey
    },
    backgroundImage: {
      position: "absolute",
      top: 0,
      right: 0,
      left: 0,
      zIndex: -1
    },
    scrollView: {
      flex: 1
    },
    editButton: {
      alignSelf: "stretch",
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "flex-start",
      marginHorizontal: 24,
      gap: 8
    },
    editButtonText: Object.assign({}, _text.newPresets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey
    }),
    errorContainer: {
      flex: 1,
      paddingHorizontal: 24,
      marginTop: 165
    },
    apiErrorContainer: {
      justifyContent: "center",
      paddingHorizontal: 24,
      marginTop: 180
    }
  });
