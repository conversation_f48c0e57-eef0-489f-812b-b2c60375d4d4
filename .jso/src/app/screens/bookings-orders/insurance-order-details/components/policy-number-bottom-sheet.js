  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.PolicyNumberBottomSheet = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNativeLinearGradient = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _text2 = _$$_REQUIRE(_dependencyMap[8]);
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _check = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _useModal2 = _$$_REQUIRE(_dependencyMap[11]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[12]);
  var _navigationType = _$$_REQUIRE(_dependencyMap[13]);
  var _deviceUtils = _$$_REQUIRE(_dependencyMap[14]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[15]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var BOTTOM_SHEET_HEIGHT = 353;
  var PolicyNumberBottomSheet = exports.PolicyNumberBottomSheet = (0, _react.forwardRef)(function (_ref, ref) {
    var policyNumber = _ref.policyNumber;
    var _useModal = (0, _useModal2.useModal)("insurancePolicyNumber"),
      isModalVisible = _useModal.isModalVisible,
      openModal = _useModal.openModal,
      closeModal = _useModal.closeModal;
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("INSURANCE_ORDER_DETAILS"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var open = function open() {
      openModal();
      try {
        (0, _deviceUtils.setStringToClipboard)(policyNumber, function () {
          return _reactNative2.AccessibilityInfo.announceForAccessibility('Policy number copied to clipboard');
        });
      } catch (error) {
        console.error('Failed to copy policy number:', error);
      }
    };
    (0, _react.useImperativeHandle)(ref, function () {
      return {
        open: open,
        close: closeModal
      };
    });
    var handleGoToChangiAssure = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        closeModal();
        handleNavigation(_navigationType.NavigationTypeEnum.deepLink, _navigationHelper.NavigationValueDeepLink.ctsAssure);
      });
      return function handleGoToChangiAssure() {
        return _ref2.apply(this, arguments);
      };
    }();
    return (0, _jsxRuntime.jsx)(_bottomSheet.default, {
      isModalVisible: Boolean(isModalVisible),
      onClosedSheet: closeModal,
      stopDragCollapse: true,
      onBackPressHandle: closeModal,
      containerStyle: styles.container,
      animationInTiming: 300,
      animationOutTiming: 300,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.content,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.policyNumber,
          children: policyNumber
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.policyNumberLabel,
          children: "Policy Number"
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.copiedBadge,
          children: [(0, _jsxRuntime.jsx)(_check.default, {
            width: 16,
            height: 16,
            color: _theme.color.palette.lightPurple
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.copiedText,
            children: "Policy Number Copied"
          })]
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.description,
          children: "Enter your policy number when you submit your claim at ChangiAssure\n\nFor orders with multiple policyholders (‘Group’ orders), please refer to your policy confirmation email for the respective policy numbers."
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.buttonContainer,
          children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: styles.closeButtonBottom,
            onPress: closeModal,
            accessibilityRole: "button",
            accessibilityLabel: "PolicyNumberBottomSheet__CloseButton",
            testID: "PolicyNumberBottomSheet__CloseButton",
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.closeButtonText,
              children: "Close"
            })
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: {
              flex: 1
            },
            onPress: handleGoToChangiAssure,
            accessibilityRole: "button",
            accessibilityLabel: "PolicyNumberBottomSheet__ChangiAssureButton",
            testID: "PolicyNumberBottomSheet__ChangiAssureButton",
            children: (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.LinearGradient, {
              style: styles.changiAssureButton,
              start: {
                x: 0,
                y: 1
              },
              end: {
                x: 1,
                y: 0
              },
              colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                style: styles.changiAssureButtonText,
                tx: "insuranceOrderDetailsScreen.goToChangiAssure"
              })
            })
          })]
        })]
      })
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    container: {
      height: BOTTOM_SHEET_HEIGHT,
      backgroundColor: _theme.color.palette.whiteGrey,
      borderTopRightRadius: 16,
      borderTopLeftRadius: 16
    },
    content: {
      flex: 1,
      paddingTop: 32
    },
    policyNumber: Object.assign({}, _text2.newPresets.bodyTextBold, {
      fontSize: 24,
      lineHeight: 28,
      alignSelf: "center",
      color: _theme.color.palette.almostBlackGrey,
      marginBottom: 8
    }),
    policyNumberLabel: Object.assign({}, _text2.newPresets.bodyTextRegular, {
      fontSize: 14,
      lineHeight: 18,
      alignSelf: "center",
      color: _theme.color.palette.darkestGrey,
      marginBottom: 8
    }),
    copiedBadge: {
      alignSelf: "center",
      flexDirection: "row",
      gap: 2,
      borderWidth: 1,
      borderColor: "#D5BBEA",
      backgroundColor: "#ECE0F5",
      height: 30,
      paddingHorizontal: 10,
      alignItems: "center",
      justifyContent: "center",
      borderRadius: 999,
      marginBottom: 8
    },
    copiedText: Object.assign({}, _text2.newPresets.bodyTextBold, {
      fontSize: 14,
      lineHeight: 18,
      color: _theme.color.palette.lightPurple
    }),
    description: Object.assign({}, _text2.newPresets.bodyTextRegular, {
      fontSize: 14,
      lineHeight: 18,
      color: _theme.color.palette.darkestGrey,
      textAlign: "center",
      marginBottom: 24,
      marginHorizontal: 24
    }),
    buttonContainer: {
      borderTopWidth: 1,
      borderColor: _theme.color.palette.lighterGrey,
      paddingTop: 15,
      paddingBottom: 24,
      paddingHorizontal: 24,
      flexDirection: "row",
      gap: 12
    },
    closeButtonBottom: {
      height: 44,
      paddingHorizontal: 16,
      justifyContent: "center",
      alignItems: "center",
      borderRadius: 999,
      borderWidth: 2,
      borderColor: _theme.color.palette.lightPurple
    },
    closeButtonText: Object.assign({}, _text2.newPresets.bodyTextBold, {
      fontSize: 16,
      lineHeight: 24,
      color: _theme.color.palette.lightPurple
    }),
    changiAssureButton: {
      flex: 1,
      height: 44,
      justifyContent: "center",
      alignItems: "center",
      borderRadius: 999
    },
    changiAssureButtonText: Object.assign({}, _text2.newPresets.bodyTextBold, {
      fontSize: 16,
      lineHeight: 24,
      color: _theme.color.palette.whiteGrey
    })
  });
