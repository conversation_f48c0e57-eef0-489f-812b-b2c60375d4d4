  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _native = _$$_REQUIRE(_dependencyMap[9]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[11]);
  var _i18n = _$$_REQUIRE(_dependencyMap[12]);
  var _text = _$$_REQUIRE(_dependencyMap[13]);
  var _icons = _$$_REQUIRE(_dependencyMap[14]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[15]);
  var _adobe = _$$_REQUIRE(_dependencyMap[16]);
  var _constants = _$$_REQUIRE(_dependencyMap[17]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[18]));
  var _dateTime = _$$_REQUIRE(_dependencyMap[19]);
  var _walletRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[20]));
  var _profileRedux = _$$_REQUIRE(_dependencyMap[21]);
  var _errorOverlay = _$$_REQUIRE(_dependencyMap[22]);
  var _loadingOrderDetails = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[23]));
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[24]);
  var _bottomSheetUnableLoadLocation = _$$_REQUIRE(_dependencyMap[25]);
  var _orderDetails = _$$_REQUIRE(_dependencyMap[26]);
  var _utils = _$$_REQUIRE(_dependencyMap[27]);
  var _focusStatusBar = _$$_REQUIRE(_dependencyMap[28]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[29]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[30]);
  var _theme = _$$_REQUIRE(_dependencyMap[31]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[32]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var LINEAR_GRADIENT = {
    "#6C2B64": "#6C2B64",
    "#AD3253": "#AD3253"
  };
  var CHECKOUT_TYPES = {
    ARRIVAL: "ARRIVAL",
    LANDSIDE: "LANDSIDE",
    DEPARTURE: "DEPARTURE",
    SG_DELIVERY: "SG DELIVERY",
    HOME_DELIVERY_TRAVELLER: "HOMEDELIVERYTRAVELLER",
    HOME_DELIVERY_NON_TRAVELLER: "HOMEDELIVERYNONTRAVELLER"
  };
  var linkContactUs = "https://www.ishopchangi.com/en/support/contact-us";
  var handleOrderDate = function handleOrderDate(date) {
    return (0, _moment.default)(date, _dateTime.DateFormats.YearMonthDay).format(_dateTime.DateFormats.DayMonthYear);
  };
  var onLayoutCardItem = function onLayoutCardItem(event, consignmentCode, consignmentLayouts, setConsignmentLayouts) {
    var _event$nativeEvent;
    var updatedConsignmentLayouts = [].concat((0, _toConsumableArray2.default)(consignmentLayouts), [{
      consignmentCode: consignmentCode,
      layoutInfo: event == null || (_event$nativeEvent = event.nativeEvent) == null ? undefined : _event$nativeEvent.layout
    }]);
    setConsignmentLayouts(updatedConsignmentLayouts);
  };
  var HeaderCard = function HeaderCard(_ref) {
    var orderItem = _ref.orderItem,
      orderDetailData = _ref.orderDetailData;
    var _ref2 = orderItem || {},
      title = _ref2.title,
      order_date = _ref2.order_date,
      image_url = _ref2.image_url;
    var _ref3 = orderDetailData || {},
      totalPrice = _ref3.totalPrice,
      transactionNo = _ref3.transactionNo,
      paymentMethod = _ref3.paymentMethod;
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: _orderDetails.styles.cardWrapper,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _orderDetails.styles.cardContentWrapper,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _orderDetails.styles.headerCardTitleWrapper,
          children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
            source: {
              uri: image_url
            },
            style: _orderDetails.styles.headerCardTitleIcon
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _orderDetails.styles.headerCardRight,
            children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _orderDetails.styles.headerCardTitle,
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                style: _orderDetails.styles.headerCardTitleText,
                children: title
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                style: _orderDetails.styles.headerCardTitleText,
                children: totalPrice
              })]
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              style: _orderDetails.styles.headerCardOrderId,
              children: transactionNo
            })]
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _orderDetails.styles.headerCardOrderInfo,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _orderDetails.styles.headerCardOrderInfoItem,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: _orderDetails.styles.orderInfoTitle,
              tx: "orderDetailPage.orderDetailCard.orderDateLabel"
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              style: _orderDetails.styles.orderInfoDetail,
              children: handleOrderDate(order_date)
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _orderDetails.styles.headerCardOrderInfoItem,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: _orderDetails.styles.orderInfoTitle,
              tx: "orderDetailPage.orderDetailCard.paymentMethodLabel"
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              style: _orderDetails.styles.orderInfoDetail,
              children: paymentMethod
            })]
          })]
        })]
      })
    });
  };
  var CollapsibleView = function CollapsibleView(_ref4) {
    var children = _ref4.children,
      isCollapsed = _ref4.isCollapsed,
      _ref4$durationTime = _ref4.durationTime,
      durationTime = _ref4$durationTime === undefined ? 150 : _ref4$durationTime,
      _ref4$maxHeight = _ref4.maxHeight,
      maxHeight = _ref4$maxHeight === undefined ? 1000 : _ref4$maxHeight;
    var _useState = (0, _react.useState)(new _reactNative2.Animated.Value(0)),
      _useState2 = (0, _slicedToArray2.default)(_useState, 1),
      animation = _useState2[0];
    var heightInterpolate = animation.interpolate({
      inputRange: [0, 1],
      outputRange: [0, maxHeight]
    });
    (0, _react.useEffect)(function () {
      if (isCollapsed) {
        _reactNative2.Animated.timing(animation, {
          toValue: 1,
          duration: durationTime,
          useNativeDriver: false
        }).start();
      } else {
        _reactNative2.Animated.timing(animation, {
          toValue: 0,
          duration: durationTime,
          useNativeDriver: false
        }).start();
      }
    }, [isCollapsed]);
    return (0, _jsxRuntime.jsx)(_reactNative2.Animated.View, {
      style: {
        maxHeight: heightInterpolate,
        overflow: "hidden"
      },
      children: children
    });
  };
  var OrderedDetailItem = function OrderedDetailItem(props) {
    var orderedDetailItem = props.orderedDetailItem;
    var _ref5 = orderedDetailItem || {},
      variant = _ref5.variant,
      quantity = _ref5.quantity,
      basePrice = _ref5.basePrice,
      brandName = _ref5.brandName,
      totalPrice = _ref5.totalPrice,
      productName = _ref5.productName;
    var productNamePrefix = basePrice === "S$0.00" ? (0, _jsxRuntime.jsx)(_text.Text, {
      tx: "orderDetailPage.consignMentList.freePrefix",
      style: _orderDetails.styles.orderedDetailItemNamePrefix
    }) : null;
    var renderVariantShowText = variant != null && variant.measureType ? (0, _jsxRuntime.jsxs)(_text.Text, {
      style: _orderDetails.styles.flightOrderedDetailItemDetailNormal,
      children: [variant == null ? undefined : variant.measureType, ": ", variant == null ? undefined : variant.qualifier, variant == null ? undefined : variant.measureUnit]
    }) : null;
    var freeText = (0, _i18n.translate)("orderDetailPage.consignMentList.freeText");
    var quantityLabel = (0, _i18n.translate)("orderDetailPage.consignMentList.quantityLabel");
    var itemPriceLabel = (0, _i18n.translate)("orderDetailPage.consignMentList.itemPriceLabel");
    var basePriceShowText = basePrice === "S$0.00" ? freeText : basePrice;
    var totalPriceShowText = totalPrice === "S$0.00" ? freeText : totalPrice;
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _orderDetails.styles.cardListElementWrapper,
      children: [!!brandName && (0, _jsxRuntime.jsx)(_text.Text, {
        style: _orderDetails.styles.flightOrderedDetailItemUppercaseTitle,
        children: brandName
      }), (0, _jsxRuntime.jsxs)(_text.Text, {
        style: _orderDetails.styles.flightOrderedDetailItemTitle,
        children: [productNamePrefix, productName]
      }), renderVariantShowText, (0, _jsxRuntime.jsxs)(_text.Text, {
        style: _orderDetails.styles.flightOrderedDetailItemDetailNormal,
        children: [quantityLabel, ": ", quantity]
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _orderDetails.styles.flightOrderedDetailItemSubTotal,
        children: [(0, _jsxRuntime.jsxs)(_text.Text, {
          style: _orderDetails.styles.flightOrderedDetailItemDetailNormal,
          children: [itemPriceLabel, ": ", basePriceShowText]
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: _orderDetails.styles.flightOrderedDetailItemDetailBold,
          children: totalPriceShowText
        })]
      })]
    });
  };
  var DepartureArrivalCard = function DepartureArrivalCard(props) {
    var isArrivalData = props.isArrivalData,
      consignmentItem = props.consignmentItem,
      consignmentLayouts = props.consignmentLayouts,
      setConsignmentLayouts = props.setConsignmentLayouts,
      onPressLocationButton = props.onPressLocationButton,
      initShouldShowDetailContent = props.shouldShowDetailContent;
    var _ref6 = consignmentItem || {},
      title = _ref6.title,
      subTotal = _ref6.subTotal,
      totalItems = _ref6.totalItems,
      statusDisplay = _ref6.statusDisplay,
      consignmentCode = _ref6.consignmentCode,
      collectionDetails = _ref6.collectionDetails,
      products = _ref6.products;
    var _ref7 = statusDisplay || {},
      statusDisplayText = _ref7.text,
      statusDisplayColor = _ref7.color;
    var _useState3 = (0, _react.useState)(initShouldShowDetailContent),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      shouldShowDetailContent = _useState4[0],
      setShouldShowDetailContent = _useState4[1];
    var orderNumberLabel = (0, _i18n.translate)("orderDetailPage.consignMentList.orderNoLabel");
    var cardWrapperStyle = [_orderDetails.styles.cardWrapper, {
      paddingBottom: shouldShowDetailContent ? 24 : 10
    }];
    var statusDisplayTextStyle = [_orderDetails.styles.cardTitleDetailLabel, {
      color: statusDisplayColor
    }];
    var renderToggleIcon = shouldShowDetailContent ? (0, _jsxRuntime.jsx)(_icons.TopArrow, {}) : (0, _jsxRuntime.jsx)(_icons.ArrowDown, {
      color: _theme.color.palette.lightPurple
    });
    var titleFlightDirectionIcon = isArrivalData ? (0, _jsxRuntime.jsx)(_icons.PlaneOutlineArrivalGray, {
      style: _orderDetails.styles.cardTitleIcon
    }) : (0, _jsxRuntime.jsx)(_icons.PlaneOutlineDepartureGray, {
      style: _orderDetails.styles.cardTitleIcon
    });
    var onPressToggleButton = function onPressToggleButton() {
      setShouldShowDetailContent(!shouldShowDetailContent);
    };
    var renderCardContent = (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _orderDetails.styles.cardContentWrapper,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _orderDetails.styles.cardOrderInfo,
        children: [(0, _jsxRuntime.jsxs)(_text.Text, {
          style: _orderDetails.styles.cardNormalText,
          children: [orderNumberLabel, (0, _jsxRuntime.jsx)(_text.Text, {
            style: _orderDetails.styles.cardOrderId,
            children: consignmentCode
          })]
        }), (0, _jsxRuntime.jsxs)(_text.Text, {
          style: _orderDetails.styles.cardNormalText,
          children: [totalItems || 0, " ", (0, _i18n.translate)("orderDetailPage.consignMentList.itemUnit")]
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _orderDetails.styles.cardOrderInfoSubTotal,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: _orderDetails.styles.cardOrderInfoTotal,
          children: (0, _i18n.translate)("orderDetailPage.consignMentList.subTotalLabel")
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: _orderDetails.styles.cardOrderInfoTotal,
          children: subTotal
        })]
      }), products == null ? undefined : products.map(function (productItem, i) {
        return (0, _jsxRuntime.jsx)(OrderedDetailItem, {
          orderedDetailItem: productItem
        }, `${productItem == null ? undefined : productItem.productName}-${i}`);
      })]
    });
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: cardWrapperStyle,
      onLayout: function onLayout(e) {
        return onLayoutCardItem(e, consignmentCode, consignmentLayouts, setConsignmentLayouts);
      },
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _orderDetails.styles.cardTitle,
        children: [titleFlightDirectionIcon, (0, _jsxRuntime.jsx)(_text.Text, {
          text: title,
          style: _orderDetails.styles.cardTitleLabel
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: _orderDetails.styles.cardToggleIcon,
          onPress: onPressToggleButton,
          children: renderToggleIcon
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _orderDetails.styles.cardTitleDetail,
        children: [!!statusDisplayText && (0, _jsxRuntime.jsx)(_text.Text, {
          style: statusDisplayTextStyle,
          children: statusDisplayText
        }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
          onPress: onPressLocationButton,
          style: _orderDetails.styles.flightCardTitleDetail,
          children: [(0, _jsxRuntime.jsx)(_icons.LocationFill, {
            width: 16,
            height: 16,
            style: _orderDetails.styles.flightCardTitleDetailIcon
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: _orderDetails.styles.flightCardTitleDetailLocation,
            children: collectionDetails == null ? undefined : collectionDetails.display
          })]
        })]
      }), (0, _jsxRuntime.jsx)(CollapsibleView, {
        isCollapsed: shouldShowDetailContent,
        maxHeight: 200 + (products == null ? undefined : products.length) * 250,
        children: renderCardContent
      })]
    });
  };
  var CommonDeliverCard = function CommonDeliverCard(props) {
    var _deliveryAddress$coun;
    var consignmentItem = props.consignmentItem,
      consignmentLayouts = props.consignmentLayouts,
      setConsignmentLayouts = props.setConsignmentLayouts,
      initShouldShowDetailContent = props.shouldShowDetailContent;
    var navigation = (0, _native.useNavigation)();
    var _useState5 = (0, _react.useState)(initShouldShowDetailContent),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      shouldShowDetailContent = _useState6[0],
      setShouldShowDetailContent = _useState6[1];
    var _ref8 = consignmentItem || {},
      title = _ref8.title,
      subTotal = _ref8.subTotal,
      totalItems = _ref8.totalItems,
      statusDisplay = _ref8.statusDisplay,
      consignmentCode = _ref8.consignmentCode,
      products = _ref8.products,
      deliveryDetails = _ref8.deliveryDetails;
    var _ref9 = deliveryDetails || {},
      deliveryAddress = _ref9.deliveryAddress;
    var _ref0 = statusDisplay || {},
      statusDisplayText = _ref0.text,
      statusDisplayColor = _ref0.color;
    var cardWrapperStyle = [_orderDetails.styles.cardWrapper, {
      paddingBottom: shouldShowDetailContent ? 24 : 10
    }];
    var statusDisplayTextStyle = [_orderDetails.styles.cardTitleDetailLabel, {
      color: statusDisplayColor
    }];
    var trackingUrl = deliveryDetails == null ? undefined : deliveryDetails.trackingURL;
    var onPressTrackShipmentButton = function onPressTrackShipmentButton() {
      navigation.navigate(_constants.NavigationConstants.webview, {
        uri: trackingUrl
      });
    };
    var renderCardContent = (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _orderDetails.styles.cardContentWrapper,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _orderDetails.styles.cardOrderInfo,
        children: [(0, _jsxRuntime.jsxs)(_text.Text, {
          style: _orderDetails.styles.cardNormalText,
          children: [(0, _i18n.translate)("orderDetailPage.consignMentList.orderNoLabel"), (0, _jsxRuntime.jsx)(_text.Text, {
            style: _orderDetails.styles.cardOrderId,
            children: consignmentCode
          })]
        }), (0, _jsxRuntime.jsxs)(_text.Text, {
          style: _orderDetails.styles.cardNormalText,
          children: [totalItems, " ", (0, _i18n.translate)("orderDetailPage.consignMentList.itemUnit")]
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _orderDetails.styles.cardOrderInfoSubTotal,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: _orderDetails.styles.cardOrderInfoTotal,
          children: (0, _i18n.translate)("orderDetailPage.consignMentList.subTotalLabel")
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: _orderDetails.styles.cardOrderInfoTotal,
          children: subTotal
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _orderDetails.styles.cardListElementWrapper,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _orderDetails.styles.deliverCardTrackShipmentWrapper,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: _orderDetails.styles.cardBoldText,
            children: (0, _i18n.translate)("orderDetailPage.consignMentList.deliveryMethodLabel")
          }), !!trackingUrl && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: onPressTrackShipmentButton,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              style: _orderDetails.styles.deliverCardTrackShipmentLabel,
              children: (0, _i18n.translate)("orderDetailPage.consignMentList.trackShipment")
            })
          })]
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: _orderDetails.styles.deliverCardSubTitleDetail,
          children: deliveryDetails == null ? undefined : deliveryDetails.deliveryMethod
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _orderDetails.styles.deliverCardSubTitleWrapper,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: _orderDetails.styles.cardBoldText,
            children: (0, _i18n.translate)("orderDetailPage.consignMentList.deliveryDateTimeLabel")
          }), (0, _jsxRuntime.jsxs)(_text.Text, {
            style: _orderDetails.styles.deliverCardSubTitleDetail,
            children: [handleOrderDate(deliveryDetails == null ? undefined : deliveryDetails.deliveryDate), !!(deliveryDetails != null && deliveryDetails.deliveryTime) && `, ${deliveryDetails == null ? undefined : deliveryDetails.deliveryTime}`]
          })]
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: _orderDetails.styles.cardBoldText,
          children: (0, _i18n.translate)("orderDetailPage.consignMentList.deliveryAddressLabel")
        }), (0, _jsxRuntime.jsxs)(_text.Text, {
          style: _orderDetails.styles.deliverCardSubTitleDetail,
          children: [deliveryAddress == null ? undefined : deliveryAddress.firstName, " ", deliveryAddress == null ? undefined : deliveryAddress.lastName]
        }), (0, _jsxRuntime.jsxs)(_text.Text, {
          style: _orderDetails.styles.deliverCardSubTitleDetail,
          children: [deliveryAddress == null ? undefined : deliveryAddress.countryISDCode, " ", deliveryAddress == null ? undefined : deliveryAddress.phone]
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: _orderDetails.styles.deliverCardSubTitleDetail,
          children: deliveryAddress == null ? undefined : deliveryAddress.formattedAddress
        }), !!(deliveryAddress != null && deliveryAddress.unit) && (0, _jsxRuntime.jsx)(_text.Text, {
          style: _orderDetails.styles.deliverCardSubTitleDetail,
          children: deliveryAddress == null ? undefined : deliveryAddress.unit
        }), (0, _jsxRuntime.jsxs)(_text.Text, {
          style: _orderDetails.styles.deliverCardSubTitleDetail,
          children: [deliveryAddress == null || (_deliveryAddress$coun = deliveryAddress.country) == null ? undefined : _deliveryAddress$coun.name, " ", deliveryAddress == null ? undefined : deliveryAddress.postalCode]
        })]
      }), products == null ? undefined : products.map(function (productItem, i) {
        return (0, _jsxRuntime.jsx)(OrderedDetailItem, {
          orderedDetailItem: productItem
        }, `${productItem == null ? undefined : productItem.productName}-${i}`);
      })]
    });
    var renderToggleIcon = shouldShowDetailContent ? (0, _jsxRuntime.jsx)(_icons.TopArrow, {}) : (0, _jsxRuntime.jsx)(_icons.ArrowDown, {
      color: _theme.color.palette.lightPurple
    });
    var onPressToggleButton = function onPressToggleButton() {
      setShouldShowDetailContent(!shouldShowDetailContent);
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: cardWrapperStyle,
      onLayout: function onLayout(e) {
        return onLayoutCardItem(e, consignmentCode, consignmentLayouts, setConsignmentLayouts);
      },
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _orderDetails.styles.cardTitle,
        children: [(0, _jsxRuntime.jsx)(_icons.DeliveryOutlineGray, {
          style: _orderDetails.styles.cardTitleIcon
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          text: title,
          style: _orderDetails.styles.cardTitleLabel
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: _orderDetails.styles.cardToggleIcon,
          onPress: onPressToggleButton,
          children: renderToggleIcon
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _orderDetails.styles.cardTitleDetail,
        children: [!!statusDisplayText && (0, _jsxRuntime.jsx)(_text.Text, {
          style: statusDisplayTextStyle,
          children: statusDisplayText
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: _orderDetails.styles.deliverCardTitleDetail,
          children: (0, _jsxRuntime.jsxs)(_text.Text, {
            style: _orderDetails.styles.cardTitleDetailLabel,
            children: [(0, _i18n.translate)("orderDetailPage.consignMentList.shippedBy"), deliveryDetails == null ? undefined : deliveryDetails.shippedBy]
          })
        })]
      }), (0, _jsxRuntime.jsx)(CollapsibleView, {
        durationTime: 500,
        isCollapsed: shouldShowDetailContent,
        maxHeight: 500 + (products == null ? undefined : products.length) * 250,
        children: renderCardContent
      })]
    });
  };
  var LandSideCard = function LandSideCard(props) {
    var consignmentItem = props.consignmentItem,
      consignmentLayouts = props.consignmentLayouts,
      setConsignmentLayouts = props.setConsignmentLayouts,
      onPressLocationButton = props.onPressLocationButton,
      initShouldShowDetailContent = props.shouldShowDetailContent;
    var _useState7 = (0, _react.useState)(initShouldShowDetailContent),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      shouldShowDetailContent = _useState8[0],
      setShouldShowDetailContent = _useState8[1];
    var _ref1 = consignmentItem || {},
      title = _ref1.title,
      subTotal = _ref1.subTotal,
      totalItems = _ref1.totalItems,
      statusDisplay = _ref1.statusDisplay,
      consignmentCode = _ref1.consignmentCode,
      landsideCollectionDetails = _ref1.landsideCollectionDetails,
      products = _ref1.products;
    var _ref10 = statusDisplay || {},
      statusDisplayText = _ref10.text,
      statusDisplayColor = _ref10.color;
    var _ref11 = landsideCollectionDetails || {},
      deliveryDate = _ref11.deliveryDate,
      deliveryTime = _ref11.deliveryTime,
      collectionAddress = _ref11.collectionAddress,
      contactNumber = _ref11.contactNumber;
    var cardWrapperStyle = [_orderDetails.styles.cardWrapper, {
      paddingBottom: shouldShowDetailContent ? 24 : 10
    }];
    var statusDisplayTextStyle = [_orderDetails.styles.cardTitleDetailLabel, {
      color: statusDisplayColor
    }];
    var renderCardContent = (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _orderDetails.styles.cardContentWrapper,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _orderDetails.styles.cardOrderInfo,
        children: [(0, _jsxRuntime.jsxs)(_text.Text, {
          style: _orderDetails.styles.cardNormalText,
          children: [(0, _i18n.translate)("orderDetailPage.consignMentList.orderNoLabel"), (0, _jsxRuntime.jsx)(_text.Text, {
            style: _orderDetails.styles.cardOrderId,
            children: consignmentCode
          })]
        }), (0, _jsxRuntime.jsxs)(_text.Text, {
          style: _orderDetails.styles.cardNormalText,
          children: [totalItems, " ", (0, _i18n.translate)("orderDetailPage.consignMentList.itemUnit")]
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _orderDetails.styles.cardOrderInfoSubTotal,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: _orderDetails.styles.cardOrderInfoTotal,
          tx: "orderDetailPage.consignMentList.subTotalLabel"
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: _orderDetails.styles.cardOrderInfoTotal,
          children: subTotal
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _orderDetails.styles.cardListElementWrapper,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: _orderDetails.styles.cardBoldText,
          tx: "orderDetailPage.consignMentList.collectionMethodLabel"
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: _orderDetails.styles.deliverCardSubTitleDetail,
          children: title
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _orderDetails.styles.deliverCardSubTitleWrapper,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: _orderDetails.styles.cardBoldText,
            tx: "orderDetailPage.consignMentList.collectionDateTimeLabel"
          }), (0, _jsxRuntime.jsxs)(_text.Text, {
            style: _orderDetails.styles.deliverCardSubTitleDetail,
            children: [deliveryDate, ", ", deliveryTime]
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: [_orderDetails.styles.deliverCardSubTitleWrapper, {
            marginTop: 0
          }],
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: [_orderDetails.styles.cardBoldText],
            tx: "orderDetailPage.consignMentList.collectionAddressLabel"
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: _orderDetails.styles.deliverCardSubTitleDetail,
            children: collectionAddress == null ? undefined : collectionAddress.formattedAddress
          })]
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: _orderDetails.styles.cardBoldText,
          tx: "orderDetailPage.consignMentList.collectionNumberLabel"
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: _orderDetails.styles.deliverCardSubTitleDetail,
          children: contactNumber
        })]
      }), products == null ? undefined : products.map(function (productItem, i) {
        return (0, _jsxRuntime.jsx)(OrderedDetailItem, {
          orderedDetailItem: productItem
        }, `${productItem == null ? undefined : productItem.productName}-${i}`);
      })]
    });
    var renderToggleIcon = shouldShowDetailContent ? (0, _jsxRuntime.jsx)(_icons.TopArrow, {}) : (0, _jsxRuntime.jsx)(_icons.ArrowDown, {
      color: _theme.color.palette.lightPurple
    });
    var onPressToggleButton = function onPressToggleButton() {
      setShouldShowDetailContent(!shouldShowDetailContent);
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: cardWrapperStyle,
      onLayout: function onLayout(e) {
        return onLayoutCardItem(e, consignmentCode, consignmentLayouts, setConsignmentLayouts);
      },
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _orderDetails.styles.cardTitle,
        children: [(0, _jsxRuntime.jsx)(_icons.ColectionJewelGray, {
          style: _orderDetails.styles.cardTitleIcon
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          text: title,
          style: _orderDetails.styles.cardTitleLabel
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: _orderDetails.styles.cardToggleIcon,
          onPress: onPressToggleButton,
          children: renderToggleIcon
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _orderDetails.styles.cardTitleDetail,
        children: [!!statusDisplayText && (0, _jsxRuntime.jsx)(_text.Text, {
          style: statusDisplayTextStyle,
          children: statusDisplayText
        }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
          style: _orderDetails.styles.flightCardTitleDetail,
          onPress: onPressLocationButton,
          children: [(0, _jsxRuntime.jsx)(_icons.LocationFill, {
            width: 16,
            height: 16,
            style: _orderDetails.styles.flightCardTitleDetailIcon
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: _orderDetails.styles.flightCardTitleDetailLocation,
            children: landsideCollectionDetails == null ? undefined : landsideCollectionDetails.terminalCode
          })]
        })]
      }), (0, _jsxRuntime.jsx)(CollapsibleView, {
        durationTime: 500,
        isCollapsed: shouldShowDetailContent,
        maxHeight: 500 + (products == null ? undefined : products.length) * 250,
        children: renderCardContent
      })]
    });
  };
  var OrderTotalCard = function OrderTotalCard(props) {
    var orderDetailData = props.orderDetailData;
    var _ref12 = orderDetailData || {},
      deliveryCost = _ref12.deliveryCost,
      earnedRewardPoints = _ref12.earnedRewardPoints,
      netTotal = _ref12.netTotal,
      redeemedPointsFormatted = _ref12.redeemedPointsFormatted,
      redeemedValues = _ref12.redeemedValues,
      subTotal = _ref12.subTotal,
      totalItems = _ref12.totalItems,
      totalPrice = _ref12.totalPrice,
      voucherCode = _ref12.voucherCode,
      voucherDiscounts = _ref12.voucherDiscounts;
    var isShowPromotion = !!voucherCode && voucherCode !== "-";
    var isValidRedeemedPoints = !!redeemedPointsFormatted && redeemedPointsFormatted !== "0";
    var isValidRedeemedValues = !!redeemedValues && redeemedValues !== "S$0.00" && redeemedValues !== "-";
    var isShowRedeem = isValidRedeemedPoints && isValidRedeemedValues;
    var orderTotalMainStyle = Object.assign({}, _orderDetails.styles.orderTotalMain, {
      marginBottom: !isShowPromotion && !isShowRedeem ? 0 : 16
    });
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        style: _orderDetails.styles.cardSectionTitle,
        tx: "orderDetailPage.orderTotal.orderTotalLabel"
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _orderDetails.styles.cardWrapper,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _orderDetails.styles.orderTotalWrapper,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _orderDetails.styles.orderTotalTitle,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: _orderDetails.styles.orderTotalTitleBold,
              tx: "orderDetailPage.orderTotal.orderSummaryLabel"
            }), (0, _jsxRuntime.jsxs)(_text.Text, {
              style: _orderDetails.styles.orderTotalTitleNormal,
              children: [totalItems, " ", (0, _i18n.translate)("orderDetailPage.consignMentList.itemUnit")]
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _orderDetails.styles.cardListElementWrapper,
            children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: orderTotalMainStyle,
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                style: _orderDetails.styles.orderTotalTitleBold,
                tx: "orderDetailPage.consignMentList.subTotalLabel"
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                style: _orderDetails.styles.orderTotalTitleBold,
                children: subTotal
              })]
            }), isShowPromotion && (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _orderDetails.styles.orderTotalDetail,
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                style: _orderDetails.styles.orderTotalDetailNormal,
                children: (0, _i18n.translate)("orderDetailsScreen.totalCardPromoTitle", {
                  code: voucherCode == null || voucherCode.toUpperCase == null ? undefined : voucherCode.toUpperCase()
                })
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                style: _orderDetails.styles.orderTotalDetailBold,
                text: `-${voucherDiscounts}`
              })]
            }), isShowRedeem && (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _orderDetails.styles.orderTotalDetail,
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                style: _orderDetails.styles.orderTotalDetailNormal,
                children: (0, _i18n.translate)("orderDetailsScreen.totalCardRedeemLabel", {
                  value: redeemedPointsFormatted
                })
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                style: _orderDetails.styles.orderTotalDetailBold,
                text: `-${redeemedValues}`
              })]
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _orderDetails.styles.cardListElementWrapper,
            children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: [_orderDetails.styles.orderTotalDetail, {
                marginTop: 0
              }],
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                style: _orderDetails.styles.orderTotalDetailNormal,
                tx: "orderDetailPage.orderTotal.netTotalLabel"
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                style: _orderDetails.styles.orderTotalDetailBold,
                text: netTotal
              })]
            }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _orderDetails.styles.orderTotalDetail,
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                style: _orderDetails.styles.orderTotalDetailNormal,
                tx: "orderDetailPage.orderTotal.shippingLabel"
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                style: _orderDetails.styles.orderTotalDetailBold,
                text: `+${deliveryCost}`
              })]
            })]
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _orderDetails.styles.cardListElementWrapper,
            children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _orderDetails.styles.orderTotalMain,
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                style: _orderDetails.styles.orderTotalTitleBold,
                tx: "orderDetailPage.orderTotal.amountPayableLabel"
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                style: _orderDetails.styles.orderTotalTitleBold,
                text: totalPrice
              })]
            })
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _orderDetails.styles.changiRewardsPointsEarned,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: _orderDetails.styles.changiRewardsPointsEarnedLeft,
              tx: "orderDetailPage.orderTotal.estimatedEarnedChangiRewardsPoints"
            }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _orderDetails.styles.changiRewardsPointsEarnedRight,
              children: [(0, _jsxRuntime.jsx)(_icons.ButterflyCrIcon, {
                style: _orderDetails.styles.changiRewardsPointsEarnedIcon
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                style: _orderDetails.styles.orderTotalDetailBold,
                text: `+${(0, _utils.toLocaleNumber)(earnedRewardPoints)} points`
              })]
            })]
          })]
        })
      })]
    });
  };
  var OrderDetails = function OrderDetails(props) {
    var _orderDetailData$cons;
    var navigation = props.navigation,
      route = props.route;
    var _ref13 = (route == null ? undefined : route.params) || {},
      orderItem = _ref13.orderItem,
      consignment = _ref13.consignment,
      onBackScreen = _ref13.onBackScreen,
      disableGesture = _ref13.disableGesture;
    var _ref14 = orderItem || {},
      orderNumber = _ref14.transaction_number,
      consignments = _ref14.consignments;
    var dispatch = (0, _reactRedux.useDispatch)();
    var scrollRef = (0, _react.useRef)(null);
    var inset = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    var unableToLoadLocationRef = (0, _react.useRef)(null);
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var profileUser = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var orderDetailData = (0, _reactRedux.useSelector)(_walletRedux.WalletSelectors.walletOrderDetailsData);
    var orderDetailsFetching = (0, _reactRedux.useSelector)(_walletRedux.WalletSelectors.walletOrderDetailsFetching);
    var orderDetailsError = (0, _reactRedux.useSelector)(_walletRedux.WalletSelectors.walletOrderDetailsError);
    var _useState9 = (0, _react.useState)([]),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      consignmentLayouts = _useState0[0],
      setConsignmentLayouts = _useState0[1];
    var _useState1 = (0, _react.useState)(false),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      isShowNoInternetModel = _useState10[0],
      setIsShowNoInternetModel = _useState10[1];
    var _useState11 = (0, _react.useState)(false),
      _useState12 = (0, _slicedToArray2.default)(_useState11, 2),
      isScrolledToConsigment = _useState12[0],
      setIsScrolledToConsigment = _useState12[1];
    var navigationTitle = (0, _i18n.translate)("orderDetailsScreen.title", {
      orderId: orderNumber || "###"
    });
    var renderErrorCustomBackButton = (0, _jsxRuntime.jsx)(_icons.ArrowLeftGray, {
      style: _orderDetails.styles.errorCustomBackButton
    });
    var onPressLocationButton = function onPressLocationButton() {
      var locationEndpoint = "https://www.ishopchangi.com/en/support/faq/collection-and-delivery";
      var headerContainerStyle = Object.assign({}, _reactNative2.Platform.OS === "android" && {
        paddingTop: inset.top
      });
      var webviewOptions = {
        uri: locationEndpoint,
        headerContainerStyle: headerContainerStyle,
        statusBarTranslucent: true
      };
      navigation.navigate(_constants.NavigationConstants.webview, webviewOptions);
    };
    var renderOrderDetailCard = orderDetailData == null || (_orderDetailData$cons = orderDetailData.consignments) == null ? undefined : _orderDetailData$cons.map(function (item, i) {
      var consignmentItemCheckoutType = item == null ? undefined : item.checkoutType;
      var flightCheckoutTypes = [CHECKOUT_TYPES.ARRIVAL, CHECKOUT_TYPES.DEPARTURE];
      var deliveryCheckoutTypes = [CHECKOUT_TYPES.SG_DELIVERY, CHECKOUT_TYPES.HOME_DELIVERY_TRAVELLER, CHECKOUT_TYPES.HOME_DELIVERY_NON_TRAVELLER];
      if (flightCheckoutTypes.includes(consignmentItemCheckoutType)) {
        var isArrivalData = consignmentItemCheckoutType === CHECKOUT_TYPES.ARRIVAL;
        return (0, _jsxRuntime.jsx)(DepartureArrivalCard, {
          consignmentItem: item,
          isArrivalData: isArrivalData,
          consignmentLayouts: consignmentLayouts,
          setConsignmentLayouts: setConsignmentLayouts,
          onPressLocationButton: onPressLocationButton
        }, `${item == null ? undefined : item.consignmentCode}-${i}`);
      }
      if (deliveryCheckoutTypes.includes(consignmentItemCheckoutType)) {
        return (0, _jsxRuntime.jsx)(CommonDeliverCard, {
          consignmentItem: item,
          consignmentLayouts: consignmentLayouts,
          setConsignmentLayouts: setConsignmentLayouts
        }, `${item == null ? undefined : item.consignmentCode}-${i}`);
      }
      if (consignmentItemCheckoutType === CHECKOUT_TYPES.LANDSIDE) {
        return (0, _jsxRuntime.jsx)(LandSideCard, {
          consignmentItem: item,
          consignmentLayouts: consignmentLayouts,
          setConsignmentLayouts: setConsignmentLayouts,
          onPressLocationButton: onPressLocationButton
        }, `${item == null ? undefined : item.consignmentCode}-${i}`);
      }
    });
    var onPressContactButton = function onPressContactButton() {
      navigation.navigate(_constants.NavigationConstants.webview, {
        uri: linkContactUs
      });
    };
    var onRetryToRequestOrderDetails = function onRetryToRequestOrderDetails() {
      if (orderNumber && profileUser != null && profileUser.email) {
        dispatch(_walletRedux.default.walletOrderDetailsRequest({
          transactionNo: orderNumber,
          ocidEmail: profileUser == null ? undefined : profileUser.email
        }));
      }
    };
    var onPressRetryToConnectInternet = /*#__PURE__*/function () {
      var _ref15 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        if (isConnected) {
          onRetryToRequestOrderDetails();
          setIsShowNoInternetModel(false);
        }
      });
      return function onPressRetryToConnectInternet() {
        return _ref15.apply(this, arguments);
      };
    }();
    var renderOrderCardDetailContent = orderDetailsFetching ? (0, _jsxRuntime.jsx)(_loadingOrderDetails.default, {}) : (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _orderDetails.styles.bodyContent,
      children: [(0, _jsxRuntime.jsx)(HeaderCard, {
        orderItem: orderItem,
        orderDetailData: orderDetailData
      }), renderOrderDetailCard, (0, _jsxRuntime.jsx)(OrderTotalCard, {
        orderDetailData: orderDetailData
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        style: _orderDetails.styles.helpQuestion,
        tx: "orderDetailPage.helpTextLabel"
      }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        style: _orderDetails.styles.contactButton,
        onPress: onPressContactButton,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          style: _orderDetails.styles.contactButtonLabel,
          tx: "orderDetailPage.contactUs"
        })
      })]
    });
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)(`Order_Detail_${orderNumber}`);
    (0, _react.useEffect)(function () {
      var unsubscribe = navigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)(`Order_Detail_${orderNumber}`, (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribe;
    }, [navigation]);
    (0, _react.useEffect)(function () {
      ;
      (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch2.isConnected;
        if (!isConnected) {
          setIsShowNoInternetModel(true);
        } else {
          if (orderNumber && profileUser != null && profileUser.email) {
            dispatch(_walletRedux.default.walletOrderDetailsRequest({
              transactionNo: orderNumber,
              ocidEmail: profileUser == null ? undefined : profileUser.email,
              isGetOrderDetailsV2: true
            }));
          }
        }
      })();
      return function () {
        dispatch(_walletRedux.default.resetWalletOrderDetailsRequest());
      };
    }, [profileUser == null ? undefined : profileUser.email]);
    (0, _react.useEffect)(function () {
      var consignmentCode = (consignment == null ? undefined : consignment.consignmentCode) || (consignment == null ? undefined : consignment.consignment_code);
      var shouldInitScroll = consignmentCode && !isScrolledToConsigment && (consignmentLayouts == null ? undefined : consignmentLayouts.length) === (consignments == null ? undefined : consignments.length);
      if (shouldInitScroll) {
        var consignmentItemLayout = consignmentLayouts == null ? undefined : consignmentLayouts.find(function (i) {
          return (i == null ? undefined : i.consignmentCode) === consignmentCode;
        });
        if (consignmentItemLayout) {
          var _consignmentItemLayou;
          var consigmentY = (consignmentItemLayout == null || (_consignmentItemLayou = consignmentItemLayout.layoutInfo) == null ? undefined : _consignmentItemLayou.y) - 12 || 0;
          setTimeout(function () {
            var _scrollRef$current;
            setIsScrolledToConsigment(true);
            scrollRef == null || (_scrollRef$current = scrollRef.current) == null || _scrollRef$current.scrollTo({
              y: consigmentY,
              animated: true
            });
          }, 100);
        }
      }
    }, [consignment, consignmentLayouts == null ? undefined : consignmentLayouts.length]);
    (0, _react.useLayoutEffect)(function () {
      navigation.setOptions({
        title: navigationTitle
      });
    }, [navigation]);
    (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
      var onBackPress = function onBackPress() {
        onBackScreen();
        navigation.goBack();
        return true;
      };
      if (disableGesture) {
        navigation.setOptions({
          gestureEnabled: false
        });
      }
      var subscription = _reactNative2.BackHandler.addEventListener("hardwareBackPress", onBackPress);
      return function () {
        return subscription.remove();
      };
    }, []));
    if (orderDetailsError && !isShowNoInternetModel) {
      return (0, _jsxRuntime.jsx)(_errorOverlay.ErrorOverlay, {
        header: true,
        reload: true,
        visible: true,
        onBack: navigation.goBack,
        headerText: navigationTitle,
        variant: _errorOverlay.ErrorOverlayVariant.VARIANT2,
        onReload: onRetryToRequestOrderDetails,
        customBackButton: renderErrorCustomBackButton,
        extendCode: _errorOverlay.ERROR_HANDLING_CODE.ERROR_PAGE_LEVEL
      });
    }
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_focusStatusBar.FocusAwareStatusBar, {
        backgroundColor: "transparent",
        barStyle: "dark-content",
        translucent: true
      }), (0, _jsxRuntime.jsxs)(_reactNative2.ScrollView, {
        bounces: false,
        ref: scrollRef,
        style: _orderDetails.styles.wrapper,
        showsVerticalScrollIndicator: false,
        children: [(0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
          angle: 215.32,
          useAngle: true,
          locations: [0.2024, 0.9531],
          angleCenter: {
            x: 0.5,
            y: 0.5
          },
          style: _orderDetails.styles.headerBackgroundImage,
          colors: [LINEAR_GRADIENT["#6C2B64"], LINEAR_GRADIENT["#AD3253"]]
        }), renderOrderCardDetailContent]
      }), (0, _jsxRuntime.jsx)(_bottomSheetUnableLoadLocation.BottomSheetUnableLoadLocation, {
        ref: unableToLoadLocationRef
      }), (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        header: true,
        onBack: navigation.goBack,
        headerText: navigationTitle,
        visible: isShowNoInternetModel,
        onReload: onPressRetryToConnectInternet,
        customBackButton: renderErrorCustomBackButton,
        testID: `${_constants.NavigationConstants.orderDetailPage}__ErrorOverlayNoConnection`
      })]
    });
  };
  var _default = exports.default = OrderDetails;
