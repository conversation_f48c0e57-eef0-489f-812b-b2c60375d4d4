  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _bookingsOrders = _$$_REQUIRE(_dependencyMap[4]);
  var _ordersListingCard = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _constants = _$$_REQUIRE(_dependencyMap[6]);
  var _react = _$$_REQUIRE(_dependencyMap[7]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[9]);
  var _forYouRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[10]));
  var _ordersListingCard2 = _$$_REQUIRE(_dependencyMap[11]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _aemRedux = _$$_REQUIRE(_dependencyMap[13]);
  var _emptyScreen = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _bookingsListingCard = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _loadingBookingsOrders = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _native = _$$_REQUIRE(_dependencyMap[17]);
  var _focusStatusBar = _$$_REQUIRE(_dependencyMap[18]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[19]);
  var _adobe = _$$_REQUIRE(_dependencyMap[20]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[21]);
  var _insuranceBookingCard = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[22]));
  var _creditsListing = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[23]));
  var _emptyState = _$$_REQUIRE(_dependencyMap[24]);
  var _useBookingsOrdersCreditsInsurances = _$$_REQUIRE(_dependencyMap[25]);
  var _account = _$$_REQUIRE(_dependencyMap[26]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[27]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[28]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var BookingsOrdersScreen = function BookingsOrdersScreen(props) {
    var _useSelector, _playpass$data, _insurance$data;
    var navigation = props.navigation;
    var scrollRef = (0, _react.useRef)(null);
    var dispatch = (0, _reactRedux.useDispatch)();
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var dataBookingAndOrderCacheData = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.dataBookingAndOrderCacheData);
    var _useContext = (0, _react.useContext)(_account.AccountContext),
      vprIncludePPFlag = _useContext.vprIncludePPFlag;
    var errorMessage = (_useSelector = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon)) == null ? undefined : _useSelector.find(function (item) {
      return (item == null ? undefined : item.code) === "EHR11.8";
    });
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isNoConnection = _useState2[0],
      setIsNoConnection = _useState2[1];
    var isVprIncludePP = (0, _remoteConfig.getFeatureFlagInit)(_remoteConfig.REMOTE_CONFIG_FLAGS.VPR_INCL_PP, vprIncludePPFlag);
    var _useBookingsOrdersCre = (0, _useBookingsOrdersCreditsInsurances.useBookingsOrdersCreditsInsurances)(),
      credits = _useBookingsOrdersCre.credits,
      insurance = _useBookingsOrdersCre.insurance,
      playpass = _useBookingsOrdersCre.playpass,
      orders = _useBookingsOrdersCre.orders,
      loading = _useBookingsOrdersCre.loading,
      error = _useBookingsOrdersCre.error,
      empty = _useBookingsOrdersCre.empty,
      fetchBookingsOrdersCreditsInsurances = _useBookingsOrdersCre.fetchBookingsOrdersCreditsInsurances;
    var onBackScreen = function onBackScreen() {
      dispatch(_forYouRedux.default.dataBookingAndOrderCacheData(true));
    };
    var handlePressBooking = function handlePressBooking(item) {
      navigation.navigate(_constants.NavigationConstants.playPassBookingDetail, {
        bookingKey: item == null ? undefined : item.bookingKey,
        onBackScreen: onBackScreen,
        disableGesture: true
      });
    };
    var handlePressInsurance = function handlePressInsurance(item) {
      navigation.navigate(_constants.NavigationConstants.insuranceOrderDetailsScreen, {
        orderId: item == null ? undefined : item.orderId
      });
    };
    var handleOnPressOrderCard = (0, _react.useCallback)(function (_ref) {
      var consignment = _ref.consignment,
        orderItem = _ref.orderItem;
      navigation == null || navigation.navigate(_constants.NavigationConstants.orderDetailsScreen, {
        consignment: consignment,
        orderItem: orderItem,
        onBackScreen: onBackScreen,
        disableGesture: true
      });
    }, [navigation, onBackScreen]);
    var checkInternetConnection = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
        isConnected = _yield$NetInfo$fetch.isConnected;
      if (isConnected) {
        setIsNoConnection(false);
        fetchBookingsOrdersCreditsInsurances();
      }
    }), [fetchBookingsOrdersCreditsInsurances]);
    var onRetryToRequestBookingsOrders = (0, _react.useCallback)(function () {
      fetchBookingsOrdersCreditsInsurances();
    }, [fetchBookingsOrdersCreditsInsurances]);
    (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
      ;
      (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch2.isConnected;
        if (!isConnected) {
          setIsNoConnection(true);
          return;
        }
        if (!dataBookingAndOrderCacheData) {
          var _scrollRef$current;
          scrollRef == null || (_scrollRef$current = scrollRef.current) == null || _scrollRef$current.scrollTo == null || _scrollRef$current.scrollTo({
            x: 0,
            y: 0
          });
          fetchBookingsOrdersCreditsInsurances();
        }
      })();
      return function () {
        return dispatch(_forYouRedux.default.dataBookingAndOrderCacheData(false));
      };
    }, [profilePayload == null ? undefined : profilePayload.email, dataBookingAndOrderCacheData, fetchBookingsOrdersCreditsInsurances]));
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)(_constants.TrackingScreenName.BookingsOrders);
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)(_constants.TrackingScreenName.BookingsOrders, (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribeFocus;
    }, [navigation]);
    if (error) {
      return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsx)(_focusStatusBar.FocusAwareStatusBar, {
          backgroundColor: "transparent",
          barStyle: "dark-content",
          translucent: true
        }), (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _bookingsOrders.styles.apiErrorContainer,
          children: (0, _jsxRuntime.jsx)(_emptyState.EmptyState.ApiError, {
            onPressReload: onRetryToRequestBookingsOrders
          })
        })]
      });
    }
    if (isNoConnection) {
      return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsx)(_focusStatusBar.FocusAwareStatusBar, {
          backgroundColor: "transparent",
          barStyle: "dark-content",
          translucent: true
        }), (0, _jsxRuntime.jsx)(_emptyState.EmptyState.NoInternet, {
          containerStyle: _bookingsOrders.styles.errorContainer,
          onPressReload: checkInternetConnection
        })]
      });
    }
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _bookingsOrders.styles.containerStyle,
        children: [(0, _jsxRuntime.jsx)(_focusStatusBar.FocusAwareStatusBar, {
          backgroundColor: "transparent",
          barStyle: "dark-content",
          translucent: true
        }), empty && (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _bookingsOrders.styles.emptySection,
          children: (0, _jsxRuntime.jsx)(_emptyScreen.default, {
            title: errorMessage == null ? undefined : errorMessage.header,
            description: errorMessage == null ? undefined : errorMessage.subHeader,
            iconUrl: _$$_REQUIRE(_dependencyMap[29]),
            testID: `${_constants.NavigationConstants.vouchersPrizesRedemptionsScreen}__EmptyMyTravel`,
            accessibilityLabel: `${_constants.NavigationConstants.vouchersPrizesRedemptionsScreen}__EmptyMyTravel`
          })
        }), (0, _jsxRuntime.jsxs)(_reactNative.ScrollView, {
          ref: scrollRef,
          showsHorizontalScrollIndicator: false,
          showsVerticalScrollIndicator: false,
          contentContainerStyle: _bookingsOrders.styles.scrollViewContentContainerStyle,
          children: [!!(credits != null && credits.data) && (0, _jsxRuntime.jsx)(_creditsListing.default, {
            creditsDetailsData: credits.data,
            navigation: navigation
          }), !!(playpass != null && playpass.data) && !isVprIncludePP && ((_playpass$data = playpass.data) == null || _playpass$data.map == null ? undefined : _playpass$data.map(function (item, index) {
            return (0, _jsxRuntime.jsx)(_bookingsListingCard.default, {
              item: item,
              onPress: handlePressBooking
            }, `${_ordersListingCard2.BOOKING_CARD_NAME}_${index}_${item.bookingKey}`);
          })), !!(insurance != null && insurance.data) && ((_insurance$data = insurance.data) == null || _insurance$data.map == null ? undefined : _insurance$data.map(function (item, index) {
            return (0, _jsxRuntime.jsx)(_insuranceBookingCard.default, {
              order: item,
              onPress: handlePressInsurance
            }, `INSURANCE_CARD_${index}_${item == null ? undefined : item.orderId}`);
          })), loading && (0, _jsxRuntime.jsx)(_loadingBookingsOrders.default, {}), !!orders && (orders == null || orders.map == null ? undefined : orders.map(function (item, index) {
            return (0, _jsxRuntime.jsx)(_ordersListingCard.default, {
              item: item,
              onPress: handleOnPressOrderCard
            }, `${_ordersListingCard2.ORDER_CARD_NAME}_${index}_${item == null ? undefined : item.transaction_number}`);
          })), (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: _bookingsOrders.styles.bottomSpaceContainerStyle
          })]
        })]
      })
    });
  };
  var _default = exports.default = BookingsOrdersScreen;
