  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _responsive = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var distanceImageToEndScreen = 179;
  var heightButtonCTA = 44;
  var distanceButtonToEndScreen = 60;
  var ratio_lottie = 0.6666666666666666;
  var _Dimensions$get = _reactNative.Dimensions.get('window'),
    width = _Dimensions$get.width;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: _theme.color.palette.almostWhiteGrey
    },
    viewContent: {
      flex: 1
    },
    viewItem: {
      flex: 1
    },
    image: {
      width: width,
      position: 'absolute',
      bottom: distanceImageToEndScreen
    },
    viewTxt: {
      flex: 1,
      paddingHorizontal: 24
    },
    txtTitle: {
      color: _theme.color.palette.whiteGrey,
      fontFamily: _theme.typography.black,
      fontSize: _responsive.default.getFontSize(32),
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      }),
      lineHeight: _responsive.default.getFontSize(40),
      marginBottom: 16
    },
    txtDescription: {
      fontFamily: _theme.typography.regular,
      color: _theme.color.palette.whiteGrey,
      fontSize: _responsive.default.getFontSize(16),
      fontWeight: _reactNative.Platform.select({
        ios: "400",
        android: "normal"
      }),
      lineHeight: _responsive.default.getFontSize(20)
    },
    button: {
      width: '100%',
      height: heightButtonCTA,
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 60,
      justifyContent: 'center',
      alignItems: 'center'
    },
    viewButton: {
      position: 'absolute',
      marginTop: 20,
      width: '100%',
      paddingHorizontal: 40,
      alignItems: 'center',
      bottom: 0,
      paddingBottom: distanceButtonToEndScreen
    },
    txtButton: {
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.lightPurple,
      fontSize: _responsive.default.getFontSize(16),
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: _responsive.default.getFontSize(20)
    },
    txtSubButton: {
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.whiteGrey,
      fontSize: _responsive.default.getFontSize(16),
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: _responsive.default.getFontSize(20),
      marginTop: 24
    },
    viewHeader: {
      width: '100%',
      position: 'absolute',
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingHorizontal: 24
    },
    viewStatus: {
      flex: 1,
      justifyContent: 'flex-end'
    },
    viewContainerStatus: {
      flexDirection: 'row',
      marginBottom: -8
    },
    status: {
      width: 24,
      height: 4,
      borderRadius: 99,
      marginRight: 4
    },
    lottieStyle: {
      width: width,
      height: width * ratio_lottie,
      position: 'absolute',
      bottom: distanceImageToEndScreen
    },
    overlayStyle: {
      position: 'absolute',
      width: '100%',
      height: '100%',
      backgroundColor: _theme.color.palette.almostWhiteGrey
    }
  });
