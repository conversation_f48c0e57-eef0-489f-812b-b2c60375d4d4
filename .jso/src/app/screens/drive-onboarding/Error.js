  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ErrorScreen = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _icons = _$$_REQUIRE(_dependencyMap[3]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[6]);
  var _iconCloud = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _text = _$$_REQUIRE(_dependencyMap[8]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _button = _$$_REQUIRE(_dependencyMap[10]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  var ErrorScreen = exports.ErrorScreen = function ErrorScreen(props) {
    var handlePressReload = props.handlePressReload,
      onBack = props.onBack;
    var insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    var distanceTopButtonBack = insets.top + 16;
    var titleTx = "errorOverlay.variant1.title";
    var messageTx = "errorOverlay.variant1.message";
    var reloadTx = "errorOverlay.variant1.reload";
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.content,
        children: [(0, _jsxRuntime.jsx)(_iconCloud.default, {}), (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.titleTextStyle,
          tx: titleTx
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.messageTextStyle,
          tx: messageTx
        }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
          style: styles.reloadButtonStyle,
          start: {
            x: 1,
            y: 0
          },
          end: {
            x: 0,
            y: 1
          },
          colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
          children: (0, _jsxRuntime.jsx)(_button.Button, {
            onPress: handlePressReload,
            sizePreset: "large",
            textPreset: "buttonLarge",
            typePreset: "primary",
            tx: reloadTx,
            backgroundPreset: "light",
            statePreset: "default"
          })
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        style: [styles.buttonBack, {
          top: distanceTopButtonBack
        }],
        onPress: onBack,
        children: (0, _jsxRuntime.jsx)(_icons.BackButton, {})
      })]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: _theme.color.palette.almostWhiteGrey
    },
    buttonBack: {
      position: 'absolute',
      left: 20
    },
    content: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 24
    },
    titleTextStyle: Object.assign({}, _text.presets.h2, {
      marginBottom: 16,
      marginTop: 40,
      textAlign: "center"
    }),
    messageTextStyle: Object.assign({}, _text.presets.caption1Regular, {
      color: _theme.color.palette.darkestGrey,
      textAlign: "center"
    }),
    reloadButtonStyle: {
      width: '100%',
      borderRadius: 60,
      paddingHorizontal: 24,
      marginTop: 24
    }
  });
