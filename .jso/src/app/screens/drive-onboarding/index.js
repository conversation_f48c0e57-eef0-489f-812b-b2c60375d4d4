  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _globalLoadingController = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _services = _$$_REQUIRE(_dependencyMap[9]);
  var _native = _$$_REQUIRE(_dependencyMap[10]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _request = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _reactNativeSwiper = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _text = _$$_REQUIRE(_dependencyMap[14]);
  var _utils = _$$_REQUIRE(_dependencyMap[15]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _reactNativeRenderHtml = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[17]));
  var _icons = _$$_REQUIRE(_dependencyMap[18]);
  var _styles = _$$_REQUIRE(_dependencyMap[19]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[20]);
  var _htmlContent = _$$_REQUIRE(_dependencyMap[21]);
  var _theme = _$$_REQUIRE(_dependencyMap[22]);
  var _responsive = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[23]));
  var _i18n = _$$_REQUIRE(_dependencyMap[24]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[25]);
  var _lottieReactNative = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[26]));
  var _Error = _$$_REQUIRE(_dependencyMap[27]);
  var _adobe = _$$_REQUIRE(_dependencyMap[28]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[29]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var systemFonts = [].concat((0, _toConsumableArray2.default)(_reactNativeRenderHtml.defaultSystemFonts), ["Lato-Regular", "Lato-Bold", "Lato-Italic"]);
  var distanceStartScreenToTitle = 88;
  var ViewItem = _react.default.memo(function (props) {
    var _item$cta3, _item$cta4;
    var refLottieImage = (0, _react.useRef)(null);
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)(),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var item = props.item,
      onPressCta1 = props.onPressCta1,
      statusBarHeight = props.statusBarHeight,
      focus = props.focus;
    var _useState = (0, _react.useState)(0),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      heightImage = _useState2[0],
      setHeightImage = _useState2[1];
    var getSizeImage = function getSizeImage(url) {
      _reactNative2.Image.getSize(url, function (width, height) {
        if (width && height) {
          var ratioCurrentCard = height / width;
          var widthScreen = _reactNative2.Dimensions.get('screen').width;
          var _heightImage = ratioCurrentCard * widthScreen;
          setHeightImage(_heightImage);
        }
      }, function (error) {
        setHeightImage(0);
      });
    };
    (0, _react.useEffect)(function () {
      if ((0, _utils.mappingUrlAem)(item == null ? undefined : item.image)) {
        getSizeImage((0, _utils.mappingUrlAem)(item == null ? undefined : item.image));
      }
    }, [item == null ? undefined : item.image]);
    var onPressCta2 = function onPressCta2() {
      var _item$cta, _item$cta2;
      var _ref = (item == null || (_item$cta = item.cta2) == null ? undefined : _item$cta.navigation) || {},
        type = _ref.type,
        value = _ref.value;
      var redirectFirst = item == null ? undefined : item.redirect;
      if (!type) return;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFeatureBuddy, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFeatureBuddy, `Parking | ${item == null ? undefined : item.title} | ${item == null || (_item$cta2 = item.cta2) == null ? undefined : _item$cta2.text}`));
      handleNavigation(type, value, redirectFirst);
    };
    var formattedHtmlContent = (0, _htmlContent.formatHtmlContent)(item == null ? undefined : item.copy);
    var tagStyle = {
      p: {
        fontFamily: _theme.typography.regular,
        color: _theme.color.palette.whiteGrey,
        fontSize: _responsive.default.getFontSize(16),
        lineHeight: _responsive.default.getFontSize(20),
        fontWeight: _reactNative2.Platform.select({
          ios: "400",
          android: "normal"
        })
      },
      b: {
        fontFamily: _theme.typography.bold,
        color: _theme.color.palette.whiteGrey,
        fontSize: _responsive.default.getFontSize(16),
        lineHeight: _responsive.default.getFontSize(20),
        fontWeight: _reactNative2.Platform.select({
          ios: "700",
          android: "normal"
        })
      },
      i: {
        fontFamily: _theme.typography.italic,
        color: _theme.color.palette.whiteGrey,
        fontSize: _responsive.default.getFontSize(16),
        lineHeight: _responsive.default.getFontSize(20),
        fontWeight: _reactNative2.Platform.select({
          ios: "400",
          android: "normal"
        }),
        fontStyle: "italic"
      }
    };
    (0, _react.useEffect)(function () {
      if (refLottieImage.current && focus) {
        refLottieImage.current.play();
      }
    }, [focus]);
    var renderIcon = function renderIcon() {
      if (!(item != null && item.image)) {
        return null;
      } else if ((0, _utils.validateImageLottieAEM)(item == null ? undefined : item.image)) {
        return (0, _jsxRuntime.jsx)(_lottieReactNative.default, {
          ref: refLottieImage,
          style: _styles.styles.lottieStyle,
          source: {
            uri: (0, _utils.mappingUrlAem)(item == null ? undefined : item.image)
          },
          autoPlay: true,
          loop: false,
          resizeMode: "contain"
        });
      } else {
        return (0, _jsxRuntime.jsx)(_baseImage.default, {
          source: {
            uri: (0, _utils.mappingUrlAem)(item == null ? undefined : item.image)
          },
          style: [_styles.styles.image, {
            height: heightImage
          }],
          resizeMode: "contain"
        });
      }
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _styles.styles.viewItem,
      children: [renderIcon(), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: [_styles.styles.viewTxt, {
          paddingTop: distanceStartScreenToTitle + statusBarHeight
        }],
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: _styles.styles.txtTitle,
          children: item == null ? undefined : item.title
        }), (0, _jsxRuntime.jsx)(_reactNative2.ScrollView, {
          showsVerticalScrollIndicator: false,
          bounces: false,
          children: (0, _jsxRuntime.jsx)(_reactNativeRenderHtml.default, {
            systemFonts: systemFonts,
            tagsStyles: tagStyle,
            source: {
              html: `<div>${formattedHtmlContent}</div>`
            }
          })
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: [_styles.styles.viewButton],
        children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: _styles.styles.button,
          onPress: onPressCta1,
          activeOpacity: 0.5,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: _styles.styles.txtButton,
            children: (item == null || (_item$cta3 = item.cta1) == null ? undefined : _item$cta3.text) || (0, _i18n.translate)("faqLanding.next")
          })
        }), (item == null ? undefined : item.cta2) && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: onPressCta2,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: _styles.styles.txtSubButton,
            children: item == null || (_item$cta4 = item.cta2) == null ? undefined : _item$cta4.text
          })
        })]
      })]
    });
  });
  var DriveOnboardingScreen = function DriveOnboardingScreen() {
    var insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    var statusBarHeight = insets.top;
    var isFocused = (0, _native.useIsFocused)();
    var _useHandleNavigation2 = (0, _navigationHelper.useHandleNavigation)(),
      handleNavigation = _useHandleNavigation2.handleNavigation;
    var navigation = (0, _native.useNavigation)();
    var swiperRef = (0, _react.useRef)(null);
    var _useState3 = (0, _react.useState)(null),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      data = _useState4[0],
      setData = _useState4[1];
    var _useState5 = (0, _react.useState)(-1),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      indexTab = _useState6[0],
      setIndex = _useState6[1];
    var sortedData = _react.default.useMemo(function () {
      if (!data) return [];
      return (0, _toConsumableArray2.default)(data).sort(function (a, b) {
        return a.sequenceNumber - b.sequenceNumber;
      });
    }, [data]);
    var _useState7 = (0, _react.useState)(false),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      error = _useState8[0],
      setError = _useState8[1];
    var getData = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        try {
          var _response$data;
          _globalLoadingController.default.showLoading(false);
          var response = yield (0, _request.default)((0, _services.getCommonRESTfulRequestData)("getDataDriveOnboarding"));
          var payload = response == null || (_response$data = response.data) == null ? undefined : _response$data.list;
          if (payload) {
            setData(payload);
            setError(false);
          } else {
            setData(null);
            setError(true);
          }
        } catch (error) {
          setData(null);
          setError(true);
        } finally {
          setTimeout(function () {
            _globalLoadingController.default.hideLoading();
          }, 1000);
        }
      });
      return function getData() {
        return _ref2.apply(this, arguments);
      };
    }();
    var viewingContent = _react.default.useMemo(function () {
      if (!sortedData || indexTab < 0 || indexTab >= sortedData.length) return null;
      return sortedData[indexTab];
    }, [sortedData, indexTab]);
    (0, _react.useEffect)(function () {
      var checkInternet = /*#__PURE__*/function () {
        var _ref3 = (0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch.isConnected;
          if (!isConnected) {
            setError(true);
          } else {
            getData();
          }
        });
        return function checkInternet() {
          return _ref3.apply(this, arguments);
        };
      }();
      checkInternet();
    }, []);
    var goBack = function goBack() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFeatureBuddy, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFeatureBuddy, `Parking | ${viewingContent == null ? undefined : viewingContent.title} | x`));
      navigation.goBack();
    };
    var handleNext = function handleNext(item) {
      var _item$cta5;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFeatureBuddy, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFeatureBuddy, `Parking | ${viewingContent == null ? undefined : viewingContent.title} | ${(item == null || (_item$cta5 = item.cta1) == null ? undefined : _item$cta5.text) || (0, _i18n.translate)("faqLanding.next")}`));
      if (item != null && item.cta1) {
        var _item$cta6;
        var _ref4 = (item == null || (_item$cta6 = item.cta1) == null ? undefined : _item$cta6.navigation) || {},
          type = _ref4.type,
          value = _ref4.value;
        var redirectFirst = item == null ? undefined : item.redirect;
        if (!type) return;
        handleNavigation(type, value, redirectFirst);
      } else {
        if (swiperRef.current && indexTab < sortedData.length - 1) {
          swiperRef.current.scrollBy(1, true); // Scroll forward by 1 slide with animation
        }
      }
    };
    var renderPagination = function renderPagination(index, total) {
      if (index < 0) {
        setTimeout(function () {
          setIndex(index);
        }, 1000);
      } else {
        setIndex(index);
      }
      var indexStatus = index;
      var arr = (0, _toConsumableArray2.default)(Array(total).keys());
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: [_styles.styles.viewHeader, {
          marginTop: statusBarHeight
        }],
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _styles.styles.viewStatus,
          children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _styles.styles.viewContainerStatus,
            children: arr == null ? undefined : arr.map(function (index) {
              return (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: [_styles.styles.status, {
                  backgroundColor: indexStatus === index ? "#FFFFFF" : "rgba(255, 255, 255, 0.6)"
                }]
              }, `Status_${index}`);
            })
          })
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          hitSlop: {
            left: 15,
            right: 15,
            bottom: 15,
            top: 15
          },
          onPress: goBack,
          children: (0, _jsxRuntime.jsx)(_icons.CloseIconWhite, {})
        })]
      });
    };
    var handlePressReload = /*#__PURE__*/function () {
      var _ref5 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch2.isConnected;
        if (!isConnected) {
          setError(true);
        } else {
          getData();
        }
      });
      return function handlePressReload() {
        return _ref5.apply(this, arguments);
      };
    }();
    if (error) {
      return (0, _jsxRuntime.jsx)(_Error.ErrorScreen, {
        onBack: goBack,
        handlePressReload: handlePressReload
      });
    }
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: [_styles.styles.container],
      children: sortedData.length > 0 && isFocused && (0, _jsxRuntime.jsx)(_reactNativeSwiper.default, {
        ref: swiperRef,
        showsPagination: sortedData.length === 1 ? false : true,
        renderPagination: renderPagination,
        loop: false,
        removeClippedSubviews: false,
        children: sortedData.map(function (item, index) {
          return (0, _jsxRuntime.jsx)(_reactNative2.ImageBackground, {
            source: {
              uri: (0, _utils.mappingUrlAem)(item == null ? undefined : item.background)
            },
            style: [_styles.styles.viewContent],
            children: (0, _jsxRuntime.jsx)(ViewItem, {
              item: item,
              onPressCta1: function onPressCta1() {
                return handleNext(item);
              },
              statusBarHeight: statusBarHeight,
              focus: indexTab === index
            })
          }, `Container_${index}`);
        })
      })
    });
  };
  var _default = exports.default = DriveOnboardingScreen;
