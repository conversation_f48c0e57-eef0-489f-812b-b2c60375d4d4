  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = exports.loadingStyles = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    containerStyle: {
      paddingTop: 12,
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      flex: 1
    },
    cardContainerStyle: Object.assign({
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 12,
      marginHorizontal: 16,
      marginTop: 12,
      paddingHorizontal: 16,
      paddingVertical: 24
    }, _theme.shadow.primaryShadow, {
      shadowOpacity: 0.1
    }),
    firstSectionContainerStyle: {
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'space-between'
    },
    creditValueTextStyle: Object.assign({}, _text.boldFontStyle, {
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 48,
      lineHeight: 58
    }),
    creditDescriptionTextStyle: Object.assign({}, _text.boldFontStyle, {
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 18,
      lineHeight: 22
    }),
    creditSubDescriptionTextStyle: Object.assign({}, _text.boldFontStyle, {
      color: _theme.color.palette.darkestGrey,
      fontSize: 11,
      marginTop: 4
    }),
    creditIconStyle: {
      borderRadius: 60,
      height: 60,
      width: 60
    },
    secondSectionContainerStyle: {
      marginTop: 32
    },
    expireLineTextStyle: Object.assign({}, _text.regularFontStyle, {
      color: _theme.color.palette.darkestGrey,
      fontSize: 14,
      lineHeight: 18
    }),
    purchaseMoreCreditBtnStyle: {
      alignSelf: 'center',
      marginBottom: 104,
      marginTop: 32
    },
    purchaseMoreCreditBtnTextStyle: Object.assign({}, _text.boldFontStyle, {
      color: _theme.color.palette.lightPurple,
      fontSize: 16,
      lineHeight: 22
    }),
    errorStyles: {
      height: "80%",
      justifyContent: "flex-start",
      paddingVertical: 30,
      width: "100%"
    },
    emptyTransactionStyle: {
      alignItems: "center",
      justifyContent: "center",
      marginBottom: 50,
      marginTop: 77
    },
    titleEmptyScreen: {
      marginTop: 24
    },
    descriptionEmptyScreen: {
      marginTop: 24
    }
  });
  var loadingStyles = exports.loadingStyles = _reactNative.StyleSheet.create({
    cardContainer: Object.assign({
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 12,
      padding: 16,
      paddingBottom: 4,
      margin: 8,
      marginBottom: 12,
      marginHorizontal: 16,
      flexDirection: "row"
    }, _theme.shadow.primaryShadow, {
      shadowOpacity: 0.1
    }),
    leftContent: {},
    rightContent: {
      flex: 1,
      paddingLeft: 12,
      paddingVertical: 22
    },
    elementLeft: {
      height: 80,
      width: 80
    },
    elementRight: {
      height: 12,
      borderRadius: 4,
      width: "100%",
      marginBottom: 12
    }
  });
