  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useErrorHandling = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _$$_REQUIRE(_dependencyMap[3]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[4]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _constants = _$$_REQUIRE(_dependencyMap[7]);
  var useErrorHandling = exports.useErrorHandling = function useErrorHandling(props) {
    var initializeData = props.initializeData,
      navigation = props.navigation;
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var _useState = (0, _react.useState)(true),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isConnected = _useState2[0],
      setConnected = _useState2[1];
    var retry = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* () {
        var isConnectedNetInfo = yield checkConnection();
        if (isConnectedNetInfo) {
          initializeData == null || initializeData();
        }
      });
      return function retry() {
        return _ref.apply(this, arguments);
      };
    }();
    var checkConnection = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnectedNetInfo = _yield$NetInfo$fetch.isConnected;
        setConnected(isConnectedNetInfo);
        return isConnectedNetInfo;
      });
      return function checkConnection() {
        return _ref2.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      checkConnection();
    }, []);
    (0, _react.useEffect)(function () {
      if (isLoggedIn) {
        retry();
        return;
      }
      if (!isLoggedIn) {
        navigation.navigate(_constants.NavigationConstants.authScreen, {
          callBackAfterLoginCancel: function callBackAfterLoginCancel() {
            return navigation.pop();
          }
        });
      }
    }, [isLoggedIn]);
    return {
      isConnected: isConnected,
      retry: retry
    };
  };
