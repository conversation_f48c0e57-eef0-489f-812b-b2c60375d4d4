  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _native = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _forYouRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[8]));
  var _utils = _$$_REQUIRE(_dependencyMap[9]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _constants = _$$_REQUIRE(_dependencyMap[11]);
  var _theme = _$$_REQUIRE(_dependencyMap[12]);
  var _credits = _$$_REQUIRE(_dependencyMap[13]);
  var _credits2 = _$$_REQUIRE(_dependencyMap[14]);
  var _changiEcardControler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[16]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[17]);
  var _adobe = _$$_REQUIRE(_dependencyMap[18]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[19]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[20]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var CREDITS_EXPIRE_LINE_NAME = "creditsExpireLine";
  var LoadingState = function LoadingState() {
    var LOADING_CARD_AMOUNT = 3;
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: Array(LOADING_CARD_AMOUNT).fill(null).map(function (ele) {
        return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _credits.loadingStyles.cardContainer,
          children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _credits.loadingStyles.leftContent,
            children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: _theme.color.shimmerPlacholderColorLighter,
              shimmerStyle: _credits.loadingStyles.elementLeft
            })
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _credits.loadingStyles.rightContent,
            children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: _theme.color.shimmerPlacholderColorLighter,
              shimmerStyle: _credits.loadingStyles.elementRight
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: _theme.color.shimmerPlacholderColorLighter,
              shimmerStyle: [_credits.loadingStyles.elementRight, {
                width: 74
              }]
            })]
          })]
        });
      })
    });
  };
  var CreditCard = (0, _react.memo)(function (_ref) {
    var _item$creditsExpireLi;
    var item = _ref.item,
      navigation = _ref.navigation;
    var getPluralText = function getPluralText(condition, pluralTx, singularTx) {
      return condition ? pluralTx : singularTx;
    };
    var onItemPress = function onItemPress() {
      _changiEcardControler.default.showModal(navigation);
    };
    return (0, _jsxRuntime.jsxs)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
      onPress: onItemPress,
      style: _credits.styles.cardContainerStyle,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _credits.styles.firstSectionContainerStyle,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: _credits.styles.creditValueTextStyle,
            children: (0, _utils.toLocaleNumber)(item == null ? undefined : item.totalCredits)
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: _credits.styles.creditDescriptionTextStyle,
            children: getPluralText((item == null ? undefined : item.totalCredits) > 1, `${item == null ? undefined : item.creditName}s`, item == null ? undefined : item.creditName)
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: _credits.styles.creditSubDescriptionTextStyle,
            tx: getPluralText((item == null ? undefined : item.redeemableCredits) > 1, "creditsScreen.redeemableCreditUnit_plural", "creditsScreen.redeemableCreditUnit"),
            txOptions: {
              value: item == null ? undefined : item.redeemableCredits
            }
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          children: (0, _jsxRuntime.jsx)(_baseImage.default, {
            source: {
              uri: item == null ? undefined : item.iconUrl
            },
            style: _credits.styles.creditIconStyle
          })
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _credits.styles.secondSectionContainerStyle,
        children: item == null || (_item$creditsExpireLi = item.creditsExpireLines) == null || _item$creditsExpireLi.map == null ? undefined : _item$creditsExpireLi.map(function (item, index) {
          return (0, _jsxRuntime.jsx)(_text.Text, {
            style: _credits.styles.expireLineTextStyle,
            tx: getPluralText((item == null ? undefined : item.totalCredits) > 1, "creditsScreen.creditsExpire_plural", "creditsScreen.creditsExpire"),
            txOptions: {
              value: item == null ? undefined : item.totalCredits,
              date: item == null ? undefined : item.expiryDate
            }
          }, `${CREDITS_EXPIRE_LINE_NAME}_${item == null ? undefined : item.expiryDate}_${index}`);
        })
      })]
    });
  });
  var MainComponent = function MainComponent(props) {
    var initializeData = props.initializeData,
      navigation = props.navigation;
    var creditsDetailsData = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.creditsDetailsData);
    var creditsDetailsFetching = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.creditsDetailsFetching);
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var isLoading = creditsDetailsFetching;
    var onPressPurchaseMoreCredits = function onPressPurchaseMoreCredits() {
      navigation.dispatch(_native.StackActions.popToTop());
      navigation.navigate(_constants.NavigationConstants.explore, {
        isOpenApp: false,
        isScrollToExploreChangiSection: true
      });
    };
    (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
      initializeData == null || initializeData();
    }, []));
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)(_constants.TrackingScreenName.Credits);
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)(_constants.TrackingScreenName.Credits, (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribeFocus;
    }, [navigation]);
    if (isLoading) return (0, _jsxRuntime.jsx)(LoadingState, {});
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [creditsDetailsData == null || creditsDetailsData.map == null ? undefined : creditsDetailsData.map(function (item) {
        return (0, _jsxRuntime.jsx)(CreditCard, {
          item: item,
          navigation: navigation
        });
      }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        onPress: onPressPurchaseMoreCredits,
        style: _credits.styles.purchaseMoreCreditBtnStyle,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          tx: "creditsScreen.purchaseMoreCreditsBtn",
          style: _credits.styles.purchaseMoreCreditBtnTextStyle
        })
      })]
    });
  };
  var CreditsScreen = function CreditsScreen(props) {
    var dispatch = (0, _reactRedux.useDispatch)();
    var initializeData = function initializeData() {
      dispatch(_forYouRedux.default.creditsDetailsRequest());
    };
    return (0, _credits2.withCommonHandler)(MainComponent, Object.assign({}, props, {
      initializeData: initializeData
    }));
  };
  var _default = exports.default = CreditsScreen;
