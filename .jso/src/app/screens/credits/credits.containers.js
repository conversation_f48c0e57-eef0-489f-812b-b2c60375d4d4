  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.withCommonHandler = undefined;
  var _reactRedux = _$$_REQUIRE(_dependencyMap[1]);
  var _credits = _$$_REQUIRE(_dependencyMap[2]);
  var _forYouRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[4]);
  var _credits2 = _$$_REQUIRE(_dependencyMap[5]);
  var _credits3 = _$$_REQUIRE(_dependencyMap[6]);
  var _errorOverlay = _$$_REQUIRE(_dependencyMap[7]);
  var _react = _$$_REQUIRE(_dependencyMap[8]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[9]);
  var _emptyScreen = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _aemRedux = _$$_REQUIRE(_dependencyMap[11]);
  var _validate = _$$_REQUIRE(_dependencyMap[12]);
  var _native = _$$_REQUIRE(_dependencyMap[13]);
  var _focusStatusBar = _$$_REQUIRE(_dependencyMap[14]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[15]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var EHR117 = "EHR11.7";
  var AEM_COMMON_DATA = "AEM_COMMON_DATA";
  var Container = function Container(_ref) {
    var children = _ref.children;
    return (0, _jsxRuntime.jsxs)(_reactNative.ScrollView, {
      showsHorizontalScrollIndicator: false,
      showsVerticalScrollIndicator: false,
      style: _credits2.styles.containerStyle,
      children: [(0, _jsxRuntime.jsx)(_focusStatusBar.FocusAwareStatusBar, {
        translucent: true,
        backgroundColor: "transparent",
        barStyle: "dark-content"
      }), children]
    });
  };
  var withCommonHandler = exports.withCommonHandler = function withCommonHandler(WrappedComponent, props) {
    var _dataCommonAEM$data;
    var _useErrorHandling = (0, _credits.useErrorHandling)(props),
      isConnected = _useErrorHandling.isConnected,
      retry = _useErrorHandling.retry;
    var creditsDetailsError = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.creditsDetailsError);
    var creditsDetailsFetching = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.creditsDetailsFetching);
    var creditsDetailsInitializing = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.creditsDetailsInitializing);
    var creditsDetailsData = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.creditsDetailsData);
    var dataCommonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(AEM_COMMON_DATA));
    var ehr117 = dataCommonAEM == null || (_dataCommonAEM$data = dataCommonAEM.data) == null || (_dataCommonAEM$data = _dataCommonAEM$data.errors) == null ? undefined : _dataCommonAEM$data.find(function (e) {
      return (e == null ? undefined : e.code) === EHR117;
    });
    var navigation = (0, _native.useNavigation)();
    var dispatch = (0, _reactRedux.useDispatch)();
    var ErrorConnection = (0, _react.useCallback)(function () {
      return (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        hideScreenHeader: false,
        reload: true,
        onReload: retry,
        noInternetOverlayStyle: _credits2.styles.errorStyles,
        headerBackgroundColor: "transparent",
        visible: true,
        testID: `${_credits3.SCREEN_NAME}__ErrorOverlayNoConnection`,
        accessibilityLabel: `${_credits3.SCREEN_NAME}__ErrorOverlayNoConnection`
      });
    }, []);
    var ErrorPage = (0, _react.useCallback)(function () {
      return (0, _jsxRuntime.jsx)(_errorOverlay.ErrorOverlay, {
        hideScreenHeader: false,
        reload: true,
        visible: true,
        onReload: retry,
        overlayStyle: _credits2.styles.errorStyles,
        variant: _errorOverlay.ErrorOverlayVariant.VARIANT2,
        testID: `${_credits3.SCREEN_NAME}__ErrorOverlay`,
        accessibilityLabel: `${_credits3.SCREEN_NAME}__ErrorOverlay`,
        extendCode: _errorOverlay.ERROR_HANDLING_CODE.ERROR_PAGE_LEVEL
      });
    }, []);
    (0, _react.useEffect)(function () {
      var unsubscribeBlur = navigation.addListener("blur", function () {
        // Reset credits data
        dispatch(_forYouRedux.default.creditsDetailsReset());
      });
      return unsubscribeBlur;
    }, [navigation]);
    if (!isConnected) {
      return (0, _jsxRuntime.jsx)(Container, {
        children: (0, _jsxRuntime.jsx)(ErrorConnection, {})
      });
    }
    if (creditsDetailsInitializing && !creditsDetailsFetching) {
      return null;
    }
    if (!creditsDetailsFetching && creditsDetailsError) {
      return (0, _jsxRuntime.jsx)(Container, {
        children: (0, _jsxRuntime.jsx)(ErrorPage, {})
      });
    }
    if (!creditsDetailsFetching && (0, _validate.isEmpty)(creditsDetailsData)) {
      return (0, _jsxRuntime.jsx)(Container, {
        children: (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _credits2.styles.emptyTransactionStyle,
          children: (0, _jsxRuntime.jsx)(_emptyScreen.default, {
            iconUrl: _$$_REQUIRE(_dependencyMap[16]),
            title: ehr117 == null ? undefined : ehr117.header,
            description: ehr117 == null ? undefined : ehr117.subHeader,
            firstAction: ehr117 == null ? undefined : ehr117.buttonLabel,
            testID: `${_credits3.SCREEN_NAME}__EmptyMyTravel`,
            accessibilityLabel: `${_credits3.SCREEN_NAME}__EmptyMyTravel`,
            firstActionPress: function firstActionPress() {
              return null;
            },
            descriptionStyles: _credits2.styles.descriptionEmptyScreen,
            titleStyles: _credits2.styles.titleEmptyScreen
          })
        })
      });
    }
    return (0, _jsxRuntime.jsx)(Container, {
      children: (0, _jsxRuntime.jsx)(WrappedComponent, Object.assign({}, props))
    });
  };
