  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _color = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var heightHeader = 100;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: _color.color.palette.almostWhiteGrey
    },
    viewContent: {
      flex: 1,
      backgroundColor: _color.color.palette.almostWhiteGrey,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16
    },
    viewHeaderAnimated: {
      width: "100%",
      position: "absolute",
      top: 0
    },
    viewFilterHeader: {
      position: "absolute",
      top: heightHeader,
      backgroundColor: _color.color.palette.whiteGrey
    },
    background: {
      borderTopLeftRadius: 0,
      borderTopRightRadius: 0,
      backgroundColor: _color.color.palette.whiteGrey
    },
    contentContainerStyle: {
      backgroundColor: _color.color.palette.almostWhiteGrey,
      paddingBottom: 24
    }
  });
