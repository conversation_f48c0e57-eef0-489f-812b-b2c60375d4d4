  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _styles = _$$_REQUIRE(_dependencyMap[3]);
  var _component = _$$_REQUIRE(_dependencyMap[4]);
  var _useFunction2 = _$$_REQUIRE(_dependencyMap[5]);
  var _collapsibleHeader = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _i18n = _$$_REQUIRE(_dependencyMap[7]);
  var _backgrounds = _$$_REQUIRE(_dependencyMap[8]);
  var _filterBottomSheet = _$$_REQUIRE(_dependencyMap[9]);
  var _viewEmpty = _$$_REQUIRE(_dependencyMap[10]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  var DineShopDirectory = function DineShopDirectory(props) {
    var route = props.route;
    var _useFunction = (0, _useFunction2.useFunction)(route == null ? undefined : route.params),
      navigation = _useFunction.navigation,
      error = _useFunction.error,
      loading = _useFunction.loading,
      data = _useFunction.data,
      handlePressReloadError = _useFunction.handlePressReloadError,
      showFilterModal = _useFunction.showFilterModal,
      openModalFilter = _useFunction.openModalFilter,
      closeModalFilter = _useFunction.closeModalFilter,
      errorFilter = _useFunction.errorFilter,
      loadingFilter = _useFunction.loadingFilter,
      dataFilter = _useFunction.dataFilter,
      _setDataFilter = _useFunction.setDataFilter,
      handlePressReloadFilter = _useFunction.handlePressReloadFilter,
      dataFilterOriginal = _useFunction.dataFilterOriginal,
      rootListRef = _useFunction.rootListRef,
      setDataSubFilter = _useFunction.setDataSubFilter,
      perkItemOffsetListRef = _useFunction.perkItemOffsetListRef,
      rootItemOffsetRef = _useFunction.rootItemOffsetRef;
    var renderFilterBar = function renderFilterBar() {
      return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
        children: loading || error ? null : (0, _jsxRuntime.jsx)(_component.ViewFilter, {
          containerStyle: _styles.styles.background,
          openModalFilter: openModalFilter,
          errorFilter: errorFilter,
          loadingFilter: loadingFilter,
          dataFilter: dataFilter,
          setDataFilter: setDataSubFilter
        })
      });
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: _styles.styles.container,
      children: [(0, _jsxRuntime.jsx)(_collapsibleHeader.default, {
        navigation: navigation,
        headerImageSource: _backgrounds.DineShopDirectoryBackground,
        headerTitle: (0, _i18n.translate)("dineShopDirectory.title"),
        renderFilter: renderFilterBar,
        hasError: !!error,
        renderError: function renderError() {
          return (0, _jsxRuntime.jsx)(_component.ViewError, {
            typeError: error,
            handlePressReload: handlePressReloadError
          });
        },
        isLoading: loading,
        listData: data,
        perkItemOffsetListRef: perkItemOffsetListRef,
        rootItemOffsetRef: rootItemOffsetRef,
        rootListRef: rootListRef,
        data: data,
        renderItem: function renderItem(_ref) {
          var index = _ref.index,
            item = _ref.item;
          return (0, _jsxRuntime.jsx)(_component.ItemContent, {
            item: item,
            index: index,
            perkItemOffsetListRef: perkItemOffsetListRef,
            dataLength: data == null ? undefined : data.length
          });
        },
        nameField: "title",
        sortBy: loading || error || (data == null ? undefined : data.legnth) <= 5 ? _filterBottomSheet.SortBy.LatestAddedDate : _filterBottomSheet.SortBy.AZ,
        keyExtractor: function keyExtractor(item) {
          return item == null ? undefined : item.id.toString();
        },
        showsVerticalScrollIndicator: false,
        contentContainerStyle: _styles.styles.contentContainerStyle,
        initialNumToRender: 600,
        windowSize: 50,
        scrollEnabled: (data == null ? undefined : data.length) > 0,
        customComponentLoading: (0, _jsxRuntime.jsx)(_component.ViewLoading, {}),
        isScrollToIndex: true,
        ListEmptyComponent: (0, _jsxRuntime.jsx)(_viewEmpty.ViewEmpty, {})
      }), (0, _jsxRuntime.jsx)(_component.BottomSheetFilter, {
        showFilterModal: showFilterModal,
        closeModalFilter: closeModalFilter,
        errorFilter: errorFilter,
        loadingFilter: loadingFilter,
        dataFilter: dataFilter,
        dataFilterOriginal: dataFilterOriginal,
        setDataFilter: function setDataFilter(data) {
          return _setDataFilter(data);
        },
        handlePressReloadFilter: handlePressReloadFilter
      })]
    });
  };
  var _default = exports.default = DineShopDirectory;
