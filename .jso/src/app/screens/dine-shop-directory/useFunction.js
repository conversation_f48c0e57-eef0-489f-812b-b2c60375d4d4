  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useFunction = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _$$_REQUIRE(_dependencyMap[3]);
  var _dineShopDirectory = _$$_REQUIRE(_dependencyMap[4]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _request = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _envParams = _$$_REQUIRE(_dependencyMap[7]);
  var _awsAmplify = _$$_REQUIRE(_dependencyMap[8]);
  var _queries = _$$_REQUIRE(_dependencyMap[9]);
  var _dineShopDirectoryUntil = _$$_REQUIRE(_dependencyMap[10]);
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[11]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _native = _$$_REQUIRE(_dependencyMap[13]);
  var useFunction = exports.useFunction = function useFunction(screenParams) {
    var _ref = screenParams || {},
      initSelectedData = _ref.filterData,
      oneLinkTimestamp = _ref.timestamp;
    var navigation = (0, _native.useNavigation)();
    var rootListRef = (0, _react.useRef)(null);
    var queryFilterDataRef = (0, _react.useRef)(null);
    var perkItemOffsetListRef = (0, _react.useRef)([]);
    var rootItemOffsetRef = (0, _react.useRef)([]);
    var dataDateToSaveValue = (0, _mmkvStorage.getDateSaveDineDirectoryInday)();
    var dataDineDirectoryInDay = (0, _mmkvStorage.getDataDineDirectoryInday)();
    var _useState = (0, _react.useState)(null),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      error = _useState2[0],
      setError = _useState2[1];
    var _useState3 = (0, _react.useState)(true),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      loading = _useState4[0],
      setLoading = _useState4[1];
    var _useState5 = (0, _react.useState)(null),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      data = _useState6[0],
      setData = _useState6[1];
    var _useState7 = (0, _react.useState)(null),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      dataOriginal = _useState8[0],
      setDataOriginal = _useState8[1];
    var _useState9 = (0, _react.useState)(false),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      errorFilter = _useState0[0],
      setErrorFilter = _useState0[1];
    var _useState1 = (0, _react.useState)(true),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      loadingFilter = _useState10[0],
      setLoadingFilter = _useState10[1];
    var _useState11 = (0, _react.useState)(null),
      _useState12 = (0, _slicedToArray2.default)(_useState11, 2),
      dataFilter = _useState12[0],
      setDataFilter = _useState12[1];
    var _useState13 = (0, _react.useState)(null),
      _useState14 = (0, _slicedToArray2.default)(_useState13, 2),
      dataFilterOriginal = _useState14[0],
      setDataFilterOriginal = _useState14[1];
    var _useState15 = (0, _react.useState)(false),
      _useState16 = (0, _slicedToArray2.default)(_useState15, 2),
      showFilterModal = _useState16[0],
      setShowFilterModal = _useState16[1];
    var _useState17 = (0, _react.useState)(0),
      _useState18 = (0, _slicedToArray2.default)(_useState17, 2),
      offsetRecalculationCount = _useState18[0],
      setOffsetRecalculationCount = _useState18[1];
    var openModalFilter = (0, _react.useCallback)(function () {
      setShowFilterModal(true);
    }, []);
    var closeModalFilter = (0, _react.useCallback)(function () {
      setShowFilterModal(false);
    }, []);
    var checkInternet = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        if (!isConnected) {
          setError(_dineShopDirectory.ErrorType.NoInternet);
        } else {
          getDataFilter();
          getData();
        }
      });
      return function checkInternet() {
        return _ref2.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      checkInternet();
    }, []);
    var getDataFilter = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* () {
        try {
          var _env, _env2, _response$data;
          setLoadingFilter(true);
          setErrorFilter(null);
          var response = yield (0, _request.default)({
            url: (_env = (0, _envParams.env)()) == null ? undefined : _env.APPSYNC_GRAPHQL_URL,
            method: "post",
            data: (0, _awsAmplify.graphqlOperation)(_queries.listFilterPillDineShop_v2),
            parameters: {},
            headers: {
              "x-api-key": (_env2 = (0, _envParams.env)()) == null ? undefined : _env2.APPSYNC_GRAPHQL_API_KEY,
              'Cache-Control': 'no-cache'
            }
          });
          var queryFilterData = response == null || (_response$data = response.data) == null || (_response$data = _response$data.data) == null || (_response$data = _response$data.listFilterPillDineShop_v2) == null ? undefined : _response$data.data;
          if (queryFilterData) {
            queryFilterDataRef.current = queryFilterData;
            var initialDataFilter = (0, _dineShopDirectoryUntil.getInitialDataFilter)(queryFilterData, initSelectedData);
            setDataFilter(initialDataFilter);
            setDataFilterOriginal(queryFilterData);
            setLoadingFilter(false);
          } else {
            setLoadingFilter(false);
            setErrorFilter(true);
          }
        } catch (error) {
          setLoadingFilter(false);
          setErrorFilter(true);
        }
      });
      return function getDataFilter() {
        return _ref3.apply(this, arguments);
      };
    }();
    var setDataInDay = function setDataInDay() {
      var date = (0, _moment.default)().format("DD/MM/YYYY");
      (0, _mmkvStorage.setDateSaveDineDirectoryInday)(date);
    };
    (0, _react.useEffect)(function () {
      if (dataDateToSaveValue === (0, _moment.default)().format("DD/MM/YYYY")) {
        if ((dataFilterOriginal == null ? undefined : dataFilterOriginal.length) > 0 || errorFilter === true || error) {
          setLoading(false);
        }
      }
    }, [dataFilterOriginal, errorFilter, dataDateToSaveValue, error]);
    var getData = /*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* () {
        if (dataDateToSaveValue === (0, _moment.default)().format("DD/MM/YYYY")) {
          setError(null);
          setLoading(true);
          setData(typeof dataDineDirectoryInDay === "string" ? JSON.parse(dataDineDirectoryInDay) : []);
          setDataOriginal(typeof dataDineDirectoryInDay === "string" ? JSON.parse(dataDineDirectoryInDay) : []);
        } else {
          try {
            var _env3, _env4, _response$data2;
            setLoading(true);
            setError(null);
            var response = yield (0, _request.default)({
              url: (_env3 = (0, _envParams.env)()) == null ? undefined : _env3.APPSYNC_GRAPHQL_URL,
              method: "post",
              data: (0, _awsAmplify.graphqlOperation)(_queries.getTenantListing),
              parameters: {},
              headers: {
                "x-api-key": (_env4 = (0, _envParams.env)()) == null ? undefined : _env4.APPSYNC_GRAPHQL_API_KEY,
                'Cache-Control': 'no-cache'
              }
            });
            if (response != null && (_response$data2 = response.data) != null && (_response$data2 = _response$data2.data) != null && (_response$data2 = _response$data2.getResultScreenByFilterDineAndShop) != null && _response$data2.nodes) {
              var _response$data3;
              var dataList = response == null || (_response$data3 = response.data) == null || (_response$data3 = _response$data3.data) == null || (_response$data3 = _response$data3.getResultScreenByFilterDineAndShop) == null ? undefined : _response$data3.nodes;
              setDataInDay();
              (0, _mmkvStorage.setDataDineDirectoryInday)(JSON.stringify(dataList));
              setData(dataList);
              setDataOriginal(dataList);
              setLoading(false);
            } else {
              setLoading(false);
              setError(_dineShopDirectory.ErrorType.ErrorDefault);
            }
          } catch (error) {
            setLoading(false);
            setError(_dineShopDirectory.ErrorType.ErrorDefault);
          }
        }
      });
      return function getData() {
        return _ref4.apply(this, arguments);
      };
    }();
    var handlePressReloadError = function handlePressReloadError() {
      if (error === _dineShopDirectory.ErrorType.NoInternet) {
        checkInternet();
      } else {
        getDataFilter();
        getData();
      }
    };
    var handlePressReloadFilter = function handlePressReloadFilter() {
      getDataFilter();
    };
    var setDataSubFilter = function setDataSubFilter(type) {
      var _dataFilter$find;
      var parentTagTitle = ((_dataFilter$find = dataFilter.find(function (item) {
        var _item$childTags;
        return (_item$childTags = item.childTags) == null ? undefined : _item$childTags.some(function (child) {
          return child.tagName === type;
        });
      })) == null ? undefined : _dataFilter$find.tagTitle) || "Location";
      var value = (0, _dineShopDirectoryUntil.toggleChildTagActive)(dataFilter, parentTagTitle, type);
      setDataFilter(value);
    };
    (0, _react.useEffect)(function () {
      if (dataFilter && (dataOriginal == null ? undefined : dataOriginal.length) > 0) {
        var _rootListRef$current;
        var dataValueAfterFilter = (0, _dineShopDirectoryUntil.filterDataListByDataFilterV2)(dataOriginal, dataFilter);
        setData(dataValueAfterFilter);
        (_rootListRef$current = rootListRef.current) == null || _rootListRef$current.scrollToOffset({
          offset: 0,
          animated: false
        });
      }
    }, [JSON.stringify(dataFilter)]);
    (0, _react.useEffect)(function () {
      if (queryFilterDataRef.current) {
        setLoading(true);
        setShowFilterModal(false);
        var initialDataFilter = (0, _dineShopDirectoryUntil.getInitialDataFilter)(queryFilterDataRef.current, initSelectedData);
        setDataFilter(initialDataFilter);
        setTimeout(function () {
          setLoading(false);
        }, 500);
      }
    }, [initSelectedData, oneLinkTimestamp]);
    return {
      navigation: navigation,
      error: error,
      loading: loading,
      data: data,
      handlePressReloadError: handlePressReloadError,
      showFilterModal: showFilterModal,
      openModalFilter: openModalFilter,
      closeModalFilter: closeModalFilter,
      errorFilter: errorFilter,
      loadingFilter: loadingFilter,
      dataFilter: dataFilter,
      setDataFilter: setDataFilter,
      handlePressReloadFilter: handlePressReloadFilter,
      dataFilterOriginal: dataFilterOriginal,
      rootListRef: rootListRef,
      setDataSubFilter: setDataSubFilter,
      perkItemOffsetListRef: perkItemOffsetListRef,
      rootItemOffsetRef: rootItemOffsetRef,
      offsetRecalculationCount: offsetRecalculationCount,
      setOffsetRecalculationCount: setOffsetRecalculationCount
    };
  };
