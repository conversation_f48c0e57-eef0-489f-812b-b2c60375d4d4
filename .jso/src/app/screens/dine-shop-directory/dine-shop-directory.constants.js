  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.TypeSubFilter = exports.Status = exports.ErrorType = exports.DineShopType = exports.COMPONENT_NAME = undefined;
  var COMPONENT_NAME = exports.COMPONENT_NAME = 'DineShopDirectory';
  var Status = exports.Status = /*#__PURE__*/function (Status) {
    Status["Open"] = "Open";
    Status["OpenSomeOutlets"] = "Open Some Outlets";
    return Status;
  }({});
  var ErrorType = exports.ErrorType = /*#__PURE__*/function (ErrorType) {
    ErrorType["ErrorDefault"] = "ErrorDefault";
    ErrorType["NoInternet"] = "NoInternet";
    return ErrorType;
  }({});
  var DineShopType = exports.DineShopType = /*#__PURE__*/function (DineShopType) {
    DineShopType["SHOP"] = "shop";
    DineShopType["DINE"] = "dine";
    return DineShopType;
  }({});
  var TypeSubFilter = exports.TypeSubFilter = /*#__PURE__*/function (TypeSubFilter) {
    TypeSubFilter["Public"] = "public";
    TypeSubFilter["Halal"] = "halal";
    TypeSubFilter["Transit"] = "transit";
    return TypeSubFilter;
  }({});
