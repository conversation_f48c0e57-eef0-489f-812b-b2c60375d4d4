  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ViewLoading = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _miffyGamificationBanner = _$$_REQUIRE(_dependencyMap[4]);
  var _constants = _$$_REQUIRE(_dependencyMap[5]);
  var _color = _$$_REQUIRE(_dependencyMap[6]);
  var _collapsibleHeader = _$$_REQUIRE(_dependencyMap[7]);
  var _styles = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  var data = [1, 2, 3, 4, 5];
  var ViewLoading = exports.ViewLoading = _react.default.memo(function (props) {
    var containerStyleProps = props.containerStyleProps;
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: [styles.container, containerStyleProps],
      children: data.map(function () {
        return (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.itemLoading,
          children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
            shimmerStyle: styles.imageLoading
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.viewColumn,
            children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
              shimmerStyle: styles.firstLoading
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
              shimmerStyle: styles.seccondLoading
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
              shimmerStyle: styles.thirdLoading
            })]
          })]
        }, Math.random());
      })
    });
  });
  var styles = _reactNative.StyleSheet.create({
    container: {
      marginTop: _collapsibleHeader.BACKGROUND_IMAGE_HEIGHT,
      paddingTop: 80,
      borderTopLeftRadius: _styles.FILTER_BORDER_RADIUS,
      borderTopRightRadius: _styles.FILTER_BORDER_RADIUS,
      height: "100%",
      overflow: "hidden",
      backgroundColor: _color.color.palette.whiteGrey,
      paddingHorizontal: 20,
      gap: 24
    },
    itemLoading: {
      width: '100%',
      flexDirection: 'row'
    },
    imageLoading: {
      width: 48,
      height: 48,
      borderRadius: 8,
      marginRight: 16
    },
    viewColumn: {
      flex: 1,
      gap: 12,
      paddingBottom: 24,
      borderBottomWidth: 1,
      borderColor: _color.color.palette.lighterGrey
    },
    firstLoading: {
      width: '100%',
      height: 12,
      borderRadius: 4
    },
    seccondLoading: {
      width: 148,
      height: 12,
      borderRadius: 4
    },
    thirdLoading: {
      width: 80,
      height: 12,
      borderRadius: 4
    }
  });
