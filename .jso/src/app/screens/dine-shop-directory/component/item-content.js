  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ItemContent = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _dineShopDirectory = _$$_REQUIRE(_dependencyMap[7]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _native = _$$_REQUIRE(_dependencyMap[10]);
  var _constants = _$$_REQUIRE(_dependencyMap[11]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var PERK_ITEM_FIXED_MARGIN = 12;
  var ItemContent = exports.ItemContent = _react.default.memo(function (props) {
    var _item$location_displa, _item$area_display;
    var navigation = (0, _native.useNavigation)();
    var item = props.item,
      index = props.index,
      offsetRecalculationCount = props.offsetRecalculationCount,
      perkItemOffsetListRef = props.perkItemOffsetListRef,
      setOffsetRecalculationCount = props.setOffsetRecalculationCount,
      dataLength = props.dataLength;
    var onPressItem = (0, _react.useCallback)(function () {
      switch (item == null ? undefined : item.tenantType) {
        case _dineShopDirectory.DineShopType.SHOP:
          navigation.navigate(_constants.NavigationConstants.shopDetailsScreen, {
            tenantId: item == null ? undefined : item.id,
            name: ""
          });
          break;
        case _dineShopDirectory.DineShopType.DINE:
          navigation.navigate(_constants.NavigationConstants.restaurantDetailScreen, {
            tenantId: item == null ? undefined : item.id,
            name: ""
          });
          break;
        default:
          navigation.navigate(_constants.NavigationConstants.shopDetailsScreen, {
            tenantId: item == null ? undefined : item.id,
            name: ""
          });
          break;
      }
    }, [item, navigation]);
    var renderStatus = function renderStatus() {
      if ((item == null ? undefined : item.openingStatus) === _dineShopDirectory.Status.Open) {
        return (0, _jsxRuntime.jsx)(_text.Text, {
          style: [styles.txtStatusGreen, styles.absoluteStyle],
          testID: `${_dineShopDirectory.COMPONENT_NAME}__Item__${index}__Status_Open`,
          children: item == null ? undefined : item.openingStatus
        });
      } else if ((item == null ? undefined : item.openingStatus) === _dineShopDirectory.Status.OpenSomeOutlets) {
        return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: [styles.viewSomeOutLet, styles.absoluteStyle],
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.txtStatusGreen,
            children: "Open"
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: [styles.txtStatusGray, {
              color: _theme.color.palette.darkGrey999
            }],
            children: "Some Outlets"
          })]
        });
      } else {
        return (0, _jsxRuntime.jsx)(_text.Text, {
          style: [styles.txtStatusGray, styles.absoluteStyle],
          testID: `${_dineShopDirectory.COMPONENT_NAME}__Item__${index}__Status_Closed`,
          children: item == null ? undefined : item.openingStatus
        });
      }
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      onPress: onPressItem,
      style: [styles.container, {
        marginTop: index === 0 ? 0 : 20
      }],
      activeOpacity: 1,
      onLayout: function onLayout(event) {
        var layoutHeight = event.nativeEvent.layout.height + PERK_ITEM_FIXED_MARGIN;
        if (!perkItemOffsetListRef) return;
        if (!(perkItemOffsetListRef != null && perkItemOffsetListRef.current)) {
          perkItemOffsetListRef.current = (0, _defineProperty2.default)({}, index, layoutHeight);
        } else {
          perkItemOffsetListRef.current[index] = layoutHeight;
        }
      },
      children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
        accessibilityLabel: `${_dineShopDirectory.COMPONENT_NAME}__Item__${index}__Icon`,
        source: {
          uri: item == null ? undefined : item.logoImage
        },
        style: styles.image,
        testID: `${_dineShopDirectory.COMPONENT_NAME}__Item__${index}__Icon`
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.viewContent,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.viewText,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.txtTitle,
            testID: `${_dineShopDirectory.COMPONENT_NAME}__Item__${index}__Title`,
            numberOfLines: 2,
            children: item == null ? undefined : item.title
          }), (item == null || (_item$location_displa = item.location_display) == null ? undefined : _item$location_displa.length) > 0 && (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.viewRowText,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.txtGate,
              testID: `${_dineShopDirectory.COMPONENT_NAME}__Item__${index}__Gate`,
              children: item == null ? undefined : item.location_display
            }), (item == null || (_item$area_display = item.area_display) == null ? undefined : _item$area_display.length) > 0 && (0, _jsxRuntime.jsxs)(_text.Text, {
              style: styles.txtStatusGate,
              testID: `${_dineShopDirectory.COMPONENT_NAME}__Item__${index}__GateStatus`,
              children: [" \xB7 ", item == null ? undefined : item.area_display]
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.viewRow,
            children: [(item == null ? undefined : item.staffPerk) === true && (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: styles.viewTagDefault,
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                tx: "dineShopDirectory.tagStaffPerks",
                style: styles.txtTagDefault
              })
            }), (item == null ? undefined : item.iscAvailable) === true && (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
              colors: _theme.color.palette.iShopChangiGradient,
              start: {
                x: 0,
                y: 0
              },
              end: {
                x: 1,
                y: 0
              },
              style: styles.viewTagGradient,
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                tx: "dineShopDirectory.tagAvailableonIShopChangi",
                style: styles.txtTagGradient
              })
            })]
          }), (item == null ? undefined : item.label) && (0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.txtService,
            testID: `${_dineShopDirectory.COMPONENT_NAME}__Item__${index}__Service`,
            numberOfLines: 2,
            children: item == null ? undefined : item.label
          })]
        }), renderStatus()]
      })]
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    container: {
      width: '100%',
      flexDirection: 'row',
      paddingHorizontal: 20
    },
    image: {
      width: 48,
      height: 48,
      borderRadius: 8,
      marginRight: 16
    },
    viewContent: {
      flex: 1,
      flexDirection: 'row',
      paddingBottom: 24,
      borderColor: _theme.color.palette.lighterGrey,
      borderBottomWidth: 1
    },
    viewText: {
      flex: 1
    },
    txtStatusGreen: {
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.basegreen,
      fontSize: 11,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 15,
      marginLeft: 16
    },
    txtStatusGray: {
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.darkestGrey,
      fontSize: 11,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 15,
      marginLeft: 16
    },
    txtTitle: {
      fontFamily: _theme.typography.black,
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 16,
      fontWeight: _reactNative2.Platform.select({
        ios: "900",
        android: "normal"
      }),
      lineHeight: 20,
      maxWidth: '80%'
    },
    viewRow: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4
    },
    txtGate: {
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 14,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 18
    },
    txtStatusGate: {
      fontFamily: _theme.typography.regular,
      color: _theme.color.palette.darkestGrey,
      fontSize: 14,
      fontWeight: _reactNative2.Platform.select({
        ios: "400",
        android: "normal"
      }),
      lineHeight: 18
    },
    txtService: {
      fontFamily: _theme.typography.regular,
      color: _theme.color.palette.darkestGrey,
      fontSize: 12,
      fontWeight: _reactNative2.Platform.select({
        ios: "400",
        android: "normal"
      }),
      lineHeight: 16
    },
    viewSomeOutLet: {
      alignItems: 'flex-end',
      gap: 2
    },
    absoluteStyle: {
      position: 'absolute',
      top: 0,
      right: 0
    },
    viewTagDefault: {
      paddingHorizontal: 6,
      paddingVertical: 2,
      borderRadius: 4,
      borderColor: _theme.color.palette.lightestPurple,
      borderWidth: 1,
      marginBottom: 4
    },
    viewTagGradient: {
      paddingHorizontal: 6,
      paddingVertical: 2,
      borderRadius: 4,
      marginBottom: 4
    },
    txtTagDefault: {
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.pink700,
      fontSize: 11,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 14
    },
    txtTagGradient: {
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.whiteGrey,
      fontSize: 11,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 14
    },
    viewRowText: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: 4,
      marginBottom: 12
    }
  });
