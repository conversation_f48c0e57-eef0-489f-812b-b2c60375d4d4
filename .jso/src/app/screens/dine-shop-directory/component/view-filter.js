  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ViewFilter = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _theme = _$$_REQUIRE(_dependencyMap[3]);
  var _icons = _$$_REQUIRE(_dependencyMap[4]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _native = _$$_REQUIRE(_dependencyMap[7]);
  var _dineShopDirectoryUntil = _$$_REQUIRE(_dependencyMap[8]);
  var _dineShopDirectory = _$$_REQUIRE(_dependencyMap[9]);
  var _icons2 = _$$_REQUIRE(_dependencyMap[10]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  var ViewFilter = exports.ViewFilter = _react.default.memo(function (props) {
    var _findChildTagByNameIn, _findChildTagByNameIn2, _dataFilter$, _dataFilter$2, _findChildTagByNameIn3, _findChildTagByNameIn4, _findChildTagByNameIn5;
    var containerStyle = props.containerStyle,
      openModalFilter = props.openModalFilter,
      errorFilter = props.errorFilter,
      loadingFilter = props.loadingFilter,
      dataFilter = props.dataFilter,
      setDataFilter = props.setDataFilter;
    var navigation = (0, _native.useNavigation)();
    var isOnlyShopActive = dataFilter && ((_findChildTagByNameIn = (0, _dineShopDirectoryUntil.findChildTagByNameInData)(dataFilter, 'shop')) == null ? undefined : _findChildTagByNameIn.isActive) === true && !((_findChildTagByNameIn2 = (0, _dineShopDirectoryUntil.findChildTagByNameInData)(dataFilter, 'dine')) != null && _findChildTagByNameIn2.isActive);
    var hasActive = dataFilter ? (0, _dineShopDirectoryUntil.hasActiveTagDataRoot)(dataFilter) : dataFilter;
    var hasTerminalActive = dataFilter ? (0, _dineShopDirectoryUntil.hasActiveTag)((_dataFilter$ = dataFilter[1]) == null ? undefined : _dataFilter$.childTags) : false;
    var txtLocation = dataFilter ? (0, _dineShopDirectoryUntil.getLocationTagTitles)((_dataFilter$2 = dataFilter[1]) == null ? undefined : _dataFilter$2.childTags) : '';
    var hasActivePublic = dataFilter ? (_findChildTagByNameIn3 = (0, _dineShopDirectoryUntil.findChildTagByNameInData)(dataFilter, _dineShopDirectory.TypeSubFilter.Public)) == null ? undefined : _findChildTagByNameIn3.isActive : false;
    var hasActiveHalal = dataFilter ? (_findChildTagByNameIn4 = (0, _dineShopDirectoryUntil.findChildTagByNameInData)(dataFilter, _dineShopDirectory.TypeSubFilter.Halal)) == null ? undefined : _findChildTagByNameIn4.isActive : false;
    var hasActiveTransit = dataFilter ? (_findChildTagByNameIn5 = (0, _dineShopDirectoryUntil.findChildTagByNameInData)(dataFilter, _dineShopDirectory.TypeSubFilter.Transit)) == null ? undefined : _findChildTagByNameIn5.isActive : false;
    var handleNavigateSearch = function handleNavigateSearch() {
      navigation.navigate("search", {
        focusTextInput: true
      });
    };
    var filterItem = function filterItem(type) {
      setDataFilter(type);
    };
    var renderTxtLocation = function renderTxtLocation() {
      if ((txtLocation == null ? undefined : txtLocation.length) > 0 && hasTerminalActive) return txtLocation;
      return "Location";
    };
    return (0, _jsxRuntime.jsx)(_reactNative2.ScrollView, {
      showsHorizontalScrollIndicator: false,
      horizontal: true,
      bounces: false,
      style: [styles.borderRadius, containerStyle],
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.container,
        children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: styles.viewItem,
          onPress: handleNavigateSearch,
          children: (0, _jsxRuntime.jsx)(_icons.SearchIconV2, {
            width: 16,
            height: 16,
            color: _theme.color.palette.darkestGrey
          })
        }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
          style: hasActive ? styles.viewItemActive : styles.viewItem,
          onPress: openModalFilter,
          children: [(0, _jsxRuntime.jsx)(_icons.FilterV2, {
            width: 16,
            height: 16,
            color: hasActive ? _theme.color.palette.lightPurple : _theme.color.palette.darkestGrey
          }), hasActive && (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.viewStatus
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
          style: [hasTerminalActive ? styles.viewItemActive : styles.viewItem, styles.viewItemRow],
          onPress: openModalFilter,
          children: [(0, _jsxRuntime.jsx)(_icons.LocationOutline, {
            width: 16,
            height: 16,
            color: hasTerminalActive ? _theme.color.palette.lightPurple : _theme.color.palette.darkestGrey
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: hasTerminalActive ? styles.txtTitleActive : styles.txtTitleInActive,
            children: renderTxtLocation()
          }), (0, _jsxRuntime.jsx)(_icons.ArrowDown, {
            width: 16,
            height: 16,
            color: hasTerminalActive ? _theme.color.palette.lightPurple : _theme.color.palette.darkestGrey
          })]
        }), !errorFilter && !loadingFilter && (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [(0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
            style: hasActivePublic ? styles.viewItemActive : styles.viewItem,
            onPress: function onPress() {
              return filterItem(_dineShopDirectory.TypeSubFilter.Public);
            },
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: hasActivePublic ? styles.txtTitleActive : styles.txtTitleInActive,
              children: "Public"
            }), hasActivePublic && (0, _jsxRuntime.jsx)(_icons2.CloseFilterMultipleNoti, {
              width: "8",
              height: "8"
            })]
          }), !isOnlyShopActive && (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
            style: hasActiveHalal ? styles.viewItemActive : styles.viewItem,
            onPress: function onPress() {
              return filterItem(_dineShopDirectory.TypeSubFilter.Halal);
            },
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: hasActiveHalal ? styles.txtTitleActive : styles.txtTitleInActive,
              children: "Halal"
            }), hasActiveHalal && (0, _jsxRuntime.jsx)(_icons2.CloseFilterMultipleNoti, {
              width: "8",
              height: "8"
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
            style: hasActiveTransit ? styles.viewItemActive : styles.viewItem,
            onPress: function onPress() {
              return filterItem(_dineShopDirectory.TypeSubFilter.Transit);
            },
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: hasActiveTransit ? styles.txtTitleActive : styles.txtTitleInActive,
              children: "Transit"
            }), hasActiveTransit && (0, _jsxRuntime.jsx)(_icons2.CloseFilterMultipleNoti, {
              width: "8",
              height: "8"
            })]
          })]
        })]
      })
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    borderRadius: {
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      backgroundColor: _theme.color.palette.almostWhiteGrey
    },
    container: {
      padding: 20,
      flexDirection: "row",
      alignItems: "center",
      gap: 4
    },
    viewItem: {
      height: 30,
      justifyContent: "center",
      alignItems: "center",
      paddingHorizontal: 10,
      borderRadius: 99,
      borderColor: _theme.color.palette.lighterGrey,
      borderWidth: 1
    },
    viewItemActive: {
      flexDirection: 'row',
      gap: 4,
      height: 30,
      justifyContent: "center",
      alignItems: "center",
      paddingHorizontal: 10,
      borderRadius: 99,
      borderColor: _theme.color.palette.purpleD5BBEA,
      borderWidth: 1,
      backgroundColor: _theme.color.palette.lightestPurple
    },
    viewItemRow: {
      flexDirection: "row",
      alignItems: "center",
      gap: 2
    },
    txtTitleInActive: {
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.darkestGrey,
      fontSize: 14,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 18
    },
    txtTitleActive: {
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.lightPurple,
      fontSize: 14,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 18
    },
    viewStatus: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: _theme.color.palette.lightPurple,
      borderWidth: 1,
      borderColor: _theme.color.palette.whiteGrey,
      position: "absolute",
      right: 0,
      top: 0
    }
  });
