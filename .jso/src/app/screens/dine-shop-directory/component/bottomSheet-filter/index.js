  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.BottomSheetFilter = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _styles = _$$_REQUIRE(_dependencyMap[6]);
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _icons = _$$_REQUIRE(_dependencyMap[8]);
  var _color = _$$_REQUIRE(_dependencyMap[9]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _viewItem = _$$_REQUIRE(_dependencyMap[11]);
  var _dineShopDirectoryUntil = _$$_REQUIRE(_dependencyMap[12]);
  var _viewError = _$$_REQUIRE(_dependencyMap[13]);
  var _dineShopDirectory = _$$_REQUIRE(_dependencyMap[14]);
  var _viewLoading = _$$_REQUIRE(_dependencyMap[15]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[16]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var BottomSheetFilter = exports.BottomSheetFilter = _react.default.memo(function (props) {
    var showFilterModal = props.showFilterModal,
      closeModalFilter = props.closeModalFilter,
      errorFilter = props.errorFilter,
      loadingFilter = props.loadingFilter,
      dataFilter = props.dataFilter,
      dataFilterOriginal = props.dataFilterOriginal,
      setDataFilter = props.setDataFilter,
      handlePressReloadFilter = props.handlePressReloadFilter;
    var _useState = (0, _react.useState)([]),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      data = _useState2[0],
      setData = _useState2[1];
    (0, _react.useEffect)(function () {
      if ((dataFilter == null ? undefined : dataFilter.length) > 0) {
        setData((0, _dineShopDirectoryUntil.moveAreaAboveLocation)(dataFilter));
      }
    }, [showFilterModal, dataFilter]);
    var closeModal = function closeModal() {
      closeModalFilter();
    };
    var renderItem = function renderItem(_ref) {
      var item = _ref.item,
        index = _ref.index;
      return (0, _jsxRuntime.jsx)(_viewItem.ViewItem, {
        data: data,
        item: item,
        index: index,
        handleOnClickItem: function handleOnClickItem(tagName) {
          return _handleOnClickItem(item, tagName);
        },
        handleClearSubItem: function handleClearSubItem() {
          return _handleClearSubItem(item);
        },
        handleClearLocation: handleClearLocation,
        handleClearCategory: function handleClearCategory() {
          return _handleClearCategory(item);
        }
      });
    };
    var handleClearLocation = function handleClearLocation() {
      setData(function (prev) {
        return prev.map(function (i) {
          return i.tagTitle === 'Area' || i.tagTitle === 'Location' ? Object.assign({}, i, {
            childTags: i.childTags.map(function (tag) {
              return Object.assign({}, tag, {
                isActive: false
              });
            })
          }) : i;
        });
      });
    };
    var _handleClearCategory = function _handleClearCategory(item) {
      if (item.tagTitle === "Category") {
        var _item$childTags$, _item$childTags$2, _item$childTags$3, _item$childTags$4;
        if ((item == null || (_item$childTags$ = item.childTags[0]) == null ? undefined : _item$childTags$.isActive) === true && (item == null || (_item$childTags$2 = item.childTags[1]) == null ? undefined : _item$childTags$2.isActive) === true) {
          var dataFilterForDine = (0, _dineShopDirectoryUntil.clearCategoryData)(data, dataFilterOriginal);
          setData(dataFilterForDine);
        } else if ((item == null || (_item$childTags$3 = item.childTags[0]) == null ? undefined : _item$childTags$3.isActive) === true) {
          var _dataFilterForDine = (0, _dineShopDirectoryUntil.revertFilterForDine)(data, dataFilterOriginal);
          setData(_dataFilterForDine);
        } else if ((item == null || (_item$childTags$4 = item.childTags[1]) == null ? undefined : _item$childTags$4.isActive) === true) {
          var dataFilterForShop = (0, _dineShopDirectoryUntil.revertFilterForShop)(data, dataFilterOriginal);
          setData(dataFilterForShop);
        }
      }
    };
    var _handleClearSubItem = function _handleClearSubItem(item) {
      setData(function (prev) {
        return prev.map(function (i) {
          return i.tagTitle === item.tagTitle ? Object.assign({}, i, {
            childTags: i.childTags.map(function (tag) {
              return Object.assign({}, tag, {
                isActive: false
              });
            })
          }) : i;
        });
      });
    };
    var _handleOnClickItem = function _handleOnClickItem(item, tagName) {
      if (item.tagTitle === "Location") {
        var locationKeys = ["jewel", "terminal 1", "terminal 2", "terminal 3", "terminal 4"];
        if (locationKeys.includes(tagName)) {
          setData(function (prev) {
            return prev.map(function (i) {
              if (i.tagTitle !== "Location") return i;
              var toggled = i.childTags.map(function (tag) {
                return tag.tagName === tagName ? Object.assign({}, tag, {
                  isActive: !tag.isActive
                }) : tag;
              });
              var allActive = locationKeys.every(function (key) {
                var _toggled$find;
                return (_toggled$find = toggled.find(function (t) {
                  return t.tagName === key;
                })) == null ? undefined : _toggled$find.isActive;
              });
              return Object.assign({}, i, {
                childTags: toggled.map(function (tag) {
                  return tag.tagName === "All" ? Object.assign({}, tag, {
                    isActive: allActive
                  }) : tag;
                })
              });
            });
          });
          return;
        }
      }
      if (tagName === "ALL") {
        setData(function (prev) {
          return prev.map(function (i) {
            return i.tagTitle === item.tagTitle ? Object.assign({}, i, {
              childTags: i.childTags.map(function (tag) {
                return Object.assign({}, tag, {
                  isActive: !i.childTags.every(function (t) {
                    return t.isActive;
                  })
                });
              })
            }) : i;
          });
        });
      } else if (tagName === "dine") {
        var _findChildTagByNameIn, _item$childTags$5, _item$childTags$6;
        if (((_findChildTagByNameIn = (0, _dineShopDirectoryUntil.findChildTagByNameInData)(data, 'shop')) == null ? undefined : _findChildTagByNameIn.isActive) === true && !(item != null && (_item$childTags$5 = item.childTags[0]) != null && _item$childTags$5.isActive)) {
          var dataFilterForDine = (0, _dineShopDirectoryUntil.revertFilterForDineAndShop)(data, dataFilterOriginal);
          setData(dataFilterForDine);
        } else if (item != null && (_item$childTags$6 = item.childTags[0]) != null && _item$childTags$6.isActive) {
          var _dataFilterForDine2 = (0, _dineShopDirectoryUntil.revertFilterForDine)(data, dataFilterOriginal);
          setData(_dataFilterForDine2);
        } else {
          var _dataFilterForDine3 = (0, _dineShopDirectoryUntil.filterForDine)(data);
          setData((0, _dineShopDirectoryUntil.toggleChildTagActive)(_dataFilterForDine3, item.tagTitle, tagName));
        }
      } else if (tagName === "shop") {
        var _findChildTagByNameIn2, _item$childTags$7, _item$childTags$8;
        if (((_findChildTagByNameIn2 = (0, _dineShopDirectoryUntil.findChildTagByNameInData)(data, 'dine')) == null ? undefined : _findChildTagByNameIn2.isActive) === true && !(item != null && (_item$childTags$7 = item.childTags[1]) != null && _item$childTags$7.isActive)) {
          var dataFilterForShop = (0, _dineShopDirectoryUntil.revertFilterForDineAndShop)(data, dataFilterOriginal);
          setData(dataFilterForShop);
        } else if (item != null && (_item$childTags$8 = item.childTags[1]) != null && _item$childTags$8.isActive) {
          var _dataFilterForShop = (0, _dineShopDirectoryUntil.revertFilterForShop)(data, dataFilterOriginal);
          setData(_dataFilterForShop);
        } else {
          var _dataFilterForShop2 = (0, _dineShopDirectoryUntil.filterForShop)(data);
          setData((0, _dineShopDirectoryUntil.toggleChildTagActive)(_dataFilterForShop2, item.tagTitle, tagName));
        }
      } else {
        setData((0, _dineShopDirectoryUntil.toggleChildTagActive)(data, item.tagTitle, tagName));
      }
    };
    var clearAllData = function clearAllData() {
      setData(dataFilterOriginal);
    };
    var renderContent = function renderContent() {
      if (errorFilter) {
        return (0, _jsxRuntime.jsx)(_viewError.ViewError, {
          typeError: _dineShopDirectory.ErrorType.ErrorDefault,
          handlePressReload: handlePressReloadFilter
        });
      } else if (loadingFilter) {
        return (0, _jsxRuntime.jsx)(_viewLoading.ViewLoading, {
          containerStyleProps: _styles.styles.viewLoading
        });
      } else {
        return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _styles.styles.viewContent,
            children: (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
              data: data,
              renderItem: renderItem,
              showsVerticalScrollIndicator: false,
              keyExtractor: function keyExtractor(item, index) {
                return item == null ? undefined : item.tagTitle;
              },
              contentContainerStyle: {
                gap: 50
              },
              ListFooterComponent: function ListFooterComponent() {
                return (0, _jsxRuntime.jsx)(_reactNative2.View, {
                  style: _styles.styles.viewBottom
                });
              }
            })
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _styles.styles.buttonContainer,
            children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _styles.styles.flexRow,
              children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                onPress: clearAllData,
                children: (0, _jsxRuntime.jsx)(_text.Text, {
                  tx: "dineShopFilter.clearAll-v2",
                  style: _styles.styles.txtClearAll
                })
              }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                onPress: function onPress() {
                  setDataFilter(data);
                  closeModal();
                },
                children: (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
                  style: _styles.styles.buttonGradient,
                  start: {
                    x: 1,
                    y: 0
                  },
                  end: {
                    x: 0,
                    y: 1
                  },
                  colors: [_color.color.palette.gradientColor1End, _color.color.palette.gradientColor1Start],
                  children: (0, _jsxRuntime.jsx)(_text.Text, {
                    tx: "dineShopFilter.applyFilters-v2",
                    style: _styles.styles.txtApply
                  })
                })
              })]
            })
          })]
        });
      }
    };
    return (0, _jsxRuntime.jsx)(_bottomSheet.default, {
      isModalVisible: showFilterModal,
      containerStyle: _styles.styles.bottomSheetContainer,
      onClosedSheet: closeModal,
      stopDragCollapse: true,
      onBackPressHandle: closeModal,
      animationInTiming: 100,
      animationOutTiming: 100,
      onModalHide: function onModalHide() {},
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _styles.styles.container,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _styles.styles.headerFilter,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            tx: "dineShopFilter.titleHeader",
            style: _styles.styles.filterTitle
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: closeModal,
            style: _styles.styles.btnCloseStyles,
            children: (0, _jsxRuntime.jsx)(_icons.CloseCross, {
              width: 24,
              height: 24
            })
          })]
        }), (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
          children: renderContent()
        })]
      })
    });
  });
