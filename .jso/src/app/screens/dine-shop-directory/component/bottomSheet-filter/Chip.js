  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.Chip = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _color = _$$_REQUIRE(_dependencyMap[3]);
  var _typography = _$$_REQUIRE(_dependencyMap[4]);
  var _icons = _$$_REQUIRE(_dependencyMap[5]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  var Chip = exports.Chip = _react.default.memo(function (props) {
    var text = props.text,
      onClick = props.onClick,
      isActive = props.isActive,
      icon = props.icon;
    return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      onPress: onClick,
      style: isActive ? styles.containerActive : styles.containerInActive,
      children: [icon && icon, (0, _jsxRuntime.jsx)(_text.Text, {
        style: isActive ? styles.txtActive : styles.txtInActive,
        children: text
      }), isActive && (0, _jsxRuntime.jsx)(_icons.CloseFilterMultipleNoti, {
        width: 8,
        height: 8
      })]
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    containerInActive: {
      flexDirection: 'row',
      paddingHorizontal: 10,
      paddingVertical: 6,
      borderRadius: 99,
      borderWidth: 1,
      borderColor: _color.color.palette.lighterGrey,
      alignItems: 'center',
      gap: 4
    },
    containerActive: {
      flexDirection: 'row',
      paddingHorizontal: 10,
      paddingVertical: 6,
      borderRadius: 99,
      borderWidth: 1,
      backgroundColor: _color.color.palette.lightestPurple,
      borderColor: _color.color.palette.purpleD5BBEA,
      alignItems: 'center',
      gap: 4
    },
    txtActive: {
      fontFamily: _typography.typography.bold,
      color: _color.color.palette.lightPurple,
      fontSize: 14,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 18
    },
    txtInActive: {
      fontFamily: _typography.typography.bold,
      color: _color.color.palette.darkestGrey,
      fontSize: 14,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 18
    }
  });
