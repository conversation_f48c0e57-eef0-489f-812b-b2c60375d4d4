  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    bottomSheetContainer: {
      height: "90%",
      margin: 0,
      position: "absolute",
      bottom: 0,
      left: 0,
      right: 0
    },
    container: {
      flex: 1,
      backgroundColor: _theme.color.palette.whiteGrey,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16
    },
    btnCloseStyles: {
      position: 'absolute',
      right: 24
    },
    headerFilter: {
      flexDirection: "row",
      justifyContent: "center",
      marginVertical: 21
    },
    filterTitle: {
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 16,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 20
    },
    viewContent: {
      flex: 1,
      paddingHorizontal: 20
    },
    buttonContainer: {
      borderTopWidth: 1,
      borderColor: _theme.color.palette.lighterGrey,
      paddingHorizontal: 24,
      paddingTop: 16,
      paddingBottom: 40,
      backgroundColor: _theme.color.palette.whiteGrey
    },
    flexRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center"
    },
    buttonGradient: {
      width: 176,
      height: 44,
      backgroundColor: _theme.color.palette.basePurple,
      borderRadius: 60,
      marginBottom: 7,
      justifyContent: 'center',
      alignItems: 'center'
    },
    txtApply: {
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.whiteGrey,
      fontSize: 16,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 20
    },
    txtClearAll: {
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.lightPurple,
      fontSize: 14,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 18
    },
    viewBottom: {
      height: 40
    },
    viewLoading: {
      paddingTop: 0,
      marginTop: 80
    }
  });
