  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ViewEmpty = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _icons = _$$_REQUIRE(_dependencyMap[5]);
  var _styles = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  var ViewEmpty = exports.ViewEmpty = _react.default.memo(function (props) {
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.container,
      children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.container,
        children: [(0, _jsxRuntime.jsx)(_icons.DineEmpty, {
          width: 120,
          height: 120
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.titleTextStyle,
          tx: 'dineShopDirectory.titleEmpty'
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.messageTextStyle,
          tx: 'dineShopDirectory.contentEmpty'
        })]
      })
    });
  });
  var styles = _reactNative.StyleSheet.create({
    container: {
      marginTop: 26,
      borderTopLeftRadius: _styles.FILTER_BORDER_RADIUS,
      borderTopRightRadius: _styles.FILTER_BORDER_RADIUS,
      height: "100%",
      overflow: "hidden",
      paddingHorizontal: 24,
      alignItems: 'center'
    },
    titleTextStyle: Object.assign({}, _text.presets.h2, {
      marginBottom: 16,
      marginTop: 40,
      textAlign: "center"
    }),
    messageTextStyle: Object.assign({}, _text.presets.caption1Regular, {
      color: _theme.color.palette.darkestGrey,
      textAlign: "center"
    }),
    reloadButtonStyle: {
      width: '100%',
      borderRadius: 60,
      paddingHorizontal: 24,
      marginTop: 24
    }
  });
