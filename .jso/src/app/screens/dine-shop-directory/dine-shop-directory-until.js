  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.toggleChildTagActive = exports.revertFilterForShop = exports.revertFilterForDineAndShop = exports.revertFilterForDine = exports.resetActiveAreaLocation = exports.moveAreaAboveLocation = exports.hasActiveTagDataRoot = exports.hasActiveTag = exports.getLocationTagTitles = exports.getInitialDataFilter = exports.findChildTagByNameInData = exports.filterForShop = exports.filterForDine = exports.filterDataListByDataFilterV2 = exports.clearCategoryData = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _objectWithoutProperties2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _excluded = ["areas", "locations"];
  var filterDataListByDataFilterV2 = exports.filterDataListByDataFilterV2 = function filterDataListByDataFilterV2(dataList, dataFilter) {
    var _areaGroup$childTags, _locationGroup$childT;
    var areaGroup = dataFilter.find(function (g) {
      return g.tagTitle === "Area";
    });
    var locationGroup = dataFilter.find(function (g) {
      return g.tagTitle === "Location";
    });
    var areaActive = areaGroup == null || (_areaGroup$childTags = areaGroup.childTags) == null ? undefined : _areaGroup$childTags.filter(function (tag) {
      return tag.isActive;
    });
    var locationActive = locationGroup == null || (_locationGroup$childT = locationGroup.childTags) == null ? undefined : _locationGroup$childT.filter(function (tag) {
      return tag.isActive;
    });
    var filterMap = Object.fromEntries(dataFilter.map(function (group) {
      var _group$childTags$;
      if (!Array.isArray(group.childTags)) return null;
      var activeTags = group.childTags.filter(function (tag) {
        return tag.isActive;
      }).map(function (tag) {
        return tag.tagName;
      });
      var filterType = (_group$childTags$ = group.childTags[0]) == null ? undefined : _group$childTags$.filterType;
      if (["Dine Category & Cuisine", "Shop Category"].includes(group.tagTitle)) filterType = "categories";
      return activeTags.length && filterType ? [filterType, activeTags] : null;
    }).filter(Boolean));
    if (areaActive != null && areaActive.length && locationActive != null && locationActive.length) {
      var _filterMap = filterMap,
        areas = _filterMap.areas,
        locations = _filterMap.locations,
        rest = (0, _objectWithoutProperties2.default)(_filterMap, _excluded);
      var combined = [];
      for (var loc of locationActive) {
        for (var area of areaActive) {
          combined.push(`${(loc.tagName + "").trim().toLowerCase()}|${(area.tagName + "").trim().toLowerCase()}`);
        }
      }
      filterMap = Object.assign({}, rest, {
        location_areas: combined
      });
    }
    if (!Object.keys(filterMap).length) return dataList;
    return dataList.filter(function (item) {
      return Array.isArray(item.filter) && Object.entries(filterMap).every(function (_ref) {
        var _ref2 = (0, _slicedToArray2.default)(_ref, 2),
          filterType = _ref2[0],
          activeTags = _ref2[1];
        var filterObj = item.filter.find(function (f) {
          return f.main === filterType;
        });
        return filterObj && Array.isArray(filterObj.child) && filterObj.child.some(function (child) {
          if (typeof child === "string") {
            return activeTags.includes((child + "").trim().toLowerCase());
          } else {
            return activeTags.includes((child.tagName + "").trim().toLowerCase());
          }
        });
      });
    });
  };
  var clearCategoryData = exports.clearCategoryData = function clearCategoryData(currentData, originalData) {
    return currentData.map(function (item) {
      if (item.tagTitle === "Category") {
        return Object.assign({}, item, {
          childTags: item.childTags.map(function (child) {
            return child.tagName === "dine" || child.tagName === "shop" ? Object.assign({}, child, {
              isActive: false
            }) : child;
          })
        });
      }
      return item;
    });
  };
  var moveAreaAboveLocation = exports.moveAreaAboveLocation = function moveAreaAboveLocation(arr) {
    var areaIdx = arr.findIndex(function (item) {
      return item.tagTitle === "Area";
    });
    var locationIdx = arr.findIndex(function (item) {
      return item.tagTitle === "Location";
    });
    if (areaIdx === -1 || locationIdx === -1 || areaIdx < locationIdx) return arr;
    if (arr[locationIdx] && Array.isArray(arr[locationIdx].childTags)) {
      var hasAll = arr[locationIdx].childTags.some(function (tag) {
        return tag.tagTitle === "All";
      });
      if (!hasAll) {
        arr[locationIdx].childTags.unshift({
          tagTitle: "All",
          tagName: "All",
          filterType: "locations"
        });
      }
    }
    var _arr$splice = arr.splice(areaIdx, 1),
      _arr$splice2 = (0, _slicedToArray2.default)(_arr$splice, 1),
      areaObj = _arr$splice2[0];
    arr.splice(locationIdx, 0, areaObj);
    return arr;
  };
  var toggleChildTagActive = exports.toggleChildTagActive = function toggleChildTagActive(data, parentTagTitle, childTagName) {
    return data.map(function (item) {
      if (item.tagTitle !== parentTagTitle) return item;
      return Object.assign({}, item, {
        childTags: item.childTags.map(function (tag) {
          return tag.tagName === childTagName ? Object.assign({}, tag, {
            isActive: !tag.isActive
          }) : tag;
        })
      });
    });
  };
  var resetActiveAreaLocation = exports.resetActiveAreaLocation = function resetActiveAreaLocation(data) {
    return data.map(function (item) {
      if (item.tagTitle === "Area" || item.tagTitle === "Location") {
        return Object.assign({}, item, {
          childTags: item.childTags.map(function (child) {
            return Object.assign({}, child, {
              isActive: false
            });
          })
        });
      }
      return item;
    });
  };
  var filterForDine = exports.filterForDine = function filterForDine(data) {
    return data.filter(function (item) {
      return item.tagTitle !== "Shop Category";
    }).map(function (item) {
      return item.tagTitle === "Availability" && Array.isArray(item.childTags) ? Object.assign({}, item, {
        childTags: item.childTags.filter(function (child) {
          return child.tagTitle !== "Available on iShopChangi";
        })
      }) : item;
    });
  };
  var revertFilterForDine = exports.revertFilterForDine = function revertFilterForDine(currentData, originalData) {
    var _currentData$find;
    var isShopActive = (_currentData$find = currentData.find(function (i) {
      return i.tagTitle === "Category";
    })) == null || (_currentData$find = _currentData$find.childTags) == null || (_currentData$find = _currentData$find.find(function (c) {
      return c.tagName === "shop";
    })) == null ? undefined : _currentData$find.isActive;
    return originalData.reduce(function (acc, origItem) {
      if (isShopActive && (origItem.tagTitle === "Dine Category & Cuisine" || origItem.tagTitle === "Dietary Options")) return acc;
      if (origItem.tagTitle === "Shop Category") {
        var _currentItem = currentData.find(function (i) {
          return i.tagTitle === "Shop Category";
        });
        return acc.concat(_currentItem || origItem);
      }
      if (origItem.tagTitle === "Availability") {
        var _currentItem2 = currentData.find(function (i) {
          return i.tagTitle === "Availability";
        });
        var childTags = origItem.childTags;
        if (isShopActive) childTags = childTags.filter(function (child) {
          return child.tagTitle !== "Online Reservations";
        });
        return acc.concat(Object.assign({}, origItem, {
          childTags: childTags.map(function (child) {
            var _currentItem2$childTa;
            var cur = _currentItem2 == null || (_currentItem2$childTa = _currentItem2.childTags) == null ? undefined : _currentItem2$childTa.find(function (c) {
              return c.tagTitle === child.tagTitle;
            });
            return cur ? Object.assign({}, child, {
              isActive: cur.isActive
            }) : child;
          })
        }));
      }
      if (origItem.tagTitle === "Category") {
        var _currentItem3 = currentData.find(function (i) {
          return i.tagTitle === "Category";
        });
        return acc.concat(Object.assign({}, origItem, {
          childTags: origItem.childTags.map(function (child) {
            var _currentItem3$childTa, _currentItem3$childTa2;
            return child.tagName === "dine" ? Object.assign({}, child, {
              isActive: false
            }) : Object.assign({}, child, {
              isActive: (_currentItem3$childTa = _currentItem3 == null || (_currentItem3$childTa2 = _currentItem3.childTags) == null || (_currentItem3$childTa2 = _currentItem3$childTa2.find(function (c) {
                return c.tagName === child.tagName;
              })) == null ? undefined : _currentItem3$childTa2.isActive) != null ? _currentItem3$childTa : child.isActive
            });
          })
        }));
      }
      var currentItem = currentData.find(function (i) {
        return i.tagTitle === origItem.tagTitle;
      });
      return acc.concat(currentItem || origItem);
    }, []);
  };
  var filterForShop = exports.filterForShop = function filterForShop(data) {
    return data.filter(function (item) {
      return item.tagTitle !== "Dietary Options" && item.tagTitle !== "Dine Category & Cuisine";
    }).map(function (item) {
      return item.tagTitle === "Availability" && Array.isArray(item.childTags) ? Object.assign({}, item, {
        childTags: item.childTags.filter(function (child) {
          return child.tagTitle !== "Online Reservations";
        })
      }) : item;
    });
  };
  var revertFilterForShop = exports.revertFilterForShop = function revertFilterForShop(currentData, originalData) {
    var _currentData$find2;
    var isDineActive = (_currentData$find2 = currentData.find(function (i) {
      return i.tagTitle === "Category";
    })) == null || (_currentData$find2 = _currentData$find2.childTags) == null || (_currentData$find2 = _currentData$find2.find(function (c) {
      return c.tagName === "dine";
    })) == null ? undefined : _currentData$find2.isActive;
    return originalData.reduce(function (acc, origItem) {
      if (isDineActive && origItem.tagTitle === "Shop Category") return acc;
      if (origItem.tagTitle === "Dietary Options" || origItem.tagTitle === "Dine Category & Cuisine") {
        var _currentItem4 = currentData.find(function (i) {
          return i.tagTitle === origItem.tagTitle;
        });
        return acc.concat(_currentItem4 || origItem);
      }
      if (origItem.tagTitle === "Availability") {
        var _currentItem5 = currentData.find(function (i) {
          return i.tagTitle === "Availability";
        });
        var childTags = origItem.childTags;
        if (isDineActive) {
          childTags = childTags.filter(function (child) {
            return child.tagTitle !== "Available on iShopChangi";
          });
        }
        return acc.concat(Object.assign({}, origItem, {
          childTags: childTags.map(function (child) {
            var _currentItem5$childTa;
            var cur = _currentItem5 == null || (_currentItem5$childTa = _currentItem5.childTags) == null ? undefined : _currentItem5$childTa.find(function (c) {
              return c.tagTitle === child.tagTitle;
            });
            return cur ? Object.assign({}, child, {
              isActive: cur.isActive
            }) : child;
          })
        }));
      }
      if (origItem.tagTitle === "Category") {
        var _currentItem6 = currentData.find(function (i) {
          return i.tagTitle === "Category";
        });
        return acc.concat(Object.assign({}, origItem, {
          childTags: origItem.childTags.map(function (child) {
            var _currentItem6$childTa, _currentItem6$childTa2;
            return child.tagName === "shop" ? Object.assign({}, child, {
              isActive: false
            }) : Object.assign({}, child, {
              isActive: (_currentItem6$childTa = _currentItem6 == null || (_currentItem6$childTa2 = _currentItem6.childTags) == null || (_currentItem6$childTa2 = _currentItem6$childTa2.find(function (c) {
                return c.tagName === child.tagName;
              })) == null ? undefined : _currentItem6$childTa2.isActive) != null ? _currentItem6$childTa : child.isActive
            });
          })
        }));
      }
      var currentItem = currentData.find(function (i) {
        return i.tagTitle === origItem.tagTitle;
      });
      return acc.concat(currentItem || origItem);
    }, []);
  };
  var revertFilterForDineAndShop = exports.revertFilterForDineAndShop = function revertFilterForDineAndShop(currentData, originalData) {
    var _cat$childTags, _cat$childTags2;
    var cat = currentData.find(function (i) {
      return i.tagTitle === "Category";
    });
    var isShopActive = cat == null || (_cat$childTags = cat.childTags) == null || (_cat$childTags = _cat$childTags.find(function (c) {
      return c.tagName === "shop";
    })) == null ? undefined : _cat$childTags.isActive;
    var isDineActive = cat == null || (_cat$childTags2 = cat.childTags) == null || (_cat$childTags2 = _cat$childTags2.find(function (c) {
      return c.tagName === "dine";
    })) == null ? undefined : _cat$childTags2.isActive;
    return originalData.map(function (origItem) {
      if (isDineActive && isShopActive && origItem.tagTitle === "Availability") {
        var _cur = currentData.find(function (i) {
          return i.tagTitle === "Availability";
        });
        return Object.assign({}, origItem, {
          childTags: origItem.childTags.map(function (child) {
            var _cur$childTags;
            var c = _cur == null || (_cur$childTags = _cur.childTags) == null ? undefined : _cur$childTags.find(function (x) {
              return x.tagName === child.tagName;
            });
            return c && typeof c.isActive !== "undefined" ? Object.assign({}, child, {
              isActive: c.isActive
            }) : child;
          })
        });
      }
      if (isDineActive && ["Dine Category & Cuisine", "Dietary Options"].includes(origItem.tagTitle)) {
        return currentData.find(function (i) {
          return i.tagTitle === origItem.tagTitle;
        }) || origItem;
      }
      if (isShopActive && origItem.tagTitle === "Shop Category") {
        return currentData.find(function (i) {
          return i.tagTitle === "Shop Category";
        }) || origItem;
      }
      if (origItem.tagTitle === "Category") {
        var _cur2 = currentData.find(function (i) {
          return i.tagTitle === "Category";
        });
        return Object.assign({}, origItem, {
          childTags: origItem.childTags.map(function (child) {
            var _cur2$childTags$find$, _cur2$childTags;
            return ["dine", "shop"].includes(child.tagName) ? Object.assign({}, child, {
              isActive: true
            }) : Object.assign({}, child, {
              isActive: (_cur2$childTags$find$ = _cur2 == null || (_cur2$childTags = _cur2.childTags) == null || (_cur2$childTags = _cur2$childTags.find(function (c) {
                return c.tagName === child.tagName;
              })) == null ? undefined : _cur2$childTags.isActive) != null ? _cur2$childTags$find$ : child.isActive
            });
          })
        });
      }
      var cur = currentData.find(function (i) {
        return i.tagTitle === origItem.tagTitle;
      });
      if (!cur || !Array.isArray(origItem.childTags)) return origItem;
      return Object.assign({}, origItem, {
        childTags: origItem.childTags.map(function (child) {
          var _cur$childTags2;
          var c = (_cur$childTags2 = cur.childTags) == null ? undefined : _cur$childTags2.find(function (x) {
            return x.tagName === child.tagName;
          });
          return c && typeof c.isActive !== "undefined" ? Object.assign({}, child, {
            isActive: c.isActive
          }) : child;
        })
      });
    });
  };
  var hasActiveTag = exports.hasActiveTag = function hasActiveTag(childTags) {
    return childTags.some(function (tag) {
      return tag.isActive === true;
    });
  };
  var hasActiveTagDataRoot = exports.hasActiveTagDataRoot = function hasActiveTagDataRoot(data) {
    return data.some(function (item) {
      return Array.isArray(item.childTags) && item.childTags.some(function (child) {
        return child.isActive === true;
      });
    });
  };
  var getLocationTagTitles = exports.getLocationTagTitles = function getLocationTagTitles(childTags) {
    var filtered = childTags.filter(function (tag) {
      return tag.tagTitle !== "All" && tag.isActive === true;
    });
    // Custom sort: T* first, others, Jewel last
    var sorted = filtered.sort(function (a, b) {
      var isT = function isT(x) {
        return /^T\d$/i.test(x.tagTitle.trim());
      };
      if (isT(a) && !isT(b)) return -1;
      if (!isT(a) && isT(b)) return 1;
      if (a.tagTitle === "Jewel") return 1;
      if (b.tagTitle === "Jewel") return -1;
      return 0;
    });
    return sorted.map(function (tag) {
      return tag.tagTitle;
    }).join(", ");
  };
  var findChildTagByNameInData = exports.findChildTagByNameInData = function findChildTagByNameInData(dataFilter, name) {
    for (var item of dataFilter) {
      if (Array.isArray(item.childTags)) {
        var found = item.childTags.find(function (tag) {
          return tag.tagName === name;
        });
        if (found) return found;
      }
    }
    return undefined;
  };
  var getInitialDataFilter = exports.getInitialDataFilter = function getInitialDataFilter(queryFilterData, initSelectedData) {
    if (!Array.isArray(queryFilterData) || (queryFilterData == null ? undefined : queryFilterData.length) === 0) return queryFilterData;
    if (!Array.isArray(initSelectedData) || initSelectedData.length === 0) return queryFilterData;
    var initialDataFilter = queryFilterData.map(function (item) {
      var _item$childTags;
      return Object.assign({}, item, {
        childTags: item == null || (_item$childTags = item.childTags) == null || _item$childTags.map == null ? undefined : _item$childTags.map(function (child) {
          return Object.assign({}, child, {
            isActive: !!(initSelectedData != null && initSelectedData.find != null && initSelectedData.find(function (i) {
              return (i == null ? undefined : i.tagName) === (child == null ? undefined : child.tagName);
            }))
          });
        })
      });
    });
    return initialDataFilter;
  };
