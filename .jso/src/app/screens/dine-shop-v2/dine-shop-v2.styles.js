  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = exports.calculateImageSize = exports.calculateBannerImageSize = exports.TOP_BAR_VISIBLE_TRIGGER_POINT = exports.STICKY_TAB_BAR_VISIBLE_TRIGGER_POINT = exports.MAXIMUM_INITIAL_PROMO_PERK_VISIBLE_HEIGHT = exports.INITIAL_VISIBLE_TAB_CONTENT_HEIGHT = exports.BANNER_IMAGE_HEIGHT = exports.BANNER_IMAGE_DEFAULT_HEIGHT = exports.BACKGROUND_COLOR = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    screenHeight = _Dimensions$get.height,
    screenWidth = _Dimensions$get.width;
  var BANNER_IMAGE_DEFAULT_HEIGHT = exports.BANNER_IMAGE_DEFAULT_HEIGHT = 328;
  var BANNER_REDUCED_HEIGHT_RATE = 0.5597826086956522;
  var BANNER_SIZE_RATE = 1.0190217391304348;
  var EXTENDED_BANNER_SIZE_RATE = 0.5095108695652174;
  var BG_IMAGE_SIZE_RATE = 0.390625;
  var BANNER_IMAGE_HEIGHT = exports.BANNER_IMAGE_HEIGHT = screenWidth / BANNER_SIZE_RATE;
  var BANNER_IMAGE_MARGIN_BOTTOM = -(screenWidth / BANNER_SIZE_RATE * 0.4402173913043478);
  var MAXIMUM_INITIAL_PROMO_PERK_VISIBLE_HEIGHT = exports.MAXIMUM_INITIAL_PROMO_PERK_VISIBLE_HEIGHT = screenHeight * 5 / 16;
  var INITIAL_VISIBLE_TAB_CONTENT_HEIGHT = exports.INITIAL_VISIBLE_TAB_CONTENT_HEIGHT = screenHeight - BANNER_IMAGE_HEIGHT - 16;
  var BACKGROUND_COLOR = exports.BACKGROUND_COLOR = "#1A2D7B";
  var TOP_BAR_VISIBLE_OFFSET_Y = 179;
  var BANNER_IMAGE_DEFAULT_TOP = -40;
  var TOP_BAR_VISIBLE_TRIGGER_POINT = exports.TOP_BAR_VISIBLE_TRIGGER_POINT = BANNER_IMAGE_DEFAULT_HEIGHT - TOP_BAR_VISIBLE_OFFSET_Y;
  var STICKY_TAB_BAR_VISIBLE_TRIGGER_POINT = exports.STICKY_TAB_BAR_VISIBLE_TRIGGER_POINT = 134;
  var BANNER_IMAGE_HEIGHT_EXTRA = 50;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    containerStyle: {
      height: "auto",
      width: screenWidth
    },
    bannerImageContainerStyle: {
      position: "absolute",
      top: BANNER_IMAGE_DEFAULT_TOP,
      flex: 1,
      bottom: 0,
      left: 0
    },
    bannerImageDefaultStyle: {
      height: screenWidth / EXTENDED_BANNER_SIZE_RATE,
      width: screenWidth
    },
    bgImageDefaultStyle: {
      marginTop: -50,
      height: screenWidth / BG_IMAGE_SIZE_RATE,
      width: screenWidth
    },
    bgImageContainerStyle: {
      marginTop: BANNER_IMAGE_HEIGHT + BANNER_IMAGE_MARGIN_BOTTOM
    },
    presentImageStyle: {
      height: 89,
      position: "absolute",
      right: screenWidth * 78.6 / 375 + 6,
      top: -48,
      width: 85
    },
    textCopyTextStyle: Object.assign({}, _text.newPresets.caption1Bold, {
      alignItems: "center",
      marginBottom: 14,
      marginTop: 52,
      minHeight: 36,
      marginHorizontal: 24,
      textAlign: "center"
    }),
    promotionPerkWrapperStyle: {
      overflow: "hidden"
    },
    promotionPerkContainerStyle: {
      gap: 12,
      paddingBottom: 28,
      padding: 16
    },
    promotionPerkContainerNoPaddingTopStyle: {
      gap: 12,
      marginTop: -15
    },
    dineShopPageContent: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      marginTop: -16,
      overflow: "hidden"
    },
    viewAllPromosContainerStyle: {
      alignItems: "center",
      marginTop: -16,
      paddingVertical: 24,
      position: "absolute",
      top: 0,
      transform: [{
        translateY: -72
      }],
      width: "100%"
    },
    viewAllPromosBtnStyle: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.almostBlackGrey,
      borderRadius: 60,
      flexDirection: "row",
      gap: 4,
      height: 32,
      paddingHorizontal: 16,
      paddingVertical: 8
    },
    viewAllPromosBtnLabelStyle: Object.assign({}, _text.newPresets.caption2Bold, {
      color: _theme.color.palette.whiteGrey
    }),
    topBarContainerStyle: {
      alignItems: "center",
      backgroundColor: "white",
      justifyContent: "flex-end",
      left: 0,
      position: "absolute",
      top: -4,
      width: "100%",
      zIndex: 99
    },
    topBarTitleTextStyle: Object.assign({}, _text.newPresets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey,
      marginBottom: 17,
      marginTop: 61
    }),
    noPromotionDisclaimer: Object.assign({}, _text.newPresets.caption1Bold, {
      fontSize: 11,
      alignItems: "center",
      paddingVertical: 8,
      marginHorizontal: 16,
      textAlign: "center",
      color: _theme.color.palette.whiteColorOpacity
    })
  });
  var calculateImageSize = exports.calculateImageSize = function calculateImageSize(width, height) {
    var sizeRate = width / height;
    var imageWidth = screenWidth;
    var imageHeight = screenWidth / sizeRate;
    return {
      height: imageHeight,
      width: imageWidth
    };
  };
  var calculateBannerImageSize = exports.calculateBannerImageSize = function calculateBannerImageSize(width, height) {
    var newSize = calculateImageSize(width, height);
    return Object.assign({}, newSize, {
      marginBottom: -(newSize.height * 0.4402173913043478 + 4)
    });
  };
