  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useInitializeData = exports.useFetchData = exports.useDineShopFlags = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[3]);
  var _react = _$$_REQUIRE(_dependencyMap[4]);
  var _utils = _$$_REQUIRE(_dependencyMap[5]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[8]);
  var _dineShop = _$$_REQUIRE(_dependencyMap[9]);
  var _native = _$$_REQUIRE(_dependencyMap[10]);
  var _useRewardTier2 = _$$_REQUIRE(_dependencyMap[11]);
  var _envParams = _$$_REQUIRE(_dependencyMap[12]);
  var _apis = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _request = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _pageConfig = _$$_REQUIRE(_dependencyMap[15]);
  var _aemGroupTwo = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[16]));
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var useDineShopFlags = exports.useDineShopFlags = function useDineShopFlags() {
    var _useContext = (0, _react.useContext)(_dineShop.DineShopContext),
      shopDineEpicFFlag = _useContext.shopDineEpicFFlag;
    var isShopDineEpicV2On = (0, _remoteConfig.getFeatureFlagInit)(_remoteConfig.REMOTE_CONFIG_FLAGS.SHOPDINE_EPIC, shopDineEpicFFlag);
    return {
      isShopDineEpicV2On: isShopDineEpicV2On
    };
  };
  var useFetchData = exports.useFetchData = function useFetchData() {
    var dispatch = (0, _reactRedux.useDispatch)();
    var fetchLandingPageAemData = function fetchLandingPageAemData() {
      dispatch(_aemGroupTwo.default.dineShopEpicLandingPageFetching());
      (0, _pageConfig.requestLandingPageAemData)();
    };
    return {
      fetchLandingPageAemData: fetchLandingPageAemData
    };
  };
  var useInitializeData = exports.useInitializeData = function useInitializeData() {
    var _useRewardTier = (0, _useRewardTier2.useRewardTier)(),
      memberIconInfo = _useRewardTier.memberIconInfo;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isRefresh = _useState2[0],
      setIsRefresh = _useState2[1];
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      showRefreshIndicator = _useState4[0],
      setShowRefreshIndicator = _useState4[1];
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var dineShopEpicLandingPagePayload = (0, _reactRedux.useSelector)(_aemGroupTwo.AemGroupTwoSelectors.dineShopEpicLandingPagePayload);
    var dineShopEpicLandingPageLoading = (0, _reactRedux.useSelector)(_aemGroupTwo.AemGroupTwoSelectors.dineShopEpicLandingPageLoading);
    var dineShopEpicLandingPageError = (0, _reactRedux.useSelector)(_aemGroupTwo.AemGroupTwoSelectors.dineShopEpicLandingPageError);
    var _useState5 = (0, _react.useState)(null),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      data = _useState6[0],
      setData = _useState6[1];
    var _useState7 = (0, _react.useState)(false),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      isLoading = _useState8[0],
      setLoading = _useState8[1];
    var _useState9 = (0, _react.useState)(false),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      isError = _useState0[0],
      setIsError = _useState0[1];
    var isFocused = (0, _native.useIsFocused)();
    var _useFetchData = useFetchData(),
      fetchLandingPageAemData = _useFetchData.fetchLandingPageAemData;
    var isFirstTimeLoading = (0, _react.useRef)(true);
    var _useState1 = (0, _react.useState)(true),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      expandDisabled = _useState10[0],
      setExpandDisabled = _useState10[1];
    var isRefreshFinished = (0, _react.useMemo)(function () {
      var isRequestLandingPageAemFinished = !dineShopEpicLandingPageLoading && (dineShopEpicLandingPagePayload || dineShopEpicLandingPageError);
      var isRequestPlayPassTilesFinished = !isLoading && ((0, _utils.ifOneTrue)([data]) || isError);
      return (0, _utils.ifAllTrue)([isRequestLandingPageAemFinished, isRequestPlayPassTilesFinished]);
    }, [JSON.stringify(dineShopEpicLandingPagePayload), dineShopEpicLandingPageLoading, dineShopEpicLandingPageError, data, isError, isLoading]);
    var initializeData = function initializeData(refreshing) {
      if (refreshing) {
        fetchLandingPageAemData();
      }
      getData();
    };
    var resetAllData = function resetAllData() {
      setData(null);
      setLoading(false);
      setIsError(false);
    };
    (0, _react.useEffect)(function () {
      setIsRefresh(true);
      return function () {
        resetAllData();
      };
    }, [isLoggedIn]);
    (0, _react.useEffect)(function () {
      if (isFocused && isRefresh) {
        if (!isFirstTimeLoading.current) {
          initializeData(true);
        } else {
          initializeData();
          isFirstTimeLoading.current = false;
        }
      }
    }, [isFocused, isRefresh]);
    var getData = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* () {
        setLoading(true);
        try {
          var _env, _env2;
          var inputData = {
            vehicleIU: profilePayload == null ? undefined : profilePayload.vehicleIU,
            tierCode: memberIconInfo == null ? undefined : memberIconInfo.title
          };
          var paramsArray = _apis.default.getEpicPerkV2.split(" ");
          var url = ((_env = (0, _envParams.env)()) == null ? undefined : _env.API_GATEWAY_URL) + paramsArray[1];
          var response = yield (0, _request.default)({
            url: url,
            method: paramsArray[0],
            data: inputData,
            headers: {
              "x-api-key": (_env2 = (0, _envParams.env)()) == null ? undefined : _env2.X_API_KEY
            }
          });
          setData(response == null ? undefined : response.data);
          enableExpandableView();
          setIsError(false);
        } catch (error) {
          setIsError(true);
        } finally {
          setLoading(false);
        }
      });
      return function getData() {
        return _ref.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      if ((0, _utils.ifAllTrue)([isRefresh, isRefreshFinished])) {
        setIsRefresh(false);
        setShowRefreshIndicator(false);
      }
    }, [isRefreshFinished]);
    var enableExpandableView = function enableExpandableView() {
      setTimeout(function () {
        setExpandDisabled(false);
      }, 2000);
    };
    return {
      isRefreshFinished: isRefreshFinished,
      setIsRefresh: setIsRefresh,
      setShowRefreshIndicator: setShowRefreshIndicator,
      showRefreshIndicator: showRefreshIndicator,
      data: data,
      isLoading: isLoading,
      isError: isError,
      expandDisabled: expandDisabled,
      setExpandDisabled: setExpandDisabled
    };
  };
