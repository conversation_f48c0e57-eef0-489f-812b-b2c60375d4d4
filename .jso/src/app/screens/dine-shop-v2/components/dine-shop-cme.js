  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[2]);
  var _baseImageBackground = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _navigationType = _$$_REQUIRE(_dependencyMap[6]);
  var _constants = _$$_REQUIRE(_dependencyMap[7]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[8]);
  var _backgrounds = _$$_REQUIRE(_dependencyMap[9]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[10]);
  var _dineShopV = _$$_REQUIRE(_dependencyMap[11]);
  var _isNil2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _adobe = _$$_REQUIRE(_dependencyMap[13]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[14]);
  var DineShopChangiMillionaireExperiences = function DineShopChangiMillionaireExperiences(props) {
    var data = props.data;
    var cm24RewardsInfo = data == null ? undefined : data.cmPerks;
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("DINE_SHOP_CME"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var getTotalMilChances = function getTotalMilChances() {
      var payloadValue = cm24RewardsInfo == null ? undefined : cm24RewardsInfo.totalMilChances;
      if ((0, _isNil2.default)(payloadValue)) return "N/A";
      return `${Math.min(Number(payloadValue), 999)}`;
    };
    var getTotalCarChances = function getTotalCarChances() {
      var payloadValue = cm24RewardsInfo == null ? undefined : cm24RewardsInfo.totalCarChances;
      if ((0, _isNil2.default)(payloadValue)) return "N/A";
      return `${Math.min(Number(payloadValue), 999)}`;
    };
    var handlePressCM24 = function handlePressCM24() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.EPICDineAndShop, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.EPICDineAndShop, "Changi Millionaire"));
      handleNavigation(_navigationType.NavigationTypeEnum.deepLink, _navigationHelper.ChangiGameValues.ChangiMillionaire, undefined, {
        redirectFrom: _constants.CM24RedirectSource.AccountCM24
      });
    };
    return (0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
      accessibilityLabel: `${_dineShopV.SCREEN_NAME}_Btn_ChangiMillionaireExperiences`,
      onPress: handlePressCM24,
      testID: `${_dineShopV.SCREEN_NAME}_Btn_ChangiMillionaireExperiences`,
      children: (0, _jsxRuntime.jsxs)(_baseImageBackground.default, {
        source: _backgrounds.CMEBackground,
        style: styles.bgImageStyle,
        children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
          source: _backgrounds.CMETitle,
          style: styles.titleImageStyle
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.rightSideContainerStyle,
          children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.infoLineContainerStyle,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.infoValueTextStyle,
              text: getTotalMilChances()
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              numberOfLines: 2,
              style: styles.infoLabelTextStyle,
              tx: "dineShopScreen.cmeMillionaireExperienceChances"
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.infoLineContainerStyle,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.infoValueTextStyle,
              text: getTotalCarChances()
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              numberOfLines: 2,
              style: styles.infoLabelTextStyle,
              tx: "dineShopScreen.cmeGrandDrawPrizeChances"
            })]
          })]
        })]
      })
    });
  };
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    screenWidth = _Dimensions$get.width;
  var CONTAINER_MARGIN_HORIZONTAL = 16;
  var SMALL_LARGE_SCREEN_WIDTH_BREAKPOINT = 375;
  var BG_IMAGE_SIZE_RATE = screenWidth >= SMALL_LARGE_SCREEN_WIDTH_BREAKPOINT ? 4.763888888888889 : 3.2282608695652173;
  var BG_IMAGE_RESPONSIVE_WIDTH = screenWidth - 32;
  var BG_IMAGE_RESPONSIVE_HEIGHT = BG_IMAGE_RESPONSIVE_WIDTH / BG_IMAGE_SIZE_RATE;
  var TITLE_IMAGE_SIZE_RATE = 3.7666666666666666;
  var TITLE_BG_SIZE_RATE = screenWidth >= SMALL_LARGE_SCREEN_WIDTH_BREAKPOINT ? 0.3294460641399417 : 0.38047138047138046;
  var TITLE_IMAGE_RESPONSIVE_WIDTH = BG_IMAGE_RESPONSIVE_WIDTH * TITLE_BG_SIZE_RATE;
  var TITLE_IMAGE_RESPONSIVE_HEIGHT = TITLE_IMAGE_RESPONSIVE_WIDTH / TITLE_IMAGE_SIZE_RATE;
  var styles = _reactNative.StyleSheet.create({
    bgImageStyle: {
      alignItems: "center",
      borderRadius: 16,
      flexDirection: "row",
      gap: 8,
      height: BG_IMAGE_RESPONSIVE_HEIGHT,
      minHeight: 76,
      overflow: "hidden",
      paddingHorizontal: 16,
      width: BG_IMAGE_RESPONSIVE_WIDTH
    },
    titleImageStyle: {
      height: TITLE_IMAGE_RESPONSIVE_HEIGHT,
      width: TITLE_IMAGE_RESPONSIVE_WIDTH
    },
    rightSideContainerStyle: {
      flex: 1,
      gap: 4
    },
    infoLineContainerStyle: {
      alignItems: "center",
      flexDirection: "row",
      gap: 4
    },
    infoValueTextStyle: Object.assign({}, _text.newPresets.bodyTextBold),
    infoLabelTextStyle: Object.assign({}, _text.newPresets.XSmallBold, {
      color: "rgba(252, 252, 252, 0.8)",
      flex: 1,
      textTransform: "none"
    }),
    loadingInfoValueStyle: {
      borderRadius: 4,
      height: 18,
      width: 28
    },
    loadingInfoLabelStyle: {
      borderRadius: 4,
      height: 18,
      width: 158
    }
  });
  var _default = exports.default = DineShopChangiMillionaireExperiences;
