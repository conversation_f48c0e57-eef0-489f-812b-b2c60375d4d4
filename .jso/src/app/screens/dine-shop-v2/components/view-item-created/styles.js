  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _color = _$$_REQUIRE(_dependencyMap[0]);
  var _typography = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    container: {
      width: "100%"
    },
    button: {
      alignItems: 'center',
      overflow: 'hidden',
      borderWidth: 1,
      borderColor: "rgba(252, 252, 252, 0.15)",
      borderRadius: 16
    },
    content: {
      padding: 16,
      flexDirection: "row",
      alignItems: "center"
    },
    img: {
      width: 67,
      height: 50,
      marginRight: 12
    },
    viewTxt: {
      flex: 1,
      marginRight: 12
    },
    viewRow: {
      flexDirection: "row"
    },
    space: {
      flex: 1
    },
    viewBackgroundText: {
      paddingVertical: 2,
      paddingHorizontal: 6,
      borderRadius: 4,
      backgroundColor: "#FCFCFC1A",
      marginBottom: 4
    },
    txtType: {
      fontFamily: _typography.typography.bold,
      color: _color.color.palette.almostWhiteGrey80,
      fontSize: 11,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 14,
      textAlignVertical: "center"
    },
    txtTitle: {
      fontFamily: _typography.typography.black,
      color: _color.color.palette.whiteGrey,
      fontSize: 14,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      }),
      marginRight: 4,
      lineHeight: 18,
      textAlignVertical: "center"
    },
    txtQty: {
      fontFamily: _typography.typography.black,
      color: _color.color.palette.whiteGrey,
      fontSize: 16,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      }),
      lineHeight: 20,
      textAlignVertical: "center"
    },
    viewIconRight: {
      flexDirection: "row",
      alignItems: "center"
    },
    lottieStyle: {
      width: 68,
      height: 20,
      position: "absolute",
      top: 3,
      left: -6
    },
    pngStyle: {
      width: 68,
      height: 20,
      position: "absolute",
      top: 3,
      left: -9.5
    }
  });
