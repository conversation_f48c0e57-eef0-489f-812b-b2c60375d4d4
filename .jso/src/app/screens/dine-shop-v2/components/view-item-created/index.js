  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ViewItem = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _styles = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _icons = _$$_REQUIRE(_dependencyMap[8]);
  var _lottieReactNative = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _EPICCredited = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[12]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var colorLinear = ["rgba(255, 255, 255, 0.12)", "rgba(255, 255, 255, 0.04)"];
  var ViewItem = exports.ViewItem = _react.default.memo(function (props) {
    var _item$tag, _item$header;
    var initAnimationInDay = (0, _mmkvStorage.getAnimationInDay)();
    var item = props.item,
      onPress = props.onPress,
      isCredited = props.isCredited;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      showIcon = _useState2[0],
      setShowIcon = _useState2[1];
    var _useState3 = (0, _react.useState)(null),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isAnimationIcon = _useState4[0],
      setIsAnimationIcon = _useState4[1];
    var setDayAnimation = function setDayAnimation() {
      var date = (0, _moment.default)().format("DD/MM/YYYY");
      (0, _mmkvStorage.setAnimationInDay)(date);
    };
    var onLayout = function onLayout() {
      if (initAnimationInDay !== (0, _moment.default)().format("DD/MM/YYYY")) {
        setIsAnimationIcon(true);
        setDayAnimation();
      } else {
        setIsAnimationIcon(false);
      }
      setShowIcon(true);
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _styles.styles.container,
      onLayout: onLayout,
      children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        onPress: onPress,
        style: _styles.styles.button,
        children: (0, _jsxRuntime.jsxs)(_reactNativeLinearGradient.default, {
          style: _styles.styles.content,
          colors: colorLinear,
          start: {
            x: 0.5,
            y: 0
          },
          end: {
            x: 0.5,
            y: 1
          },
          children: [(0, _jsxRuntime.jsx)(_reactNative2.Image, {
            style: _styles.styles.img,
            source: {
              uri: isCredited ? item == null ? undefined : item.image : item == null ? undefined : item.tokenImgUrl
            }
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _styles.styles.viewTxt,
            children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _styles.styles.viewRow,
              children: [((item == null || (_item$tag = item.tag) == null ? undefined : _item$tag.length) > 0 || (item == null || (_item$header = item.header) == null ? undefined : _item$header.length) > 0) && (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: _styles.styles.viewBackgroundText,
                children: (0, _jsxRuntime.jsx)(_text.Text, {
                  style: _styles.styles.txtType,
                  children: isCredited ? item == null ? undefined : item.tag : item == null ? undefined : item.header
                })
              }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: _styles.styles.space
              })]
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              style: _styles.styles.txtTitle,
              children: isCredited ? item == null ? undefined : item.title : (item == null ? undefined : item.tokenQty) === 1 ? item == null ? undefined : item.tokenNameSingular : item == null ? undefined : item.tokenNamePlural
            })]
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _styles.styles.viewIconRight,
            children: isCredited ? (0, _jsxRuntime.jsx)(_icons.ActiveParkingCredited, {}) : (0, _jsxRuntime.jsxs)(_text.Text, {
              style: _styles.styles.txtQty,
              children: ["+", item == null ? undefined : item.tokenQty]
            })
          })]
        })
      }), showIcon && (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
        children: isAnimationIcon ? (0, _jsxRuntime.jsx)(_lottieReactNative.default, {
          style: _styles.styles.lottieStyle,
          source: _EPICCredited.default,
          loop: false,
          autoPlay: true,
          resizeMode: "cover"
        }) : (0, _jsxRuntime.jsx)(_reactNative2.Image, {
          source: _icons.CreditedIcon,
          style: _styles.styles.pngStyle
        })
      })]
    });
  });
