  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var _useRewardTier2 = _$$_REQUIRE(_dependencyMap[2]);
  var _theme = _$$_REQUIRE(_dependencyMap[3]);
  var _dateTime = _$$_REQUIRE(_dependencyMap[4]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _react = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[7]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _i18n = _$$_REQUIRE(_dependencyMap[9]);
  var _reactNativeReanimated = _$$_REQUIRE(_dependencyMap[10]);
  var _reactNativeAnimateableText = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _native = _$$_REQUIRE(_dependencyMap[12]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[13]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[14]);
  var COUNTING_ANIMATION_THRESHOLD = 15;
  var _worklet_9804323813768_init_data = {
    code: "function dineShopEarnedForTodayTsx1(){const{animatedPointsAwarded}=this.__closure;let displayValue=\"\";if(animatedPointsAwarded.value===0){displayValue=\"\"+animatedPointsAwarded.value;}else if(animatedPointsAwarded.value){const getCurrencyDisplayValue=function(rawNumber){return rawNumber.toString().replace(/./g,function(c,i,a){return i>0&&c!==\".\"&&(a.length-i)%3===0?\",\"+c:c;});};displayValue=\"+\"+getCurrencyDisplayValue(Math.floor(animatedPointsAwarded.value));}return{text:displayValue};}"
  };
  var DineShopEarnedForToday = function DineShopEarnedForToday(props) {
    var isRefreshFinished = props.isRefreshFinished,
      data = props.data;
    var _useRewardTier = (0, _useRewardTier2.useRewardTier)(),
      memberIconInfo = _useRewardTier.memberIconInfo;
    var transactionSummaryData = data == null ? undefined : data.transactionSummary;
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var TierIcon = memberIconInfo == null ? undefined : memberIconInfo.smallIcon;
    var pointsAwarded = transactionSummaryData == null ? undefined : transactionSummaryData.totalPointsAwarded;
    var animatedPointsAwarded = (0, _reactNativeReanimated.useSharedValue)(null);
    var animatedProps = (0, _reactNativeReanimated.useAnimatedProps)(function () {
      var dineShopEarnedForTodayTsx1 = function dineShopEarnedForTodayTsx1() {
        var displayValue = "";
        if (animatedPointsAwarded.value === 0) {
          displayValue = `${animatedPointsAwarded.value}`;
        } else if (animatedPointsAwarded.value) {
          var getCurrencyDisplayValue = function getCurrencyDisplayValue(rawNumber) {
            return rawNumber.toString().replace(/./g, function (c, i, a) {
              return i > 0 && c !== "." && (a.length - i) % 3 === 0 ? "," + c : c;
            });
          };
          displayValue = `+${getCurrencyDisplayValue(Math.floor(animatedPointsAwarded.value))}`;
        }
        return {
          text: displayValue
        };
      };
      dineShopEarnedForTodayTsx1.__closure = {
        animatedPointsAwarded: animatedPointsAwarded
      };
      dineShopEarnedForTodayTsx1.__workletHash = 9804323813768;
      dineShopEarnedForTodayTsx1.__initData = _worklet_9804323813768_init_data;
      return dineShopEarnedForTodayTsx1;
    }());
    (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
      _reactNative.InteractionManager.runAfterInteractions(function () {
        if (!isRefreshFinished) return;
        if (!pointsAwarded || pointsAwarded <= 0) {
          animatedPointsAwarded.value = Number(pointsAwarded);
        } else {
          animatedPointsAwarded.value = Number(pointsAwarded) - COUNTING_ANIMATION_THRESHOLD;
          animatedPointsAwarded.value = (0, _reactNativeReanimated.withTiming)(Number(pointsAwarded), {
            duration: 1000,
            easing: _reactNativeReanimated.Easing.out(_reactNativeReanimated.Easing.quad)
          });
        }
      });
    }, [pointsAwarded, isRefreshFinished]));
    (0, _react.useEffect)(function () {
      animatedPointsAwarded.value = null;
    }, [isLoggedIn]);
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.containerStyle,
      children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.leftSideContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.dateLabelTextStyle,
          tx: "dineShopScreen.earnedFor"
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.dateValueTextStyle,
          text: (0, _i18n.translate)("dineShopScreen.todayText", {
            date: (0, _moment.default)().format(_dateTime.DateFormats.DayMonthYear)
          })
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.rightSideContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_reactNativeAnimateableText.default, {
          animatedProps: animatedProps,
          style: styles.pointAmountTextStyle
        }), (0, _jsxRuntime.jsx)(TierIcon, {
          height: 16,
          width: 16
        })]
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    containerStyle: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: 12,
      marginHorizontal: 24,
      marginTop: 48
    },
    leftSideContainerStyle: {
      gap: 2
    },
    dateLabelTextStyle: Object.assign({}, _text.newPresets.XSmallBold, {
      color: "rgba(252, 252, 252, 0.8)",
      textTransform: "none"
    }),
    dateValueTextStyle: Object.assign({}, _text.newPresets.caption1Bold),
    rightSideContainerStyle: {
      alignItems: "center",
      flexDirection: "row",
      gap: 4
    },
    pointAmountTextStyle: {
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.whiteGrey,
      fontStyle: "normal",
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      }),
      fontSize: 20,
      lineHeight: 28
    }
  });
  var _default = exports.default = DineShopEarnedForToday;
