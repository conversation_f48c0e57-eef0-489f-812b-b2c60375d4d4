  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[7]);
  var _globalLoadingController = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _constants = _$$_REQUIRE(_dependencyMap[9]);
  var _exploreItemType = _$$_REQUIRE(_dependencyMap[10]);
  var _native = _$$_REQUIRE(_dependencyMap[11]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[12]);
  var _adobe = _$$_REQUIRE(_dependencyMap[13]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[14]);
  var _react = _$$_REQUIRE(_dependencyMap[15]);
  var _account = _$$_REQUIRE(_dependencyMap[16]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[17]);
  var DineShopOtherAvailable = function DineShopOtherAvailable(props) {
    var _data$parkingPerks, _data$playpassPackage, _data$parkingPerks2, _data$playpassPackage2;
    var _useContext = (0, _react.useContext)(_account.AccountContext),
      accountPromoCodesFF = _useContext.accountPromoCodesFF;
    var isAccountPromoCodes = (0, _remoteConfig.getFeatureFlagInit)(_remoteConfig.REMOTE_CONFIG_FLAGS.ACCOUNT_PROMOCODES, accountPromoCodesFF);
    var data = props.data;
    var navigation = (0, _native.useNavigation)();
    var _useGeneratePlayPassU = (0, _screenHook.useGeneratePlayPassUrl)("viewExplorePlayPass"),
      getPlayPassUrl = _useGeneratePlayPassU.getPlayPassUrl;
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("STATIC_PARKING_CM_TILE"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var handlePressTilePlayPass = function handlePressTilePlayPass(item) {
      var creditedText = item != null && item.credited ? "Credited" : "Non-credited";
      (0, _adobe.trackAction)(_adobe.AdobeTagName.EPICDineAndShop, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.EPICDineAndShop, `Post-spend | ${item == null ? undefined : item.name} | Other Available Perks | ${creditedText}`));
      if (item != null && item.tokenType.includes("crdt")) {
        if (isAccountPromoCodes) {
          navigation.navigate(_constants.NavigationConstants.bookingsOrdersScreen);
        } else {
          navigation.navigate(_constants.NavigationConstants.creditsScreen);
        }
      } else {
        _globalLoadingController.default.showLoading();
        getPlayPassUrl(_constants.StateCode.PPEVENT, item == null ? undefined : item.code, {
          entryPoint: _exploreItemType.PlayPassEntryPoint.PERK_DETAILS,
          eventName: item == null ? undefined : item.name
        });
      }
    };
    var handlePressTileCarParking = function handlePressTileCarParking(item) {
      var _item$cta;
      var creditedText = item != null && item.credited ? "Credited" : "Non-credited";
      (0, _adobe.trackAction)(_adobe.AdobeTagName.EPICDineAndShop, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.EPICDineAndShop, `Post-spend | ${item == null ? undefined : item.title} | Other Available Perks | ${creditedText}`));
      var navigationData = item == null || (_item$cta = item.cta) == null ? undefined : _item$cta.navigationLink;
      if (navigationData != null && navigationData.value) {
        var _item$cta2;
        handleNavigation(navigationData == null ? undefined : navigationData.type, navigationData == null ? undefined : navigationData.value, item == null || (_item$cta2 = item.cta) == null || (_item$cta2 = _item$cta2.aemUtmRedirect) == null ? undefined : _item$cta2.redirectTarget);
      }
    };
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: ((data == null || (_data$parkingPerks = data.parkingPerks) == null ? undefined : _data$parkingPerks.aemParkingTile) || (data == null || (_data$playpassPackage = data.playpassPackages) == null ? undefined : _data$playpassPackage.packages)) && (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.viewTitleHeader,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            tx: "dineShopScreen.Other_Available_Perks",
            style: styles.titleHeader
          })
        }), (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
          children: data == null || (_data$parkingPerks2 = data.parkingPerks) == null || (_data$parkingPerks2 = _data$parkingPerks2.aemParkingTile) == null || _data$parkingPerks2.map == null ? undefined : _data$parkingPerks2.map(function (item, index) {
            var _item$tag, _item$description;
            return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
              children: !(item != null && item.credited) ? (0, _jsxRuntime.jsxs)(_reactNative2.TouchableOpacity, {
                onPress: function onPress() {
                  return handlePressTileCarParking(item);
                },
                style: styles.containerStyle,
                children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
                  source: {
                    uri: item == null ? undefined : item.image
                  },
                  style: styles.imageStyle
                }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
                  style: styles.rightSideContainerStyle,
                  children: [(item == null || (_item$tag = item.tag) == null ? undefined : _item$tag.length) > 0 && (0, _jsxRuntime.jsx)(_reactNative.View, {
                    style: styles.tagContainerStyle,
                    children: (0, _jsxRuntime.jsx)(_text.Text, {
                      style: styles.tagTextStyle,
                      text: item == null ? undefined : item.tag
                    })
                  }), (0, _jsxRuntime.jsx)(_text.Text, {
                    style: styles.titleTextStyle,
                    text: item == null ? undefined : item.title
                  }), (item == null || (_item$description = item.description) == null ? undefined : _item$description.length) > 0 && (0, _jsxRuntime.jsx)(_text.Text, {
                    style: styles.descriptionTextStyle,
                    text: item == null ? undefined : item.description
                  })]
                })]
              }, `getParkingTiles_${index}`) : null
            });
          })
        }), data == null || (_data$playpassPackage2 = data.playpassPackages) == null || (_data$playpassPackage2 = _data$playpassPackage2.packages) == null || _data$playpassPackage2.map == null ? undefined : _data$playpassPackage2.map(function (item, index) {
          var _item$header, _item$mechanics;
          return (0, _jsxRuntime.jsxs)(_reactNative2.TouchableOpacity, {
            onPress: function onPress() {
              return handlePressTilePlayPass(item);
            },
            style: styles.containerStyle,
            children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
              source: {
                uri: item == null ? undefined : item.thumbnailUrl
              },
              style: styles.imageStyle
            }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
              style: styles.rightSideContainerStyle,
              children: [(item == null || (_item$header = item.header) == null ? undefined : _item$header.length) > 0 && (0, _jsxRuntime.jsx)(_reactNative.View, {
                style: styles.tagContainerStyle,
                children: (0, _jsxRuntime.jsx)(_text.Text, {
                  style: styles.tagTextStyle,
                  text: item == null ? undefined : item.header
                })
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                style: styles.titleTextStyle,
                text: item == null ? undefined : item.name
              }), (item == null || (_item$mechanics = item.mechanics) == null ? undefined : _item$mechanics.length) > 0 && (0, _jsxRuntime.jsx)(_text.Text, {
                style: styles.descriptionTextStyle,
                text: item == null ? undefined : item.mechanics
              })]
            })]
          }, `getPlaypassPackages_${index}`);
        })]
      })
    });
  };
  var styles = _reactNative.StyleSheet.create({
    containerStyle: {
      borderColor: "rgba(252, 252, 252, 0.1)",
      borderBottomWidth: 1,
      flexDirection: "row",
      gap: 12,
      paddingBottom: 24,
      paddingHorizontal: 4,
      paddingTop: 8
    },
    viewTitleHeader: {
      width: "100%",
      paddingTop: 16,
      paddingBottom: 8,
      alignItems: "center"
    },
    titleHeader: {
      color: _theme.color.palette.baseYellow,
      fontSize: 12,
      lineHeight: 16,
      fontFamily: _theme.typography.bold,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      letterSpacing: 2.5
    },
    imageStyle: {
      height: 50,
      width: 67
    },
    rightSideContainerStyle: {
      alignItems: "flex-start",
      flex: 1,
      gap: 4
    },
    tagContainerStyle: {
      backgroundColor: "rgba(252, 252, 252, 0.1)",
      borderRadius: 4,
      paddingHorizontal: 6,
      paddingVertical: 3
    },
    tagTextStyle: {
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.almostWhiteGrey80,
      fontSize: 11,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 14,
      textAlignVertical: "center"
    },
    titleTextStyle: Object.assign({}, _text.newPresets.caption1Bold, {
      color: _theme.color.palette.whiteGrey
    }),
    descriptionTextStyle: Object.assign({}, _text.newPresets.caption2Regular, {
      color: "rgba(252, 252, 252, 0.8)"
    })
  });
  var _default = exports.default = DineShopOtherAvailable;
