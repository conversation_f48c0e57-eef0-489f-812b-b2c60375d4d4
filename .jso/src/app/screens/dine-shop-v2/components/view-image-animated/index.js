  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ViewImageAnimated = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _lottieReactNative = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _parking = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativeSizeMatters = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var leftIconLottie = (0, _reactNativeSizeMatters.scale)(-24);
  var topIconLottie = (0, _reactNativeSizeMatters.scale)(-25);
  var widthIconLottie = (0, _reactNativeSizeMatters.scale)(200);
  var heightIconLottie = (0, _reactNativeSizeMatters.scale)(222);
  var widthIconPNG = (0, _reactNativeSizeMatters.scale)(110);
  var heightIconPNG = (0, _reactNativeSizeMatters.scale)(95);
  var bottomIconPNG = (0, _reactNativeSizeMatters.scale)(18);
  var rightIconPNG = (0, _reactNativeSizeMatters.scale)(-10);
  var ViewImageAnimated = exports.ViewImageAnimated = _react.default.memo(function (_ref) {
    var onPress = _ref.onPress,
      showAnimation = _ref.showAnimation;
    var renderAnimationStamp = (0, _react.useCallback)(function () {
      return (0, _jsxRuntime.jsx)(_lottieReactNative.default, {
        style: styles.iconAnimated,
        source: _parking.default,
        loop: false,
        autoPlay: true
      });
    }, []);
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: showAnimation ? (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        style: styles.container,
        onPress: onPress,
        activeOpacity: 1,
        children: renderAnimationStamp()
      }) : (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        onPress: onPress,
        activeOpacity: 1,
        children: (0, _jsxRuntime.jsx)(_reactNative2.Image, {
          source: _icons.ParkingAnimationStop,
          style: styles.iconPng
        })
      })
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    container: {
      position: "absolute",
      left: leftIconLottie,
      top: topIconLottie
    },
    iconAnimated: {
      width: widthIconLottie,
      height: heightIconLottie
    },
    iconPng: {
      position: 'absolute',
      width: widthIconPNG,
      height: heightIconPNG,
      bottom: bottomIconPNG,
      right: rightIconPNG
    }
  });
