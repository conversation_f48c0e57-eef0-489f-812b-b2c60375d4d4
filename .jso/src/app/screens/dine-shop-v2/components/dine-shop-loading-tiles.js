  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _constants = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  var DineShopLoadingTiles = function DineShopLoadingTiles() {
    return new Array(4).fill(null).map(function (_item, index) {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.loadingItemContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: SHIMMER_PLACEHOLDER_COLORS,
          shimmerStyle: styles.loadingImageStyle
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.loadingRightSideContainerStyle,
          children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: SHIMMER_PLACEHOLDER_COLORS,
            shimmerStyle: styles.loadingTitleStyle
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: SHIMMER_PLACEHOLDER_COLORS,
            shimmerStyle: styles.loadingDescriptionStyle
          })]
        })]
      }, `loadingItem_${index}`);
    });
  };
  var SHIMMER_PLACEHOLDER_COLORS = ["rgba(252, 252, 252, 0.1)", "rgba(252, 252, 252, 0.01)", "rgba(252, 252, 252, 0.1)"];
  var styles = _reactNative.StyleSheet.create({
    loadingItemContainerStyle: {
      alignItems: "center",
      borderColor: "rgba(252, 252, 252, 0.1)",
      borderRadius: 16,
      borderWidth: 1,
      flexDirection: "row",
      gap: 12,
      height: 82,
      padding: 16
    },
    loadingImageStyle: {
      height: 50,
      width: 67
    },
    loadingRightSideContainerStyle: {
      flex: 1,
      gap: 8,
      height: 50,
      justifyContent: "center"
    },
    loadingTitleStyle: {
      borderRadius: 4,
      height: 18,
      width: 75
    },
    loadingDescriptionStyle: {
      borderRadius: 4,
      height: 18,
      width: "100%"
    }
  });
  var _default = exports.default = DineShopLoadingTiles;
