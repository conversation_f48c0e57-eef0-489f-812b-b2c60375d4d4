  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _utils = _$$_REQUIRE(_dependencyMap[2]);
  var _react = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNativeFastImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  var BaseImage = function BaseImage(props) {
    var source = props.source,
      _props$resizeMode = props.resizeMode,
      resizeMode = _props$resizeMode === undefined ? _reactNativeFastImage.default.resizeMode.cover : _props$resizeMode,
      style = props.style,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "ImageElement" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "ImageElement" : _props$accessibilityL,
      _props$cache = props.cache,
      cache = _props$cache === undefined ? true : _props$cache,
      onLoad = props.onLoad,
      onError = props.onError,
      alternativeImageSource = props.alternativeImageSource;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isAltImgVisible = _useState2[0],
      setIsAltImgVisible = _useState2[1];
    var handleLoadStart = function handleLoadStart() {
      setIsAltImgVisible(false);
    };
    var handleError = function handleError() {
      setIsAltImgVisible(true);
      onError == null || onError();
    };

    // === Render section ===
    if ((0, _utils.ifAllTrue)([typeof source === "object", !Array.isArray(source), !!source, !(source != null && source.uri), !alternativeImageSource])) {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: style,
        testID: testID,
        accessibilityLabel: accessibilityLabel
      });
    }
    if (typeof source === "object" && cache) {
      var sourceProps = {
        uri: source == null ? undefined : source.uri,
        priority: _reactNativeFastImage.default.priority.normal,
        cache: _reactNativeFastImage.default.cacheControl.immutable
      };
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        accessibilityLabel: accessibilityLabel,
        testID: testID,
        children: [alternativeImageSource && isAltImgVisible && (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: defaultStyle.clientImageContainerStyle,
          children: (0, _jsxRuntime.jsx)(_reactNative.Image, {
            resizeMode: resizeMode,
            source: alternativeImageSource,
            style: style
          })
        }), (0, _jsxRuntime.jsx)(_reactNativeFastImage.default, {
          source: sourceProps,
          resizeMode: resizeMode,
          style: style,
          onLoadStart: handleLoadStart,
          onLoad: onLoad,
          onError: handleError,
          defaultSource: alternativeImageSource
        })]
      });
    }
    return (0, _jsxRuntime.jsx)(_reactNative.Image, {
      source: source,
      resizeMode: resizeMode,
      style: style,
      testID: testID,
      accessibilityLabel: accessibilityLabel
    });
  };
  var defaultStyle = _reactNative.StyleSheet.create({
    clientImageContainerStyle: {
      left: 0,
      position: "absolute",
      top: 0
    }
  });
  var _default = exports.default = BaseImage;
