  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    screenWidth = _Dimensions$get.width;
  var CONTAINER_MARGIN_HORIZONTAL = 16;
  var BG_IMAGE_SIZE_RATIO = 3.648936170212766;
  var BG_IMAGE_RESPONSIVE_WIDTH = screenWidth - 32;
  var BG_IMAGE_RESPONSIVE_HEIGHT = BG_IMAGE_RESPONSIVE_WIDTH / BG_IMAGE_SIZE_RATIO;
  var INSIDE_WIDTH_RATIO = BG_IMAGE_RESPONSIVE_WIDTH / 343;
  var INSIDE_HEIGHT_RATIO = BG_IMAGE_RESPONSIVE_HEIGHT / 94;
  var TITLE_IMAGE_RESPONSIVE_WIDTH = 113 * INSIDE_WIDTH_RATIO;
  var TITLE_IMAGE_RESPONSIVE_HEIGHT = 30 * INSIDE_HEIGHT_RATIO;
  var styles = _reactNative.StyleSheet.create({
    bgImageStyle: {
      borderRadius: 16,
      overflow: "hidden",
      resizeMode: "cover",
      justifyContent: "space-between",
      width: BG_IMAGE_RESPONSIVE_WIDTH,
      height: BG_IMAGE_RESPONSIVE_HEIGHT,
      padding: 16 * INSIDE_HEIGHT_RATIO
    },
    titleImageStyle: {
      resizeMode: "contain",
      marginRight: 8 * INSIDE_WIDTH_RATIO,
      width: TITLE_IMAGE_RESPONSIVE_WIDTH,
      height: TITLE_IMAGE_RESPONSIVE_HEIGHT
    },
    topContentStyle: {
      flexDirection: "row"
    },
    numberOfChances: {
      flexDirection: "row",
      alignItems: "center",
      gap: 4 * INSIDE_WIDTH_RATIO,
      marginTop: -4 * INSIDE_HEIGHT_RATIO
    },
    numberOfChancesText: Object.assign({}, _text.newPresets.bodyTextBold, {
      color: _theme.color.palette.whiteGrey,
      lineHeight: 20 * INSIDE_HEIGHT_RATIO
    }),
    numberOfChancesUnit: Object.assign({}, _text.newPresets.XSmallBold, {
      flex: 1,
      textTransform: "none",
      fontSize: 11,
      color: "rgba(252, 252, 252, 0.8)",
      lineHeight: 14 * INSIDE_HEIGHT_RATIO
    }),
    chanceDescription: Object.assign({}, _text.newPresets.bodyTextRegular, {
      marginTop: 3 * INSIDE_HEIGHT_RATIO,
      color: "rgba(252, 252, 252, 0.6)",
      fontSize: 11,
      lineHeight: 11 * INSIDE_HEIGHT_RATIO
    }),
    dateContainer: {
      alignSelf: "center",
      top: 3 * INSIDE_HEIGHT_RATIO
    },
    dateText: Object.assign({}, _text.newPresets.bodyTextRegular, {
      color: "rgba(252, 252, 252, 0.6)",
      fontSize: 11,
      lineHeight: 11 * INSIDE_HEIGHT_RATIO
    }),
    numberOfChancesShimmer: {
      width: 28 * INSIDE_WIDTH_RATIO,
      height: 18 * INSIDE_HEIGHT_RATIO,
      borderRadius: 4 * INSIDE_HEIGHT_RATIO
    },
    numberOfChancesUnitShimmer: {
      width: 158 * INSIDE_WIDTH_RATIO,
      height: 18 * INSIDE_HEIGHT_RATIO,
      borderRadius: 4 * INSIDE_HEIGHT_RATIO
    },
    dateTextShimmer: {
      width: 120 * INSIDE_WIDTH_RATIO,
      height: 14 * INSIDE_HEIGHT_RATIO,
      borderRadius: 4 * INSIDE_HEIGHT_RATIO
    }
  });
  var _default = exports.default = styles;
