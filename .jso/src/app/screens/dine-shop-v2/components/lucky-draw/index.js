  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _dineShopV = _$$_REQUIRE(_dependencyMap[5]);
  var _styles = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _screenHelper = _$$_REQUIRE(_dependencyMap[7]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var LuckyDraw = function LuckyDraw(_ref) {
    var data = _ref.data;
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("DINE_SHOP_CME"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var _ref2 = data || {},
      period = _ref2.period,
      numberOfChances = _ref2.numberOfChances,
      bgImage = _ref2.bgImage,
      navigation = _ref2.navigation,
      campaignUnit = _ref2.campaignUnit,
      campaignDescription = _ref2.campaignDescription,
      redirect = _ref2.redirect;
    var backgroundImage = (0, _screenHelper.getUriImage)(bgImage);
    var onPressLuckyDraw = function onPressLuckyDraw() {
      var _ref3 = navigation || {},
        navigationType = _ref3.type,
        navigationValue = _ref3.value;
      if (!navigationType || !navigationValue) return null;
      handleNavigation(navigationType, navigationValue, redirect);
    };
    if (!data) {
      return null;
    }
    return (0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
      accessibilityLabel: `${_dineShopV.SCREEN_NAME}_Btn_LuckyDraw`,
      onPress: onPressLuckyDraw,
      testID: `${_dineShopV.SCREEN_NAME}_Btn_LuckyDraw`,
      children: (0, _jsxRuntime.jsxs)(_reactNative.ImageBackground, {
        source: {
          uri: backgroundImage
        },
        style: _styles.default.bgImageStyle,
        children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: _styles.default.topContentStyle,
          children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
            style: _styles.default.titleImageStyle
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: {
              flex: 1
            },
            children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
              style: _styles.default.numberOfChances,
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                style: _styles.default.numberOfChancesText,
                children: numberOfChances
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                numberOfLines: 1,
                style: _styles.default.numberOfChancesUnit,
                children: campaignUnit
              })]
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              numberOfLines: 1,
              style: _styles.default.chanceDescription,
              children: campaignDescription
            })]
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _styles.default.dateContainer,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: _styles.default.dateText,
            children: period
          })
        })]
      })
    });
  };
  var _default = exports.default = (0, _react.memo)(LuckyDraw);
