  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.flushAllImageCached = exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNativeFastImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _dineImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  var defaultBackgroundImage = {
    position: "absolute",
    left: 0,
    right: 0,
    top: 0,
    bottom: 0
  };
  var flushAllImageCached = exports.flushAllImageCached = function flushAllImageCached() {
    _reactNativeFastImage.default.clearMemoryCache();
    _reactNativeFastImage.default.clearDiskCache();
  };
  var DineImageBackground = function DineImageBackground(props) {
    var source = props.source,
      _props$resizeMode = props.resizeMode,
      resizeMode = _props$resizeMode === undefined ? "cover" : _props$resizeMode,
      imageStyle = props.imageStyle,
      children = props.children,
      style = props.style,
      _onError = props.onError,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "testID" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "accessibilityLabel" : _props$accessibilityL,
      cache = props.cache,
      _props$trailing = props.trailing,
      trailing = _props$trailing === undefined ? false : _props$trailing,
      alternativeImageSource = props.alternativeImageSource;
    if (typeof source === "object") {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: trailing ? style : imageStyle,
        children: [(0, _jsxRuntime.jsx)(_dineImage.default, {
          source: source,
          style: Object.assign({}, defaultBackgroundImage, imageStyle),
          resizeMode: resizeMode,
          testID: testID,
          accessibilityLabel: accessibilityLabel,
          cache: cache,
          alternativeImageSource: alternativeImageSource
        }), children]
      });
    }
    return (0, _jsxRuntime.jsx)(_reactNative.ImageBackground, {
      source: source,
      style: style,
      imageStyle: imageStyle,
      onError: function onError() {
        return _onError && _onError();
      },
      children: children
    });
  };
  var _default = exports.default = DineImageBackground;
