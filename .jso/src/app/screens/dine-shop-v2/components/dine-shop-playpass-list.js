  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.DineShopPlayPass = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _viewItemCreated = _$$_REQUIRE(_dependencyMap[3]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[4]);
  var _constants = _$$_REQUIRE(_dependencyMap[5]);
  var _globalLoadingController = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _exploreItemType = _$$_REQUIRE(_dependencyMap[7]);
  var _native = _$$_REQUIRE(_dependencyMap[8]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[9]);
  var _dineShopV = _$$_REQUIRE(_dependencyMap[10]);
  var _navigationType = _$$_REQUIRE(_dependencyMap[11]);
  var _authentication = _$$_REQUIRE(_dependencyMap[12]);
  var _utils = _$$_REQUIRE(_dependencyMap[13]);
  var _adobe = _$$_REQUIRE(_dependencyMap[14]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[15]);
  var _account = _$$_REQUIRE(_dependencyMap[16]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[17]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var DineShopPlayPass = exports.DineShopPlayPass = function DineShopPlayPass(props) {
    var _data$parkingPerks, _data$playpassPackage;
    var _useContext = (0, _react.useContext)(_account.AccountContext),
      accountPromoCodesFF = _useContext.accountPromoCodesFF;
    var isAccountPromoCodes = (0, _remoteConfig.getFeatureFlagInit)(_remoteConfig.REMOTE_CONFIG_FLAGS.ACCOUNT_PROMOCODES, accountPromoCodesFF);
    var data = props.data;
    var navigation = (0, _native.useNavigation)();
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("STATIC_PARKING_CM_TILE"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var _useGeneratePlayPassU = (0, _screenHook.useGeneratePlayPassUrl)("viewExplorePlayPass"),
      getPlayPassUrl = _useGeneratePlayPassU.getPlayPassUrl;
    var onClick = function onClick(item) {
      var tokenName = (item == null ? undefined : item.tokenQty) === 1 ? item == null ? undefined : item.tokenNameSingular : item == null ? undefined : item.tokenNamePlural;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.EPICDineAndShop, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.EPICDineAndShop, `Post-spend | ${tokenName} | ${item == null ? undefined : item.header} | Credited`));
      if ((item == null ? undefined : item.tokenType) === _dineShopV.CreditedTokenType.MiffySpend10) {
        handleNavigation(_navigationType.NavigationTypeEnum.deepLink, _navigationHelper.NavigationValueDeepLink.missionpass, {
          aaTag: (0, _utils.joinTexts)([_navigationHelper.NavigationAATag.Epic, tokenName]),
          isLoggedInAtTriggerTime: (0, _authentication.checkLoginState)(),
          pageSource: _navigationHelper.NavigationPageSource.Epic,
          taskCode: _dineShopV.TaskCode.Spend10
        });
      } else if ((item == null ? undefined : item.tokenType) === _dineShopV.CreditedTokenType.MiffyISC60) {
        handleNavigation(_navigationType.NavigationTypeEnum.deepLink, _navigationHelper.NavigationValueDeepLink.missionpass, {
          aaTag: (0, _utils.joinTexts)([_navigationHelper.NavigationAATag.Epic, tokenName]),
          isLoggedInAtTriggerTime: (0, _authentication.checkLoginState)(),
          pageSource: _navigationHelper.NavigationPageSource.Epic,
          taskCode: _dineShopV.TaskCode.ISC60
        });
      } else if (item != null && item.tokenType.includes("crdt")) {
        if (isAccountPromoCodes) {
          navigation.navigate(_constants.NavigationConstants.bookingsOrdersScreen);
        } else {
          navigation.navigate(_constants.NavigationConstants.creditsScreen);
        }
      } else {
        _globalLoadingController.default.showLoading();
        getPlayPassUrl(_constants.StateCode.PPEVENT, item == null ? undefined : item.packageCodes, {
          entryPoint: _exploreItemType.PlayPassEntryPoint.PERK_DETAILS,
          eventName: tokenName
        });
      }
    };
    var onClickParking = function onClickParking(item) {
      var _item$cta;
      var creditedText = item != null && item.credited ? "Credited" : "Non-credited";
      (0, _adobe.trackAction)(_adobe.AdobeTagName.EPICDineAndShop, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.EPICDineAndShop, `Post-spend | ${item == null ? undefined : item.title} | ${item == null ? undefined : item.tag} | ${creditedText}`));
      var navigationData = item == null || (_item$cta = item.cta) == null ? undefined : _item$cta.navigationLink;
      if (navigationData != null && navigationData.value) {
        var _item$cta2;
        handleNavigation(navigationData == null ? undefined : navigationData.type, navigationData == null ? undefined : navigationData.value, item == null || (_item$cta2 = item.cta) == null || (_item$cta2 = _item$cta2.aemUtmRedirect) == null ? undefined : _item$cta2.redirectTarget);
      }
    };
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [data == null || (_data$parkingPerks = data.parkingPerks) == null || (_data$parkingPerks = _data$parkingPerks.aemParkingTile) == null || _data$parkingPerks.map == null ? undefined : _data$parkingPerks.map(function (item, index) {
        return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
          children: item != null && item.credited ? (0, _jsxRuntime.jsx)(_viewItemCreated.ViewItem, {
            item: item,
            onPress: function onPress() {
              return onClickParking(item);
            },
            isCredited: item == null ? undefined : item.credited
          }, `playpassPackages-credited${index}`) : null
        });
      }), data == null || (_data$playpassPackage = data.playpassPackages) == null || (_data$playpassPackage = _data$playpassPackage.creditedTokens) == null ? undefined : _data$playpassPackage.map(function (item, index) {
        return (0, _jsxRuntime.jsx)(_viewItemCreated.ViewItem, {
          item: item,
          onPress: function onPress() {
            return onClick(item);
          },
          isCredited: item == null ? undefined : item.credited
        }, `playpassPackages${index}`);
      })]
    });
  };
