  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ViewProgress = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _styles = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[7]));
  var _native = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var AnimatedGradient = _reactNativeReanimated.default.createAnimatedComponent(_reactNativeLinearGradient.default);
  var _worklet_12182206493696_init_data = {
    code: "function indexTsx1(){const{animatedWidth}=this.__closure;return{width:animatedWidth.value};}"
  };
  var ViewProgress = exports.ViewProgress = _react.default.memo(function (props) {
    var _props$current = props.current,
      current = _props$current === undefined ? 0 : _props$current,
      max = props.max,
      colors = props.colors,
      isError = props.isError,
      ref = props.ref;
    var animatedWidth = (0, _reactNativeReanimated.useSharedValue)(0);
    var isFirstTimeMounting = (0, _react.useRef)(true);
    var _useState = (0, _react.useState)(0),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      progressBarMaxWidth = _useState2[0],
      setProgressBarMaxWidth = _useState2[1];
    var progressBarStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var indexTsx1 = function indexTsx1() {
        return {
          width: animatedWidth.value
        };
      };
      indexTsx1.__closure = {
        animatedWidth: animatedWidth
      };
      indexTsx1.__workletHash = 12182206493696;
      indexTsx1.__initData = _worklet_12182206493696_init_data;
      return indexTsx1;
    }());
    var handleTriggerPBAnimation = (0, _react.useCallback)(function () {
      if (!progressBarMaxWidth) return;
      var currentValue = Math.min(Number(current), Number(max));
      var outputWidthPercent = currentValue / Number(max) * 100;
      var outputWidth = progressBarMaxWidth * outputWidthPercent / 100;
      animatedWidth.value = (0, _reactNativeReanimated.withTiming)(outputWidth, {
        duration: 700
      });
    }, [current, isError, max, progressBarMaxWidth]);
    var handleProgressBarLayout = function handleProgressBarLayout(event) {
      var width = event.nativeEvent.layout.width;
      setProgressBarMaxWidth(width);
    };
    (0, _react.useImperativeHandle)(ref, function () {
      return {
        triggerPBAnimation: handleTriggerPBAnimation
      };
    });
    (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
      if (!isFirstTimeMounting.current) {
        setTimeout(function () {
          handleTriggerPBAnimation();
          isFirstTimeMounting.current = false;
        }, 300);
      }
      return function () {
        animatedWidth.value = 0;
      };
    }, [handleTriggerPBAnimation]));
    (0, _react.useEffect)(function () {
      setTimeout(function () {
        isFirstTimeMounting.current = false;
      }, 500);
    }, []);
    (0, _react.useEffect)(function () {
      if (progressBarMaxWidth) {
        handleTriggerPBAnimation();
      }
    }, [progressBarMaxWidth]);
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: _styles.styles.viewRow,
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        onLayout: handleProgressBarLayout,
        style: _styles.styles.container,
        children: (0, _jsxRuntime.jsx)(AnimatedGradient, {
          colors: colors,
          start: {
            x: 1,
            y: 0
          },
          end: {
            x: 0,
            y: 0
          },
          style: [_styles.styles.viewProgress, progressBarStyle]
        })
      }), (0, _jsxRuntime.jsxs)(_text.Text, {
        children: [isError ? (0, _jsxRuntime.jsxs)(_text.Text, {
          style: _styles.styles.txtCurrent,
          children: ["$", Number(current).toFixed(2)]
        }) : (0, _jsxRuntime.jsxs)(_text.Text, {
          style: _styles.styles.txtCurrent,
          children: ["$", Number(current) >= Number(max) ? Number(max).toFixed(2) : Number(current).toFixed(2), " "]
        }), (0, _jsxRuntime.jsxs)(_text.Text, {
          style: _styles.styles.txtMax,
          children: ["/$", max]
        })]
      })]
    });
  });
