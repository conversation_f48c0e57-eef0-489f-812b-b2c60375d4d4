  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _color = _$$_REQUIRE(_dependencyMap[0]);
  var _typography = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    container: {
      width: 60,
      height: 6,
      borderRadius: 12,
      backgroundColor: "#FCFCFC4D",
      overflow: 'hidden'
    },
    viewRow: {
      width: "100%",
      flexDirection: "row",
      alignItems: "center",
      gap: 8
    },
    txtCurrent: {
      fontFamily: _typography.typography.bold,
      color: _color.color.palette.whiteGrey,
      fontSize: 11,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 14
    },
    txtMax: {
      fontFamily: _typography.typography.bold,
      color: "#A5ACCA",
      fontSize: 11,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 14
    },
    viewProgress: {
      position: 'absolute',
      height: 6,
      top: 0,
      bottom: 0,
      right: 0,
      left: 0,
      borderRadius: 12
    }
  });
