  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.DineShopActiveParking = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _typography = _$$_REQUIRE(_dependencyMap[8]);
  var _color = _$$_REQUIRE(_dependencyMap[9]);
  var _viewProgress = _$$_REQUIRE(_dependencyMap[10]);
  var _viewImageAnimated = _$$_REQUIRE(_dependencyMap[11]);
  var _icons = _$$_REQUIRE(_dependencyMap[12]);
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[13]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _screenHook = _$$_REQUIRE(_dependencyMap[15]);
  var _constants = _$$_REQUIRE(_dependencyMap[16]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[17]);
  var _drive = _$$_REQUIRE(_dependencyMap[18]);
  var _native = _$$_REQUIRE(_dependencyMap[19]);
  var _adobe = _$$_REQUIRE(_dependencyMap[20]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[21]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var ratioItem = 0.8634020618556701;
  var colorLinear = ["rgba(255, 255, 255, 0.12)", "rgba(255, 255, 255, 0.04)"];
  var ViewItem = function ViewItem(_ref) {
    var item = _ref.item,
      index = _ref.index,
      transactionSummaryData = _ref.transactionSummaryData;
    var navigation = (0, _native.useNavigation)();
    var testID = `ViewItemParkingCPMS_${index}`;
    var initAnimationInDayLeft = (0, _mmkvStorage.getAnimationInDayParkingLeft)();
    var initAnimationInDayRight = (0, _mmkvStorage.getAnimationInDayParkingRight)();
    var _useState = (0, _react.useState)(null),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isAnimationIcon = _useState2[0],
      setIsAnimationIcon = _useState2[1];
    var _useGeneratePlayPassU = (0, _screenHook.useGeneratePlayPassUrl)("viewCarPassAirPort"),
      getCommonLoginModule = _useGeneratePlayPassU.getCommonLoginModule;
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      showIcon = _useState4[0],
      setShowIcon = _useState4[1];
    var _useContext = (0, _react.useContext)(_drive.DriveContext),
      driveParkingFeatureFlag = _useContext.driveParkingFeatureFlag;
    var isDriveParking = (0, _remoteConfig.getFeatureFlagInit)(_remoteConfig.REMOTE_CONFIG_FLAGS.DRIVE_PARKING, driveParkingFeatureFlag);
    var setDayAnimation = function setDayAnimation() {
      var date = (0, _moment.default)().format("DD/MM/YYYY");
      if (index === 0) {
        (0, _mmkvStorage.setAnimationInDayParkingLeft)(date);
      } else {
        (0, _mmkvStorage.setAnimationInDayParkingRight)(date);
      }
    };
    (0, _react.useEffect)(function () {
      if (!(item != null && item.progressBar)) {
        onLayout();
      } else {
        setShowIcon(false);
      }
    }, [item == null ? undefined : item.progressBar]);
    var onLayout = function onLayout() {
      var initAnimationInDay = index === 0 ? initAnimationInDayLeft : initAnimationInDayRight;
      var isAnimation = initAnimationInDay !== (0, _moment.default)().format("DD/MM/YYYY");
      setIsAnimationIcon(isAnimation);
      isAnimation && setDayAnimation();
      !showIcon && setShowIcon(true);
    };
    var onClick = function onClick(item) {
      var creditedText = item != null && item.stamp ? "Credited" : "Non-credited";
      (0, _adobe.trackAction)(_adobe.AdobeTagName.EPICDineAndShop, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.EPICDineAndShop, `Post-spend | ${item == null ? undefined : item.title1} | Parking Coupon | ${creditedText}`));
      if (isDriveParking) {
        navigation.navigate(_constants.NavigationConstants.parkingLanding);
        return;
      }
      getCommonLoginModule(_constants.StateCode.CARPASS);
    };
    var convertSubtitle = function convertSubtitle() {
      var _item$subtitle;
      if (item != null && (_item$subtitle = item.subtitle) != null && _item$subtitle.startsWith("Expires")) {
        var subTxt = item == null ? undefined : item.subtitle.replace("Expires", "Expires \n");
        return subTxt;
      } else {
        return item == null ? undefined : item.subtitle;
      }
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        style: styles.item,
        activeOpacity: 1,
        onPress: function onPress() {
          return onClick(item);
        },
        testID: `${testID}__TouchableBaggage`,
        accessibilityLabel: `${testID}__TouchableBaggage`,
        children: (0, _jsxRuntime.jsxs)(_reactNativeLinearGradient.default, {
          style: styles.content,
          colors: colorLinear,
          start: {
            x: 0.5,
            y: 0
          },
          end: {
            x: 0.5,
            y: 1
          },
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            children: [(0, _jsxRuntime.jsx)(_reactNative2.Image, {
              source: (item == null ? undefined : item.direction) === 'left' ? _icons.ParkingIconLeft : _icons.ParkingIconRight,
              style: styles.icon,
              resizeMode: "contain"
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.txtTitle,
              children: item == null ? undefined : item.title1
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.txtSubTitle,
              children: item == null ? undefined : item.title2
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.padding,
            children: [(item == null ? undefined : item.progressBar) && (0, _jsxRuntime.jsx)(_viewProgress.ViewProgress, {
              current: (item == null ? undefined : item.direction) === 'left' ? transactionSummaryData == null ? undefined : transactionSummaryData.jewelTerminalSubtotalAmount : transactionSummaryData == null ? undefined : transactionSummaryData.mainTerminalSubtotalAmount,
              max: (item == null ? undefined : item.direction) === 'left' ? "80" : "60",
              colors: (item == null ? undefined : item.direction) === 'left' ? _color.color.palette.overlayLinearT1Jewel : _color.color.palette.overlayLinearT1T4,
              isError: item == null ? undefined : item.isError
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.txtContent,
              children: convertSubtitle()
            })]
          })]
        })
      }, index), showIcon && (0, _jsxRuntime.jsx)(_viewImageAnimated.ViewImageAnimated, {
        onPress: function onPress() {
          return onClick(item);
        },
        showAnimation: isAnimationIcon
      })]
    });
  };
  var DineShopActiveParking = exports.DineShopActiveParking = _react.default.memo(function (props) {
    var _convertData;
    var data = props.data;
    var transactionSummaryData = data == null ? undefined : data.transactionSummary;
    var jewelTerminalSubtotalAmount = transactionSummaryData != null && transactionSummaryData.jewelTerminalSubtotalAmount ? transactionSummaryData == null ? undefined : transactionSummaryData.jewelTerminalSubtotalAmount : 0;
    var mainTerminalSubtotalAmount = transactionSummaryData != null && transactionSummaryData.mainTerminalSubtotalAmount ? transactionSummaryData == null ? undefined : transactionSummaryData.mainTerminalSubtotalAmount : 0;
    var isShowNumberSubTitleLeft = Number(80) - Number(jewelTerminalSubtotalAmount) > 0;
    var isShowNumberSubTitleRight = Number(60) - Number(mainTerminalSubtotalAmount) > 0;
    var subtitleLeft = Number(80 - Number(jewelTerminalSubtotalAmount)).toFixed(2);
    var subtitleRight = Number(60 - Number(mainTerminalSubtotalAmount)).toFixed(2);
    var numberZero = "0.00";
    var dataWhenApiError = [{
      direction: 'left',
      progressBar: true,
      title1: '2-hour Free Parking',
      title2: "T1/Jewel*",
      subtitle: `Spend $${isShowNumberSubTitleLeft ? subtitleLeft : numberZero} more at Jewel today`,
      isError: true
    }, {
      direction: 'right',
      progressBar: true,
      title1: '2-hour Free Parking',
      title2: "T1/Jewel*, T2, T3, T4",
      subtitle: `Spend $${isShowNumberSubTitleRight ? subtitleRight : numberZero} more at T1-T4 Public Areas today`,
      isError: true
    }];
    var convertData = function convertData() {
      var _data$parkingPerks;
      if (data != null && (_data$parkingPerks = data.parkingPerks) != null && _data$parkingPerks.cpmsParkingTile) {
        var _data$parkingPerks2;
        var dataLocal = [];
        if ((data == null || (_data$parkingPerks2 = data.parkingPerks) == null || (_data$parkingPerks2 = _data$parkingPerks2.cpmsParkingTile) == null ? undefined : _data$parkingPerks2.length) > 0) {
          var _data$parkingPerks3, _data$parkingPerks4, _data$parkingPerks5, _data$parkingPerks6;
          var indexLeft = data == null || (_data$parkingPerks3 = data.parkingPerks) == null || (_data$parkingPerks3 = _data$parkingPerks3.cpmsParkingTile) == null ? undefined : _data$parkingPerks3.findIndex(function (item) {
            return (item == null ? undefined : item.direction) === 'left';
          });
          var indexRight = data == null || (_data$parkingPerks4 = data.parkingPerks) == null || (_data$parkingPerks4 = _data$parkingPerks4.cpmsParkingTile) == null ? undefined : _data$parkingPerks4.findIndex(function (item) {
            return (item == null ? undefined : item.direction) === 'right';
          });
          dataLocal.push(data == null || (_data$parkingPerks5 = data.parkingPerks) == null ? undefined : _data$parkingPerks5.cpmsParkingTile[indexLeft]);
          dataLocal.push(data == null || (_data$parkingPerks6 = data.parkingPerks) == null ? undefined : _data$parkingPerks6.cpmsParkingTile[indexRight]);
        }
        return dataLocal;
      } else {
        return dataWhenApiError;
      }
    };
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: styles.container,
      children: (_convertData = convertData()) == null ? undefined : _convertData.map(function (item, index) {
        return (0, _jsxRuntime.jsx)(ViewItem, {
          item: item,
          index: index,
          transactionSummaryData: transactionSummaryData
        });
      })
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    container: {
      width: "100%",
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center"
    },
    item: {
      width: (_reactNative2.Dimensions.get("window").width - 48) / 2,
      borderWidth: 1,
      borderRadius: 16,
      borderColor: "rgba(252, 252, 252, 0.15)",
      overflow: "hidden",
      opacity: 0.8,
      height: (_reactNative2.Dimensions.get("window").width - 48) / 2 / ratioItem
    },
    content: {
      flex: 1,
      paddingTop: 16,
      paddingHorizontal: 16,
      paddingBottom: 12,
      justifyContent: "space-between"
    },
    icon: {
      width: 32,
      height: 32,
      marginBottom: 4
    },
    txtTitle: {
      fontFamily: _typography.typography.black,
      color: _color.color.palette.whiteGrey,
      fontSize: 14,
      fontWeight: _reactNative2.Platform.select({
        ios: "900",
        android: "normal"
      }),
      lineHeight: 18,
      textAlignVertical: "center"
    },
    txtSubTitle: {
      fontFamily: _typography.typography.regular,
      color: _color.color.palette.whiteGrey,
      fontSize: 12,
      fontWeight: _reactNative2.Platform.select({
        ios: "400",
        android: "normal"
      }),
      lineHeight: 16,
      marginTop: 4
    },
    txtContent: {
      fontFamily: _typography.typography.regular,
      color: _color.color.palette.almostWhiteGrey80,
      fontSize: 12,
      fontWeight: _reactNative2.Platform.select({
        ios: "400",
        android: "normal"
      }),
      lineHeight: 16,
      marginTop: 5
    },
    padding: {
      paddingVertical: 4
    }
  });
