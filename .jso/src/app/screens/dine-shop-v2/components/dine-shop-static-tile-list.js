  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[5]);
  var _react = _$$_REQUIRE(_dependencyMap[6]);
  var _dineShopV = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  var DineShopStaticTileList = function DineShopStaticTileList(props) {
    var _data$parkingPerks, _playpassData$playpas2;
    var onPressParkingCMTile = props.onPressParkingCMTile,
      onPressPPTile = props.onPressPPTile,
      data = props.data;
    var staticParkingCMTileData = data == null || (_data$parkingPerks = data.parkingPerks) == null ? undefined : _data$parkingPerks.aemParkingTile;
    var playpassData = data;
    var staticPPTileData = (0, _react.useMemo)(function () {
      var _playpassData$playpas;
      return playpassData == null || (_playpassData$playpas = playpassData.playpassPackages) == null ? undefined : _playpassData$playpas.packages;
    }, [JSON.stringify(playpassData == null || (_playpassData$playpas2 = playpassData.playpassPackages) == null ? undefined : _playpassData$playpas2.packages)]);
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {}), staticParkingCMTileData == null || staticParkingCMTileData.map == null ? undefined : staticParkingCMTileData.map(function (item, index) {
        return (0, _jsxRuntime.jsxs)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
          accessibilityLabel: `${_dineShopV.SCREEN_NAME}_Tile_StaticParkingNChangiMillionaire`,
          androidRippleColor: "transparent",
          onPress: function onPress() {
            return onPressParkingCMTile(item);
          },
          style: styles.containerStyle,
          testID: `${_dineShopV.SCREEN_NAME}_Tile_StaticParkingNChangiMillionaire`,
          children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
            source: {
              uri: item == null ? undefined : item.image
            },
            style: styles.imageStyle
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.rightSideContainerStyle,
            children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
              style: styles.tagContainerStyle,
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                style: styles.tagTextStyle,
                text: item == null ? undefined : item.tag
              })
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.titleTextStyle,
              text: item == null ? undefined : item.title
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.descriptionTextStyle,
              text: item == null ? undefined : item.description
            })]
          })]
        }, `staticTile_ParkingCM_${index}`);
      }), staticPPTileData == null || staticPPTileData.map == null ? undefined : staticPPTileData.map(function (item, index) {
        return (0, _jsxRuntime.jsxs)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
          accessibilityLabel: `${_dineShopV.SCREEN_NAME}_Tile_StaticPlayPass`,
          androidRippleColor: "transparent",
          onPress: function onPress() {
            return onPressPPTile(item);
          },
          style: styles.containerStyle,
          testID: `${_dineShopV.SCREEN_NAME}_Tile_StaticPlayPass`,
          children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
            source: {
              uri: item == null ? undefined : item.heroUrl
            },
            style: styles.imageStyle
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.rightSideContainerStyle,
            children: [(item == null ? undefined : item.header) && (0, _jsxRuntime.jsx)(_reactNative.View, {
              style: styles.tagContainerStyle,
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                style: styles.tagTextStyle,
                text: item == null ? undefined : item.header
              })
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.titleTextStyle,
              text: item == null ? undefined : item.name
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.descriptionTextStyle,
              text: item == null ? undefined : item.mechanics
            })]
          })]
        }, `staticTile_Playpass_${index}`);
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    containerStyle: {
      borderColor: "rgba(252, 252, 252, 0.1)",
      borderBottomWidth: 1,
      flexDirection: "row",
      gap: 12,
      paddingBottom: 24,
      paddingHorizontal: 4,
      paddingTop: 8
    },
    imageStyle: {
      height: 50,
      width: 67
    },
    rightSideContainerStyle: {
      alignItems: "flex-start",
      flex: 1,
      gap: 4
    },
    tagContainerStyle: {
      backgroundColor: "rgba(252, 252, 252, 0.1)",
      borderRadius: 4,
      paddingHorizontal: 6,
      paddingVertical: 2
    },
    tagTextStyle: Object.assign({}, _text.newPresets.XSmallBold, {
      color: "rgba(252, 252, 252, 0.8)",
      textTransform: "none"
    }),
    titleTextStyle: Object.assign({}, _text.newPresets.caption1Bold, {
      color: _theme.color.palette.whiteGrey
    }),
    descriptionTextStyle: Object.assign({}, _text.newPresets.caption2Regular, {
      color: "rgba(252, 252, 252, 0.8)"
    })
  });
  var _default = exports.default = DineShopStaticTileList;
