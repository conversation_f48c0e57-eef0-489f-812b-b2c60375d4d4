  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.DineError = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _icons = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _typography = _$$_REQUIRE(_dependencyMap[6]);
  var _color = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var DineError = exports.DineError = _react.default.memo(function () {
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      activeView = _useState2[0],
      setActiveView = _useState2[1];
    (0, _react.useEffect)(function () {
      setTimeout(function () {
        setActiveView(true);
      }, 800);
    }, []);
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: activeView && (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.container,
        children: [(0, _jsxRuntime.jsx)(_icons.ReloadWhite, {}), (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.title,
          tx: "dineShopScreen.dineTitle"
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.subTitle,
          tx: "dineShopScreen.dineReload"
        })]
      })
    });
  });
  var styles = _reactNative.StyleSheet.create({
    container: {
      alignItems: 'center',
      paddingBottom: 38,
      marginTop: 32
    },
    title: {
      fontFamily: _typography.typography.regular,
      color: _color.color.palette.almostWhiteGrey,
      fontSize: 14,
      fontWeight: _reactNative.Platform.select({
        ios: "400",
        android: "normal"
      }),
      lineHeight: 18,
      textAlign: 'center',
      marginTop: 8,
      letterSpacing: 0.5
    },
    subTitle: {
      fontFamily: _typography.typography.regular,
      color: _color.color.palette.almostWhiteGrey80,
      fontSize: 12,
      fontWeight: _reactNative.Platform.select({
        ios: "400",
        android: "normal"
      }),
      lineHeight: 16,
      textAlign: 'center',
      marginTop: 8,
      width: '100%',
      letterSpacing: 0.5
    }
  });
