  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.TAB_BAR_PADDING_N_REFINEMENT = exports.NO_PADDING_TAB_BAR_HEIGHT = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[2]);
  var _dineShopV = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _react = _$$_REQUIRE(_dependencyMap[7]);
  var _dineShopV2TabNavigator = _$$_REQUIRE(_dependencyMap[8]);
  var _i18n = _$$_REQUIRE(_dependencyMap[9]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var AnimatedText = _reactNativeReanimated.default.createAnimatedComponent(_reactNative.Text);
  var _worklet_14022364887055_init_data = {
    code: "function dineShopTabBarTsx1(){const{animatedLeft,interpolate,animatedScrollX,screenWidth,Extrapolation,color,tabNavItemWidth}=this.__closure;var _animatedScrollX;animatedLeft.value=interpolate((_animatedScrollX=animatedScrollX)===null||_animatedScrollX===void 0?void 0:_animatedScrollX.value,[0,screenWidth*2],[0,screenWidth*2/3],Extrapolation.CLAMP);return{backgroundColor:color.palette.lightPurple,bottom:-1,height:2,left:0,position:\"absolute\",width:tabNavItemWidth,transform:[{translateX:animatedLeft.value}]};}"
  };
  var _worklet_16362092389280_init_data = {
    code: "function dineShopTabBarTsx2(){const{interpolateColor,animatedScrollX,screenWidth,TAB_CONTENT_GAP,TAB_BAR_CONFIG,index,color}=this.__closure;var _animatedScrollX;const titleColor=interpolateColor((_animatedScrollX=animatedScrollX)===null||_animatedScrollX===void 0?void 0:_animatedScrollX.value,[0,screenWidth+TAB_CONTENT_GAP,(screenWidth+TAB_CONTENT_GAP)*2],TAB_BAR_CONFIG.map(function(_config,configIdx){return configIdx===index?color.palette.almostBlackGrey:color.palette.darkGrey999;}));return{color:titleColor};}"
  };
  var DineShopTabBar = function DineShopTabBar(props) {
    var animatedScrollX = props.animatedScrollX,
      handleScrollToTab = props.handleScrollToTab,
      noPaddingTop = props.noPaddingTop,
      isShopDineV2 = props.isShopDineV2;
    var tabNavItemWidth = screenWidth / 3;
    var animatedLeft = (0, _reactNativeReanimated.useSharedValue)(screenWidth * 2 / 3);
    var underlineStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var dineShopTabBarTsx1 = function dineShopTabBarTsx1() {
        animatedLeft.value = (0, _reactNativeReanimated.interpolate)(animatedScrollX == null ? undefined : animatedScrollX.value, [0, screenWidth * 2], [0, screenWidth * 2 / 3], _reactNativeReanimated.Extrapolation.CLAMP);
        return {
          backgroundColor: _theme.color.palette.lightPurple,
          bottom: -1,
          height: 2,
          left: 0,
          position: "absolute",
          width: tabNavItemWidth,
          transform: [{
            translateX: animatedLeft.value
          }]
        };
      };
      dineShopTabBarTsx1.__closure = {
        animatedLeft: animatedLeft,
        interpolate: _reactNativeReanimated.interpolate,
        animatedScrollX: animatedScrollX,
        screenWidth: screenWidth,
        Extrapolation: _reactNativeReanimated.Extrapolation,
        color: _theme.color,
        tabNavItemWidth: tabNavItemWidth
      };
      dineShopTabBarTsx1.__workletHash = 14022364887055;
      dineShopTabBarTsx1.__initData = _worklet_14022364887055_init_data;
      return dineShopTabBarTsx1;
    }(), [animatedScrollX == null ? undefined : animatedScrollX.value]);
    if (isShopDineV2) {
      return null;
    }
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: noPaddingTop ? styles.noPaddingContainerStyle : styles.containerStyle,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.tabBarContainerStyle,
        children: [_dineShopV.TAB_BAR_CONFIG.map(function (config, index) {
          var onPress = function onPress() {
            handleScrollToTab(config.name);
          };
          var animatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
            var dineShopTabBarTsx2 = function dineShopTabBarTsx2() {
              var titleColor = (0, _reactNativeReanimated.interpolateColor)(animatedScrollX == null ? undefined : animatedScrollX.value, [0, screenWidth + _dineShopV2TabNavigator.TAB_CONTENT_GAP, (screenWidth + _dineShopV2TabNavigator.TAB_CONTENT_GAP) * 2], _dineShopV.TAB_BAR_CONFIG.map(function (_config, configIdx) {
                return configIdx === index ? _theme.color.palette.almostBlackGrey : _theme.color.palette.darkGrey999;
              }));
              return {
                color: titleColor
              };
            };
            dineShopTabBarTsx2.__closure = {
              interpolateColor: _reactNativeReanimated.interpolateColor,
              animatedScrollX: animatedScrollX,
              screenWidth: screenWidth,
              TAB_CONTENT_GAP: _dineShopV2TabNavigator.TAB_CONTENT_GAP,
              TAB_BAR_CONFIG: _dineShopV.TAB_BAR_CONFIG,
              index: index,
              color: _theme.color
            };
            dineShopTabBarTsx2.__workletHash = 16362092389280;
            dineShopTabBarTsx2.__initData = _worklet_16362092389280_init_data;
            return dineShopTabBarTsx2;
          }());
          return (0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
            accessibilityLabel: `${_dineShopV.SCREEN_NAME}_TabBar__TouchableNavItem`,
            androidRippleColor: "transparent",
            onPress: onPress,
            style: styles.tabNavItemStyle,
            testID: `${_dineShopV.SCREEN_NAME}_TabBar__TouchableNavItem`,
            children: (0, _jsxRuntime.jsx)(AnimatedText, {
              style: [styles.tabNavItemLabelStyle, animatedStyle],
              children: (0, _i18n.translate)(config.labelTx)
            })
          }, config == null ? undefined : config.name);
        }), (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
          style: underlineStyle
        })]
      })
    });
  };
  var _Dimensions$get = _reactNative2.Dimensions.get("window"),
    screenWidth = _Dimensions$get.width;
  var NO_PADDING_TAB_BAR_HEIGHT = exports.NO_PADDING_TAB_BAR_HEIGHT = 40;
  // The padding and refinement of the tab bar
  var TAB_BAR_PADDING_N_REFINEMENT = exports.TAB_BAR_PADDING_N_REFINEMENT = 40;
  var styles = _reactNative2.StyleSheet.create({
    containerStyle: {
      height: NO_PADDING_TAB_BAR_HEIGHT + 16,
      paddingTop: 16
    },
    noPaddingContainerStyle: {
      height: NO_PADDING_TAB_BAR_HEIGHT
    },
    tabBarContainerStyle: {
      height: 40,
      flexDirection: "row",
      borderBottomWidth: 1,
      borderBottomColor: _theme.color.palette.lighterGrey
    },
    tabNavItemStyle: {
      alignItems: "center",
      height: 40,
      justifyContent: "center",
      width: screenWidth / 3
    },
    tabNavItemLabelStyle: Object.assign({}, _text.newPresets.caption2Bold, {
      color: _theme.color.palette.darkGrey999
    }),
    tabNavItemLabelActiveStyle: {
      color: _theme.color.palette.almostBlackGrey
    }
  });
  var _default = exports.default = (0, _react.memo)(DineShopTabBar);
