  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _backgrounds = _$$_REQUIRE(_dependencyMap[6]);
  var _react = _$$_REQUIRE(_dependencyMap[7]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[8]);
  var _dineShopV = _$$_REQUIRE(_dependencyMap[9]);
  var _dineShopV2 = _$$_REQUIRE(_dependencyMap[10]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[11]);
  var _utils = _$$_REQUIRE(_dependencyMap[12]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[13]);
  var _i18n = _$$_REQUIRE(_dependencyMap[14]);
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[15]);
  var _icons = _$$_REQUIRE(_dependencyMap[16]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[18]));
  var _dineShopV2TabNavigator = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[19]));
  var _native = _$$_REQUIRE(_dependencyMap[20]);
  var _dineShopStaticTileList = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[21]));
  var _dineShopOtherAvailable = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[22]));
  var _dineShopPlaypassList = _$$_REQUIRE(_dependencyMap[23]);
  var _dineShopActiveParking = _$$_REQUIRE(_dependencyMap[24]);
  var _navigators = _$$_REQUIRE(_dependencyMap[25]);
  var _dineImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[26]));
  var _dineBackgroundImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[27]));
  var _useRewardTier2 = _$$_REQUIRE(_dependencyMap[28]);
  var _changiRewardsMemberCard = _$$_REQUIRE(_dependencyMap[29]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[30]);
  var _dineShopCme = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[31]));
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[32]);
  var _account = _$$_REQUIRE(_dependencyMap[33]);
  var _dineShopEarnedForToday = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[34]));
  var _dineShopTabBar = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[35]));
  var _dineShopV3 = _$$_REQUIRE(_dependencyMap[36]);
  var _isNumber2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[37]));
  var _focusStatusBar = _$$_REQUIRE(_dependencyMap[38]);
  var _theme = _$$_REQUIRE(_dependencyMap[39]);
  var _dineShopLoadingTiles = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[40]));
  var _dineError = _$$_REQUIRE(_dependencyMap[41]);
  var _globalLoadingController = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[42]));
  var _constants = _$$_REQUIRE(_dependencyMap[43]);
  var _exploreItemType = _$$_REQUIRE(_dependencyMap[44]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[45]);
  var _imageManagerRedux = _$$_REQUIRE(_dependencyMap[46]);
  var _lodash = _$$_REQUIRE(_dependencyMap[47]);
  var _aemGroupTwo = _$$_REQUIRE(_dependencyMap[48]);
  var _adobe = _$$_REQUIRE(_dependencyMap[49]);
  var _luckyDraw = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[50]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[51]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var AnimatedLinearGradient = _reactNativeReanimated.default.createAnimatedComponent(_reactNativeLinearGradient.default);
  var _Dimensions$get = _reactNative2.Dimensions.get("window"),
    screenHeight = _Dimensions$get.height,
    screenWidth = _Dimensions$get.width;
  var _worklet_4835769135521_init_data = {
    code: "function dineShopV2Tsx1(){const{animatedHeight}=this.__closure;return{height:animatedHeight.value};}"
  };
  var _worklet_7841774232580_init_data = {
    code: "function dineShopV2Tsx2(){const{animatedTabContentOpacity}=this.__closure;return{opacity:animatedTabContentOpacity.value};}"
  };
  var _worklet_6614154823109_init_data = {
    code: "function dineShopV2Tsx3(){const{animatedViewAllPromosOpacity}=this.__closure;return{opacity:animatedViewAllPromosOpacity.value};}"
  };
  var _worklet_10802434208938_init_data = {
    code: "function dineShopV2Tsx4(){const{pageOffsetY,TOP_BAR_VISIBLE_TRIGGER_POINT,topBarStyleOpacity,withTiming}=this.__closure;if(pageOffsetY.value>=TOP_BAR_VISIBLE_TRIGGER_POINT){topBarStyleOpacity.value=withTiming(1,{duration:150});}else{topBarStyleOpacity.value=withTiming(0,{duration:150});}return{opacity:topBarStyleOpacity.value};}"
  };
  var _worklet_1170758351589_init_data = {
    code: "function dineShopV2Tsx5(){const{animatedTabBarHeight}=this.__closure;return{height:animatedTabBarHeight.value,overflow:\"hidden\"};}"
  };
  var _worklet_13762661738352_init_data = {
    code: "function dineShopV2Tsx6(){const{interpolateColor,tabContentPy,STICKY_TAB_BAR_VISIBLE_TRIGGER_POINT,color,BACKGROUND_COLOR}=this.__closure;const backgroundColor=interpolateColor(tabContentPy.value,[0,STICKY_TAB_BAR_VISIBLE_TRIGGER_POINT],[color.palette.lightestGrey,BACKGROUND_COLOR]);return{backgroundColor:backgroundColor};}"
  };
  var DineShopScreenV2 = function DineShopScreenV2(_ref) {
    var _useContext, _data$parkingPerks, _data$parkingPerks2;
    var navigation = _ref.navigation,
      route = _ref.route;
    var _ref2 = (route == null ? undefined : route.params) || {},
      screen = _ref2.screen;
    var dineShopV2Banner = (0, _reactRedux.useSelector)(_imageManagerRedux.ImageManagerSelectors.dineShopV2Banner);
    var dineShopV2Background = (0, _reactRedux.useSelector)(_imageManagerRedux.ImageManagerSelectors.dineShopV2Background);
    var isShopDineV2 = (0, _remoteConfig.getFeatureFlagInit)(_remoteConfig.REMOTE_CONFIG_FLAGS.SHOPDINE_V2);
    var _useState = (0, _react.useState)(_dineShopV.styles.bannerImageDefaultStyle),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      bannerImageStyle = _useState2[0],
      setBannerImageStyle = _useState2[1];
    var _useState3 = (0, _react.useState)(_dineShopV.styles.bgImageDefaultStyle),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      bgImageStyle = _useState4[0],
      setBgImageStyle = _useState4[1];
    var _useState5 = (0, _react.useState)(0),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      measuredPromoHeight = _useState6[0],
      setMeasuredPromoHeight = _useState6[1];
    var _useState7 = (0, _react.useState)("light-content"),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      statusBarStyle = _useState8[0],
      setStatusBarStyle = _useState8[1];
    var _ref3 = (0, _reactRedux.useSelector)(_aemGroupTwo.AemGroupTwoSelectors.dineShopEpicLandingPagePayload) || {},
      loggedInTextCopy = _ref3.loggedInTextCopy,
      nonLoggedInTextCopy = _ref3.nonLoggedInTextCopy;
    var _useRewardTier = (0, _useRewardTier2.useRewardTier)(),
      memberIconInfo = _useRewardTier.memberIconInfo;
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var animatedHeight = (0, _reactNativeReanimated.useSharedValue)(_dineShopV.INITIAL_VISIBLE_TAB_CONTENT_HEIGHT);
    var animatedTabContentOpacity = (0, _reactNativeReanimated.useSharedValue)(0);
    var animatedScrollX = (0, _reactNativeReanimated.useSharedValue)(0);
    var animatedViewAllPromosOpacity = (0, _reactNativeReanimated.useSharedValue)(1);
    var contentHeight = (0, _reactNativeReanimated.useSharedValue)(screenHeight + 100);
    var dineHeight = (0, _reactNativeReanimated.useSharedValue)(0);
    var shopHeight = (0, _reactNativeReanimated.useSharedValue)(0);
    var marketHeight = (0, _reactNativeReanimated.useSharedValue)(0);
    var animatedTabBarHeight = (0, _reactNativeReanimated.useSharedValue)(0);
    var tabContentPy = (0, _reactNativeReanimated.useSharedValue)(0);
    var isFirstTimeFocus = (0, _react.useRef)(true);
    var isMounted = (0, _react.useRef)(false);
    var overallScrollRef = (0, _react.useRef)(null);
    var contentRef = (0, _react.useRef)(null);
    var tabContentScrollRef = (0, _react.useRef)(null);
    var _useGeneratePlayPassU = (0, _screenHook.useGeneratePlayPassUrl)("dineShopEpic"),
      getPlayPassUrl = _useGeneratePlayPassU.getPlayPassUrl;
    var isAccountV2CM24 = (0, _remoteConfig.isFlagOnCondition)((_useContext = (0, _react.useContext)(_account.AccountContext)) == null ? undefined : _useContext.accountCM24FeatureFlag);
    var _useHandleScroll = (0, _navigators.useHandleScroll)(),
      handleScroll = _useHandleScroll.handleScroll,
      isTabVisible = _useHandleScroll.isTabVisible;
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("STATIC_PARKING_CM_TILE"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var pageOffsetY = (0, _reactNativeReanimated.useSharedValue)(0);
    var topBarStyleOpacity = (0, _reactNativeReanimated.useSharedValue)(0);

    // Initiate all dine & shop screen requests
    var _useInitializeData = (0, _dineShopV2.useInitializeData)(),
      isRefreshFinished = _useInitializeData.isRefreshFinished,
      setIsRefresh = _useInitializeData.setIsRefresh,
      setShowRefreshIndicator = _useInitializeData.setShowRefreshIndicator,
      showRefreshIndicator = _useInitializeData.showRefreshIndicator,
      expandDisabled = _useInitializeData.expandDisabled,
      data = _useInitializeData.data,
      isLoading = _useInitializeData.isLoading,
      isError = _useInitializeData.isError;
    var _useState9 = (0, _react.useState)([]),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      loadedScreen = _useState0[0],
      setLoadedScreen = _useState0[1];
    var transactionSummaryData = data == null ? undefined : data.transactionSummary;
    var isActiveLandingPage = (transactionSummaryData == null ? undefined : transactionSummaryData.isLogin) && (data == null ? undefined : data.transactionSummary.totalNrOfTransactions) > 0;
    var isAEM = data == null || (_data$parkingPerks = data.parkingPerks) == null ? undefined : _data$parkingPerks.aemParkingTile;
    var isPP = data == null ? undefined : data.playpassPackages;
    var isCM = data == null ? undefined : data.cmPerks;
    var isCPMS = data == null || (_data$parkingPerks2 = data.parkingPerks) == null ? undefined : _data$parkingPerks2.cpmsParkingTile;
    var isTilesFetching = isLoading;
    var showError = !isTilesFetching && (0, _utils.ifOneTrue)([isError, isActiveLandingPage ? (0, _utils.ifAllTrue)([!isPP, !isAEM, !isCM, !isCPMS]) : (0, _utils.ifAllTrue)([!isPP, !isAEM])]);
    var isShowText = isTilesFetching || !showError;
    var promoListStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var dineShopV2Tsx1 = function dineShopV2Tsx1() {
        return {
          height: animatedHeight.value
        };
      };
      dineShopV2Tsx1.__closure = {
        animatedHeight: animatedHeight
      };
      dineShopV2Tsx1.__workletHash = 4835769135521;
      dineShopV2Tsx1.__initData = _worklet_4835769135521_init_data;
      return dineShopV2Tsx1;
    }(), [measuredPromoHeight, animatedHeight.value]);
    var tabContentStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var dineShopV2Tsx2 = function dineShopV2Tsx2() {
        return {
          opacity: animatedTabContentOpacity.value
        };
      };
      dineShopV2Tsx2.__closure = {
        animatedTabContentOpacity: animatedTabContentOpacity
      };
      dineShopV2Tsx2.__workletHash = 7841774232580;
      dineShopV2Tsx2.__initData = _worklet_7841774232580_init_data;
      return dineShopV2Tsx2;
    }(), [animatedTabContentOpacity.value]);
    var viewAllPromosStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var dineShopV2Tsx3 = function dineShopV2Tsx3() {
        return {
          opacity: animatedViewAllPromosOpacity.value
        };
      };
      dineShopV2Tsx3.__closure = {
        animatedViewAllPromosOpacity: animatedViewAllPromosOpacity
      };
      dineShopV2Tsx3.__workletHash = 6614154823109;
      dineShopV2Tsx3.__initData = _worklet_6614154823109_init_data;
      return dineShopV2Tsx3;
    }());
    var topBarStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var dineShopV2Tsx4 = function dineShopV2Tsx4() {
        if (pageOffsetY.value >= _dineShopV.TOP_BAR_VISIBLE_TRIGGER_POINT) {
          topBarStyleOpacity.value = (0, _reactNativeReanimated.withTiming)(1, {
            duration: 150
          });
        } else {
          topBarStyleOpacity.value = (0, _reactNativeReanimated.withTiming)(0, {
            duration: 150
          });
        }
        return {
          opacity: topBarStyleOpacity.value
        };
      };
      dineShopV2Tsx4.__closure = {
        pageOffsetY: pageOffsetY,
        TOP_BAR_VISIBLE_TRIGGER_POINT: _dineShopV.TOP_BAR_VISIBLE_TRIGGER_POINT,
        topBarStyleOpacity: topBarStyleOpacity,
        withTiming: _reactNativeReanimated.withTiming
      };
      dineShopV2Tsx4.__workletHash = 10802434208938;
      dineShopV2Tsx4.__initData = _worklet_10802434208938_init_data;
      return dineShopV2Tsx4;
    }(), [pageOffsetY.value]);
    var tabBarStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var dineShopV2Tsx5 = function dineShopV2Tsx5() {
        return {
          height: animatedTabBarHeight.value,
          overflow: "hidden"
        };
      };
      dineShopV2Tsx5.__closure = {
        animatedTabBarHeight: animatedTabBarHeight
      };
      dineShopV2Tsx5.__workletHash = 1170758351589;
      dineShopV2Tsx5.__initData = _worklet_1170758351589_init_data;
      return dineShopV2Tsx5;
    }(), [animatedTabBarHeight.value]);
    var bgStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var dineShopV2Tsx6 = function dineShopV2Tsx6() {
        var backgroundColor = (0, _reactNativeReanimated.interpolateColor)(tabContentPy.value, [0, _dineShopV.STICKY_TAB_BAR_VISIBLE_TRIGGER_POINT], [_theme.color.palette.lightestGrey, _dineShopV.BACKGROUND_COLOR]);
        return {
          backgroundColor: backgroundColor
        };
      };
      dineShopV2Tsx6.__closure = {
        interpolateColor: _reactNativeReanimated.interpolateColor,
        tabContentPy: tabContentPy,
        STICKY_TAB_BAR_VISIBLE_TRIGGER_POINT: _dineShopV.STICKY_TAB_BAR_VISIBLE_TRIGGER_POINT,
        color: _theme.color,
        BACKGROUND_COLOR: _dineShopV.BACKGROUND_COLOR
      };
      dineShopV2Tsx6.__workletHash = 13762661738352;
      dineShopV2Tsx6.__initData = _worklet_13762661738352_init_data;
      return dineShopV2Tsx6;
    }(), [tabContentPy.value]);
    var textCopy = (0, _react.useMemo)(function () {
      if (!transactionSummaryData || isActiveLandingPage) {
        return "";
      }
      if (!isLoggedIn) {
        return nonLoggedInTextCopy || (0, _i18n.translate)("dineShopScreen.nonLoggedInTextCopy");
      }
      return loggedInTextCopy || (0, _i18n.translate)("dineShopScreen.loggedInTextCopy");
    }, [isLoggedIn, loggedInTextCopy, nonLoggedInTextCopy, isActiveLandingPage]);
    var imageBgCoverStyle = (0, _react.useMemo)(function () {
      return {
        backgroundColor: _dineShopV.BACKGROUND_COLOR,
        height: measuredPromoHeight,
        left: 0,
        marginTop: Number(bannerImageStyle.height),
        position: "absolute",
        top: 0,
        width: screenWidth
      };
    }, [measuredPromoHeight, bannerImageStyle.height]);
    var handlePromoPerkLayout = function handlePromoPerkLayout(evt) {
      var _evt$nativeEvent;
      var newHeight = evt == null || (_evt$nativeEvent = evt.nativeEvent) == null || (_evt$nativeEvent = _evt$nativeEvent.layout) == null ? undefined : _evt$nativeEvent.height;
      newHeight = newHeight > _dineShopV.MAXIMUM_INITIAL_PROMO_PERK_VISIBLE_HEIGHT ? newHeight : _dineShopV.MAXIMUM_INITIAL_PROMO_PERK_VISIBLE_HEIGHT;
      setMeasuredPromoHeight(function (oldValue) {
        return newHeight !== oldValue ? newHeight : oldValue;
      });
    };
    var handleToggleExpandBtnVisible = function handleToggleExpandBtnVisible(isEnable) {
      if (!isEnable) {
        animatedViewAllPromosOpacity.value = (0, _reactNativeReanimated.withTiming)(0);
        return;
      }
      animatedViewAllPromosOpacity.value = 1;
    };
    var handleExpandPromoPerk = function handleExpandPromoPerk() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.EPICDineAndShop, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.EPICDineAndShop, "View All Promotions"));
      if (animatedViewAllPromosOpacity.value !== 1) return;
      handleToggleExpandBtnVisible(false);
      animatedHeight.value = (0, _reactNativeReanimated.withTiming)(measuredPromoHeight);
    };
    var resetScrollingState = function resetScrollingState() {
      setTimeout(function () {
        animatedHeight.value = (0, _reactNativeReanimated.withTiming)(_dineShopV.MAXIMUM_INITIAL_PROMO_PERK_VISIBLE_HEIGHT, {
          duration: 500,
          easing: _reactNativeReanimated.Easing.out(_reactNativeReanimated.Easing.quad)
        });
        animatedTabContentOpacity.value = (0, _reactNativeReanimated.withTiming)(1, {
          duration: 500,
          easing: _reactNativeReanimated.Easing.out(_reactNativeReanimated.Easing.quad)
        });
      }, 500);
    };
    var onScroll = function onScroll(e) {
      var _e$nativeEvent, _route$params, _tabContentScrollRef$;
      var offsetY = e == null || (_e$nativeEvent = e.nativeEvent) == null || (_e$nativeEvent = _e$nativeEvent.contentOffset) == null ? undefined : _e$nativeEvent.y;
      handleScroll(e);
      route == null || (_route$params = route.params) == null || _route$params.setOptions == null || _route$params.setOptions({
        tabBarVisible: isTabVisible.current
      });
      if (offsetY >= _dineShopV.TOP_BAR_VISIBLE_TRIGGER_POINT) {
        setStatusBarStyle("dark-content");
      } else {
        setStatusBarStyle("light-content");
      }
      (_tabContentScrollRef$ = tabContentScrollRef.current) == null || _tabContentScrollRef$.measure == null || _tabContentScrollRef$.measure(function (_fx, _fy, _width, _height, _px, py) {
        tabContentPy.value = py;
        if (py <= _dineShopV.STICKY_TAB_BAR_VISIBLE_TRIGGER_POINT) {
          animatedTabBarHeight.value = _dineShopTabBar.NO_PADDING_TAB_BAR_HEIGHT;
        } else {
          animatedTabBarHeight.value = 0;
        }
      });
      pageOffsetY.value = offsetY;
    };
    var isFirstMountDone = (0, _react.useRef)(false);
    var populatePosition = function populatePosition() {
      var _contentRef$current, _overallScrollRef$cur;
      if (!isFirstMountDone.current) {
        isFirstMountDone.current = true;
        if (!screen) return;
      }
      contentRef == null || (_contentRef$current = contentRef.current) == null || _contentRef$current.measureLayout == null || _contentRef$current.measureLayout(overallScrollRef == null || (_overallScrollRef$cur = overallScrollRef.current) == null ? undefined : _overallScrollRef$cur.getInnerViewNode(), function (x, y) {
        // Scroll to the position of the ref
        overallScrollRef.current.scrollTo({
          y: y - _dineShopTabBar.NO_PADDING_TAB_BAR_HEIGHT - _dineShopTabBar.TAB_BAR_PADDING_N_REFINEMENT,
          animated: true
        });
      });
    };
    var handleScrollToTab = function handleScrollToTab(newTab) {
      var _TAB_BAR_CONFIG$index;
      var indexToScroll = _dineShopV3.TAB_BAR_CONFIG.findIndex(function (config) {
        return config.name === newTab;
      });
      if ((0, _isNumber2.default)(indexToScroll)) {
        var _tabContentScrollRef$2;
        tabContentScrollRef == null || (_tabContentScrollRef$2 = tabContentScrollRef.current) == null || _tabContentScrollRef$2.scrollTo == null || _tabContentScrollRef$2.scrollTo({
          animated: true,
          x: indexToScroll * screenWidth
        });
      }
      if (!(0, _isNumber2.default)(indexToScroll)) {
        return;
      }
      if (indexToScroll === 0) {
        contentHeight.value = (0, _reactNativeReanimated.withSpring)(dineHeight.value + _dineShopV2TabNavigator.BOTTOM_SPACING);
      } else if (indexToScroll === 1) {
        contentHeight.value = (0, _reactNativeReanimated.withSpring)(shopHeight.value + _dineShopV2TabNavigator.BOTTOM_SPACING);
      } else {
        contentHeight.value = (0, _reactNativeReanimated.withSpring)(marketHeight.value + _dineShopV2TabNavigator.BOTTOM_SPACING);
      }
      populatePosition();
      indexTab(newTab);
      if (_dineShopV3.TAB_BAR_CONFIG != null && (_TAB_BAR_CONFIG$index = _dineShopV3.TAB_BAR_CONFIG[indexToScroll]) != null && _TAB_BAR_CONFIG$index.labelTx) {
        var _TAB_BAR_CONFIG$index2;
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppShopDineTopMenuToggle, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppShopDineTopMenuToggle, (0, _i18n.translate)(_dineShopV3.TAB_BAR_CONFIG == null || (_TAB_BAR_CONFIG$index2 = _dineShopV3.TAB_BAR_CONFIG[indexToScroll]) == null ? undefined : _TAB_BAR_CONFIG$index2.labelTx)));
      }
    };
    var indexTab = function indexTab(name) {
      if (loadedScreen.includes(name)) {
        return;
      }
      _reactNative2.InteractionManager.runAfterInteractions(function () {
        setLoadedScreen(function (loaded) {
          var after = [].concat((0, _toConsumableArray2.default)(loaded), [name]);
          return after;
        });
      });
    };
    var handlePressParkingCMTile = function handlePressParkingCMTile(item) {
      var _item$cta;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.EPICDineAndShop, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.EPICDineAndShop, `Pre-spend | ${item == null ? undefined : item.title} | ${item == null ? undefined : item.tag} | Null`));
      var navigationData = item == null || (_item$cta = item.cta) == null ? undefined : _item$cta.navigationLink;
      if (navigationData != null && navigationData.value) {
        var _item$cta2;
        handleNavigation(navigationData == null ? undefined : navigationData.type, navigationData == null ? undefined : navigationData.value, item == null || (_item$cta2 = item.cta) == null ? undefined : _item$cta2.aemUtmRedirect);
      }
    };
    var handlePressPPTile = function handlePressPPTile(item) {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.EPICDineAndShop, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.EPICDineAndShop, `Pre-spend | ${item == null ? undefined : item.name} | ${item == null ? undefined : item.header} | Null`));
      if (!(item != null && item.code)) return;
      _globalLoadingController.default.showLoading(true);
      getPlayPassUrl(_constants.StateCode.PPEVENT, item == null ? undefined : item.code, {
        entryPoint: _exploreItemType.PlayPassEntryPoint.DINE_SHOP_EPIC,
        eventName: item == null ? undefined : item.name
      });
    };
    var handlePullToRefresh = function handlePullToRefresh() {
      setIsRefresh(true);
      setShowRefreshIndicator(true);
      resetScrollingState();
      handleToggleExpandBtnVisible(true);
    };
    (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
      if (isFirstTimeFocus != null && isFirstTimeFocus.current) {
        resetScrollingState();
        isFirstTimeFocus.current = false;
      }
    }, []));
    (0, _react.useEffect)(function () {
      if (!(dineShopV2Banner != null && dineShopV2Banner.uri)) return;
      var imageSize = (0, _dineShopV.calculateBannerImageSize)(dineShopV2Banner == null ? undefined : dineShopV2Banner.width, dineShopV2Banner == null ? undefined : dineShopV2Banner.height);
      setBannerImageStyle(function (oldStyles) {
        return Object.assign({}, oldStyles, imageSize);
      });
    }, [dineShopV2Banner == null ? undefined : dineShopV2Banner.uri]);
    (0, _react.useEffect)(function () {
      if (!(dineShopV2Background != null && dineShopV2Background.uri)) return;
      setBgImageStyle(function (oldStyles) {
        return Object.assign({}, oldStyles, (0, _dineShopV.calculateImageSize)(dineShopV2Background == null ? undefined : dineShopV2Background.width, dineShopV2Background == null ? undefined : dineShopV2Background.height));
      });
    }, [dineShopV2Background == null ? undefined : dineShopV2Background.uri]);
    (0, _react.useEffect)(function () {
      if (isMounted != null && isMounted.current) {
        var _overallScrollRef$cur2;
        isFirstTimeFocus.current = true;
        handleToggleExpandBtnVisible(true);
        overallScrollRef == null || (_overallScrollRef$cur2 = overallScrollRef.current) == null || _overallScrollRef$cur2.scrollTo == null || _overallScrollRef$cur2.scrollTo({
          animated: true,
          y: 0
        });
        animatedHeight.value = _dineShopV.INITIAL_VISIBLE_TAB_CONTENT_HEIGHT;
      } else {
        isMounted.current = true;
      }
      isFirstMountDone.current = false;
    }, [isLoggedIn]);
    (0, _react.useEffect)(function () {
      if (isRefreshFinished && showError) {
        animatedHeight.value = "auto";
        animatedViewAllPromosOpacity.value = 0;
      }
    }, [isRefreshFinished, showError]);
    var renderLoading = function renderLoading() {
      return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
        children: (0, _jsxRuntime.jsx)(_dineShopLoadingTiles.default, {})
      });
    };
    var renderParkingPromotion = function renderParkingPromotion() {
      var _data$parkingPerks3;
      var isMonarch = (memberIconInfo == null ? undefined : memberIconInfo.title) === _changiRewardsMemberCard.Tier.Monarch || (memberIconInfo == null ? undefined : memberIconInfo.title) === _changiRewardsMemberCard.Tier.StaffMonarch;
      var disclaimer = data == null || (_data$parkingPerks3 = data.parkingPerks) == null ? undefined : _data$parkingPerks3.disclaimer;
      if (isMonarch) return null;
      if (!(0, _lodash.isEmpty)(disclaimer)) {
        return (0, _jsxRuntime.jsx)(_text.Text, {
          style: _dineShopV.styles.noPromotionDisclaimer,
          text: disclaimer
        });
      }
      return (0, _jsxRuntime.jsx)(_dineShopActiveParking.DineShopActiveParking, {
        data: data
      });
    };
    var renderContentActive = function renderContentActive() {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _dineShopV.styles.promotionPerkContainerNoPaddingTopStyle,
        children: isActiveLandingPage ? (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [renderParkingPromotion(), (0, _jsxRuntime.jsx)(_dineShopPlaypassList.DineShopPlayPass, {
            data: data
          }), isAccountV2CM24 && isCM && (0, _jsxRuntime.jsx)(_dineShopCme.default, {
            data: data
          }), isAccountV2CM24 && isLoggedIn && (0, _jsxRuntime.jsx)(_luckyDraw.default, {
            data: data == null ? undefined : data.luckyDrawPerks
          }), (0, _jsxRuntime.jsx)(_dineShopOtherAvailable.default, {
            data: data
          })]
        }) : (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [(0, _jsxRuntime.jsx)(_dineShopStaticTileList.default, {
            onPressParkingCMTile: handlePressParkingCMTile,
            onPressPPTile: handlePressPPTile,
            data: data
          }), isAccountV2CM24 && isCM && (0, _jsxRuntime.jsx)(_dineShopCme.default, {
            data: data
          }), isAccountV2CM24 && isLoggedIn && (0, _jsxRuntime.jsx)(_luckyDraw.default, {
            data: data == null ? undefined : data.luckyDrawPerks
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: {
              height: 44
            }
          })]
        })
      });
    };
    var renderContent = function renderContent() {
      if (isTilesFetching) {
        return renderLoading();
      } else {
        //Scenario 2 and 11
        if (showError) {
          return (0, _jsxRuntime.jsx)(_dineError.DineError, {});
        } else {
          return renderContentActive();
        }
      }
    };
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_focusStatusBar.FocusAwareStatusBar, {
        translucent: true,
        backgroundColor: "transparent",
        barStyle: statusBarStyle
      }), (0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
        style: [_dineShopV.styles.topBarContainerStyle, topBarStyle],
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: _dineShopV.styles.topBarTitleTextStyle,
          tx: "dineShopScreen.title"
        }), !isShopDineV2 && (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
          style: tabBarStyle,
          children: (0, _jsxRuntime.jsx)(_dineShopTabBar.default, {
            animatedScrollX: animatedScrollX,
            handleScrollToTab: handleScrollToTab,
            noPaddingTop: true
          })
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
        style: bgStyle,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _dineShopV.styles.bannerImageContainerStyle,
          children: (0, _jsxRuntime.jsx)(_dineImage.default, {
            alternativeImageSource: dineShopV2Banner != null && dineShopV2Banner.uri ? dineShopV2Banner == null ? undefined : dineShopV2Banner.uri : _backgrounds.DineShopEpicDefaultBanner,
            resizeMode: "contain",
            source: dineShopV2Banner != null && dineShopV2Banner.uri ? {
              uri: dineShopV2Banner == null ? undefined : dineShopV2Banner.uri
            } : _backgrounds.DineShopEpicDefaultBanner,
            style: [bannerImageStyle]
          })
        }), (0, _jsxRuntime.jsxs)(_reactNative2.ScrollView, {
          alwaysBounceVertical: false,
          onScroll: onScroll,
          ref: overallScrollRef,
          showsVerticalScrollIndicator: false,
          style: _dineShopV.styles.containerStyle,
          refreshControl: (0, _jsxRuntime.jsx)(_reactNative.RefreshControl, {
            colors: [_theme.color.palette.whiteGrey],
            onRefresh: handlePullToRefresh,
            progressBackgroundColor: "transparent",
            progressViewOffset: 52,
            refreshing: showRefreshIndicator,
            tintColor: _theme.color.palette.whiteGrey
          }),
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: imageBgCoverStyle
            }), (0, _jsxRuntime.jsxs)(_dineBackgroundImage.default, {
              alternativeImageSource: dineShopV2Background != null && dineShopV2Background.uri ? dineShopV2Background == null ? undefined : dineShopV2Background.uri : _backgrounds.DineShopEpicDefaultBg,
              imageStyle: bgImageStyle,
              resizeMode: "contain",
              source: dineShopV2Background != null && dineShopV2Background.uri ? {
                uri: dineShopV2Background == null ? undefined : dineShopV2Background.uri
              } : _backgrounds.DineShopEpicDefaultBg,
              style: _dineShopV.styles.bgImageContainerStyle,
              trailing: true,
              children: [isActiveLandingPage ? !showError && (0, _jsxRuntime.jsx)(_dineShopEarnedForToday.default, {
                isRefreshFinished: isRefreshFinished,
                data: data
              }) : isShowText && (0, _jsxRuntime.jsx)(_text.Text, {
                style: _dineShopV.styles.textCopyTextStyle,
                text: textCopy
              }), (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
                style: [_dineShopV.styles.promotionPerkWrapperStyle, promoListStyle],
                children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
                  onLayout: handlePromoPerkLayout,
                  style: _dineShopV.styles.promotionPerkContainerStyle,
                  children: renderContent()
                })
              })]
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            children: [!showError && (0, _jsxRuntime.jsx)(AnimatedLinearGradient, {
              colors: ["rgba(18, 18, 18, 0)", "rgba(18, 18, 18, 0.5)"],
              end: {
                x: 0,
                y: 1
              },
              start: {
                x: 0,
                y: 0
              },
              style: [_dineShopV.styles.viewAllPromosContainerStyle, viewAllPromosStyle],
              children: (0, _jsxRuntime.jsxs)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
                accessibilityLabel: `${_dineShopV3.SCREEN_NAME}_Btn_ViewAllPromotions`,
                androidRippleColor: "transparent",
                disabled: !isRefreshFinished || expandDisabled,
                onPress: handleExpandPromoPerk,
                style: _dineShopV.styles.viewAllPromosBtnStyle,
                testID: `${_dineShopV3.SCREEN_NAME}_Btn_ViewAllPromotions`,
                children: [(0, _jsxRuntime.jsx)(_text.Text, {
                  style: _dineShopV.styles.viewAllPromosBtnLabelStyle,
                  text: "View All Promotions"
                }), (0, _jsxRuntime.jsx)(_icons.DownArrowGrey, {})]
              })
            }), (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
              ref: contentRef,
              style: [_dineShopV.styles.dineShopPageContent, tabContentStyle],
              children: (0, _jsxRuntime.jsx)(_dineShopV2TabNavigator.default, {
                animatedScrollX: animatedScrollX,
                contentHeight: contentHeight,
                dineHeight: dineHeight,
                handleScrollToTab: handleScrollToTab,
                marketHeight: marketHeight,
                navigation: navigation,
                route: route,
                shopHeight: shopHeight,
                tabContentScrollRef: tabContentScrollRef,
                loadedScreen: loadedScreen,
                indexTab: indexTab,
                overallScrollRef: overallScrollRef,
                isShopDineV2: isShopDineV2
              })
            })]
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: {
              backgroundColor: _theme.color.palette.lightestGrey,
              width: screenWidth,
              height: screenHeight / 3,
              position: 'absolute',
              bottom: 0,
              left: 0,
              transform: [{
                translateY: screenHeight / 3
              }]
            }
          })]
        })]
      })]
    });
  };
  var _default = exports.default = DineShopScreenV2;
