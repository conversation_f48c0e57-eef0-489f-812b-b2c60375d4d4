  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.TAB_CONTENT_GAP = exports.BOTTOM_SPACING = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _dineShopTabBar = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _$$_REQUIRE(_dependencyMap[3]);
  var _constants = _$$_REQUIRE(_dependencyMap[4]);
  var _get3 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _dineShopContext = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[7]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[9]);
  var _dineScreenV = _$$_REQUIRE(_dependencyMap[10]);
  var _dineShopV2TabNavigatorContent = _$$_REQUIRE(_dependencyMap[11]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var BOTTOM_SPACING = exports.BOTTOM_SPACING = 72;
  var _worklet_13459095320610_init_data = {
    code: "function dineShopV2TabNavigatorTsx1(event){const{animatedScrollX}=this.__closure;var _event$contentOffset;animatedScrollX.value=event===null||event===void 0||(_event$contentOffset=event.contentOffset)===null||_event$contentOffset===void 0?void 0:_event$contentOffset.x;}"
  };
  var _worklet_4276244072065_init_data = {
    code: "function dineShopV2TabNavigatorTsx2(){const{contentHeight,screenHeight}=this.__closure;return{height:Math.max(contentHeight.value,screenHeight)};}"
  };
  var DineShopV2TabNavigator = function DineShopV2TabNavigator(props) {
    var animatedScrollX = props.animatedScrollX,
      contentHeight = props.contentHeight,
      dineHeight = props.dineHeight,
      handleScrollToTab = props.handleScrollToTab,
      marketHeight = props.marketHeight,
      route = props.route,
      shopHeight = props.shopHeight,
      tabContentScrollRef = props.tabContentScrollRef,
      loadedScreen = props.loadedScreen,
      indexTab = props.indexTab,
      isShopDineV2 = props.isShopDineV2;
    var _get2 = (0, _get3.default)(route, "params", {}),
      screen = _get2.screen,
      timestamp = _get2.timestamp,
      section = _get2.section;
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var lastTimestampRef = (0, _react.useRef)(null);

    // layout measure:
    var onDineLayout = function onDineLayout(e) {
      var newHeight = e.nativeEvent.layout.height;
      if (newHeight && marketHeight.value !== newHeight) {
        dineHeight.value = newHeight;
        contentHeight.value = (0, _reactNativeReanimated.withSpring)(newHeight + BOTTOM_SPACING);
      }
    };
    var onShopLayout = function onShopLayout(e) {
      var newHeight = e.nativeEvent.layout.height;
      if (newHeight && marketHeight.value !== newHeight) {
        shopHeight.value = newHeight;
        contentHeight.value = (0, _reactNativeReanimated.withSpring)(newHeight + BOTTOM_SPACING);
      }
    };
    var onMarketLayout = function onMarketLayout(e) {
      var newHeight = e.nativeEvent.layout.height;
      if (newHeight && marketHeight.value !== newHeight) {
        marketHeight.value = newHeight;
        contentHeight.value = (0, _reactNativeReanimated.withSpring)(newHeight + BOTTOM_SPACING);
      }
    };
    var scrollHandler = (0, _reactNativeReanimated.useAnimatedScrollHandler)(function () {
      var dineShopV2TabNavigatorTsx1 = function dineShopV2TabNavigatorTsx1(event) {
        var _event$contentOffset;
        animatedScrollX.value = event == null || (_event$contentOffset = event.contentOffset) == null ? undefined : _event$contentOffset.x;
      };
      dineShopV2TabNavigatorTsx1.__closure = {
        animatedScrollX: animatedScrollX
      };
      dineShopV2TabNavigatorTsx1.__workletHash = 13459095320610;
      dineShopV2TabNavigatorTsx1.__initData = _worklet_13459095320610_init_data;
      return dineShopV2TabNavigatorTsx1;
    }());
    var scrollViewAnimatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var dineShopV2TabNavigatorTsx2 = function dineShopV2TabNavigatorTsx2() {
        return {
          height: Math.max(contentHeight.value, screenHeight)
        };
      };
      dineShopV2TabNavigatorTsx2.__closure = {
        contentHeight: contentHeight,
        screenHeight: screenHeight
      };
      dineShopV2TabNavigatorTsx2.__workletHash = 4276244072065;
      dineShopV2TabNavigatorTsx2.__initData = _worklet_4276244072065_init_data;
      return dineShopV2TabNavigatorTsx2;
    }());
    (0, _react.useEffect)(function () {
      if (isShopDineV2 && section) return;
      var hasRouteScreen = !!screen && !!timestamp && timestamp !== lastTimestampRef.current;
      var timeoutValue = hasRouteScreen ? 2100 : 1;
      var timeoutId = null;
      timeoutId = setTimeout(function () {
        if (hasRouteScreen) {
          lastTimestampRef.current = timestamp;
          handleScrollToTab(screen);
          indexTab(screen);
        } else {
          handleScrollToTab(_constants.NavigationConstants.marketplace);
          indexTab(_constants.NavigationConstants.marketplace);
        }
      }, timeoutValue);
      return function () {
        if (timeoutId) clearTimeout(timeoutId);
      };
    }, [screen, timestamp, isLoggedIn, section, isShopDineV2]);
    return (0, _jsxRuntime.jsxs)(_dineShopContext.DineShopContext.Provider, {
      value: {
        Handlers: _dineShopContext.Handlers
      },
      children: [(0, _jsxRuntime.jsx)(_dineShopTabBar.default, {
        animatedScrollX: animatedScrollX,
        handleScrollToTab: handleScrollToTab,
        isShopDineV2: isShopDineV2
      }), isShopDineV2 ? (0, _jsxRuntime.jsx)(_dineScreenV.DineScreenV2, {
        overallScrollRef: props.overallScrollRef,
        route: route
      }) : (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.ScrollView, {
        horizontal: true,
        onScroll: scrollHandler,
        scrollEnabled: false,
        pagingEnabled: true,
        ref: tabContentScrollRef,
        style: [styles.tabBarWrapperViewStyle, scrollViewAnimatedStyle],
        showsHorizontalScrollIndicator: false,
        children: (0, _jsxRuntime.jsx)(_dineShopV2TabNavigatorContent.DineShopV2TabContent, {
          loadedScreen: loadedScreen,
          props: props,
          onDineLayout: onDineLayout,
          onShopLayout: onShopLayout,
          onMarketLayout: onMarketLayout,
          styles: styles
        })
      })]
    });
  };
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    screenHeight = _Dimensions$get.height,
    screenWidth = _Dimensions$get.width;
  var TAB_CONTENT_GAP = exports.TAB_CONTENT_GAP = 8;
  var styles = _reactNative.StyleSheet.create({
    tabBarWrapperViewStyle: {
      minHeight: screenHeight - 114,
      gap: TAB_CONTENT_GAP,
      width: screenWidth
    },
    tabContentStyle: {
      width: screenWidth
    },
    emptyContentStyle: {
      height: screenHeight - 114,
      width: screenWidth
    }
  });
  var _default = exports.default = (0, _react.memo)(DineShopV2TabNavigator);
