  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.DineShopV2TabContent = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _constants = _$$_REQUIRE(_dependencyMap[2]);
  var _dineScreen = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _shopScreen = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _marketPlaceScreen = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  var DineShopV2TabContent = exports.DineShopV2TabContent = function DineShopV2TabContent(_ref) {
    var loadedScreen = _ref.loadedScreen,
      props = _ref.props,
      onDineLayout = _ref.onDineLayout,
      onShopLayout = _ref.onShopLayout,
      onMarketLayout = _ref.onMarketLayout,
      styles = _ref.styles;
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.tabContentStyle,
        children: loadedScreen.includes(_constants.NavigationConstants.dine) && (0, _jsxRuntime.jsx)(_dineScreen.default, Object.assign({}, props, {
          onLayout: onDineLayout
        }))
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.tabContentStyle,
        children: loadedScreen.includes(_constants.NavigationConstants.shop) && (0, _jsxRuntime.jsx)(_shopScreen.default, Object.assign({}, props, {
          onLayout: onShopLayout
        }))
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.tabContentStyle,
        children: loadedScreen.includes(_constants.NavigationConstants.marketplace) && (0, _jsxRuntime.jsx)(_marketPlaceScreen.default, Object.assign({}, props, {
          onLayout: onMarketLayout
        }))
      })]
    });
  };
