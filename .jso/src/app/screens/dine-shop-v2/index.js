  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _$$_REQUIRE(_dependencyMap[1]);
  var _dineShop = _$$_REQUIRE(_dependencyMap[2]);
  var _dineShopV = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _dineShopV2 = _$$_REQUIRE(_dependencyMap[4]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  var DineShopWrapper = function DineShopWrapper(props) {
    var _useDineShopFlags = (0, _dineShopV2.useDineShopFlags)(),
      isShopDineEpicV2On = _useDineShopFlags.isShopDineEpicV2On;
    return isShopDineEpicV2On ? (0, _jsxRuntime.jsx)(_dineShopV.default, Object.assign({}, props)) : (0, _jsxRuntime.jsx)(_dineShop.DineShopScreenWrapper, Object.assign({}, props));
  };
  var _default = exports.default = (0, _react.memo)(DineShopWrapper);
