  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.TaskCode = exports.TAB_BAR_CONFIG = exports.SCREEN_NAME = exports.PromotionTileLocationDisplaying = exports.CreditedTokenType = undefined;
  var _constants = _$$_REQUIRE(_dependencyMap[0]);
  var SCREEN_NAME = exports.SCREEN_NAME = "DineShopScreenV2";
  var TAB_BAR_CONFIG = exports.TAB_BAR_CONFIG = [{
    labelTx: "dineShopScreen.tabBar.dine",
    name: _constants.NavigationConstants.dine
  }, {
    labelTx: "dineShopScreen.tabBar.shop",
    name: _constants.NavigationConstants.shop
  }, {
    labelTx: "dineShopScreen.tabBar.marketplace",
    name: _constants.NavigationConstants.marketplace
  }];
  var PromotionTileLocationDisplaying = exports.PromotionTileLocationDisplaying = /*#__PURE__*/function (PromotionTileLocationDisplaying) {
    PromotionTileLocationDisplaying["OtherAvailablePerks"] = "otherAvailablePerks";
    PromotionTileLocationDisplaying["StaticListing"] = "staticListing";
    return PromotionTileLocationDisplaying;
  }({});
  var CreditedTokenType = exports.CreditedTokenType = /*#__PURE__*/function (CreditedTokenType) {
    CreditedTokenType["MiffySpend10"] = "miffyspend10-tkn";
    CreditedTokenType["MiffyISC60"] = "miffyisc60-tkn";
    return CreditedTokenType;
  }({});
  var TaskCode = exports.TaskCode = /*#__PURE__*/function (TaskCode) {
    TaskCode["Spend10"] = "SPEND10";
    TaskCode["ISC60"] = "ISC60";
    return TaskCode;
  }({});
