  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.uniqfyKeywordCollection = exports.renderTitleHeaderStepMid = exports.getUnitNumber = exports.getTerminalNameFromWe = exports.getTerminalIcon = exports.getTenantIcon = exports.getLevelName = exports.getAATagLevelSelector = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _icons = _$$_REQUIRE(_dependencyMap[3]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _lodash = _$$_REQUIRE(_dependencyMap[5]);
  var _adobe = _$$_REQUIRE(_dependencyMap[6]);
  var _i18n = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  var getTerminalNameFromWe = exports.getTerminalNameFromWe = function getTerminalNameFromWe(str) {
    var parts = str.split(".");
    return parts[parts.length - 2];
  };
  var getTerminalIcon = exports.getTerminalIcon = function getTerminalIcon(item) {
    var terminal = getTerminalNameFromWe(item == null ? undefined : item.whereDimension);
    switch (terminal) {
      case "t1":
        return (0, _jsxRuntime.jsx)(_icons.Terminal1, {});
      case "t2":
        return (0, _jsxRuntime.jsx)(_icons.Terminal2, {});
      case "t3":
        return (0, _jsxRuntime.jsx)(_icons.Terminal3, {});
      case "t4":
        return (0, _jsxRuntime.jsx)(_icons.Terminal4, {});
      case "jewel":
        return (0, _jsxRuntime.jsx)(_icons.Jewel, {});
      default:
        return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {});
    }
  };
  var getTenantIcon = exports.getTenantIcon = function getTenantIcon(item) {
    var _item$properties;
    var whatTax = (item == null ? undefined : item.whatDimension) || (item == null || (_item$properties = item.properties) == null ? undefined : _item$properties.dimension_what);
    switch (whatTax) {
      case "what.shop.food_anchor":
        return (0, _jsxRuntime.jsx)(_icons.DineIcon, {});
      case "what.shop.food":
        return (0, _jsxRuntime.jsx)(_icons.DineIcon, {});
      case "what.shop.retail_anchor":
        return (0, _jsxRuntime.jsx)(_icons.ShopIcon, {});
      case "what.shop.retail":
        return (0, _jsxRuntime.jsx)(_icons.ShopIcon, {});
      case "what.airplane_gate":
        return (0, _jsxRuntime.jsx)(_icons.GateIcon, {});
      case "what.infra.entrance":
        return (0, _jsxRuntime.jsx)(_icons.DoorIcon, {});
      case "what.infra.exit":
        return (0, _jsxRuntime.jsx)(_icons.DoorIcon, {});
      case "what.service.checkin":
        return (0, _jsxRuntime.jsx)(_icons.CheckInRowIcon, {});
      case "what.infra.carpark":
        return (0, _jsxRuntime.jsx)(_icons.CarParkIcon, {});
      case "what.shop.fun":
        return (0, _jsxRuntime.jsx)(_icons.AttractionIcon, {});
      case "what.service.baggage_claim":
        return (0, _jsxRuntime.jsx)(_icons.BaggageBeltIcon, {});
      default:
        return (0, _jsxRuntime.jsx)(_icons.FacilityIcon, {});
    }
  };
  var getLevelName = exports.getLevelName = function getLevelName(item) {
    var _item$properties2;
    var whereTax = (item == null ? undefined : item.whereDimension) || (item == null || (_item$properties2 = item.properties) == null ? undefined : _item$properties2.dimension_where);
    var parts = whereTax.split(".");
    var terminal = parts[parts.length - 2];
    var level = parts[parts.length - 1];
    var terminalLabel = terminal.includes("jewel") ? "" : "Terminal";
    var levelLabel = level.includes("l") ? "Level" : "Basement";
    return `${levelLabel} ${level.toUpperCase()}`;
  };
  var getUnitNumber = exports.getUnitNumber = function getUnitNumber(item) {
    var _item$properties3;
    var localRef = item == null || (_item$properties3 = item.properties) == null ? undefined : _item$properties3.localRef;
    var terminalActiveT1 = "T1";
    var terminalActiveT2 = "T2";
    var terminalActiveT3 = "T3";
    var terminalActiveT4 = "T4";
    var terminalActiveJ1 = "J1";
    var common_dining = "common_dining";
    var carpark = "carpark";
    var canopy_bridge = "canopy_bridge";
    var canopy_park = "canopy_park";
    var hedge_maze = "hedge_maze";
    var mirror_maze = "mirror_maze";
    var manulife_sky_nets_walking = "manulife_sky_nets_walking";
    var topiary_walk = "topiary_walk";
    var manulife_sky_nets_bouncing = "manulife_sky_nets_bouncing";
    var park = "park";
    var foggy_bowls = "foggy_bowls";
    var cloud9_piazza = "cloud9_piazza";
    var forest_valley_1 = "forest_valley_1";
    var rain_vortex = "rain_vortex";
    var carpark_electric = "carpark-electric";
    var entrance_jewel = "entrance_jewel";
    if (localRef != null && localRef.endsWith(common_dining) || localRef != null && localRef.endsWith(carpark) || localRef != null && localRef.endsWith(canopy_bridge) || localRef != null && localRef.endsWith(canopy_park) || localRef != null && localRef.endsWith(hedge_maze) || localRef != null && localRef.endsWith(mirror_maze) || localRef != null && localRef.endsWith(manulife_sky_nets_walking) || localRef != null && localRef.endsWith(topiary_walk) || localRef != null && localRef.endsWith(manulife_sky_nets_bouncing) || localRef != null && localRef.endsWith(park) || localRef != null && localRef.endsWith(foggy_bowls) || localRef != null && localRef.endsWith(cloud9_piazza) || localRef != null && localRef.endsWith(forest_valley_1) || localRef != null && localRef.endsWith(rain_vortex) || localRef != null && localRef.endsWith(carpark_electric) || localRef != null && localRef.endsWith(entrance_jewel)) {
      return "";
    } else {
      if (localRef != null && localRef.startsWith(terminalActiveT1) || localRef != null && localRef.startsWith(terminalActiveT2) || localRef != null && localRef.startsWith(terminalActiveT3) || localRef != null && localRef.startsWith(terminalActiveT4) || localRef != null && localRef.startsWith(terminalActiveJ1)) {
        var result = localRef == null ? undefined : localRef.split("_").slice(1).join("_");
        return `, #${result}`;
      } else {
        return "";
      }
    }
  };
  var renderTitleHeaderStepMid = exports.renderTitleHeaderStepMid = function renderTitleHeaderStepMid(data) {
    var isPortal = (data == null ? undefined : data.isPortal) === true;
    var whatDimension = data == null ? undefined : data.startEdge.whatDimension;
    if (isPortal && (whatDimension === "what.transport.skytrain" || whatDimension === "what.service.airport_transfer" || whatDimension === "what.infra.entrance" || whatDimension === "what.infra.exit")) {
      var _data$startEdge;
      return `${data == null || (_data$startEdge = data.startEdge) == null ? undefined : _data$startEdge.name}`;
    } else {
      var _data$endEdge;
      if ((data == null || (_data$endEdge = data.endEdge) == null || (_data$endEdge = _data$endEdge.name) == null ? undefined : _data$endEdge.length) > 0) {
        var _data$endEdge2;
        return `${(0, _i18n.translate)("changimap.To")} ${data == null || (_data$endEdge2 = data.endEdge) == null ? undefined : _data$endEdge2.name}`;
      } else {
        return "";
      }
    }
  };
  var getAATagLevelSelector = exports.getAATagLevelSelector = function getAATagLevelSelector(whereDim) {
    var parts = whereDim.split(".");
    var terminal = parts[parts.length - 2];
    var level = parts[parts.length - 1];
    return `${terminal.toUpperCase()}|${level.toUpperCase()}`;
  };
  var uniqfyKeywordCollection = exports.uniqfyKeywordCollection = function uniqfyKeywordCollection(sourceArr) {
    if ((0, _lodash.isEmpty)(sourceArr)) return;
    var preHandleSourceArr = (0, _lodash.uniq)(sourceArr).sort(function (a, b) {
      return a.length - b.length;
    });
    function isIncluded(tar, arr) {
      var isExist = arr.find(function (e) {
        return e.includes(tar);
      });
      if (isExist) return true;
      return false;
    }
    var finalArr = [];
    (0, _lodash.forEach)(preHandleSourceArr, function (e, i) {
      var newArr = (0, _toConsumableArray2.default)(preHandleSourceArr);
      newArr.splice(i, 1);
      var isExist = isIncluded(e, newArr);
      if (!isExist) {
        finalArr.push(e);
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppATOMSSearchKeyword, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppATOMSSearchKeyword, `${e}`));
      }
    });
  };
