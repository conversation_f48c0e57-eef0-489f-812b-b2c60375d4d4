  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _changiMaps = _$$_REQUIRE(_dependencyMap[6]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[7]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[9]);
  var _bottomSheetUnableLoadLocation = _$$_REQUIRE(_dependencyMap[10]);
  var _changiMapHeaderV = _$$_REQUIRE(_dependencyMap[11]);
  var _lodash = _$$_REQUIRE(_dependencyMap[12]);
  var _pageConfigSaga = _$$_REQUIRE(_dependencyMap[13]);
  var _changiMapCallout = _$$_REQUIRE(_dependencyMap[14]);
  var _constants = _$$_REQUIRE(_dependencyMap[15]);
  var _mapAction = _$$_REQUIRE(_dependencyMap[16]);
  var _adobe = _$$_REQUIRE(_dependencyMap[17]);
  var _levelSelector = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[18]));
  var _envParams = _$$_REQUIRE(_dependencyMap[19]);
  var _storage = _$$_REQUIRE(_dependencyMap[20]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[21]);
  var _i18n = _$$_REQUIRE(_dependencyMap[22]);
  var _buttonSearch = _$$_REQUIRE(_dependencyMap[23]);
  var _bottomSheetSearch = _$$_REQUIRE(_dependencyMap[24]);
  var _viewDetailLocation = _$$_REQUIRE(_dependencyMap[25]);
  var _bottomSheetMultiple = _$$_REQUIRE(_dependencyMap[26]);
  var _viewFilterLocation = _$$_REQUIRE(_dependencyMap[27]);
  var _viewDirection = _$$_REQUIRE(_dependencyMap[28]);
  var _viewEmptyDirection = _$$_REQUIRE(_dependencyMap[29]);
  var _reactNativeGestureHandler = _$$_REQUIRE(_dependencyMap[30]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[31]));
  var _theme = _$$_REQUIRE(_dependencyMap[32]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[33]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var DefaultZoom = /*#__PURE__*/function (DefaultZoom) {
    DefaultZoom[DefaultZoom["VALUE"] = 13.95] = "VALUE";
    return DefaultZoom;
  }(DefaultZoom || {});
  var heightScreen = _reactNative.Dimensions.get('window').height;
  var widthScreen = _reactNative.Dimensions.get('window').width;
  var mapHeight = 0;
  var skipOverview = 2;
  var _worklet_12392382145115_init_data = {
    code: "function indexTsx1(){const{interpolateColor,height,heightScreen,color,interpolate,widthScreen}=this.__closure;return{backgroundColor:interpolateColor(height.value,[0,244,heightScreen-64],['transparent','transparent',color.palette.overlayColor]),width:interpolate(height.value,[240,250,heightScreen-64],[0,widthScreen,widthScreen]),height:interpolate(height.value,[240,250,heightScreen-64],[0,heightScreen,heightScreen])};}"
  };
  var AtomMap = function AtomMap(_ref) {
    var _route$params, _route$params2, _env4, _env5;
    var navigation = _ref.navigation,
      route = _ref.route;
    var MetaAtlasSDK = _$$_REQUIRE(_dependencyMap[34]).MetaAtlasSDK;
    var atoms = (0, _react.useRef)(null);
    var _useState = (0, _react.useState)(true),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isLoading = _useState2[0],
      setLoading = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isNoConnection = _useState4[0],
      setNoConnection = _useState4[1];
    var unableToLoadLocationRef = (0, _react.useRef)(null);
    var _useState5 = (0, _react.useState)(),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      currentPoint = _useState6[0],
      setCurrentPoint = _useState6[1];
    var _useState7 = (0, _react.useState)(false),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      isMapMoved = _useState8[0],
      setMapMoved = _useState8[1];
    var _useState9 = (0, _react.useState)(0),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      mapLoadedCount = _useState0[0],
      setMapLoadedCount = _useState0[1];
    var _useState1 = (0, _react.useState)([]),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      floors = _useState10[0],
      setFloors = _useState10[1];
    var _useState11 = (0, _react.useState)(),
      _useState12 = (0, _slicedToArray2.default)(_useState11, 2),
      activeFloor = _useState12[0],
      setActiveFloor = _useState12[1];
    var _useState13 = (0, _react.useState)(""),
      _useState14 = (0, _slicedToArray2.default)(_useState13, 2),
      activeBuildingName = _useState14[0],
      setActiveBuildingName = _useState14[1];
    var _useState15 = (0, _react.useState)(null),
      _useState16 = (0, _slicedToArray2.default)(_useState15, 2),
      levelSelector = _useState16[0],
      setLevelSelector = _useState16[1];
    var _useState17 = (0, _react.useState)(false),
      _useState18 = (0, _slicedToArray2.default)(_useState17, 2),
      isMoveEnded = _useState18[0],
      setMoveEnded = _useState18[1];
    var _useState19 = (0, _react.useState)(false),
      _useState20 = (0, _slicedToArray2.default)(_useState19, 2),
      isShouldHide = _useState20[0],
      setHideLvlSelector = _useState20[1];
    var _useState21 = (0, _react.useState)(false),
      _useState22 = (0, _slicedToArray2.default)(_useState21, 2),
      showBottomSheetSearch = _useState22[0],
      setShowBottomSheet = _useState22[1];
    var _useState23 = (0, _react.useState)(false),
      _useState24 = (0, _slicedToArray2.default)(_useState23, 2),
      showBottomSheetSearchMutiple = _useState24[0],
      setShowBottomSheetMultiple = _useState24[1];
    var _useState25 = (0, _react.useState)(false),
      _useState26 = (0, _slicedToArray2.default)(_useState25, 2),
      showButtonSmall = _useState26[0],
      setShowButtonSmall = _useState26[1];
    var _useState27 = (0, _react.useState)(null),
      _useState28 = (0, _slicedToArray2.default)(_useState27, 2),
      dataLocationTo = _useState28[0],
      _setDataLocationTo = _useState28[1];
    var _useState29 = (0, _react.useState)(null),
      _useState30 = (0, _slicedToArray2.default)(_useState29, 2),
      dataLocationFrom = _useState30[0],
      _setDataLocationFrom = _useState30[1];
    var _useState31 = (0, _react.useState)({
        visible: false,
        data: null,
        tenantId: null
      }),
      _useState32 = (0, _slicedToArray2.default)(_useState31, 2),
      showViewDetailLocation = _useState32[0],
      setShowViewDetailLocation = _useState32[1];
    var _useState33 = (0, _react.useState)(false),
      _useState34 = (0, _slicedToArray2.default)(_useState33, 2),
      showViewFilterLocation = _useState34[0],
      setShowViewFilterLocation = _useState34[1];
    var _useState35 = (0, _react.useState)(false),
      _useState36 = (0, _slicedToArray2.default)(_useState35, 2),
      showViewDirection = _useState36[0],
      setShowViewDirection = _useState36[1];
    var _useState37 = (0, _react.useState)(false),
      _useState38 = (0, _slicedToArray2.default)(_useState37, 2),
      showViewEmptyDirection = _useState38[0],
      setShowViewEmptyDirection = _useState38[1];
    var _useState39 = (0, _react.useState)(false),
      _useState40 = (0, _slicedToArray2.default)(_useState39, 2),
      accessibleRoute = _useState40[0],
      setAccessibleRoute = _useState40[1];
    var _useState41 = (0, _react.useState)(null),
      _useState42 = (0, _slicedToArray2.default)(_useState41, 2),
      dataDirection = _useState42[0],
      setDataDirection = _useState42[1];
    var _useState43 = (0, _react.useState)("OVERVIEW"),
      _useState44 = (0, _slicedToArray2.default)(_useState43, 2),
      statusViewDirection = _useState44[0],
      setStatusViewDirection = _useState44[1];
    var _useState45 = (0, _react.useState)(undefined),
      _useState46 = (0, _slicedToArray2.default)(_useState45, 2),
      highlightedSegment = _useState46[0],
      setHighlightedSegment = _useState46[1];
    var _useState47 = (0, _react.useState)((0, _i18n.translate)("changimap.header-v2")),
      _useState48 = (0, _slicedToArray2.default)(_useState47, 2),
      titleHeader = _useState48[0],
      setTitleHeader = _useState48[1];
    var _useState49 = (0, _react.useState)(false),
      _useState50 = (0, _slicedToArray2.default)(_useState49, 2),
      firstShowViewDetailLocation = _useState50[0],
      setFirstShowViewDetailLocation = _useState50[1];
    var _useState51 = (0, _react.useState)(false),
      _useState52 = (0, _slicedToArray2.default)(_useState51, 2),
      readyViewDetailLocation = _useState52[0],
      setReadyViewDetailLocation = _useState52[1];
    var height = (0, _reactNativeReanimated.useSharedValue)(0);
    var calloutRef = (0, _react.useRef)(null);
    var activeFloorRef = (0, _react.useRef)(null);
    var currentPointRef = (0, _react.useRef)(null);
    var searchKeywordCollection = (0, _react.useRef)([]);
    var appState = (0, _react.useRef)(_reactNative.AppState.currentState);
    var _useState53 = (0, _react.useState)(false),
      _useState54 = (0, _slicedToArray2.default)(_useState53, 2),
      isRoutingStatusUpdateSuccesss = _useState54[0],
      saveRoutingStatusUpdate = _useState54[1];
    var _useState55 = (0, _react.useState)(_reactNative.Platform.OS === 'ios' ? true : false),
      _useState56 = (0, _slicedToArray2.default)(_useState55, 2),
      delayOnMove = _useState56[0],
      setDelayOnMove = _useState56[1];
    var localRef = route == null || (_route$params = route.params) == null ? undefined : _route$params.localRef;
    var isFocusToArea = route == null || (_route$params2 = route.params) == null ? undefined : _route$params2.isFocusToArea;
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)(_constants.TrackingScreenName.ChangiMap);
    (0, _react.useEffect)(function () {
      var subscription = _reactNative.AppState.addEventListener("change", function (nextAppState) {
        if (appState.current.match(/inactive/) && nextAppState === "background") {
          (0, _storage.save)(_storage.StorageKey.keywordAtomSearchMissingByAppState, searchKeywordCollection.current);
        }
        appState.current = nextAppState;
      });
      return function () {
        subscription.remove();
      };
    }, []);

    // call SDK function on highlightRoute segment state update
    (0, _react.useEffect)(function () {
      var _dataDirection$segmen;
      if (highlightedSegment !== undefined) {
        ;
        atoms.current.highlightRouteSegment(highlightedSegment);
      }
      if (highlightedSegment === (dataDirection == null || (_dataDirection$segmen = dataDirection.segments) == null ? undefined : _dataDirection$segmen.length) - 1 || highlightedSegment === 0) {
        ;
        atoms.current.dropPin(dataLocationTo, (0, _jsxRuntime.jsx)(_changiMapCallout.ChangiMapCallout, {
          content: dataLocationTo,
          tenantId: null,
          handleNavigate: function handleNavigate() {},
          ref: calloutRef
        }));
      } else {
        var _calloutRef$current;
        atoms.current.hidePin();
        calloutRef == null || (_calloutRef$current = calloutRef.current) == null || _calloutRef$current.hideCallout();
      }
      changeTitleHeader(highlightedSegment);
    }, [highlightedSegment]);
    (0, _react.useEffect)(function () {
      var getMissingKeywordSearch = /*#__PURE__*/function () {
        var _ref2 = (0, _asyncToGenerator2.default)(function* () {
          var keywordCollection = yield (0, _storage.load)(_storage.StorageKey.keywordAtomSearchMissingByAppState);
          if (!(0, _lodash.isEmpty)(keywordCollection)) {
            (0, _mapAction.uniqfyKeywordCollection)(keywordCollection);
            (0, _storage.remove)(_storage.StorageKey.keywordAtomSearchMissingByAppState);
          }
        });
        return function getMissingKeywordSearch() {
          return _ref2.apply(this, arguments);
        };
      }();
      getMissingKeywordSearch();
      return function () {
        (0, _storage.remove)(_storage.StorageKey.keywordAtomSearchMissingByAppState);
      };
    }, []);
    (0, _react.useEffect)(function () {
      navigation == null || navigation.setOptions({
        headerShown: false
      });
    }, []);
    (0, _react.useEffect)(function () {
      var checkInternet = /*#__PURE__*/function () {
        var _ref3 = (0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch.isConnected;
          if (!isConnected) {
            setNoConnection(true);
          }
        });
        return function checkInternet() {
          return _ref3.apply(this, arguments);
        };
      }();
      checkInternet();
    }, [navigation]);
    var defaultFloors = {
      T1: "where.changi.terminals.t1.l1",
      T2: "where.changi.terminals.t2.l1",
      T3: "where.changi.terminals.t3.l1",
      T4: "where.changi.terminals.t4.l1",
      Jewel: "where.changi.terminals.jewel.l1"
    };
    var getTerminal = function getTerminal(localRefStr) {
      var _localRefArr$at;
      if (!localRefStr) return undefined;
      var localRefArr = localRefStr.split(".");
      return (_localRefArr$at = localRefArr.at(-2)) == null ? undefined : _localRefArr$at.toUpperCase();
    };
    var terminal = getTerminal(localRef);
    // If navigate from details flight, change chosen terminal's default floor to l2
    if (!!terminal && !!(defaultFloors != null && defaultFloors[terminal]) && !!isFocusToArea) {
      defaultFloors[terminal] = localRef.split(".").slice(0, -1).join(".") + ".l2";
    }
    var changeFocus = /*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* (level) {
        if (level.taxonomyPath !== (currentPoint == null ? undefined : currentPoint.whereDimension)) {
          var _calloutRef$current2;
          calloutRef == null || (_calloutRef$current2 = calloutRef.current) == null || _calloutRef$current2.hideCallout();
        } else {
          var _calloutRef$current3;
          calloutRef == null || (_calloutRef$current3 = calloutRef.current) == null || _calloutRef$current3.showCallout();
        }
        changeFocusToTax(level.taxonomyPath);
        setActiveFloor(level);
        activeFloorRef.current = level;
      });
      return function changeFocus(_x) {
        return _ref4.apply(this, arguments);
      };
    }();
    var changeFocusToTax = function changeFocusToTax(taxonomyPath) {
      ;
      atoms.current.focusTo(taxonomyPath, false);
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppATOMSLevelSelector, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppATOMSLevelSelector, `${(0, _mapAction.getAATagLevelSelector)(taxonomyPath)}`));
    };
    var onReloadData = function onReloadData() {
      var onReload = /*#__PURE__*/function () {
        var _ref5 = (0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch2.isConnected;
          if (isConnected) {
            setNoConnection(false);
          }
        });
        return function onReload() {
          return _ref5.apply(this, arguments);
        };
      }();
      onReload();
    };
    var onSearchStatusUpdate = function onSearchStatusUpdate(value) {
      if (value) {
        setLoading(false);
        if (localRef === undefined) {
          _reactNative.Platform.OS === 'ios' && setTimeout(function () {
            setDelayOnMove(false);
          }, 3000);
        } else {
          if (isFocusToArea && localRef) {
            atoms.current.focusTo(localRef, true);
          } else {
            findByLocalRef(localRef);
          }
          _reactNative.Platform.OS === 'ios' && setTimeout(function () {
            setDelayOnMove(false);
          }, 300);
        }
      }
    };
    var onRoutingStatusUpdate = function onRoutingStatusUpdate(value) {
      if (value) {
        saveRoutingStatusUpdate(true);
      }
    };
    var handleNavigate = function handleNavigate(content, id) {
      if ((content == null ? undefined : content.whatDimension) === "what.shop.retail" || (content == null ? undefined : content.whatDimension) === "what.shop.retail_anchor") {
        navigation.navigate(_constants.NavigationConstants.shopDetailsScreen, {
          tenantId: id,
          name: content.name
        });
      } else {
        navigation.navigate(_constants.NavigationConstants.restaurantDetailScreen, {
          tenantId: id,
          name: content.name
        });
      }
    };
    var onPressMarker = function onPressMarker(currentPointParams, tenantId) {
      setShowViewDetailLocation({
        data: currentPointParams,
        visible: true,
        tenantId: tenantId
      });
    };
    var flyNDrop = /*#__PURE__*/function () {
      var _ref6 = (0, _asyncToGenerator2.default)(function* (currentPointParams, iswithDefaultZoom) {
        var _currentPointParams$p, _calloutRef$current4;
        var input = {
          id: "",
          localRef: currentPointParams == null || (_currentPointParams$p = currentPointParams.properties) == null ? undefined : _currentPointParams$p.localRef
        };
        var tenantPageID = yield (0, _pageConfigSaga.getTenantDetail)({
          input: input
        });
        var center = atoms.current.getMidPointOfFeature(currentPointParams);
        calloutRef == null || (_calloutRef$current4 = calloutRef.current) == null || _calloutRef$current4.showCallout();
        if (iswithDefaultZoom) {
          ;
          atoms.current.flyTo({
            center: center,
            zoomLevel: 17.25
          });
          atoms.current.dropPin(currentPointParams, (0, _jsxRuntime.jsx)(_changiMapCallout.ChangiMapCallout, {
            content: currentPointParams,
            tenantId: tenantPageID,
            handleNavigate: function handleNavigate() {
              return onPressMarker(currentPointParams, tenantPageID);
            },
            ref: calloutRef
          }));
        } else {
          var zoom = yield atoms.current.getZoom();
          atoms.current.flyTo({
            center: center,
            zoomLevel: zoom
          });
          atoms.current.dropPin(currentPointParams, (0, _jsxRuntime.jsx)(_changiMapCallout.ChangiMapCallout, {
            content: currentPointParams,
            tenantId: tenantPageID,
            handleNavigate: function handleNavigate() {
              return onPressMarker(currentPointParams, tenantPageID);
            },
            ref: calloutRef
          }));
        }
        setShowViewDetailLocation({
          visible: true,
          data: currentPointParams,
          tenantId: tenantPageID
        });
      });
      return function flyNDrop(_x2, _x3) {
        return _ref6.apply(this, arguments);
      };
    }();
    var mapPressed = /*#__PURE__*/function () {
      var _ref7 = (0, _asyncToGenerator2.default)(function* (_data) {
        if (!showViewDirection) {
          setFirstShowViewDetailLocation(false);
          setLoading(true);
          var lastClicked = atoms.current.getLastClickedFeature();
          if (lastClicked.name !== undefined) {
            var activeFloorTemp = {
              name: lastClicked.whereDimension.slice(lastClicked.whereDimension.lastIndexOf(".") + 1, lastClicked.whereDimension.length).toUpperCase(),
              taxonomyPath: lastClicked.whereDimension
            };
            changeFocusToTax(activeFloorTemp.taxonomyPath);
            yield flyNDrop(lastClicked, true);
            (0, _reactNative.unstable_batchedUpdates)(function () {
              setActiveFloor(activeFloorTemp);
              activeFloorRef.current = activeFloorTemp;
              setCurrentPoint(lastClicked);
              currentPointRef.current = lastClicked;
            });
            (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppATOMSMapClicks, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppATOMSMapClicks, `${lastClicked == null ? undefined : lastClicked.name}`));
            (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppATOMsMap, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppATOMsMap, `${_adobe.AdobeValueByTagName.CAppATOMSMapMapClicks}${lastClicked == null ? undefined : lastClicked.name}`));
          }
          (0, _reactNative.unstable_batchedUpdates)(function () {
            setLoading(false);
          });
        }
      });
      return function mapPressed(_x4) {
        return _ref7.apply(this, arguments);
      };
    }();
    var resetState = function resetState(atvFloor, targPoint) {
      setActiveFloor(atvFloor);
      activeFloorRef.current = atvFloor;
      setCurrentPoint(targPoint);
      currentPointRef.current = targPoint;
      setLoading(false);
    };
    var findByLocalRef = function findByLocalRef(localRef) {
      if (!localRef) {
        unableToLoadLocationRef.current.show();
        setLoading(false);
      } else {
        try {
          atoms.current.getMapObjectByLocalRef(localRef, /*#__PURE__*/function () {
            var _ref8 = (0, _asyncToGenerator2.default)(function* (data) {
              var _target$properties, _target$geometry;
              var target = data == null ? undefined : data[0];
              if (!target || !(target != null && (_target$properties = target.properties) != null && _target$properties.localRef)) {
                unableToLoadLocationRef.current.show();
                setLoading(false);
                return;
              }
              var targetPoint = {
                mapObjectId: target == null ? undefined : target.mapObjectId,
                coordinates: (target == null ? undefined : target.coordinates) || (target == null || (_target$geometry = target.geometry) == null ? undefined : _target$geometry.coordinates),
                name: target == null ? undefined : target.name,
                whatDimension: target == null ? undefined : target.whatDimension,
                whereDimension: target == null ? undefined : target.whereDimension,
                geometry: target == null ? undefined : target.geometry,
                properties: target == null ? undefined : target.properties
              };
              var activeFloorTemp = {
                name: targetPoint.whereDimension.slice(targetPoint.whereDimension.lastIndexOf(".") + 1, targetPoint.whereDimension.length).toUpperCase(),
                taxonomyPath: targetPoint.whereDimension
              };
              changeFocusToTax(activeFloorTemp.taxonomyPath);
              yield flyNDrop(targetPoint, true);
              (0, _reactNative.unstable_batchedUpdates)(function () {
                resetState(activeFloorTemp, targetPoint);
              });
            });
            return function (_x5) {
              return _ref8.apply(this, arguments);
            };
          }());
        } catch (error) {
          unableToLoadLocationRef.current.show();
          setLoading(false);
        }
      }
    };
    var onMoveEnd = /*#__PURE__*/function () {
      var _ref9 = (0, _asyncToGenerator2.default)(function* () {
        if (!showViewDirection) {
          var changeFocusPromise = atoms.current.changeTerminalInFocusToUserViewpoint(defaultFloors);
          yield changeFocusPromise;
          var building = atoms.current.getCurrentFocusBuilding();
          var floor = atoms.current.getCurrentFocusFloor();
          if (!floor) {
            return;
          }
          (0, _reactNative.unstable_batchedUpdates)(function () {
            if (!(0, _lodash.isEmpty)(floor)) {
              setActiveFloor(floor);
              activeFloorRef.current = floor;
            }
            setActiveBuildingName(building.name);
            setFloors(building.floors);
            setMapLoadedCount(mapLoadedCount + 1);
            setMoveEnded(true);
          });
          if (showViewDetailLocation != null && showViewDetailLocation.data) {
            setFirstShowViewDetailLocation(true);
          }
        }
      });
      return function onMoveEnd() {
        return _ref9.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      var fetchMapProperties = /*#__PURE__*/function () {
        var _ref0 = (0, _asyncToGenerator2.default)(function* () {
          var zoom = yield atoms.current.getZoom();
          if (zoom <= DefaultZoom.VALUE) {
            setHideLvlSelector(true);
          } else {
            setHideLvlSelector(false);
          }
        });
        return function fetchMapProperties() {
          return _ref0.apply(this, arguments);
        };
      }();
      fetchMapProperties();
      isMoveEnded && setLevelSelector(function () {
        return (0, _jsxRuntime.jsx)(_levelSelector.default, {
          building: activeBuildingName,
          levelData: floors,
          onSelectLevel: changeFocus,
          selectedFloor: activeFloor,
          isMoved: mapLoadedCount > 1 && isMapMoved,
          isShouldHide: isShouldHide,
          currentPoint: currentPoint
        });
      });
    }, [activeFloor, floors, isMapMoved, mapLoadedCount, isMoveEnded, isShouldHide]);
    var navigateSearchResult = /*#__PURE__*/function () {
      var _ref1 = (0, _asyncToGenerator2.default)(function* (targetPoint) {
        setFirstShowViewDetailLocation(false);
        atoms.current.hidePin();
        setLoading(true);
        _reactNative.Keyboard.dismiss();
        var activeFloorTemp = {
          name: targetPoint.whereDimension.slice(targetPoint.whereDimension.lastIndexOf(".") + 1, targetPoint.whereDimension.length).toUpperCase(),
          taxonomyPath: targetPoint.whereDimension
        };
        changeFocusToTax(activeFloorTemp.taxonomyPath);
        yield flyNDrop(targetPoint, true);
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppATOMSSearchResults, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppATOMSSearchResults, `|${targetPoint.name}`));
        (0, _reactNative.unstable_batchedUpdates)(function () {
          resetState(activeFloorTemp, targetPoint);
        });
      });
      return function navigateSearchResult(_x6) {
        return _ref1.apply(this, arguments);
      };
    }();
    var closeOrOpenBottomSheet = (0, _react.useCallback)(function () {
      setShowBottomSheet(!showBottomSheetSearch);
    }, [showBottomSheetSearch]);
    var closeOrOpenBottomSheetMultiple = (0, _react.useCallback)(function () {
      setShowBottomSheetMultiple(!showBottomSheetSearchMutiple);
    }, [showBottomSheetSearchMutiple]);
    var onDirection = function onDirection(from, to) {
      var _calloutRef$current5;
      atoms.current.hidePin();
      calloutRef == null || (_calloutRef$current5 = calloutRef.current) == null || _calloutRef$current5.hideCallout();
      setShowViewDetailLocation({
        visible: false,
        data: null,
        tenantId: null
      });
      setFirstShowViewDetailLocation(false);
      _setDataLocationFrom(from);
      _setDataLocationTo(to);
      if (from && to) {
        closeOrOpenBottomSheetMultiple();
        getDirection(from, to, accessibleRoute);
      }
    };
    var reverseLocationBottomSheet = function reverseLocationBottomSheet(from, to) {
      if (from && to) {
        closeOrOpenBottomSheetMultiple();
        getDirection(from, to, accessibleRoute);
      }
    };
    var reverseLocation = (0, _react.useCallback)(function () {
      _setDataLocationFrom(dataLocationTo);
      _setDataLocationTo(dataLocationFrom);
      if (dataLocationTo && dataLocationFrom) {
        getDirection(dataLocationTo, dataLocationFrom, accessibleRoute);
      }
    }, [dataLocationFrom, dataLocationTo, accessibleRoute]);
    var getDirection = function getDirection(from, to) {
      var accessible = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
      setLoading(true);
      setTimeout(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
        var _directionData$segmen;
        setDataDirection(null);
        setHighlightedSegment(undefined);
        atoms.current.clearRoute();
        var mapObjectIdFrom = from == null ? undefined : from.mapObjectId;
        var mapObjectIdTo = to == null ? undefined : to.mapObjectId;
        var mapObjectIds = [mapObjectIdFrom, mapObjectIdTo];
        var directionData = yield atoms.current.computeRoutes(mapObjectIds, accessible);
        if ((directionData == null || (_directionData$segmen = directionData.segments) == null ? undefined : _directionData$segmen.length) === 0) {
          var _dataLocationTo$prope;
          setShowViewFilterLocation(true);
          setShowViewDirection(false);
          setShowViewEmptyDirection(true);
          setTitleHeader(dataLocationTo == null || (_dataLocationTo$prope = dataLocationTo.properties) == null ? undefined : _dataLocationTo$prope.title);
        } else {
          setShowViewFilterLocation(true);
          setShowViewDirection(true);
          setShowViewEmptyDirection(false);
          setDataDirection(directionData);
          atoms.current.setActiveRoute(directionData);
          setHighlightedSegment(0);
        }
        setLoading(false);
      }), 100);
    };
    var animatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var indexTsx1 = function indexTsx1() {
        return {
          backgroundColor: (0, _reactNativeReanimated.interpolateColor)(height.value, [0, 244, heightScreen - 64], ['transparent', 'transparent', _theme.color.palette.overlayColor]),
          width: (0, _reactNativeReanimated.interpolate)(height.value, [240, 250, heightScreen - 64], [0, widthScreen, widthScreen]),
          height: (0, _reactNativeReanimated.interpolate)(height.value, [240, 250, heightScreen - 64], [0, heightScreen, heightScreen])
        };
      };
      indexTsx1.__closure = {
        interpolateColor: _reactNativeReanimated.interpolateColor,
        height: height,
        heightScreen: heightScreen,
        color: _theme.color,
        interpolate: _reactNativeReanimated.interpolate,
        widthScreen: widthScreen
      };
      indexTsx1.__workletHash = 12392382145115;
      indexTsx1.__initData = _worklet_12392382145115_init_data;
      return indexTsx1;
    }());
    var onClearRoute = function onClearRoute() {
      ;
      atoms.current.clearRoute();
      setShowViewDirection(false);
      setShowViewFilterLocation(false);
      setShowViewEmptyDirection(false);
      setAccessibleRoute(false);
      setDataDirection(null);
      _setDataLocationFrom(null);
      setStatusViewDirection("OVERVIEW");
      setFirstShowViewDetailLocation(false);
    };
    var onCloseViewDirection = function onCloseViewDirection() {
      setTitleHeader((0, _i18n.translate)("changimap.header-v2"));
      onClearRoute();
      flyNDrop(dataLocationTo, true);
    };
    var onBack = function onBack() {
      if (showViewFilterLocation) {
        setTitleHeader((0, _i18n.translate)("changimap.header-v2"));
        onClearRoute();
        flyNDrop(dataLocationTo, true);
      } else if (!showViewFilterLocation && statusViewDirection === "STEP") {
        onExitDirection();
      } else {
        navigation.goBack();
      }
    };
    var changeTitleHeader = function changeTitleHeader(step) {
      if (dataDirection) {
        var _dataDirection$segmen3, _dataDirection$segmen4;
        if (step === 0) {
          var _dataDirection$segmen2, _dataLocationTo$prope2;
          var distance = Math.ceil(Number(dataDirection == null || (_dataDirection$segmen2 = dataDirection.segments[0]) == null ? undefined : _dataDirection$segmen2.distance));
          var title = `${distance}m ${(0, _i18n.translate)("changimap.to")} ${dataLocationTo == null || (_dataLocationTo$prope2 = dataLocationTo.properties) == null ? undefined : _dataLocationTo$prope2.title}`;
          setTitleHeader(title);
        } else if (step === (dataDirection == null || (_dataDirection$segmen3 = dataDirection.segments) == null ? undefined : _dataDirection$segmen3.length) - 1) {
          var _segment$endEdge;
          var segment = dataDirection == null ? undefined : dataDirection.segments[step];
          var _title = `${segment == null || (_segment$endEdge = segment.endEdge) == null ? undefined : _segment$endEdge.name}`;
          setTitleHeader(_title);
        } else if (step !== 0 || step !== (dataDirection == null || (_dataDirection$segmen4 = dataDirection.segments) == null ? undefined : _dataDirection$segmen4.length) - 1) {
          var _segment = dataDirection == null ? undefined : dataDirection.segments[step];
          var _title2 = (0, _mapAction.renderTitleHeaderStepMid)(_segment);
          setTitleHeader(_title2);
        }
      } else {
        if (showViewFilterLocation) {
          var _dataLocationTo$prope3;
          setTitleHeader(dataLocationTo == null || (_dataLocationTo$prope3 = dataLocationTo.properties) == null ? undefined : _dataLocationTo$prope3.title);
        } else {
          setTitleHeader((0, _i18n.translate)("changimap.header-v2"));
        }
      }
    };
    var onStartDirection = function onStartDirection(value) {
      var _dataLocationFrom$pro, _dataLocationTo$prope4;
      var routeType = accessibleRoute ? (0, _i18n.translate)("changimap.accessible-Route") : (0, _i18n.translate)("changimap.walking-Route");
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppATOMsMap, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppATOMsMap, `${dataLocationFrom == null || (_dataLocationFrom$pro = dataLocationFrom.properties) == null ? undefined : _dataLocationFrom$pro.title} | ${dataLocationTo == null || (_dataLocationTo$prope4 = dataLocationTo.properties) == null ? undefined : _dataLocationTo$prope4.title} | ${routeType} | Start`));
      setShowViewFilterLocation(false);
      setHighlightedSegment(value);
      setStatusViewDirection("STEP");
    };
    var onExitDirection = function onExitDirection() {
      setShowViewFilterLocation(true);
      setHighlightedSegment(0);
      setStatusViewDirection("OVERVIEW");
    };
    var onNextStep = function onNextStep(value) {
      setHighlightedSegment(value);
    };
    var onPreviousStep = function onPreviousStep(value) {
      setHighlightedSegment(value);
    };
    var onMoveOrZoom = function onMoveOrZoom() {
      if (!showButtonSmall && !delayOnMove) {
        setShowButtonSmall(true);
      }
      if (firstShowViewDetailLocation && showViewDetailLocation != null && showViewDetailLocation.visible && readyViewDetailLocation) {
        setShowViewDetailLocation(Object.assign({}, showViewDetailLocation, {
          visible: false
        }));
      }
    };
    var initSDK = (0, _react.useMemo)(function () {
      var _env, _env2, _env3;
      return (0, _jsxRuntime.jsx)(MetaAtlasSDK, {
        ref: atoms,
        tileserverRoleName: (_env = (0, _envParams.env)()) == null ? undefined : _env.CHANGI_MAP_ROLE_NAME,
        accessToken: (_env2 = (0, _envParams.env)()) == null ? undefined : _env2.CHANGI_MAP_ACCESS_TOKEN,
        onPress: mapPressed,
        onMove: function onMove() {
          setMapMoved(true);
          setMoveEnded(false);
          onMoveOrZoom();
        },
        onMoveEnd: onMoveEnd,
        maxMapHeight: mapHeight,
        isFullHeight: true,
        secretKey: (_env3 = (0, _envParams.env)()) == null ? undefined : _env3.CHANGI_MAP_SECRET_KEY,
        onRoutingStatusUpdate: onRoutingStatusUpdate,
        onSearchStatusUpdate: onSearchStatusUpdate
      });
    }, [mapPressed, onMoveEnd, onMoveOrZoom]);
    if (!((_env4 = (0, _envParams.env)()) != null && _env4.CHANGI_MAP_ROLE_NAME) || !((_env5 = (0, _envParams.env)()) != null && _env5.CHANGI_MAP_ACCESS_TOKEN)) return null;
    return (0, _jsxRuntime.jsx)(_reactNativeGestureHandler.GestureHandlerRootView, {
      children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _changiMaps.styles.container,
        children: [(0, _jsxRuntime.jsx)(_reactNative.StatusBar, {
          translucent: true,
          barStyle: "dark-content"
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: _changiMaps.styles.container,
          children: [(0, _jsxRuntime.jsx)(_changiMapHeaderV.ChangiMapHeaderV2, {
            title: titleHeader,
            onBack: onBack
          }), initSDK, !showViewDirection && !showViewFilterLocation && !(showViewDetailLocation != null && showViewDetailLocation.visible) && showButtonSmall && floors && levelSelector, showViewEmptyDirection && (0, _jsxRuntime.jsx)(_viewEmptyDirection.ViewEmptyDirection, {
            onPress: closeOrOpenBottomSheetMultiple
          }), showViewFilterLocation && (0, _jsxRuntime.jsx)(_viewFilterLocation.ViewFilterLocation, {
            dataLocationTo: dataLocationTo,
            dataLocationFrom: dataLocationFrom,
            reverseLocation: reverseLocation,
            onClick: closeOrOpenBottomSheetMultiple
          }), !showViewFilterLocation && (0, _jsxRuntime.jsx)(_buttonSearch.ButtonSearch, {
            isSmall: showButtonSmall,
            onPress: closeOrOpenBottomSheet,
            containerStyle: {
              bottom: showViewDetailLocation != null && showViewDetailLocation.visible ? 180 : 20
            }
          }), (showViewDetailLocation == null ? undefined : showViewDetailLocation.visible) && (0, _jsxRuntime.jsx)(_viewDetailLocation.ViewDetailLocation, {
            data: showViewDetailLocation == null ? undefined : showViewDetailLocation.data,
            onReady: function onReady(value) {
              return setReadyViewDetailLocation(value);
            },
            closeView: function closeView() {
              // (atoms.current as any).hidePin();
              // setDataLocationTo(null)
              // calloutRef?.current?.hideCallout()
              setShowViewDetailLocation(Object.assign({}, showViewDetailLocation, {
                visible: false
              }));
              // setFirstShowViewDetailLocation(false)
            },
            onPressRouting: function onPressRouting(data) {
              _setDataLocationTo(data);
              closeOrOpenBottomSheetMultiple();
            },
            tenantId: showViewDetailLocation == null ? undefined : showViewDetailLocation.tenantId,
            handleNavigate: handleNavigate
          }), showViewDirection && (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
            style: [_changiMaps.styles.viewBlack, animatedStyle]
          }), showViewDirection && dataDirection && (0, _jsxRuntime.jsx)(_viewDirection.ViewDirection, {
            onShowHideViewBackground: function onShowHideViewBackground(value) {
              return height.value = value;
            },
            onCloseViewDirection: onCloseViewDirection,
            data: dataDirection,
            dataLocationTo: dataLocationTo,
            dataLocationFrom: dataLocationFrom,
            searchDirection: function searchDirection(value) {
              return getDirection(dataLocationFrom, dataLocationTo, value);
            },
            accessibleRoute: accessibleRoute,
            setAccessibleRoute: setAccessibleRoute,
            status: statusViewDirection,
            skipOverview: skipOverview,
            step: highlightedSegment,
            onStartDirection: onStartDirection,
            onExitDirection: onExitDirection,
            onNextStep: onNextStep,
            onPreviousStep: onPreviousStep
          })]
        }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
          visible: isLoading,
          customStyle: _changiMaps.styles.loadingRoot
        }), (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
          reload: true,
          header: false,
          hideScreenHeader: true,
          headerBackgroundColor: "transparent",
          visible: isNoConnection,
          onReload: onReloadData,
          onBack: function onBack() {
            navigation.goBack();
          },
          noInternetOverlayStyle: _changiMaps.styles.overlayStyle
        }), (0, _jsxRuntime.jsx)(_bottomSheetUnableLoadLocation.BottomSheetUnableLoadLocation, {
          ref: unableToLoadLocationRef
        }), showBottomSheetSearch && (0, _jsxRuntime.jsx)(_bottomSheetSearch.BottomSheetSearch, {
          visible: showBottomSheetSearch,
          onClosedSheet: closeOrOpenBottomSheet,
          mapRef: atoms,
          onSelectedSearchResult: function onSelectedSearchResult(item) {
            if (!showButtonSmall) {
              setShowButtonSmall(true);
            }
            navigateSearchResult(item);
          },
          searchKeywordCollection: searchKeywordCollection
        }), showBottomSheetSearchMutiple && (0, _jsxRuntime.jsx)(_bottomSheetMultiple.BottomSheetSearchMultiple, {
          dataLocationTo: dataLocationTo,
          dataLocationFrom: dataLocationFrom,
          visible: showBottomSheetSearchMutiple,
          onClosedSheet: closeOrOpenBottomSheetMultiple,
          mapRef: atoms,
          setDataLocationTo: function setDataLocationTo(dataTo) {
            _setDataLocationTo(dataTo);
          },
          setDataLocationFrom: function setDataLocationFrom(dataFrom) {
            _setDataLocationFrom(dataFrom);
          },
          onDirection: onDirection,
          reverseLocation: reverseLocationBottomSheet
        })]
      })
    });
  };
  var _default = exports.default = AtomMap;
