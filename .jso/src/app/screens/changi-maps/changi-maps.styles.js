  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  /* eslint-disable react-native/no-color-literals */

  var styles = exports.styles = _reactNative.StyleSheet.create({
    container: {
      flex: 1
    },
    desContainer: {
      alignItems: "center",
      flexDirection: "row"
    },
    dividerStyle: {
      backgroundColor: _theme.color.palette.lighterGrey,
      height: 1
    },
    emptyContainer: {
      padding: 24,
      width: "100%"
    },
    emptyText: Object.assign({}, _text.presets.caption1Regular, {
      color: _theme.color.palette.almostBlackGrey,
      textAlign: "center",
      width: "100%"
    }),
    flatListItemsStyle: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "flex-start",
      paddingVertical: 16
    },
    locationTextStyle: Object.assign({}, _text.presets.caption2Regular, {
      color: _theme.color.palette.darkestGrey,
      marginLeft: 8
    }),
    mainContainer: {
      backgroundColor: _theme.color.palette.lightestGrey,
      height: "100%",
      marginTop: 118,
      width: "100%",
      zIndex: 8000
    },
    overlayStyle: {
      height: "100%",
      width: "100%"
    },
    paddingContentList: {
      paddingBottom: 130,
      paddingHorizontal: 24
    },
    searchOverlayStyles: {
      flex: 1,
      height: "100%"
    },
    textContainer: {
      alignSelf: "center",
      flexBasis: "72%",
      flexDirection: "column",
      justifyContent: "center",
      marginLeft: 16,
      marginVertical: 16
    },
    titleStyle: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey,
      marginBottom: 4
    }),
    viewBlack: {
      position: "absolute",
      zIndex: 999
    },
    loadingRoot: {
      zIndex: 99999
    }
  });
