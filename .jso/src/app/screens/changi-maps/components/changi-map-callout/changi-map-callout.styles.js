  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  /* eslint-disable react-native/no-color-literals */

  var styles = exports.styles = _reactNative.StyleSheet.create({
    calloutBackground: Object.assign({
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 44,
      height: 47,
      justifyContent: "center",
      paddingHorizontal: 8,
      shadowColor: "#121212",
      width: 177
    }, _reactNative.Platform.select({
      ios: {
        shadowRadius: 2,
        shadowOpacity: 0.08,
        shadowOffset: {
          width: 0,
          height: 3
        }
      },
      android: {
        elevation: 3
      }
    })),
    calloutContainerHideStyle: {
      alignItems: "center",
      backgroundColor: _theme.color.transparent,
      display: "none",
      paddingBottom: 100,
      paddingHorizontal: 5
    },
    calloutContainerStyle: {
      alignItems: "center",
      backgroundColor: _theme.color.transparent,
      paddingBottom: 100,
      paddingHorizontal: 5
    },
    calloutContent: {
      alignItems: "center",
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "space-between",
      width: 161
    },
    container: {
      flex: 1
    },
    customCalloutText: Object.assign({}, _text.presets.caption1Bold, {
      color: _theme.color.palette.black,
      marginRight: 8,
      textAlign: "center",
      textAlignVertical: "center",
      width: 121
    }),
    customCalloutTextNoIcon: Object.assign({}, _text.presets.caption1Bold, {
      color: _theme.color.palette.black,
      marginRight: 8,
      textAlign: "center",
      textAlignVertical: "center",
      width: "100%"
    }),
    overlayStyle: {
      height: "100%",
      width: "100%"
    },
    rectangle: {
      backgroundColor: _theme.color.palette.whiteGrey,
      bottom: 10,
      // height: 8,
      left: -10,
      position: 'absolute',
      width: 30
    },
    triagle: Object.assign({
      backgroundColor: _theme.color.palette.whiteGrey,
      height: 10,
      marginBottom: 4,
      marginTop: -5,
      shadowColor: "#121212",
      transform: [{
        rotate: "45deg"
      }],
      width: 10
    }, _reactNative.Platform.select({
      ios: {
        shadowRadius: 2,
        shadowOpacity: 0.01,
        shadowOffset: {
          width: 0,
          height: 3
        }
      },
      android: {
        elevation: 0
      }
    }))
  });
