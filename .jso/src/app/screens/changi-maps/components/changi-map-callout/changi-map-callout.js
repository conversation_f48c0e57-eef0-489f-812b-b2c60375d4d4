  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ChangiMapCallout = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _icons = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _changiMapCallout = _$$_REQUIRE(_dependencyMap[6]);
  var _utils = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var ChangiMapCalloutComponent = function ChangiMapCalloutComponent(_ref, ref) {
    var content = _ref.content,
      tenantId = _ref.tenantId,
      handleNavigate = _ref.handleNavigate;
    var _useState = (0, _react.useState)(true),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      visible = _useState2[0],
      setVisible = _useState2[1];
    var hideCallout = function hideCallout() {
      setVisible(false);
    };
    var showCallout = function showCallout() {
      setVisible(true);
    };
    (0, _react.useImperativeHandle)(ref, function () {
      return {
        hideCallout: hideCallout,
        showCallout: showCallout
      };
    });
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: (0, _utils.handleCondition)(visible, _changiMapCallout.styles.calloutContainerStyle, _changiMapCallout.styles.calloutContainerHideStyle),
      pointerEvents: "box-none",
      children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: {
          marginTop: 47
        },
        children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: handleNavigate,
          children: (0, _jsxRuntime.jsx)(_icons.MapMarkLocation, {
            translateY: 5
          })
        })
      })
    });
  };
  var ChangiMapCallout = exports.ChangiMapCallout = (0, _react.forwardRef)(ChangiMapCalloutComponent);
