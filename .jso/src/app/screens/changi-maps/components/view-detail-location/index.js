  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ViewDetailLocation = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _styles = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _theme = _$$_REQUIRE(_dependencyMap[8]);
  var _mapAction = _$$_REQUIRE(_dependencyMap[9]);
  var _account = _$$_REQUIRE(_dependencyMap[10]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[11]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var ViewDetailLocation = exports.ViewDetailLocation = _react.default.memo(function (props) {
    var _useContext = (0, _react.useContext)(_account.AccountContext),
      enableRoutingMap = _useContext.enableRoutingMap;
    var data = props.data,
      closeView = props.closeView,
      handleNavigate = props.handleNavigate,
      tenantId = props.tenantId,
      onPressRouting = props.onPressRouting,
      onReady = props.onReady;
    var isShowButtonRouting = (0, _remoteConfig.isFlagOnCondition)(enableRoutingMap);
    (0, _react.useEffect)(function () {
      checkReadyComponent();
      return function () {
        onReady(false);
      };
    }, []);
    var checkReadyComponent = function checkReadyComponent() {
      setTimeout(function () {
        onReady(true);
      }, 3000);
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _styles.styles.container,
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        style: _styles.styles.txtTitle,
        children: data == null ? undefined : data.name
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _styles.styles.viewRow,
        children: [data && (0, _mapAction.getTerminalIcon)(data), (0, _jsxRuntime.jsx)(_text.Text, {
          style: _styles.styles.txtSubtitle,
          children: data && `${(0, _mapAction.getLevelName)(data)}${(0, _mapAction.getUnitNumber)(data)}`
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _styles.styles.viewRowButton,
        children: [(tenantId == null ? undefined : tenantId.id) !== null && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: [_styles.styles.buttonMoreInfo, {
            flex: isShowButtonRouting ? 0 : 1
          }],
          onPress: function onPress() {
            handleNavigate(data, tenantId == null ? undefined : tenantId.id);
          },
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: _styles.styles.txtMoreInfo,
            tx: "changimap.more-info"
          })
        }), isShowButtonRouting && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: {
            flex: 1
          },
          onPress: function onPress() {
            return onPressRouting(data);
          },
          children: (0, _jsxRuntime.jsxs)(_reactNativeLinearGradient.default, {
            style: _styles.styles.buttonGetDirection,
            start: {
              x: 0,
              y: 1
            },
            end: {
              x: 1,
              y: 0
            },
            colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: _styles.styles.txtButtonDirection,
              tx: "changimap.get-direction"
            }), (0, _jsxRuntime.jsx)(_icons.DirectionIcon, {})]
          })
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        style: _styles.styles.buttonClose,
        onPress: closeView,
        hitSlop: {
          top: 10,
          bottom: 10,
          left: 10,
          right: 10
        },
        children: (0, _jsxRuntime.jsx)(_icons.CrossBlack, {})
      })]
    });
  });
