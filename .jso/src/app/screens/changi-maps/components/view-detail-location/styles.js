  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    container: {
      width: '100%',
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      position: 'absolute',
      bottom: 0,
      backgroundColor: _theme.color.palette.whiteGrey,
      paddingTop: 24,
      paddingHorizontal: 20,
      paddingBottom: 57
    },
    txtTitle: {
      color: _theme.color.palette.almostBlackGrey,
      fontFamily: _theme.typography.black,
      fontSize: 20,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: 'normal'
      }),
      marginHorizontal: 2,
      lineHeight: 28
    },
    viewRow: {
      width: '100%',
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: 8
    },
    txtSubtitle: {
      fontFamily: _theme.typography.regular,
      color: _theme.color.palette.darkestGrey,
      fontSize: 14,
      fontWeight: _reactNative.Platform.select({
        ios: "400",
        android: 'normal'
      }),
      marginLeft: 4
    },
    buttonClose: {
      position: 'absolute',
      top: 20,
      right: 20
    },
    viewRowButton: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: 16
    },
    buttonMoreInfo: {
      paddingHorizontal: 18,
      height: 44,
      borderRadius: 60,
      borderWidth: 1,
      borderColor: _theme.color.palette.purpleD5BBEA,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 8
    },
    txtMoreInfo: {
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.lightPurple,
      fontSize: 16,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: 'normal'
      })
    },
    buttonGetDirection: {
      width: '100%',
      height: 44,
      borderRadius: 60,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center'
    },
    txtButtonDirection: {
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.whiteGrey,
      fontSize: 16,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: 'normal'
      }),
      marginRight: 8
    }
  });
