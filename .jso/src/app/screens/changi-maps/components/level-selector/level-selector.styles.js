  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.upperGradientStyles = exports.scrollViewStyle = exports.lowerGradientStyles = exports.levelTextMainStyles = exports.levelTextMainActiveStyles = exports.levelButtonStyles = exports.levelButtonActiveStyles = exports.hiddenStyles = exports.directionNoteStyles = exports.directionNoteActiveStyles = exports.containerStyles = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var containerStyles = exports.containerStyles = Object.assign({}, _theme.shadow.primaryShadow, {
    justifyContent: "flex-start",
    alignItems: "stretch",
    zIndex: 999,
    position: "absolute",
    bottom: 55,
    left: 24,
    backgroundColor: _theme.color.palette.lightestGrey,
    borderRadius: 2,
    overflow: "hidden"
  });
  var scrollViewStyle = exports.scrollViewStyle = Object.assign({}, _theme.shadow.primaryShadow, {
    justifyContent: "flex-start",
    alignItems: "stretch",
    width: 44,
    height: 264,
    zIndex: 999,
    position: "absolute",
    bottom: 55,
    left: 24,
    backgroundColor: _theme.color.palette.lightestGrey,
    borderRadius: 2,
    overflow: "hidden"
  });
  var levelTextMainStyles = exports.levelTextMainStyles = {
    color: _theme.color.palette.midGrey,
    fontSize: 14,
    textAlign: "center",
    textAlignVertical: "center",
    fontWeight: "normal"
  };
  var levelTextMainActiveStyles = exports.levelTextMainActiveStyles = {
    color: _theme.color.palette.almostBlackGrey,
    fontSize: 14,
    textAlign: "center",
    textAlignVertical: "center",
    fontFamily: _theme.typography.bold
  };
  var directionNoteStyles = exports.directionNoteStyles = {
    color: _theme.color.palette.midGrey,
    fontSize: 9,
    textAlign: "center",
    textAlignVertical: "center"
  };
  var directionNoteActiveStyles = exports.directionNoteActiveStyles = {
    color: _theme.color.palette.almostBlackGrey,
    fontSize: 9,
    textAlign: "center",
    textAlignVertical: "center"
  };
  var levelButtonStyles = exports.levelButtonStyles = {
    height: 44,
    backgroundColor: _theme.color.palette.lightestGrey,
    width: 44,
    justifyContent: "center",
    alignItems: "center"
  };
  var levelButtonActiveStyles = exports.levelButtonActiveStyles = {
    height: 44,
    backgroundColor: _theme.color.palette.whiteGrey,
    width: 44,
    justifyContent: "center",
    alignItems: "center"
  };
  var upperGradientStyles = exports.upperGradientStyles = {
    position: "absolute",
    top: 0,
    width: 44,
    height: 24,
    zIndex: 99
  };
  var lowerGradientStyles = exports.lowerGradientStyles = {
    position: "absolute",
    bottom: 0,
    width: 44,
    height: 24,
    zIndex: 99
  };
  var hiddenStyles = exports.hiddenStyles = {
    display: "none"
  };
