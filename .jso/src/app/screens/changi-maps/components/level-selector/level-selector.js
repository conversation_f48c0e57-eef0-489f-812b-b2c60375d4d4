  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[8]));
  var _reactNativeSnapCarousel = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _lodash = _$$_REQUIRE(_dependencyMap[10]);
  var _utils = _$$_REQUIRE(_dependencyMap[11]);
  var _adobe = _$$_REQUIRE(_dependencyMap[12]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var validTerminal = ["T1", "T2", "T3", "T4"];
  var LevelSelector = function LevelSelector(_ref) {
    var building = _ref.building,
      onSelectLevel = _ref.onSelectLevel,
      levelData = _ref.levelData,
      selectedFloor = _ref.selectedFloor,
      isMoved = _ref.isMoved,
      isShouldHide = _ref.isShouldHide,
      currentPoint = _ref.currentPoint;
    var carousel = (0, _react.useRef)(null);
    var _useState = (0, _react.useState)(selectedFloor),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      activeFloor = _useState2[0],
      setActiveFloor = _useState2[1];
    (0, _react.useEffect)(function () {
      if (!(0, _lodash.isEmpty)(currentPoint) && !(0, _lodash.isEmpty)(levelData)) {
        var whereTaxanomy = currentPoint == null ? undefined : currentPoint.whereDimension;
        var currentFloorIndex = levelData == null ? undefined : levelData.findIndex(function (e) {
          return (e == null ? undefined : e.taxonomyPath) === whereTaxanomy;
        });
        if (currentFloorIndex >= 0) {
          handleLevelSelector(levelData[currentFloorIndex], currentFloorIndex);
        }
      }
    }, [currentPoint]);
    var getButtonStyle = (0, _react.useCallback)(function (levelName, activeFlr) {
      return levelName === activeFlr ? styles.levelButtonActiveStyles : styles.levelButtonStyles;
    }, [activeFloor]);
    var getTextStyle = function getTextStyle(levelName, activeFlr) {
      return levelName === activeFlr ? styles.levelTextMainActiveStyles : styles.levelTextMainStyles;
    };
    var getDirNoteStyle = function getDirNoteStyle(levelName, activeFlr) {
      return levelName === activeFlr ? styles.directionNoteActiveStyles : styles.directionNoteStyles;
    };
    var getDirectionNote = function getDirectionNote(levelName, activeFlr) {
      if (!validTerminal.includes(building)) return null;
      if (levelName !== "L1" && levelName !== "L2") return null;
      return (0, _jsxRuntime.jsx)(_text.Text, {
        style: getDirNoteStyle(levelName, activeFlr),
        children: levelName === "L1" ? "ARR" : "DEP"
      });
    };
    var handleLevelSelector = function handleLevelSelector(item, index) {
      var _carousel$current;
      onSelectLevel(item);
      carousel == null || (_carousel$current = carousel.current) == null || _carousel$current.snapToItem(index);
      setActiveFloor(item);
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppATOMsMap, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppATOMsMap, `${_adobe.AdobeValueByTagName.CAppATOMSMapLevelSelector}${item == null ? undefined : item.name}`));
    };
    var LevelItem = function LevelItem(_ref2) {
      var item = _ref2.item;
      var index = levelData == null ? undefined : levelData.findIndex(function (e) {
        return (e == null ? undefined : e.taxonomyPath) === (item == null ? undefined : item.taxonomyPath);
      });
      return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        onPress: function onPress() {
          return handleLevelSelector(item, index);
        },
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: getButtonStyle(item == null ? undefined : item.name, activeFloor == null ? undefined : activeFloor.name),
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: getTextStyle(item == null ? undefined : item.name, activeFloor == null ? undefined : activeFloor.name),
            children: item == null ? undefined : item.name
          }), getDirectionNote(item == null ? undefined : item.name, activeFloor == null ? undefined : activeFloor.name)]
        })
      });
    };
    (0, _react.useEffect)(function () {
      var index = levelData == null ? undefined : levelData.findIndex(function (e) {
        return e.name === selectedFloor.name;
      });
      setTimeout(function () {
        var _carousel$current2;
        carousel == null || (_carousel$current2 = carousel.current) == null || _carousel$current2.snapToItem(index);
      }, 1500);
    }, [selectedFloor, levelData]);
    var renderLevelCarousel = function renderLevelCarousel() {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: isShouldHide ? styles.hiddenStyles : styles.scrollViewStyle,
        children: [(0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
          pointerEvents: "none",
          style: styles.upperGradientStyles,
          colors: ["rgba(204,204,204,0.78)", "rgba(247,247,247,0.1)"]
        }), (0, _jsxRuntime.jsx)(_reactNativeSnapCarousel.default, {
          data: levelData,
          ref: carousel,
          renderItem: LevelItem,
          sliderHeight: 264,
          itemWidth: 44,
          itemHeight: 44,
          loop: true,
          activeSlideAlignment: "center",
          enableMomentum: true,
          decelerationRate: 0.9,
          vertical: true,
          layout: "default",
          enableSnap: true
        }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
          pointerEvents: "none",
          style: styles.lowerGradientStyles,
          colors: ["rgba(247,247,247,0.1)", "rgba(204,204,204,0.78)"]
        })]
      });
    };

    // Todo: Handle active level when navigate from Dine/Shop... get direction
    return (levelData == null ? undefined : levelData.length) >= 6 ? renderLevelCarousel() : (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: (0, _utils.handleCondition)(isShouldHide, styles.hiddenStyles, styles.containerStyles),
      children: levelData == null ? undefined : levelData.map(function (level) {
        return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: function onPress() {
            return onSelectLevel(level);
          },
          children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: getButtonStyle(level == null ? undefined : level.name, selectedFloor == null ? undefined : selectedFloor.name),
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: getTextStyle(level == null ? undefined : level.name, selectedFloor == null ? undefined : selectedFloor.name),
              children: level == null ? undefined : level.name
            }), getDirectionNote(level == null ? undefined : level.name, selectedFloor == null ? undefined : selectedFloor.name)]
          })
        }, level.name);
      })
    });
  };
  var _default = exports.default = (0, _react.memo)(LevelSelector);
