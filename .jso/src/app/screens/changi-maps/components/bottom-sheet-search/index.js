  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.BottomSheetSearch = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _styles = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _text = _$$_REQUIRE(_dependencyMap[8]);
  var _icons = _$$_REQUIRE(_dependencyMap[9]);
  var _inputField = _$$_REQUIRE(_dependencyMap[10]);
  var _isEmpty = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _loadingModal = _$$_REQUIRE(_dependencyMap[12]);
  var _mapAction = _$$_REQUIRE(_dependencyMap[13]);
  var _i18n = _$$_REQUIRE(_dependencyMap[14]);
  var _keyboardHook = _$$_REQUIRE(_dependencyMap[15]);
  var _adobe = _$$_REQUIRE(_dependencyMap[16]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[17]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var testID = "ChangiMapBottomSheetSearch";
  var BottomSheetSearch = exports.BottomSheetSearch = _react.default.memo(function (props) {
    var visible = props.visible,
      onClosedSheet = props.onClosedSheet,
      mapRef = props.mapRef,
      searchKeywordCollection = props.searchKeywordCollection,
      onSelectedSearchResult = props.onSelectedSearchResult;
    var timeout = (0, _react.useRef)(null);
    var searchRef = (0, _react.useRef)(null);
    var _useKeyboard = (0, _keyboardHook.useKeyboard)(),
      isKeyboardVisible = _useKeyboard.isKeyboardVisible;
    var _useState = (0, _react.useState)(""),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      value = _useState2[0],
      setValue = _useState2[1];
    var _useState3 = (0, _react.useState)([]),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      searchData = _useState4[0],
      setSearchResultData = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      loading = _useState6[0],
      setLoading = _useState6[1];
    var _useState7 = (0, _react.useState)(false),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      isSearching = _useState8[0],
      setIsSearching = _useState8[1];
    (0, _react.useEffect)(function () {
      if (!isKeyboardVisible) {
        var _searchRef$current;
        searchRef == null || (_searchRef$current = searchRef.current) == null || _searchRef$current.blur();
      }
    }, [isKeyboardVisible]);
    var onChangeText = function onChangeText(text) {
      setValue(text);
      setIsSearching(true);
      clearTimeout(timeout.current);
      if ((text == null ? undefined : text.length) === 0) {
        setValue("");
      } else if ((text == null ? undefined : text.length) > 0 && (text == null ? undefined : text.length) <= 4) {
        timeout.current = setTimeout(function () {
          searchLocation(text.trim());
        }, 1500);
      } else {
        timeout.current = setTimeout(function () {
          searchLocation(text.trim());
        }, 500);
      }
    };
    var searchLocation = function searchLocation(e) {
      setLoading(true);
      mapRef.current.getMapObjectsByName(e, true, "RelevanceStrict", function (data) {
        setLoading(false);
        var searchAbleResult = data == null ? undefined : data.filter(function (item) {
          var _item$properties;
          return (item == null || (_item$properties = item.properties) == null ? undefined : _item$properties.isSearchable) === true;
        });
        setIsSearching(false);
        setSearchResultData(searchAbleResult);
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppATOMsMap, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppATOMsMap, `${_adobe.AdobeValueByTagName.CAppATOMSMapSearchKeyword}${e} | ${(searchAbleResult == null ? undefined : searchAbleResult.length) || 0}`));
      });
    };
    var sendSearchKeywordToAA = function sendSearchKeywordToAA() {
      (0, _mapAction.uniqfyKeywordCollection)(searchKeywordCollection.current);
      searchKeywordCollection.current = [];
    };
    var selectResult = function selectResult(item) {
      onSelectedSearchResult(item);
      searchKeywordCollection.current.push(item == null ? undefined : item.name);
      sendSearchKeywordToAA();
      onClosedSheet();
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppATOMsMap, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppATOMsMap, `${_adobe.AdobeValueByTagName.CAppATOMSMapSearchResult}${item == null ? undefined : item.name}`));
    };
    var renderFlatListData = function renderFlatListData(item, index) {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _styles.styles.flatListItemsStyle,
        children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: function onPress() {
            return selectResult(item);
          },
          children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _styles.styles.flatListItemsStyle,
            children: [(0, _mapAction.getTenantIcon)(item), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _styles.styles.textContainer,
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                style: _styles.styles.titleStyle,
                numberOfLines: 1,
                children: item == null ? undefined : item.name
              }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: _styles.styles.desContainer,
                children: [(0, _mapAction.getTerminalIcon)(item), (0, _jsxRuntime.jsx)(_text.Text, {
                  style: _styles.styles.locationTextStyle,
                  numberOfLines: 1,
                  children: (0, _mapAction.getLevelName)(item)
                })]
              })]
            })]
          })
        })
      }, `${index}`);
    };
    var renderSeparator = function renderSeparator() {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _styles.styles.dividerStyle
      });
    };
    var emptyComponent = function emptyComponent() {
      if ((value == null ? undefined : value.length) < 2 || isSearching) {
        return null;
      }
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _styles.styles.emptyContainer,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          style: _styles.styles.emptyText,
          children: "No search results found"
        })
      });
    };
    var onClearSearchAll = function onClearSearchAll() {
      setValue("");
      setSearchResultData([]);
    };
    return (0, _jsxRuntime.jsxs)(_bottomSheet.default, {
      isModalVisible: visible,
      onClosedSheet: onClosedSheet,
      containerStyle: _styles.styles.bottomSheetStyle,
      stopDragCollapse: true,
      onBackPressHandle: onClosedSheet,
      animationInTiming: 200,
      animationOutTiming: 200,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _styles.styles.headerFilter,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _styles.styles.rightHeader
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: _styles.styles.titleSheet,
          tx: "changimap.header-search"
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: function onPress() {
            setValue("");
            setSearchResultData([]);
            onClosedSheet();
          },
          children: (0, _jsxRuntime.jsx)(_icons.CrossBlack, {
            width: 24,
            height: 24
          })
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _styles.styles.viewSearch,
        children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _styles.styles.viewBorder,
          children: (0, _jsxRuntime.jsx)(_inputField.InputField, {
            testID: `${testID}__InputField`,
            forwardedRef: searchRef,
            search: true,
            isShowClearAll: !(0, _isEmpty.default)(value),
            value: value,
            returnKeyType: "search",
            highlightOnFocused: true,
            autoCapitalize: "none",
            autoCorrect: false,
            maxLength: 50,
            onChangeText: onChangeText,
            onSubmitEditing: function onSubmitEditing() {
              _reactNative2.Keyboard.dismiss();
            },
            onSearchClear: onClearSearchAll,
            placeHolderValue: value || (0, _i18n.translate)("changimap.placeholder-search"),
            customLeftIconActive: (0, _jsxRuntime.jsx)(_icons.SearchIconInActive, {
              style: _styles.styles.iconSearch,
              width: "18",
              height: "18"
            }),
            customLeftIconInActive: (0, _jsxRuntime.jsx)(_icons.SearchIconInActive, {
              style: _styles.styles.iconSearch,
              width: "18",
              height: "18"
            }),
            iconClose: (0, _jsxRuntime.jsx)(_icons.CrossGray, {
              width: 24,
              height: 24
            })
          })
        })
      }), (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
        keyExtractor: function keyExtractor(item) {
          return item.mapObjectId;
        },
        onScroll: _reactNative2.Keyboard.dismiss,
        keyboardShouldPersistTaps: "handled",
        data: searchData,
        renderItem: function renderItem(_ref) {
          var item = _ref.item,
            index = _ref.index;
          return renderFlatListData(item, index);
        },
        ItemSeparatorComponent: renderSeparator,
        onEndReachedThreshold: 0.3,
        style: _styles.styles.container,
        contentContainerStyle: _styles.styles.paddingContentList,
        ListEmptyComponent: emptyComponent,
        scrollEnabled: true
      }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
        visible: loading
      })]
    });
  });
