  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    bottomSheetStyle: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      height: _reactNative.Dimensions.get('window').height - 64,
      overflow: "hidden",
      width: "100%"
    },
    headerFilter: {
      display: "flex",
      flexDirection: "row",
      marginVertical: 22,
      width: "100%",
      paddingHorizontal: 16
    },
    rightHeader: {
      width: 24
    },
    titleSheet: Object.assign({
      color: _theme.color.palette.almostBlackGrey
    }, _text.presets.subTitleBold, {
      flexGrow: 2,
      textAlign: "center"
    }),
    iconStyleBlur: {
      color: _theme.color.palette.darkGrey999,
      marginRight: 10,
      marginTop: 9
    },
    iconStyleFocus: {
      color: _theme.color.palette.lightPurple,
      marginRight: 10,
      marginTop: 9
    },
    iconSearch: {
      marginLeft: 5,
      marginRight: 10,
      marginTop: 12
    },
    viewSearch: {
      paddingHorizontal: 20
    },
    viewBorder: {
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 2.9,
      borderColor: _theme.color.palette.lightestPurple,
      borderRadius: 15,
      padding: 0
    },
    container: {
      flex: 1
    },
    paddingContentList: {
      paddingBottom: 130,
      paddingHorizontal: 24
    },
    flatListItemsStyle: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "flex-start",
      paddingVerticals: 16
    },
    textContainer: {
      alignSelf: "center",
      flexBasis: "72%",
      flexDirection: "column",
      justifyContent: "center",
      marginLeft: 16,
      marginVertical: 16
    },
    titleStyle: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey,
      marginBottom: 4
    }),
    desContainer: {
      alignItems: "center",
      flexDirection: "row"
    },
    locationTextStyle: Object.assign({}, _text.presets.caption2Regular, {
      color: _theme.color.palette.darkestGrey,
      marginLeft: 8
    }),
    dividerStyle: {
      backgroundColor: _theme.color.palette.lighterGrey,
      height: 1
    },
    emptyContainer: {
      padding: 24,
      width: "100%"
    },
    emptyText: Object.assign({}, _text.presets.caption1Regular, {
      color: _theme.color.palette.almostBlackGrey,
      textAlign: "center",
      width: "100%"
    })
  });
