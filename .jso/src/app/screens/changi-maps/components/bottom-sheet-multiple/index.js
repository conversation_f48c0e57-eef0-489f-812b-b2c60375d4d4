  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.BottomSheetSearchMultiple = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _styles = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _icons = _$$_REQUIRE(_dependencyMap[8]);
  var _inputField = _$$_REQUIRE(_dependencyMap[9]);
  var _isEmpty = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _loadingModal = _$$_REQUIRE(_dependencyMap[11]);
  var _mapAction = _$$_REQUIRE(_dependencyMap[12]);
  var _i18n = _$$_REQUIRE(_dependencyMap[13]);
  var _keyboardHook = _$$_REQUIRE(_dependencyMap[14]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[15]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var BottomSheetSearchMultiple = exports.BottomSheetSearchMultiple = _react.default.memo(function (props) {
    var _dataLocationFrom$pro, _dataLocationTo$prope;
    var visible = props.visible,
      onClosedSheet = props.onClosedSheet,
      mapRef = props.mapRef,
      dataLocationTo = props.dataLocationTo,
      dataLocationFrom = props.dataLocationFrom,
      setDataLocationTo = props.setDataLocationTo,
      setDataLocationFrom = props.setDataLocationFrom,
      onDirection = props.onDirection,
      reverseLocation = props.reverseLocation;
    var timeoutFrom = (0, _react.useRef)(null);
    var timeoutTo = (0, _react.useRef)(null);
    var searchRefFrom = (0, _react.useRef)(null);
    var searchRefTo = (0, _react.useRef)(null);
    var _useKeyboard = (0, _keyboardHook.useKeyboard)(),
      isKeyboardVisible = _useKeyboard.isKeyboardVisible;
    var _useState = (0, _react.useState)(dataLocationFrom == null || (_dataLocationFrom$pro = dataLocationFrom.properties) == null ? undefined : _dataLocationFrom$pro.title),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      valueFrom = _useState2[0],
      setValueFrom = _useState2[1];
    var _useState3 = (0, _react.useState)(dataLocationTo == null || (_dataLocationTo$prope = dataLocationTo.properties) == null ? undefined : _dataLocationTo$prope.title),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      valueTo = _useState4[0],
      setValueTo = _useState4[1];
    var _useState5 = (0, _react.useState)(dataLocationFrom),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      valueDataFrom = _useState6[0],
      setValueDataFrom = _useState6[1];
    var _useState7 = (0, _react.useState)(dataLocationTo),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      valueDataTo = _useState8[0],
      setValueDataTo = _useState8[1];
    var _useState9 = (0, _react.useState)(null),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      focus = _useState0[0],
      setFocus = _useState0[1];
    var _useState1 = (0, _react.useState)([]),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      searchData = _useState10[0],
      setSearchResultData = _useState10[1];
    var _useState11 = (0, _react.useState)(false),
      _useState12 = (0, _slicedToArray2.default)(_useState11, 2),
      loading = _useState12[0],
      setLoading = _useState12[1];
    (0, _react.useEffect)(function () {
      if (!isKeyboardVisible) {
        if (focus === 'FROM') {
          var _searchRefFrom$curren;
          searchRefFrom == null || (_searchRefFrom$curren = searchRefFrom.current) == null || _searchRefFrom$curren.blur();
        } else {
          var _searchRefTo$current;
          searchRefTo == null || (_searchRefTo$current = searchRefTo.current) == null || _searchRefTo$current.blur();
        }
      }
    }, [isKeyboardVisible]);
    var onChangeText = function onChangeText(text) {
      setValueFrom(text);
      clearTimeout(timeoutFrom.current);
      if ((text == null ? undefined : text.length) === 0) {
        setValueFrom("");
      } else if ((text == null ? undefined : text.length) > 0 && (text == null ? undefined : text.length) <= 4) {
        timeoutFrom.current = setTimeout(function () {
          searchLocation(text.trim());
        }, 1500);
      } else {
        timeoutFrom.current = setTimeout(function () {
          searchLocation(text.trim());
        }, 500);
      }
    };
    var onChangeTextTo = function onChangeTextTo(text) {
      setValueTo(text);
      clearTimeout(timeoutTo.current);
      if ((text == null ? undefined : text.length) === 0) {
        setValueTo("");
      } else if ((text == null ? undefined : text.length) > 0 && (text == null ? undefined : text.length) <= 4) {
        timeoutTo.current = setTimeout(function () {
          searchLocation(text.trim());
        }, 1500);
      } else {
        timeoutTo.current = setTimeout(function () {
          searchLocation(text.trim());
        }, 500);
      }
    };
    var searchLocation = function searchLocation(e) {
      setLoading(true);
      mapRef.current.getMapObjectsByName(e, true, "RelevanceStrict", function (data) {
        setLoading(false);
        var searchAbleResult = data == null ? undefined : data.filter(function (item) {
          var _item$properties;
          return (item == null || (_item$properties = item.properties) == null ? undefined : _item$properties.isSearchable) === true;
        });
        setSearchResultData(searchAbleResult);
      });
    };
    var selectResult = function selectResult(item) {
      setSearchResultData([]);
      if (focus === 'FROM') {
        var _item$properties2;
        setValueFrom(item == null || (_item$properties2 = item.properties) == null ? undefined : _item$properties2.title);
        setDataLocationFrom(item);
        setValueDataFrom(item);
        onDirection(item, valueDataTo);
      } else if (focus === 'TO') {
        var _item$properties3;
        setValueTo(item == null || (_item$properties3 = item.properties) == null ? undefined : _item$properties3.title);
        setDataLocationTo(item);
        setValueDataTo(item);
        onDirection(valueDataFrom, item);
      }
    };
    var renderFlatListData = function renderFlatListData(item, index) {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _styles.styles.flatListItemsStyle,
        children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: function onPress() {
            return selectResult(item);
          },
          children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _styles.styles.flatListItemsStyle,
            children: [(0, _mapAction.getTenantIcon)(item), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _styles.styles.textContainer,
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                style: _styles.styles.titleStyle,
                numberOfLines: 1,
                children: item == null ? undefined : item.name
              }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: _styles.styles.desContainer,
                children: [(0, _mapAction.getTerminalIcon)(item), (0, _jsxRuntime.jsx)(_text.Text, {
                  style: _styles.styles.locationTextStyle,
                  numberOfLines: 1,
                  children: (0, _mapAction.getLevelName)(item)
                })]
              })]
            })]
          })
        })
      }, `${index}`);
    };
    var renderSeparator = function renderSeparator() {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _styles.styles.dividerStyle
      });
    };
    var onClearSearchAllFrom = (0, _react.useCallback)(function () {
      setValueFrom("");
      setValueDataFrom(null);
      setSearchResultData([]);
    }, []);
    var onClearSearchAllTo = (0, _react.useCallback)(function () {
      setValueTo("");
      setValueDataTo(null);
      setSearchResultData([]);
    }, []);
    var closeSheet = function closeSheet() {
      setSearchResultData([]);
      onClosedSheet();
    };
    var reverseLocationComponent = function reverseLocationComponent() {
      if (valueDataFrom && valueDataTo) {
        setDataLocationFrom(dataLocationTo);
        setDataLocationTo(dataLocationFrom);
        reverseLocation(valueDataTo, valueDataFrom);
      } else {
        setValueDataFrom(valueDataTo);
        setValueDataTo(valueDataFrom);
        setValueTo(valueFrom);
        setValueFrom(valueTo);
      }
    };
    return (0, _jsxRuntime.jsxs)(_bottomSheet.default, {
      isModalVisible: visible,
      onClosedSheet: closeSheet,
      containerStyle: _styles.styles.bottomSheetStyle,
      stopDragCollapse: true,
      onBackPressHandle: closeSheet,
      animationInTiming: 200,
      animationOutTiming: 200,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _styles.styles.headerFilter,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _styles.styles.rightHeader
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: _styles.styles.titleSheet,
          tx: "changimap.titleSearchLocationFromTo"
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: closeSheet,
          children: (0, _jsxRuntime.jsx)(_icons.CrossBlack, {
            width: 24,
            height: 24
          })
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _styles.styles.viewSearch,
        children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _styles.styles.viewBorder,
          children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _styles.styles.viewContentSearch,
            children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _styles.styles.viewContentLeft,
              children: [(0, _jsxRuntime.jsx)(_icons.AtomLocationFrom, {}), (0, _jsxRuntime.jsx)(_icons.AtomLocationThreeDot, {}), (0, _jsxRuntime.jsx)(_icons.AtomLocationTo, {})]
            }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _styles.styles.viewInput,
              children: [(0, _jsxRuntime.jsx)(_inputField.InputField, {
                isShowClearAll: !(0, _isEmpty.default)(valueFrom),
                value: valueFrom,
                autoCapitalize: "none",
                autoCorrect: false,
                maxLength: 50,
                onChangeText: onChangeText,
                onSubmitEditing: function onSubmitEditing() {
                  _reactNative2.Keyboard.dismiss();
                },
                onSearchClear: onClearSearchAllFrom,
                placeHolderValue: (0, _i18n.translate)("changimap.placeholder-from"),
                customStyle: _styles.styles.input,
                onFocus: function onFocus() {
                  setFocus("FROM");
                },
                iconClose: (0, _jsxRuntime.jsx)(_icons.CrossGray, {
                  width: 24,
                  height: 24
                }),
                forwardedRef: searchRefFrom
              }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: _styles.styles.divinder
              }), (0, _jsxRuntime.jsx)(_inputField.InputField, {
                isShowClearAll: !(0, _isEmpty.default)(valueTo),
                value: valueTo,
                autoCapitalize: "none",
                autoCorrect: false,
                maxLength: 50,
                onChangeText: onChangeTextTo,
                onSubmitEditing: function onSubmitEditing() {
                  _reactNative2.Keyboard.dismiss();
                },
                onSearchClear: onClearSearchAllTo,
                placeHolderValue: (0, _i18n.translate)("changimap.placeholder-to"),
                customStyle: _styles.styles.input,
                onFocus: function onFocus() {
                  setFocus("TO");
                },
                iconClose: (0, _jsxRuntime.jsx)(_icons.CrossGray, {
                  width: 24,
                  height: 24
                }),
                forwardedRef: searchRefTo
              })]
            }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: _styles.styles.viewContentRight,
              children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                onPress: reverseLocationComponent,
                children: (0, _jsxRuntime.jsx)(_icons.AtomLocationChangeLocation, {})
              })
            })]
          })
        })
      }), (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
        keyExtractor: function keyExtractor(item) {
          return item.mapObjectId;
        },
        onScroll: _reactNative2.Keyboard.dismiss,
        keyboardShouldPersistTaps: "handled",
        data: searchData,
        renderItem: function renderItem(_ref) {
          var item = _ref.item,
            index = _ref.index;
          return renderFlatListData(item, index);
        },
        ItemSeparatorComponent: renderSeparator,
        onEndReachedThreshold: 0.3,
        style: _styles.styles.container,
        contentContainerStyle: _styles.styles.paddingContentList,
        scrollEnabled: true
      }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
        visible: loading
      })]
    });
  });
