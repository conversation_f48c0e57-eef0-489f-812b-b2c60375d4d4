  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ChangiMapHeaderV2 = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _styles = _$$_REQUIRE(_dependencyMap[4]);
  var _icons = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  var ChangiMapHeaderV2 = exports.ChangiMapHeaderV2 = _react.default.memo(function (props) {
    var title = props.title,
      onBack = props.onBack;
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: _styles.styles.container,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _styles.styles.viewRowItem,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _styles.styles.viewIcon,
          children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: onBack,
            children: (0, _jsxRuntime.jsx)(_icons.ArrowLeftGray, {})
          })
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _styles.styles.viewTitle,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            text: title,
            style: _styles.styles.titleStyles,
            numberOfLines: 1
          })
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _styles.styles.viewIcon
        })]
      })
    });
  });
