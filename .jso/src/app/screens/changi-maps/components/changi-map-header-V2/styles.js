  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    container: {
      width: '100%',
      height: 100,
      backgroundColor: _theme.color.palette.whiteGrey,
      justifyContent: 'flex-end',
      paddingBottom: 16,
      paddingHorizontal: 16,
      zIndex: 9
    },
    viewRowItem: {
      width: '100%',
      flexDirection: 'row',
      alignItems: 'center'
    },
    viewIcon: {
      width: '15%'
    },
    viewTitle: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center'
    },
    titleStyles: {
      fontFamily: _theme.typography.bold,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: 'normal'
      }),
      justifyContent: "center",
      textAlign: "center",
      fontSize: 16,
      color: _theme.color.palette.almostBlackGrey,
      lineHeight: 20
    }
  });
