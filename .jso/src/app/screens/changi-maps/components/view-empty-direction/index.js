  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ViewEmptyDirection = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _styles = _$$_REQUIRE(_dependencyMap[4]);
  var _icons = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _theme = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  var colors = [_theme.color.palette.gradientColor1End, _theme.color.palette.lightPurple];
  var ViewEmptyDirection = exports.ViewEmptyDirection = _react.default.memo(function (_ref) {
    var onPress = _ref.onPress;
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _styles.styles.container,
      children: [(0, _jsxRuntime.jsx)(_icons.AtomEmptyDirection, {}), (0, _jsxRuntime.jsx)(_text.Text, {
        tx: "changimap.empty-direction",
        style: _styles.styles.txtEmpty
      }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        onPress: onPress,
        children: (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
          style: _styles.styles.btn,
          colors: colors,
          end: {
            x: 0,
            y: 1
          },
          start: {
            x: 0,
            y: 0
          },
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            tx: "changimap.update-locations",
            style: _styles.styles.txtButton
          })
        })
      })]
    });
  });
