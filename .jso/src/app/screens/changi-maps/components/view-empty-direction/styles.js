  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var widthScreen = _reactNative.Dimensions.get("window").width;
  var heightScreen = _reactNative.Dimensions.get("window").height;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    container: {
      width: widthScreen,
      height: heightScreen - 50,
      position: 'absolute',
      top: 100,
      bottom: 0,
      left: 0,
      right: 0,
      paddingTop: 156,
      alignItems: 'center',
      backgroundColor: _theme.color.palette.almostWhiteGrey
    },
    txtEmpty: {
      fontFamily: _theme.typography.regular,
      fontWeight: _reactNative.Platform.select({
        ios: "400",
        android: 'normal'
      }),
      fontSize: 16,
      lineHeight: 20,
      color: _theme.color.palette.almostBlackGrey,
      marginVertical: 24
    },
    btn: {
      width: 216,
      height: 44,
      borderRadius: 60,
      justifyContent: 'center',
      alignItems: 'center'
    },
    txtButton: {
      fontFamily: _theme.typography.bold,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: 'normal'
      }),
      color: _theme.color.palette.whiteGrey,
      fontSize: 16
    }
  });
