  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ViewStep = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  var ViewStep = exports.ViewStep = _react.default.memo(function (props) {
    var _dataDirection$step;
    var onExitDirection = props.onExitDirection,
      step = props.step,
      dataDirection = props.dataDirection,
      onPrevious = props.onPrevious,
      onNext = props.onNext,
      onListDirection = props.onListDirection;
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        style: styles.txt,
        children: (_dataDirection$step = dataDirection[step]) == null ? undefined : _dataDirection$step.instructions[0]
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.viewButton,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.viewRow,
          children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: styles.buttonSmall,
            onPress: onListDirection,
            children: (0, _jsxRuntime.jsx)(_icons.AtomList, {})
          }), step > 0 ? (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: styles.buttonSmall,
            onPress: onPrevious,
            children: (0, _jsxRuntime.jsx)(_icons.AtomPrevious, {})
          }) : null, step === (dataDirection == null ? undefined : dataDirection.length) - 1 ? null : (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: styles.buttonSmall,
            onPress: onNext,
            children: (0, _jsxRuntime.jsx)(_icons.AtomNext, {})
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: styles.buttonExit,
          onPress: onExitDirection,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            tx: "changimap.exit",
            style: styles.txtExit
          })
        })]
      })]
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    container: {
      position: 'absolute',
      width: '100%',
      paddingBottom: 48,
      paddingTop: 24,
      paddingHorizontal: 20,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      bottom: 0,
      backgroundColor: _theme.color.palette.whiteGrey,
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 1
      },
      shadowOpacity: 0.22,
      shadowRadius: 2.22,
      elevation: 3
    },
    txt: {
      fontFamily: _theme.typography.black,
      fontSize: 18,
      fontWeight: _reactNative2.Platform.select({
        ios: "900",
        android: 'normal'
      }),
      color: _theme.color.palette.almostBlackGrey,
      lineHeight: 22
    },
    viewButton: {
      width: '100%',
      flexDirection: 'row',
      alignItems: 'flex-end',
      justifyContent: 'space-between',
      marginTop: 8
    },
    viewRow: {
      flexDirection: 'row',
      alignItems: 'center'
    },
    buttonSmall: {
      marginRight: 8
    },
    buttonExit: {
      width: 69,
      height: 44,
      borderRadius: 60,
      borderWidth: 1,
      borderColor: _theme.color.palette.purpleD5BBEA,
      justifyContent: 'center',
      alignItems: 'center'
    },
    txtExit: {
      fontFamily: _theme.typography.bold,
      fontSize: 16,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: 'normal'
      }),
      color: _theme.color.palette.lightPurple
    }
  });
