  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ViewDash = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _icons = _$$_REQUIRE(_dependencyMap[4]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  var ViewDash = exports.ViewDash = _react.default.memo(function () {
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.container,
        children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.viewAbsolute
        }), (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.viewPositionTop,
          children: (0, _jsxRuntime.jsx)(_icons.AtomLocationFrom, {})
        }), (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.viewPositionBottom,
          children: (0, _jsxRuntime.jsx)(_icons.AtomLocationTo, {})
        })]
      })
    });
  });
  var styles = _reactNative.StyleSheet.create({
    container: {
      height: '100%',
      width: 10.5,
      borderWidth: 2,
      borderStyle: 'dashed',
      borderColor: '#CCCCCC',
      position: 'absolute'
    },
    viewAbsolute: {
      flex: 1,
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      marginLeft: -10
    },
    viewPositionTop: {
      position: 'absolute',
      top: -2,
      left: -2,
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      paddingBottom: 5
    },
    viewPositionBottom: {
      position: 'absolute',
      bottom: -2,
      left: -2,
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      paddingTop: 5
    }
  });
