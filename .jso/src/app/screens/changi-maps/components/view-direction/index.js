  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ViewDirection = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[0]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNativeGestureHandler = _$$_REQUIRE(_dependencyMap[4]);
  var _styles = _$$_REQUIRE(_dependencyMap[5]);
  var _ViewHeader = _$$_REQUIRE(_dependencyMap[6]);
  var _ViewDash = _$$_REQUIRE(_dependencyMap[7]);
  var _icons = _$$_REQUIRE(_dependencyMap[8]);
  var _HeaderList = _$$_REQUIRE(_dependencyMap[9]);
  var _ViewStep = _$$_REQUIRE(_dependencyMap[10]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var heightView = _reactNative2.Dimensions.get('window').height - 64;
  var _worklet_13883591828281_init_data = {
    code: "function indexTsx1(){const{offset}=this.__closure;return{height:offset.value};}"
  };
  var _worklet_6084457578522_init_data = {
    code: "function indexTsx2(event){const{offset,withSpring,heightView}=this.__closure;if((event===null||event===void 0?void 0:event.translationY)<1){offset.value=withSpring(heightView);}else{offset.value=withSpring(240);}}"
  };
  var _worklet_16436813826125_init_data = {
    code: "function indexTsx3(event){const{offset}=this.__closure;offset.value=Math.max(100,offset.value-event.changeY);}"
  };
  var _worklet_3261898965559_init_data = {
    code: "function indexTsx4(){}"
  };
  var ViewDirection = exports.ViewDirection = _react.default.memo(function (props) {
    var onShowHideViewBackground = props.onShowHideViewBackground,
      onCloseViewDirection = props.onCloseViewDirection,
      data = props.data,
      dataLocationTo = props.dataLocationTo,
      dataLocationFrom = props.dataLocationFrom,
      searchDirection = props.searchDirection,
      accessibleRoute = props.accessibleRoute,
      setAccessibleRoute = props.setAccessibleRoute,
      status = props.status,
      skipOverview = props.skipOverview,
      step = props.step,
      _onStartDirection = props.onStartDirection,
      onExitDirection = props.onExitDirection,
      onPreviousStep = props.onPreviousStep,
      onNextStep = props.onNextStep;
    var offset = (0, _reactNativeReanimated.useSharedValue)(240);
    var dataSlice = data == null ? undefined : data.segments.slice(skipOverview);
    var animatedStyles = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var indexTsx1 = function indexTsx1() {
        return {
          height: offset.value
        };
      };
      indexTsx1.__closure = {
        offset: offset
      };
      indexTsx1.__workletHash = 13883591828281;
      indexTsx1.__initData = _worklet_13883591828281_init_data;
      return indexTsx1;
    }());
    var pan = _reactNativeGestureHandler.Gesture.Pan().onBegin(function () {
      var indexTsx4 = function indexTsx4() {};
      indexTsx4.__closure = {};
      indexTsx4.__workletHash = 3261898965559;
      indexTsx4.__initData = _worklet_3261898965559_init_data;
      return indexTsx4;
    }()).onChange(function () {
      var indexTsx3 = function indexTsx3(event) {
        offset.value = Math.max(100, offset.value - event.changeY);
      };
      indexTsx3.__closure = {
        offset: offset
      };
      indexTsx3.__workletHash = 16436813826125;
      indexTsx3.__initData = _worklet_16436813826125_init_data;
      return indexTsx3;
    }()).onFinalize(function () {
      var indexTsx2 = function indexTsx2(event) {
        if ((event == null ? undefined : event.translationY) < 1) {
          offset.value = (0, _reactNativeReanimated.withSpring)(heightView);
        } else {
          offset.value = (0, _reactNativeReanimated.withSpring)(240);
        }
      };
      indexTsx2.__closure = {
        offset: offset,
        withSpring: _reactNativeReanimated.withSpring,
        heightView: heightView
      };
      indexTsx2.__workletHash = 6084457578522;
      indexTsx2.__initData = _worklet_6084457578522_init_data;
      return indexTsx2;
    }());
    var _renderItem = (0, _react.useCallback)(function (_ref) {
      var item = _ref.item,
        index = _ref.index;
      return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: _styles.styles.viewItem,
        onPress: function onPress() {
          _onStartDirection(index + skipOverview);
          onShowHideViewBackground(0);
        },
        children: [(0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: _styles.styles.itemTxt,
          children: item == null ? undefined : item.instructions[0]
        }), (0, _jsxRuntime.jsx)(_icons.AtomDirectionRight, {})]
      });
    }, [_onStartDirection, skipOverview]);
    var headerSubList = (0, _react.useCallback)(function () {
      var _dataLocationFrom$pro;
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: [_styles.styles.viewItemLastAndFirst, {
          paddingBottom: 16,
          borderBottomWidth: 1
        }],
        children: (0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: _styles.styles.itemTxtBold,
          children: dataLocationFrom == null || (_dataLocationFrom$pro = dataLocationFrom.properties) == null ? undefined : _dataLocationFrom$pro.title
        })
      });
    }, [dataLocationFrom]);
    var fotterSubList = (0, _react.useCallback)(function () {
      var _dataLocationTo$prope;
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: [_styles.styles.viewItemLastAndFirst, {
          marginTop: 16
        }],
        children: (0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: _styles.styles.itemTxtBold,
          children: dataLocationTo == null || (_dataLocationTo$prope = dataLocationTo.properties) == null ? undefined : _dataLocationTo$prope.title
        })
      });
    }, [dataLocationTo]);
    var renderHeaderList = (0, _react.useCallback)(function () {
      return (0, _jsxRuntime.jsx)(_HeaderList.HeaderList, {
        data: data,
        onStartDirection: function onStartDirection() {
          _onStartDirection(skipOverview);
          onShowHideViewBackground(0);
        },
        accessibleRoute: accessibleRoute
      });
    }, [data, _onStartDirection, skipOverview, accessibleRoute]);
    var onLayout = (0, _react.useCallback)(function (event) {
      var height = event.nativeEvent.layout.height;
      onShowHideViewBackground(height);
    }, [onShowHideViewBackground]);
    var onPrevious = (0, _react.useCallback)(function () {
      var index = step - skipOverview;
      if (index >= 1) {
        onPreviousStep(step - 1);
      }
    }, [step, skipOverview]);
    var onNext = (0, _react.useCallback)(function () {
      var index = step - skipOverview;
      if (index !== (dataSlice == null ? undefined : dataSlice.length) - 1) {
        onNextStep(step + 1);
      }
    }, [step, skipOverview, dataSlice]);
    var onExit = function onExit() {
      offset.value = (0, _reactNativeReanimated.withSpring)(240);
      onExitDirection();
    };
    var onListDirection = function onListDirection() {
      offset.value = heightView;
      onExitDirection();
    };

    // useEffect(() => {
    //   if (status === 'OVERVIEW') {
    //     offset.value = withSpring(240);
    //   }
    // }, [status])

    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: status === 'OVERVIEW' ? (0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
        style: [_styles.styles.container, animatedStyles],
        onLayout: onLayout,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _styles.styles.viewHeader,
          children: [(0, _jsxRuntime.jsx)(_reactNativeGestureHandler.GestureDetector, {
            gesture: pan,
            children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: _styles.styles.viewGesture
            })
          }), (0, _jsxRuntime.jsx)(_ViewHeader.ViewHeader, {
            status: accessibleRoute,
            setStatus: function setStatus(value) {
              searchDirection(value);
              setAccessibleRoute(value);
            },
            onClose: onCloseViewDirection
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
          data: [1],
          renderItem: function renderItem() {
            return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _styles.styles.viewList,
              children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: _styles.styles.viewColumnList,
                children: (0, _jsxRuntime.jsx)(_ViewDash.ViewDash, {})
              }), (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
                style: _styles.styles.list,
                data: dataSlice,
                renderItem: _renderItem,
                keyExtractor: function keyExtractor(item, index) {
                  var _item$startEdge;
                  return `${item == null || (_item$startEdge = item.startEdge) == null ? undefined : _item$startEdge.mapObjectId}+${index}`;
                },
                bounces: false,
                scrollEnabled: false,
                ListHeaderComponent: headerSubList,
                ListFooterComponent: fotterSubList
              })]
            });
          },
          ListHeaderComponent: renderHeaderList,
          keyExtractor: function keyExtractor(index) {
            return index.toString();
          },
          bounces: false
        })]
      }) : (0, _jsxRuntime.jsx)(_ViewStep.ViewStep, {
        step: step - skipOverview,
        onExitDirection: onExit,
        dataDirection: dataSlice,
        onPrevious: onPrevious,
        onNext: onNext,
        onListDirection: onListDirection
      })
    });
  });
