  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    container: {
      width: '100%',
      position: 'absolute',
      bottom: 0,
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      zIndex: 9999
    },
    viewHeader: {
      paddingHorizontal: 20
    },
    viewList: {
      flex: 1,
      flexDirection: 'row',
      paddingHorizontal: 20,
      marginBottom: 30
    },
    list: {
      flex: 1
    },
    viewColumnList: {
      height: '100%',
      width: 24,
      marginRight: 16,
      justifyContent: 'space-between'
    },
    viewItem: {
      flex: 1,
      flexDirection: 'row',
      borderColor: _theme.color.palette.lighterGrey,
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: 16,
      borderBottomWidth: 1,
      paddingBottom: 16
    },
    viewItemLastAndFirst: {
      flex: 1,
      flexDirection: 'row',
      borderColor: _theme.color.palette.lighterGrey,
      justifyContent: 'space-between',
      alignItems: 'center'
    },
    itemTxt: {
      fontFamily: _theme.typography.regular,
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 14,
      fontWeight: _reactNative.Platform.select({
        ios: "400",
        android: 'normal'
      })
    },
    itemTxtBold: {
      fontFamily: _theme.typography.black,
      fontSize: 16,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: 'normal'
      }),
      color: _theme.color.palette.almostBlackGrey
    },
    viewGesture: {
      width: _reactNative.Dimensions.get('window').width,
      height: 50,
      position: 'absolute',
      top: -30,
      backgroundColor: _theme.color.transparent,
      zIndex: 999999
    }
  });
