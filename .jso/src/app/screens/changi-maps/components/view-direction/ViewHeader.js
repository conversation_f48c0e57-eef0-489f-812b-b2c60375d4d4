  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ViewHeader = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _icons = _$$_REQUIRE(_dependencyMap[3]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _text2 = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  var colorBorderInActive = _theme.color.palette.lighterGrey;
  var colorTextInActive = _theme.color.palette.darkGrey999;
  var colorTextActive = _theme.color.palette.almostBlackGrey;
  var colorActive = _theme.color.palette.lightPurple;
  var colorIconInactive = "#ABABAB";
  var ViewHeader = exports.ViewHeader = _react.default.memo(function (props) {
    var status = props.status,
      setStatus = props.setStatus,
      onClose = props.onClose;
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.viewRowContent,
        children: [(0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
          style: [styles.button, {
            borderColor: !status ? colorActive : colorBorderInActive
          }],
          onPress: function onPress() {
            if (status) {
              setStatus(false);
            }
          },
          children: [(0, _jsxRuntime.jsx)(_reactNative2.Image, {
            source: _icons.WalkingIcon,
            tintColor: !status ? colorActive : colorIconInactive,
            style: styles.icon
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            tx: "changimap.walking-Route",
            style: [styles.txtButton, {
              color: !status ? colorTextActive : colorTextInActive
            }]
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
          style: [styles.button, {
            borderColor: status ? colorActive : colorBorderInActive
          }],
          onPress: function onPress() {
            if (!status) {
              setStatus(true);
            }
          },
          children: [(0, _jsxRuntime.jsx)(_reactNative2.Image, {
            source: _icons.AccessibleIcon,
            tintColor: status ? colorActive : colorIconInactive,
            style: styles.icon
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            tx: "changimap.accessible-Route",
            style: [styles.txtButton, {
              color: status ? colorTextActive : colorTextInActive
            }]
          })]
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        onPress: onClose,
        style: styles.buttonClose,
        children: (0, _jsxRuntime.jsx)(_icons.CloseIconBlack, {})
      })]
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    container: {
      width: '100%',
      flexDirection: 'row',
      alignItems: 'center',
      paddingTop: 20
    },
    viewRowContent: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: 19
    },
    button: {
      width: '50%',
      height: 48,
      justifyContent: 'center',
      alignItems: 'center',
      borderBottomWidth: 2,
      flexDirection: 'row'
    },
    txtButton: Object.assign({}, _text2.presets.caption1Bold, {
      marginLeft: 8
    }),
    icon: {
      width: 24,
      height: 24
    },
    buttonClose: {
      marginTop: -18
    }
  });
