  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.HeaderList = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _i18n = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  var HeaderList = exports.HeaderList = _react.default.memo(function (_ref) {
    var data = _ref.data,
      onStartDirection = _ref.onStartDirection,
      accessibleRoute = _ref.accessibleRoute;
    var walkingTime = Math.ceil(Number((data == null ? undefined : data.walkingTime) / 60));
    var transportTime = Math.ceil(Number((data == null ? undefined : data.transportTime) / 60));
    var totalTime = Number(walkingTime + transportTime);
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: styles.container,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.viewContent,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.viewText,
          children: [totalTime > 0 && (0, _jsxRuntime.jsxs)(_text.Text, {
            style: styles.txtTotalTime,
            children: [totalTime, " ", (0, _i18n.translate)("changimap.min")]
          }), walkingTime > 0 && (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.viewRow,
            children: [accessibleRoute ? (0, _jsxRuntime.jsx)(_reactNative2.Image, {
              source: _icons.AccessibleIcon,
              style: styles.icon,
              tintColor: _theme.color.palette.darkestGrey
            }) : (0, _jsxRuntime.jsx)(_reactNative2.Image, {
              source: _icons.WalkingIcon,
              style: styles.icon,
              tintColor: _theme.color.palette.darkestGrey
            }), (0, _jsxRuntime.jsxs)(_text.Text, {
              style: styles.subTotalTime,
              children: [walkingTime, " ", (0, _i18n.translate)("changimap.min")]
            })]
          }), transportTime > 0 && (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.viewRow,
            children: [(0, _jsxRuntime.jsx)(_icons.AtomTrain, {}), (0, _jsxRuntime.jsxs)(_text.Text, {
              style: styles.subTotalTime,
              children: [transportTime, " ", (0, _i18n.translate)("changimap.min")]
            })]
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: styles.button,
          onPress: onStartDirection,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            tx: "changimap.start",
            style: styles.txtButton
          })
        })]
      })
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    container: {
      marginVertical: 24,
      paddingHorizontal: 20
    },
    viewContent: {
      flexDirection: 'row',
      alignItems: 'center'
    },
    viewText: {
      flex: 1
    },
    viewRow: {
      flexDirection: 'row',
      alignItems: 'center'
    },
    txtTotalTime: {
      fontFamily: _theme.typography.heavy,
      fontWeight: _reactNative2.Platform.select({
        ios: "900",
        android: "normal"
      }),
      fontSize: 20,
      lineHeight: 28
    },
    subTotalTime: {
      fontFamily: _theme.typography.regular,
      fontWeight: _reactNative2.Platform.select({
        ios: "400",
        android: "normal"
      }),
      fontSize: 14,
      lineHeight: 18,
      color: _theme.color.palette.darkestGrey,
      marginLeft: 4
    },
    icon: {
      width: 16,
      height: 16
    },
    button: {
      width: 120,
      height: 44,
      borderRadius: 60,
      borderColor: _theme.color.palette.purpleD5BBEA,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 1
    },
    txtButton: {
      fontFamily: _theme.typography.bold,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      fontSize: 14,
      color: _theme.color.palette.lightPurple
    }
  });
