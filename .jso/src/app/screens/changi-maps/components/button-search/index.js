  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ButtonSearch = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _backgrounds = _$$_REQUIRE(_dependencyMap[4]);
  var _styles = _$$_REQUIRE(_dependencyMap[5]);
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  var testID = "ChangiMapInitButtonSearch";
  var ButtonSearch = exports.ButtonSearch = _react.default.memo(function (props) {
    var _props$isSmall = props.isSmall,
      isSmall = _props$isSmall === undefined ? false : _props$isSmall,
      onPress = props.onPress,
      containerStyle = props.containerStyle;
    return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
      testID: `${testID}`,
      onPress: onPress,
      style: [isSmall ? _styles.styles.containerSmall : _styles.styles.container, containerStyle],
      activeOpacity: 0.5,
      children: isSmall ? (0, _jsxRuntime.jsx)(_reactNative2.Image, {
        source: _icons.ButtonSearchLocationSmallPng,
        style: _styles.styles.buttonSearchSmall
      }) : (0, _jsxRuntime.jsx)(_reactNative2.Image, {
        source: _backgrounds.ButtonSearchLocation,
        style: _styles.styles.buttonSearch
      })
    });
  });
