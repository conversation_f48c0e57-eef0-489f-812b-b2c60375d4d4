  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var width = _reactNative.Dimensions.get('window').width;
  var ratioButtonSearch = 4.076086956521739;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    container: {
      width: '100%',
      position: 'absolute',
      bottom: 30
    },
    containerSmall: {
      position: 'absolute',
      bottom: 40,
      right: 0
    },
    buttonSearch: {
      width: width,
      height: (width - 48) / ratioButtonSearch
    },
    buttonSearchSmall: {
      width: 96,
      height: 96
    }
  });
