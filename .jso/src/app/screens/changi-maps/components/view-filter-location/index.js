  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ViewFilterLocation = undefined;
  var _backgrounds = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _styles = _$$_REQUIRE(_dependencyMap[5]);
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  var ViewFilterLocation = exports.ViewFilterLocation = _react.default.memo(function (props) {
    var _dataLocationFrom$pro, _dataLocationTo$prope;
    var dataLocationTo = props.dataLocationTo,
      dataLocationFrom = props.dataLocationFrom,
      reverseLocation = props.reverseLocation,
      onClick = props.onClick;
    return (0, _jsxRuntime.jsx)(_reactNative2.ImageBackground, {
      source: _backgrounds.BackgroundBlur,
      style: _styles.styles.container,
      children: (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: _styles.styles.viewContent,
        onPress: onClick,
        activeOpacity: 0.5,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _styles.styles.viewContentLeft,
          children: [(0, _jsxRuntime.jsx)(_icons.AtomLocationFrom, {}), (0, _jsxRuntime.jsx)(_icons.AtomLocationThreeDot, {}), (0, _jsxRuntime.jsx)(_icons.AtomLocationTo, {})]
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _styles.styles.viewContentCenter,
          children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _styles.styles.viewInput,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              style: _styles.styles.txtTitleLocation,
              children: dataLocationFrom == null || (_dataLocationFrom$pro = dataLocationFrom.properties) == null ? undefined : _dataLocationFrom$pro.title
            })
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _styles.styles.divinder
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _styles.styles.viewInput,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              style: _styles.styles.txtTitleLocation,
              children: dataLocationTo == null || (_dataLocationTo$prope = dataLocationTo.properties) == null ? undefined : _dataLocationTo$prope.title
            })
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _styles.styles.viewContentRight,
          children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: reverseLocation,
            children: (0, _jsxRuntime.jsx)(_icons.AtomLocationChangeLocation, {})
          })
        })]
      })
    });
  });
