  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var ratio = 0.24533333333333332;
  var width_screen = _reactNative.Dimensions.get('window').width;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    container: {
      position: 'absolute',
      width: '100%',
      height: width_screen * ratio,
      top: 100,
      alignItems: 'center',
      zIndex: 99
    },
    viewContent: {
      width: width_screen - 40,
      backgroundColor: _theme.color.palette.whiteGrey,
      paddingVertical: 10,
      paddingHorizontal: 12,
      borderRadius: 8,
      flexDirection: 'row',
      alignItems: 'center',
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 1
      },
      shadowOpacity: 0.22,
      shadowRadius: 2.22,
      elevation: 3
    },
    viewContentLeft: {
      paddingVertical: 10,
      flex: 1,
      maxWidth: 20,
      justifyContent: 'space-between',
      alignItems: 'center'
    },
    viewContentRight: {
      flex: 1,
      maxWidth: 20,
      justifyContent: 'center',
      alignItems: 'center'
    },
    viewContentCenter: {
      flex: 1,
      marginHorizontal: 16
    },
    viewInput: {
      width: '100%',
      height: 40,
      justifyContent: 'center'
    },
    divinder: {
      width: '100%',
      borderWidth: 0.5,
      borderColor: _theme.color.palette.lighterGrey
    },
    txtLocation: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.almostBlackGrey
    }),
    txtTitleLocation: {
      fontFamily: _theme.typography.regular,
      fontSize: 16,
      fontWeight: _reactNative.Platform.select({
        ios: "400",
        android: 'normal'
      }),
      color: _theme.color.palette.almostBlackGrey,
      lineHeight: 20
    }
  });
