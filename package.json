{"name": "ichangi-fe", "version": "0.0.1", "private": true, "scripts": {"ios": "npx react-native run-ios", "android": "./bin/cpay_aws_cli_setup sitDebug && ./bin/cpay_token_setup && npx react-native run-android --mode=sitDebug --appIdSuffix=sit", "android-ent": "./bin/cpay_aws_cli_setup sitDebug && ./bin/cpay_token_setup && npx react-native run-android --mode=sitEntDebug --appId=com.changiairport.cagapp.sit", "android-uatD": "./bin/cpay_aws_cli_setup uatDebug && ./bin/cpay_token_setup && npx react-native run-android --mode=uatDebug --appIdSuffix=uat", "android-uat": "npx react-native run-android --mode=uatRelease --appIdSuffix=uat", "install-app-debug": "adb install android/app/build/outputs/apk/uat/debug/app-uat-debug.apk", "install-app-release": "adb install android/app/build/outputs/apk/uat/release/app-uat-release.apk", "start": "react-native start", "compile": "tsc --noEmit -p . --pretty", "format": "npm-run-all format:*", "format:js": "prettier --write \"**/*.js\"", "format:json": "prettier --write \"**/*.json\"", "format:md": "prettier --write \"**/*.md\"", "format:ts": "prettier --write \"**/*.ts{,x}\"", "hack:types-react-navigation": "rimraf node_modules/@types/react-navigation/node_modules/@types", "hack:types-react-native": "rimraf node_modules/@types/react-native/node_modules/@types", "hack:types-react-test-renderer": "rimraf node_modules/@types/react-test-renderer/node_modules/@types", "lint": "eslint index.js app test --fix --ext .js,.ts,.tsx && npm run format", "patch": "patch-package", "test": "jest --config ./jest.config.json --colors", "test:coverage": "jest --config ./jest.config.json --coverage --silent --colors", "adb": "adb reverse tcp:9090 tcp:9090 && adb reverse tcp:3000 tcp:3000 && adb reverse tcp:9001 tcp:9001 && adb reverse tcp:8081 tcp:8081", "postinstall": "npm run patch && node ./bin/postInstall && copy-files-from-to --when-file-exists overwrite && npx instrumentDynatrace && ./bin/cpay_aws_cli_setup sitDebug && ./bin/cpay_token_setup", "build:ios": "react-native bundle --entry-file index.js --platform ios --dev false --bundle-output ios/main.jsbundle --assets-dest ios", "build:android": "react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res/", "build:android-bundle-sit": "react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/build/intermediates/res/merged/release/", "build:android-apk-sit": "./bin/cpay_aws_cli_setup sitRelease && ./bin/cpay_token_setup && cd android && ./gradlew assemblesitRelease && cd ..", "build:android-bundle-uat": "react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/build/intermediates/res/merged/release/ && rm -rf android/app/src/main/res/drawable-* && rm -rf android/app/src/main/res/raw/*", "build:android-apk-uat": "./bin/cpay_aws_cli_setup uatRelease && ./bin/cpay_token_setup && clear && cd android && ./gradlew clean && ./gradlew assembleuatRelease && cd ..", "build:android-apk-uatD": "./bin/cpay_aws_cli_setup uatDebug && ./bin/cpay_token_setup && cd android && ./gradlew clean && ./gradlew assembleuatDebug && cd ..", "build:android-bundle-preprod": "react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/build/intermediates/res/merged/release/ && rm -rf android/app/src/main/res/drawable-* && rm -rf android/app/src/main/res/raw/*", "build:android-apk-preprod": "./bin/cpay_aws_cli_setup preprodRelease && ./bin/cpay_token_setup && cd android && ./gradlew assemblepreprodRelease && cd ..", "build:android-bundle-prod": "react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/build/intermediates/res/merged/release/ && rm -rf android/app/src/main/res/drawable-* && rm -rf android/app/src/main/res/raw/*", "build:android-apk-prod": "./bin/cpay_aws_cli_setup prodRelease && ./bin/cpay_token_setup && cd android && ./gradlew generateCodegenArtifactsFromSchema --rerun-tasks && ./gradlew assembleprodRelease && cd ..", "build:android-apk-sit-ent": "./bin/cpay_aws_cli_setup sitEntRelease && ./bin/cpay_token_setup && cd android && ./gradlew generateCodegenArtifactsFromSchema --rerun-tasks && ./gradlew assemblesitEntRelease && cd ..", "build:android-apk-uat-ent": "./bin/cpay_aws_cli_setup uatEntRelease && ./bin/cpay_token_setup && cd android && ./gradlew generateCodegenArtifactsFromSchema --rerun-tasks && ./gradlew assembleuatEntRelease && cd ..", "build:android-apk-preprod-ent": "./bin/cpay_aws_cli_setup preprodEntRelease && ./bin/cpay_token_setup && cd android && ./gradlew generateCodegenArtifactsFromSchema --rerun-tasks && ./gradlew assemblepreprodEntRelease && cd ..", "prepare": "husky"}, "dependencies": {"@adobe/react-native-acpanalytics": "^2.0.1", "@adobe/react-native-acpcampaign": "^2.0.1", "@adobe/react-native-acpcore": "^2.0.2", "@adobe/react-native-acptarget": "^2.0.1", "@adrianso/react-native-device-brightness": "^1.2.7", "@braze/react-native-sdk": "^16.1.0", "@d11/react-native-fast-image": "^8.12.0", "@dynatrace/react-native-plugin": "2.317.2", "@maplibre/maplibre-react-native": "10.1.6", "@miblanchard/react-native-slider": "^2.6.0", "@notifee/react-native": "~9.1.8", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-clipboard/clipboard": "^1.14.1", "@react-native-community/blur": "^4.4.1", "@react-native-community/datetimepicker": "^8.2.0", "@react-native-community/image-editor": "4.3.0", "@react-native-community/netinfo": "^11.3.2", "@react-native-cookies/cookies": "6.2.1", "@react-native-firebase/analytics": "^23.5.0", "@react-native-firebase/app": "^23.5.0", "@react-native-firebase/crashlytics": "^23.5.0", "@react-native-firebase/messaging": "^23.5.0", "@react-native-firebase/perf": "^23.5.0", "@react-native-firebase/remote-config": "^23.5.0", "@react-native-masked-view/masked-view": "^0.3.1", "@react-native-picker/picker": "^2.8.0", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/material-top-tabs": "^7.2.10", "@react-navigation/native": "^7.1.6", "@react-navigation/stack": "^7.2.10", "@twotalltotems/react-native-otp-input": "^1.3.11", "apisauce": "~3.2.0", "aws-amplify": "^5.3.19", "aws-amplify-react-native": "^7.0.8", "axios": "1.10.0", "crypto-js": "^4.2.0", "formik": "^2.4.6", "graphql": "15.8.0", "i18n-js": "^3.8.0", "jail-monkey": "^2.8.0", "lottie-react-native": "^7.2.4", "moment": "2.30.1", "moment-timezone": "^0.5.45", "node-html-parser": "4.1.5", "path-to-regexp": "^6.2.1", "qs": "6.11.0", "ramda": "^0.29.1", "react": "19.0.0", "react-freeze": "^1.0.4", "react-native": "~0.79.5", "react-native-add-calendar-event": "^5.0.0", "react-native-aes-crypto": "^3.2.1", "react-native-animateable-text": "0.16.0", "react-native-appsflyer": "6.17.0", "react-native-autoheight-webview": "^1.6.5", "react-native-base64": "0.2.1", "react-native-biometrics": "^3.0.0", "react-native-blob-util": "^0.22.2", "react-native-bootsplash": "^6.3.11", "react-native-calendars": "^1.1306.0", "react-native-cardview": "^2.0.5", "react-native-collapsible": "^1.6.0", "react-native-color-matrix-image-filters": "^7.0.2", "react-native-dashed-line": "1.1.0", "react-native-date-picker": "^4.2.6", "react-native-device-info": "^14.0.4", "react-native-dynamic-render": "1.1.3", "react-native-encrypted-storage": "4.0.3", "react-native-fbsdk-next": "13.3.0", "react-native-file-viewer": "2.1.5", "react-native-fs": "2.20.0", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "2.24.0", "react-native-hole-view": "3.0.1", "react-native-image-crop-picker": "0.51.0", "react-native-image-pan-zoom": "^2.1.12", "react-native-image-picker": "^7.1.2", "react-native-image-resizer": "^1.4.5", "react-native-in-app-review": "^4.3.3", "react-native-inappbrowser-reborn": "3.7.0", "react-native-keyboard-aware-scroll-view": "0.9.5", "react-native-keyboard-controller": "^1.17.5", "react-native-keychain": "^10.0.0", "react-native-linear-gradient": "2.8.3", "react-native-maps": "1.20.1", "react-native-mmkv": "^3.3.3", "react-native-modal": "^14.0.0-rc.1", "react-native-modal-datetime-picker": "17.1.0", "react-native-pager-view": "^6.4.1", "react-native-pdf": "6.7.7", "react-native-permissions": "^4.1.5", "react-native-picker-select": "^9.3.1", "react-native-progress": "4.1.2", "react-native-qrcode-svg": "6.1.1", "react-native-qualtrics": "2.11.0", "react-native-reanimated": "^3.19.1", "react-native-reanimated-carousel": "^4.0.2", "react-native-render-html": "^6.3.4", "react-native-root-toast": "^3.4.0", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "4.15.4", "react-native-share": "^12.2.1", "react-native-size-matters": "0.4.0", "react-native-snap-carousel": "3.9.1", "react-native-startup-trace": "^0.5.0", "react-native-svg": "^15.12.1", "react-native-swiper": "1.6.0", "react-native-uuid": "^2.0.2", "react-native-video": "^6.16.1", "react-native-vision-camera": "4.7.1", "react-native-walkthrough-tooltip": "^1.6.0", "react-native-webp-format": "^1.2.0", "react-native-webview": "^13.13.5", "react-native-zip-archive": "^7.0.2", "react-redux": "^7.2.9", "reactotron-redux-saga": "4.2.3", "redux": "4.2.1", "redux-logger": "^3.0.6", "redux-persist": "6.0.0", "reduxsauce": "1.2.1", "rn-qr-generator": "1.2.0", "rn-swipe-button": "3.0.1", "rn-webview-pool": "file:third_party/rn-webview-pool/rn-webview-pool-1.0.0-uat.tgz", "seamless-immutable": "7.1.4", "url-parse": "^1.5.10", "validate.js": "0.13.1", "yup": "0.32.9", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-proposal-decorators": "^7.24.7", "@babel/plugin-transform-optional-catch-binding": "^7.24.7", "@babel/preset-env": "^7.25.4", "@babel/runtime": "^7.25.6", "@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "~0.79.5", "@react-native/eslint-config": "~0.79.5", "@react-native/metro-config": "~0.79.5", "@react-native/typescript-config": "~0.79.5", "@tsconfig/react-native": "^3.0.5", "@types/i18n-js": "^3.8.9", "@types/jest": "^29.5.13", "@types/ramda": "0.27.32", "@types/react": "^19.0.0", "@types/react-native-snap-carousel": "^3.8.5", "@types/react-redux": "7.1.33", "@types/react-test-renderer": "^19.0.0", "@typescript-eslint/eslint-plugin": "4.10.0", "@typescript-eslint/parser": "4.10.0", "babel-jest": "^29.6.3", "babel-loader": "9.1.2", "babel-plugin-transform-remove-console": "^6.9.4", "bufferutil": "4.0.2", "copy-files-from-to": "^3.11.0", "enzyme": "^3.11.0", "eslint": "^8.19.0", "eslint-config-prettier": "7.0.0", "eslint-config-standard": "16.0.2", "eslint-plugin-import": "2.22.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.2.1", "eslint-plugin-react": "7.21.5", "eslint-plugin-react-native": "3.10.0", "husky": "^9.1.6", "jest": "^29.6.3", "jetifier": "^1.6.6", "lint-staged": "10.5.4", "npm-run-all": "4.1.5", "obfuscator-io-metro-plugin": "^2.1.3", "patch-package": "8.0.0", "postinstall-prepare": "^2.0.0", "prettier": "^2.8.8", "react-devtools": "^5.3.1", "react-native-svg-transformer": "1.5.0", "react-test-renderer": "19.0.0", "reactotron-react-native": "~5.1.14", "reactotron-redux": "~3.2.0", "redux-saga": "^1.2.3", "rimraf": "3.0.2", "solidarity": "3.0.4", "typescript": "5.0.4", "utf-8-validate": "5.0.3"}, "engines": {"node": ">=18"}, "resolutions": {"@types/react": "17.0.44"}, "prettier": {"printWidth": 100, "semi": false, "singleQuote": false, "trailingComma": "all"}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}}